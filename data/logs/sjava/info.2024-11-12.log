info | 1 | 1731376479211 | 2024-11-12 09:54:39 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 6534 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 13 | 1731376479206 | 2024-11-12 09:54:39 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 40c434c9a8f44cd5a9ce77ba404a1b8f | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731376479222 | 2024-11-12 09:54:39 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731376479655 | 2024-11-12 09:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 439 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731376479658 | 2024-11-12 09:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 441 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731376479660 | 2024-11-12 09:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 443 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731376479662 | 2024-11-12 09:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 445 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731376479666 | 2024-11-12 09:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 449 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731376479705 | 2024-11-12 09:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 488 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731376479735 | 2024-11-12 09:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 518 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731376479737 | 2024-11-12 09:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 520 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731376479737 | 2024-11-12 09:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 520 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731376479791 | 2024-11-12 09:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 574 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731376481716 | 2024-11-12 09:54:41 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 2500 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731376481718 | 2024-11-12 09:54:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 2501 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731376481758 | 2024-11-12 09:54:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 2541 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 33 ms. Found 0 JPA repository interfaces.

info | 1 | 1731376481768 | 2024-11-12 09:54:41 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 2551 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731376481769 | 2024-11-12 09:54:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 2552 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731376481794 | 2024-11-12 09:54:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 2577 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1731376482530 | 2024-11-12 09:54:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 3317 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$8521deef] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376482548 | 2024-11-12 09:54:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 3332 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$4282467] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376482607 | 2024-11-12 09:54:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 3391 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$82912f30] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376482614 | 2024-11-12 09:54:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 3397 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376482674 | 2024-11-12 09:54:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 3457 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376482678 | 2024-11-12 09:54:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 3461 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376483202 | 2024-11-12 09:54:43 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 3985 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731376483212 | 2024-11-12 09:54:43 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 3995 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731376483212 | 2024-11-12 09:54:43 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 3995 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731376483302 | 2024-11-12 09:54:43 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 4085 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731376497304 | 2024-11-12 09:54:57 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 18087 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731376497376 | 2024-11-12 09:54:57 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 18159 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731376497415 | 2024-11-12 09:54:57 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 18198 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731376497510 | 2024-11-12 09:54:57 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 18293 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731376497584 | 2024-11-12 09:54:57 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 18367 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731376497709 | 2024-11-12 09:54:57 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 18492 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731376497720 | 2024-11-12 09:54:57 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 18503 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731376500994 | 2024-11-12 09:55:00 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 21778 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731376501271 | 2024-11-12 09:55:01 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 22054 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731376501292 | 2024-11-12 09:55:01 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 22075 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 40 | 1731376501445 | 2024-11-12 09:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 96c2167133bf452bbf47a4b75829961d | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 

info | 40 | 1731376501466 | 2024-11-12 09:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 96c2167133bf452bbf47a4b75829961d | - | - | - | - | 21 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 40 | 1731376501475 | 2024-11-12 09:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 96c2167133bf452bbf47a4b75829961d | - | - | - | - | 30 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 40 | 1731376501582 | 2024-11-12 09:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 96c2167133bf452bbf47a4b75829961d | - | - | - | - | 137 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 106 ms to scan 310 urls, producing 0 keys and 0 values 

info | 40 | 1731376501588 | 2024-11-12 09:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 96c2167133bf452bbf47a4b75829961d | - | - | - | - | 144 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 40 | 1731376501595 | 2024-11-12 09:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 96c2167133bf452bbf47a4b75829961d | - | - | - | - | 151 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 40 | 1731376501602 | 2024-11-12 09:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 96c2167133bf452bbf47a4b75829961d | - | - | - | - | 158 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 40 | 1731376501705 | 2024-11-12 09:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 96c2167133bf452bbf47a4b75829961d | - | - | - | - | 261 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 100 ms to scan 310 urls, producing 0 keys and 0 values 

info | 1 | 1731376504092 | 2024-11-12 09:55:04 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 24875 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731376504763 | 2024-11-12 09:55:04 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 25547 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@689774fc with [org.springframework.security.web.session.DisableEncodeUrlFilter@41f1feae, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6b4bc8f6, org.springframework.security.web.context.SecurityContextPersistenceFilter@3d530201, org.springframework.security.web.header.HeaderWriterFilter@702d5e75, org.springframework.security.web.authentication.logout.LogoutFilter@43a5e40c, org.springframework.web.filter.CorsFilter@55f77306, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@67bf0d91, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@6c790029, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1283f2ea, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7c8d9235, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3f1ab9ce, org.springframework.security.web.session.SessionManagementFilter@6dd30df3, org.springframework.security.web.access.ExceptionTranslationFilter@64eb6eda, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@69c7965a]

info | 1 | 1731376504778 | 2024-11-12 09:55:04 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 25561 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731376504855 | 2024-11-12 09:55:04 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 25638 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731376504856 | 2024-11-12 09:55:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 25640 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731376504857 | 2024-11-12 09:55:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 25640 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731376504859 | 2024-11-12 09:55:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 25643 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731376504862 | 2024-11-12 09:55:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 25645 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731376504862 | 2024-11-12 09:55:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 25645 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731376504862 | 2024-11-12 09:55:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 25645 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1731376505417 | 2024-11-12 09:55:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26201 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1731376505539 | 2024-11-12 09:55:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26322 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731376505539 | 2024-11-12 09:55:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26323 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731376505540 | 2024-11-12 09:55:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26323 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731376505542 | 2024-11-12 09:55:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26325 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731376505645 | 2024-11-12 09:55:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26428 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731376505646 | 2024-11-12 09:55:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26429 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1731376505659 | 2024-11-12 09:55:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26442 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@422cf64e, tech.powerjob.worker.actors.ProcessorTrackerActor@58d8c305, tech.powerjob.worker.actors.WorkerActor@77e7be90])

info | 1 | 1731376505697 | 2024-11-12 09:55:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26480 | 0 | - | - | - | - | main o.r.Reflections Reflections took 27 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731376505703 | 2024-11-12 09:55:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26486 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1731376505704 | 2024-11-12 09:55:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26487 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@263059cd

info | 1 | 1731376505704 | 2024-11-12 09:55:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26487 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731376505705 | 2024-11-12 09:55:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26488 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1731376505707 | 2024-11-12 09:55:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 26490 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 132 | 1731376506136 | 2024-11-12 09:55:06 | v2/Mailbox/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | run | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731376506481 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27264 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731376506482 | 2024-11-12 09:55:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27265 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731376506482 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27265 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731376506482 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27265 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731376506482 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27265 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731376506482 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27265 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731376506482 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27265 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731376506483 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27266 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731376506483 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27266 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731376506483 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27266 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731376506483 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27266 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731376506483 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27266 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731376506483 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27266 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731376506483 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27266 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731376506483 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27266 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731376506485 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27268 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731376506487 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27270 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731376506487 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27270 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731376506488 | 2024-11-12 09:55:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27271 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 783.4 ms

info | 1 | 1731376506672 | 2024-11-12 09:55:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27455 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731376506679 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27462 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731376506679 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27462 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731376506685 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27468 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731376506936 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27719 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731376506936 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27719 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/e5cc5a3412cf4c7d8848846bb58e45dd/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731376506945 | 2024-11-12 09:55:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27728 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/e5cc5a3412cf4c7d8848846bb58e45dd/] on JVM exit successfully

info | 1 | 1731376506963 | 2024-11-12 09:55:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27746 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731376506964 | 2024-11-12 09:55:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27747 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 2.109 s, congratulations!

info | 161 | 1731376506969 | 2024-11-12 09:55:06 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | f428caeef8aa4f8c8fff17ed58327d81 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 161 | 1731376506970 | 2024-11-12 09:55:06 | v2/ThreadPoolExecutor$Worker/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor$Worker | run | f428caeef8aa4f8c8fff17ed58327d81 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731376507026 | 2024-11-12 09:55:07 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27809 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731376507055 | 2024-11-12 09:55:07 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27838 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731376507072 | 2024-11-12 09:55:07 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27855 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731376507072 | 2024-11-12 09:55:07 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27856 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731376507098 | 2024-11-12 09:55:07 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27881 | 0 | - | - | - | - | main c.t.g.Application Started Application in 28.249 seconds (JVM running for 28.612)

info | 1 | 1731376507120 | 2024-11-12 09:55:07 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27903 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731376507121 | 2024-11-12 09:55:07 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 9c887067320a44619d0348e2f9490516 | - | - | - | - | 27904 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 231 | 1731376507124 | 2024-11-12 09:55:07 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | c9335b58fb454bfc9fb6b909098def07 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 26 | 1731376515054 | 2024-11-12 09:55:15 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 514899ec2c0a4f909aeeea40725bcec0 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 26 | 1731376515085 | 2024-11-12 09:55:15 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 514899ec2c0a4f909aeeea40725bcec0 | - | - | - | - | 31 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 26 | 1731376515087 | 2024-11-12 09:55:15 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | 514899ec2c0a4f909aeeea40725bcec0 | - | - | - | - | 33 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 26 | 1731376515103 | 2024-11-12 09:55:15 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | AbstractApplicationContext | destroyBeans | 514899ec2c0a4f909aeeea40725bcec0 | - | - | - | - | 49 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 15 | 1731376582623 | 2024-11-12 09:56:22 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 2db52a3b12d44a1695073560fbed5ea1 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731376582630 | 2024-11-12 09:56:22 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 5cd0bf5adea44cfea96f6dc9f6d22798 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 6587 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1731376582641 | 2024-11-12 09:56:22 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 5cd0bf5adea44cfea96f6dc9f6d22798 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 15 | 1731376633271 | 2024-11-12 09:57:13 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | afd05bc3db6f42ffb4f6b3f35b333fb1 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731376633279 | 2024-11-12 09:57:13 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 6670 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1731376633288 | 2024-11-12 09:57:13 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731376641574 | 2024-11-12 09:57:21 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 8290 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731376641578 | 2024-11-12 09:57:21 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 8294 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731376641580 | 2024-11-12 09:57:21 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 8296 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731376641582 | 2024-11-12 09:57:21 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 8298 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731376641584 | 2024-11-12 09:57:21 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 8300 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731376641623 | 2024-11-12 09:57:21 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 8339 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731376641656 | 2024-11-12 09:57:21 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 8372 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731376641658 | 2024-11-12 09:57:21 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 8374 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731376641659 | 2024-11-12 09:57:21 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 8375 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731376641709 | 2024-11-12 09:57:21 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 8425 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731376643728 | 2024-11-12 09:57:23 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 10444 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731376643729 | 2024-11-12 09:57:23 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 10445 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731376643772 | 2024-11-12 09:57:23 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 10488 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 37 ms. Found 0 JPA repository interfaces.

info | 1 | 1731376643785 | 2024-11-12 09:57:23 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 10501 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731376643787 | 2024-11-12 09:57:23 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 10503 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731376643826 | 2024-11-12 09:57:23 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 10542 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.

info | 1 | 1731376644747 | 2024-11-12 09:57:24 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 11467 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$189800b6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376644772 | 2024-11-12 09:57:24 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 11488 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$979e462e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376644844 | 2024-11-12 09:57:24 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 11560 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$160750f7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376644848 | 2024-11-12 09:57:24 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 11564 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376644904 | 2024-11-12 09:57:24 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 11621 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376644909 | 2024-11-12 09:57:24 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 11626 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376645357 | 2024-11-12 09:57:25 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 12073 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731376645370 | 2024-11-12 09:57:25 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 12086 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731376645370 | 2024-11-12 09:57:25 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 12086 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731376645461 | 2024-11-12 09:57:25 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 12177 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731376659478 | 2024-11-12 09:57:39 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 26195 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731376659539 | 2024-11-12 09:57:39 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 26255 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731376659583 | 2024-11-12 09:57:39 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 26299 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731376659704 | 2024-11-12 09:57:39 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 26420 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731376659790 | 2024-11-12 09:57:39 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 26506 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731376659956 | 2024-11-12 09:57:39 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 26672 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731376659965 | 2024-11-12 09:57:39 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 26681 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731376663793 | 2024-11-12 09:57:43 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 30510 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731376664238 | 2024-11-12 09:57:44 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 30954 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731376664261 | 2024-11-12 09:57:44 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 3bc1af9f8ddd48b58ea554394c3b1e05 | - | - | - | - | 30977 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1731376664408 | 2024-11-12 09:57:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 9e3cfd8708d546ce8f32688c290d1c2a | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1731376664430 | 2024-11-12 09:57:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 9e3cfd8708d546ce8f32688c290d1c2a | - | - | - | - | 22 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1731376664439 | 2024-11-12 09:57:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 9e3cfd8708d546ce8f32688c290d1c2a | - | - | - | - | 31 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1731376664569 | 2024-11-12 09:57:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 9e3cfd8708d546ce8f32688c290d1c2a | - | - | - | - | 161 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 128 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1731376664575 | 2024-11-12 09:57:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 9e3cfd8708d546ce8f32688c290d1c2a | - | - | - | - | 167 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1731376664582 | 2024-11-12 09:57:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 9e3cfd8708d546ce8f32688c290d1c2a | - | - | - | - | 174 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1731376664589 | 2024-11-12 09:57:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 9e3cfd8708d546ce8f32688c290d1c2a | - | - | - | - | 181 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1731376664687 | 2024-11-12 09:57:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 9e3cfd8708d546ce8f32688c290d1c2a | - | - | - | - | 280 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 96 ms to scan 311 urls, producing 0 keys and 0 values 

info | 15 | 1731376737087 | 2024-11-12 09:58:57 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 01308f3860004ae3a707e6b16b1c0669 | - | - | - | - | 3 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731376737093 | 2024-11-12 09:58:57 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 3 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 6738 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1731376737103 | 2024-11-12 09:58:57 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731376737610 | 2024-11-12 09:58:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 512 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731376737613 | 2024-11-12 09:58:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 514 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731376737615 | 2024-11-12 09:58:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 516 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731376737616 | 2024-11-12 09:58:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 518 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731376737619 | 2024-11-12 09:58:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 520 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731376737652 | 2024-11-12 09:58:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 553 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731376737683 | 2024-11-12 09:58:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 584 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731376737685 | 2024-11-12 09:58:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 586 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731376737685 | 2024-11-12 09:58:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 586 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731376737733 | 2024-11-12 09:58:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 635 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731376739726 | 2024-11-12 09:58:59 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 2627 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731376739727 | 2024-11-12 09:58:59 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 2628 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731376739772 | 2024-11-12 09:58:59 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 2673 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 38 ms. Found 0 JPA repository interfaces.

info | 1 | 1731376739781 | 2024-11-12 09:58:59 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 2682 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731376739782 | 2024-11-12 09:58:59 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 2683 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731376739808 | 2024-11-12 09:58:59 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 2709 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1731376740572 | 2024-11-12 09:59:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 3476 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b1264240] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376740599 | 2024-11-12 09:59:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 3500 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$302c87b8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376740664 | 2024-11-12 09:59:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 3566 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$ae959281] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376740669 | 2024-11-12 09:59:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 3570 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376740726 | 2024-11-12 09:59:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 3628 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376740730 | 2024-11-12 09:59:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 3631 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731376741268 | 2024-11-12 09:59:01 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 4169 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731376741284 | 2024-11-12 09:59:01 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 4185 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731376741285 | 2024-11-12 09:59:01 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 4186 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731376741371 | 2024-11-12 09:59:01 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 4272 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731376756559 | 2024-11-12 09:59:16 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 19461 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731376756653 | 2024-11-12 09:59:16 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 19555 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731376756702 | 2024-11-12 09:59:16 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 19603 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731376756844 | 2024-11-12 09:59:16 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 19746 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731376756942 | 2024-11-12 09:59:16 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 19843 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731376757081 | 2024-11-12 09:59:17 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 19982 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731376757090 | 2024-11-12 09:59:17 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 19991 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731376760712 | 2024-11-12 09:59:20 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 23614 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731376760983 | 2024-11-12 09:59:20 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 23884 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731376761007 | 2024-11-12 09:59:21 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 23908 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 1 | 1731376761161 | 2024-11-12 09:59:21 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 24063 | 0 | - | - | - | - | main o.r.Reflections Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 

info | 1 | 1731376761182 | 2024-11-12 09:59:21 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 24084 | 0 | - | - | - | - | main o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 

info | 1 | 1731376761191 | 2024-11-12 09:59:21 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 24092 | 0 | - | - | - | - | main o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 1 | 1731376761308 | 2024-11-12 09:59:21 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 24209 | 0 | - | - | - | - | main o.r.Reflections Reflections took 115 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731376761314 | 2024-11-12 09:59:21 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 24215 | 0 | - | - | - | - | main o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 1 | 1731376761322 | 2024-11-12 09:59:21 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 24223 | 0 | - | - | - | - | main o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 1 | 1731376761330 | 2024-11-12 09:59:21 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 24231 | 0 | - | - | - | - | main o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 1 | 1731376761463 | 2024-11-12 09:59:21 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 24364 | 0 | - | - | - | - | main o.r.Reflections Reflections took 131 ms to scan 311 urls, producing 0 keys and 0 values 

info | 38 | 1731376814307 | 2024-11-12 10:00:14 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | 9191dae5f9bb404a9c2f9da5d64b4a87 | - | - | - | - | 2 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-11-12 09:59:20,982 to 2024-11-12 10:00:14,291
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 1 | 1731376814454 | 2024-11-12 10:00:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 77355 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731376815401 | 2024-11-12 10:00:15 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78302 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@14d954b4 with [org.springframework.security.web.session.DisableEncodeUrlFilter@37dcc1fd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4b992ca0, org.springframework.security.web.context.SecurityContextPersistenceFilter@8edfd06, org.springframework.security.web.header.HeaderWriterFilter@4b51bc96, org.springframework.security.web.authentication.logout.LogoutFilter@2ad0806f, org.springframework.web.filter.CorsFilter@57ad1859, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@59acd853, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@3cd33897, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@59fd8c1c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@41402d1b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@62f1a74d, org.springframework.security.web.session.SessionManagementFilter@66bfc1d0, org.springframework.security.web.access.ExceptionTranslationFilter@30b6cfc7, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@b2ef2f4]

info | 1 | 1731376815424 | 2024-11-12 10:00:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78325 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731376815508 | 2024-11-12 10:00:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78410 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731376815510 | 2024-11-12 10:00:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78411 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731376815511 | 2024-11-12 10:00:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78412 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731376815513 | 2024-11-12 10:00:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78414 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731376815516 | 2024-11-12 10:00:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78417 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731376815516 | 2024-11-12 10:00:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78417 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731376815516 | 2024-11-12 10:00:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78417 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1731376815811 | 2024-11-12 10:00:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78712 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1731376815881 | 2024-11-12 10:00:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78782 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731376815881 | 2024-11-12 10:00:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78782 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731376815881 | 2024-11-12 10:00:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78782 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731376815883 | 2024-11-12 10:00:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78784 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731376815995 | 2024-11-12 10:00:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78896 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731376815996 | 2024-11-12 10:00:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78897 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1731376816005 | 2024-11-12 10:00:16 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78906 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@50c41141, tech.powerjob.worker.actors.ProcessorTrackerActor@3e632037, tech.powerjob.worker.actors.WorkerActor@6299cbbb])

info | 1 | 1731376816039 | 2024-11-12 10:00:16 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78941 | 0 | - | - | - | - | main o.r.Reflections Reflections took 25 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731376816051 | 2024-11-12 10:00:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78952 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1731376816052 | 2024-11-12 10:00:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78953 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@492fbf58

info | 1 | 1731376816052 | 2024-11-12 10:00:16 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78953 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731376816053 | 2024-11-12 10:00:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78954 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1731376816055 | 2024-11-12 10:00:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 78956 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 185 | 1731376816670 | 2024-11-12 10:00:16 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731376817139 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80040 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731376817140 | 2024-11-12 10:00:17 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80041 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731376817141 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80042 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731376817141 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80042 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731376817141 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80042 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731376817141 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80042 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731376817141 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80042 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731376817141 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80042 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731376817141 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80042 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731376817141 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80042 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731376817141 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80042 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731376817142 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80043 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731376817142 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80043 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731376817142 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80043 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731376817142 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80043 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731376817144 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80045 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731376817148 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80049 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731376817148 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80049 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731376817149 | 2024-11-12 10:00:17 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80051 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.097 s

info | 1 | 1731376817239 | 2024-11-12 10:00:17 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80141 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731376817248 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80150 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731376817249 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80150 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731376817252 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80153 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731376817476 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80377 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731376817476 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80377 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/a4dded06434447d88a1f2442f7a390e7/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731376817482 | 2024-11-12 10:00:17 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80383 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/a4dded06434447d88a1f2442f7a390e7/] on JVM exit successfully

info | 1 | 1731376817498 | 2024-11-12 10:00:17 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80399 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731376817499 | 2024-11-12 10:00:17 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80400 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.990 s, congratulations!

info | 221 | 1731376817504 | 2024-11-12 10:00:17 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | b1164bf2f022436cad8f0304110d0a1f | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 221 | 1731376817505 | 2024-11-12 10:00:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b1164bf2f022436cad8f0304110d0a1f | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731376817570 | 2024-11-12 10:00:17 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.639 | *************** | - | 2 | TomcatWebServer | start | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80472 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731376817598 | 2024-11-12 10:00:17 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.641 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80499 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731376817612 | 2024-11-12 10:00:17 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.643 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80513 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731376817612 | 2024-11-12 10:00:17 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.645 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80513 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731376817642 | 2024-11-12 10:00:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.647 | *************** | - | 2 | Application | main | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80543 | 0 | - | - | - | - | main c.t.g.Application Started Application in 80.932 seconds (JVM running for 81.403)

info | 1 | 1731376817665 | 2024-11-12 10:00:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.649 | *************** | - | 2 | Application | main | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80567 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731376817666 | 2024-11-12 10:00:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.651 | *************** | - | 2 | Application | main | 7d1d3bd6402b4de495cc805fec1d30ed | - | - | - | - | 80567 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 291 | 1731376817672 | 2024-11-12 10:00:17 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | cb4b05a271284e91adcd7728cd2f03d5 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 221 | 1731376827504 | 2024-11-12 10:00:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b1164bf2f022436cad8f0304110d0a1f | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 221 | 1731376837501 | 2024-11-12 10:00:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b1164bf2f022436cad8f0304110d0a1f | - | - | - | - | 19998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 221 | 1731376847503 | 2024-11-12 10:00:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b1164bf2f022436cad8f0304110d0a1f | - | - | - | - | 29999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 221 | 1731376857500 | 2024-11-12 10:00:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b1164bf2f022436cad8f0304110d0a1f | - | - | - | - | 39997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 221 | 1731376867500 | 2024-11-12 10:01:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b1164bf2f022436cad8f0304110d0a1f | - | - | - | - | 49996 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 221 | 1731376877501 | 2024-11-12 10:01:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b1164bf2f022436cad8f0304110d0a1f | - | - | - | - | 59998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 221 | 1731376887500 | 2024-11-12 10:01:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b1164bf2f022436cad8f0304110d0a1f | - | - | - | - | 69997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 221 | 1731376897503 | 2024-11-12 10:01:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b1164bf2f022436cad8f0304110d0a1f | - | - | - | - | 80000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 225 | 1731376907507 | 2024-11-12 10:01:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ddd479026f3e41e782527ff0ae3bbd68 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 225 | 1731376917504 | 2024-11-12 10:01:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ddd479026f3e41e782527ff0ae3bbd68 | - | - | - | - | 9996 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 225 | 1731376927500 | 2024-11-12 10:02:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ddd479026f3e41e782527ff0ae3bbd68 | - | - | - | - | 19993 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 225 | 1731376937500 | 2024-11-12 10:02:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ddd479026f3e41e782527ff0ae3bbd68 | - | - | - | - | 29993 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 225 | 1731376947502 | 2024-11-12 10:02:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ddd479026f3e41e782527ff0ae3bbd68 | - | - | - | - | 39995 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 291 | 1731376949777 | 2024-11-12 10:02:29 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ChatRoomService | refreshChatCache | cb4b05a271284e91adcd7728cd2f03d5 | - | - | - | - | 132106 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 225 | 1731376957503 | 2024-11-12 10:02:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ddd479026f3e41e782527ff0ae3bbd68 | - | - | - | - | 49996 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 225 | 1731376967506 | 2024-11-12 10:02:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ddd479026f3e41e782527ff0ae3bbd68 | - | - | - | - | 59998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 225 | 1731376977506 | 2024-11-12 10:02:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ddd479026f3e41e782527ff0ae3bbd68 | - | - | - | - | 69998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 225 | 1731376987503 | 2024-11-12 10:03:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ddd479026f3e41e782527ff0ae3bbd68 | - | - | - | - | 79995 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1731376994672 | 2024-11-12 10:03:14 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 50995bcdf5744960b91eba1e6f9c6582 | - | - | - | - | 1 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 1 | 1731376998184 | 2024-11-12 10:03:18 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 6900 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1731376998178 | 2024-11-12 10:03:18 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | b4690621576f4eb6b2a0c919cd198d8b | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731376998197 | 2024-11-12 10:03:18 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731376999141 | 2024-11-12 10:03:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 951 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731376999144 | 2024-11-12 10:03:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 953 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731376999148 | 2024-11-12 10:03:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 957 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731376999151 | 2024-11-12 10:03:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 960 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731376999154 | 2024-11-12 10:03:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 963 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731376999243 | 2024-11-12 10:03:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 1053 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731376999369 | 2024-11-12 10:03:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 1178 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731376999372 | 2024-11-12 10:03:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 1181 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731376999373 | 2024-11-12 10:03:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 1182 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731376999459 | 2024-11-12 10:03:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 1269 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731377001511 | 2024-11-12 10:03:21 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 3320 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731377001512 | 2024-11-12 10:03:21 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 3321 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731377001558 | 2024-11-12 10:03:21 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 3367 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 39 ms. Found 0 JPA repository interfaces.

info | 1 | 1731377001567 | 2024-11-12 10:03:21 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 3376 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731377001568 | 2024-11-12 10:03:21 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 3377 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731377001598 | 2024-11-12 10:03:21 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 3407 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.

info | 1 | 1731377002348 | 2024-11-12 10:03:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 4160 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$860661c2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731377002375 | 2024-11-12 10:03:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 4185 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$50ca73a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731377002446 | 2024-11-12 10:03:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 4256 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$8375b203] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731377002451 | 2024-11-12 10:03:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 4260 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731377002520 | 2024-11-12 10:03:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 4329 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731377002524 | 2024-11-12 10:03:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 4333 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731377003029 | 2024-11-12 10:03:23 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 4838 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731377003040 | 2024-11-12 10:03:23 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 4849 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731377003040 | 2024-11-12 10:03:23 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 4849 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731377003115 | 2024-11-12 10:03:23 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 4924 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731377018927 | 2024-11-12 10:03:38 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 20736 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731377019000 | 2024-11-12 10:03:39 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 20810 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731377019031 | 2024-11-12 10:03:39 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 20840 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731377019118 | 2024-11-12 10:03:39 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 20928 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731377019192 | 2024-11-12 10:03:39 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 21001 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731377019342 | 2024-11-12 10:03:39 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 21151 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731377019348 | 2024-11-12 10:03:39 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 21158 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731377022811 | 2024-11-12 10:03:42 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 24620 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731377023077 | 2024-11-12 10:03:43 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 24886 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731377023099 | 2024-11-12 10:03:43 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 24908 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1731377023256 | 2024-11-12 10:03:43 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ffd5ddc5f791476fa19cf435c3846763 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1731377023278 | 2024-11-12 10:03:43 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ffd5ddc5f791476fa19cf435c3846763 | - | - | - | - | 22 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1731377023289 | 2024-11-12 10:03:43 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ffd5ddc5f791476fa19cf435c3846763 | - | - | - | - | 34 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1731377023445 | 2024-11-12 10:03:43 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ffd5ddc5f791476fa19cf435c3846763 | - | - | - | - | 189 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 153 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1731377023451 | 2024-11-12 10:03:43 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ffd5ddc5f791476fa19cf435c3846763 | - | - | - | - | 196 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1731377023459 | 2024-11-12 10:03:43 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ffd5ddc5f791476fa19cf435c3846763 | - | - | - | - | 204 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1731377023467 | 2024-11-12 10:03:43 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ffd5ddc5f791476fa19cf435c3846763 | - | - | - | - | 211 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1731377023574 | 2024-11-12 10:03:43 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ffd5ddc5f791476fa19cf435c3846763 | - | - | - | - | 318 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 105 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731377026019 | 2024-11-12 10:03:46 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 27829 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731377026672 | 2024-11-12 10:03:46 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 28481 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@6246a249 with [org.springframework.security.web.session.DisableEncodeUrlFilter@416a6d16, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7f0bc723, org.springframework.security.web.context.SecurityContextPersistenceFilter@4f86f4f4, org.springframework.security.web.header.HeaderWriterFilter@632e7345, org.springframework.security.web.authentication.logout.LogoutFilter@2765f545, org.springframework.web.filter.CorsFilter@2e0169d0, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@786594c8, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@5511d1a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@45547014, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@274f5cc3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@52d2a313, org.springframework.security.web.session.SessionManagementFilter@820abe5, org.springframework.security.web.access.ExceptionTranslationFilter@61606066, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3c7045f5]

info | 1 | 1731377026688 | 2024-11-12 10:03:46 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 28497 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731377026760 | 2024-11-12 10:03:46 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 28570 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731377026762 | 2024-11-12 10:03:46 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 28571 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731377026763 | 2024-11-12 10:03:46 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 28572 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731377026765 | 2024-11-12 10:03:46 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 28574 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731377026768 | 2024-11-12 10:03:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 28577 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731377026768 | 2024-11-12 10:03:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 28577 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731377026768 | 2024-11-12 10:03:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 28577 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1731377027139 | 2024-11-12 10:03:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 28948 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1731377027317 | 2024-11-12 10:03:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29126 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731377027317 | 2024-11-12 10:03:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29126 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731377027318 | 2024-11-12 10:03:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29127 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731377027321 | 2024-11-12 10:03:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29131 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731377027425 | 2024-11-12 10:03:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29234 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731377027426 | 2024-11-12 10:03:47 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29235 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1731377027441 | 2024-11-12 10:03:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29251 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@1a62a6db, tech.powerjob.worker.actors.ProcessorTrackerActor@3656a7c8, tech.powerjob.worker.actors.WorkerActor@13b6cd6])

info | 1 | 1731377027481 | 2024-11-12 10:03:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29290 | 0 | - | - | - | - | main o.r.Reflections Reflections took 25 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731377027486 | 2024-11-12 10:03:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29296 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1731377027487 | 2024-11-12 10:03:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29296 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@461cb79e

info | 1 | 1731377027487 | 2024-11-12 10:03:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29296 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731377027488 | 2024-11-12 10:03:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29297 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1731377027491 | 2024-11-12 10:03:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 29300 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 124 | 1731377027937 | 2024-11-12 10:03:47 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731377028303 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30113 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731377028304 | 2024-11-12 10:03:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30113 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731377028305 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30114 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731377028305 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30114 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731377028305 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30114 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731377028305 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30114 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731377028305 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30114 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731377028305 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30114 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731377028305 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30115 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731377028306 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30115 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731377028306 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30115 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731377028306 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30115 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731377028306 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30115 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731377028306 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30115 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731377028306 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30115 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731377028307 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30117 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731377028309 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30119 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731377028310 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30119 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731377028310 | 2024-11-12 10:03:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30120 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 823.0 ms

info | 1 | 1731377028449 | 2024-11-12 10:03:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30258 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731377028456 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30265 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731377028457 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30266 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731377028461 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30270 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731377028679 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30488 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731377028679 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30488 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/6d3a977e894a463898dd761b9afbc06b/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731377028684 | 2024-11-12 10:03:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30494 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/6d3a977e894a463898dd761b9afbc06b/] on JVM exit successfully

info | 1 | 1731377028698 | 2024-11-12 10:03:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30507 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731377028699 | 2024-11-12 10:03:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30508 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.939 s, congratulations!

info | 159 | 1731377028703 | 2024-11-12 10:03:48 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 2836683d8590468da053bfcbbac992f7 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 159 | 1731377028704 | 2024-11-12 10:03:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2836683d8590468da053bfcbbac992f7 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731377028754 | 2024-11-12 10:03:48 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30563 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731377028777 | 2024-11-12 10:03:48 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30586 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731377028796 | 2024-11-12 10:03:48 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30605 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731377028797 | 2024-11-12 10:03:48 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30606 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731377028826 | 2024-11-12 10:03:48 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30635 | 0 | - | - | - | - | main c.t.g.Application Started Application in 31.176 seconds (JVM running for 31.565)

info | 1 | 1731377028847 | 2024-11-12 10:03:48 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30656 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731377028847 | 2024-11-12 10:03:48 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | aa904b0812ab4f6abeed2263fcc02686 | - | - | - | - | 30656 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 229 | 1731377028852 | 2024-11-12 10:03:48 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 2f51ac56af114276a83dcde9f7884aa4 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 28 | 1731377034828 | 2024-11-12 10:03:54 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1731377034866 | 2024-11-12 10:03:54 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | ******************************** | - | - | - | - | 38 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1731377034868 | 2024-11-12 10:03:54 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | ******************************** | - | - | - | - | 40 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1731377034883 | 2024-11-12 10:03:54 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | AbstractApplicationContext | destroyBeans | ******************************** | - | - | - | - | 55 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 15 | 1731378036294 | 2024-11-12 10:20:36 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 71badbb6272a4396895429796d040dd0 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731378036298 | 2024-11-12 10:20:36 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 7470 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1731378036312 | 2024-11-12 10:20:36 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731378036822 | 2024-11-12 10:20:36 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 516 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731378036825 | 2024-11-12 10:20:36 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 518 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731378036826 | 2024-11-12 10:20:36 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 520 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731378036830 | 2024-11-12 10:20:36 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 524 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731378057927 | 2024-11-12 10:20:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 21623 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731378058004 | 2024-11-12 10:20:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 21698 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731378058053 | 2024-11-12 10:20:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 21746 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731378058056 | 2024-11-12 10:20:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 21749 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731378058056 | 2024-11-12 10:20:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 21749 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731378058108 | 2024-11-12 10:20:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 21801 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731378060229 | 2024-11-12 10:21:00 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 23923 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731378060231 | 2024-11-12 10:21:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 23924 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731378060276 | 2024-11-12 10:21:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 23969 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 38 ms. Found 0 JPA repository interfaces.

info | 1 | 1731378060287 | 2024-11-12 10:21:00 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 23980 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731378060288 | 2024-11-12 10:21:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 23981 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731378060319 | 2024-11-12 10:21:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 24012 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.

info | 1 | 1731378061115 | 2024-11-12 10:21:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 24811 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$fb383721] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731378061135 | 2024-11-12 10:21:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 24828 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7a3e7c99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731378061203 | 2024-11-12 10:21:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 24896 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$f8a78762] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731378061207 | 2024-11-12 10:21:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 24901 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731378061267 | 2024-11-12 10:21:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 24961 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731378061271 | 2024-11-12 10:21:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 24964 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731378061865 | 2024-11-12 10:21:01 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 25558 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731378061876 | 2024-11-12 10:21:01 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 25569 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731378061876 | 2024-11-12 10:21:01 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 25569 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731378061978 | 2024-11-12 10:21:01 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 25671 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731378077741 | 2024-11-12 10:21:17 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 41436 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731378077832 | 2024-11-12 10:21:17 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 41526 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731378077877 | 2024-11-12 10:21:17 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 41571 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731378078036 | 2024-11-12 10:21:18 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 41729 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731378078123 | 2024-11-12 10:21:18 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 41817 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731378078293 | 2024-11-12 10:21:18 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 41986 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731378078305 | 2024-11-12 10:21:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 41999 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731378082252 | 2024-11-12 10:21:22 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 45945 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731378082586 | 2024-11-12 10:21:22 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 46280 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731378082608 | 2024-11-12 10:21:22 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 46301 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1731378082766 | 2024-11-12 10:21:22 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2c79bf4acb6340fb963fc6d2310ebf9f | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1731378083000 | 2024-11-12 10:21:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2c79bf4acb6340fb963fc6d2310ebf9f | - | - | - | - | 233 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1731378083008 | 2024-11-12 10:21:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2c79bf4acb6340fb963fc6d2310ebf9f | - | - | - | - | 241 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1731378083114 | 2024-11-12 10:21:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2c79bf4acb6340fb963fc6d2310ebf9f | - | - | - | - | 347 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 104 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1731378083120 | 2024-11-12 10:21:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2c79bf4acb6340fb963fc6d2310ebf9f | - | - | - | - | 353 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1731378083127 | 2024-11-12 10:21:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2c79bf4acb6340fb963fc6d2310ebf9f | - | - | - | - | 360 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1731378083133 | 2024-11-12 10:21:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2c79bf4acb6340fb963fc6d2310ebf9f | - | - | - | - | 367 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1731378083234 | 2024-11-12 10:21:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2c79bf4acb6340fb963fc6d2310ebf9f | - | - | - | - | 468 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 99 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731378085547 | 2024-11-12 10:21:25 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 49241 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731378086349 | 2024-11-12 10:21:26 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50043 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@15eae4fa with [org.springframework.security.web.session.DisableEncodeUrlFilter@56106cef, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3daad86d, org.springframework.security.web.context.SecurityContextPersistenceFilter@55f0e377, org.springframework.security.web.header.HeaderWriterFilter@14c84fd6, org.springframework.security.web.authentication.logout.LogoutFilter@dfb23a6, org.springframework.web.filter.CorsFilter@2f8288b2, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@280c955c, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@2d62a5cc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@73969353, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@71886642, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6fed59ae, org.springframework.security.web.session.SessionManagementFilter@495b3de0, org.springframework.security.web.access.ExceptionTranslationFilter@82feb0b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2b68172f]

info | 1 | 1731378086371 | 2024-11-12 10:21:26 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50064 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731378086459 | 2024-11-12 10:21:26 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50153 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731378086461 | 2024-11-12 10:21:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50155 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731378086462 | 2024-11-12 10:21:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50156 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731378086465 | 2024-11-12 10:21:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50180 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731378086502 | 2024-11-12 10:21:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50195 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731378086503 | 2024-11-12 10:21:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50196 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731378086503 | 2024-11-12 10:21:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50196 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1731378086776 | 2024-11-12 10:21:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50470 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1731378086901 | 2024-11-12 10:21:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50594 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731378086902 | 2024-11-12 10:21:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50595 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731378086902 | 2024-11-12 10:21:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50595 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731378086903 | 2024-11-12 10:21:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50596 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731378087040 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50734 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731378087042 | 2024-11-12 10:21:27 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50735 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1731378087056 | 2024-11-12 10:21:27 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50749 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@267bf28, tech.powerjob.worker.actors.ProcessorTrackerActor@74bf3795, tech.powerjob.worker.actors.WorkerActor@76b5f3f0])

info | 1 | 1731378087090 | 2024-11-12 10:21:27 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50783 | 0 | - | - | - | - | main o.r.Reflections Reflections took 22 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731378087096 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50789 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1731378087097 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50790 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@67b8b180

info | 1 | 1731378087098 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50791 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@2032cd25

info | 1 | 1731378087098 | 2024-11-12 10:21:27 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50791 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731378087099 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50792 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1731378087101 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 50794 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 121 | 1731378087590 | 2024-11-12 10:21:27 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731378087978 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51672 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731378087979 | 2024-11-12 10:21:27 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51672 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731378087979 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51672 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731378087979 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51672 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731378087980 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731378087982 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51675 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731378087984 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51677 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731378087984 | 2024-11-12 10:21:27 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51678 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731378087985 | 2024-11-12 10:21:27 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51678 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 886.6 ms

info | 1 | 1731378088127 | 2024-11-12 10:21:28 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51821 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731378088133 | 2024-11-12 10:21:28 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51826 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731378088134 | 2024-11-12 10:21:28 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51827 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731378088139 | 2024-11-12 10:21:28 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 51832 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731378088348 | 2024-11-12 10:21:28 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52041 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731378088348 | 2024-11-12 10:21:28 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52041 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/0959e42c48094972ba1fef169dc0a67d/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731378088354 | 2024-11-12 10:21:28 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52047 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/0959e42c48094972ba1fef169dc0a67d/] on JVM exit successfully

info | 1 | 1731378088368 | 2024-11-12 10:21:28 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52061 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731378088369 | 2024-11-12 10:21:28 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52062 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.910 s, congratulations!

info | 152 | 1731378088373 | 2024-11-12 10:21:28 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 5d319522991e43fa93263d347c867eca | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 152 | 1731378088373 | 2024-11-12 10:21:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731378088414 | 2024-11-12 10:21:28 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | TomcatWebServer | start | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52108 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731378088442 | 2024-11-12 10:21:28 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52135 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731378088462 | 2024-11-12 10:21:28 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52155 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731378088462 | 2024-11-12 10:21:28 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52155 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731378088487 | 2024-11-12 10:21:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52180 | 0 | - | - | - | - | main c.t.g.Application Started Application in 52.621 seconds (JVM running for 53.174)

info | 1 | 1731378088508 | 2024-11-12 10:21:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52201 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731378088508 | 2024-11-12 10:21:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | Application | main | 76f2f692bb3245a5b30eff96fdd66e93 | - | - | - | - | 52201 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 222 | 1731378088513 | 2024-11-12 10:21:28 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 1fbc0967487343cebff56696a587b8e2 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 152 | 1731378098374 | 2024-11-12 10:21:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 10002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378108371 | 2024-11-12 10:21:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 19999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378118374 | 2024-11-12 10:21:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 30001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378128371 | 2024-11-12 10:22:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 40000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378138371 | 2024-11-12 10:22:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 49998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378148370 | 2024-11-12 10:22:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 59997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378158374 | 2024-11-12 10:22:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 70001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378168373 | 2024-11-12 10:22:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 80004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378178375 | 2024-11-12 10:22:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 90003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378188375 | 2024-11-12 10:23:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 100003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378198376 | 2024-11-12 10:23:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 110018 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378208371 | 2024-11-12 10:23:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 120000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 222 | 1731378218333 | 2024-11-12 10:23:38 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ChatRoomService | refreshChatCache | 1fbc0967487343cebff56696a587b8e2 | - | - | - | - | 129821 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 152 | 1731378218375 | 2024-11-12 10:23:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 130003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378228373 | 2024-11-12 10:23:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 140001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378238372 | 2024-11-12 10:23:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 149999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378248373 | 2024-11-12 10:24:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 160000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378258376 | 2024-11-12 10:24:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 170003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378268372 | 2024-11-12 10:24:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 179999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378278374 | 2024-11-12 10:24:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 190001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378288373 | 2024-11-12 10:24:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 200001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378298374 | 2024-11-12 10:24:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 210002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378308376 | 2024-11-12 10:25:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 220004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378318373 | 2024-11-12 10:25:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 230000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378328376 | 2024-11-12 10:25:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 240004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731378338377 | 2024-11-12 10:25:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731378348374 | 2024-11-12 10:25:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 9998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731378358374 | 2024-11-12 10:25:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 19998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731378368372 | 2024-11-12 10:26:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 29995 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378378372 | 2024-11-12 10:26:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 290000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378388377 | 2024-11-12 10:26:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 300004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378398377 | 2024-11-12 10:26:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 310004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378408377 | 2024-11-12 10:26:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 320004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378418373 | 2024-11-12 10:26:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 330000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378428376 | 2024-11-12 10:27:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 340005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378438377 | 2024-11-12 10:27:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 350005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378448377 | 2024-11-12 10:27:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 360006 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378458377 | 2024-11-12 10:27:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 370005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378468373 | 2024-11-12 10:27:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 380000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378478373 | 2024-11-12 10:27:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 390000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378488374 | 2024-11-12 10:28:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 400001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378498377 | 2024-11-12 10:28:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.77 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 410005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378508374 | 2024-11-12 10:28:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.79 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 420001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378518374 | 2024-11-12 10:28:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.81 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 430002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378528375 | 2024-11-12 10:28:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.83 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 440003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1731378529059 | 2024-11-12 10:28:49 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 52b8b6e2501b425c9e4a7594edb3e9e4 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 160 | 1731378529060 | 2024-11-12 10:28:49 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 52b8b6e2501b425c9e4a7594edb3e9e4 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 160 | 1731378529089 | 2024-11-12 10:28:49 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 52b8b6e2501b425c9e4a7594edb3e9e4 | - | - | - | - | 30 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 29 ms

info | 152 | 1731378538374 | 2024-11-12 10:28:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.85 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 450002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378548373 | 2024-11-12 10:29:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.87 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 460000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378558375 | 2024-11-12 10:29:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.89 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 470002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378568376 | 2024-11-12 10:29:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.91 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 480003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378578375 | 2024-11-12 10:29:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378588377 | 2024-11-12 10:29:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 10002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378598376 | 2024-11-12 10:29:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 20002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1731378600028 | 2024-11-12 10:30:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | dd1c0eb4a8824ef29c830137faac2724 | - | - | - | - | 0 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-11-12 10:21:22,585 to 2024-11-12 10:30:00,019
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 152 | 1731378608378 | 2024-11-12 10:30:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.93 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 520006 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378618373 | 2024-11-12 10:30:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.95 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 530000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378628374 | 2024-11-12 10:30:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 50000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378638376 | 2024-11-12 10:30:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 60002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378648377 | 2024-11-12 10:30:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.97 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 560004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378658376 | 2024-11-12 10:30:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.99 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 570004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378668376 | 2024-11-12 10:31:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 90003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378678376 | 2024-11-12 10:31:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 100001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378688377 | 2024-11-12 10:31:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 110003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378698374 | 2024-11-12 10:31:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 119999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378708376 | 2024-11-12 10:31:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.101 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 620004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378718379 | 2024-11-12 10:31:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 140004 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378728379 | 2024-11-12 10:32:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 150005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378738376 | 2024-11-12 10:32:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 160002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378748376 | 2024-11-12 10:32:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 170002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378758377 | 2024-11-12 10:32:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 180002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378768375 | 2024-11-12 10:32:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 190000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731378778376 | 2024-11-12 10:32:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 440001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378788379 | 2024-11-12 10:33:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.103 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 700006 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378798377 | 2024-11-12 10:33:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.105 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 710004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378808379 | 2024-11-12 10:33:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.107 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 720006 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731378818374 | 2024-11-12 10:33:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 479999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378828378 | 2024-11-12 10:33:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.109 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 740005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378838377 | 2024-11-12 10:33:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.111 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 750005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378848378 | 2024-11-12 10:34:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.113 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 760007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378858379 | 2024-11-12 10:34:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.115 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 770006 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731378868376 | 2024-11-12 10:34:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 529999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378887735 | 2024-11-12 10:34:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 309360 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378888378 | 2024-11-12 10:34:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.117 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 800006 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378898376 | 2024-11-12 10:34:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.119 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 810003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378908377 | 2024-11-12 10:35:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 330002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378918375 | 2024-11-12 10:35:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 340001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378928378 | 2024-11-12 10:35:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 350004 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378938376 | 2024-11-12 10:35:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 360003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378948377 | 2024-11-12 10:35:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 370002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378958380 | 2024-11-12 10:35:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 380005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378968380 | 2024-11-12 10:36:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 390006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378978381 | 2024-11-12 10:36:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 400007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731378988376 | 2024-11-12 10:36:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 410001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731378998378 | 2024-11-12 10:36:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.121 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 910006 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379008380 | 2024-11-12 10:36:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.123 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 920007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379018376 | 2024-11-12 10:36:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.125 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 930004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379028381 | 2024-11-12 10:37:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.127 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 940009 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379049533 | 2024-11-12 10:37:29 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.129 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 961169 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379049545 | 2024-11-12 10:37:29 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.131 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 961177 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379058381 | 2024-11-12 10:37:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 720004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379068382 | 2024-11-12 10:37:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 730007 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379078380 | 2024-11-12 10:37:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 740004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379088381 | 2024-11-12 10:38:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.133 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1000008 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379098376 | 2024-11-12 10:38:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 520002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379108378 | 2024-11-12 10:38:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 530004 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379118376 | 2024-11-12 10:38:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 540002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379128381 | 2024-11-12 10:38:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 790005 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379138380 | 2024-11-12 10:38:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 560006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379148381 | 2024-11-12 10:39:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 570007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379160904 | 2024-11-12 10:39:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 822527 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379179217 | 2024-11-12 10:39:39 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 840840 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379179227 | 2024-11-12 10:39:39 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 840850 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379199811 | 2024-11-12 10:39:59 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.135 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1111439 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379199826 | 2024-11-12 10:39:59 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.137 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1111453 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379209119 | 2024-11-12 10:40:09 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 630744 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379225357 | 2024-11-12 10:40:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.139 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1136988 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379228359 | 2024-11-12 10:40:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 889982 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379238361 | 2024-11-12 10:40:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.141 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1149990 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379248360 | 2024-11-12 10:40:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.143 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1159988 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379258359 | 2024-11-12 10:40:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.145 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1169986 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379268358 | 2024-11-12 10:41:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.147 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1179986 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379278354 | 2024-11-12 10:41:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.149 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1189981 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379288356 | 2024-11-12 10:41:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.151 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1199986 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379298357 | 2024-11-12 10:41:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.153 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1209985 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379308358 | 2024-11-12 10:41:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.155 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1219986 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379318357 | 2024-11-12 10:41:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.157 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1229985 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1731379328354 | 2024-11-12 10:42:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.159 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 5d319522991e43fa93263d347c867eca | - | - | - | - | 1239981 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379338354 | 2024-11-12 10:42:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 759980 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379348358 | 2024-11-12 10:42:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 769983 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379358357 | 2024-11-12 10:42:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 779982 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379368358 | 2024-11-12 10:42:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 789984 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731379378358 | 2024-11-12 10:42:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1d9cc1250d6f469680fc9997aa58542c | - | - | - | - | 799983 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525198 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186853 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1731379525242 | 2024-11-12 10:45:25 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | dd1c0eb4a8824ef29c830137faac2724 | - | - | - | - | 925214 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-11-12 10:30:00,019 to 2024-11-12 10:45:25,198
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 156 | 1731379525246 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186870 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525251 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186876 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525260 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186884 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525262 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186885 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525262 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186885 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525262 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186885 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525262 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186885 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525262 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186885 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525263 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186886 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525263 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186886 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525264 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186887 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525264 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186887 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731379525264 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 1f3f34c26a5c42828ffaf92448c569ce | - | - | - | - | 1186887 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731389586451 | 2024-11-12 13:33:06 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 12462 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1731389586445 | 2024-11-12 13:33:06 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 43a8e804f1be4386bd7596c376518f4c | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731389586463 | 2024-11-12 13:33:06 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731389586966 | 2024-11-12 13:33:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 509 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731389586968 | 2024-11-12 13:33:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 512 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731389586970 | 2024-11-12 13:33:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 513 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731389586974 | 2024-11-12 13:33:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 517 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731389586976 | 2024-11-12 13:33:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 519 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731389587017 | 2024-11-12 13:33:07 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 560 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731389622188 | 2024-11-12 13:33:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 35731 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731389622194 | 2024-11-12 13:33:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 35737 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731389622194 | 2024-11-12 13:33:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 35737 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731389622302 | 2024-11-12 13:33:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f0fa50fff3d0431a9a8c115e2cdc7242 | - | - | - | - | 35845 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731389625970 | 2024-11-12 13:33:45 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 3 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 12508 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1731389625967 | 2024-11-12 13:33:45 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 323f2fb6b3674dcfb229374db7cf970e | - | - | - | - | 3 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731389625983 | 2024-11-12 13:33:45 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731389626425 | 2024-11-12 13:33:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 446 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731389626427 | 2024-11-12 13:33:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 453 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731389626434 | 2024-11-12 13:33:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 455 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731389626436 | 2024-11-12 13:33:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 457 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731389626437 | 2024-11-12 13:33:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 458 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731389626475 | 2024-11-12 13:33:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 497 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731389663778 | 2024-11-12 13:34:23 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 37800 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731389663782 | 2024-11-12 13:34:23 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 37804 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731389663783 | 2024-11-12 13:34:23 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 37804 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731389663868 | 2024-11-12 13:34:23 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 37889 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731389665916 | 2024-11-12 13:34:25 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 39938 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731389665918 | 2024-11-12 13:34:25 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 39939 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731389665958 | 2024-11-12 13:34:25 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 39979 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.

info | 1 | 1731389665967 | 2024-11-12 13:34:25 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 39989 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731389665968 | 2024-11-12 13:34:25 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 39990 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731389665996 | 2024-11-12 13:34:25 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 40018 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1731389666772 | 2024-11-12 13:34:26 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 40796 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$cb11a08] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731389666794 | 2024-11-12 13:34:26 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 40816 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$8bb75f80] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731389666859 | 2024-11-12 13:34:26 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 40880 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$a206a49] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731389666863 | 2024-11-12 13:34:26 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 40884 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731389666928 | 2024-11-12 13:34:26 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 40949 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731389666932 | 2024-11-12 13:34:26 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 40954 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731389667442 | 2024-11-12 13:34:27 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 41463 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731389667451 | 2024-11-12 13:34:27 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 41472 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731389667451 | 2024-11-12 13:34:27 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 41472 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731389667530 | 2024-11-12 13:34:27 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 41552 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731389684753 | 2024-11-12 13:34:44 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 58775 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731389684834 | 2024-11-12 13:34:44 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 58856 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731389684875 | 2024-11-12 13:34:44 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 58896 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731389684998 | 2024-11-12 13:34:44 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 59019 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731389685075 | 2024-11-12 13:34:45 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 59096 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731389685205 | 2024-11-12 13:34:45 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 59226 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731389685211 | 2024-11-12 13:34:45 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 59232 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731389688644 | 2024-11-12 13:34:48 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 62666 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731389688962 | 2024-11-12 13:34:48 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 62983 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731389688988 | 2024-11-12 13:34:48 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 63010 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 1 | 1731389689149 | 2024-11-12 13:34:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 63171 | 0 | - | - | - | - | main o.r.Reflections Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 

info | 1 | 1731389689174 | 2024-11-12 13:34:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 63196 | 0 | - | - | - | - | main o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 1 | 1731389689184 | 2024-11-12 13:34:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 63205 | 0 | - | - | - | - | main o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 1 | 1731389689301 | 2024-11-12 13:34:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 63322 | 0 | - | - | - | - | main o.r.Reflections Reflections took 115 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731389689307 | 2024-11-12 13:34:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 63328 | 0 | - | - | - | - | main o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 1 | 1731389689316 | 2024-11-12 13:34:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 63338 | 0 | - | - | - | - | main o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 

info | 1 | 1731389689325 | 2024-11-12 13:34:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 63346 | 0 | - | - | - | - | main o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 1 | 1731389689447 | 2024-11-12 13:34:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 63468 | 0 | - | - | - | - | main o.r.Reflections Reflections took 120 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731389694385 | 2024-11-12 13:34:54 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 68406 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731389695131 | 2024-11-12 13:34:55 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 69152 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@6faab2bd with [org.springframework.security.web.session.DisableEncodeUrlFilter@30814bf1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@c32d8f1, org.springframework.security.web.context.SecurityContextPersistenceFilter@2fdd7467, org.springframework.security.web.header.HeaderWriterFilter@73ac8829, org.springframework.security.web.authentication.logout.LogoutFilter@284d5de4, org.springframework.web.filter.CorsFilter@359da2e7, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@36f35774, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@161dca1d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6ab8b279, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6893fc3c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2c18bd39, org.springframework.security.web.session.SessionManagementFilter@39c1b088, org.springframework.security.web.access.ExceptionTranslationFilter@3b9e1d3e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@66db5a9a]

info | 1 | 1731389695148 | 2024-11-12 13:34:55 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 69169 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731389695239 | 2024-11-12 13:34:55 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 69260 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731389695241 | 2024-11-12 13:34:55 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 69262 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731389695243 | 2024-11-12 13:34:55 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 69264 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731389695246 | 2024-11-12 13:34:55 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 69267 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731389695249 | 2024-11-12 13:34:55 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 69270 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731389695249 | 2024-11-12 13:34:55 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 69270 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731389695249 | 2024-11-12 13:34:55 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 69270 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1731389696355 | 2024-11-12 13:34:56 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 70376 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1731389697358 | 2024-11-12 13:34:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 71380 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731389697359 | 2024-11-12 13:34:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 71380 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731389697359 | 2024-11-12 13:34:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 71380 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731389697361 | 2024-11-12 13:34:57 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 71383 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731389699055 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73076 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731389699057 | 2024-11-12 13:34:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73078 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1731389699075 | 2024-11-12 13:34:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73097 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@1de1311e, tech.powerjob.worker.actors.ProcessorTrackerActor@f45db3e, tech.powerjob.worker.actors.WorkerActor@39cf6a30])

info | 1 | 1731389699113 | 2024-11-12 13:34:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73134 | 0 | - | - | - | - | main o.r.Reflections Reflections took 23 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731389699120 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73141 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1731389699121 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73142 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@7a853d3d

info | 1 | 1731389699121 | 2024-11-12 13:34:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73142 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731389699121 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73142 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1731389699124 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73145 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 123 | 1731389699575 | 2024-11-12 13:34:59 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731389699957 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73978 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731389699958 | 2024-11-12 13:34:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73979 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731389699958 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73979 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731389699959 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73980 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731389699959 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73980 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731389699959 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73980 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731389699959 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73980 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731389699959 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73980 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731389699959 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73980 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731389699959 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73980 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731389699960 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73981 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731389699960 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73981 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731389699960 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73981 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731389699960 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73981 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731389699960 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73981 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731389699961 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73982 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731389699964 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73985 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731389699964 | 2024-11-12 13:34:59 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73985 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731389699965 | 2024-11-12 13:34:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 73986 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 843.8 ms

info | 1 | 1731389700148 | 2024-11-12 13:35:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74170 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731389700155 | 2024-11-12 13:35:00 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74176 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731389700155 | 2024-11-12 13:35:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74176 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731389700160 | 2024-11-12 13:35:00 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74181 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731389700365 | 2024-11-12 13:35:00 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74386 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731389700365 | 2024-11-12 13:35:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74386 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/43702a2cebb147f4b22c640dc8b4c0d8/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731389700371 | 2024-11-12 13:35:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74392 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/43702a2cebb147f4b22c640dc8b4c0d8/] on JVM exit successfully

info | 1 | 1731389700386 | 2024-11-12 13:35:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74407 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731389700387 | 2024-11-12 13:35:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74408 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 5.148 s, congratulations!

info | 160 | 1731389700391 | 2024-11-12 13:35:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 2c8dab1574d74356a7c409f1c7bcfce5 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 160 | 1731389700392 | 2024-11-12 13:35:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2c8dab1574d74356a7c409f1c7bcfce5 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731389700434 | 2024-11-12 13:35:00 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.639 | *************** | - | 2 | TomcatWebServer | start | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74455 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731389700470 | 2024-11-12 13:35:00 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.641 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74492 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731389700486 | 2024-11-12 13:35:00 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.643 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74507 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731389700486 | 2024-11-12 13:35:00 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.645 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74507 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731389700517 | 2024-11-12 13:35:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.647 | *************** | - | 2 | Application | main | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74541 | 0 | - | - | - | - | main c.t.g.Application Started Application in 74.962 seconds (JVM running for 75.43)

info | 1 | 1731389700544 | 2024-11-12 13:35:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.649 | *************** | - | 2 | Application | main | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74566 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731389700545 | 2024-11-12 13:35:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.651 | *************** | - | 2 | Application | main | 6dd1c08ad8574e64858164eaedc1f642 | - | - | - | - | 74566 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 230 | 1731389700547 | 2024-11-12 13:35:00 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 9bff66387ce440d8816e7b77d5d11bda | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 160 | 1731389710391 | 2024-11-12 13:35:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2c8dab1574d74356a7c409f1c7bcfce5 | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1731389720388 | 2024-11-12 13:35:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2c8dab1574d74356a7c409f1c7bcfce5 | - | - | - | - | 19997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1731389730392 | 2024-11-12 13:35:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2c8dab1574d74356a7c409f1c7bcfce5 | - | - | - | - | 30002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1731389740391 | 2024-11-12 13:35:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2c8dab1574d74356a7c409f1c7bcfce5 | - | - | - | - | 40000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1731389750389 | 2024-11-12 13:35:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2c8dab1574d74356a7c409f1c7bcfce5 | - | - | - | - | 49999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1731389760388 | 2024-11-12 13:36:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2c8dab1574d74356a7c409f1c7bcfce5 | - | - | - | - | 59997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1731389763163 | 2024-11-12 13:36:03 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 7d42a2705f3d495284bff3485547471a | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

