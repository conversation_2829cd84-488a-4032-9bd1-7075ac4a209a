error | 232 | 1732614453446 | 2024-11-26 17:47:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | 192.168.144.31 | - | 2 | Thread | run | 790bd24bc4724a428ed06909de66847e | - | - | - | - | 26395 | 0 | - | - | - | - | task-1 o.s.a.i.SimpleAsyncUncaughtExceptionHandler SimpleAsyncUncaughtExceptionHandler.java:39 Unexpected exception occurred invoking async method: public void cn.taqu.gonghui.chatroom.service.ChatRoomService.refreshChatCache()
org.springframework.data.redis.RedisConnectionFailureException: java.net.SocketTimeoutException: Read timed out; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: Read timed out
	at org.springframework.data.redis.connection.jedis.JedisExceptionConverter.convert(JedisExceptionConverter.java:65)
	at org.springframework.data.redis.connection.jedis.JedisExceptionConverter.convert(JedisExceptionConverter.java:42)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.jedis.JedisConnection.convertJedisAccessException(JedisConnection.java:135)
	at org.springframework.data.redis.connection.jedis.JedisStringCommands.convertJedisAccessException(JedisStringCommands.java:754)
	at org.springframework.data.redis.connection.jedis.JedisStringCommands.set(JedisStringCommands.java:146)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:288)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:984)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at cn.taqu.gonghui.chatroom.service.ChatRoomService.refreshChatCache(ChatRoomService.java:1685)
	at cn.taqu.gonghui.chatroom.service.ChatRoomService$$FastClassBySpringCGLIB$$fbf0035b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: Read timed out
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:205)
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:43)
	at redis.clients.jedis.Protocol.process(Protocol.java:155)
	at redis.clients.jedis.Protocol.read(Protocol.java:220)
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:278)
	at redis.clients.jedis.Connection.getStatusCodeReply(Connection.java:196)
	at redis.clients.jedis.BinaryJedis.set(BinaryJedis.java:226)
	at org.springframework.data.redis.connection.jedis.JedisStringCommands.set(JedisStringCommands.java:144)
	... 20 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at java.net.SocketInputStream.read(SocketInputStream.java:127)
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:199)
	... 27 common frames omitted

org.springframework.data.redis.RedisConnectionFailureException: java.net.SocketTimeoutException: Read timed out; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: Read timed out
	at org.springframework.data.redis.connection.jedis.JedisExceptionConverter.convert(JedisExceptionConverter.java:65)
	at org.springframework.data.redis.connection.jedis.JedisExceptionConverter.convert(JedisExceptionConverter.java:42)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.jedis.JedisConnection.convertJedisAccessException(JedisConnection.java:135)
	at org.springframework.data.redis.connection.jedis.JedisStringCommands.convertJedisAccessException(JedisStringCommands.java:754)
	at org.springframework.data.redis.connection.jedis.JedisStringCommands.set(JedisStringCommands.java:146)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:288)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:984)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:222)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at cn.taqu.gonghui.chatroom.service.ChatRoomService.refreshChatCache(ChatRoomService.java:1685)
	at cn.taqu.gonghui.chatroom.service.ChatRoomService$$FastClassBySpringCGLIB$$fbf0035b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:115)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: Read timed out
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:205)
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:43)
	at redis.clients.jedis.Protocol.process(Protocol.java:155)
	at redis.clients.jedis.Protocol.read(Protocol.java:220)
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:278)
	at redis.clients.jedis.Connection.getStatusCodeReply(Connection.java:196)
	at redis.clients.jedis.BinaryJedis.set(BinaryJedis.java:226)
	at org.springframework.data.redis.connection.jedis.JedisStringCommands.set(JedisStringCommands.java:144)
	... 20 more
java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at java.net.SocketInputStream.read(SocketInputStream.java:127)
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:199)
	... 27 more
error | 172 | 1732615469721 | 2024-11-26 18:04:29 | v2/ manageChatRoom/ roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | 192.168.144.31 | - | 2 |  manageChatRoom |  roomChatData | 605ee355f16d4891910ecafefba27700 | - | - | - | - | 205 | 0 | - | - | - | - | http-nio-8087-exec-8 c.t.g.c.w.SpringExceptionHandler SpringExceptionHandler.java:139 service=%20manageChatRoom&method=%20roomChatData 错误码[405]:出错了啊！请骚候再试[j01002]

error | 173 | 1732615478483 | 2024-11-26 18:04:38 | v2/ manageChatRoom/ roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | 192.168.144.31 | - | 2 |  manageChatRoom |  roomChatData | c656bc8d0fc547439abc060912b29a12 | - | - | - | - | 138 | 0 | - | - | - | - | http-nio-8087-exec-9 c.t.g.c.w.SpringExceptionHandler SpringExceptionHandler.java:139 service=%20manageChatRoom&method=%20roomChatData 错误码[405]:出错了啊！请骚候再试[j01002]

error | 174 | 1732615489621 | 2024-11-26 18:04:49 | v2/manageChatRoom/ roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | 192.168.144.31 | - | 2 | manageChatRoom |  roomChatData | 2beb853dd9ef485bb9e7e9bd2c7123cf | - | - | - | - | 100 | 0 | - | - | - | - | http-nio-8087-exec-10 c.t.g.c.w.SpringExceptionHandler SpringExceptionHandler.java:139 service=manageChatRoom&method=%20roomChatData 错误码[405]:出错了啊！请骚候再试[j01002]

