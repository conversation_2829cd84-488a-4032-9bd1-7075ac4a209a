warning | 1 | 1753414345100 | 2025-07-25 11:32:25 | v2/StandardContext/stopInternal | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | StandardContext | stopInternal | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 27184 | 0 | - | - | - | - | main o.a.c.l.WebappClassLoaderBase The web application [tq-newgonghui] appears to have started a thread named [pool-2-thread-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2039)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1081)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)

warning | 1 | 1753414386141 | 2025-07-25 11:33:06 | v2/StandardContext/stopInternal | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | StandardContext | stopInternal | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 24706 | 0 | - | - | - | - | main o.a.c.l.WebappClassLoaderBase The web application [tq-newgonghui] appears to have started a thread named [pool-2-thread-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2039)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1081)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)

warning | 1 | 1753414618302 | 2025-07-25 11:36:58 | v2/StandardContext/stopInternal | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | StandardContext | stopInternal | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 24736 | 0 | - | - | - | - | main o.a.c.l.WebappClassLoaderBase The web application [tq-newgonghui] appears to have started a thread named [pool-2-thread-1] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 sun.misc.Unsafe.park(Native Method)
 java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
 java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2039)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1081)
 java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:809)
 java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1074)
 java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1134)
 java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
 java.lang.Thread.run(Thread.java:750)

warning | 125 | 1753414691105 | 2025-07-25 11:38:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | df94ececa4274042a84311e1f9245ce0 | - | - | - | - | 122 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 125 | 1753414691105 | 2025-07-25 11:38:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | df94ececa4274042a84311e1f9245ce0 | - | - | - | - | 122 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 228 | 1753414699824 | 2025-07-25 11:38:19 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 5b2afb355da2451cbd67d85443b83279 | - | - | - | - | 4694 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 4549 ms]

warning | 291 | 1753414733196 | 2025-07-25 11:38:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 54bc3fa658a940079026f518836ad796 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753414692024, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9868, jvmUsedMemory=0.4231, jvmMaxMemory=3.5557, jvmMemoryUsage=0.119, diskUsed=202.9896, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 291 | 1753414733203 | 2025-07-25 11:38:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 54bc3fa658a940079026f518836ad796 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753414733127, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0054, jvmUsedMemory=0.5943, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1671, diskUsed=202.9902, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 291 | 1753414733203 | 2025-07-25 11:38:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 54bc3fa658a940079026f518836ad796 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753414733164, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0054, jvmUsedMemory=0.5967, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1678, diskUsed=202.9902, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 291 | 1753414733204 | 2025-07-25 11:38:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | Actor | aroundReceive | 54bc3fa658a940079026f518836ad796 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753414733169, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0054, jvmUsedMemory=0.5979, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1682, diskUsed=202.9902, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 291 | 1753414733204 | 2025-07-25 11:38:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | Actor | aroundReceive | 54bc3fa658a940079026f518836ad796 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753414733170, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0054, jvmUsedMemory=0.5979, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1682, diskUsed=202.9902, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 133 | 1753414733258 | 2025-07-25 11:38:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | ae33bfd7b443403a905d2d0cadfd530b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 133 | 1753414733259 | 2025-07-25 11:38:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | ae33bfd7b443403a905d2d0cadfd530b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 133 | 1753414733262 | 2025-07-25 11:38:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | ae33bfd7b443403a905d2d0cadfd530b | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 133 | 1753414733264 | 2025-07-25 11:38:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | ae33bfd7b443403a905d2d0cadfd530b | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 133 | 1753414733264 | 2025-07-25 11:38:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | ae33bfd7b443403a905d2d0cadfd530b | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 41 | 1753414733862 | 2025-07-25 11:38:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | d21f68dace3442fab4373b7418797856 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 45 | 1753414733862 | 2025-07-25 11:38:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 2dccbb011305493992562bc79fb596d3 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 45 | 1753414733864 | 2025-07-25 11:38:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 2dccbb011305493992562bc79fb596d3 | - | - | - | - | 2 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753414733865 | 2025-07-25 11:38:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | d21f68dace3442fab4373b7418797856 | - | - | - | - | 2 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 133 | 1753414733909 | 2025-07-25 11:38:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | ae33bfd7b443403a905d2d0cadfd530b | - | - | - | - | 651 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 126 | 1753415467358 | 2025-07-25 11:51:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 2a5fe0c9534b437199bbc53623bf60ab | - | - | - | - | 105 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 126 | 1753415467358 | 2025-07-25 11:51:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 2a5fe0c9534b437199bbc53623bf60ab | - | - | - | - | 105 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 163 | 1753415605821 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor$Worker/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor$Worker | run | e2aced964daa485d9b05c607c6d2c370 | - | - | - | - | 13 | 0 | - | - | - | - | HikariPool-1 housekeeper c.z.h.p.HikariPool HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m17s788ms).

warning | 268 | 1753415605889 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415467918, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5015, jvmUsedMemory=0.4647, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1307, diskUsed=202.9976, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605894 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605823, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5869, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1651, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605895 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605854, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5889, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1656, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605896 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605857, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5893, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1657, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605896 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605859, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5908, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1662, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605897 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605860, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5912, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1663, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605897 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605863, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5912, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1663, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605897 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605864, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5912, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1663, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605901 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605865, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5912, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1663, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605901 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605866, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5916, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1664, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605901 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605867, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5927, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1667, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605901 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605869, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5931, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1668, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605902 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605870, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5935, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1669, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1753415605902 | 2025-07-25 11:53:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | Actor | aroundReceive | ad9b9da84af741bf8869c91a8c6417cd | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753415605873, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7437, jvmUsedMemory=0.5938, jvmMaxMemory=3.5557, jvmMemoryUsage=0.167, diskUsed=203.0002, diskTotal=460.4317, diskUsage=0.4409, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1753415605979 | 2025-07-25 11:53:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | f029cc773bd34166b1b4abcf0433ba80 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 270 | 1753415605980 | 2025-07-25 11:53:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | f029cc773bd34166b1b4abcf0433ba80 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 270 | 1753415605982 | 2025-07-25 11:53:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | f029cc773bd34166b1b4abcf0433ba80 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 270 | 1753415605985 | 2025-07-25 11:53:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | f029cc773bd34166b1b4abcf0433ba80 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 270 | 1753415605985 | 2025-07-25 11:53:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | f029cc773bd34166b1b4abcf0433ba80 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 230 | 1753415606144 | 2025-07-25 11:53:26 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 2ea0c4229771405899f320e6ee5a3ee4 | - | - | - | - | 133617 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 133447 ms]

warning | 45 | 1753415606886 | 2025-07-25 11:53:26 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 48c6a9d852ab40f18cfe703ac0e54d8f | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 45 | 1753415606888 | 2025-07-25 11:53:26 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 48c6a9d852ab40f18cfe703ac0e54d8f | - | - | - | - | 2 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753415606888 | 2025-07-25 11:53:26 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 6a8b2b2ad870448b96e2be629d130485 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 41 | 1753415606889 | 2025-07-25 11:53:26 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 6a8b2b2ad870448b96e2be629d130485 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 270 | 1753415606928 | 2025-07-25 11:53:26 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | f029cc773bd34166b1b4abcf0433ba80 | - | - | - | - | 949 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 125 | 1753415639060 | 2025-07-25 11:53:59 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | d41d2732adfb40e98c3fdc59eef43866 | - | - | - | - | 102 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 125 | 1753415639060 | 2025-07-25 11:53:59 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | d41d2732adfb40e98c3fdc59eef43866 | - | - | - | - | 102 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 231 | 1753415652686 | 2025-07-25 11:54:12 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 99bf11b1c9af4977a70c7975c0fad00c | - | - | - | - | 12732 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 12529 ms]

warning | 41 | 1753415653645 | 2025-07-25 11:54:13 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 91822804d6d946678fecc85762f9097f | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 45 | 1753415653644 | 2025-07-25 11:54:13 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 33853eeb0e3248e4baf878996aa32826 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 45 | 1753415653645 | 2025-07-25 11:54:13 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 33853eeb0e3248e4baf878996aa32826 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753415653646 | 2025-07-25 11:54:13 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 91822804d6d946678fecc85762f9097f | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 125 | 1753415653674 | 2025-07-25 11:54:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | d41d2732adfb40e98c3fdc59eef43866 | - | - | - | - | 14716 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 123 | 1753415688823 | 2025-07-25 11:54:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | b60b7dbe04f2479095653e2a0fc508e4 | - | - | - | - | 102 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 123 | 1753415688824 | 2025-07-25 11:54:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | b60b7dbe04f2479095653e2a0fc508e4 | - | - | - | - | 103 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 226 | 1753415693323 | 2025-07-25 11:54:53 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 6d48bfd24997440a816851a4f2668849 | - | - | - | - | 3758 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1129 ms]

warning | 45 | 1753415693702 | 2025-07-25 11:54:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 96916ed8aaf84c80981468f26d407bae | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 41 | 1753415693703 | 2025-07-25 11:54:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 59a7c44f4d764439aac8d928c04c9150 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 45 | 1753415693703 | 2025-07-25 11:54:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 96916ed8aaf84c80981468f26d407bae | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753415693706 | 2025-07-25 11:54:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 59a7c44f4d764439aac8d928c04c9150 | - | - | - | - | 3 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 131 | 1753415693741 | 2025-07-25 11:54:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | fb64f014eac240fabe1492f410192c6f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

