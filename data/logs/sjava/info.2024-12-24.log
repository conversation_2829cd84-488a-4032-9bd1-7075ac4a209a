info | 1 | 1735005350502 | 2024-12-24 09:55:50 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 074f114e11b8499395516a6442154e41 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 80865 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1735005350494 | 2024-12-24 09:55:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 4163c11473574ebd8881dddb1a7bf227 | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735005350514 | 2024-12-24 09:55:50 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 074f114e11b8499395516a6442154e41 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735010309766 | 2024-12-24 11:18:29 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 84668 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1735010309759 | 2024-12-24 11:18:29 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 72c8045465284921b325479a73a120dc | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735010309777 | 2024-12-24 11:18:29 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735010310246 | 2024-12-24 11:18:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 476 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735010310250 | 2024-12-24 11:18:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 479 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735010310252 | 2024-12-24 11:18:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 482 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735010310255 | 2024-12-24 11:18:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 484 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735010310268 | 2024-12-24 11:18:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 497 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735010310336 | 2024-12-24 11:18:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 565 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735010310374 | 2024-12-24 11:18:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 603 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735010310377 | 2024-12-24 11:18:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 606 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735010310377 | 2024-12-24 11:18:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 606 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735010310442 | 2024-12-24 11:18:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 671 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735010312368 | 2024-12-24 11:18:32 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 2598 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735010312370 | 2024-12-24 11:18:32 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 2599 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735010312408 | 2024-12-24 11:18:32 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 2638 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 33 ms. Found 0 JPA repository interfaces.

info | 1 | 1735010312417 | 2024-12-24 11:18:32 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 2647 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735010312418 | 2024-12-24 11:18:32 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 2647 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735010312444 | 2024-12-24 11:18:32 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 2673 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1735010313293 | 2024-12-24 11:18:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 3525 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$21b146bd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010313318 | 2024-12-24 11:18:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 3547 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a0b78c35] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010313376 | 2024-12-24 11:18:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 3605 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$1f2096fe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010313380 | 2024-12-24 11:18:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 3609 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010313442 | 2024-12-24 11:18:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 3671 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010313446 | 2024-12-24 11:18:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 3676 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010313988 | 2024-12-24 11:18:33 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 4218 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735010313997 | 2024-12-24 11:18:33 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 4226 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735010313997 | 2024-12-24 11:18:33 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 4226 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735010314075 | 2024-12-24 11:18:34 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 4304 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735010321022 | 2024-12-24 11:18:41 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 11251 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735010321080 | 2024-12-24 11:18:41 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 11310 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735010321127 | 2024-12-24 11:18:41 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 11356 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735010321241 | 2024-12-24 11:18:41 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 11470 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735010321334 | 2024-12-24 11:18:41 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 11564 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735010321467 | 2024-12-24 11:18:41 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 11696 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735010321475 | 2024-12-24 11:18:41 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 11704 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735010325528 | 2024-12-24 11:18:45 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 15760 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735010325851 | 2024-12-24 11:18:45 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 16081 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735010325879 | 2024-12-24 11:18:45 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 16108 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735010326252 | 2024-12-24 11:18:46 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a528b64747f04f88aab989428114bd35 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 109 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735010326373 | 2024-12-24 11:18:46 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a528b64747f04f88aab989428114bd35 | - | - | - | - | 121 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 67 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735010326415 | 2024-12-24 11:18:46 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a528b64747f04f88aab989428114bd35 | - | - | - | - | 164 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 38 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735010326670 | 2024-12-24 11:18:46 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a528b64747f04f88aab989428114bd35 | - | - | - | - | 418 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 249 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735010326676 | 2024-12-24 11:18:46 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a528b64747f04f88aab989428114bd35 | - | - | - | - | 424 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735010326684 | 2024-12-24 11:18:46 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a528b64747f04f88aab989428114bd35 | - | - | - | - | 432 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735010326691 | 2024-12-24 11:18:46 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a528b64747f04f88aab989428114bd35 | - | - | - | - | 439 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735010326851 | 2024-12-24 11:18:46 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a528b64747f04f88aab989428114bd35 | - | - | - | - | 599 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 158 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735010329488 | 2024-12-24 11:18:49 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 19717 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735010330296 | 2024-12-24 11:18:50 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20525 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@1f2da372 with [org.springframework.security.web.session.DisableEncodeUrlFilter@75c6032f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7587fa5b, org.springframework.security.web.context.SecurityContextPersistenceFilter@34ec0ebf, org.springframework.security.web.header.HeaderWriterFilter@43d921d3, org.springframework.security.web.authentication.logout.LogoutFilter@26dfd907, org.springframework.web.filter.CorsFilter@39dc0721, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@5adf6039, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@67ab93c4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3221ee78, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@29ff3af, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@15ef9a70, org.springframework.security.web.session.SessionManagementFilter@3d70ecb6, org.springframework.security.web.access.ExceptionTranslationFilter@14a689a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1d9edbad]

info | 1 | 1735010330312 | 2024-12-24 11:18:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20541 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735010330393 | 2024-12-24 11:18:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20622 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735010330395 | 2024-12-24 11:18:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20624 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735010330396 | 2024-12-24 11:18:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20625 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735010330398 | 2024-12-24 11:18:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20627 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735010330401 | 2024-12-24 11:18:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20630 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735010330401 | 2024-12-24 11:18:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20630 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735010330401 | 2024-12-24 11:18:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20630 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735010330581 | 2024-12-24 11:18:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20811 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735010330582 | 2024-12-24 11:18:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20811 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735010330582 | 2024-12-24 11:18:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20811 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735010330582 | 2024-12-24 11:18:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20811 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735010330638 | 2024-12-24 11:18:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20868 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735010330697 | 2024-12-24 11:18:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20926 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735010330698 | 2024-12-24 11:18:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20927 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735010330706 | 2024-12-24 11:18:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20935 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@1a3bebf9, tech.powerjob.worker.actors.ProcessorTrackerActor@6b8abde9, tech.powerjob.worker.actors.WorkerActor@6b238c47])

info | 1 | 1735010330742 | 2024-12-24 11:18:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20971 | 0 | - | - | - | - | main o.r.Reflections Reflections took 27 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735010330750 | 2024-12-24 11:18:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20979 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1735010330751 | 2024-12-24 11:18:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20981 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@6cd42516

info | 1 | 1735010330752 | 2024-12-24 11:18:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20981 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735010330752 | 2024-12-24 11:18:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20981 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735010330756 | 2024-12-24 11:18:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 20985 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 125 | 1735010331405 | 2024-12-24 11:18:51 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735010331800 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22029 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735010331801 | 2024-12-24 11:18:51 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22030 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735010331801 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22030 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735010331801 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735010331802 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22031 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735010331804 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22033 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735010331806 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22035 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735010331806 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22035 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735010331806 | 2024-12-24 11:18:51 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22036 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.055 s

info | 1 | 1735010331872 | 2024-12-24 11:18:51 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22101 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735010331876 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22105 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735010331880 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22109 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735010331883 | 2024-12-24 11:18:51 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22112 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735010332084 | 2024-12-24 11:18:52 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22313 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735010332085 | 2024-12-24 11:18:52 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22314 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/d8b47a113d4a4785af71fd7eeddcf5b4/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735010332093 | 2024-12-24 11:18:52 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22322 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/d8b47a113d4a4785af71fd7eeddcf5b4/] on JVM exit successfully

info | 1 | 1735010332110 | 2024-12-24 11:18:52 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22339 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735010332111 | 2024-12-24 11:18:52 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22340 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.718 s, congratulations!

info | 154 | 1735010332115 | 2024-12-24 11:18:52 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 154 | 1735010332115 | 2024-12-24 11:18:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735010332159 | 2024-12-24 11:18:52 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22388 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735010332187 | 2024-12-24 11:18:52 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22416 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735010332204 | 2024-12-24 11:18:52 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22433 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735010332204 | 2024-12-24 11:18:52 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22433 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735010332232 | 2024-12-24 11:18:52 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22461 | 0 | - | - | - | - | main c.t.g.Application Started Application in 22.956 seconds (JVM running for 23.479)

info | 1 | 1735010332245 | 2024-12-24 11:18:52 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22474 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735010332245 | 2024-12-24 11:18:52 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 8c15ebdd359c4767a980fa8504036d17 | - | - | - | - | 22475 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 224 | 1735010332249 | 2024-12-24 11:18:52 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 8174c2c211514cac9db08aed90d9ef50 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 154 | 1735010342115 | 2024-12-24 11:19:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010352114 | 2024-12-24 11:19:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 19999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010362113 | 2024-12-24 11:19:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 29999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010372115 | 2024-12-24 11:19:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 40001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010382112 | 2024-12-24 11:19:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 49997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010392112 | 2024-12-24 11:19:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 59997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010402115 | 2024-12-24 11:20:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 70001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735010410824 | 2024-12-24 11:20:10 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 512e8b6ff3164f1cabedcdfc7fe422fe | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 161 | 1735010410825 | 2024-12-24 11:20:10 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 512e8b6ff3164f1cabedcdfc7fe422fe | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 161 | 1735010410832 | 2024-12-24 11:20:10 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 512e8b6ff3164f1cabedcdfc7fe422fe | - | - | - | - | 7 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 7 ms

info | 154 | 1735010412115 | 2024-12-24 11:20:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 80000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010422115 | 2024-12-24 11:20:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 90000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010432112 | 2024-12-24 11:20:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 99998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010442115 | 2024-12-24 11:20:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 110001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010452116 | 2024-12-24 11:20:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 120001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010462117 | 2024-12-24 11:21:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 130002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010472117 | 2024-12-24 11:21:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 140003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 224 | 1735010475761 | 2024-12-24 11:21:15 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ChatRoomService | refreshChatCache | 8174c2c211514cac9db08aed90d9ef50 | - | - | - | - | 143513 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 154 | 1735010482118 | 2024-12-24 11:21:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 150003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010492116 | 2024-12-24 11:21:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 160001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010502117 | 2024-12-24 11:21:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 170003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010512116 | 2024-12-24 11:21:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 180002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010522117 | 2024-12-24 11:22:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 190003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010532117 | 2024-12-24 11:22:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 200002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010542115 | 2024-12-24 11:22:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 210000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010552115 | 2024-12-24 11:22:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 220001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735010562117 | 2024-12-24 11:22:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 230003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1735010571375 | 2024-12-24 11:22:51 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | 37f5a32836544f03b0cd6677ea4a12ea | - | - | - | - | 31 | 0 | - | - | - | - | http-nio-8087-exec-8 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=37f5a32836544f03b0cd6677ea4a12ea, request data=[0,null,**********,**********,null,null,[],null,0]

info | 168 | 1735010571408 | 2024-12-24 11:22:51 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | 37f5a32836544f03b0cd6677ea4a12ea | - | - | - | - | 65 | 0 | - | - | - | - | http-nio-8087-exec-8 c.t.c.c.c.SoaClient register http client : [http://live-api.test3.hbmonitor.com/v1/Soa/jService] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 154 | 1735010574051 | 2024-12-24 11:22:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 01447009c1b04952b14cd02f4e5ac658 | - | - | - | - | 241945 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1735010574318 | 2024-12-24 11:22:54 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | manageMultiLive | hostData | 37f5a32836544f03b0cd6677ea4a12ea | - | - | - | - | 2974 | 0 | - | - | - | - | http-nio-8087-exec-8 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[],\"total\":0}","dataBlank":false,"dataNotBlank":true}

info | 159 | 1735010582119 | 2024-12-24 11:23:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | c0d4ccb091ad451c80e85df6499f954a | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735010592118 | 2024-12-24 11:23:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | c0d4ccb091ad451c80e85df6499f954a | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735010602118 | 2024-12-24 11:23:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | d0616dc9fedf4d238d5503dc9c9dca76 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1735010609062 | 2024-12-24 11:23:29 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | 225bc7f257354feba433cbe2d35a17e9 | - | - | - | - | 13 | 0 | - | - | - | - | http-nio-8087-exec-9 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=225bc7f257354feba433cbe2d35a17e9, request data=[0,null,**********,**********,null,null,[],null,0]

info | 169 | 1735010609178 | 2024-12-24 11:23:29 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | 225bc7f257354feba433cbe2d35a17e9 | - | - | - | - | 130 | 0 | - | - | - | - | http-nio-8087-exec-9 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2024-10-21\",\"host_name\":\"xgh150\",\"host_uuid\":\"chchrm2jiv9o\",\"consortia_id\":\"110648\",\"total_live_duration\":\"6.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"大哥\",\"host_uuid\":\"bfqyp2l343rf\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"19.4\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"wkm\",\"host_uuid\":\"iop44ezeziy\",\"consortia_id\":\"1000309\",\"total_live_duration\":\"30.6\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":994680,\"room_amount\":994680,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":994680,\"consortia_amount\":994680,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh4\",\"host_uuid\":\"ccjbobv0oeal\",\"consortia_id\":\"110016\",\"total_live_duration\":\"11.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"163.8\",\"valid_up_meet_days\":1,\"total_amount\":20420,\"room_amount\":20000,\"room_split_amount\":0,\"meet_amount\":420,\"bind_room_amount\":20000,\"consortia_amount\":20000,\"other_consortia_amount\":420,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh3\",\"host_uuid\":\"cbhhdszbb1xm\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"574.7\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"8.3\",\"valid_up_meet_days\":1,\"total_amount\":8115100,\"room_amount\":7995100,\"room_split_amount\":0,\"meet_amount\":120000,\"bind_room_amount\":7995100,\"consortia_amount\":7995100,\"other_consortia_amount\":120000,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"szx080901\",\"host_uuid\":\"bgjbffbifbjdjgih\",\"consortia_id\":\"40\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":503,\"room_amount\":503,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":503,\"other_consortia_amount\":0,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":10684,\"room_amount\":186,\"room_split_amount\":12,\"meet_amount\":10486,\"bind_room_amount\":10498,\"consortia_amount\":10604,\"other_consortia_amount\":80,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5432,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5432,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":5432,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"110002\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5646,\"room_amount\":40,\"room_split_amount\":6,\"meet_amount\":5600,\"bind_room_amount\":0,\"consortia_amount\":46,\"other_consortia_amount\":5600,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5520,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5520,\"bind_room_amount\":206,\"consortia_amount\":5458,\"other_consortia_amount\":62,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5338,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5338,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":80,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女3\",\"host_uuid\":\"bggjbjbhjegbgfbe\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5418,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5418,\"bind_room_amount\":5298,\"consortia_amount\":5350,\"other_consortia_amount\":68,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女2\",\"host_uuid\":\"bggjbjbhhgabgfbd\",\"consortia_id\":\"40\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5392,\"room_amount\":100,\"room_split_amount\":6,\"meet_amount\":5286,\"bind_room_amount\":5206,\"consortia_amount\":106,\"other_consortia_amount\":5286,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"测试的的太热过饿也行\",\"host_uuid\":\"bggjbjbhfhbhafbc\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5452,\"room_amount\":5200,\"room_split_amount\":6,\"meet_amount\":246,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":194,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"自动创建4430\",\"host_uuid\":\"bhcjfiaiecfbhfib\",\"consortia_id\":\"110016\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"240.0\",\"valid_up_meet_days\":1,\"total_amount\":1512,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1200,\"bind_room_amount\":600,\"consortia_amount\":0,\"other_consortia_amount\":1512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":7020,\"room_amount\":640,\"room_split_amount\":5200,\"meet_amount\":1180,\"bind_room_amount\":12544,\"consortia_amount\":12532,\"other_consortia_amount\":-5512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1440.0\",\"valid_up_meet_days\":1,\"total_amount\":1306,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":1306,\"bind_room_amount\":540,\"consortia_amount\":0,\"other_consortia_amount\":1306,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"480.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":122,\"room_amount\":46,\"room_split_amount\":18,\"meet_amount\":58,\"bind_room_amount\":18,\"consortia_amount\":82,\"other_consortia_amount\":40,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"2160.0\",\"valid_up_meet_days\":1,\"total_amount\":1424,\"room_amount\":0,\"room_split_amount\":18,\"meet_amount\":1406,\"bind_room_amount\":896,\"consortia_amount\":1204,\"other_consortia_amount\":220,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":1888,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1576,\"bind_room_amount\":1906,\"consortia_amount\":1714,\"other_consortia_amount\":174,\"has_priv\":1}],\"total\":0}","dataBlank":false,"dataNotBlank":true}

info | 158 | 1735010612115 | 2024-12-24 11:23:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | d0616dc9fedf4d238d5503dc9c9dca76 | - | - | - | - | 9998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735010622541 | 2024-12-24 11:23:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | d0616dc9fedf4d238d5503dc9c9dca76 | - | - | - | - | 20423 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1735010622654 | 2024-12-24 11:23:42 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | bf3ba3aa234b449e8ea3fb787beac21c | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1735010622703 | 2024-12-24 11:23:42 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | bf3ba3aa234b449e8ea3fb787beac21c | - | - | - | - | 49 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1735010622704 | 2024-12-24 11:23:42 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | bf3ba3aa234b449e8ea3fb787beac21c | - | - | - | - | 50 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1735010622719 | 2024-12-24 11:23:42 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | AbstractApplicationContext | destroyBeans | bf3ba3aa234b449e8ea3fb787beac21c | - | - | - | - | 65 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 15 | 1735010641721 | 2024-12-24 11:24:01 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | ba582116f48f4317a4e93bda0a53c085 | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735010641728 | 2024-12-24 11:24:01 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 84922 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1735010641740 | 2024-12-24 11:24:01 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735010642243 | 2024-12-24 11:24:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 509 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735010642246 | 2024-12-24 11:24:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 512 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735010642249 | 2024-12-24 11:24:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 515 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735010642251 | 2024-12-24 11:24:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 517 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735010642255 | 2024-12-24 11:24:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 521 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735010642300 | 2024-12-24 11:24:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 566 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735010642332 | 2024-12-24 11:24:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 598 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735010642335 | 2024-12-24 11:24:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 601 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735010642335 | 2024-12-24 11:24:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 601 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735010642384 | 2024-12-24 11:24:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 650 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735010644412 | 2024-12-24 11:24:04 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 2678 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735010644413 | 2024-12-24 11:24:04 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 2680 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735010644455 | 2024-12-24 11:24:04 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 2721 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 36 ms. Found 0 JPA repository interfaces.

info | 1 | 1735010644466 | 2024-12-24 11:24:04 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 2732 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735010644467 | 2024-12-24 11:24:04 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 2733 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735010644494 | 2024-12-24 11:24:04 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 2760 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1735010645246 | 2024-12-24 11:24:05 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 3516 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$22c4c465] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010645275 | 2024-12-24 11:24:05 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 3542 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a1cb09dd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010645349 | 2024-12-24 11:24:05 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 3615 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$203414a6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010645354 | 2024-12-24 11:24:05 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 3620 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010645415 | 2024-12-24 11:24:05 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 3681 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010645419 | 2024-12-24 11:24:05 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 3685 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735010645953 | 2024-12-24 11:24:05 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 4219 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735010645965 | 2024-12-24 11:24:05 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 4232 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735010645966 | 2024-12-24 11:24:05 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 4232 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735010646041 | 2024-12-24 11:24:06 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 4307 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735010653186 | 2024-12-24 11:24:13 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 11453 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735010653263 | 2024-12-24 11:24:13 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 11529 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735010653307 | 2024-12-24 11:24:13 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 11573 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735010653478 | 2024-12-24 11:24:13 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 11744 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735010653557 | 2024-12-24 11:24:13 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 11823 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735010653688 | 2024-12-24 11:24:13 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 11954 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735010653694 | 2024-12-24 11:24:13 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 11961 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735010657157 | 2024-12-24 11:24:17 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 15424 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735010657452 | 2024-12-24 11:24:17 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 15718 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735010657480 | 2024-12-24 11:24:17 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 15746 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735010657639 | 2024-12-24 11:24:17 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b1526c52c7424aae992dca58fe3819d7 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735010657661 | 2024-12-24 11:24:17 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b1526c52c7424aae992dca58fe3819d7 | - | - | - | - | 22 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735010657669 | 2024-12-24 11:24:17 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b1526c52c7424aae992dca58fe3819d7 | - | - | - | - | 30 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735010657780 | 2024-12-24 11:24:17 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b1526c52c7424aae992dca58fe3819d7 | - | - | - | - | 141 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 109 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735010657786 | 2024-12-24 11:24:17 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b1526c52c7424aae992dca58fe3819d7 | - | - | - | - | 147 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735010657794 | 2024-12-24 11:24:17 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b1526c52c7424aae992dca58fe3819d7 | - | - | - | - | 155 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735010657801 | 2024-12-24 11:24:17 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b1526c52c7424aae992dca58fe3819d7 | - | - | - | - | 162 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735010657938 | 2024-12-24 11:24:17 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b1526c52c7424aae992dca58fe3819d7 | - | - | - | - | 299 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 134 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735010660445 | 2024-12-24 11:24:20 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 18711 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735010661155 | 2024-12-24 11:24:21 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19421 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@79dcfb0d with [org.springframework.security.web.session.DisableEncodeUrlFilter@37b8aab2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1f8585a5, org.springframework.security.web.context.SecurityContextPersistenceFilter@5f990556, org.springframework.security.web.header.HeaderWriterFilter@421e8dfa, org.springframework.security.web.authentication.logout.LogoutFilter@61b0b203, org.springframework.web.filter.CorsFilter@414f46af, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@3f0938c4, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@451e5f57, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@216a0542, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b238c47, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5b04d06a, org.springframework.security.web.session.SessionManagementFilter@5ab85072, org.springframework.security.web.access.ExceptionTranslationFilter@47d5e231, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@16f7cc4c]

info | 1 | 1735010661170 | 2024-12-24 11:24:21 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19436 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735010661244 | 2024-12-24 11:24:21 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19510 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735010661246 | 2024-12-24 11:24:21 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19512 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735010661247 | 2024-12-24 11:24:21 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19513 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735010661250 | 2024-12-24 11:24:21 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19516 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735010661252 | 2024-12-24 11:24:21 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19518 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735010661252 | 2024-12-24 11:24:21 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19519 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735010661253 | 2024-12-24 11:24:21 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19519 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735010661430 | 2024-12-24 11:24:21 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19696 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735010661430 | 2024-12-24 11:24:21 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19696 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735010661430 | 2024-12-24 11:24:21 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19696 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735010661430 | 2024-12-24 11:24:21 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19696 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735010661484 | 2024-12-24 11:24:21 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19751 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735010661549 | 2024-12-24 11:24:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19815 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735010661550 | 2024-12-24 11:24:21 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19816 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735010661560 | 2024-12-24 11:24:21 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19826 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@1ae721d6, tech.powerjob.worker.actors.ProcessorTrackerActor@7a28868f, tech.powerjob.worker.actors.WorkerActor@10a1d1b9])

info | 1 | 1735010661593 | 2024-12-24 11:24:21 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19860 | 0 | - | - | - | - | main o.r.Reflections Reflections took 23 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735010661604 | 2024-12-24 11:24:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19870 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1735010661605 | 2024-12-24 11:24:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19871 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@6ec5c03

info | 1 | 1735010661605 | 2024-12-24 11:24:21 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19871 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735010661605 | 2024-12-24 11:24:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19871 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735010661608 | 2024-12-24 11:24:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 19874 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 124 | 1735010662296 | 2024-12-24 11:24:22 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735010662676 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20943 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735010662677 | 2024-12-24 11:24:22 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20943 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735010662677 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20943 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735010662677 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735010662678 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20944 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735010662680 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20946 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735010662682 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20948 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735010662682 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20948 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735010662682 | 2024-12-24 11:24:22 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 20949 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.078 s

info | 1 | 1735010662750 | 2024-12-24 11:24:22 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21016 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735010662755 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21021 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735010662755 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21021 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735010662758 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21024 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735010662956 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21222 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735010662957 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21224 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/4e4d05df692a40d7a32f95857491c9f6/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735010662969 | 2024-12-24 11:24:22 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21235 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/4e4d05df692a40d7a32f95857491c9f6/] on JVM exit successfully

info | 1 | 1735010662988 | 2024-12-24 11:24:22 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21254 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735010662990 | 2024-12-24 11:24:22 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21256 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.746 s, congratulations!

info | 156 | 1735010662995 | 2024-12-24 11:24:22 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 156 | 1735010662996 | 2024-12-24 11:24:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735010663051 | 2024-12-24 11:24:23 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21317 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735010663079 | 2024-12-24 11:24:23 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21345 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735010663091 | 2024-12-24 11:24:23 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21358 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735010663092 | 2024-12-24 11:24:23 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21358 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735010663115 | 2024-12-24 11:24:23 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21381 | 0 | - | - | - | - | main c.t.g.Application Started Application in 21.782 seconds (JVM running for 22.294)

info | 1 | 1735010663133 | 2024-12-24 11:24:23 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21399 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735010663133 | 2024-12-24 11:24:23 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | b9308215e76e4ecb9d47f08bc03d161e | - | - | - | - | 21399 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 226 | 1735010663136 | 2024-12-24 11:24:23 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | dc700cc0f5e94b598ca14524edc2a28e | - | - | - | - | 1 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 156 | 1735010672995 | 2024-12-24 11:24:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735010680591 | 2024-12-24 11:24:40 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 04d2fd1b021b476989598b9ab2deb714 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 163 | 1735010680592 | 2024-12-24 11:24:40 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 04d2fd1b021b476989598b9ab2deb714 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 163 | 1735010680605 | 2024-12-24 11:24:40 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 04d2fd1b021b476989598b9ab2deb714 | - | - | - | - | 14 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 13 ms

info | 163 | 1735010680789 | 2024-12-24 11:24:40 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | 7b2366fc1ca54f67b4691d40e51f1dab | - | - | - | - | 179 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=7b2366fc1ca54f67b4691d40e51f1dab, request data=[0,null,**********,**********,null,null,[],null,0]

info | 163 | 1735010680798 | 2024-12-24 11:24:40 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | 7b2366fc1ca54f67b4691d40e51f1dab | - | - | - | - | 188 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.c.c.c.SoaClient register http client : [http://live-api.test3.hbmonitor.com/v1/Soa/jService] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 163 | 1735010681100 | 2024-12-24 11:24:41 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | manageMultiLive | hostData | 7b2366fc1ca54f67b4691d40e51f1dab | - | - | - | - | 490 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2024-10-21\",\"host_name\":\"xgh150\",\"host_uuid\":\"chchrm2jiv9o\",\"consortia_id\":\"110648\",\"total_live_duration\":\"6.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"大哥\",\"host_uuid\":\"bfqyp2l343rf\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"19.4\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"wkm\",\"host_uuid\":\"iop44ezeziy\",\"consortia_id\":\"1000309\",\"total_live_duration\":\"30.6\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":994680,\"room_amount\":994680,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":994680,\"consortia_amount\":994680,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh4\",\"host_uuid\":\"ccjbobv0oeal\",\"consortia_id\":\"110016\",\"total_live_duration\":\"11.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"163.8\",\"valid_up_meet_days\":1,\"total_amount\":20420,\"room_amount\":20000,\"room_split_amount\":0,\"meet_amount\":420,\"bind_room_amount\":20000,\"consortia_amount\":20000,\"other_consortia_amount\":420,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh3\",\"host_uuid\":\"cbhhdszbb1xm\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"574.7\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"8.3\",\"valid_up_meet_days\":1,\"total_amount\":8115100,\"room_amount\":7995100,\"room_split_amount\":0,\"meet_amount\":120000,\"bind_room_amount\":7995100,\"consortia_amount\":7995100,\"other_consortia_amount\":120000,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"szx080901\",\"host_uuid\":\"bgjbffbifbjdjgih\",\"consortia_id\":\"40\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":503,\"room_amount\":503,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":503,\"other_consortia_amount\":0,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":10684,\"room_amount\":186,\"room_split_amount\":12,\"meet_amount\":10486,\"bind_room_amount\":10498,\"consortia_amount\":10604,\"other_consortia_amount\":80,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5432,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5432,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":5432,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"110002\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5646,\"room_amount\":40,\"room_split_amount\":6,\"meet_amount\":5600,\"bind_room_amount\":0,\"consortia_amount\":46,\"other_consortia_amount\":5600,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5520,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5520,\"bind_room_amount\":206,\"consortia_amount\":5458,\"other_consortia_amount\":62,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5338,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5338,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":80,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女3\",\"host_uuid\":\"bggjbjbhjegbgfbe\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5418,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5418,\"bind_room_amount\":5298,\"consortia_amount\":5350,\"other_consortia_amount\":68,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女2\",\"host_uuid\":\"bggjbjbhhgabgfbd\",\"consortia_id\":\"40\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5392,\"room_amount\":100,\"room_split_amount\":6,\"meet_amount\":5286,\"bind_room_amount\":5206,\"consortia_amount\":106,\"other_consortia_amount\":5286,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"测试的的太热过饿也行\",\"host_uuid\":\"bggjbjbhfhbhafbc\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5452,\"room_amount\":5200,\"room_split_amount\":6,\"meet_amount\":246,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":194,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"自动创建4430\",\"host_uuid\":\"bhcjfiaiecfbhfib\",\"consortia_id\":\"110016\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"240.0\",\"valid_up_meet_days\":1,\"total_amount\":1512,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1200,\"bind_room_amount\":600,\"consortia_amount\":0,\"other_consortia_amount\":1512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":7020,\"room_amount\":640,\"room_split_amount\":5200,\"meet_amount\":1180,\"bind_room_amount\":12544,\"consortia_amount\":12532,\"other_consortia_amount\":-5512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1440.0\",\"valid_up_meet_days\":1,\"total_amount\":1306,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":1306,\"bind_room_amount\":540,\"consortia_amount\":0,\"other_consortia_amount\":1306,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"480.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":122,\"room_amount\":46,\"room_split_amount\":18,\"meet_amount\":58,\"bind_room_amount\":18,\"consortia_amount\":82,\"other_consortia_amount\":40,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"2160.0\",\"valid_up_meet_days\":1,\"total_amount\":1424,\"room_amount\":0,\"room_split_amount\":18,\"meet_amount\":1406,\"bind_room_amount\":896,\"consortia_amount\":1204,\"other_consortia_amount\":220,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":1888,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1576,\"bind_room_amount\":1906,\"consortia_amount\":1714,\"other_consortia_amount\":174,\"has_priv\":1}],\"total\":0}","dataBlank":false,"dataNotBlank":true}

info | 156 | 1735010682995 | 2024-12-24 11:24:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 20000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010692992 | 2024-12-24 11:24:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 29997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010702995 | 2024-12-24 11:25:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 40000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735010712994 | 2024-12-24 11:25:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735010722995 | 2024-12-24 11:25:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735010732995 | 2024-12-24 11:25:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 20001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1735010737875 | 2024-12-24 11:25:37 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | 0440cc5aed154275aae74e3edf83c6d4 | - | - | - | - | 13 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=0440cc5aed154275aae74e3edf83c6d4, request data=[0,null,**********,**********,null,null,[],null,0]

info | 165 | 1735010737996 | 2024-12-24 11:25:37 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | 0440cc5aed154275aae74e3edf83c6d4 | - | - | - | - | 134 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2024-10-21\",\"host_name\":\"xgh150\",\"host_uuid\":\"chchrm2jiv9o\",\"consortia_id\":\"110648\",\"total_live_duration\":\"6.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"大哥\",\"host_uuid\":\"bfqyp2l343rf\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"19.4\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"wkm\",\"host_uuid\":\"iop44ezeziy\",\"consortia_id\":\"1000309\",\"total_live_duration\":\"30.6\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":994680,\"room_amount\":994680,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":994680,\"consortia_amount\":994680,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh4\",\"host_uuid\":\"ccjbobv0oeal\",\"consortia_id\":\"110016\",\"total_live_duration\":\"11.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"163.8\",\"valid_up_meet_days\":1,\"total_amount\":20420,\"room_amount\":20000,\"room_split_amount\":0,\"meet_amount\":420,\"bind_room_amount\":20000,\"consortia_amount\":20000,\"other_consortia_amount\":420,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh3\",\"host_uuid\":\"cbhhdszbb1xm\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"574.7\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"8.3\",\"valid_up_meet_days\":1,\"total_amount\":8115100,\"room_amount\":7995100,\"room_split_amount\":0,\"meet_amount\":120000,\"bind_room_amount\":7995100,\"consortia_amount\":7995100,\"other_consortia_amount\":120000,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"szx080901\",\"host_uuid\":\"bgjbffbifbjdjgih\",\"consortia_id\":\"40\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":503,\"room_amount\":503,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":503,\"other_consortia_amount\":0,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":10684,\"room_amount\":186,\"room_split_amount\":12,\"meet_amount\":10486,\"bind_room_amount\":10498,\"consortia_amount\":10604,\"other_consortia_amount\":80,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5432,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5432,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":5432,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"110002\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5646,\"room_amount\":40,\"room_split_amount\":6,\"meet_amount\":5600,\"bind_room_amount\":0,\"consortia_amount\":46,\"other_consortia_amount\":5600,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5520,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5520,\"bind_room_amount\":206,\"consortia_amount\":5458,\"other_consortia_amount\":62,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5338,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5338,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":80,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女3\",\"host_uuid\":\"bggjbjbhjegbgfbe\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5418,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5418,\"bind_room_amount\":5298,\"consortia_amount\":5350,\"other_consortia_amount\":68,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女2\",\"host_uuid\":\"bggjbjbhhgabgfbd\",\"consortia_id\":\"40\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5392,\"room_amount\":100,\"room_split_amount\":6,\"meet_amount\":5286,\"bind_room_amount\":5206,\"consortia_amount\":106,\"other_consortia_amount\":5286,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"测试的的太热过饿也行\",\"host_uuid\":\"bggjbjbhfhbhafbc\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5452,\"room_amount\":5200,\"room_split_amount\":6,\"meet_amount\":246,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":194,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"自动创建4430\",\"host_uuid\":\"bhcjfiaiecfbhfib\",\"consortia_id\":\"110016\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"240.0\",\"valid_up_meet_days\":1,\"total_amount\":1512,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1200,\"bind_room_amount\":600,\"consortia_amount\":0,\"other_consortia_amount\":1512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":7020,\"room_amount\":640,\"room_split_amount\":5200,\"meet_amount\":1180,\"bind_room_amount\":12544,\"consortia_amount\":12532,\"other_consortia_amount\":-5512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1440.0\",\"valid_up_meet_days\":1,\"total_amount\":1306,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":1306,\"bind_room_amount\":540,\"consortia_amount\":0,\"other_consortia_amount\":1306,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"480.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":122,\"room_amount\":46,\"room_split_amount\":18,\"meet_amount\":58,\"bind_room_amount\":18,\"consortia_amount\":82,\"other_consortia_amount\":40,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"2160.0\",\"valid_up_meet_days\":1,\"total_amount\":1424,\"room_amount\":0,\"room_split_amount\":18,\"meet_amount\":1406,\"bind_room_amount\":896,\"consortia_amount\":1204,\"other_consortia_amount\":220,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":1888,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1576,\"bind_room_amount\":1906,\"consortia_amount\":1714,\"other_consortia_amount\":174,\"has_priv\":1}],\"total\":0}","dataBlank":false,"dataNotBlank":true}

info | 160 | 1735010742993 | 2024-12-24 11:25:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 29998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735010752994 | 2024-12-24 11:25:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 39999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735010762992 | 2024-12-24 11:26:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 49999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735010772992 | 2024-12-24 11:26:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 59998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735010782993 | 2024-12-24 11:26:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b027bc14af2a46e2b1cc6d8aadc10653 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735010792992 | 2024-12-24 11:26:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b027bc14af2a46e2b1cc6d8aadc10653 | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735010802993 | 2024-12-24 11:26:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b027bc14af2a46e2b1cc6d8aadc10653 | - | - | - | - | 20000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010812993 | 2024-12-24 11:26:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 149998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010822995 | 2024-12-24 11:27:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 160001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010832996 | 2024-12-24 11:27:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 170002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010842996 | 2024-12-24 11:27:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 180002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010852996 | 2024-12-24 11:27:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 190002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010862995 | 2024-12-24 11:27:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 200000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735010872994 | 2024-12-24 11:27:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b027bc14af2a46e2b1cc6d8aadc10653 | - | - | - | - | 90001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735010882996 | 2024-12-24 11:28:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b027bc14af2a46e2b1cc6d8aadc10653 | - | - | - | - | 100003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735010892995 | 2024-12-24 11:28:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b027bc14af2a46e2b1cc6d8aadc10653 | - | - | - | - | 110003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010902995 | 2024-12-24 11:28:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 240001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010912994 | 2024-12-24 11:28:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 250000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010922999 | 2024-12-24 11:28:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 260006 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010932993 | 2024-12-24 11:28:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 269999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010942993 | 2024-12-24 11:29:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 279998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010952994 | 2024-12-24 11:29:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 289999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735010962994 | 2024-12-24 11:29:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b027bc14af2a46e2b1cc6d8aadc10653 | - | - | - | - | 180001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735010972994 | 2024-12-24 11:29:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b027bc14af2a46e2b1cc6d8aadc10653 | - | - | - | - | 190001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735010982992 | 2024-12-24 11:29:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b027bc14af2a46e2b1cc6d8aadc10653 | - | - | - | - | 200000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735010992997 | 2024-12-24 11:29:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 330002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1735011000028 | 2024-12-24 11:30:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | 7567870db475494c86deab293181d785 | - | - | - | - | 0 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-12-24 11:24:17,450 to 2024-12-24 11:30:00,020
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 156 | 1735011002993 | 2024-12-24 11:30:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 339999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735011012995 | 2024-12-24 11:30:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 350000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735011022995 | 2024-12-24 11:30:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 360000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735011032993 | 2024-12-24 11:30:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 370000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735011042996 | 2024-12-24 11:30:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 380002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735011052994 | 2024-12-24 11:30:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 390001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735011062997 | 2024-12-24 11:31:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 350003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735011072995 | 2024-12-24 11:31:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 360001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735011082993 | 2024-12-24 11:31:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 369998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735011092997 | 2024-12-24 11:31:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 380002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735011102997 | 2024-12-24 11:31:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 390003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735011112998 | 2024-12-24 11:31:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | fe9c09aa717640ba98acd35c654d9f6c | - | - | - | - | 400004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735011122998 | 2024-12-24 11:32:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 460004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735011132994 | 2024-12-24 11:32:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 469999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735011142995 | 2024-12-24 11:32:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 480000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735011152997 | 2024-12-24 11:32:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 490002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735011162997 | 2024-12-24 11:32:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cd06efc757d040f7b44baf987d9056fc | - | - | - | - | 500005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1735011169794 | 2024-12-24 11:32:49 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | cc500f3398a247ae826a21ddd28523e7 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1735011169830 | 2024-12-24 11:32:49 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | cc500f3398a247ae826a21ddd28523e7 | - | - | - | - | 37 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1735011169833 | 2024-12-24 11:32:49 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | cc500f3398a247ae826a21ddd28523e7 | - | - | - | - | 39 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1735011169853 | 2024-12-24 11:32:49 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | AbstractApplicationContext | destroyBeans | cc500f3398a247ae826a21ddd28523e7 | - | - | - | - | 60 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 15 | 1735011174953 | 2024-12-24 11:32:54 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | e73cc7f5ecec46bf938edeeb876a1404 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735011174954 | 2024-12-24 11:32:54 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 85307 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1735011174969 | 2024-12-24 11:32:54 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735011175446 | 2024-12-24 11:32:55 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 482 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735011175449 | 2024-12-24 11:32:55 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 485 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735011175450 | 2024-12-24 11:32:55 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 487 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735011175452 | 2024-12-24 11:32:55 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 489 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735011175455 | 2024-12-24 11:32:55 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 491 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735011175499 | 2024-12-24 11:32:55 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 535 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735011175525 | 2024-12-24 11:32:55 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 561 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735011175527 | 2024-12-24 11:32:55 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 563 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735011175527 | 2024-12-24 11:32:55 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 563 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735011175591 | 2024-12-24 11:32:55 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 627 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735011177601 | 2024-12-24 11:32:57 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 2637 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735011177602 | 2024-12-24 11:32:57 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 2639 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735011177649 | 2024-12-24 11:32:57 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 2685 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 41 ms. Found 0 JPA repository interfaces.

info | 1 | 1735011177657 | 2024-12-24 11:32:57 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 2693 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735011177658 | 2024-12-24 11:32:57 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 2694 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735011177683 | 2024-12-24 11:32:57 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 2719 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1735011178391 | 2024-12-24 11:32:58 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 3430 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f62582] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011178411 | 2024-12-24 11:32:58 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 3447 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7ffc6afa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011178472 | 2024-12-24 11:32:58 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 3508 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$fe6575c3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011178477 | 2024-12-24 11:32:58 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 3513 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011178534 | 2024-12-24 11:32:58 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 3570 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011178538 | 2024-12-24 11:32:58 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 3574 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011179054 | 2024-12-24 11:32:59 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 4090 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735011179063 | 2024-12-24 11:32:59 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 4099 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735011179063 | 2024-12-24 11:32:59 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 4099 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735011179147 | 2024-12-24 11:32:59 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 4183 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735011186515 | 2024-12-24 11:33:06 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 11551 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735011186587 | 2024-12-24 11:33:06 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 11623 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735011186618 | 2024-12-24 11:33:06 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 11655 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735011186705 | 2024-12-24 11:33:06 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 11741 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735011186796 | 2024-12-24 11:33:06 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 11832 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735011186908 | 2024-12-24 11:33:06 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 11944 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735011186914 | 2024-12-24 11:33:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 11950 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735011190582 | 2024-12-24 11:33:10 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 15618 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735011190822 | 2024-12-24 11:33:10 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 15858 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735011190847 | 2024-12-24 11:33:10 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 15883 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735011191120 | 2024-12-24 11:33:11 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 38cbf98d49e94cb98faac4b08c33c9b0 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 77 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735011191151 | 2024-12-24 11:33:11 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 38cbf98d49e94cb98faac4b08c33c9b0 | - | - | - | - | 31 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735011191160 | 2024-12-24 11:33:11 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 38cbf98d49e94cb98faac4b08c33c9b0 | - | - | - | - | 40 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735011191273 | 2024-12-24 11:33:11 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 38cbf98d49e94cb98faac4b08c33c9b0 | - | - | - | - | 153 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 111 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735011191279 | 2024-12-24 11:33:11 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 38cbf98d49e94cb98faac4b08c33c9b0 | - | - | - | - | 159 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735011191287 | 2024-12-24 11:33:11 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 38cbf98d49e94cb98faac4b08c33c9b0 | - | - | - | - | 167 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735011191294 | 2024-12-24 11:33:11 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 38cbf98d49e94cb98faac4b08c33c9b0 | - | - | - | - | 175 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735011191394 | 2024-12-24 11:33:11 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 38cbf98d49e94cb98faac4b08c33c9b0 | - | - | - | - | 275 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 97 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735011193993 | 2024-12-24 11:33:13 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 19029 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735011195012 | 2024-12-24 11:33:15 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20048 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@f98d9b1 with [org.springframework.security.web.session.DisableEncodeUrlFilter@3daeaf18, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@60015b68, org.springframework.security.web.context.SecurityContextPersistenceFilter@209e457f, org.springframework.security.web.header.HeaderWriterFilter@a0d8640, org.springframework.security.web.authentication.logout.LogoutFilter@66728302, org.springframework.web.filter.CorsFilter@41758fc9, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@1c93530b, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@36542e6d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@50f53c99, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7974d89d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7fc83c47, org.springframework.security.web.session.SessionManagementFilter@bbe9eb4, org.springframework.security.web.access.ExceptionTranslationFilter@2c8ee744, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1878a144]

info | 1 | 1735011195033 | 2024-12-24 11:33:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20070 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735011195143 | 2024-12-24 11:33:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20179 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735011195146 | 2024-12-24 11:33:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20182 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735011195147 | 2024-12-24 11:33:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20183 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735011195150 | 2024-12-24 11:33:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20186 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735011195153 | 2024-12-24 11:33:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20189 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735011195153 | 2024-12-24 11:33:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20189 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735011195153 | 2024-12-24 11:33:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20189 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735011195406 | 2024-12-24 11:33:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20443 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735011195407 | 2024-12-24 11:33:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20443 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735011195407 | 2024-12-24 11:33:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20443 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735011195407 | 2024-12-24 11:33:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20443 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735011195462 | 2024-12-24 11:33:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20499 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735011195525 | 2024-12-24 11:33:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20561 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735011195526 | 2024-12-24 11:33:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20562 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735011195535 | 2024-12-24 11:33:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20571 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@79dcfb0d, tech.powerjob.worker.actors.ProcessorTrackerActor@37b8aab2, tech.powerjob.worker.actors.WorkerActor@1f8585a5])

info | 1 | 1735011195563 | 2024-12-24 11:33:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20599 | 0 | - | - | - | - | main o.r.Reflections Reflections took 19 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735011195568 | 2024-12-24 11:33:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20604 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1735011195569 | 2024-12-24 11:33:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20605 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@7b62b17c

info | 1 | 1735011195569 | 2024-12-24 11:33:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20605 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@ef5a994

info | 1 | 1735011195569 | 2024-12-24 11:33:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20605 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735011195570 | 2024-12-24 11:33:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20606 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735011195573 | 2024-12-24 11:33:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 20609 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 124 | 1735011196043 | 2024-12-24 11:33:16 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735011196485 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21521 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735011196485 | 2024-12-24 11:33:16 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735011196485 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735011196485 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735011196486 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735011196486 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735011196486 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735011196486 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735011196486 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735011196486 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735011196486 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735011196486 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735011196486 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21523 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735011196487 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21523 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735011196487 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21523 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735011196488 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21524 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735011196489 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21526 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735011196490 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21526 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735011196490 | 2024-12-24 11:33:16 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21527 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 920.9 ms

info | 1 | 1735011196552 | 2024-12-24 11:33:16 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21588 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735011196556 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21593 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735011196557 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21593 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735011196559 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21595 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735011196754 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21791 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735011196755 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21791 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/65ccf35e8c2148c78455f571306fe101/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735011196763 | 2024-12-24 11:33:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21799 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/65ccf35e8c2148c78455f571306fe101/] on JVM exit successfully

info | 1 | 1735011196781 | 2024-12-24 11:33:16 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21817 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735011196782 | 2024-12-24 11:33:16 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21819 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.640 s, congratulations!

info | 155 | 1735011196787 | 2024-12-24 11:33:16 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | ca3c1988c57847c6b00081e72e5054bc | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 155 | 1735011196788 | 2024-12-24 11:33:16 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ca3c1988c57847c6b00081e72e5054bc | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735011196841 | 2024-12-24 11:33:16 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | TomcatWebServer | start | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21877 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735011196884 | 2024-12-24 11:33:16 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21921 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735011196901 | 2024-12-24 11:33:16 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21938 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735011196902 | 2024-12-24 11:33:16 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21938 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735011196937 | 2024-12-24 11:33:16 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 21974 | 0 | - | - | - | - | main c.t.g.Application Started Application in 22.492 seconds (JVM running for 22.93)

info | 1 | 1735011196972 | 2024-12-24 11:33:16 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 22009 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735011196973 | 2024-12-24 11:33:16 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | Application | main | 871cf6c2a6ef4481960d72afb5a03c6b | - | - | - | - | 22009 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 225 | 1735011196979 | 2024-12-24 11:33:16 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | d0f529bda86147349528c88f02dd2d3c | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 155 | 1735011206788 | 2024-12-24 11:33:26 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ca3c1988c57847c6b00081e72e5054bc | - | - | - | - | 10002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735011216787 | 2024-12-24 11:33:36 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ca3c1988c57847c6b00081e72e5054bc | - | - | - | - | 20000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735011226788 | 2024-12-24 11:33:46 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ca3c1988c57847c6b00081e72e5054bc | - | - | - | - | 30002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735011236783 | 2024-12-24 11:33:56 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ca3c1988c57847c6b00081e72e5054bc | - | - | - | - | 39997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735011246788 | 2024-12-24 11:34:06 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ca3c1988c57847c6b00081e72e5054bc | - | - | - | - | 50002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735011256788 | 2024-12-24 11:34:16 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ca3c1988c57847c6b00081e72e5054bc | - | - | - | - | 60002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735011265278 | 2024-12-24 11:34:25 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | ca99cad8f77b425290110038e0706abb | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 163 | 1735011265278 | 2024-12-24 11:34:25 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | ca99cad8f77b425290110038e0706abb | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 163 | 1735011265285 | 2024-12-24 11:34:25 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | ca99cad8f77b425290110038e0706abb | - | - | - | - | 8 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 7 ms

info | 163 | 1735011265447 | 2024-12-24 11:34:25 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | 18e7c0d52422474f97d6483cb4709542 | - | - | - | - | 153 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=18e7c0d52422474f97d6483cb4709542, request data=[0,null,**********,**********,null,null,[],null,0]

info | 163 | 1735011265460 | 2024-12-24 11:34:25 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | 18e7c0d52422474f97d6483cb4709542 | - | - | - | - | 165 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.c.c.c.SoaClient register http client : [http://live-api.test3.hbmonitor.com/v1/Soa/jService] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 163 | 1735011265675 | 2024-12-24 11:34:25 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | manageMultiLive | hostData | 18e7c0d52422474f97d6483cb4709542 | - | - | - | - | 380 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2024-10-21\",\"host_name\":\"xgh150\",\"host_uuid\":\"chchrm2jiv9o\",\"consortia_id\":\"110648\",\"total_live_duration\":\"6.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"大哥\",\"host_uuid\":\"bfqyp2l343rf\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"19.4\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"wkm\",\"host_uuid\":\"iop44ezeziy\",\"consortia_id\":\"1000309\",\"total_live_duration\":\"30.6\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":994680,\"room_amount\":994680,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":994680,\"consortia_amount\":994680,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh4\",\"host_uuid\":\"ccjbobv0oeal\",\"consortia_id\":\"110016\",\"total_live_duration\":\"11.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"163.8\",\"valid_up_meet_days\":1,\"total_amount\":20420,\"room_amount\":20000,\"room_split_amount\":0,\"meet_amount\":420,\"bind_room_amount\":20000,\"consortia_amount\":20000,\"other_consortia_amount\":420,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh3\",\"host_uuid\":\"cbhhdszbb1xm\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"574.7\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"8.3\",\"valid_up_meet_days\":1,\"total_amount\":8115100,\"room_amount\":7995100,\"room_split_amount\":0,\"meet_amount\":120000,\"bind_room_amount\":7995100,\"consortia_amount\":7995100,\"other_consortia_amount\":120000,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"szx080901\",\"host_uuid\":\"bgjbffbifbjdjgih\",\"consortia_id\":\"40\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":503,\"room_amount\":503,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":503,\"other_consortia_amount\":0,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":10684,\"room_amount\":186,\"room_split_amount\":12,\"meet_amount\":10486,\"bind_room_amount\":10498,\"consortia_amount\":10604,\"other_consortia_amount\":80,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5432,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5432,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":5432,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"110002\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5646,\"room_amount\":40,\"room_split_amount\":6,\"meet_amount\":5600,\"bind_room_amount\":0,\"consortia_amount\":46,\"other_consortia_amount\":5600,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5520,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5520,\"bind_room_amount\":206,\"consortia_amount\":5458,\"other_consortia_amount\":62,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5338,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5338,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":80,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女3\",\"host_uuid\":\"bggjbjbhjegbgfbe\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5418,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5418,\"bind_room_amount\":5298,\"consortia_amount\":5350,\"other_consortia_amount\":68,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女2\",\"host_uuid\":\"bggjbjbhhgabgfbd\",\"consortia_id\":\"40\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5392,\"room_amount\":100,\"room_split_amount\":6,\"meet_amount\":5286,\"bind_room_amount\":5206,\"consortia_amount\":106,\"other_consortia_amount\":5286,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"测试的的太热过饿也行\",\"host_uuid\":\"bggjbjbhfhbhafbc\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5452,\"room_amount\":5200,\"room_split_amount\":6,\"meet_amount\":246,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":194,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"自动创建4430\",\"host_uuid\":\"bhcjfiaiecfbhfib\",\"consortia_id\":\"110016\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"240.0\",\"valid_up_meet_days\":1,\"total_amount\":1512,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1200,\"bind_room_amount\":600,\"consortia_amount\":0,\"other_consortia_amount\":1512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":7020,\"room_amount\":640,\"room_split_amount\":5200,\"meet_amount\":1180,\"bind_room_amount\":12544,\"consortia_amount\":12532,\"other_consortia_amount\":-5512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1440.0\",\"valid_up_meet_days\":1,\"total_amount\":1306,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":1306,\"bind_room_amount\":540,\"consortia_amount\":0,\"other_consortia_amount\":1306,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"480.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":122,\"room_amount\":46,\"room_split_amount\":18,\"meet_amount\":58,\"bind_room_amount\":18,\"consortia_amount\":82,\"other_consortia_amount\":40,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"2160.0\",\"valid_up_meet_days\":1,\"total_amount\":1424,\"room_amount\":0,\"room_split_amount\":18,\"meet_amount\":1406,\"bind_room_amount\":896,\"consortia_amount\":1204,\"other_consortia_amount\":220,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":1888,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1576,\"bind_room_amount\":1906,\"consortia_amount\":1714,\"other_consortia_amount\":174,\"has_priv\":1}],\"total\":0}","dataBlank":false,"dataNotBlank":true}

info | 160 | 1735011271320 | 2024-12-24 11:34:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | da9f5aab09dc43b386e429e3bdbe8571 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735011276784 | 2024-12-24 11:34:36 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | da9f5aab09dc43b386e429e3bdbe8571 | - | - | - | - | 5464 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735011286784 | 2024-12-24 11:34:46 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | da9f5aab09dc43b386e429e3bdbe8571 | - | - | - | - | 15463 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1735011296481 | 2024-12-24 11:34:56 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 114894e15eac4ab7af8e3be86f70de2e | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1735011296536 | 2024-12-24 11:34:56 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 114894e15eac4ab7af8e3be86f70de2e | - | - | - | - | 55 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1735011296540 | 2024-12-24 11:34:56 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | 114894e15eac4ab7af8e3be86f70de2e | - | - | - | - | 59 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 15 | 1735011299992 | 2024-12-24 11:35:00 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 0242129d5f9f48d394b94c05acb39200 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735011299996 | 2024-12-24 11:35:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 85408 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1735011300009 | 2024-12-24 11:35:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735011300537 | 2024-12-24 11:35:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 534 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735011300542 | 2024-12-24 11:35:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 539 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735011300547 | 2024-12-24 11:35:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 544 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735011300553 | 2024-12-24 11:35:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 549 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735011300556 | 2024-12-24 11:35:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 553 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735011300615 | 2024-12-24 11:35:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 611 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735011300652 | 2024-12-24 11:35:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 649 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735011300655 | 2024-12-24 11:35:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 651 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735011300655 | 2024-12-24 11:35:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 651 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735011300714 | 2024-12-24 11:35:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 711 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735011302789 | 2024-12-24 11:35:02 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 2785 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735011302791 | 2024-12-24 11:35:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 2787 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735011302829 | 2024-12-24 11:35:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 2825 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.

info | 1 | 1735011302836 | 2024-12-24 11:35:02 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 2833 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735011302838 | 2024-12-24 11:35:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 2834 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735011302860 | 2024-12-24 11:35:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 2856 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.

info | 1 | 1735011303524 | 2024-12-24 11:35:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 3523 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$dc32f2be] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011303549 | 2024-12-24 11:35:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 3546 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$5b393836] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011303624 | 2024-12-24 11:35:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 3620 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$d9a242ff] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011303628 | 2024-12-24 11:35:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 3625 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011303683 | 2024-12-24 11:35:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 3680 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011303687 | 2024-12-24 11:35:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 3683 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011304167 | 2024-12-24 11:35:04 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 4164 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735011304179 | 2024-12-24 11:35:04 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 4175 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735011304180 | 2024-12-24 11:35:04 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 4176 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735011304257 | 2024-12-24 11:35:04 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 4253 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735011311657 | 2024-12-24 11:35:11 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 11653 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735011311716 | 2024-12-24 11:35:11 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 11712 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735011311747 | 2024-12-24 11:35:11 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 11743 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735011311846 | 2024-12-24 11:35:11 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 11842 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735011311917 | 2024-12-24 11:35:11 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 11914 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735011312018 | 2024-12-24 11:35:12 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 12014 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735011312024 | 2024-12-24 11:35:12 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 12020 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735011315997 | 2024-12-24 11:35:15 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 15993 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735011316259 | 2024-12-24 11:35:16 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 16255 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735011316281 | 2024-12-24 11:35:16 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 16277 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 1 | 1735011316434 | 2024-12-24 11:35:16 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 16431 | 0 | - | - | - | - | main o.r.Reflections Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 

info | 1 | 1735011316456 | 2024-12-24 11:35:16 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 16452 | 0 | - | - | - | - | main o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 1 | 1735011316464 | 2024-12-24 11:35:16 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 16460 | 0 | - | - | - | - | main o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 1 | 1735011316572 | 2024-12-24 11:35:16 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 16568 | 0 | - | - | - | - | main o.r.Reflections Reflections took 105 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735011316577 | 2024-12-24 11:35:16 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 16573 | 0 | - | - | - | - | main o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 1 | 1735011316584 | 2024-12-24 11:35:16 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 16580 | 0 | - | - | - | - | main o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 1 | 1735011316591 | 2024-12-24 11:35:16 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 16587 | 0 | - | - | - | - | main o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 1 | 1735011316697 | 2024-12-24 11:35:16 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 16693 | 0 | - | - | - | - | main o.r.Reflections Reflections took 104 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735011319081 | 2024-12-24 11:35:19 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 19078 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735011320006 | 2024-12-24 11:35:20 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20002 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@4249978b with [org.springframework.security.web.session.DisableEncodeUrlFilter@81eba6b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@40250ddd, org.springframework.security.web.context.SecurityContextPersistenceFilter@67b2bcbd, org.springframework.security.web.header.HeaderWriterFilter@44dce520, org.springframework.security.web.authentication.logout.LogoutFilter@628e4c7d, org.springframework.web.filter.CorsFilter@2fc58f15, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@70c1a887, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@715059be, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5c51366c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@26af5f16, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7f2b6dd0, org.springframework.security.web.session.SessionManagementFilter@8ec040, org.springframework.security.web.access.ExceptionTranslationFilter@370a3e08, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2edcc5f0]

info | 1 | 1735011320026 | 2024-12-24 11:35:20 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20022 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735011320105 | 2024-12-24 11:35:20 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20102 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735011320107 | 2024-12-24 11:35:20 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20103 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735011320108 | 2024-12-24 11:35:20 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20104 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735011320110 | 2024-12-24 11:35:20 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20106 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735011320112 | 2024-12-24 11:35:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20108 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735011320112 | 2024-12-24 11:35:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20108 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735011320112 | 2024-12-24 11:35:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20109 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735011320327 | 2024-12-24 11:35:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20323 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735011320328 | 2024-12-24 11:35:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20324 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735011320328 | 2024-12-24 11:35:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20324 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735011320328 | 2024-12-24 11:35:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20324 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735011320383 | 2024-12-24 11:35:20 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20379 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735011320443 | 2024-12-24 11:35:20 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20439 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735011320444 | 2024-12-24 11:35:20 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20440 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735011320452 | 2024-12-24 11:35:20 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20449 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@4da51c08, tech.powerjob.worker.actors.ProcessorTrackerActor@6d0c95d3, tech.powerjob.worker.actors.WorkerActor@73051634])

info | 1 | 1735011320478 | 2024-12-24 11:35:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20475 | 0 | - | - | - | - | main o.r.Reflections Reflections took 18 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735011320483 | 2024-12-24 11:35:20 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20479 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1735011320484 | 2024-12-24 11:35:20 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20480 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@5553f7f7

info | 1 | 1735011320484 | 2024-12-24 11:35:20 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20480 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735011320484 | 2024-12-24 11:35:20 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20481 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735011320488 | 2024-12-24 11:35:20 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 20484 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 124 | 1735011320891 | 2024-12-24 11:35:20 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735011321469 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21465 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735011321470 | 2024-12-24 11:35:21 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21466 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735011321470 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21466 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735011321470 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21466 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735011321470 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21466 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735011321471 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21467 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735011321471 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21467 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735011321471 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21467 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735011321471 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21467 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735011321471 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21467 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735011321471 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21467 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735011321471 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21467 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735011321471 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21467 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735011321471 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21467 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735011321471 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21467 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735011321473 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21469 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735011321475 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21471 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735011321475 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21471 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735011321476 | 2024-12-24 11:35:21 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21473 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 992.5 ms

info | 1 | 1735011321537 | 2024-12-24 11:35:21 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21533 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735011321541 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21537 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735011321541 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21537 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735011321543 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21539 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735011321703 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21699 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735011321704 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21700 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/6be3624cef8a40ceb3b48c612c6aa000/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735011321709 | 2024-12-24 11:35:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21705 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/6be3624cef8a40ceb3b48c612c6aa000/] on JVM exit successfully

info | 1 | 1735011321722 | 2024-12-24 11:35:21 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21718 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735011321723 | 2024-12-24 11:35:21 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21719 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.619 s, congratulations!

info | 157 | 1735011321726 | 2024-12-24 11:35:21 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 066826d3ef2b49689f12354d5eab8698 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 157 | 1735011321726 | 2024-12-24 11:35:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 066826d3ef2b49689f12354d5eab8698 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735011321773 | 2024-12-24 11:35:21 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.639 | *************** | - | 2 | TomcatWebServer | start | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21770 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735011321850 | 2024-12-24 11:35:21 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.641 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21847 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735011321866 | 2024-12-24 11:35:21 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.643 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21862 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735011321866 | 2024-12-24 11:35:21 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.645 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21862 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735011321892 | 2024-12-24 11:35:21 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.647 | *************** | - | 2 | Application | main | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21888 | 0 | - | - | - | - | main c.t.g.Application Started Application in 22.358 seconds (JVM running for 22.773)

info | 1 | 1735011321923 | 2024-12-24 11:35:21 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.649 | *************** | - | 2 | Application | main | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21919 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735011321923 | 2024-12-24 11:35:21 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.651 | *************** | - | 2 | Application | main | 47bed49bcdc84f31bc0810e89882f9eb | - | - | - | - | 21920 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 227 | 1735011321928 | 2024-12-24 11:35:21 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 3ab72fd003854f1081c520741085a0a0 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 165 | 1735011328414 | 2024-12-24 11:35:28 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 859012dd28f1485bafc376c587b741d0 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 165 | 1735011328415 | 2024-12-24 11:35:28 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 859012dd28f1485bafc376c587b741d0 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 165 | 1735011328418 | 2024-12-24 11:35:28 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 859012dd28f1485bafc376c587b741d0 | - | - | - | - | 4 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 3 ms

info | 165 | 1735011328584 | 2024-12-24 11:35:28 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | 78e0ca13ffe449aba17f22f5ed792d4e | - | - | - | - | 162 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=78e0ca13ffe449aba17f22f5ed792d4e, request data=[0,null,**********,**********,null,null,[],null,0]

info | 165 | 1735011328591 | 2024-12-24 11:35:28 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | 78e0ca13ffe449aba17f22f5ed792d4e | - | - | - | - | 168 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.c.c.c.SoaClient register http client : [http://live-api.test3.hbmonitor.com/v1/Soa/jService] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 165 | 1735011328892 | 2024-12-24 11:35:28 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | manageMultiLive | hostData | 78e0ca13ffe449aba17f22f5ed792d4e | - | - | - | - | 469 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2024-10-21\",\"host_name\":\"xgh150\",\"host_uuid\":\"chchrm2jiv9o\",\"consortia_id\":\"110648\",\"total_live_duration\":\"6.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"大哥\",\"host_uuid\":\"bfqyp2l343rf\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"19.4\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"wkm\",\"host_uuid\":\"iop44ezeziy\",\"consortia_id\":\"1000309\",\"total_live_duration\":\"30.6\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":994680,\"room_amount\":994680,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":994680,\"consortia_amount\":994680,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh4\",\"host_uuid\":\"ccjbobv0oeal\",\"consortia_id\":\"110016\",\"total_live_duration\":\"11.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"163.8\",\"valid_up_meet_days\":1,\"total_amount\":20420,\"room_amount\":20000,\"room_split_amount\":0,\"meet_amount\":420,\"bind_room_amount\":20000,\"consortia_amount\":20000,\"other_consortia_amount\":420,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh3\",\"host_uuid\":\"cbhhdszbb1xm\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"574.7\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"8.3\",\"valid_up_meet_days\":1,\"total_amount\":8115100,\"room_amount\":7995100,\"room_split_amount\":0,\"meet_amount\":120000,\"bind_room_amount\":7995100,\"consortia_amount\":7995100,\"other_consortia_amount\":120000,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"szx080901\",\"host_uuid\":\"bgjbffbifbjdjgih\",\"consortia_id\":\"40\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":503,\"room_amount\":503,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":503,\"other_consortia_amount\":0,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":10684,\"room_amount\":186,\"room_split_amount\":12,\"meet_amount\":10486,\"bind_room_amount\":10498,\"consortia_amount\":10604,\"other_consortia_amount\":80,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5432,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5432,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":5432,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"110002\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5646,\"room_amount\":40,\"room_split_amount\":6,\"meet_amount\":5600,\"bind_room_amount\":0,\"consortia_amount\":46,\"other_consortia_amount\":5600,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5520,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5520,\"bind_room_amount\":206,\"consortia_amount\":5458,\"other_consortia_amount\":62,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5338,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5338,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":80,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女3\",\"host_uuid\":\"bggjbjbhjegbgfbe\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5418,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5418,\"bind_room_amount\":5298,\"consortia_amount\":5350,\"other_consortia_amount\":68,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女2\",\"host_uuid\":\"bggjbjbhhgabgfbd\",\"consortia_id\":\"40\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5392,\"room_amount\":100,\"room_split_amount\":6,\"meet_amount\":5286,\"bind_room_amount\":5206,\"consortia_amount\":106,\"other_consortia_amount\":5286,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"测试的的太热过饿也行\",\"host_uuid\":\"bggjbjbhfhbhafbc\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5452,\"room_amount\":5200,\"room_split_amount\":6,\"meet_amount\":246,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":194,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"自动创建4430\",\"host_uuid\":\"bhcjfiaiecfbhfib\",\"consortia_id\":\"110016\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"240.0\",\"valid_up_meet_days\":1,\"total_amount\":1512,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1200,\"bind_room_amount\":600,\"consortia_amount\":0,\"other_consortia_amount\":1512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":7020,\"room_amount\":640,\"room_split_amount\":5200,\"meet_amount\":1180,\"bind_room_amount\":12544,\"consortia_amount\":12532,\"other_consortia_amount\":-5512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1440.0\",\"valid_up_meet_days\":1,\"total_amount\":1306,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":1306,\"bind_room_amount\":540,\"consortia_amount\":0,\"other_consortia_amount\":1306,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"480.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":122,\"room_amount\":46,\"room_split_amount\":18,\"meet_amount\":58,\"bind_room_amount\":18,\"consortia_amount\":82,\"other_consortia_amount\":40,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"2160.0\",\"valid_up_meet_days\":1,\"total_amount\":1424,\"room_amount\":0,\"room_split_amount\":18,\"meet_amount\":1406,\"bind_room_amount\":896,\"consortia_amount\":1204,\"other_consortia_amount\":220,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":1888,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1576,\"bind_room_amount\":1906,\"consortia_amount\":1714,\"other_consortia_amount\":174,\"has_priv\":1}],\"total\":0}","dataBlank":false,"dataNotBlank":true}

info | 157 | 1735011332638 | 2024-12-24 11:35:32 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 066826d3ef2b49689f12354d5eab8698 | - | - | - | - | 10921 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735011353348 | 2024-12-24 11:35:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 06d92dce3f014f09811bd5d43e9ee16f | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735011366838 | 2024-12-24 11:36:06 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 06d92dce3f014f09811bd5d43e9ee16f | - | - | - | - | 12757 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735011366843 | 2024-12-24 11:36:06 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 06d92dce3f014f09811bd5d43e9ee16f | - | - | - | - | 12761 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735011376973 | 2024-12-24 11:36:16 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 06d92dce3f014f09811bd5d43e9ee16f | - | - | - | - | 22891 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735011382129 | 2024-12-24 11:36:22 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 06d92dce3f014f09811bd5d43e9ee16f | - | - | - | - | 28047 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735011395268 | 2024-12-24 11:36:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 6c5b6e8ccea44b57846fd6463f7f5c3b | - | - | - | - | 1312 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735011402498 | 2024-12-24 11:36:42 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 066826d3ef2b49689f12354d5eab8698 | - | - | - | - | 80772 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735011419848 | 2024-12-24 11:36:59 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 066826d3ef2b49689f12354d5eab8698 | - | - | - | - | 98122 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735011424612 | 2024-12-24 11:37:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 066826d3ef2b49689f12354d5eab8698 | - | - | - | - | 102886 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735011431714 | 2024-12-24 11:37:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 06d92dce3f014f09811bd5d43e9ee16f | - | - | - | - | 77632 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735011441717 | 2024-12-24 11:37:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 06d92dce3f014f09811bd5d43e9ee16f | - | - | - | - | 87635 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735011451715 | 2024-12-24 11:37:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 06d92dce3f014f09811bd5d43e9ee16f | - | - | - | - | 97633 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735011461714 | 2024-12-24 11:37:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 06d92dce3f014f09811bd5d43e9ee16f | - | - | - | - | 107632 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735011471714 | 2024-12-24 11:37:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 06d92dce3f014f09811bd5d43e9ee16f | - | - | - | - | 117633 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1735011473582 | 2024-12-24 11:37:53 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 9d0dcf45152d46618f99434df5cc6ead | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1735011473651 | 2024-12-24 11:37:53 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 9d0dcf45152d46618f99434df5cc6ead | - | - | - | - | 69 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1735011473655 | 2024-12-24 11:37:53 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | 9d0dcf45152d46618f99434df5cc6ead | - | - | - | - | 73 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1735011473677 | 2024-12-24 11:37:53 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | AbstractApplicationContext | destroyBeans | 9d0dcf45152d46618f99434df5cc6ead | - | - | - | - | 96 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 15 | 1735011477292 | 2024-12-24 11:37:57 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | c91c69ddfba84ea2973c690d4fd21319 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735011477297 | 2024-12-24 11:37:57 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 85599 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1735011477310 | 2024-12-24 11:37:57 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735011477775 | 2024-12-24 11:37:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 470 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735011477777 | 2024-12-24 11:37:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 472 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735011477779 | 2024-12-24 11:37:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 475 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735011477782 | 2024-12-24 11:37:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 478 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735011477784 | 2024-12-24 11:37:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 479 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735011477819 | 2024-12-24 11:37:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 514 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735011477848 | 2024-12-24 11:37:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 543 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735011477850 | 2024-12-24 11:37:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 545 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735011477850 | 2024-12-24 11:37:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 545 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735011477900 | 2024-12-24 11:37:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 595 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735011480013 | 2024-12-24 11:38:00 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 2708 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735011480014 | 2024-12-24 11:38:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 2709 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735011480054 | 2024-12-24 11:38:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 2749 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.

info | 1 | 1735011480062 | 2024-12-24 11:38:00 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 2757 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735011480063 | 2024-12-24 11:38:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 2758 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735011480089 | 2024-12-24 11:38:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 2784 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.

info | 1 | 1735011480892 | 2024-12-24 11:38:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 3587 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$5693cfc9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011480914 | 2024-12-24 11:38:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 3609 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d59a1541] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011480986 | 2024-12-24 11:38:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 3682 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$5403200a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011480992 | 2024-12-24 11:38:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 3687 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011481055 | 2024-12-24 11:38:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 3750 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011481060 | 2024-12-24 11:38:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 3755 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011482039 | 2024-12-24 11:38:02 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 4735 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735011482056 | 2024-12-24 11:38:02 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 4751 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735011482057 | 2024-12-24 11:38:02 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 4752 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735011482172 | 2024-12-24 11:38:02 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 4867 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735011489619 | 2024-12-24 11:38:09 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 12314 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735011489701 | 2024-12-24 11:38:09 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 12396 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735011489734 | 2024-12-24 11:38:09 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 12429 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735011489824 | 2024-12-24 11:38:09 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 12519 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735011489910 | 2024-12-24 11:38:09 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 12605 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735011490019 | 2024-12-24 11:38:10 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 12715 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735011490025 | 2024-12-24 11:38:10 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 12720 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735011494361 | 2024-12-24 11:38:14 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 17057 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735011494646 | 2024-12-24 11:38:14 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 17341 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735011494672 | 2024-12-24 11:38:14 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 17367 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735011494854 | 2024-12-24 11:38:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 5a368084626b4d68a3c90d7789f695db | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735011494901 | 2024-12-24 11:38:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 5a368084626b4d68a3c90d7789f695db | - | - | - | - | 46 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735011494913 | 2024-12-24 11:38:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 5a368084626b4d68a3c90d7789f695db | - | - | - | - | 59 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735011495046 | 2024-12-24 11:38:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 5a368084626b4d68a3c90d7789f695db | - | - | - | - | 191 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 130 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735011495052 | 2024-12-24 11:38:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 5a368084626b4d68a3c90d7789f695db | - | - | - | - | 198 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735011495061 | 2024-12-24 11:38:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 5a368084626b4d68a3c90d7789f695db | - | - | - | - | 206 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735011495094 | 2024-12-24 11:38:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 5a368084626b4d68a3c90d7789f695db | - | - | - | - | 239 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735011495266 | 2024-12-24 11:38:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 5a368084626b4d68a3c90d7789f695db | - | - | - | - | 411 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 164 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735011497741 | 2024-12-24 11:38:17 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 20436 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735011498493 | 2024-12-24 11:38:18 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21188 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@50a90e19 with [org.springframework.security.web.session.DisableEncodeUrlFilter@21a77782, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3b58d2cb, org.springframework.security.web.context.SecurityContextPersistenceFilter@5f990556, org.springframework.security.web.header.HeaderWriterFilter@421e8dfa, org.springframework.security.web.authentication.logout.LogoutFilter@374aa0a8, org.springframework.web.filter.CorsFilter@74121a9, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@35cca8bd, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@e5ca0bd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@216a0542, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@28e6d65f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@169daea9, org.springframework.security.web.session.SessionManagementFilter@5ab85072, org.springframework.security.web.access.ExceptionTranslationFilter@47d5e231, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1a000cb9]

info | 1 | 1735011498507 | 2024-12-24 11:38:18 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21202 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735011498570 | 2024-12-24 11:38:18 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21265 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735011498572 | 2024-12-24 11:38:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21267 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735011498573 | 2024-12-24 11:38:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21268 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735011498575 | 2024-12-24 11:38:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21270 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735011498577 | 2024-12-24 11:38:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21272 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735011498578 | 2024-12-24 11:38:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21273 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735011498578 | 2024-12-24 11:38:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21273 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735011498724 | 2024-12-24 11:38:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21419 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735011498724 | 2024-12-24 11:38:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21419 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735011498724 | 2024-12-24 11:38:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21420 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735011498725 | 2024-12-24 11:38:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21420 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735011498779 | 2024-12-24 11:38:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21474 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735011498838 | 2024-12-24 11:38:18 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21534 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735011498839 | 2024-12-24 11:38:18 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21534 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735011498849 | 2024-12-24 11:38:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21544 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@7a28868f, tech.powerjob.worker.actors.ProcessorTrackerActor@10a1d1b9, tech.powerjob.worker.actors.WorkerActor@3d363e8e])

info | 1 | 1735011498899 | 2024-12-24 11:38:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21594 | 0 | - | - | - | - | main o.r.Reflections Reflections took 41 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735011498906 | 2024-12-24 11:38:18 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21601 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1735011498919 | 2024-12-24 11:38:18 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21614 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@3a64c797

info | 1 | 1735011498919 | 2024-12-24 11:38:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21614 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735011498920 | 2024-12-24 11:38:18 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21615 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735011498923 | 2024-12-24 11:38:18 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 21618 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 124 | 1735011499363 | 2024-12-24 11:38:19 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735011499704 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22399 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735011499704 | 2024-12-24 11:38:19 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22399 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735011499705 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735011499706 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22401 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735011499707 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22402 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735011499709 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22404 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735011499709 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22404 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735011499710 | 2024-12-24 11:38:19 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22405 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 790.6 ms

info | 1 | 1735011499771 | 2024-12-24 11:38:19 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22466 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735011499776 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22471 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735011499776 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22471 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735011499779 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22474 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735011499957 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22652 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735011499957 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22652 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/d8797a0d28894a98bf24c3276d5674ec/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735011499964 | 2024-12-24 11:38:19 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22660 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/d8797a0d28894a98bf24c3276d5674ec/] on JVM exit successfully

info | 1 | 1735011499982 | 2024-12-24 11:38:19 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22677 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735011499983 | 2024-12-24 11:38:19 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22678 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.413 s, congratulations!

info | 155 | 1735011499988 | 2024-12-24 11:38:19 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 055a9fd8799b487a95bd8353fe351c39 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 155 | 1735011499989 | 2024-12-24 11:38:19 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 055a9fd8799b487a95bd8353fe351c39 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735011500035 | 2024-12-24 11:38:20 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22730 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735011500073 | 2024-12-24 11:38:20 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22768 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735011500101 | 2024-12-24 11:38:20 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22796 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735011500101 | 2024-12-24 11:38:20 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22796 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735011500129 | 2024-12-24 11:38:20 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22824 | 0 | - | - | - | - | main c.t.g.Application Started Application in 23.245 seconds (JVM running for 23.704)

info | 1 | 1735011500151 | 2024-12-24 11:38:20 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22846 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735011500152 | 2024-12-24 11:38:20 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | a598b170ef8d443db5e183e978d37933 | - | - | - | - | 22847 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 225 | 1735011500156 | 2024-12-24 11:38:20 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 8700907c0dcb47a9983a9b1c0858945f | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 162 | 1735011509761 | 2024-12-24 11:38:29 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | b8b5b48948f3434b96a2a2eb18626576 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 162 | 1735011509762 | 2024-12-24 11:38:29 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | b8b5b48948f3434b96a2a2eb18626576 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 162 | 1735011509764 | 2024-12-24 11:38:29 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | b8b5b48948f3434b96a2a2eb18626576 | - | - | - | - | 2 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 2 ms

info | 162 | 1735011509960 | 2024-12-24 11:38:29 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | c4f229e94cea4634abfab409b8239bb8 | - | - | - | - | 191 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=c4f229e94cea4634abfab409b8239bb8, request data=[0,null,**********,**********,null,null,[],null,0]

info | 162 | 1735011509969 | 2024-12-24 11:38:29 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | c4f229e94cea4634abfab409b8239bb8 | - | - | - | - | 200 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.c.c.c.SoaClient register http client : [http://live-api.test3.hbmonitor.com/v1/Soa/jService] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 155 | 1735011509988 | 2024-12-24 11:38:29 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 055a9fd8799b487a95bd8353fe351c39 | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735011510194 | 2024-12-24 11:38:30 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | manageMultiLive | hostData | c4f229e94cea4634abfab409b8239bb8 | - | - | - | - | 425 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2024-10-21\",\"host_name\":\"xgh150\",\"host_uuid\":\"chchrm2jiv9o\",\"consortia_id\":\"110648\",\"total_live_duration\":\"6.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"大哥\",\"host_uuid\":\"bfqyp2l343rf\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"19.4\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"wkm\",\"host_uuid\":\"iop44ezeziy\",\"consortia_id\":\"1000309\",\"total_live_duration\":\"30.6\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":994680,\"room_amount\":994680,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":994680,\"consortia_amount\":994680,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh4\",\"host_uuid\":\"ccjbobv0oeal\",\"consortia_id\":\"110016\",\"total_live_duration\":\"11.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"163.8\",\"valid_up_meet_days\":1,\"total_amount\":20420,\"room_amount\":20000,\"room_split_amount\":0,\"meet_amount\":420,\"bind_room_amount\":20000,\"consortia_amount\":20000,\"other_consortia_amount\":420,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh3\",\"host_uuid\":\"cbhhdszbb1xm\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"574.7\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"8.3\",\"valid_up_meet_days\":1,\"total_amount\":8115100,\"room_amount\":7995100,\"room_split_amount\":0,\"meet_amount\":120000,\"bind_room_amount\":7995100,\"consortia_amount\":7995100,\"other_consortia_amount\":120000,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"szx080901\",\"host_uuid\":\"bgjbffbifbjdjgih\",\"consortia_id\":\"40\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":503,\"room_amount\":503,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":503,\"other_consortia_amount\":0,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":10684,\"room_amount\":186,\"room_split_amount\":12,\"meet_amount\":10486,\"bind_room_amount\":10498,\"consortia_amount\":10604,\"other_consortia_amount\":80,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5432,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5432,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":5432,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"110002\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5646,\"room_amount\":40,\"room_split_amount\":6,\"meet_amount\":5600,\"bind_room_amount\":0,\"consortia_amount\":46,\"other_consortia_amount\":5600,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5520,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5520,\"bind_room_amount\":206,\"consortia_amount\":5458,\"other_consortia_amount\":62,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5338,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5338,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":80,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女3\",\"host_uuid\":\"bggjbjbhjegbgfbe\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5418,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5418,\"bind_room_amount\":5298,\"consortia_amount\":5350,\"other_consortia_amount\":68,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女2\",\"host_uuid\":\"bggjbjbhhgabgfbd\",\"consortia_id\":\"40\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5392,\"room_amount\":100,\"room_split_amount\":6,\"meet_amount\":5286,\"bind_room_amount\":5206,\"consortia_amount\":106,\"other_consortia_amount\":5286,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"测试的的太热过饿也行\",\"host_uuid\":\"bggjbjbhfhbhafbc\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5452,\"room_amount\":5200,\"room_split_amount\":6,\"meet_amount\":246,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":194,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"自动创建4430\",\"host_uuid\":\"bhcjfiaiecfbhfib\",\"consortia_id\":\"110016\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"240.0\",\"valid_up_meet_days\":1,\"total_amount\":1512,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1200,\"bind_room_amount\":600,\"consortia_amount\":0,\"other_consortia_amount\":1512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":7020,\"room_amount\":640,\"room_split_amount\":5200,\"meet_amount\":1180,\"bind_room_amount\":12544,\"consortia_amount\":12532,\"other_consortia_amount\":-5512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1440.0\",\"valid_up_meet_days\":1,\"total_amount\":1306,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":1306,\"bind_room_amount\":540,\"consortia_amount\":0,\"other_consortia_amount\":1306,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"480.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":122,\"room_amount\":46,\"room_split_amount\":18,\"meet_amount\":58,\"bind_room_amount\":18,\"consortia_amount\":82,\"other_consortia_amount\":40,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"2160.0\",\"valid_up_meet_days\":1,\"total_amount\":1424,\"room_amount\":0,\"room_split_amount\":18,\"meet_amount\":1406,\"bind_room_amount\":896,\"consortia_amount\":1204,\"other_consortia_amount\":220,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":1888,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1576,\"bind_room_amount\":1906,\"consortia_amount\":1714,\"other_consortia_amount\":174,\"has_priv\":1}],\"total\":0}","dataBlank":false,"dataNotBlank":true}

info | 155 | 1735011519988 | 2024-12-24 11:38:39 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 055a9fd8799b487a95bd8353fe351c39 | - | - | - | - | 20001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735011529984 | 2024-12-24 11:38:49 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 055a9fd8799b487a95bd8353fe351c39 | - | - | - | - | 29996 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735011539989 | 2024-12-24 11:38:59 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 055a9fd8799b487a95bd8353fe351c39 | - | - | - | - | 40001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735011546652 | 2024-12-24 11:39:06 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | 26b6d8034e074d359effe4d78fc0a54d | - | - | - | - | 16 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=26b6d8034e074d359effe4d78fc0a54d, request data=[0,null,**********,**********,null,null,[],null,0]

info | 163 | 1735011546777 | 2024-12-24 11:39:06 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | 26b6d8034e074d359effe4d78fc0a54d | - | - | - | - | 142 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2024-10-21\",\"host_name\":\"xgh150\",\"host_uuid\":\"chchrm2jiv9o\",\"consortia_id\":\"110648\",\"total_live_duration\":\"6.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"大哥\",\"host_uuid\":\"bfqyp2l343rf\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"19.4\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"wkm\",\"host_uuid\":\"iop44ezeziy\",\"consortia_id\":\"1000309\",\"total_live_duration\":\"30.6\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":994680,\"room_amount\":994680,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":994680,\"consortia_amount\":994680,\"other_consortia_amount\":0,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh4\",\"host_uuid\":\"ccjbobv0oeal\",\"consortia_id\":\"110016\",\"total_live_duration\":\"11.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"163.8\",\"valid_up_meet_days\":1,\"total_amount\":20420,\"room_amount\":20000,\"room_split_amount\":0,\"meet_amount\":420,\"bind_room_amount\":20000,\"consortia_amount\":20000,\"other_consortia_amount\":420,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"xgh3\",\"host_uuid\":\"cbhhdszbb1xm\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"574.7\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"8.3\",\"valid_up_meet_days\":1,\"total_amount\":8115100,\"room_amount\":7995100,\"room_split_amount\":0,\"meet_amount\":120000,\"bind_room_amount\":7995100,\"consortia_amount\":7995100,\"other_consortia_amount\":120000,\"has_priv\":1},{\"time\":\"2024-10-21\",\"host_name\":\"szx080901\",\"host_uuid\":\"bgjbffbifbjdjgih\",\"consortia_id\":\"40\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":503,\"room_amount\":503,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":503,\"other_consortia_amount\":0,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":10684,\"room_amount\":186,\"room_split_amount\":12,\"meet_amount\":10486,\"bind_room_amount\":10498,\"consortia_amount\":10604,\"other_consortia_amount\":80,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5432,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5432,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":5432,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"110002\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5646,\"room_amount\":40,\"room_split_amount\":6,\"meet_amount\":5600,\"bind_room_amount\":0,\"consortia_amount\":46,\"other_consortia_amount\":5600,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5520,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5520,\"bind_room_amount\":206,\"consortia_amount\":5458,\"other_consortia_amount\":62,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5338,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5338,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":80,\"has_priv\":0},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女3\",\"host_uuid\":\"bggjbjbhjegbgfbe\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5418,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5418,\"bind_room_amount\":5298,\"consortia_amount\":5350,\"other_consortia_amount\":68,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女2\",\"host_uuid\":\"bggjbjbhhgabgfbd\",\"consortia_id\":\"40\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5392,\"room_amount\":100,\"room_split_amount\":6,\"meet_amount\":5286,\"bind_room_amount\":5206,\"consortia_amount\":106,\"other_consortia_amount\":5286,\"has_priv\":1},{\"time\":\"2024-10-20\",\"host_name\":\"测试的的太热过饿也行\",\"host_uuid\":\"bggjbjbhfhbhafbc\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5452,\"room_amount\":5200,\"room_split_amount\":6,\"meet_amount\":246,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":194,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"自动创建4430\",\"host_uuid\":\"bhcjfiaiecfbhfib\",\"consortia_id\":\"110016\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"240.0\",\"valid_up_meet_days\":1,\"total_amount\":1512,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1200,\"bind_room_amount\":600,\"consortia_amount\":0,\"other_consortia_amount\":1512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":7020,\"room_amount\":640,\"room_split_amount\":5200,\"meet_amount\":1180,\"bind_room_amount\":12544,\"consortia_amount\":12532,\"other_consortia_amount\":-5512,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1440.0\",\"valid_up_meet_days\":1,\"total_amount\":1306,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":1306,\"bind_room_amount\":540,\"consortia_amount\":0,\"other_consortia_amount\":1306,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"480.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":122,\"room_amount\":46,\"room_split_amount\":18,\"meet_amount\":58,\"bind_room_amount\":18,\"consortia_amount\":82,\"other_consortia_amount\":40,\"has_priv\":1},{\"time\":\"2024-10-19\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"2160.0\",\"valid_up_meet_days\":1,\"total_amount\":1424,\"room_amount\":0,\"room_split_amount\":18,\"meet_amount\":1406,\"bind_room_amount\":896,\"consortia_amount\":1204,\"other_consortia_amount\":220,\"has_priv\":0},{\"time\":\"2024-10-19\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":1888,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1576,\"bind_room_amount\":1906,\"consortia_amount\":1714,\"other_consortia_amount\":174,\"has_priv\":1}],\"total\":0}","dataBlank":false,"dataNotBlank":true}

info | 155 | 1735011549987 | 2024-12-24 11:39:09 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 055a9fd8799b487a95bd8353fe351c39 | - | - | - | - | 49999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735011559988 | 2024-12-24 11:39:19 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 58aa652643ba46b1a088deda5da09529 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1735011567471 | 2024-12-24 11:39:27 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | e41124026ddf4837a0a7b2b984f5542e | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1735011567508 | 2024-12-24 11:39:27 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | e41124026ddf4837a0a7b2b984f5542e | - | - | - | - | 36 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1735011567509 | 2024-12-24 11:39:27 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | e41124026ddf4837a0a7b2b984f5542e | - | - | - | - | 37 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1735011567518 | 2024-12-24 11:39:27 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | AbstractApplicationContext | destroyBeans | e41124026ddf4837a0a7b2b984f5542e | - | - | - | - | 47 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 15 | 1735011570633 | 2024-12-24 11:39:30 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 69080fe2b13d4aeebed9ffc0f9c2d1cc | - | - | - | - | 3 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735011570639 | 2024-12-24 11:39:30 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 3 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 85697 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1735011570649 | 2024-12-24 11:39:30 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735011571111 | 2024-12-24 11:39:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 467 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735011571114 | 2024-12-24 11:39:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 469 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735011571116 | 2024-12-24 11:39:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 471 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735011571118 | 2024-12-24 11:39:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 473 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735011571119 | 2024-12-24 11:39:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 474 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735011571159 | 2024-12-24 11:39:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 515 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735011571186 | 2024-12-24 11:39:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 541 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735011571188 | 2024-12-24 11:39:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 543 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735011571188 | 2024-12-24 11:39:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 543 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735011571236 | 2024-12-24 11:39:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 592 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735011573269 | 2024-12-24 11:39:33 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 2624 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735011573270 | 2024-12-24 11:39:33 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 2625 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735011573309 | 2024-12-24 11:39:33 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 2665 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.

info | 1 | 1735011573317 | 2024-12-24 11:39:33 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 2672 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735011573318 | 2024-12-24 11:39:33 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 2673 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735011573345 | 2024-12-24 11:39:33 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 2700 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.

info | 1 | 1735011574085 | 2024-12-24 11:39:34 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 3440 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$d3601707] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011574110 | 2024-12-24 11:39:34 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 3465 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$52665c7f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011574203 | 2024-12-24 11:39:34 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 3558 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$d0cf6748] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011574208 | 2024-12-24 11:39:34 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 3563 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011574315 | 2024-12-24 11:39:34 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 3671 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011574326 | 2024-12-24 11:39:34 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 3682 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011574830 | 2024-12-24 11:39:34 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 4185 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735011574844 | 2024-12-24 11:39:34 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 4199 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735011574844 | 2024-12-24 11:39:34 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 4199 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735011574919 | 2024-12-24 11:39:34 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 4274 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735011581907 | 2024-12-24 11:39:41 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 11263 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735011581974 | 2024-12-24 11:39:41 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 11330 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735011582009 | 2024-12-24 11:39:42 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 11364 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735011582102 | 2024-12-24 11:39:42 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 11457 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735011582190 | 2024-12-24 11:39:42 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 11546 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735011582407 | 2024-12-24 11:39:42 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 11762 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735011582415 | 2024-12-24 11:39:42 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 11770 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735011587082 | 2024-12-24 11:39:47 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 16438 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735011587472 | 2024-12-24 11:39:47 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 16827 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735011587498 | 2024-12-24 11:39:47 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 16853 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735011587662 | 2024-12-24 11:39:47 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c3fce4834d8d4a9eabde346547c7fcf3 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735011587687 | 2024-12-24 11:39:47 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c3fce4834d8d4a9eabde346547c7fcf3 | - | - | - | - | 24 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735011587700 | 2024-12-24 11:39:47 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c3fce4834d8d4a9eabde346547c7fcf3 | - | - | - | - | 37 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735011587830 | 2024-12-24 11:39:47 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c3fce4834d8d4a9eabde346547c7fcf3 | - | - | - | - | 167 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 128 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735011587837 | 2024-12-24 11:39:47 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c3fce4834d8d4a9eabde346547c7fcf3 | - | - | - | - | 175 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735011587850 | 2024-12-24 11:39:47 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c3fce4834d8d4a9eabde346547c7fcf3 | - | - | - | - | 187 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735011587860 | 2024-12-24 11:39:47 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c3fce4834d8d4a9eabde346547c7fcf3 | - | - | - | - | 197 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735011587989 | 2024-12-24 11:39:47 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c3fce4834d8d4a9eabde346547c7fcf3 | - | - | - | - | 326 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 126 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735011590510 | 2024-12-24 11:39:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 19865 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735011591525 | 2024-12-24 11:39:51 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 20880 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@74c7ba2a with [org.springframework.security.web.session.DisableEncodeUrlFilter@1be219b8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@34cd7055, org.springframework.security.web.context.SecurityContextPersistenceFilter@58d450b7, org.springframework.security.web.header.HeaderWriterFilter@41e95d06, org.springframework.security.web.authentication.logout.LogoutFilter@6de3442f, org.springframework.web.filter.CorsFilter@6066c4ff, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@3ac662ba, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@55ba69f3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2c716cd0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2ff74dc2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1ce58f84, org.springframework.security.web.session.SessionManagementFilter@81e6f3, org.springframework.security.web.access.ExceptionTranslationFilter@5928fb8b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@161722eb]

info | 1 | 1735011591581 | 2024-12-24 11:39:51 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 20936 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735011591694 | 2024-12-24 11:39:51 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21049 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735011591695 | 2024-12-24 11:39:51 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21051 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735011591698 | 2024-12-24 11:39:51 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21053 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735011591701 | 2024-12-24 11:39:51 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21056 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735011591704 | 2024-12-24 11:39:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21059 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735011591704 | 2024-12-24 11:39:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21059 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735011591704 | 2024-12-24 11:39:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21059 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735011591972 | 2024-12-24 11:39:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21327 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735011591972 | 2024-12-24 11:39:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21327 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735011591972 | 2024-12-24 11:39:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21327 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735011591972 | 2024-12-24 11:39:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21327 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735011592128 | 2024-12-24 11:39:52 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21484 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735011592277 | 2024-12-24 11:39:52 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21633 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735011592278 | 2024-12-24 11:39:52 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21633 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735011592287 | 2024-12-24 11:39:52 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21642 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@73cae081, tech.powerjob.worker.actors.ProcessorTrackerActor@6cd6b98b, tech.powerjob.worker.actors.WorkerActor@7532c8a8])

info | 1 | 1735011592315 | 2024-12-24 11:39:52 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21670 | 0 | - | - | - | - | main o.r.Reflections Reflections took 20 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735011592321 | 2024-12-24 11:39:52 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21676 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1735011592322 | 2024-12-24 11:39:52 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21677 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@3949a26a

info | 1 | 1735011592322 | 2024-12-24 11:39:52 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21677 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735011592322 | 2024-12-24 11:39:52 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21677 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735011592329 | 2024-12-24 11:39:52 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 21684 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 125 | 1735011592785 | 2024-12-24 11:39:52 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735011593348 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22703 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735011593348 | 2024-12-24 11:39:53 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22703 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735011593349 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22704 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735011593349 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22704 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735011593349 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22704 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735011593349 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22705 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735011593350 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22705 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735011593350 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22705 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735011593350 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22705 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735011593350 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22705 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735011593350 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22705 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735011593350 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22705 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735011593350 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22705 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735011593350 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22705 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735011593351 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22706 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735011593356 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22711 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735011593359 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22714 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735011593359 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22714 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735011593360 | 2024-12-24 11:39:53 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22715 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.038 s

info | 1 | 1735011593518 | 2024-12-24 11:39:53 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22873 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735011593526 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22881 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735011593527 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22882 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735011593532 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 22887 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735011593719 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23074 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735011593719 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23074 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/6cbb3cb370464af89ef14ab7d3065d1c/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735011593724 | 2024-12-24 11:39:53 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23079 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/6cbb3cb370464af89ef14ab7d3065d1c/] on JVM exit successfully

info | 1 | 1735011593738 | 2024-12-24 11:39:53 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23093 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735011593738 | 2024-12-24 11:39:53 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23094 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 2.045 s, congratulations!

info | 162 | 1735011593744 | 2024-12-24 11:39:53 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 2da2b2814f794555842ef90ec3cbf7e6 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 162 | 1735011593744 | 2024-12-24 11:39:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2da2b2814f794555842ef90ec3cbf7e6 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735011593781 | 2024-12-24 11:39:53 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23137 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735011593805 | 2024-12-24 11:39:53 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23160 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735011593820 | 2024-12-24 11:39:53 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23175 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735011593821 | 2024-12-24 11:39:53 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23176 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735011593850 | 2024-12-24 11:39:53 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23205 | 0 | - | - | - | - | main c.t.g.Application Started Application in 23.675 seconds (JVM running for 24.075)

info | 1 | 1735011593869 | 2024-12-24 11:39:53 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23224 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735011593869 | 2024-12-24 11:39:53 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 1c0e05a0f01b47f0933928411e390778 | - | - | - | - | 23224 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 232 | 1735011593873 | 2024-12-24 11:39:53 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | c4b049d0bebd4bf190c2d22c0153841e | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 162 | 1735011603743 | 2024-12-24 11:40:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2da2b2814f794555842ef90ec3cbf7e6 | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1735011613166 | 2024-12-24 11:40:13 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 63070c09cb584574b81e0acaad1ea68f | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1735011613210 | 2024-12-24 11:40:13 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 63070c09cb584574b81e0acaad1ea68f | - | - | - | - | 44 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1735011613214 | 2024-12-24 11:40:13 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | 63070c09cb584574b81e0acaad1ea68f | - | - | - | - | 48 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1735011613230 | 2024-12-24 11:40:13 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | AbstractApplicationContext | destroyBeans | 63070c09cb584574b81e0acaad1ea68f | - | - | - | - | 64 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735011638769 | 2024-12-24 11:40:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 85766 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1735011638762 | 2024-12-24 11:40:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 93574deda214432d9437244ad7ff2004 | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735011638780 | 2024-12-24 11:40:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735011639271 | 2024-12-24 11:40:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 497 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735011639273 | 2024-12-24 11:40:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 499 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735011639275 | 2024-12-24 11:40:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 501 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735011639277 | 2024-12-24 11:40:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 503 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735011639279 | 2024-12-24 11:40:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 505 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735011639317 | 2024-12-24 11:40:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 543 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735011639350 | 2024-12-24 11:40:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 576 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735011639352 | 2024-12-24 11:40:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 578 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735011639353 | 2024-12-24 11:40:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 579 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735011639401 | 2024-12-24 11:40:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 627 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735011641442 | 2024-12-24 11:40:41 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 2668 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735011641444 | 2024-12-24 11:40:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 2670 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735011641483 | 2024-12-24 11:40:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 2709 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 33 ms. Found 0 JPA repository interfaces.

info | 1 | 1735011641493 | 2024-12-24 11:40:41 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 2719 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735011641494 | 2024-12-24 11:40:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 2720 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735011641521 | 2024-12-24 11:40:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 2747 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1735011642290 | 2024-12-24 11:40:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 3520 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$dfe52e98] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011642313 | 2024-12-24 11:40:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 3539 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$5eeb7410] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011642376 | 2024-12-24 11:40:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 3602 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$dd547ed9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011642380 | 2024-12-24 11:40:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 3606 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011642466 | 2024-12-24 11:40:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 3692 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011642478 | 2024-12-24 11:40:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 3705 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735011643351 | 2024-12-24 11:40:43 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 4577 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735011643364 | 2024-12-24 11:40:43 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 4590 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735011643364 | 2024-12-24 11:40:43 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 4590 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735011643448 | 2024-12-24 11:40:43 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 4674 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735011650611 | 2024-12-24 11:40:50 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 11838 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735011650692 | 2024-12-24 11:40:50 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 11919 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735011650732 | 2024-12-24 11:40:50 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 11958 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735011650840 | 2024-12-24 11:40:50 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 12066 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735011650916 | 2024-12-24 11:40:50 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 12142 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735011651042 | 2024-12-24 11:40:51 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 12268 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735011651049 | 2024-12-24 11:40:51 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 12275 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735011654572 | 2024-12-24 11:40:54 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 15798 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735011654967 | 2024-12-24 11:40:54 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 16193 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735011654995 | 2024-12-24 11:40:54 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 16221 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735011655159 | 2024-12-24 11:40:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 00a1189002734e1391b858554617b86f | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735011655182 | 2024-12-24 11:40:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 00a1189002734e1391b858554617b86f | - | - | - | - | 23 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735011655191 | 2024-12-24 11:40:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 00a1189002734e1391b858554617b86f | - | - | - | - | 32 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735011655302 | 2024-12-24 11:40:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 00a1189002734e1391b858554617b86f | - | - | - | - | 143 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 109 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735011655308 | 2024-12-24 11:40:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 00a1189002734e1391b858554617b86f | - | - | - | - | 149 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735011655315 | 2024-12-24 11:40:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 00a1189002734e1391b858554617b86f | - | - | - | - | 156 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735011655323 | 2024-12-24 11:40:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 00a1189002734e1391b858554617b86f | - | - | - | - | 164 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735011655427 | 2024-12-24 11:40:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 00a1189002734e1391b858554617b86f | - | - | - | - | 268 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 102 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735011658130 | 2024-12-24 11:40:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 19356 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735011658932 | 2024-12-24 11:40:58 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20158 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@3fdcbaf4 with [org.springframework.security.web.session.DisableEncodeUrlFilter@51b635fc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2478f015, org.springframework.security.web.context.SecurityContextPersistenceFilter@324f600a, org.springframework.security.web.header.HeaderWriterFilter@77e3087, org.springframework.security.web.authentication.logout.LogoutFilter@64d394e5, org.springframework.web.filter.CorsFilter@6fa53b0d, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@513f965e, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@43c3bbf1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@19599603, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@361b25cf, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1912a267, org.springframework.security.web.session.SessionManagementFilter@1870e28d, org.springframework.security.web.access.ExceptionTranslationFilter@64c82a3b, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@24da3c3b]

info | 1 | 1735011658950 | 2024-12-24 11:40:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20176 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735011659062 | 2024-12-24 11:40:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20288 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735011659064 | 2024-12-24 11:40:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20290 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735011659065 | 2024-12-24 11:40:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20291 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735011659067 | 2024-12-24 11:40:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20293 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735011659070 | 2024-12-24 11:40:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20296 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735011659070 | 2024-12-24 11:40:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20296 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735011659070 | 2024-12-24 11:40:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20296 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735011659213 | 2024-12-24 11:40:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20439 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735011659213 | 2024-12-24 11:40:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20439 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735011659213 | 2024-12-24 11:40:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20439 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735011659213 | 2024-12-24 11:40:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20439 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735011659272 | 2024-12-24 11:40:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20499 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735011659332 | 2024-12-24 11:40:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20558 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735011659332 | 2024-12-24 11:40:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20558 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735011659343 | 2024-12-24 11:40:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20569 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@60344d8f, tech.powerjob.worker.actors.ProcessorTrackerActor@210741f0, tech.powerjob.worker.actors.WorkerActor@41b22d29])

info | 1 | 1735011659372 | 2024-12-24 11:40:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20598 | 0 | - | - | - | - | main o.r.Reflections Reflections took 20 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735011659378 | 2024-12-24 11:40:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20604 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1735011659379 | 2024-12-24 11:40:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20605 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@75823c39

info | 1 | 1735011659379 | 2024-12-24 11:40:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20605 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735011659380 | 2024-12-24 11:40:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20606 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735011659382 | 2024-12-24 11:40:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 20609 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 124 | 1735011659808 | 2024-12-24 11:40:59 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735011660286 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21513 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735011660287 | 2024-12-24 11:41:00 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21513 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735011660287 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735011660288 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21514 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735011660290 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21516 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735011660292 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21518 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735011660293 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21519 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735011660293 | 2024-12-24 11:41:00 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21520 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 913.8 ms

info | 1 | 1735011660355 | 2024-12-24 11:41:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21581 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735011660358 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21585 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735011660359 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21585 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735011660362 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21588 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735011660562 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21788 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735011660562 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21788 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/f882aa6409974fada8353335f11b9493/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735011660569 | 2024-12-24 11:41:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21795 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/f882aa6409974fada8353335f11b9493/] on JVM exit successfully

info | 1 | 1735011660583 | 2024-12-24 11:41:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21809 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735011660584 | 2024-12-24 11:41:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21810 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.522 s, congratulations!

info | 154 | 1735011660588 | 2024-12-24 11:41:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 02ce1f2ebd7c4a128580ac5cb6418a76 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 154 | 1735011660589 | 2024-12-24 11:41:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 02ce1f2ebd7c4a128580ac5cb6418a76 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735011660639 | 2024-12-24 11:41:00 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21865 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735011660660 | 2024-12-24 11:41:00 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21886 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735011660687 | 2024-12-24 11:41:00 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21914 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735011660688 | 2024-12-24 11:41:00 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21914 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735011660723 | 2024-12-24 11:41:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21949 | 0 | - | - | - | - | main c.t.g.Application Started Application in 22.384 seconds (JVM running for 22.797)

info | 1 | 1735011660747 | 2024-12-24 11:41:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21973 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735011660747 | 2024-12-24 11:41:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 0bf2bdc6cf4b40bdb11fcdf2a9826f97 | - | - | - | - | 21973 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 224 | 1735011660751 | 2024-12-24 11:41:00 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 9499c9354e2445ff98a35db206584391 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 154 | 1735011670586 | 2024-12-24 11:41:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 02ce1f2ebd7c4a128580ac5cb6418a76 | - | - | - | - | 9999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735011680587 | 2024-12-24 11:41:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | e7f1358a5e0a418dbc38fc48318e3faa | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735011690589 | 2024-12-24 11:41:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | e7f1358a5e0a418dbc38fc48318e3faa | - | - | - | - | 10003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735011700585 | 2024-12-24 11:41:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 02ce1f2ebd7c4a128580ac5cb6418a76 | - | - | - | - | 39998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735011710587 | 2024-12-24 11:41:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 02ce1f2ebd7c4a128580ac5cb6418a76 | - | - | - | - | 49999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735011720588 | 2024-12-24 11:42:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 02ce1f2ebd7c4a128580ac5cb6418a76 | - | - | - | - | 60001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011730588 | 2024-12-24 11:42:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011740589 | 2024-12-24 11:42:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011750586 | 2024-12-24 11:42:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 19997 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011760589 | 2024-12-24 11:42:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 30001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011770588 | 2024-12-24 11:42:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 40000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011780588 | 2024-12-24 11:43:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 49999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011790589 | 2024-12-24 11:43:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 60001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011800588 | 2024-12-24 11:43:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 69999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011810589 | 2024-12-24 11:43:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 80000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 224 | 1735011817445 | 2024-12-24 11:43:37 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ChatRoomService | refreshChatCache | 9499c9354e2445ff98a35db206584391 | - | - | - | - | 156694 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 159 | 1735011820585 | 2024-12-24 11:43:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 89996 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011830589 | 2024-12-24 11:43:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 100001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011840590 | 2024-12-24 11:44:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 110002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011850588 | 2024-12-24 11:44:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 120000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011860588 | 2024-12-24 11:44:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 129999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011870589 | 2024-12-24 11:44:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 140002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735011880586 | 2024-12-24 11:44:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | e7f1358a5e0a418dbc38fc48318e3faa | - | - | - | - | 200000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735011890585 | 2024-12-24 11:44:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | e7f1358a5e0a418dbc38fc48318e3faa | - | - | - | - | 209998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1735011900019 | 2024-12-24 11:45:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | 707ad9d337d74accb89396616ed763a3 | - | - | - | - | 0 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-12-24 11:40:54,965 to 2024-12-24 11:45:00,011
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 159 | 1735011900589 | 2024-12-24 11:45:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 170001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011910589 | 2024-12-24 11:45:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 180001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011920589 | 2024-12-24 11:45:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 190000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011930589 | 2024-12-24 11:45:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 200001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011940589 | 2024-12-24 11:45:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 210001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011950589 | 2024-12-24 11:45:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 220000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011960588 | 2024-12-24 11:46:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 229999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011970589 | 2024-12-24 11:46:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 240000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011980589 | 2024-12-24 11:46:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 250001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735011990588 | 2024-12-24 11:46:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 260000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012000589 | 2024-12-24 11:46:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 270001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012010587 | 2024-12-24 11:46:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 279999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012020587 | 2024-12-24 11:47:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 289999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012030589 | 2024-12-24 11:47:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 300001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012040589 | 2024-12-24 11:47:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 310000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012050589 | 2024-12-24 11:47:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 320001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012060590 | 2024-12-24 11:47:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 330001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 154 | 1735012070587 | 2024-12-24 11:47:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 02ce1f2ebd7c4a128580ac5cb6418a76 | - | - | - | - | 409999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012080586 | 2024-12-24 11:48:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 349998 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012090588 | 2024-12-24 11:48:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 360000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012100588 | 2024-12-24 11:48:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 370000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012110589 | 2024-12-24 11:48:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 380001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1735012120864 | 2024-12-24 11:48:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4389d37a409c4d27946156f09f1954f3 | - | - | - | - | 390290 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

