info | 15 | 1732614399193 | 2024-11-26 17:46:39 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 949f6701df944856a18db94095d22f64 | - | - | - | - | 22 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1732614399243 | 2024-11-26 17:46:39 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 10 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 82529 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1732614399258 | 2024-11-26 17:46:39 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 16 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1732614400474 | 2024-11-26 17:46:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 1231 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1732614400479 | 2024-11-26 17:46:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 1237 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1732614400485 | 2024-11-26 17:46:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 1242 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1732614400492 | 2024-11-26 17:46:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 1250 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1732614400498 | 2024-11-26 17:46:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 1255 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1732614400695 | 2024-11-26 17:46:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 1452 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1732614400746 | 2024-11-26 17:46:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 1503 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1732614400748 | 2024-11-26 17:46:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 1505 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1732614400748 | 2024-11-26 17:46:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 1505 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1732614400997 | 2024-11-26 17:46:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 1755 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1732614403064 | 2024-11-26 17:46:43 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 3821 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732614403065 | 2024-11-26 17:46:43 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 3822 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1732614403107 | 2024-11-26 17:46:43 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 3864 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 36 ms. Found 0 JPA repository interfaces.

info | 1 | 1732614403116 | 2024-11-26 17:46:43 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 3873 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732614403117 | 2024-11-26 17:46:43 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 3874 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1732614403143 | 2024-11-26 17:46:43 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 3901 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1732614403896 | 2024-11-26 17:46:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 4657 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$dbef1554] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614403916 | 2024-11-26 17:46:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 4673 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$5af55acc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614403978 | 2024-11-26 17:46:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 4735 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$d95e6595] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614403983 | 2024-11-26 17:46:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 4741 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614404037 | 2024-11-26 17:46:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 4794 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614404043 | 2024-11-26 17:46:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 4800 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614404611 | 2024-11-26 17:46:44 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 5369 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1732614404624 | 2024-11-26 17:46:44 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 5381 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1732614404624 | 2024-11-26 17:46:44 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 5381 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1732614404710 | 2024-11-26 17:46:44 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 5468 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1732614411653 | 2024-11-26 17:46:51 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 12412 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1732614411772 | 2024-11-26 17:46:51 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 12529 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1732614411818 | 2024-11-26 17:46:51 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 12575 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1732614411936 | 2024-11-26 17:46:51 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 12693 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1732614412016 | 2024-11-26 17:46:52 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 12774 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1732614412150 | 2024-11-26 17:46:52 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 12908 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1732614412157 | 2024-11-26 17:46:52 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 12914 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1732614416037 | 2024-11-26 17:46:56 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 16794 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1732614416348 | 2024-11-26 17:46:56 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 17106 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1732614416369 | 2024-11-26 17:46:56 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 17127 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1732614416522 | 2024-11-26 17:46:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 417a92a6b8464b56b5d72a7489105ea2 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1732614416544 | 2024-11-26 17:46:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 417a92a6b8464b56b5d72a7489105ea2 | - | - | - | - | 22 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1732614416552 | 2024-11-26 17:46:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 417a92a6b8464b56b5d72a7489105ea2 | - | - | - | - | 31 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1732614416783 | 2024-11-26 17:46:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 417a92a6b8464b56b5d72a7489105ea2 | - | - | - | - | 262 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 229 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1732614416806 | 2024-11-26 17:46:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 417a92a6b8464b56b5d72a7489105ea2 | - | - | - | - | 284 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 18 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1732614416826 | 2024-11-26 17:46:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 417a92a6b8464b56b5d72a7489105ea2 | - | - | - | - | 304 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1732614416849 | 2024-11-26 17:46:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 417a92a6b8464b56b5d72a7489105ea2 | - | - | - | - | 328 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1732614417000 | 2024-11-26 17:46:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 417a92a6b8464b56b5d72a7489105ea2 | - | - | - | - | 479 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 147 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1732614422779 | 2024-11-26 17:47:02 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 23537 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1732614424941 | 2024-11-26 17:47:04 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 25699 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@2029d432 with [org.springframework.security.web.session.DisableEncodeUrlFilter@6411cb24, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4110dd10, org.springframework.security.web.context.SecurityContextPersistenceFilter@6107fce9, org.springframework.security.web.header.HeaderWriterFilter@5b5752a, org.springframework.security.web.authentication.logout.LogoutFilter@5d196acf, org.springframework.web.filter.CorsFilter@54a1be3a, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@772f022b, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@65a41213, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@8fd4fac, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2df593ac, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@bc2c2d1, org.springframework.security.web.session.SessionManagementFilter@545b28fa, org.springframework.security.web.access.ExceptionTranslationFilter@1183b7f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@254fe08]

info | 1 | 1732614424971 | 2024-11-26 17:47:04 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 25728 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1732614425156 | 2024-11-26 17:47:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 25913 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1732614425159 | 2024-11-26 17:47:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 25916 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1732614425161 | 2024-11-26 17:47:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 25919 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1732614425167 | 2024-11-26 17:47:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 25924 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1732614425190 | 2024-11-26 17:47:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 25948 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1732614425191 | 2024-11-26 17:47:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 25948 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1732614425191 | 2024-11-26 17:47:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 25948 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1732614425450 | 2024-11-26 17:47:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26207 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1732614425450 | 2024-11-26 17:47:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26207 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1732614425450 | 2024-11-26 17:47:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26207 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1732614425450 | 2024-11-26 17:47:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26207 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1732614425450 | 2024-11-26 17:47:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26207 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1732614425456 | 2024-11-26 17:47:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26213 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1732614425541 | 2024-11-26 17:47:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26299 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1732614425543 | 2024-11-26 17:47:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26300 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1732614425554 | 2024-11-26 17:47:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26311 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@79e12f24, tech.powerjob.worker.actors.ProcessorTrackerActor@11908095, tech.powerjob.worker.actors.WorkerActor@71c6bf20])

info | 1 | 1732614425599 | 2024-11-26 17:47:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26356 | 0 | - | - | - | - | main o.r.Reflections Reflections took 33 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1732614425611 | 2024-11-26 17:47:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26368 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1732614425614 | 2024-11-26 17:47:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26371 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@7ce3710f

info | 1 | 1732614425614 | 2024-11-26 17:47:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26371 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1732614425615 | 2024-11-26 17:47:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26372 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1732614425619 | 2024-11-26 17:47:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 26376 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 128 | 1732614426151 | 2024-11-26 17:47:06 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1732614426550 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27307 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1732614426550 | 2024-11-26 17:47:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27307 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1732614426551 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27308 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1732614426551 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27308 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1732614426551 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27308 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732614426552 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27309 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1732614426552 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27309 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732614426552 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27309 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732614426552 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27309 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1732614426552 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27309 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1732614426552 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27309 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732614426552 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27309 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732614426552 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27309 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732614426552 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27309 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1732614426552 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27309 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1732614426554 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27311 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1732614426556 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27313 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1732614426556 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27313 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1732614426556 | 2024-11-26 17:47:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27314 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 942.3 ms

info | 1 | 1732614426655 | 2024-11-26 17:47:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27412 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1732614426661 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27418 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1732614426661 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27418 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1732614426666 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27423 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1732614426864 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27621 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1732614426864 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27621 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/288933c512e4458fa8eada9fef3ee4d7/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1732614426869 | 2024-11-26 17:47:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27627 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/288933c512e4458fa8eada9fef3ee4d7/] on JVM exit successfully

info | 1 | 1732614426885 | 2024-11-26 17:47:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27642 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1732614426886 | 2024-11-26 17:47:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27643 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.730 s, congratulations!

info | 162 | 1732614426890 | 2024-11-26 17:47:06 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 5d50e28645544fb6aaec694fa8541d61 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 162 | 1732614426890 | 2024-11-26 17:47:06 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 5d50e28645544fb6aaec694fa8541d61 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1732614426947 | 2024-11-26 17:47:06 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | TomcatWebServer | start | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27704 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1732614426970 | 2024-11-26 17:47:06 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27727 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1732614426987 | 2024-11-26 17:47:06 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27744 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1732614426987 | 2024-11-26 17:47:06 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27744 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1732614427023 | 2024-11-26 17:47:07 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | Application | main | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27780 | 0 | - | - | - | - | main c.t.g.Application Started Application in 28.932 seconds (JVM running for 29.611)

info | 1 | 1732614427049 | 2024-11-26 17:47:07 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27806 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1732614427049 | 2024-11-26 17:47:07 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 263a562ec28242cbae7954d2af7a1c05 | - | - | - | - | 27807 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 232 | 1732614427053 | 2024-11-26 17:47:07 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 790bd24bc4724a428ed06909de66847e | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 162 | 1732614456998 | 2024-11-26 17:47:36 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 5d50e28645544fb6aaec694fa8541d61 | - | - | - | - | 30108 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1732614503200 | 2024-11-26 17:48:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1f4b018913244352b38b4f9d80001e63 | - | - | - | - | 20007 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1732614750893 | 2024-11-26 17:52:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1f4b018913244352b38b4f9d80001e63 | - | - | - | - | 267700 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732614760858 | 2024-11-26 17:52:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 29ea21cb6ba64e8bb6ae4a165b47762e | - | - | - | - | 298856 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732614770859 | 2024-11-26 17:52:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 29ea21cb6ba64e8bb6ae4a165b47762e | - | - | - | - | 308857 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732614780861 | 2024-11-26 17:53:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 29ea21cb6ba64e8bb6ae4a165b47762e | - | - | - | - | 318860 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732614790859 | 2024-11-26 17:53:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 29ea21cb6ba64e8bb6ae4a165b47762e | - | - | - | - | 328857 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732614800862 | 2024-11-26 17:53:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 5d50e28645544fb6aaec694fa8541d61 | - | - | - | - | 373973 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732614810861 | 2024-11-26 17:53:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 29ea21cb6ba64e8bb6ae4a165b47762e | - | - | - | - | 348860 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732614820861 | 2024-11-26 17:53:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 29ea21cb6ba64e8bb6ae4a165b47762e | - | - | - | - | 358860 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732614830862 | 2024-11-26 17:53:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 5d50e28645544fb6aaec694fa8541d61 | - | - | - | - | 403974 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732614840861 | 2024-11-26 17:54:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 5d50e28645544fb6aaec694fa8541d61 | - | - | - | - | 413972 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732614850863 | 2024-11-26 17:54:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 29ea21cb6ba64e8bb6ae4a165b47762e | - | - | - | - | 388861 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1732614860860 | 2024-11-26 17:54:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1f4b018913244352b38b4f9d80001e63 | - | - | - | - | 377665 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732614870864 | 2024-11-26 17:54:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 5d50e28645544fb6aaec694fa8541d61 | - | - | - | - | 443976 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1732614878319 | 2024-11-26 17:54:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 3 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 82979 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1732614878312 | 2024-11-26 17:54:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | e61d54958c6146fb91b3c4a6ccf66db0 | - | - | - | - | 3 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1732614878330 | 2024-11-26 17:54:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1732614878844 | 2024-11-26 17:54:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 518 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1732614878997 | 2024-11-26 17:54:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 671 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1732614879004 | 2024-11-26 17:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 678 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1732614879011 | 2024-11-26 17:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 685 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1732614879020 | 2024-11-26 17:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 694 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1732614879498 | 2024-11-26 17:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 1173 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1732614879580 | 2024-11-26 17:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 1255 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1732614879583 | 2024-11-26 17:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 1258 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1732614879584 | 2024-11-26 17:54:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 1258 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1732614880252 | 2024-11-26 17:54:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 1927 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1732614882269 | 2024-11-26 17:54:42 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 3943 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732614882270 | 2024-11-26 17:54:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 3944 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1732614882308 | 2024-11-26 17:54:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 3982 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 33 ms. Found 0 JPA repository interfaces.

info | 1 | 1732614882316 | 2024-11-26 17:54:42 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 3990 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732614882317 | 2024-11-26 17:54:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 3991 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1732614882341 | 2024-11-26 17:54:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 4015 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1732614883180 | 2024-11-26 17:54:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 4858 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$77cc3461] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614883201 | 2024-11-26 17:54:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 4875 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$f6d279d9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614883269 | 2024-11-26 17:54:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 4943 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$753b84a2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614883275 | 2024-11-26 17:54:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 4950 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614883337 | 2024-11-26 17:54:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 5011 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614883340 | 2024-11-26 17:54:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 5015 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732614883903 | 2024-11-26 17:54:43 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 5577 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1732614883914 | 2024-11-26 17:54:43 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 5588 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1732614883914 | 2024-11-26 17:54:43 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 5588 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1732614884008 | 2024-11-26 17:54:44 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 5682 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1732614895036 | 2024-11-26 17:54:55 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 16712 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1732614895144 | 2024-11-26 17:54:55 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 16818 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1732614895187 | 2024-11-26 17:54:55 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 16861 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1732614895325 | 2024-11-26 17:54:55 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 16999 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1732614895397 | 2024-11-26 17:54:55 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 17071 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1732614895530 | 2024-11-26 17:54:55 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 17204 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1732614895540 | 2024-11-26 17:54:55 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 17214 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1732614901387 | 2024-11-26 17:55:01 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 23062 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1732614901666 | 2024-11-26 17:55:01 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 23341 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1732614901690 | 2024-11-26 17:55:01 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 23365 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1732614901847 | 2024-11-26 17:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b668964ed3494db091a35e783a5414f4 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1732614901872 | 2024-11-26 17:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b668964ed3494db091a35e783a5414f4 | - | - | - | - | 25 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1732614901883 | 2024-11-26 17:55:01 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b668964ed3494db091a35e783a5414f4 | - | - | - | - | 36 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1732614902016 | 2024-11-26 17:55:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b668964ed3494db091a35e783a5414f4 | - | - | - | - | 169 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 131 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1732614902025 | 2024-11-26 17:55:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b668964ed3494db091a35e783a5414f4 | - | - | - | - | 178 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1732614902222 | 2024-11-26 17:55:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b668964ed3494db091a35e783a5414f4 | - | - | - | - | 376 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1732614902229 | 2024-11-26 17:55:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b668964ed3494db091a35e783a5414f4 | - | - | - | - | 382 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1732614902324 | 2024-11-26 17:55:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b668964ed3494db091a35e783a5414f4 | - | - | - | - | 478 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 93 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1732614905546 | 2024-11-26 17:55:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 27220 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1732614906478 | 2024-11-26 17:55:06 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 28153 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@16a553f9 with [org.springframework.security.web.session.DisableEncodeUrlFilter@44f9e70f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1e22c706, org.springframework.security.web.context.SecurityContextPersistenceFilter@78ed3c71, org.springframework.security.web.header.HeaderWriterFilter@795a3df7, org.springframework.security.web.authentication.logout.LogoutFilter@25d2921d, org.springframework.web.filter.CorsFilter@402b10af, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@20a0eb1d, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@147faaa8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2ae04d56, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@624bc592, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@28791945, org.springframework.security.web.session.SessionManagementFilter@1efb1a84, org.springframework.security.web.access.ExceptionTranslationFilter@2a117d87, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1808513f]

info | 1 | 1732614906501 | 2024-11-26 17:55:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 28175 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1732614906646 | 2024-11-26 17:55:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 28320 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1732614906649 | 2024-11-26 17:55:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 28323 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1732614906650 | 2024-11-26 17:55:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 28325 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1732614906654 | 2024-11-26 17:55:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 28328 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1732614906658 | 2024-11-26 17:55:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 28333 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1732614906659 | 2024-11-26 17:55:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 28333 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1732614906659 | 2024-11-26 17:55:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 28333 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1732614907888 | 2024-11-26 17:55:07 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 29563 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1732614907915 | 2024-11-26 17:55:07 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 29589 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1732614907943 | 2024-11-26 17:55:07 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 29617 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1732614907972 | 2024-11-26 17:55:07 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 29646 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1732614907972 | 2024-11-26 17:55:07 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 29646 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1732614907980 | 2024-11-26 17:55:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 29654 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1732614908614 | 2024-11-26 17:55:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 30289 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1732614908615 | 2024-11-26 17:55:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 30290 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1732614908627 | 2024-11-26 17:55:08 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 30301 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@35754437, tech.powerjob.worker.actors.ProcessorTrackerActor@141d9b6e, tech.powerjob.worker.actors.WorkerActor@2d6399c6])

info | 1 | 1732614908738 | 2024-11-26 17:55:08 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 30413 | 0 | - | - | - | - | main o.r.Reflections Reflections took 20 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1732614908744 | 2024-11-26 17:55:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 30418 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1732614908745 | 2024-11-26 17:55:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 30419 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@4d251371

info | 1 | 1732614908745 | 2024-11-26 17:55:08 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 30419 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1732614908745 | 2024-11-26 17:55:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 30419 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1732614908748 | 2024-11-26 17:55:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 30422 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 123 | 1732614909287 | 2024-11-26 17:55:09 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1732614909699 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31373 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1732614909700 | 2024-11-26 17:55:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31374 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1732614909700 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31374 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1732614909700 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31374 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1732614909700 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31374 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732614909700 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31374 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1732614909701 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732614909701 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732614909701 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1732614909701 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1732614909701 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732614909701 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732614909701 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732614909701 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1732614909701 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1732614909703 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31377 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1732614909705 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31379 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1732614909705 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31380 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1732614909706 | 2024-11-26 17:55:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31381 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 961.2 ms

info | 1 | 1732614909912 | 2024-11-26 17:55:09 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31586 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1732614909921 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31595 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1732614909921 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31595 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1732614909926 | 2024-11-26 17:55:09 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31601 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1732614910132 | 2024-11-26 17:55:10 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31806 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1732614910132 | 2024-11-26 17:55:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31806 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/bf9e4c8f3e324a41ac2ded824a31a846/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1732614910139 | 2024-11-26 17:55:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31813 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/bf9e4c8f3e324a41ac2ded824a31a846/] on JVM exit successfully

info | 1 | 1732614910157 | 2024-11-26 17:55:10 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31832 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1732614910159 | 2024-11-26 17:55:10 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31833 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 3.513 s, congratulations!

info | 157 | 1732614910164 | 2024-11-26 17:55:10 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 157 | 1732614910165 | 2024-11-26 17:55:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1732614910245 | 2024-11-26 17:55:10 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | TomcatWebServer | start | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31920 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1732614910284 | 2024-11-26 17:55:10 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31958 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1732614910300 | 2024-11-26 17:55:10 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31975 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1732614910301 | 2024-11-26 17:55:10 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 31975 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1732614910332 | 2024-11-26 17:55:10 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | Application | main | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 32006 | 0 | - | - | - | - | main c.t.g.Application Started Application in 32.487 seconds (JVM running for 32.977)

info | 1 | 1732614910368 | 2024-11-26 17:55:10 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 32042 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1732614910368 | 2024-11-26 17:55:10 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 16e0ae50b6514b71b9724fe2f103f603 | - | - | - | - | 32042 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 227 | 1732614910379 | 2024-11-26 17:55:10 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 242bfb6134984f5397c5bb1366186fae | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 157 | 1732614920159 | 2024-11-26 17:55:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 9996 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732614930161 | 2024-11-26 17:55:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732614940161 | 2024-11-26 17:55:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732614950163 | 2024-11-26 17:55:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 20002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732614960165 | 2024-11-26 17:56:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 30005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732614970163 | 2024-11-26 17:56:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 40002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732614980163 | 2024-11-26 17:56:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732614990165 | 2024-11-26 17:56:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 10002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615000164 | 2024-11-26 17:56:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 20002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615010165 | 2024-11-26 17:56:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 30002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615020163 | 2024-11-26 17:57:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 40001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615030163 | 2024-11-26 17:57:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 119999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732615040166 | 2024-11-26 17:57:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 110006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732615050165 | 2024-11-26 17:57:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 120005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732615060165 | 2024-11-26 17:57:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 130005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732615070166 | 2024-11-26 17:57:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 140006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732615080166 | 2024-11-26 17:58:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 150005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732615090166 | 2024-11-26 17:58:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 160006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732615100166 | 2024-11-26 17:58:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 170006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1732615108276 | 2024-11-26 17:58:28 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | StandardWrapper | initServlet | efe8152eb1c74ce782eb542c86c44824 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 165 | 1732615108277 | 2024-11-26 17:58:28 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | AuthenticatorBase | invoke | efe8152eb1c74ce782eb542c86c44824 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 165 | 1732615108305 | 2024-11-26 17:58:28 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | AuthenticatorBase | invoke | efe8152eb1c74ce782eb542c86c44824 | - | - | - | - | 28 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 28 ms

info | 162 | 1732615110164 | 2024-11-26 17:58:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 180004 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 227 | 1732615116568 | 2024-11-26 17:58:36 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ChatRoomService | refreshChatCache | 242bfb6134984f5397c5bb1366186fae | - | - | - | - | 206191 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 162 | 1732615120164 | 2024-11-26 17:58:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 190003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615130166 | 2024-11-26 17:58:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 220002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615140167 | 2024-11-26 17:59:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 160006 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615150167 | 2024-11-26 17:59:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 240003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615160166 | 2024-11-26 17:59:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 250003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615170163 | 2024-11-26 17:59:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 190000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615180167 | 2024-11-26 17:59:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 200004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615190167 | 2024-11-26 17:59:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 210005 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1732615200024 | 2024-11-26 18:00:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | 3d8ebc3443bc4c749f417ee06391f589 | - | - | - | - | 0 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-11-26 17:55:01,665 to 2024-11-26 18:00:00,014
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 162 | 1732615200163 | 2024-11-26 18:00:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 270002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615210167 | 2024-11-26 18:00:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 300004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615220167 | 2024-11-26 18:00:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 310004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615230166 | 2024-11-26 18:00:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 320003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615240164 | 2024-11-26 18:00:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 260004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615250168 | 2024-11-26 18:00:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 340005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615260163 | 2024-11-26 18:01:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 350000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615270170 | 2024-11-26 18:01:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 360007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615280168 | 2024-11-26 18:01:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 370004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615290167 | 2024-11-26 18:01:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 380003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615300168 | 2024-11-26 18:01:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 390005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615310168 | 2024-11-26 18:01:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 400005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615320167 | 2024-11-26 18:02:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 410004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615330165 | 2024-11-26 18:02:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 420003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615340165 | 2024-11-26 18:02:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 430002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615350169 | 2024-11-26 18:02:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 440007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615360166 | 2024-11-26 18:02:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 450002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615370170 | 2024-11-26 18:02:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 460007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615380183 | 2024-11-26 18:03:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 470020 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732615390169 | 2024-11-26 18:03:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 460008 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732615400168 | 2024-11-26 18:03:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 470008 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615410168 | 2024-11-26 18:03:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 430006 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615420170 | 2024-11-26 18:03:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 440007 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615430169 | 2024-11-26 18:03:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 450007 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615440168 | 2024-11-26 18:04:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 460005 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615450169 | 2024-11-26 18:04:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 470007 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615460171 | 2024-11-26 18:04:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 480009 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615470166 | 2024-11-26 18:04:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 490003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615480171 | 2024-11-26 18:04:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6970ad242d9f4ff9a08ccf28501d6241 | - | - | - | - | 570008 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732615490171 | 2024-11-26 18:04:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 560010 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 175 | 1732615498370 | 2024-11-26 18:04:58 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | manageChatRoom | roomChatData | 37614436c26d4913bb2cadc41b456fd5 | - | - | - | - | 365 | 0 | - | - | - | - | http-nio-8087-exec-11 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-20","endTime":"2024-11-25","orgId":1001121,"teamId":null,"export":null,"status":null,"chatUuid":"","liveNo":"","nickName":null,"total":null,"type":null,"download":null}

info | 175 | 1732615498541 | 2024-11-26 18:04:58 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | manageChatRoom | roomChatData | 37614436c26d4913bb2cadc41b456fd5 | - | - | - | - | 536 | 0 | - | - | - | - | http-nio-8087-exec-11 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-20,结束时间：2024-11-25,团队集合:[],hostUuids:[],chatStatus:null

info | 162 | 1732615500187 | 2024-11-26 18:05:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 570027 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 175 | 1732615500739 | 2024-11-26 18:05:00 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | manageChatRoom | roomChatData | 37614436c26d4913bb2cadc41b456fd5 | - | - | - | - | 2734 | 0 | - | - | - | - | http-nio-8087-exec-11 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-20,结束时间：2024-11-25,团队集合:[],hostUuids:[],chatStatus:null

info | 162 | 1732615510171 | 2024-11-26 18:05:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 55114534cd034e83898ad5d7c4a17277 | - | - | - | - | 580010 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615520170 | 2024-11-26 18:05:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 540008 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615530172 | 2024-11-26 18:05:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 550010 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1732615540166 | 2024-11-26 18:05:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4259c69b4b444655be1882bd8589a0ab | - | - | - | - | 560004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1732615548499 | 2024-11-26 18:05:48 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | e398e2fa0c154174831d39e1dc46881d | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1732615548566 | 2024-11-26 18:05:48 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | e398e2fa0c154174831d39e1dc46881d | - | - | - | - | 67 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1732615548568 | 2024-11-26 18:05:48 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | e398e2fa0c154174831d39e1dc46881d | - | - | - | - | 70 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1732615548600 | 2024-11-26 18:05:48 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | e398e2fa0c154174831d39e1dc46881d | - | - | - | - | 101 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1732615552412 | 2024-11-26 18:05:52 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 83452 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1732615552407 | 2024-11-26 18:05:52 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 5a3f3874e35142e68f1fa81b09223eb2 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1732615552423 | 2024-11-26 18:05:52 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1732615552957 | 2024-11-26 18:05:52 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 538 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1732615552990 | 2024-11-26 18:05:52 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 571 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1732615553000 | 2024-11-26 18:05:53 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 581 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1732615553004 | 2024-11-26 18:05:53 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 585 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1732615553018 | 2024-11-26 18:05:53 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 599 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1732615553426 | 2024-11-26 18:05:53 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 1007 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1732615553536 | 2024-11-26 18:05:53 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 1117 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1732615553539 | 2024-11-26 18:05:53 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 1120 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1732615553539 | 2024-11-26 18:05:53 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 1120 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1732615554103 | 2024-11-26 18:05:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 1685 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1732615557638 | 2024-11-26 18:05:57 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 5220 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732615557642 | 2024-11-26 18:05:57 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 5223 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1732615557708 | 2024-11-26 18:05:57 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 5289 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 58 ms. Found 0 JPA repository interfaces.

info | 1 | 1732615557720 | 2024-11-26 18:05:57 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 5301 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732615557721 | 2024-11-26 18:05:57 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 5302 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1732615557751 | 2024-11-26 18:05:57 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 5332 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1732615558796 | 2024-11-26 18:05:58 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 6378 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$599b5321] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615558815 | 2024-11-26 18:05:58 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 6397 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d8a19899] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615558907 | 2024-11-26 18:05:58 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 6489 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$570aa362] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615558915 | 2024-11-26 18:05:58 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 6496 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615559054 | 2024-11-26 18:05:59 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 6635 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615559062 | 2024-11-26 18:05:59 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 6643 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615559699 | 2024-11-26 18:05:59 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 7281 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1732615559712 | 2024-11-26 18:05:59 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 7293 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1732615559712 | 2024-11-26 18:05:59 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 7293 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1732615559800 | 2024-11-26 18:05:59 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 7381 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1732615570172 | 2024-11-26 18:06:10 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 17754 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1732615570249 | 2024-11-26 18:06:10 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 17830 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1732615570284 | 2024-11-26 18:06:10 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 17865 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1732615570390 | 2024-11-26 18:06:10 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 17971 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1732615570472 | 2024-11-26 18:06:10 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 18053 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1732615570600 | 2024-11-26 18:06:10 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 18181 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1732615570607 | 2024-11-26 18:06:10 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 18188 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1732615574923 | 2024-11-26 18:06:14 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 22504 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1732615575205 | 2024-11-26 18:06:15 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 22786 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1732615575236 | 2024-11-26 18:06:15 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 22817 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1732615575384 | 2024-11-26 18:06:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | f11fc63641ac45cfbe772082d9a5afef | - | - | - | - | 1 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1732615575407 | 2024-11-26 18:06:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | f11fc63641ac45cfbe772082d9a5afef | - | - | - | - | 23 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1732615575417 | 2024-11-26 18:06:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | f11fc63641ac45cfbe772082d9a5afef | - | - | - | - | 33 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1732615575576 | 2024-11-26 18:06:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | f11fc63641ac45cfbe772082d9a5afef | - | - | - | - | 192 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 156 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1732615575582 | 2024-11-26 18:06:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | f11fc63641ac45cfbe772082d9a5afef | - | - | - | - | 198 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1732615575616 | 2024-11-26 18:06:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | f11fc63641ac45cfbe772082d9a5afef | - | - | - | - | 232 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 33 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1732615575628 | 2024-11-26 18:06:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | f11fc63641ac45cfbe772082d9a5afef | - | - | - | - | 244 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1732615575735 | 2024-11-26 18:06:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | f11fc63641ac45cfbe772082d9a5afef | - | - | - | - | 351 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 104 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1732615581235 | 2024-11-26 18:06:21 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 28817 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1732615582059 | 2024-11-26 18:06:22 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 29640 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@33e99277 with [org.springframework.security.web.session.DisableEncodeUrlFilter@7ecac756, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@719b8c4e, org.springframework.security.web.context.SecurityContextPersistenceFilter@608e8516, org.springframework.security.web.header.HeaderWriterFilter@2f9ceea8, org.springframework.security.web.authentication.logout.LogoutFilter@4756971e, org.springframework.web.filter.CorsFilter@40d1d34d, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@2506cf0d, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@19599603, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6d709289, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4e0554ca, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@665cc575, org.springframework.security.web.session.SessionManagementFilter@3124e9b9, org.springframework.security.web.access.ExceptionTranslationFilter@57d86841, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4948c2db]

info | 1 | 1732615582080 | 2024-11-26 18:06:22 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 29661 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1732615582174 | 2024-11-26 18:06:22 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 29755 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1732615582176 | 2024-11-26 18:06:22 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 29757 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1732615582177 | 2024-11-26 18:06:22 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 29758 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1732615582180 | 2024-11-26 18:06:22 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 29761 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1732615582183 | 2024-11-26 18:06:22 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 29764 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1732615582183 | 2024-11-26 18:06:22 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 29764 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1732615582183 | 2024-11-26 18:06:22 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 29764 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1732615582504 | 2024-11-26 18:06:22 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30085 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1732615582504 | 2024-11-26 18:06:22 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30085 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1732615582504 | 2024-11-26 18:06:22 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30085 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1732615582504 | 2024-11-26 18:06:22 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30085 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1732615582504 | 2024-11-26 18:06:22 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30085 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1732615582507 | 2024-11-26 18:06:22 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30089 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1732615582687 | 2024-11-26 18:06:22 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30269 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1732615582688 | 2024-11-26 18:06:22 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30269 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1732615582697 | 2024-11-26 18:06:22 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30278 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@2f263009, tech.powerjob.worker.actors.ProcessorTrackerActor@1b17b5cb, tech.powerjob.worker.actors.WorkerActor@189c12c4])

info | 1 | 1732615582730 | 2024-11-26 18:06:22 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30312 | 0 | - | - | - | - | main o.r.Reflections Reflections took 25 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1732615582738 | 2024-11-26 18:06:22 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30319 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1732615582740 | 2024-11-26 18:06:22 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30321 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@37af6911

info | 1 | 1732615582740 | 2024-11-26 18:06:22 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30321 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1732615582740 | 2024-11-26 18:06:22 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30322 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1732615582745 | 2024-11-26 18:06:22 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 30326 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 123 | 1732615583240 | 2024-11-26 18:06:23 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1732615583662 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31243 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1732615583662 | 2024-11-26 18:06:23 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31243 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31244 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732615583663 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31245 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732615583665 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31246 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1732615583668 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31249 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1732615583669 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31250 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1732615583670 | 2024-11-26 18:06:23 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31251 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 929.9 ms

info | 1 | 1732615583801 | 2024-11-26 18:06:23 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31382 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1732615583806 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31388 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1732615583807 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31388 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1732615583810 | 2024-11-26 18:06:23 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31391 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1732615584006 | 2024-11-26 18:06:24 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31587 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1732615584006 | 2024-11-26 18:06:24 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31587 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/06ca76da73d349719d865428d1533067/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1732615584014 | 2024-11-26 18:06:24 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31595 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/06ca76da73d349719d865428d1533067/] on JVM exit successfully

info | 1 | 1732615584030 | 2024-11-26 18:06:24 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31611 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1732615584031 | 2024-11-26 18:06:24 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31612 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.857 s, congratulations!

info | 152 | 1732615584034 | 2024-11-26 18:06:24 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | ec8b63dc7e5d411fa08ef2939b15ca37 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 152 | 1732615584035 | 2024-11-26 18:06:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ec8b63dc7e5d411fa08ef2939b15ca37 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1732615584090 | 2024-11-26 18:06:24 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | TomcatWebServer | start | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31671 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1732615584138 | 2024-11-26 18:06:24 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31719 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1732615584177 | 2024-11-26 18:06:24 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31759 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1732615584178 | 2024-11-26 18:06:24 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31759 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1732615584207 | 2024-11-26 18:06:24 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | Application | main | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31788 | 0 | - | - | - | - | main c.t.g.Application Started Application in 32.291 seconds (JVM running for 32.91)

info | 1 | 1732615584235 | 2024-11-26 18:06:24 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31816 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1732615584235 | 2024-11-26 18:06:24 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 47758678f15c4e9bafc6613f2d527ad9 | - | - | - | - | 31816 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 222 | 1732615584239 | 2024-11-26 18:06:24 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 0944126fc02f4f1db7a4ab07a85f4b31 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 152 | 1732615594036 | 2024-11-26 18:06:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ec8b63dc7e5d411fa08ef2939b15ca37 | - | - | - | - | 10003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1732615604036 | 2024-11-26 18:06:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ec8b63dc7e5d411fa08ef2939b15ca37 | - | - | - | - | 20002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615614037 | 2024-11-26 18:06:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | bbd71f950a6b4d04afe6096f2a474f14 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615624037 | 2024-11-26 18:07:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | bbd71f950a6b4d04afe6096f2a474f14 | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615634032 | 2024-11-26 18:07:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | bbd71f950a6b4d04afe6096f2a474f14 | - | - | - | - | 19995 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615644037 | 2024-11-26 18:07:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | bbd71f950a6b4d04afe6096f2a474f14 | - | - | - | - | 30001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615654032 | 2024-11-26 18:07:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615664035 | 2024-11-26 18:07:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 10003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615674033 | 2024-11-26 18:07:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 20001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615684037 | 2024-11-26 18:08:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 30006 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615694037 | 2024-11-26 18:08:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 40004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615704033 | 2024-11-26 18:08:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 50001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615714037 | 2024-11-26 18:08:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 60005 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615724038 | 2024-11-26 18:08:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 70007 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615734048 | 2024-11-26 18:08:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 80019 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615744037 | 2024-11-26 18:09:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 90005 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615754041 | 2024-11-26 18:09:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 100016 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615764040 | 2024-11-26 18:09:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 110019 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615774034 | 2024-11-26 18:09:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 120001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615784037 | 2024-11-26 18:09:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 130004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 222 | 1732615793189 | 2024-11-26 18:09:53 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ChatRoomService | refreshChatCache | 0944126fc02f4f1db7a4ab07a85f4b31 | - | - | - | - | 208953 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 156 | 1732615794042 | 2024-11-26 18:09:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 140009 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1732615804034 | 2024-11-26 18:10:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 05a7fa1f9e644e4fb12aa878754587aa | - | - | - | - | 150008 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1732615814043 | 2024-11-26 18:10:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | bbd71f950a6b4d04afe6096f2a474f14 | - | - | - | - | 200007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1732615824038 | 2024-11-26 18:10:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ec8b63dc7e5d411fa08ef2939b15ca37 | - | - | - | - | 240005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1732615833735 | 2024-11-26 18:10:33 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 83627 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1732615833729 | 2024-11-26 18:10:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | a1db4ac335d44fab9bf48e8775f3ca6c | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1732615833746 | 2024-11-26 18:10:33 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1732615834219 | 2024-11-26 18:10:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 478 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1732615834374 | 2024-11-26 18:10:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 632 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1732615834379 | 2024-11-26 18:10:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 638 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1732615834385 | 2024-11-26 18:10:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 643 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1732615834397 | 2024-11-26 18:10:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 655 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1732615834795 | 2024-11-26 18:10:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 1054 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1732615834895 | 2024-11-26 18:10:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 1153 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1732615834897 | 2024-11-26 18:10:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 1155 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1732615834898 | 2024-11-26 18:10:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 1156 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1732615835569 | 2024-11-26 18:10:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 1828 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1732615837432 | 2024-11-26 18:10:37 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 3690 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732615837433 | 2024-11-26 18:10:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 3691 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1732615837473 | 2024-11-26 18:10:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 3731 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 35 ms. Found 0 JPA repository interfaces.

info | 1 | 1732615837480 | 2024-11-26 18:10:37 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 3739 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732615837481 | 2024-11-26 18:10:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 3739 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1732615837504 | 2024-11-26 18:10:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 3763 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.

info | 1 | 1732615838176 | 2024-11-26 18:10:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 4437 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f014e0d4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615838196 | 2024-11-26 18:10:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 4454 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$6f1b264c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615838256 | 2024-11-26 18:10:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 4515 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$ed843115] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615838261 | 2024-11-26 18:10:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 4519 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615838350 | 2024-11-26 18:10:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 4608 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615838353 | 2024-11-26 18:10:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 4611 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732615838808 | 2024-11-26 18:10:38 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 5066 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1732615838817 | 2024-11-26 18:10:38 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 5075 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1732615838817 | 2024-11-26 18:10:38 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 5076 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1732615838894 | 2024-11-26 18:10:38 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 5153 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1732615851348 | 2024-11-26 18:10:51 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 17607 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1732615851430 | 2024-11-26 18:10:51 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 17688 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1732615851463 | 2024-11-26 18:10:51 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 17721 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1732615851548 | 2024-11-26 18:10:51 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 17806 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1732615851621 | 2024-11-26 18:10:51 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 17879 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1732615851815 | 2024-11-26 18:10:51 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 18073 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1732615851824 | 2024-11-26 18:10:51 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 18082 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1732615857322 | 2024-11-26 18:10:57 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 23580 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1732615857586 | 2024-11-26 18:10:57 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 23844 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1732615857611 | 2024-11-26 18:10:57 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 23869 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1732615857758 | 2024-11-26 18:10:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 05ccb4390912422d8a9dab1db68f0185 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1732615857781 | 2024-11-26 18:10:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 05ccb4390912422d8a9dab1db68f0185 | - | - | - | - | 23 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1732615857790 | 2024-11-26 18:10:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 05ccb4390912422d8a9dab1db68f0185 | - | - | - | - | 33 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1732615857907 | 2024-11-26 18:10:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 05ccb4390912422d8a9dab1db68f0185 | - | - | - | - | 150 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 114 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1732615857913 | 2024-11-26 18:10:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 05ccb4390912422d8a9dab1db68f0185 | - | - | - | - | 156 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1732615857921 | 2024-11-26 18:10:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 05ccb4390912422d8a9dab1db68f0185 | - | - | - | - | 164 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1732615857928 | 2024-11-26 18:10:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 05ccb4390912422d8a9dab1db68f0185 | - | - | - | - | 171 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1732615858036 | 2024-11-26 18:10:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 05ccb4390912422d8a9dab1db68f0185 | - | - | - | - | 278 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 105 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1732615860952 | 2024-11-26 18:11:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 27211 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1732615861992 | 2024-11-26 18:11:01 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 28251 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@67026899 with [org.springframework.security.web.session.DisableEncodeUrlFilter@53937bc6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2c944389, org.springframework.security.web.context.SecurityContextPersistenceFilter@4f58822a, org.springframework.security.web.header.HeaderWriterFilter@4a41faaa, org.springframework.security.web.authentication.logout.LogoutFilter@3124e9b9, org.springframework.web.filter.CorsFilter@532fc97b, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@48945548, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@56f94ad2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5c65480d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2f9ceea8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@224ba634, org.springframework.security.web.session.SessionManagementFilter@4147625b, org.springframework.security.web.access.ExceptionTranslationFilter@1783fe7e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@246b6267]

info | 1 | 1732615862009 | 2024-11-26 18:11:02 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 28267 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1732615862086 | 2024-11-26 18:11:02 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 28345 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1732615862088 | 2024-11-26 18:11:02 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 28346 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1732615862089 | 2024-11-26 18:11:02 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 28347 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1732615862091 | 2024-11-26 18:11:02 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 28350 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1732615862095 | 2024-11-26 18:11:02 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 28353 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1732615862095 | 2024-11-26 18:11:02 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 28353 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1732615862095 | 2024-11-26 18:11:02 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 28353 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1732615863737 | 2024-11-26 18:11:03 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 29997 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1732615863740 | 2024-11-26 18:11:03 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 29998 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1732615863740 | 2024-11-26 18:11:03 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 29998 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1732615863741 | 2024-11-26 18:11:03 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 29999 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1732615863741 | 2024-11-26 18:11:03 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 29999 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1732615863745 | 2024-11-26 18:11:03 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30004 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1732615863843 | 2024-11-26 18:11:03 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30101 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1732615863846 | 2024-11-26 18:11:03 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30104 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1732615863865 | 2024-11-26 18:11:03 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30124 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@6783409c, tech.powerjob.worker.actors.ProcessorTrackerActor@2de76568, tech.powerjob.worker.actors.WorkerActor@42582432])

info | 1 | 1732615863907 | 2024-11-26 18:11:03 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30166 | 0 | - | - | - | - | main o.r.Reflections Reflections took 27 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1732615863913 | 2024-11-26 18:11:03 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30171 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1732615863915 | 2024-11-26 18:11:03 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30173 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@4b35d2db

info | 1 | 1732615863916 | 2024-11-26 18:11:03 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30174 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@5bd48bbb

info | 1 | 1732615863916 | 2024-11-26 18:11:03 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30174 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1732615863916 | 2024-11-26 18:11:03 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30174 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1732615863920 | 2024-11-26 18:11:03 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 30178 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 124 | 1732615864449 | 2024-11-26 18:11:04 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1732615864909 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31167 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1732615864909 | 2024-11-26 18:11:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31167 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1732615864910 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31168 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1732615864910 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31168 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1732615864910 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31168 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732615864910 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31168 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1732615864910 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31168 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732615864910 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31168 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732615864910 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31168 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1732615864910 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31169 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1732615864911 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31169 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732615864911 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31169 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732615864911 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31169 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732615864911 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31169 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1732615864911 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31169 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1732615864912 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31170 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1732615864914 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31172 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1732615864915 | 2024-11-26 18:11:04 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31173 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1732615864915 | 2024-11-26 18:11:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31333 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 999.1 ms

info | 1 | 1732615865282 | 2024-11-26 18:11:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31540 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1732615865288 | 2024-11-26 18:11:05 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31546 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1732615865288 | 2024-11-26 18:11:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31546 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1732615865291 | 2024-11-26 18:11:05 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31550 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1732615865467 | 2024-11-26 18:11:05 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31725 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1732615865467 | 2024-11-26 18:11:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31725 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/4392e7e1933f4baea2a664edbee96644/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1732615865473 | 2024-11-26 18:11:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31731 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/4392e7e1933f4baea2a664edbee96644/] on JVM exit successfully

info | 1 | 1732615865490 | 2024-11-26 18:11:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31748 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1732615865492 | 2024-11-26 18:11:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31750 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 3.405 s, congratulations!

info | 159 | 1732615865499 | 2024-11-26 18:11:05 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 3bf5e84fe4d04a52a755a70971c550dd | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 159 | 1732615865500 | 2024-11-26 18:11:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 3bf5e84fe4d04a52a755a70971c550dd | - | - | - | - | 2 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1732615865581 | 2024-11-26 18:11:05 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31839 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1732615865612 | 2024-11-26 18:11:05 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31871 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1732615865629 | 2024-11-26 18:11:05 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31887 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1732615865629 | 2024-11-26 18:11:05 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31888 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1732615865655 | 2024-11-26 18:11:05 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31913 | 0 | - | - | - | - | main c.t.g.Application Started Application in 32.46 seconds (JVM running for 33.231)

info | 1 | 1732615865682 | 2024-11-26 18:11:05 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31941 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1732615865683 | 2024-11-26 18:11:05 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 4a32252a2f2f46aa96edd7c64e4c8d25 | - | - | - | - | 31941 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 229 | 1732615865688 | 2024-11-26 18:11:05 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | c42529b560ef4702abfea7bfae535a08 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 168 | 1732615868652 | 2024-11-26 18:11:08 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | StandardWrapper | initServlet | b0688305f3b945b4bca7cd960e353366 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 168 | 1732615868652 | 2024-11-26 18:11:08 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | AuthenticatorBase | invoke | b0688305f3b945b4bca7cd960e353366 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 168 | 1732615868655 | 2024-11-26 18:11:08 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | AuthenticatorBase | invoke | b0688305f3b945b4bca7cd960e353366 | - | - | - | - | 3 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 3 ms

info | 168 | 1732615868930 | 2024-11-26 18:11:08 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | manageChatRoom | roomChatData | 4e48a0bf24fc442badf5696433c24abc | - | - | - | - | 271 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-20","endTime":"2024-11-25","orgId":1001121,"teamId":null,"export":null,"status":null,"chatUuid":"","liveNo":"","nickName":null,"total":null,"type":null,"download":null}

info | 168 | 1732615869022 | 2024-11-26 18:11:09 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | manageChatRoom | roomChatData | 4e48a0bf24fc442badf5696433c24abc | - | - | - | - | 363 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-20,结束时间：2024-11-25,团队集合:[],hostUuids:[],chatStatus:null

info | 168 | 1732615869460 | 2024-11-26 18:11:09 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | manageChatRoom | roomChatData | 4e48a0bf24fc442badf5696433c24abc | - | - | - | - | 801 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-20,结束时间：2024-11-25,团队集合:[],hostUuids:[],chatStatus:null

info | 159 | 1732615875494 | 2024-11-26 18:11:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 3bf5e84fe4d04a52a755a70971c550dd | - | - | - | - | 9995 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732615884429 | 2024-11-26 18:11:24 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | manageChatRoom | roomChatData | 8a68a198c3004d94b948a1e9f962072b | - | - | - | - | 86 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-20","endTime":"2024-11-25","orgId":1001121,"teamId":null,"export":null,"status":null,"chatUuid":"","liveNo":"","nickName":null,"total":null,"type":null,"download":null}

info | 169 | 1732615884530 | 2024-11-26 18:11:24 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | manageChatRoom | roomChatData | 8a68a198c3004d94b948a1e9f962072b | - | - | - | - | 187 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-20,结束时间：2024-11-25,团队集合:[],hostUuids:[],chatStatus:null

info | 169 | 1732615884890 | 2024-11-26 18:11:24 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | manageChatRoom | roomChatData | 8a68a198c3004d94b948a1e9f962072b | - | - | - | - | 546 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-20,结束时间：2024-11-25,团队集合:[],hostUuids:[],chatStatus:null

info | 163 | 1732615887476 | 2024-11-26 18:11:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 432be5517df8434d8cea1ef1070bfcfc | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732615895497 | 2024-11-26 18:11:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 90ec2767d4a64e55835a9dc3b5742718 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732615905494 | 2024-11-26 18:11:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 90ec2767d4a64e55835a9dc3b5742718 | - | - | - | - | 9997 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 170 | 1732615913527 | 2024-11-26 18:11:53 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | manageChatRoom | roomChatData | b705c99664824b47a75cac45d16e66b3 | - | - | - | - | 68 | 0 | - | - | - | - | http-nio-8087-exec-4 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-20","endTime":"2024-11-25","orgId":1001121,"teamId":null,"export":null,"status":null,"chatUuid":"","liveNo":"","nickName":null,"total":null,"type":null,"download":null}

info | 170 | 1732615913610 | 2024-11-26 18:11:53 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | manageChatRoom | roomChatData | b705c99664824b47a75cac45d16e66b3 | - | - | - | - | 144 | 0 | - | - | - | - | http-nio-8087-exec-4 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-20,结束时间：2024-11-25,团队集合:[],hostUuids:[],chatStatus:null

info | 170 | 1732615913856 | 2024-11-26 18:11:53 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | manageChatRoom | roomChatData | b705c99664824b47a75cac45d16e66b3 | - | - | - | - | 391 | 0 | - | - | - | - | http-nio-8087-exec-4 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-20,结束时间：2024-11-25,团队集合:[],hostUuids:[],chatStatus:null

info | 164 | 1732615916606 | 2024-11-26 18:11:56 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 90ec2767d4a64e55835a9dc3b5742718 | - | - | - | - | 21109 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1732615925497 | 2024-11-26 18:12:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 432be5517df8434d8cea1ef1070bfcfc | - | - | - | - | 38021 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 171 | 1732615928489 | 2024-11-26 18:12:08 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | manageChatRoom | roomChatData | 97b0bc9012894875a4f1c5502d74b816 | - | - | - | - | 109 | 0 | - | - | - | - | http-nio-8087-exec-5 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-20","endTime":"2024-11-25","orgId":1001121,"teamId":null,"export":null,"status":null,"chatUuid":"","liveNo":"","nickName":null,"total":null,"type":null,"download":null}

info | 171 | 1732615928560 | 2024-11-26 18:12:08 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | manageChatRoom | roomChatData | 97b0bc9012894875a4f1c5502d74b816 | - | - | - | - | 180 | 0 | - | - | - | - | http-nio-8087-exec-5 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-20,结束时间：2024-11-25,团队集合:[],hostUuids:[],chatStatus:null

info | 171 | 1732615929127 | 2024-11-26 18:12:09 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | manageChatRoom | roomChatData | 97b0bc9012894875a4f1c5502d74b816 | - | - | - | - | 747 | 0 | - | - | - | - | http-nio-8087-exec-5 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-20,结束时间：2024-11-25,团队集合:[],hostUuids:[],chatStatus:null

info | 163 | 1732615935499 | 2024-11-26 18:12:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 432be5517df8434d8cea1ef1070bfcfc | - | - | - | - | 48024 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1732615943412 | 2024-11-26 18:12:23 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 412d7dc891194ec09e1b2bbc1ff03663 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1732615943481 | 2024-11-26 18:12:23 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 412d7dc891194ec09e1b2bbc1ff03663 | - | - | - | - | 69 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

