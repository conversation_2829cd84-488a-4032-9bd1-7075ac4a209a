warning | 125 | 1733740342645 | 2024-12-09 18:32:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 05346b3a53384275b949c6f57eb3982a | - | - | - | - | 220 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 125 | 1733740342645 | 2024-12-09 18:32:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 05346b3a53384275b949c6f57eb3982a | - | - | - | - | 221 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 265 | 1733740363955 | 2024-12-09 18:32:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | ee527dfddcd842ec94285aa1a31650f4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740343726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.4819, jvmUsedMemory=0.3449, jvmMaxMemory=3.5557, jvmMemoryUsage=0.097, diskUsed=231.2748, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 265 | 1733740363963 | 2024-12-09 18:32:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | ee527dfddcd842ec94285aa1a31650f4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740353726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.7056, jvmUsedMemory=0.5117, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1439, diskUsed=231.2749, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 265 | 1733740363964 | 2024-12-09 18:32:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | ee527dfddcd842ec94285aa1a31650f4 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740363723, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.5967, jvmUsedMemory=0.5178, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1456, diskUsed=231.277, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 132 | 1733740364020 | 2024-12-09 18:32:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | b87a165f48914791952b845c92fcc961 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 132 | 1733740364020 | 2024-12-09 18:32:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | b87a165f48914791952b845c92fcc961 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 132 | 1733740364025 | 2024-12-09 18:32:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | b87a165f48914791952b845c92fcc961 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 132 | 1733740364027 | 2024-12-09 18:32:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | b87a165f48914791952b845c92fcc961 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 132 | 1733740364027 | 2024-12-09 18:32:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | b87a165f48914791952b845c92fcc961 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 266 | 1733740393762 | 2024-12-09 18:33:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | e743f05edf3b434e9c1edb6d7b00e89a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740373729, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.2573, jvmUsedMemory=0.5406, jvmMaxMemory=3.5557, jvmMemoryUsage=0.152, diskUsed=231.2772, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 266 | 1733740393772 | 2024-12-09 18:33:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | e743f05edf3b434e9c1edb6d7b00e89a | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740383723, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.7495, jvmUsedMemory=0.5522, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1553, diskUsed=231.2782, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 266 | 1733740393791 | 2024-12-09 18:33:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | e743f05edf3b434e9c1edb6d7b00e89a | - | - | - | - | 31 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740393722, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.5474, jvmUsedMemory=0.5605, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1576, diskUsed=231.2697, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 312 | 1733740393796 | 2024-12-09 18:33:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | b46185bda71342cca838e22d1626f632 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 312 | 1733740393810 | 2024-12-09 18:33:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | b46185bda71342cca838e22d1626f632 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 311 | 1733740423766 | 2024-12-09 18:33:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | d67f26a46b144caba3ebdb2d09db4bad | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740403727, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.1553, jvmUsedMemory=0.5711, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1606, diskUsed=231.271, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 311 | 1733740423772 | 2024-12-09 18:33:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | d67f26a46b144caba3ebdb2d09db4bad | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740413724, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.0381, jvmUsedMemory=0.5804, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1632, diskUsed=231.2743, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 311 | 1733740423791 | 2024-12-09 18:33:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | d67f26a46b144caba3ebdb2d09db4bad | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740423726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.7524, jvmUsedMemory=0.5871, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1651, diskUsed=231.2755, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 313 | 1733740423797 | 2024-12-09 18:33:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | 951c7336cca54a94b577696820ea78bf | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 313 | 1733740423809 | 2024-12-09 18:33:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 951c7336cca54a94b577696820ea78bf | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 388 | 1733740453766 | 2024-12-09 18:34:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 86349148f618451b8596789af75b60cf | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740433726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.9414, jvmUsedMemory=0.5942, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1671, diskUsed=231.2767, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 389 | 1733740453787 | 2024-12-09 18:34:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | efa828a5d5fc4447b5c07245b284a253 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 388 | 1733740453792 | 2024-12-09 18:34:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 86349148f618451b8596789af75b60cf | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740443728, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5815, jvmUsedMemory=0.6031, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1696, diskUsed=231.28, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 389 | 1733740453793 | 2024-12-09 18:34:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | efa828a5d5fc4447b5c07245b284a253 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 388 | 1733740453793 | 2024-12-09 18:34:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 86349148f618451b8596789af75b60cf | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740453733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.418, jvmUsedMemory=0.6102, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1716, diskUsed=231.2823, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 389 | 1733740483772 | 2024-12-09 18:34:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | efa828a5d5fc4447b5c07245b284a253 | - | - | - | - | 29987 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740463726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1128, jvmUsedMemory=0.6213, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1747, diskUsed=231.2869, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 389 | 1733740483797 | 2024-12-09 18:34:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | Actor | aroundReceive | efa828a5d5fc4447b5c07245b284a253 | - | - | - | - | 30010 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740473726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2969, jvmUsedMemory=0.6302, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1772, diskUsed=231.2881, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 428 | 1733740483797 | 2024-12-09 18:34:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | 907ec793996a4b01907989cd14addb5f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 389 | 1733740483798 | 2024-12-09 18:34:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | Actor | aroundReceive | efa828a5d5fc4447b5c07245b284a253 | - | - | - | - | 30011 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740483724, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8633, jvmUsedMemory=0.6357, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1788, diskUsed=231.2898, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 428 | 1733740483798 | 2024-12-09 18:34:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 907ec793996a4b01907989cd14addb5f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 427 | 1733740513752 | 2024-12-09 18:35:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 57c2e117653a49e7b6fb418cee354e58 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740493724, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5029, jvmUsedMemory=0.644, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1811, diskUsed=231.2901, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 427 | 1733740513755 | 2024-12-09 18:35:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 57c2e117653a49e7b6fb418cee354e58 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740503727, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4058, jvmUsedMemory=0.6534, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1838, diskUsed=231.2896, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 427 | 1733740513755 | 2024-12-09 18:35:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 57c2e117653a49e7b6fb418cee354e58 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740513727, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9619, jvmUsedMemory=0.6603, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1857, diskUsed=231.2908, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 427 | 1733740513782 | 2024-12-09 18:35:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 57c2e117653a49e7b6fb418cee354e58 | - | - | - | - | 29 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 427 | 1733740513796 | 2024-12-09 18:35:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 57c2e117653a49e7b6fb418cee354e58 | - | - | - | - | 42 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 462 | 1733740543753 | 2024-12-09 18:35:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 816d4d4ba74e46d1bae9b0622fb017f7 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740523724, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6602, jvmUsedMemory=0.6712, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1888, diskUsed=231.2916, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 462 | 1733740543756 | 2024-12-09 18:35:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 816d4d4ba74e46d1bae9b0622fb017f7 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740533723, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2788, jvmUsedMemory=0.6795, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1911, diskUsed=231.2917, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 462 | 1733740543757 | 2024-12-09 18:35:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 816d4d4ba74e46d1bae9b0622fb017f7 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740543723, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2227, jvmUsedMemory=0.685, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1926, diskUsed=231.2918, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 462 | 1733740543781 | 2024-12-09 18:35:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 816d4d4ba74e46d1bae9b0622fb017f7 | - | - | - | - | 28 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 462 | 1733740543794 | 2024-12-09 18:35:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 816d4d4ba74e46d1bae9b0622fb017f7 | - | - | - | - | 41 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 465 | 1733740573766 | 2024-12-09 18:36:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | b2eeb5eaf1244fe08459b7f8a1b4a0a0 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740553728, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=8.7324, jvmUsedMemory=0.6922, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1947, diskUsed=231.2941, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=4)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 465 | 1733740573777 | 2024-12-09 18:36:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | b2eeb5eaf1244fe08459b7f8a1b4a0a0 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740563725, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.8374, jvmUsedMemory=0.6999, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1968, diskUsed=231.2962, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=5)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 465 | 1733740573814 | 2024-12-09 18:36:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | b2eeb5eaf1244fe08459b7f8a1b4a0a0 | - | - | - | - | 47 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740573727, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.3687, jvmUsedMemory=0.706, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1985, diskUsed=232.2813, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 465 | 1733740573830 | 2024-12-09 18:36:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | b2eeb5eaf1244fe08459b7f8a1b4a0a0 | - | - | - | - | 62 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 465 | 1733740573834 | 2024-12-09 18:36:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | b2eeb5eaf1244fe08459b7f8a1b4a0a0 | - | - | - | - | 66 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 535 | 1733740603754 | 2024-12-09 18:36:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 79d8aa9354534bcc9ce38846ee4c3053 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740583727, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.1255, jvmUsedMemory=0.7143, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2009, diskUsed=232.2817, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 535 | 1733740603776 | 2024-12-09 18:36:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 79d8aa9354534bcc9ce38846ee4c3053 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740593725, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.5771, jvmUsedMemory=0.7231, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2034, diskUsed=232.2812, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 535 | 1733740603791 | 2024-12-09 18:36:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 79d8aa9354534bcc9ce38846ee4c3053 | - | - | - | - | 36 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740603730, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.3599, jvmUsedMemory=0.7292, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2051, diskUsed=232.2843, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 536 | 1733740603796 | 2024-12-09 18:36:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | dfa6ffe58c514defa279a5764120c2fe | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 536 | 1733740603809 | 2024-12-09 18:36:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | dfa6ffe58c514defa279a5764120c2fe | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 610 | 1733740633764 | 2024-12-09 18:37:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 0408524525f74d5597ccf34b65679b5a | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740613727, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.0894, jvmUsedMemory=0.7372, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2073, diskUsed=232.2863, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 610 | 1733740633801 | 2024-12-09 18:37:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 0408524525f74d5597ccf34b65679b5a | - | - | - | - | 37 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740623724, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.5469, jvmUsedMemory=0.7455, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2097, diskUsed=232.2863, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 610 | 1733740633819 | 2024-12-09 18:37:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 0408524525f74d5597ccf34b65679b5a | - | - | - | - | 55 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740633726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.7676, jvmUsedMemory=0.7516, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2114, diskUsed=232.283, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 610 | 1733740633826 | 2024-12-09 18:37:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 0408524525f74d5597ccf34b65679b5a | - | - | - | - | 61 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 610 | 1733740633855 | 2024-12-09 18:37:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 0408524525f74d5597ccf34b65679b5a | - | - | - | - | 91 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 535 | 1733740663755 | 2024-12-09 18:37:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | Actor | aroundReceive | 79d8aa9354534bcc9ce38846ee4c3053 | - | - | - | - | 60002 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740643727, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5083, jvmUsedMemory=0.7657, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2154, diskUsed=232.2841, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 535 | 1733740663758 | 2024-12-09 18:37:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | Actor | aroundReceive | 79d8aa9354534bcc9ce38846ee4c3053 | - | - | - | - | 60004 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740653726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.356, jvmUsedMemory=0.7737, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2176, diskUsed=232.2843, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 535 | 1733740663761 | 2024-12-09 18:37:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | Actor | aroundReceive | 79d8aa9354534bcc9ce38846ee4c3053 | - | - | - | - | 60010 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740663726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9194, jvmUsedMemory=0.7793, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2192, diskUsed=232.2853, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 535 | 1733740663797 | 2024-12-09 18:37:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ActorCell | receiveMessage | 79d8aa9354534bcc9ce38846ee4c3053 | - | - | - | - | 60043 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 535 | 1733740663799 | 2024-12-09 18:37:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ActorCell | receiveMessage | 79d8aa9354534bcc9ce38846ee4c3053 | - | - | - | - | 60044 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 611 | 1733740693807 | 2024-12-09 18:38:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 5bea7ff8101b494ebfa53ccde1ab098e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740673724, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5566, jvmUsedMemory=0.787, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2213, diskUsed=232.2867, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 611 | 1733740693830 | 2024-12-09 18:38:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 5bea7ff8101b494ebfa53ccde1ab098e | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740683726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5698, jvmUsedMemory=0.8688, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2443, diskUsed=232.2868, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 611 | 1733740693838 | 2024-12-09 18:38:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 5bea7ff8101b494ebfa53ccde1ab098e | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740693723, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8022, jvmUsedMemory=0.8749, jvmMaxMemory=3.5557, jvmMemoryUsage=0.246, diskUsed=232.2881, diskTotal=460.4317, diskUsage=0.5045, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 611 | 1733740693924 | 2024-12-09 18:38:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 5bea7ff8101b494ebfa53ccde1ab098e | - | - | - | - | 106 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 611 | 1733740693924 | 2024-12-09 18:38:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 5bea7ff8101b494ebfa53ccde1ab098e | - | - | - | - | 106 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 698 | 1733740723760 | 2024-12-09 18:38:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 29073715277949b5af62448aaa26abb7 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740703726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3647, jvmUsedMemory=0.884, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2486, diskUsed=232.3174, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 698 | 1733740723776 | 2024-12-09 18:38:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 29073715277949b5af62448aaa26abb7 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740713726, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9209, jvmUsedMemory=0.8912, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2506, diskUsed=232.3183, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 733 | 1733740723791 | 2024-12-09 18:38:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | c8cd3cf546c3455ca953be73ea330f01 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 733 | 1733740723808 | 2024-12-09 18:38:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | c8cd3cf546c3455ca953be73ea330f01 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 698 | 1733740723808 | 2024-12-09 18:38:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 29073715277949b5af62448aaa26abb7 | - | - | - | - | 47 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740723725, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.333, jvmUsedMemory=0.8991, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2529, diskUsed=232.3185, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 734 | 1733740753757 | 2024-12-09 18:39:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | eec7bad06f8544adace89b22bb767805 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740733723, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7969, jvmUsedMemory=0.9083, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2554, diskUsed=232.3186, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 734 | 1733740753778 | 2024-12-09 18:39:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | eec7bad06f8544adace89b22bb767805 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740743723, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7476, jvmUsedMemory=0.916, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2576, diskUsed=232.3186, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 734 | 1733740753794 | 2024-12-09 18:39:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | eec7bad06f8544adace89b22bb767805 | - | - | - | - | 37 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740753727, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4111, jvmUsedMemory=0.9215, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2592, diskUsed=232.32, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 770 | 1733740753796 | 2024-12-09 18:39:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | 23ad3f85c2c340f18c8d2eb6fb76331b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 770 | 1733740753828 | 2024-12-09 18:39:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 23ad3f85c2c340f18c8d2eb6fb76331b | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 804 | 1733740783753 | 2024-12-09 18:39:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 94ef1ebc62514019ab6a5d6e5391d557 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740763724, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5205, jvmUsedMemory=0.9299, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2615, diskUsed=232.3253, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 804 | 1733740783763 | 2024-12-09 18:39:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 94ef1ebc62514019ab6a5d6e5391d557 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740773725, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5742, jvmUsedMemory=0.9376, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2637, diskUsed=232.3214, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 804 | 1733740783781 | 2024-12-09 18:39:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 94ef1ebc62514019ab6a5d6e5391d557 | - | - | - | - | 29 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740783724, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.104, jvmUsedMemory=0.9431, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2652, diskUsed=232.3214, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 804 | 1733740783786 | 2024-12-09 18:39:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 94ef1ebc62514019ab6a5d6e5391d557 | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 804 | 1733740783818 | 2024-12-09 18:39:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 94ef1ebc62514019ab6a5d6e5391d557 | - | - | - | - | 65 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 831 | 1733740814384 | 2024-12-09 18:40:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 85cf7671a8b24e7c8780401b3df920a9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740793723, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4614, jvmUsedMemory=0.9503, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2673, diskUsed=232.3214, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 831 | 1733740814386 | 2024-12-09 18:40:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 85cf7671a8b24e7c8780401b3df920a9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740804422, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0825, jvmUsedMemory=0.9748, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2741, diskUsed=232.3218, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 831 | 1733740814403 | 2024-12-09 18:40:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 85cf7671a8b24e7c8780401b3df920a9 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 831 | 1733740814403 | 2024-12-09 18:40:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 85cf7671a8b24e7c8780401b3df920a9 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 769 | 1733740847235 | 2024-12-09 18:40:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 5cc480cd679f436d8c6d06e5530e6484 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740823723, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4116, jvmUsedMemory=0.9917, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2789, diskUsed=232.3228, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 769 | 1733740847237 | 2024-12-09 18:40:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 5cc480cd679f436d8c6d06e5530e6484 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740833727, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0405, jvmUsedMemory=0.9989, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2809, diskUsed=232.3228, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 769 | 1733740847238 | 2024-12-09 18:40:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 5cc480cd679f436d8c6d06e5530e6484 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740847231, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.2441, jvmUsedMemory=1.0094, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2839, diskUsed=232.323, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 769 | 1733740847265 | 2024-12-09 18:40:47 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 5cc480cd679f436d8c6d06e5530e6484 | - | - | - | - | 29 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 769 | 1733740847266 | 2024-12-09 18:40:47 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 5cc480cd679f436d8c6d06e5530e6484 | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1733740850157 | 2024-12-09 18:40:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 97938861c8ef47f8a0d9b30e20a5e49a | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1733740850158 | 2024-12-09 18:40:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 0122fae416e14bd4af623baa176cc99c | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1733740850158 | 2024-12-09 18:40:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 97938861c8ef47f8a0d9b30e20a5e49a | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1733740850167 | 2024-12-09 18:40:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 0122fae416e14bd4af623baa176cc99c | - | - | - | - | 10 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 769 | 1733740850257 | 2024-12-09 18:40:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | 5cc480cd679f436d8c6d06e5530e6484 | - | - | - | - | 3021 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 126 | 1733740980754 | 2024-12-09 18:43:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 676f53a8001d4d5f83e7267b94abc3f0 | - | - | - | - | 158 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 126 | 1733740980756 | 2024-12-09 18:43:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 676f53a8001d4d5f83e7267b94abc3f0 | - | - | - | - | 159 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 263 | 1733741001646 | 2024-12-09 18:43:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 0690cdc6fb2742d3aa5ff29736c7ae39 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740981478, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8813, jvmUsedMemory=0.3582, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1008, diskUsed=231.3381, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 263 | 1733741001656 | 2024-12-09 18:43:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 0690cdc6fb2742d3aa5ff29736c7ae39 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733740991479, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6045, jvmUsedMemory=0.5187, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1459, diskUsed=231.3392, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 263 | 1733741001657 | 2024-12-09 18:43:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 0690cdc6fb2742d3aa5ff29736c7ae39 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741001480, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0264, jvmUsedMemory=0.5291, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1488, diskUsed=231.3393, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 126 | 1733741001701 | 2024-12-09 18:43:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 676f53a8001d4d5f83e7267b94abc3f0 | - | - | - | - | 21104 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 126 | 1733741001701 | 2024-12-09 18:43:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 676f53a8001d4d5f83e7267b94abc3f0 | - | - | - | - | 21104 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 126 | 1733741001703 | 2024-12-09 18:43:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | 676f53a8001d4d5f83e7267b94abc3f0 | - | - | - | - | 21107 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 126 | 1733741001706 | 2024-12-09 18:43:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ActorCell | receiveMessage | 676f53a8001d4d5f83e7267b94abc3f0 | - | - | - | - | 21109 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 126 | 1733741001706 | 2024-12-09 18:43:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ActorCell | receiveMessage | 676f53a8001d4d5f83e7267b94abc3f0 | - | - | - | - | 21109 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 264 | 1733741031520 | 2024-12-09 18:43:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 37bd8cf0505e453c82c4c11ffa9c188a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741011480, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8682, jvmUsedMemory=0.5525, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1554, diskUsed=232.3394, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 264 | 1733741031521 | 2024-12-09 18:43:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 37bd8cf0505e453c82c4c11ffa9c188a | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741021481, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4941, jvmUsedMemory=0.5606, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1577, diskUsed=232.3404, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 264 | 1733741031522 | 2024-12-09 18:43:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 37bd8cf0505e453c82c4c11ffa9c188a | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741031481, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5586, jvmUsedMemory=0.5688, jvmMaxMemory=3.5557, jvmMemoryUsage=0.16, diskUsed=232.3405, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 264 | 1733741031541 | 2024-12-09 18:43:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 37bd8cf0505e453c82c4c11ffa9c188a | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 264 | 1733741031542 | 2024-12-09 18:43:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 37bd8cf0505e453c82c4c11ffa9c188a | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 395 | 1733741061510 | 2024-12-09 18:44:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | fb303e4d2fe14392820f206ff75cfc32 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741041481, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4053, jvmUsedMemory=0.5824, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1638, diskUsed=232.3405, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 395 | 1733741061511 | 2024-12-09 18:44:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | fb303e4d2fe14392820f206ff75cfc32 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741055918, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.7705, jvmUsedMemory=0.6729, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1892, diskUsed=231.3414, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 395 | 1733741061512 | 2024-12-09 18:44:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | fb303e4d2fe14392820f206ff75cfc32 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741061479, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4683, jvmUsedMemory=0.7192, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2023, diskUsed=231.3414, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 395 | 1733741061526 | 2024-12-09 18:44:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | fb303e4d2fe14392820f206ff75cfc32 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 395 | 1733741061527 | 2024-12-09 18:44:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | fb303e4d2fe14392820f206ff75cfc32 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 426 | 1733741099415 | 2024-12-09 18:44:59 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 402e956181b84f24ba4787fa0b41322d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741071479, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2964, jvmUsedMemory=0.7323, jvmMaxMemory=3.5557, jvmMemoryUsage=0.206, diskUsed=231.3376, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 426 | 1733741099417 | 2024-12-09 18:44:59 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 402e956181b84f24ba4787fa0b41322d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741083585, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=9.2153, jvmUsedMemory=0.7378, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2075, diskUsed=231.3377, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=4)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 426 | 1733741099417 | 2024-12-09 18:44:59 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 402e956181b84f24ba4787fa0b41322d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741099386, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.6313, jvmUsedMemory=0.7432, jvmMaxMemory=3.5557, jvmMemoryUsage=0.209, diskUsed=231.3389, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=5)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 396 | 1733741099439 | 2024-12-09 18:44:59 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | 2ed2c86e05bc4b99b78abf40a62eeda4 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 396 | 1733741099439 | 2024-12-09 18:44:59 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 2ed2c86e05bc4b99b78abf40a62eeda4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1733741122830 | 2024-12-09 18:45:22 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 0c0ee9e39dbf4eec82828598ac83cb34 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1733741122831 | 2024-12-09 18:45:22 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | a0337dacb77942bd96ae1865bcb1c61f | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1733741122842 | 2024-12-09 18:45:22 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | a0337dacb77942bd96ae1865bcb1c61f | - | - | - | - | 11 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1733741122845 | 2024-12-09 18:45:22 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 0c0ee9e39dbf4eec82828598ac83cb34 | - | - | - | - | 17 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 124 | 1733741157816 | 2024-12-09 18:45:57 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | e6b60e9f1ad74ab3893739d8a860a209 | - | - | - | - | 98 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 124 | 1733741157816 | 2024-12-09 18:45:57 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | e6b60e9f1ad74ab3893739d8a860a209 | - | - | - | - | 98 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 270 | 1733741187270 | 2024-12-09 18:46:27 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 735d2b57e9ea45bf860627bf0e1f30fe | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741158485, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=8.6914, jvmUsedMemory=0.2777, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0781, diskUsed=231.336, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=5)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1733741187283 | 2024-12-09 18:46:27 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 735d2b57e9ea45bf860627bf0e1f30fe | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741168488, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.957, jvmUsedMemory=0.4135, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1163, diskUsed=232.3386, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1733741187284 | 2024-12-09 18:46:27 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 735d2b57e9ea45bf860627bf0e1f30fe | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741187251, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.293, jvmUsedMemory=0.5101, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1435, diskUsed=232.3403, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 124 | 1733741187323 | 2024-12-09 18:46:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | e6b60e9f1ad74ab3893739d8a860a209 | - | - | - | - | 29605 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1733741187323 | 2024-12-09 18:46:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | e6b60e9f1ad74ab3893739d8a860a209 | - | - | - | - | 29605 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1733741187325 | 2024-12-09 18:46:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | e6b60e9f1ad74ab3893739d8a860a209 | - | - | - | - | 29607 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1733741187326 | 2024-12-09 18:46:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ActorCell | receiveMessage | e6b60e9f1ad74ab3893739d8a860a209 | - | - | - | - | 29609 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1733741187327 | 2024-12-09 18:46:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ActorCell | receiveMessage | e6b60e9f1ad74ab3893739d8a860a209 | - | - | - | - | 29609 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 271 | 1733741209180 | 2024-12-09 18:46:49 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 7f926d8633e3402c89d39c5b7ab72f25 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741189151, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.8687, jvmUsedMemory=0.529, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1488, diskUsed=232.3413, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 271 | 1733741209181 | 2024-12-09 18:46:49 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 7f926d8633e3402c89d39c5b7ab72f25 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741198488, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.5586, jvmUsedMemory=0.5456, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1534, diskUsed=232.3415, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 271 | 1733741209181 | 2024-12-09 18:46:49 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 7f926d8633e3402c89d39c5b7ab72f25 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741208486, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.6299, jvmUsedMemory=0.5532, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1556, diskUsed=232.3417, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 271 | 1733741209211 | 2024-12-09 18:46:49 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 7f926d8633e3402c89d39c5b7ab72f25 | - | - | - | - | 31 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 271 | 1733741209212 | 2024-12-09 18:46:49 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 7f926d8633e3402c89d39c5b7ab72f25 | - | - | - | - | 32 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 270 | 1733741238515 | 2024-12-09 18:47:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | Actor | aroundReceive | 735d2b57e9ea45bf860627bf0e1f30fe | - | - | - | - | 51232 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741218487, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.3975, jvmUsedMemory=0.5651, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1589, diskUsed=232.3417, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1733741238516 | 2024-12-09 18:47:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | Actor | aroundReceive | 735d2b57e9ea45bf860627bf0e1f30fe | - | - | - | - | 51233 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741228489, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.708, jvmUsedMemory=0.5745, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1616, diskUsed=232.3383, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1733741238516 | 2024-12-09 18:47:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | Actor | aroundReceive | 735d2b57e9ea45bf860627bf0e1f30fe | - | - | - | - | 51234 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741238488, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.3838, jvmUsedMemory=0.5822, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1637, diskUsed=232.3384, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1733741238541 | 2024-12-09 18:47:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ActorCell | receiveMessage | 735d2b57e9ea45bf860627bf0e1f30fe | - | - | - | - | 51258 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 270 | 1733741238541 | 2024-12-09 18:47:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ActorCell | receiveMessage | 735d2b57e9ea45bf860627bf0e1f30fe | - | - | - | - | 51258 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 400 | 1733741268536 | 2024-12-09 18:47:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 95dc137ad9ff415bbe8243e6b3498b15 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741248489, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.5576, jvmUsedMemory=0.5935, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1669, diskUsed=232.3395, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 400 | 1733741268538 | 2024-12-09 18:47:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 95dc137ad9ff415bbe8243e6b3498b15 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741258485, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.8628, jvmUsedMemory=0.6074, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1708, diskUsed=232.3395, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 400 | 1733741268538 | 2024-12-09 18:47:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 95dc137ad9ff415bbe8243e6b3498b15 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741268489, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.0347, jvmUsedMemory=0.6145, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1728, diskUsed=232.3407, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 354 | 1733741268557 | 2024-12-09 18:47:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | ee39c6393b434cf4b441d436c68526d8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 354 | 1733741268558 | 2024-12-09 18:47:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | ee39c6393b434cf4b441d436c68526d8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 400 | 1733741298506 | 2024-12-09 18:48:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | Actor | aroundReceive | 95dc137ad9ff415bbe8243e6b3498b15 | - | - | - | - | 29969 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741278485, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4878, jvmUsedMemory=0.6225, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1751, diskUsed=232.3408, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 400 | 1733741298507 | 2024-12-09 18:48:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | Actor | aroundReceive | 95dc137ad9ff415bbe8243e6b3498b15 | - | - | - | - | 29970 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741288486, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1851, jvmUsedMemory=0.6296, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1771, diskUsed=232.341, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 400 | 1733741298507 | 2024-12-09 18:48:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | Actor | aroundReceive | 95dc137ad9ff415bbe8243e6b3498b15 | - | - | - | - | 29970 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741298486, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.541, jvmUsedMemory=0.6379, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1794, diskUsed=232.3411, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 400 | 1733741298518 | 2024-12-09 18:48:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ActorCell | receiveMessage | 95dc137ad9ff415bbe8243e6b3498b15 | - | - | - | - | 29981 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 400 | 1733741298519 | 2024-12-09 18:48:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ActorCell | receiveMessage | 95dc137ad9ff415bbe8243e6b3498b15 | - | - | - | - | 29982 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 437 | 1733741328521 | 2024-12-09 18:48:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 14e0156444524952b9b0e33c3f4c6028 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741308484, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6978, jvmUsedMemory=0.6484, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1824, diskUsed=232.3412, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 437 | 1733741328522 | 2024-12-09 18:48:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 14e0156444524952b9b0e33c3f4c6028 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741318488, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3628, jvmUsedMemory=0.6567, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1847, diskUsed=232.3412, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 437 | 1733741328523 | 2024-12-09 18:48:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 14e0156444524952b9b0e33c3f4c6028 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741328485, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1465, jvmUsedMemory=0.6649, jvmMaxMemory=3.5557, jvmMemoryUsage=0.187, diskUsed=232.3422, diskTotal=460.4317, diskUsage=0.5046, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 437 | 1733741328546 | 2024-12-09 18:48:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 14e0156444524952b9b0e33c3f4c6028 | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 437 | 1733741328548 | 2024-12-09 18:48:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 14e0156444524952b9b0e33c3f4c6028 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 438 | 1733741358505 | 2024-12-09 18:49:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 2c82c6a6b6cf4783be095eb73c95a729 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741338486, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9697, jvmUsedMemory=0.676, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1901, diskUsed=231.297, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 438 | 1733741358506 | 2024-12-09 18:49:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 2c82c6a6b6cf4783be095eb73c95a729 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741348487, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5928, jvmUsedMemory=0.6847, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1926, diskUsed=231.2962, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 438 | 1733741358506 | 2024-12-09 18:49:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 2c82c6a6b6cf4783be095eb73c95a729 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741358483, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1934, jvmUsedMemory=0.692, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1946, diskUsed=231.2961, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 438 | 1733741358515 | 2024-12-09 18:49:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 2c82c6a6b6cf4783be095eb73c95a729 | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 438 | 1733741358515 | 2024-12-09 18:49:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 2c82c6a6b6cf4783be095eb73c95a729 | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 521 | 1733741388507 | 2024-12-09 18:49:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | fc81f1ef5c544cdea78877031a81001a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741368483, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1763, jvmUsedMemory=0.7043, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1981, diskUsed=231.2973, diskTotal=460.4317, diskUsage=0.5023, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 521 | 1733741388508 | 2024-12-09 18:49:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | fc81f1ef5c544cdea78877031a81001a | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741378484, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1553, jvmUsedMemory=0.7114, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2001, diskUsed=231.3015, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 521 | 1733741388509 | 2024-12-09 18:49:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | fc81f1ef5c544cdea78877031a81001a | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741388485, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2046, jvmUsedMemory=0.7179, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2019, diskUsed=231.3015, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 521 | 1733741388528 | 2024-12-09 18:49:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | fc81f1ef5c544cdea78877031a81001a | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 521 | 1733741388528 | 2024-12-09 18:49:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | fc81f1ef5c544cdea78877031a81001a | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 593 | 1733741418539 | 2024-12-09 18:50:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | b4010b452c534e4fafc12654729afcef | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741398501, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1494, jvmUsedMemory=0.725, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2039, diskUsed=231.3016, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 593 | 1733741418541 | 2024-12-09 18:50:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | b4010b452c534e4fafc12654729afcef | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741408506, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8857, jvmUsedMemory=0.7333, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2062, diskUsed=231.3006, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 593 | 1733741418542 | 2024-12-09 18:50:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | b4010b452c534e4fafc12654729afcef | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741418510, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2231, jvmUsedMemory=0.7401, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2081, diskUsed=231.3017, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 593 | 1733741418575 | 2024-12-09 18:50:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | b4010b452c534e4fafc12654729afcef | - | - | - | - | 35 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 593 | 1733741418576 | 2024-12-09 18:50:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | b4010b452c534e4fafc12654729afcef | - | - | - | - | 36 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1733741440770 | 2024-12-09 18:50:40 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 8730d1053bab466ea2d51ca492e0ef38 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1733741440770 | 2024-12-09 18:50:40 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 1dc265e42fae45b583dbcbbc6346765f | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1733741440772 | 2024-12-09 18:50:40 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 1dc265e42fae45b583dbcbbc6346765f | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1733741440776 | 2024-12-09 18:50:40 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 8730d1053bab466ea2d51ca492e0ef38 | - | - | - | - | 6 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 594 | 1733741440838 | 2024-12-09 18:50:40 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | 789f7f2e95aa4fb985570b5942a7de5c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 519 | 1733741440858 | 2024-12-09 18:50:40 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | d38759da11b34eaf9d2f1ee77c880491 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741428512, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5376, jvmUsedMemory=0.7496, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2108, diskUsed=231.3044, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 519 | 1733741440860 | 2024-12-09 18:50:40 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | d38759da11b34eaf9d2f1ee77c880491 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1733741438514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4609, jvmUsedMemory=0.7585, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2133, diskUsed=231.3044, diskTotal=460.4317, diskUsage=0.5024, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 519 | 1733741440892 | 2024-12-09 18:50:40 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | d38759da11b34eaf9d2f1ee77c880491 | - | - | - | - | 34 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

