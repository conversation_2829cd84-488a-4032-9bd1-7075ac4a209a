warning | 125 | 1753666146099 | 2025-07-28 09:29:06 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 7ec8d10563554656ba5119c94592f7b2 | - | - | - | - | 114 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 125 | 1753666146099 | 2025-07-28 09:29:06 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 7ec8d10563554656ba5119c94592f7b2 | - | - | - | - | 114 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 228 | 1753666150901 | 2025-07-28 09:29:10 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 613e406952d743bda361545dceeab9d4 | - | - | - | - | 4043 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1136 ms]

warning | 258 | 1753666190732 | 2025-07-28 09:29:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 8d28a03a8af34a33b99779aa027a391a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666146716, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1855, jvmUsedMemory=0.6794, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1911, diskUsed=203.929, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 258 | 1753666190746 | 2025-07-28 09:29:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 8d28a03a8af34a33b99779aa027a391a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666190693, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8652, jvmUsedMemory=0.8458, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2379, diskUsed=203.9226, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 258 | 1753666190747 | 2025-07-28 09:29:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 8d28a03a8af34a33b99779aa027a391a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666190705, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8652, jvmUsedMemory=0.8467, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2381, diskUsed=203.9226, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 258 | 1753666190748 | 2025-07-28 09:29:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | Actor | aroundReceive | 8d28a03a8af34a33b99779aa027a391a | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666190710, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8652, jvmUsedMemory=0.8481, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2385, diskUsed=203.9226, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 258 | 1753666190750 | 2025-07-28 09:29:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | Actor | aroundReceive | 8d28a03a8af34a33b99779aa027a391a | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666190711, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8652, jvmUsedMemory=0.8481, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2385, diskUsed=203.9226, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 41 | 1753666190764 | 2025-07-28 09:29:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 8e761584daa44353b125f32a39c35426 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 45 | 1753666190765 | 2025-07-28 09:29:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 2565b65961d44d9d91c3840063172088 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 45 | 1753666190765 | 2025-07-28 09:29:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 2565b65961d44d9d91c3840063172088 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753666190767 | 2025-07-28 09:29:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 8e761584daa44353b125f32a39c35426 | - | - | - | - | 4 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 125 | 1753666190812 | 2025-07-28 09:29:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 7ec8d10563554656ba5119c94592f7b2 | - | - | - | - | 44828 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 125 | 1753666190820 | 2025-07-28 09:29:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 7ec8d10563554656ba5119c94592f7b2 | - | - | - | - | 44835 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 125 | 1753666190820 | 2025-07-28 09:29:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | 7ec8d10563554656ba5119c94592f7b2 | - | - | - | - | 44835 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 126 | 1753666249979 | 2025-07-28 09:30:49 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | e11973cd66614fa89cd904e0b8cbd39c | - | - | - | - | 160 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 126 | 1753666249980 | 2025-07-28 09:30:49 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | e11973cd66614fa89cd904e0b8cbd39c | - | - | - | - | 161 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 41 | 1753666253527 | 2025-07-28 09:30:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 3619883156be41fab4a7e30e742e6009 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 45 | 1753666253527 | 2025-07-28 09:30:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | d86be3b660d94bcc8d8172a6890f6fd1 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 45 | 1753666253527 | 2025-07-28 09:30:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | d86be3b660d94bcc8d8172a6890f6fd1 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753666253529 | 2025-07-28 09:30:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 3619883156be41fab4a7e30e742e6009 | - | - | - | - | 2 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 126 | 1753666253572 | 2025-07-28 09:30:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | e11973cd66614fa89cd904e0b8cbd39c | - | - | - | - | 3754 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 128 | 1753666357300 | 2025-07-28 09:32:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 9aa9e3f9d3ac43c8a24547a6eca517ee | - | - | - | - | 108 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 128 | 1753666357301 | 2025-07-28 09:32:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 9aa9e3f9d3ac43c8a24547a6eca517ee | - | - | - | - | 109 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 232 | 1753666374763 | 2025-07-28 09:32:54 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | b2a40898fe9a4796a12798713b4e910a | - | - | - | - | 16688 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 11348 ms]

warning | 165 | 1753666434372 | 2025-07-28 09:33:54 | v2/ThreadPoolExecutor$Worker/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor$Worker | run | c2d8ece912fb42da88aab77046afbce4 | - | - | - | - | 0 | 0 | - | - | - | - | HikariPool-1 housekeeper c.z.h.p.HikariPool HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m16s349ms).

warning | 305 | 1753666434421 | 2025-07-28 09:33:54 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 2a2efd75b92c4bb881c0388a64d79a05 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666357922, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0513, jvmUsedMemory=0.7643, jvmMaxMemory=3.5557, jvmMemoryUsage=0.215, diskUsed=203.9745, diskTotal=460.4317, diskUsage=0.443, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 45 | 1753666434430 | 2025-07-28 09:33:54 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | f3bdd8bb43da478c9d98a2ea478395bf | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 45 | 1753666434430 | 2025-07-28 09:33:54 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | f3bdd8bb43da478c9d98a2ea478395bf | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 305 | 1753666434430 | 2025-07-28 09:33:54 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 2a2efd75b92c4bb881c0388a64d79a05 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666370684, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1011, jvmUsedMemory=0.8934, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2513, diskUsed=203.9746, diskTotal=460.4317, diskUsage=0.443, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 305 | 1753666434431 | 2025-07-28 09:33:54 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 2a2efd75b92c4bb881c0388a64d79a05 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666434368, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5249, jvmUsedMemory=0.9704, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2729, diskUsed=203.9416, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 305 | 1753666434431 | 2025-07-28 09:33:54 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | Actor | aroundReceive | 2a2efd75b92c4bb881c0388a64d79a05 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666434379, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5249, jvmUsedMemory=0.9745, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2741, diskUsed=203.9416, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 305 | 1753666434431 | 2025-07-28 09:33:54 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | Actor | aroundReceive | 2a2efd75b92c4bb881c0388a64d79a05 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666434387, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5249, jvmUsedMemory=0.975, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2742, diskUsed=203.9416, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 41 | 1753666434434 | 2025-07-28 09:33:54 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 99acd661656b4d178f63a2269b0b837b | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 135 | 1753666434436 | 2025-07-28 09:33:54 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 07b6f42f19324eb4a04a66d8df7bb959 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666434388, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5249, jvmUsedMemory=0.9755, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2743, diskUsed=203.9416, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 135 | 1753666434437 | 2025-07-28 09:33:54 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 07b6f42f19324eb4a04a66d8df7bb959 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666434391, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5249, jvmUsedMemory=0.977, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2748, diskUsed=203.9416, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 41 | 1753666434437 | 2025-07-28 09:33:54 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 99acd661656b4d178f63a2269b0b837b | - | - | - | - | 3 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 135 | 1753666434437 | 2025-07-28 09:33:54 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 07b6f42f19324eb4a04a66d8df7bb959 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666434394, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5249, jvmUsedMemory=0.9785, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2752, diskUsed=203.9416, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 308 | 1753666434497 | 2025-07-28 09:33:54 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | f97d76d69d7145919563d234864bb5e4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 308 | 1753666434500 | 2025-07-28 09:33:54 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | f97d76d69d7145919563d234864bb5e4 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 308 | 1753666434501 | 2025-07-28 09:33:54 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | f97d76d69d7145919563d234864bb5e4 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 308 | 1753666434510 | 2025-07-28 09:33:54 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | f97d76d69d7145919563d234864bb5e4 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 308 | 1753666434517 | 2025-07-28 09:33:54 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | f97d76d69d7145919563d234864bb5e4 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 308 | 1753666434519 | 2025-07-28 09:33:54 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | f97d76d69d7145919563d234864bb5e4 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1753666488641 | 2025-07-28 09:34:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | e8c7b99f96bf43968d9ee4963ee309fe | - | - | - | - | 113 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 127 | 1753666488641 | 2025-07-28 09:34:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | e8c7b99f96bf43968d9ee4963ee309fe | - | - | - | - | 113 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 41 | 1753666502150 | 2025-07-28 09:35:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 44cab577e0524f20b010f55dc623356e | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 45 | 1753666502151 | 2025-07-28 09:35:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 7fbe86d89e8d48ca9516972ba3be8fb5 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 45 | 1753666502153 | 2025-07-28 09:35:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 7fbe86d89e8d48ca9516972ba3be8fb5 | - | - | - | - | 2 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753666502154 | 2025-07-28 09:35:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 44cab577e0524f20b010f55dc623356e | - | - | - | - | 2 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 134 | 1753666502193 | 2025-07-28 09:35:02 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | a34c93b55c5f4148a5f7bc1977ec7805 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 226 | 1753666502236 | 2025-07-28 09:35:02 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 9d18e44fa0e24f2f8e734fb0c651286a | - | - | - | - | 12695 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 12524 ms]

warning | 130 | 1753666816320 | 2025-07-28 09:40:16 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | b99ac52b0eeb4b02864f4e163030818b | - | - | - | - | 133 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 130 | 1753666816320 | 2025-07-28 09:40:16 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | b99ac52b0eeb4b02864f4e163030818b | - | - | - | - | 134 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 282 | 1753666850406 | 2025-07-28 09:40:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | b2bb135f27ca44859f82c991b25d5514 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666816994, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5708, jvmUsedMemory=0.3316, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0933, diskUsed=203.9404, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 282 | 1753666850411 | 2025-07-28 09:40:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | b2bb135f27ca44859f82c991b25d5514 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666831814, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3569, jvmUsedMemory=0.4863, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1368, diskUsed=203.9408, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 282 | 1753666850411 | 2025-07-28 09:40:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | b2bb135f27ca44859f82c991b25d5514 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666850378, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0938, jvmUsedMemory=0.4931, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1387, diskUsed=203.9401, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 282 | 1753666850411 | 2025-07-28 09:40:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | Actor | aroundReceive | b2bb135f27ca44859f82c991b25d5514 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666850386, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0938, jvmUsedMemory=0.4955, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1393, diskUsed=203.9401, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 281 | 1753666850464 | 2025-07-28 09:40:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | f59e0c43a6094852b15a3ac853983c87 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 281 | 1753666850465 | 2025-07-28 09:40:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | f59e0c43a6094852b15a3ac853983c87 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 281 | 1753666850467 | 2025-07-28 09:40:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | f59e0c43a6094852b15a3ac853983c87 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 281 | 1753666850470 | 2025-07-28 09:40:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | f59e0c43a6094852b15a3ac853983c87 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 281 | 1753666850470 | 2025-07-28 09:40:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | f59e0c43a6094852b15a3ac853983c87 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 228 | 1753666875258 | 2025-07-28 09:41:15 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 0661a63c89b34518a4c6346008130b49 | - | - | - | - | 58101 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 57933 ms]

warning | 283 | 1753666877020 | 2025-07-28 09:41:17 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | f8200f21cd724bcba70d1bc663c81ccb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666856997, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8457, jvmUsedMemory=0.5425, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1526, diskUsed=203.9316, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 283 | 1753666877021 | 2025-07-28 09:41:17 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | f8200f21cd724bcba70d1bc663c81ccb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666866995, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6353, jvmUsedMemory=0.5528, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1555, diskUsed=203.9319, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 283 | 1753666877022 | 2025-07-28 09:41:17 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | f8200f21cd724bcba70d1bc663c81ccb | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666876994, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4634, jvmUsedMemory=0.5687, jvmMaxMemory=3.5557, jvmMemoryUsage=0.16, diskUsed=203.9321, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 283 | 1753666877039 | 2025-07-28 09:41:17 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | f8200f21cd724bcba70d1bc663c81ccb | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 283 | 1753666877040 | 2025-07-28 09:41:17 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | f8200f21cd724bcba70d1bc663c81ccb | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 342 | 1753666907060 | 2025-07-28 09:41:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | dc2dc542541b49319b54d974b47b545c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666887016, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7188, jvmUsedMemory=0.5826, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1638, diskUsed=203.9372, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 342 | 1753666907062 | 2025-07-28 09:41:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | dc2dc542541b49319b54d974b47b545c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666897025, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.002, jvmUsedMemory=0.594, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1671, diskUsed=203.9374, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 342 | 1753666907062 | 2025-07-28 09:41:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | dc2dc542541b49319b54d974b47b545c | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666907036, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8472, jvmUsedMemory=0.6003, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1688, diskUsed=203.9367, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 386 | 1753666907081 | 2025-07-28 09:41:47 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | 88c7c0cea414499586aa40c3f049cfe2 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 386 | 1753666907081 | 2025-07-28 09:41:47 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 88c7c0cea414499586aa40c3f049cfe2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 387 | 1753666937067 | 2025-07-28 09:42:17 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | bff977ac41ae40519e89bb30e1abfaa8 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666917040, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5566, jvmUsedMemory=0.6107, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1717, diskUsed=203.9367, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 387 | 1753666937068 | 2025-07-28 09:42:17 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | bff977ac41ae40519e89bb30e1abfaa8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666927043, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6177, jvmUsedMemory=0.6221, jvmMaxMemory=3.5557, jvmMemoryUsage=0.175, diskUsed=203.9378, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 387 | 1753666937068 | 2025-07-28 09:42:17 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | bff977ac41ae40519e89bb30e1abfaa8 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666937042, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6021, jvmUsedMemory=0.6289, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1769, diskUsed=203.941, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 387 | 1753666937084 | 2025-07-28 09:42:17 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | bff977ac41ae40519e89bb30e1abfaa8 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 387 | 1753666937084 | 2025-07-28 09:42:17 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | bff977ac41ae40519e89bb30e1abfaa8 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 429 | 1753666967068 | 2025-07-28 09:42:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 40ea3929d2544932af1a9403dba23625 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666947044, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5894, jvmUsedMemory=0.6376, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1793, diskUsed=203.943, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1753666967070 | 2025-07-28 09:42:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 40ea3929d2544932af1a9403dba23625 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666957044, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5654, jvmUsedMemory=0.6467, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1819, diskUsed=203.9428, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1753666967070 | 2025-07-28 09:42:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 40ea3929d2544932af1a9403dba23625 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666967041, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7983, jvmUsedMemory=0.6524, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1835, diskUsed=203.9431, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1753666967087 | 2025-07-28 09:42:47 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 40ea3929d2544932af1a9403dba23625 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 429 | 1753666967088 | 2025-07-28 09:42:47 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 40ea3929d2544932af1a9403dba23625 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 428 | 1753666997057 | 2025-07-28 09:43:17 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 1f925693f1db463ba209c52021e30a7b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666977042, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5278, jvmUsedMemory=0.6599, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1856, diskUsed=203.9463, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 428 | 1753666997058 | 2025-07-28 09:43:17 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 1f925693f1db463ba209c52021e30a7b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666987045, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.52, jvmUsedMemory=0.6679, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1878, diskUsed=203.9425, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 428 | 1753666997058 | 2025-07-28 09:43:17 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 1f925693f1db463ba209c52021e30a7b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753666997041, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2056, jvmUsedMemory=0.6736, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1894, diskUsed=203.9429, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 428 | 1753666997070 | 2025-07-28 09:43:17 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 1f925693f1db463ba209c52021e30a7b | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 428 | 1753666997070 | 2025-07-28 09:43:17 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 1f925693f1db463ba209c52021e30a7b | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 45 | 1753667001980 | 2025-07-28 09:43:21 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 9a8dfae204a248bfbeebced0c8658c67 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 41 | 1753667001980 | 2025-07-28 09:43:21 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | e193fb7de5384167aa8af4fe1aaf9c38 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 45 | 1753667001981 | 2025-07-28 09:43:21 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 9a8dfae204a248bfbeebced0c8658c67 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753667001982 | 2025-07-28 09:43:21 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | e193fb7de5384167aa8af4fe1aaf9c38 | - | - | - | - | 2 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 428 | 1753667002030 | 2025-07-28 09:43:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | 1f925693f1db463ba209c52021e30a7b | - | - | - | - | 4973 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 127 | 1753667035123 | 2025-07-28 09:43:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | f6e8c63973dc4df1bc74840bc1a81f68 | - | - | - | - | 104 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 127 | 1753667035123 | 2025-07-28 09:43:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | f6e8c63973dc4df1bc74840bc1a81f68 | - | - | - | - | 104 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 230 | 1753667040624 | 2025-07-28 09:44:00 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 000c6b20801f4defa660907835f30b9a | - | - | - | - | 4789 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 4636 ms]

warning | 260 | 1753667059933 | 2025-07-28 09:44:19 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 24523a4e76ec46ad9b9bb6788ca60035 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753667035679, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3564, jvmUsedMemory=0.5977, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1681, diskUsed=203.9434, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 260 | 1753667059938 | 2025-07-28 09:44:19 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 24523a4e76ec46ad9b9bb6788ca60035 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753667059905, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2251, jvmUsedMemory=0.77, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2166, diskUsed=203.9446, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 260 | 1753667059939 | 2025-07-28 09:44:19 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 24523a4e76ec46ad9b9bb6788ca60035 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1753667059916, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2251, jvmUsedMemory=0.7716, jvmMaxMemory=3.5557, jvmMemoryUsage=0.217, diskUsed=203.9446, diskTotal=460.4317, diskUsage=0.4429, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 45 | 1753667059979 | 2025-07-28 09:44:19 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 14bc3e313ea642c789df734ea2be2760 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 45 | 1753667059980 | 2025-07-28 09:44:19 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 14bc3e313ea642c789df734ea2be2760 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753667059980 | 2025-07-28 09:44:19 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | e9d8b48f467e4395948762b1bb5092fd | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 41 | 1753667059984 | 2025-07-28 09:44:19 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | e9d8b48f467e4395948762b1bb5092fd | - | - | - | - | 4 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 127 | 1753667059985 | 2025-07-28 09:44:19 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | f6e8c63973dc4df1bc74840bc1a81f68 | - | - | - | - | 24966 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1753667059985 | 2025-07-28 09:44:19 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | f6e8c63973dc4df1bc74840bc1a81f68 | - | - | - | - | 24966 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1753667059990 | 2025-07-28 09:44:19 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | f6e8c63973dc4df1bc74840bc1a81f68 | - | - | - | - | 24971 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1753667059995 | 2025-07-28 09:44:19 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ActorCell | receiveMessage | f6e8c63973dc4df1bc74840bc1a81f68 | - | - | - | - | 24976 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1753667059995 | 2025-07-28 09:44:19 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ActorCell | receiveMessage | f6e8c63973dc4df1bc74840bc1a81f68 | - | - | - | - | 24976 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1753667060088 | 2025-07-28 09:44:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ActorCell | receiveMessage | f6e8c63973dc4df1bc74840bc1a81f68 | - | - | - | - | 25069 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 125 | 1753667174060 | 2025-07-28 09:46:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 826cde41c2cc49598813a84bbc7569fd | - | - | - | - | 108 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 125 | 1753667174061 | 2025-07-28 09:46:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 826cde41c2cc49598813a84bbc7569fd | - | - | - | - | 109 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 228 | 1753667179837 | 2025-07-28 09:46:19 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | b2016d74975a45feb0a0477c6f045f05 | - | - | - | - | 4869 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 4696 ms]

warning | 41 | 1753667193872 | 2025-07-28 09:46:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 3f976e6b384549509547d0997491f58e | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 45 | 1753667193872 | 2025-07-28 09:46:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | cda127295c11465b959834aff95776fd | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 45 | 1753667193874 | 2025-07-28 09:46:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | cda127295c11465b959834aff95776fd | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753667193874 | 2025-07-28 09:46:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 3f976e6b384549509547d0997491f58e | - | - | - | - | 2 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 125 | 1753667193902 | 2025-07-28 09:46:33 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 826cde41c2cc49598813a84bbc7569fd | - | - | - | - | 19951 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 126 | 1753667867674 | 2025-07-28 09:57:47 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 43bf0e974fd14b6c9a1cb2d68aff15ae | - | - | - | - | 121 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 126 | 1753667867675 | 2025-07-28 09:57:47 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 43bf0e974fd14b6c9a1cb2d68aff15ae | - | - | - | - | 122 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 228 | 1753667885736 | 2025-07-28 09:58:05 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | d4701c7458b44768987a436e89330d29 | - | - | - | - | 17278 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 17123 ms]

warning | 45 | 1753667887508 | 2025-07-28 09:58:07 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | bdbe4800dbc741d98927b84095f7b7b7 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 41 | 1753667887508 | 2025-07-28 09:58:07 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 3a7914934ce74cc3968055f054a06ab0 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 45 | 1753667887509 | 2025-07-28 09:58:07 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | bdbe4800dbc741d98927b84095f7b7b7 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 41 | 1753667887510 | 2025-07-28 09:58:07 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 3a7914934ce74cc3968055f054a06ab0 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 126 | 1753667887539 | 2025-07-28 09:58:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 43bf0e974fd14b6c9a1cb2d68aff15ae | - | - | - | - | 19986 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

