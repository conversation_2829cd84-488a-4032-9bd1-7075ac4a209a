error | 1 | 1753414345151 | 2025-07-25 11:32:25 | v2/SpringApplication/run | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | SpringApplication | run | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 27235 | 0 | - | - | - | - | main o.s.b.d.LoggingFailureAnalysisReporter LoggingFailureAnalysisReporter.java:40 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in cn.taqu.gonghui.soa.TqTradeCenterService required a bean of type 'java.lang.String' that could not be found.


Action:

Consider defining a bean of type 'java.lang.String' in your configuration.


error | 1 | 1753414386171 | 2025-07-25 11:33:06 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | Application | main | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 24735 | 0 | - | - | - | - | main o.s.b.SpringApplication SpringApplication.java:871 Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'tqTradeCenterService' defined in file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/soa/TqTradeCenterService.class]: Initialization of bean failed; nested exception is org.springframework.core.annotation.AnnotationConfigurationException: Field annotationed by EtcdValue must be static in class: cn.taqu.gonghui.soa.TqTradeCenterService; field:host
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:610)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:782)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:774)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1340)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at cn.taqu.gonghui.Application.main(Application.java:39)
Caused by: org.springframework.core.annotation.AnnotationConfigurationException: Field annotationed by EtcdValue must be static in class: cn.taqu.gonghui.soa.TqTradeCenterService; field:host
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.lambda$initEtcdValueAnnotationedValue$1(AutowireEtcdValueProcessor.java:54)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:710)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:689)
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.initEtcdValueAnnotationedValue(AutowireEtcdValueProcessor.java:47)
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.postProcessAfterInitialization(AutowireEtcdValueProcessor.java:41)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:437)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	... 16 common frames omitted

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'tqTradeCenterService' defined in file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/soa/TqTradeCenterService.class]: Initialization of bean failed; nested exception is org.springframework.core.annotation.AnnotationConfigurationException: Field annotationed by EtcdValue must be static in class: cn.taqu.gonghui.soa.TqTradeCenterService; field:host
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:610)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:782)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:774)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1340)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at cn.taqu.gonghui.Application.main(Application.java:39)
org.springframework.core.annotation.AnnotationConfigurationException: Field annotationed by EtcdValue must be static in class: cn.taqu.gonghui.soa.TqTradeCenterService; field:host
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.lambda$initEtcdValueAnnotationedValue$1(AutowireEtcdValueProcessor.java:54)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:710)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:689)
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.initEtcdValueAnnotationedValue(AutowireEtcdValueProcessor.java:47)
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.postProcessAfterInitialization(AutowireEtcdValueProcessor.java:41)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:437)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	... 16 more
error | 1 | 1753414618326 | 2025-07-25 11:36:58 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | Application | main | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 24760 | 0 | - | - | - | - | main o.s.b.SpringApplication SpringApplication.java:871 Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'tqTradeCenterService' defined in file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/soa/TqTradeCenterService.class]: Initialization of bean failed; nested exception is org.springframework.core.annotation.AnnotationConfigurationException: Field annotationed by EtcdValue must be static in class: cn.taqu.gonghui.soa.TqTradeCenterService; field:host
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:610)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:782)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:774)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1340)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at cn.taqu.gonghui.Application.main(Application.java:40)
Caused by: org.springframework.core.annotation.AnnotationConfigurationException: Field annotationed by EtcdValue must be static in class: cn.taqu.gonghui.soa.TqTradeCenterService; field:host
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.lambda$initEtcdValueAnnotationedValue$1(AutowireEtcdValueProcessor.java:54)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:710)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:689)
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.initEtcdValueAnnotationedValue(AutowireEtcdValueProcessor.java:47)
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.postProcessAfterInitialization(AutowireEtcdValueProcessor.java:41)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:437)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	... 16 common frames omitted

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'tqTradeCenterService' defined in file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/soa/TqTradeCenterService.class]: Initialization of bean failed; nested exception is org.springframework.core.annotation.AnnotationConfigurationException: Field annotationed by EtcdValue must be static in class: cn.taqu.gonghui.soa.TqTradeCenterService; field:host
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:610)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:144)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:782)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:774)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1340)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at cn.taqu.gonghui.Application.main(Application.java:40)
org.springframework.core.annotation.AnnotationConfigurationException: Field annotationed by EtcdValue must be static in class: cn.taqu.gonghui.soa.TqTradeCenterService; field:host
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.lambda$initEtcdValueAnnotationedValue$1(AutowireEtcdValueProcessor.java:54)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:710)
	at org.springframework.util.ReflectionUtils.doWithFields(ReflectionUtils.java:689)
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.initEtcdValueAnnotationedValue(AutowireEtcdValueProcessor.java:47)
	at cn.taqu.core.etcd.processor.AutowireEtcdValueProcessor.postProcessAfterInitialization(AutowireEtcdValueProcessor.java:41)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsAfterInitialization(AbstractAutowireCapableBeanFactory.java:437)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:602)
	... 16 more
