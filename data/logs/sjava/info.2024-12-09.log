info | 1 | 1733740316348 | 2024-12-09 18:31:56 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 24960 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1733740316341 | 2024-12-09 18:31:56 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 9cf59b0eb1bb4ffe8675f064f4371a35 | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1733740316360 | 2024-12-09 18:31:56 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1733740316914 | 2024-12-09 18:31:56 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 561 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1733740316922 | 2024-12-09 18:31:56 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 569 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1733740316927 | 2024-12-09 18:31:56 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 574 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1733740316932 | 2024-12-09 18:31:56 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 580 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1733740316936 | 2024-12-09 18:31:56 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 583 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1733740317119 | 2024-12-09 18:31:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 766 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1733740317177 | 2024-12-09 18:31:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 824 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1733740317180 | 2024-12-09 18:31:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 827 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1733740317180 | 2024-12-09 18:31:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 828 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1733740317452 | 2024-12-09 18:31:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 1099 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1733740319485 | 2024-12-09 18:31:59 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 3132 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1733740319487 | 2024-12-09 18:31:59 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 3134 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1733740319524 | 2024-12-09 18:31:59 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 3171 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 32 ms. Found 0 JPA repository interfaces.

info | 1 | 1733740319534 | 2024-12-09 18:31:59 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 3181 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1733740319535 | 2024-12-09 18:31:59 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 3182 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1733740319560 | 2024-12-09 18:31:59 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 3207 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.

info | 1 | 1733740320328 | 2024-12-09 18:32:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 3975 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$cd20910a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740320345 | 2024-12-09 18:32:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 3992 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$4c26d682] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740320412 | 2024-12-09 18:32:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 4059 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$ca8fe14b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740320416 | 2024-12-09 18:32:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 4063 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740320477 | 2024-12-09 18:32:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 4124 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740320481 | 2024-12-09 18:32:00 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 4128 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740321122 | 2024-12-09 18:32:01 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 4770 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1733740321138 | 2024-12-09 18:32:01 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 4785 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1733740321138 | 2024-12-09 18:32:01 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 4785 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1733740321220 | 2024-12-09 18:32:01 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 4867 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1733740328695 | 2024-12-09 18:32:08 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 12343 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1733740328781 | 2024-12-09 18:32:08 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 12429 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1733740328830 | 2024-12-09 18:32:08 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 12478 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1733740328947 | 2024-12-09 18:32:08 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 12595 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1733740329032 | 2024-12-09 18:32:09 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 12680 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1733740329165 | 2024-12-09 18:32:09 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 12812 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1733740329172 | 2024-12-09 18:32:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 12819 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1733740333820 | 2024-12-09 18:32:13 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 17467 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1733740334285 | 2024-12-09 18:32:14 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 17933 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1733740334307 | 2024-12-09 18:32:14 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 17954 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1733740334476 | 2024-12-09 18:32:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 75574d3f00934b5bb9695f4b2e3c389c | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1733740334501 | 2024-12-09 18:32:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 75574d3f00934b5bb9695f4b2e3c389c | - | - | - | - | 24 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1733740334510 | 2024-12-09 18:32:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 75574d3f00934b5bb9695f4b2e3c389c | - | - | - | - | 33 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1733740334680 | 2024-12-09 18:32:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 75574d3f00934b5bb9695f4b2e3c389c | - | - | - | - | 203 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 168 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1733740334686 | 2024-12-09 18:32:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 75574d3f00934b5bb9695f4b2e3c389c | - | - | - | - | 209 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1733740334693 | 2024-12-09 18:32:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 75574d3f00934b5bb9695f4b2e3c389c | - | - | - | - | 216 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1733740334700 | 2024-12-09 18:32:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 75574d3f00934b5bb9695f4b2e3c389c | - | - | - | - | 223 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1733740334794 | 2024-12-09 18:32:14 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 75574d3f00934b5bb9695f4b2e3c389c | - | - | - | - | 318 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 92 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1733740337486 | 2024-12-09 18:32:17 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 21133 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1733740338185 | 2024-12-09 18:32:18 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 21832 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@2e67722b with [org.springframework.security.web.session.DisableEncodeUrlFilter@5b4a84c0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7d395621, org.springframework.security.web.context.SecurityContextPersistenceFilter@74899df1, org.springframework.security.web.header.HeaderWriterFilter@59c66ec5, org.springframework.security.web.authentication.logout.LogoutFilter@65061a8b, org.springframework.web.filter.CorsFilter@382de41b, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@5cafef6f, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@6ad46185, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6a4eb4f0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3d349564, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7fb7b0de, org.springframework.security.web.session.SessionManagementFilter@17e04cc5, org.springframework.security.web.access.ExceptionTranslationFilter@15b4f6b3, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@b35ac04]

info | 1 | 1733740338200 | 2024-12-09 18:32:18 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 21847 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1733740338273 | 2024-12-09 18:32:18 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 21920 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1733740338275 | 2024-12-09 18:32:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 21922 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1733740338276 | 2024-12-09 18:32:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 21923 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1733740338278 | 2024-12-09 18:32:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 21925 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1733740338280 | 2024-12-09 18:32:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 21927 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1733740338280 | 2024-12-09 18:32:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 21928 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1733740338281 | 2024-12-09 18:32:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 21928 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1733740340956 | 2024-12-09 18:32:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24603 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1733740340957 | 2024-12-09 18:32:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24604 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1733740340958 | 2024-12-09 18:32:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24605 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1733740340958 | 2024-12-09 18:32:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24605 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1733740340958 | 2024-12-09 18:32:20 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24605 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1733740340962 | 2024-12-09 18:32:20 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24610 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1733740341178 | 2024-12-09 18:32:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24826 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1733740341186 | 2024-12-09 18:32:21 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24836 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1733740341225 | 2024-12-09 18:32:21 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24872 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@7ca1e839, tech.powerjob.worker.actors.ProcessorTrackerActor@79009ee, tech.powerjob.worker.actors.WorkerActor@3af3581a])

info | 1 | 1733740341272 | 2024-12-09 18:32:21 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24919 | 0 | - | - | - | - | main o.r.Reflections Reflections took 33 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1733740341282 | 2024-12-09 18:32:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24929 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1733740341284 | 2024-12-09 18:32:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24932 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@32589ed7

info | 1 | 1733740341285 | 2024-12-09 18:32:21 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24932 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1733740341285 | 2024-12-09 18:32:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24932 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1733740341291 | 2024-12-09 18:32:21 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 24938 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 125 | 1733740342425 | 2024-12-09 18:32:22 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1733740343053 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26701 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1733740343054 | 2024-12-09 18:32:23 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26701 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1733740343055 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26702 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1733740343055 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26702 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1733740343056 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26703 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1733740343056 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26703 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1733740343056 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26703 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1733740343056 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26703 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1733740343056 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26703 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1733740343056 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26704 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1733740343057 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26704 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1733740343057 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26704 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1733740343057 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26704 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1733740343057 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26704 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1733740343057 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26704 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1733740343059 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26706 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1733740343064 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26711 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1733740343066 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26713 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1733740343066 | 2024-12-09 18:32:23 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26714 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.781 s

info | 1 | 1733740343174 | 2024-12-09 18:32:23 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26821 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1733740343180 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26827 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1733740343181 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26828 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1733740343184 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 26831 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1733740343670 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27317 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1733740343671 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27318 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/494dca7b21734fe1b8f729a396f95a5e/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1733740343693 | 2024-12-09 18:32:23 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27341 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/494dca7b21734fe1b8f729a396f95a5e/] on JVM exit successfully

info | 1 | 1733740343718 | 2024-12-09 18:32:23 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27366 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1733740343723 | 2024-12-09 18:32:23 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27371 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 5.451 s, congratulations!

info | 159 | 1733740343729 | 2024-12-09 18:32:23 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 159 | 1733740343731 | 2024-12-09 18:32:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1733740343857 | 2024-12-09 18:32:23 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | TomcatWebServer | start | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27505 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1733740343901 | 2024-12-09 18:32:23 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27548 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1733740343921 | 2024-12-09 18:32:23 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27568 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1733740343924 | 2024-12-09 18:32:23 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27571 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1733740343956 | 2024-12-09 18:32:23 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | Application | main | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27604 | 0 | - | - | - | - | main c.t.g.Application Started Application in 28.089 seconds (JVM running for 28.656)

info | 1 | 1733740343975 | 2024-12-09 18:32:23 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27622 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1733740343975 | 2024-12-09 18:32:23 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 3fb3215d95ce444b95d6f84e0cf7784e | - | - | - | - | 27622 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 229 | 1733740343979 | 2024-12-09 18:32:23 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 2ddff22dcc514eb9b64ee577be990d60 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 159 | 1733740353727 | 2024-12-09 18:32:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 9998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740363723 | 2024-12-09 18:32:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 19994 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740373729 | 2024-12-09 18:32:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 30000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740383723 | 2024-12-09 18:33:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 39994 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740393722 | 2024-12-09 18:33:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 49992 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740403727 | 2024-12-09 18:33:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740413724 | 2024-12-09 18:33:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 9997 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740423726 | 2024-12-09 18:33:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 20000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740433726 | 2024-12-09 18:33:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 89997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740443728 | 2024-12-09 18:34:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 100000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740453734 | 2024-12-09 18:34:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 110010 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740463726 | 2024-12-09 18:34:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 120001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740473727 | 2024-12-09 18:34:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 129998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740483724 | 2024-12-09 18:34:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 139995 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740493724 | 2024-12-09 18:34:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 149995 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 229 | 1733740501817 | 2024-12-09 18:35:01 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ChatRoomService | refreshChatCache | 2ddff22dcc514eb9b64ee577be990d60 | - | - | - | - | 157840 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 159 | 1733740503727 | 2024-12-09 18:35:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 159998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740513727 | 2024-12-09 18:35:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 169998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740523724 | 2024-12-09 18:35:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 179994 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740533724 | 2024-12-09 18:35:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740543723 | 2024-12-09 18:35:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 9999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740553728 | 2024-12-09 18:35:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 20006 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740563725 | 2024-12-09 18:36:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 30002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740573727 | 2024-12-09 18:36:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 40005 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740583727 | 2024-12-09 18:36:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 239997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740593725 | 2024-12-09 18:36:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 249997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740603730 | 2024-12-09 18:36:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 260007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740613727 | 2024-12-09 18:36:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 80004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740623724 | 2024-12-09 18:37:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 90000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740633726 | 2024-12-09 18:37:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 100003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740643727 | 2024-12-09 18:37:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 110003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740653726 | 2024-12-09 18:37:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 120003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740663726 | 2024-12-09 18:37:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 130002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740673725 | 2024-12-09 18:37:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 270000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1733740675025 | 2024-12-09 18:37:55 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | StandardWrapper | initServlet | bb9f8a3223b848e29ab26f92a3f1a150 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 167 | 1733740675026 | 2024-12-09 18:37:55 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | AuthenticatorBase | invoke | bb9f8a3223b848e29ab26f92a3f1a150 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 167 | 1733740675135 | 2024-12-09 18:37:55 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | AuthenticatorBase | invoke | bb9f8a3223b848e29ab26f92a3f1a150 | - | - | - | - | 109 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 107 ms

info | 167 | 1733740676017 | 2024-12-09 18:37:56 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | chatRoom | dailyOperationData | ae56a51839ef4d11856bd2e484eeb599 | - | - | - | - | 834 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-25","endTime":"2024-12-08","orgId":null,"teamId":2342701,"export":0,"status":null,"chatUuid":null,"liveNo":null,"nickName":null,"total":null,"type":null,"download":null}

info | 167 | 1733740676276 | 2024-12-09 18:37:56 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | chatRoom | dailyOperationData | ae56a51839ef4d11856bd2e484eeb599 | - | - | - | - | 1093 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.c.s.ChatRoomService 当前角色：{"roleId":29,"roleKey":"manager","type":1,"roleName":null},当前用户id:1000708

info | 167 | 1733740676350 | 2024-12-09 18:37:56 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | chatRoom | dailyOperationData | ae56a51839ef4d11856bd2e484eeb599 | - | - | - | - | 1168 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 167 | 1733740676826 | 2024-12-09 18:37:56 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.7 | ************** | - | 2 | chatRoom | dailyOperationData | ae56a51839ef4d11856bd2e484eeb599 | - | - | - | - | 1644 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 164 | 1733740683726 | 2024-12-09 18:38:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 280000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740693723 | 2024-12-09 18:38:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 289997 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740703726 | 2024-12-09 18:38:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 300001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740713726 | 2024-12-09 18:38:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 310001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740723725 | 2024-12-09 18:38:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 319999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740733724 | 2024-12-09 18:38:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 329997 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740743723 | 2024-12-09 18:39:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 339996 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740753728 | 2024-12-09 18:39:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 350002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740763724 | 2024-12-09 18:39:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 359997 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1733740773725 | 2024-12-09 18:39:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 86721b09958c4011b509dfcdcccd163c | - | - | - | - | 369998 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740783724 | 2024-12-09 18:39:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 250001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1733740793723 | 2024-12-09 18:39:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 259999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 171 | 1733740795620 | 2024-12-09 18:39:55 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | chatRoom | dailyOperationData | c7e0ce3e13214e209dc2d5b2c42fa41c | - | - | - | - | 163 | 0 | - | - | - | - | http-nio-8087-exec-5 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-25","endTime":"2024-12-08","orgId":null,"teamId":2342701,"export":0,"status":null,"chatUuid":null,"liveNo":null,"nickName":null,"total":null,"type":null,"download":null}

info | 171 | 1733740795863 | 2024-12-09 18:39:55 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | chatRoom | dailyOperationData | c7e0ce3e13214e209dc2d5b2c42fa41c | - | - | - | - | 405 | 0 | - | - | - | - | http-nio-8087-exec-5 c.t.g.c.s.ChatRoomService 当前角色：{"roleId":29,"roleKey":"manager","type":1,"roleName":null},当前用户id:1000708

info | 163 | 1733740804423 | 2024-12-09 18:40:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 270708 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 171 | 1733740804429 | 2024-12-09 18:40:04 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | chatRoom | dailyOperationData | c7e0ce3e13214e209dc2d5b2c42fa41c | - | - | - | - | 8977 | 0 | - | - | - | - | http-nio-8087-exec-5 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 171 | 1733740804695 | 2024-12-09 18:40:04 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.7 | ************** | - | 2 | chatRoom | dailyOperationData | c7e0ce3e13214e209dc2d5b2c42fa41c | - | - | - | - | 9237 | 0 | - | - | - | - | http-nio-8087-exec-5 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 163 | 1733740814378 | 2024-12-09 18:40:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef5a3f3c2e3645ce96c13f3ec11760c3 | - | - | - | - | 280655 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740823724 | 2024-12-09 18:40:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 479995 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1733740833727 | 2024-12-09 18:40:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 489997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 172 | 1733740839988 | 2024-12-09 18:40:39 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | chatRoom | dailyOperationData | c1c26776e34349d5830bfe5d1a0bf692 | - | - | - | - | 135 | 0 | - | - | - | - | http-nio-8087-exec-6 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-25","endTime":"2024-12-08","orgId":null,"teamId":2342701,"export":0,"status":null,"chatUuid":null,"liveNo":null,"nickName":null,"total":null,"type":null,"download":null}

info | 172 | 1733740840619 | 2024-12-09 18:40:40 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | chatRoom | dailyOperationData | c1c26776e34349d5830bfe5d1a0bf692 | - | - | - | - | 766 | 0 | - | - | - | - | http-nio-8087-exec-6 c.t.g.c.s.ChatRoomService 当前角色：{"roleId":29,"roleKey":"manager","type":1,"roleName":null},当前用户id:1000708

info | 172 | 1733740843186 | 2024-12-09 18:40:43 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | chatRoom | dailyOperationData | c1c26776e34349d5830bfe5d1a0bf692 | - | - | - | - | 3332 | 0 | - | - | - | - | http-nio-8087-exec-6 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 159 | 1733740847231 | 2024-12-09 18:40:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | ************** | - | 2 | ThreadPoolExecutor | runWorker | de2030d25c3a47f2afcfc1f81c3e500d | - | - | - | - | 503502 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 172 | 1733740847445 | 2024-12-09 18:40:47 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.7 | ************** | - | 2 | chatRoom | dailyOperationData | c1c26776e34349d5830bfe5d1a0bf692 | - | - | - | - | 7591 | 0 | - | - | - | - | http-nio-8087-exec-6 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 1 | 1733740957078 | 2024-12-09 18:42:37 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 7016c10652f3484385073e55f9309902 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 25522 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1733740957069 | 2024-12-09 18:42:37 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 49ff520d1d5b4c41ae82a82f04b4a0a1 | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1733740957089 | 2024-12-09 18:42:37 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 7016c10652f3484385073e55f9309902 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1733740957871 | 2024-12-09 18:42:37 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7016c10652f3484385073e55f9309902 | - | - | - | - | 790 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1733740957876 | 2024-12-09 18:42:37 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7016c10652f3484385073e55f9309902 | - | - | - | - | 794 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1733740957888 | 2024-12-09 18:42:37 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7016c10652f3484385073e55f9309902 | - | - | - | - | 806 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1733740957894 | 2024-12-09 18:42:37 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7016c10652f3484385073e55f9309902 | - | - | - | - | 812 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1733740957898 | 2024-12-09 18:42:37 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7016c10652f3484385073e55f9309902 | - | - | - | - | 816 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1733740958065 | 2024-12-09 18:42:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7016c10652f3484385073e55f9309902 | - | - | - | - | 983 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1733740958120 | 2024-12-09 18:42:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7016c10652f3484385073e55f9309902 | - | - | - | - | 1039 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1733740958123 | 2024-12-09 18:42:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7016c10652f3484385073e55f9309902 | - | - | - | - | 1041 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1733740958124 | 2024-12-09 18:42:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7016c10652f3484385073e55f9309902 | - | - | - | - | 1042 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1733740958386 | 2024-12-09 18:42:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7016c10652f3484385073e55f9309902 | - | - | - | - | 1304 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1733740960402 | 2024-12-09 18:42:40 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 7016c10652f3484385073e55f9309902 | - | - | - | - | 3320 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1733740960403 | 2024-12-09 18:42:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7016c10652f3484385073e55f9309902 | - | - | - | - | 3321 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1733740960442 | 2024-12-09 18:42:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7016c10652f3484385073e55f9309902 | - | - | - | - | 3360 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.

info | 1 | 1733740960452 | 2024-12-09 18:42:40 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 7016c10652f3484385073e55f9309902 | - | - | - | - | 3370 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1733740960453 | 2024-12-09 18:42:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7016c10652f3484385073e55f9309902 | - | - | - | - | 3371 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1733740960479 | 2024-12-09 18:42:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7016c10652f3484385073e55f9309902 | - | - | - | - | 3397 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.

info | 1 | 1733740961344 | 2024-12-09 18:42:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 4263 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$7d9bc639] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740961361 | 2024-12-09 18:42:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 4280 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$fca20bb1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740961420 | 2024-12-09 18:42:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 4338 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$7b0b167a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740961424 | 2024-12-09 18:42:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 4342 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740961482 | 2024-12-09 18:42:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 4400 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740961486 | 2024-12-09 18:42:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 4404 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733740962065 | 2024-12-09 18:42:42 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 7016c10652f3484385073e55f9309902 | - | - | - | - | 4984 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1733740962079 | 2024-12-09 18:42:42 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 7016c10652f3484385073e55f9309902 | - | - | - | - | 4997 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1733740962079 | 2024-12-09 18:42:42 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 7016c10652f3484385073e55f9309902 | - | - | - | - | 4997 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1733740962183 | 2024-12-09 18:42:42 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 7016c10652f3484385073e55f9309902 | - | - | - | - | 5102 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1733740969627 | 2024-12-09 18:42:49 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 7016c10652f3484385073e55f9309902 | - | - | - | - | 12546 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1733740969703 | 2024-12-09 18:42:49 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 7016c10652f3484385073e55f9309902 | - | - | - | - | 12621 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1733740969749 | 2024-12-09 18:42:49 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 7016c10652f3484385073e55f9309902 | - | - | - | - | 12668 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1733740969888 | 2024-12-09 18:42:49 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 7016c10652f3484385073e55f9309902 | - | - | - | - | 12806 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1733740969976 | 2024-12-09 18:42:49 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 7016c10652f3484385073e55f9309902 | - | - | - | - | 12894 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1733740970114 | 2024-12-09 18:42:50 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 7016c10652f3484385073e55f9309902 | - | - | - | - | 13033 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1733740970123 | 2024-12-09 18:42:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7016c10652f3484385073e55f9309902 | - | - | - | - | 13041 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1733740975099 | 2024-12-09 18:42:55 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 7016c10652f3484385073e55f9309902 | - | - | - | - | 18018 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1733740975436 | 2024-12-09 18:42:55 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 7016c10652f3484385073e55f9309902 | - | - | - | - | 18354 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1733740975463 | 2024-12-09 18:42:55 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 7016c10652f3484385073e55f9309902 | - | - | - | - | 18381 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1733740975684 | 2024-12-09 18:42:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b468589bc50a4023a93c5346a98da505 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1733740975804 | 2024-12-09 18:42:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b468589bc50a4023a93c5346a98da505 | - | - | - | - | 120 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 80 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1733740975816 | 2024-12-09 18:42:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b468589bc50a4023a93c5346a98da505 | - | - | - | - | 133 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1733740975957 | 2024-12-09 18:42:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b468589bc50a4023a93c5346a98da505 | - | - | - | - | 273 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 138 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1733740975964 | 2024-12-09 18:42:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b468589bc50a4023a93c5346a98da505 | - | - | - | - | 280 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1733740975973 | 2024-12-09 18:42:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b468589bc50a4023a93c5346a98da505 | - | - | - | - | 289 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1733740975982 | 2024-12-09 18:42:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b468589bc50a4023a93c5346a98da505 | - | - | - | - | 298 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1733740976084 | 2024-12-09 18:42:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b468589bc50a4023a93c5346a98da505 | - | - | - | - | 400 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 100 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1733740978615 | 2024-12-09 18:42:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7016c10652f3484385073e55f9309902 | - | - | - | - | 21533 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1733740979509 | 2024-12-09 18:42:59 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22428 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@411775eb with [org.springframework.security.web.session.DisableEncodeUrlFilter@3cf8b6d5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1fbb7b9b, org.springframework.security.web.context.SecurityContextPersistenceFilter@52d55407, org.springframework.security.web.header.HeaderWriterFilter@1ef346ef, org.springframework.security.web.authentication.logout.LogoutFilter@12029dd2, org.springframework.web.filter.CorsFilter@3068f882, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@7a9e618e, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@579e065f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@169a28e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4bd8491e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2ddc896a, org.springframework.security.web.session.SessionManagementFilter@49099d93, org.springframework.security.web.access.ExceptionTranslationFilter@5ad0c9bc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1c77f2fb]

info | 1 | 1733740979529 | 2024-12-09 18:42:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22447 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1733740979641 | 2024-12-09 18:42:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22559 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1733740979647 | 2024-12-09 18:42:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22565 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1733740979648 | 2024-12-09 18:42:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22566 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1733740979652 | 2024-12-09 18:42:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22570 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1733740979656 | 2024-12-09 18:42:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22574 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1733740979656 | 2024-12-09 18:42:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22574 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1733740979656 | 2024-12-09 18:42:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22574 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1733740979826 | 2024-12-09 18:42:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22744 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1733740979826 | 2024-12-09 18:42:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22744 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1733740979826 | 2024-12-09 18:42:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22744 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1733740979826 | 2024-12-09 18:42:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22744 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1733740979826 | 2024-12-09 18:42:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22744 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1733740979828 | 2024-12-09 18:42:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22746 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1733740979896 | 2024-12-09 18:42:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22814 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1733740979897 | 2024-12-09 18:42:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22815 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1733740979909 | 2024-12-09 18:42:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22827 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@724e8fba, tech.powerjob.worker.actors.ProcessorTrackerActor@3578461a, tech.powerjob.worker.actors.WorkerActor@790833c0])

info | 1 | 1733740979942 | 2024-12-09 18:42:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22860 | 0 | - | - | - | - | main o.r.Reflections Reflections took 24 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1733740979948 | 2024-12-09 18:42:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22866 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1733740979949 | 2024-12-09 18:42:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22867 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@72e9940f

info | 1 | 1733740979950 | 2024-12-09 18:42:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22868 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@5046c3f6

info | 1 | 1733740979950 | 2024-12-09 18:42:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22868 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1733740979950 | 2024-12-09 18:42:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22869 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1733740979954 | 2024-12-09 18:42:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 22872 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 126 | 1733740980597 | 2024-12-09 18:43:00 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1733740981140 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24059 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1733740981141 | 2024-12-09 18:43:01 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24059 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1733740981142 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24060 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1733740981142 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24060 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1733740981142 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24060 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1733740981142 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24060 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1733740981142 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24060 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1733740981142 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24061 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1733740981143 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24061 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1733740981143 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24061 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1733740981143 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24061 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1733740981143 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24061 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1733740981143 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24061 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1733740981143 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24061 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1733740981143 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24061 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1733740981144 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24063 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1733740981147 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24065 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1733740981148 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24066 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1733740981148 | 2024-12-09 18:43:01 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24066 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.198 s

info | 1 | 1733740981220 | 2024-12-09 18:43:01 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24139 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1733740981225 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24143 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1733740981225 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24144 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1733740981229 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24147 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1733740981453 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24371 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1733740981453 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24371 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/98368cfc7b234c5a9992d8872d473851/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1733740981460 | 2024-12-09 18:43:01 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24378 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/98368cfc7b234c5a9992d8872d473851/] on JVM exit successfully

info | 1 | 1733740981475 | 2024-12-09 18:43:01 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24393 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1733740981476 | 2024-12-09 18:43:01 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24394 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.835 s, congratulations!

info | 156 | 1733740981480 | 2024-12-09 18:43:01 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 156 | 1733740981481 | 2024-12-09 18:43:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1733740981558 | 2024-12-09 18:43:01 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24476 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1733740981616 | 2024-12-09 18:43:01 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24534 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1733740981645 | 2024-12-09 18:43:01 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24564 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1733740981646 | 2024-12-09 18:43:01 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24564 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1733740981686 | 2024-12-09 18:43:01 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24604 | 0 | - | - | - | - | main c.t.g.Application Started Application in 25.028 seconds (JVM running for 25.699)

info | 1 | 1733740981721 | 2024-12-09 18:43:01 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24639 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1733740981722 | 2024-12-09 18:43:01 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 7016c10652f3484385073e55f9309902 | - | - | - | - | 24640 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 226 | 1733740981730 | 2024-12-09 18:43:01 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | dcf65698371c4c628a06bb8fcc14a660 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 156 | 1733740991479 | 2024-12-09 18:43:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1733741001480 | 2024-12-09 18:43:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 20001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1733741011480 | 2024-12-09 18:43:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 30007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1733741021481 | 2024-12-09 18:43:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 40002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1733741031481 | 2024-12-09 18:43:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 50001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1733741041481 | 2024-12-09 18:44:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 60002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1733741043814 | 2024-12-09 18:44:03 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | StandardWrapper | initServlet | c071b21203334db7886a204f7567fb5b | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 165 | 1733741043815 | 2024-12-09 18:44:03 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | AuthenticatorBase | invoke | c071b21203334db7886a204f7567fb5b | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 165 | 1733741043830 | 2024-12-09 18:44:03 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | AuthenticatorBase | invoke | c071b21203334db7886a204f7567fb5b | - | - | - | - | 16 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 15 ms

info | 165 | 1733741044241 | 2024-12-09 18:44:04 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | chatRoom | dailyOperationData | e1e91b80a98e4c4cb23f86b76df83b8b | - | - | - | - | 392 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-25","endTime":"2024-12-08","orgId":null,"teamId":2342701,"export":0,"status":null,"chatUuid":null,"liveNo":null,"nickName":null,"total":null,"type":null,"download":null}

info | 165 | 1733741044485 | 2024-12-09 18:44:04 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | chatRoom | dailyOperationData | e1e91b80a98e4c4cb23f86b76df83b8b | - | - | - | - | 636 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.s.ChatRoomService 当前角色：{"roleId":29,"roleKey":"manager","type":1,"roleName":null},当前用户id:1000708

info | 165 | 1733741049581 | 2024-12-09 18:44:09 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | chatRoom | dailyOperationData | e1e91b80a98e4c4cb23f86b76df83b8b | - | - | - | - | 5743 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 156 | 1733741055918 | 2024-12-09 18:44:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 74447 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1733741056239 | 2024-12-09 18:44:16 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.7 | ************** | - | 2 | chatRoom | dailyOperationData | e1e91b80a98e4c4cb23f86b76df83b8b | - | - | - | - | 12391 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 160 | 1733741061479 | 2024-12-09 18:44:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2d1452af346a46db9a455d43102b189e | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1733741071480 | 2024-12-09 18:44:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2d1452af346a46db9a455d43102b189e | - | - | - | - | 10002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1733741080836 | 2024-12-09 18:44:40 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | chatRoom | dailyOperationData | dd7f0c161c314dc4928df074dec78bcf | - | - | - | - | 139 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-25","endTime":"2024-12-08","orgId":null,"teamId":2342701,"export":0,"status":null,"chatUuid":null,"liveNo":null,"nickName":null,"total":null,"type":null,"download":null}

info | 166 | 1733741081010 | 2024-12-09 18:44:41 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | chatRoom | dailyOperationData | dd7f0c161c314dc4928df074dec78bcf | - | - | - | - | 312 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.c.s.ChatRoomService 当前角色：{"roleId":29,"roleKey":"manager","type":1,"roleName":null},当前用户id:1000708

info | 166 | 1733741081074 | 2024-12-09 18:44:41 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | chatRoom | dailyOperationData | dd7f0c161c314dc4928df074dec78bcf | - | - | - | - | 376 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 160 | 1733741083586 | 2024-12-09 18:44:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2d1452af346a46db9a455d43102b189e | - | - | - | - | 22108 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1733741099386 | 2024-12-09 18:44:59 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2d1452af346a46db9a455d43102b189e | - | - | - | - | 37917 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1733741099390 | 2024-12-09 18:44:59 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.7 | ************** | - | 2 | chatRoom | dailyOperationData | dd7f0c161c314dc4928df074dec78bcf | - | - | - | - | 18698 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 156 | 1733741112403 | 2024-12-09 18:45:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 130931 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1733741112434 | 2024-12-09 18:45:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ba5c4ac9d5df4f3bbc32bb79d1352006 | - | - | - | - | 130955 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1733741112438 | 2024-12-09 18:45:12 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | a073ce388a3244b18481a2cb4fd3b9af | - | - | - | - | 0 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-12-09 18:42:55,435 to 2024-12-09 18:45:12,420
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 167 | 1733741114673 | 2024-12-09 18:45:14 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | chatRoom | dailyOperationData | 568f3aa410804cab8d05dd9afff7c7bc | - | - | - | - | 119 | 0 | - | - | - | - | http-nio-8087-exec-4 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-25","endTime":"2024-12-08","orgId":null,"teamId":2342701,"export":0,"status":null,"chatUuid":null,"liveNo":null,"nickName":null,"total":null,"type":null,"download":null}

info | 167 | 1733741114895 | 2024-12-09 18:45:14 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | chatRoom | dailyOperationData | 568f3aa410804cab8d05dd9afff7c7bc | - | - | - | - | 342 | 0 | - | - | - | - | http-nio-8087-exec-4 c.t.g.c.s.ChatRoomService 当前角色：{"roleId":29,"roleKey":"manager","type":1,"roleName":null},当前用户id:1000708

info | 167 | 1733741114953 | 2024-12-09 18:45:14 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | chatRoom | dailyOperationData | 568f3aa410804cab8d05dd9afff7c7bc | - | - | - | - | 399 | 0 | - | - | - | - | http-nio-8087-exec-4 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 167 | 1733741121501 | 2024-12-09 18:45:21 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.7 | ************** | - | 2 | chatRoom | dailyOperationData | 568f3aa410804cab8d05dd9afff7c7bc | - | - | - | - | 6947 | 0 | - | - | - | - | http-nio-8087-exec-4 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 161 | 1733741121501 | 2024-12-09 18:45:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6fd0a8dadd5f4381902f86015d34d39d | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 15 | 1733741134607 | 2024-12-09 18:45:34 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | bbd9bc21482d44f4a440fb272dd0ec49 | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1733741134615 | 2024-12-09 18:45:34 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 25661 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1733741134654 | 2024-12-09 18:45:34 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 29 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1733741135240 | 2024-12-09 18:45:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 616 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1733741135246 | 2024-12-09 18:45:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 621 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1733741135258 | 2024-12-09 18:45:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 633 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1733741135264 | 2024-12-09 18:45:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 639 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1733741135269 | 2024-12-09 18:45:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 644 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1733741135437 | 2024-12-09 18:45:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 812 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1733741135498 | 2024-12-09 18:45:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 873 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1733741135499 | 2024-12-09 18:45:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 875 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1733741135500 | 2024-12-09 18:45:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 875 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1733741135779 | 2024-12-09 18:45:35 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 1155 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1733741137694 | 2024-12-09 18:45:37 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 3069 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1733741137695 | 2024-12-09 18:45:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 3070 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1733741137736 | 2024-12-09 18:45:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 3111 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 35 ms. Found 0 JPA repository interfaces.

info | 1 | 1733741137748 | 2024-12-09 18:45:37 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 3123 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1733741137766 | 2024-12-09 18:45:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 3141 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1733741137809 | 2024-12-09 18:45:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 3184 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1733741138809 | 2024-12-09 18:45:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 4184 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$fb3ef3d3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733741138832 | 2024-12-09 18:45:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 4207 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7a45394b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733741138904 | 2024-12-09 18:45:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 4280 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$f8ae4414] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733741138909 | 2024-12-09 18:45:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 4284 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733741138977 | 2024-12-09 18:45:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 4352 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733741138980 | 2024-12-09 18:45:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 4355 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1733741139568 | 2024-12-09 18:45:39 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 4943 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1733741139578 | 2024-12-09 18:45:39 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 4953 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1733741139578 | 2024-12-09 18:45:39 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 4953 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1733741139666 | 2024-12-09 18:45:39 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 5041 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1733741147102 | 2024-12-09 18:45:47 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 12477 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1733741147176 | 2024-12-09 18:45:47 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 12551 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1733741147204 | 2024-12-09 18:45:47 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 12579 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1733741147283 | 2024-12-09 18:45:47 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 12658 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1733741147365 | 2024-12-09 18:45:47 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 12740 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1733741147491 | 2024-12-09 18:45:47 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 12866 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1733741147497 | 2024-12-09 18:45:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 12872 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1733741151928 | 2024-12-09 18:45:51 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 17304 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1733741152445 | 2024-12-09 18:45:52 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 17820 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1733741152482 | 2024-12-09 18:45:52 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 17857 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1733741152663 | 2024-12-09 18:45:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 572516119f7d4aa5863ba1c8e4817f7e | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1733741152690 | 2024-12-09 18:45:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 572516119f7d4aa5863ba1c8e4817f7e | - | - | - | - | 26 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1733741152705 | 2024-12-09 18:45:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 572516119f7d4aa5863ba1c8e4817f7e | - | - | - | - | 41 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1733741152850 | 2024-12-09 18:45:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 572516119f7d4aa5863ba1c8e4817f7e | - | - | - | - | 186 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 139 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1733741152856 | 2024-12-09 18:45:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 572516119f7d4aa5863ba1c8e4817f7e | - | - | - | - | 192 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1733741152864 | 2024-12-09 18:45:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 572516119f7d4aa5863ba1c8e4817f7e | - | - | - | - | 200 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1733741152872 | 2024-12-09 18:45:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 572516119f7d4aa5863ba1c8e4817f7e | - | - | - | - | 208 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1733741153071 | 2024-12-09 18:45:53 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 572516119f7d4aa5863ba1c8e4817f7e | - | - | - | - | 407 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 196 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1733741155730 | 2024-12-09 18:45:55 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 21106 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1733741156816 | 2024-12-09 18:45:56 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22191 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@35b53a35 with [org.springframework.security.web.session.DisableEncodeUrlFilter@6ad4a475, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1e4fd633, org.springframework.security.web.context.SecurityContextPersistenceFilter@4696e669, org.springframework.security.web.header.HeaderWriterFilter@5d493952, org.springframework.security.web.authentication.logout.LogoutFilter@10b6fbab, org.springframework.web.filter.CorsFilter@fbd0581, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@7e1e6f28, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@5c455d42, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4c6d366c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@120d4e85, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@40bdb06d, org.springframework.security.web.session.SessionManagementFilter@10a1c090, org.springframework.security.web.access.ExceptionTranslationFilter@62458e7e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@fef700c]

info | 1 | 1733741156831 | 2024-12-09 18:45:56 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22206 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1733741156914 | 2024-12-09 18:45:56 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22289 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1733741156916 | 2024-12-09 18:45:56 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22291 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1733741156917 | 2024-12-09 18:45:56 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22292 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1733741156919 | 2024-12-09 18:45:56 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22294 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1733741156921 | 2024-12-09 18:45:56 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22296 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1733741156922 | 2024-12-09 18:45:56 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22297 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1733741156922 | 2024-12-09 18:45:56 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22297 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1733741157165 | 2024-12-09 18:45:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22540 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1733741157165 | 2024-12-09 18:45:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22540 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1733741157165 | 2024-12-09 18:45:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22541 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1733741157166 | 2024-12-09 18:45:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22541 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1733741157166 | 2024-12-09 18:45:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22541 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1733741157167 | 2024-12-09 18:45:57 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22542 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1733741157229 | 2024-12-09 18:45:57 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22604 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1733741157230 | 2024-12-09 18:45:57 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22606 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1733741157238 | 2024-12-09 18:45:57 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22613 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@51cc3cc2, tech.powerjob.worker.actors.ProcessorTrackerActor@2e4d2aaf, tech.powerjob.worker.actors.WorkerActor@42c309e9])

info | 1 | 1733741157271 | 2024-12-09 18:45:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22646 | 0 | - | - | - | - | main o.r.Reflections Reflections took 24 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1733741157276 | 2024-12-09 18:45:57 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22651 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1733741157277 | 2024-12-09 18:45:57 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22652 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@67d7f791

info | 1 | 1733741157278 | 2024-12-09 18:45:57 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22653 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@3abbffd9

info | 1 | 1733741157278 | 2024-12-09 18:45:57 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22653 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1733741157278 | 2024-12-09 18:45:57 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22653 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1733741157283 | 2024-12-09 18:45:57 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 22658 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 124 | 1733741157718 | 2024-12-09 18:45:57 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1733741158127 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23502 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1733741158128 | 2024-12-09 18:45:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23503 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1733741158128 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23503 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1733741158128 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1733741158129 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1733741158131 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23506 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1733741158133 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23508 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1733741158133 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23508 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1733741158133 | 2024-12-09 18:45:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23509 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 855.8 ms

info | 1 | 1733741158201 | 2024-12-09 18:45:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23576 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1733741158205 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23580 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1733741158205 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23580 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1733741158208 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23583 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1733741158452 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23827 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1733741158456 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23831 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/32ba1a30446241319d978da57513eddb/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1733741158464 | 2024-12-09 18:45:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23839 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/32ba1a30446241319d978da57513eddb/] on JVM exit successfully

info | 1 | 1733741158482 | 2024-12-09 18:45:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23857 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1733741158483 | 2024-12-09 18:45:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23858 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.569 s, congratulations!

info | 153 | 1733741158487 | 2024-12-09 18:45:58 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 153 | 1733741158488 | 2024-12-09 18:45:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1733741158550 | 2024-12-09 18:45:58 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23925 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1733741158579 | 2024-12-09 18:45:58 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23954 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1733741158600 | 2024-12-09 18:45:58 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23975 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1733741158600 | 2024-12-09 18:45:58 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23975 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1733741158622 | 2024-12-09 18:45:58 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 23997 | 0 | - | - | - | - | main c.t.g.Application Started Application in 24.648 seconds (JVM running for 25.198)

info | 1 | 1733741158636 | 2024-12-09 18:45:58 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 24011 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1733741158636 | 2024-12-09 18:45:58 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | e8ad438c9f134b32bae7046719890c88 | - | - | - | - | 24011 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 223 | 1733741158639 | 2024-12-09 18:45:58 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 6f34e2a9f24d4a0898cc10939e8663d5 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 153 | 1733741168488 | 2024-12-09 18:46:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1733741174425 | 2024-12-09 18:46:14 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | StandardWrapper | initServlet | f9ad86074ed94052b8485065915790bf | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 161 | 1733741174426 | 2024-12-09 18:46:14 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | AuthenticatorBase | invoke | f9ad86074ed94052b8485065915790bf | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 161 | 1733741174435 | 2024-12-09 18:46:14 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | AuthenticatorBase | invoke | f9ad86074ed94052b8485065915790bf | - | - | - | - | 10 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 9 ms

info | 161 | 1733741174825 | 2024-12-09 18:46:14 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | chatRoom | dailyOperationData | d8b2c28332c345f5b60856a745745d6b | - | - | - | - | 369 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-25","endTime":"2024-12-08","orgId":null,"teamId":2342701,"export":0,"status":null,"chatUuid":null,"liveNo":null,"nickName":null,"total":null,"type":null,"download":null}

info | 161 | 1733741175008 | 2024-12-09 18:46:15 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | chatRoom | dailyOperationData | d8b2c28332c345f5b60856a745745d6b | - | - | - | - | 552 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.c.s.ChatRoomService 当前角色：{"roleId":29,"roleKey":"manager","type":1,"roleName":null},当前用户id:1000708

info | 161 | 1733741175076 | 2024-12-09 18:46:15 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | chatRoom | dailyOperationData | d8b2c28332c345f5b60856a745745d6b | - | - | - | - | 620 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 161 | 1733741187244 | 2024-12-09 18:46:27 | v2/chatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.7 | ************** | - | 2 | chatRoom | dailyOperationData | d8b2c28332c345f5b60856a745745d6b | - | - | - | - | 12788 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-25,结束时间：2024-12-08,团队集合:[2342701],hostUuids:null,chatStatus:null

info | 153 | 1733741187251 | 2024-12-09 18:46:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 28765 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1733741189151 | 2024-12-09 18:46:29 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a992432fa1014b02bc4454e67cfba260 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1733741198488 | 2024-12-09 18:46:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 0a7bbfc68af149e6a4960a7a18777141 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1733741208486 | 2024-12-09 18:46:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 0a7bbfc68af149e6a4960a7a18777141 | - | - | - | - | 10006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1733741218487 | 2024-12-09 18:46:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 0a7bbfc68af149e6a4960a7a18777141 | - | - | - | - | 19999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 223 | 1733741227287 | 2024-12-09 18:47:07 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ChatRoomService | refreshChatCache | 6f34e2a9f24d4a0898cc10939e8663d5 | - | - | - | - | 68651 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 158 | 1733741228489 | 2024-12-09 18:47:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 0a7bbfc68af149e6a4960a7a18777141 | - | - | - | - | 30001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1733741238488 | 2024-12-09 18:47:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 0a7bbfc68af149e6a4960a7a18777141 | - | - | - | - | 40000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741248491 | 2024-12-09 18:47:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 90009 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741258485 | 2024-12-09 18:47:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 99998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741268489 | 2024-12-09 18:47:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 110012 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741278485 | 2024-12-09 18:47:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 119999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741288486 | 2024-12-09 18:48:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 130000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741298486 | 2024-12-09 18:48:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 139999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741308484 | 2024-12-09 18:48:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 150003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741318488 | 2024-12-09 18:48:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 160002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741328486 | 2024-12-09 18:48:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 170000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741338486 | 2024-12-09 18:48:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 180000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741348487 | 2024-12-09 18:49:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 190002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741358483 | 2024-12-09 18:49:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 199997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741368484 | 2024-12-09 18:49:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 209997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741378484 | 2024-12-09 18:49:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 219998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741388485 | 2024-12-09 18:49:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 229999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1733741398501 | 2024-12-09 18:49:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 0a7bbfc68af149e6a4960a7a18777141 | - | - | - | - | 200013 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1733741408506 | 2024-12-09 18:50:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 0a7bbfc68af149e6a4960a7a18777141 | - | - | - | - | 210017 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741418510 | 2024-12-09 18:50:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 260024 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741428512 | 2024-12-09 18:50:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 270026 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 153 | 1733741438515 | 2024-12-09 18:50:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a4764ef8af344aefbccb4797c119a498 | - | - | - | - | 280029 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

