info | 16 | 1754018864180 | 2025-08-01 11:27:44 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 83b05216d56c46a68c60f90a6d4d61ad | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1754018864188 | 2025-08-01 11:27:44 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 71196 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1754018864202 | 2025-08-01 11:27:44 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1754018864874 | 2025-08-01 11:27:44 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 679 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1754018864883 | 2025-08-01 11:27:44 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 687 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1754018864893 | 2025-08-01 11:27:44 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 697 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1754018864905 | 2025-08-01 11:27:44 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 709 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1754018864916 | 2025-08-01 11:27:44 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 726 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1754018865023 | 2025-08-01 11:27:45 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 827 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1754018865068 | 2025-08-01 11:27:45 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 872 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1754018865071 | 2025-08-01 11:27:45 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 875 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1754018865071 | 2025-08-01 11:27:45 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 875 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1754018865164 | 2025-08-01 11:27:45 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 968 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1754018867847 | 2025-08-01 11:27:47 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 3651 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1754018867849 | 2025-08-01 11:27:47 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 3653 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1754018867910 | 2025-08-01 11:27:47 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 3714 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 55 ms. Found 0 JPA repository interfaces.

info | 1 | 1754018867922 | 2025-08-01 11:27:47 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 3726 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1754018867923 | 2025-08-01 11:27:47 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 3727 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1754018867968 | 2025-08-01 11:27:47 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 3772 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.

info | 1 | 1754018869393 | 2025-08-01 11:27:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 5197 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$65525617] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1754018869421 | 2025-08-01 11:27:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 5226 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e4589b8f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1754018869534 | 2025-08-01 11:27:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 5340 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$62c1a658] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1754018869552 | 2025-08-01 11:27:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 5356 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1754018869698 | 2025-08-01 11:27:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 5502 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1754018869706 | 2025-08-01 11:27:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 5511 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1754018870546 | 2025-08-01 11:27:50 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 6350 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1754018870558 | 2025-08-01 11:27:50 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 6363 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1754018870559 | 2025-08-01 11:27:50 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 6363 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1754018870677 | 2025-08-01 11:27:50 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 6481 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1754018886695 | 2025-08-01 11:28:06 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 22499 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1754018886780 | 2025-08-01 11:28:06 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 22584 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1754018886823 | 2025-08-01 11:28:06 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 22627 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1754018886968 | 2025-08-01 11:28:06 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 22772 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1754018887080 | 2025-08-01 11:28:07 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 22884 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1754018887234 | 2025-08-01 11:28:07 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 23038 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1754018887243 | 2025-08-01 11:28:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 66702a8c67b24c1cbae97ad890765ca9 | - | - | - | - | 23047 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

