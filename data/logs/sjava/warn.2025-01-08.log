warning | 123 | 1736324794974 | 2025-01-08 16:26:34 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 984bed9cf36d4c71a98774beeb2c03b3 | - | - | - | - | 191 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 123 | 1736324794975 | 2025-01-08 16:26:34 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 984bed9cf36d4c71a98774beeb2c03b3 | - | - | - | - | 192 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 259 | 1736324816386 | 2025-01-08 16:26:56 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 59451970f31745938738681d542070a3 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324796172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.9409, jvmUsedMemory=0.4266, jvmMaxMemory=3.5557, jvmMemoryUsage=0.12, diskUsed=229.0938, diskTotal=460.4317, diskUsage=0.4976, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 259 | 1736324816395 | 2025-01-08 16:26:56 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 59451970f31745938738681d542070a3 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324806171, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.3345, jvmUsedMemory=0.5458, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1535, diskUsed=229.094, diskTotal=460.4317, diskUsage=0.4976, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 259 | 1736324816396 | 2025-01-08 16:26:56 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 59451970f31745938738681d542070a3 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324816173, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.166, jvmUsedMemory=0.554, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1558, diskUsed=230.0952, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 123 | 1736324816454 | 2025-01-08 16:26:56 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 984bed9cf36d4c71a98774beeb2c03b3 | - | - | - | - | 21671 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1736324816455 | 2025-01-08 16:26:56 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 984bed9cf36d4c71a98774beeb2c03b3 | - | - | - | - | 21672 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1736324816457 | 2025-01-08 16:26:56 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 984bed9cf36d4c71a98774beeb2c03b3 | - | - | - | - | 21675 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1736324816460 | 2025-01-08 16:26:56 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 984bed9cf36d4c71a98774beeb2c03b3 | - | - | - | - | 21677 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1736324816460 | 2025-01-08 16:26:56 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 984bed9cf36d4c71a98774beeb2c03b3 | - | - | - | - | 21677 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 259 | 1736324846196 | 2025-01-08 16:27:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 59451970f31745938738681d542070a3 | - | - | - | - | 29802 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324826169, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.605, jvmUsedMemory=0.5734, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1613, diskUsed=230.0942, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 259 | 1736324846197 | 2025-01-08 16:27:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 59451970f31745938738681d542070a3 | - | - | - | - | 29803 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324836169, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.9893, jvmUsedMemory=0.6337, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1782, diskUsed=230.0957, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 259 | 1736324846200 | 2025-01-08 16:27:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 59451970f31745938738681d542070a3 | - | - | - | - | 29806 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324846173, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.2954, jvmUsedMemory=0.6398, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1799, diskUsed=230.0953, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 259 | 1736324846226 | 2025-01-08 16:27:26 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 59451970f31745938738681d542070a3 | - | - | - | - | 29832 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 259 | 1736324846226 | 2025-01-08 16:27:26 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 59451970f31745938738681d542070a3 | - | - | - | - | 29832 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 302 | 1736324876204 | 2025-01-08 16:27:56 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c1e94dfa4948466f9d035ef7e5c26f34 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324856172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.6348, jvmUsedMemory=0.6576, jvmMaxMemory=3.5557, jvmMemoryUsage=0.185, diskUsed=230.0957, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 302 | 1736324876207 | 2025-01-08 16:27:56 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c1e94dfa4948466f9d035ef7e5c26f34 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324866169, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1426, jvmUsedMemory=0.6638, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1867, diskUsed=230.0971, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 302 | 1736324876208 | 2025-01-08 16:27:56 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c1e94dfa4948466f9d035ef7e5c26f34 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324876170, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6655, jvmUsedMemory=0.6693, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1882, diskUsed=230.0971, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 302 | 1736324876227 | 2025-01-08 16:27:56 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c1e94dfa4948466f9d035ef7e5c26f34 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 302 | 1736324876227 | 2025-01-08 16:27:56 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c1e94dfa4948466f9d035ef7e5c26f34 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 347 | 1736324906753 | 2025-01-08 16:28:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 5188f6f0e058499db5e31799aa914b1e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324886173, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1816, jvmUsedMemory=0.6804, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1914, diskUsed=230.0979, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 347 | 1736324906756 | 2025-01-08 16:28:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 5188f6f0e058499db5e31799aa914b1e | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324896172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9258, jvmUsedMemory=0.6855, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1928, diskUsed=230.098, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 348 | 1736324906774 | 2025-01-08 16:28:26 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 4f7fdad7392f4a1b85409c2328c6e9bc | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 348 | 1736324906774 | 2025-01-08 16:28:26 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 4f7fdad7392f4a1b85409c2328c6e9bc | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 430 | 1736324937375 | 2025-01-08 16:28:57 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6adfa3315bbf4ea895fcdb034f75aaa5 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324917347, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4229, jvmUsedMemory=0.7235, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2035, diskUsed=230.0513, diskTotal=460.4317, diskUsage=0.4996, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 430 | 1736324937378 | 2025-01-08 16:28:57 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6adfa3315bbf4ea895fcdb034f75aaa5 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324926173, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.271, jvmUsedMemory=0.7329, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2061, diskUsed=230.0527, diskTotal=460.4317, diskUsage=0.4996, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 430 | 1736324937379 | 2025-01-08 16:28:57 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6adfa3315bbf4ea895fcdb034f75aaa5 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324936173, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5171, jvmUsedMemory=0.7552, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2124, diskUsed=230.0548, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 430 | 1736324937398 | 2025-01-08 16:28:57 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 6adfa3315bbf4ea895fcdb034f75aaa5 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 430 | 1736324937399 | 2025-01-08 16:28:57 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 6adfa3315bbf4ea895fcdb034f75aaa5 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 429 | 1736324966205 | 2025-01-08 16:29:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 63b86561b88040edac9678d44e72c461 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324946172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5044, jvmUsedMemory=0.765, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2151, diskUsed=230.0538, diskTotal=460.4317, diskUsage=0.4996, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1736324966208 | 2025-01-08 16:29:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 63b86561b88040edac9678d44e72c461 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324956170, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5, jvmUsedMemory=0.7696, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2164, diskUsed=230.0548, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1736324966208 | 2025-01-08 16:29:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 63b86561b88040edac9678d44e72c461 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324966173, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6567, jvmUsedMemory=0.7752, jvmMaxMemory=3.5557, jvmMemoryUsage=0.218, diskUsed=230.0548, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1736324966232 | 2025-01-08 16:29:26 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 63b86561b88040edac9678d44e72c461 | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 429 | 1736324966233 | 2025-01-08 16:29:26 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 63b86561b88040edac9678d44e72c461 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 465 | 1736324996195 | 2025-01-08 16:29:56 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | d3e69cbc20e74dfa91e671e19300f164 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324976174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6963, jvmUsedMemory=0.784, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2205, diskUsed=230.0549, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 465 | 1736324996196 | 2025-01-08 16:29:56 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | d3e69cbc20e74dfa91e671e19300f164 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324986170, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4414, jvmUsedMemory=0.7895, jvmMaxMemory=3.5557, jvmMemoryUsage=0.222, diskUsed=230.0549, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 465 | 1736324996197 | 2025-01-08 16:29:56 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | d3e69cbc20e74dfa91e671e19300f164 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736324996173, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2256, jvmUsedMemory=0.795, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2236, diskUsed=230.0549, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 465 | 1736324996216 | 2025-01-08 16:29:56 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | d3e69cbc20e74dfa91e671e19300f164 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 465 | 1736324996219 | 2025-01-08 16:29:56 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | d3e69cbc20e74dfa91e671e19300f164 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 502 | 1736325026194 | 2025-01-08 16:30:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8bb449672c614c8c9a4914148d01bc77 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325006174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.104, jvmUsedMemory=0.8052, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2265, diskUsed=230.0559, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 502 | 1736325026197 | 2025-01-08 16:30:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8bb449672c614c8c9a4914148d01bc77 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325016172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9336, jvmUsedMemory=0.8103, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2279, diskUsed=230.0569, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 502 | 1736325026198 | 2025-01-08 16:30:26 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8bb449672c614c8c9a4914148d01bc77 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325026174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9497, jvmUsedMemory=0.8159, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2295, diskUsed=230.0588, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 502 | 1736325026213 | 2025-01-08 16:30:26 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8bb449672c614c8c9a4914148d01bc77 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 502 | 1736325026214 | 2025-01-08 16:30:26 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8bb449672c614c8c9a4914148d01bc77 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1736325035269 | 2025-01-08 16:30:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 9de2d588faab4649a5775bf0374dd6f8 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1736325035270 | 2025-01-08 16:30:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | ab40ed041b004f67a6dbeca381038cbb | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736325035270 | 2025-01-08 16:30:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 9de2d588faab4649a5775bf0374dd6f8 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736325035272 | 2025-01-08 16:30:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | ab40ed041b004f67a6dbeca381038cbb | - | - | - | - | 4 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 502 | 1736325035316 | 2025-01-08 16:30:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 8bb449672c614c8c9a4914148d01bc77 | - | - | - | - | 9121 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 124 | 1736325087639 | 2025-01-08 16:31:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | ad3b7cd9ad844d899b3608251304d13c | - | - | - | - | 114 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 124 | 1736325087639 | 2025-01-08 16:31:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | ad3b7cd9ad844d899b3608251304d13c | - | - | - | - | 114 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 124 | 1736325108288 | 2025-01-08 16:31:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | ad3b7cd9ad844d899b3608251304d13c | - | - | - | - | 20772 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325088162, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1855, jvmUsedMemory=0.4753, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1337, diskUsed=230.0551, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 124 | 1736325108297 | 2025-01-08 16:31:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | ad3b7cd9ad844d899b3608251304d13c | - | - | - | - | 20772 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325098164, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9966, jvmUsedMemory=0.6237, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1754, diskUsed=230.0515, diskTotal=460.4317, diskUsage=0.4996, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 124 | 1736325108297 | 2025-01-08 16:31:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | ad3b7cd9ad844d899b3608251304d13c | - | - | - | - | 20773 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325108166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7627, jvmUsedMemory=0.6855, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1928, diskUsed=230.0527, diskTotal=460.4317, diskUsage=0.4996, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 132 | 1736325108361 | 2025-01-08 16:31:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 656f0d28cbf14ec8b2d0a8038263add1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 132 | 1736325108362 | 2025-01-08 16:31:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 656f0d28cbf14ec8b2d0a8038263add1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 132 | 1736325108365 | 2025-01-08 16:31:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 656f0d28cbf14ec8b2d0a8038263add1 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 132 | 1736325108368 | 2025-01-08 16:31:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 656f0d28cbf14ec8b2d0a8038263add1 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 132 | 1736325108369 | 2025-01-08 16:31:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 656f0d28cbf14ec8b2d0a8038263add1 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 272 | 1736325138188 | 2025-01-08 16:32:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6626f60ddc894ad4990105b1e48888ca | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325118165, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5776, jvmUsedMemory=0.7042, jvmMaxMemory=3.5557, jvmMemoryUsage=0.198, diskUsed=230.053, diskTotal=460.4317, diskUsage=0.4996, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 272 | 1736325138189 | 2025-01-08 16:32:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6626f60ddc894ad4990105b1e48888ca | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325128163, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5493, jvmUsedMemory=0.713, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2005, diskUsed=230.053, diskTotal=460.4317, diskUsage=0.4996, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 272 | 1736325138189 | 2025-01-08 16:32:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6626f60ddc894ad4990105b1e48888ca | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325138166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2305, jvmUsedMemory=0.7176, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2018, diskUsed=230.0573, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 272 | 1736325138200 | 2025-01-08 16:32:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 6626f60ddc894ad4990105b1e48888ca | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 272 | 1736325138201 | 2025-01-08 16:32:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 6626f60ddc894ad4990105b1e48888ca | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 312 | 1736325168198 | 2025-01-08 16:32:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c55e12af0adb45e98e2712dee943effc | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325148166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3418, jvmUsedMemory=0.727, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2045, diskUsed=230.0573, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 312 | 1736325168200 | 2025-01-08 16:32:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c55e12af0adb45e98e2712dee943effc | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325158166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6631, jvmUsedMemory=0.7375, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2074, diskUsed=230.0573, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 312 | 1736325168200 | 2025-01-08 16:32:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c55e12af0adb45e98e2712dee943effc | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325168164, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5737, jvmUsedMemory=0.7434, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2091, diskUsed=230.0576, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 312 | 1736325168218 | 2025-01-08 16:32:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c55e12af0adb45e98e2712dee943effc | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 312 | 1736325168218 | 2025-01-08 16:32:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c55e12af0adb45e98e2712dee943effc | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 396 | 1736325198186 | 2025-01-08 16:33:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3a08da575db643ce9516aecadffc077e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325178164, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6387, jvmUsedMemory=0.7514, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2113, diskUsed=230.0576, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 396 | 1736325198187 | 2025-01-08 16:33:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3a08da575db643ce9516aecadffc077e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325188167, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6138, jvmUsedMemory=0.7577, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2131, diskUsed=230.0567, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 396 | 1736325198188 | 2025-01-08 16:33:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3a08da575db643ce9516aecadffc077e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325198166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6602, jvmUsedMemory=0.7633, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2147, diskUsed=230.0576, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 359 | 1736325198209 | 2025-01-08 16:33:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 9dacd541cfe7492292b38aa1e0b07264 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 359 | 1736325198209 | 2025-01-08 16:33:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 9dacd541cfe7492292b38aa1e0b07264 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 397 | 1736325228189 | 2025-01-08 16:33:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c3b1a95fbd144df3a932ca492405493d | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325208164, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2505, jvmUsedMemory=0.7722, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2172, diskUsed=230.0586, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 397 | 1736325228191 | 2025-01-08 16:33:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c3b1a95fbd144df3a932ca492405493d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325218167, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9839, jvmUsedMemory=0.7794, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2192, diskUsed=230.0586, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 397 | 1736325228192 | 2025-01-08 16:33:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c3b1a95fbd144df3a932ca492405493d | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325228166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1523, jvmUsedMemory=0.7844, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2206, diskUsed=230.0594, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 397 | 1736325228210 | 2025-01-08 16:33:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c3b1a95fbd144df3a932ca492405493d | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 397 | 1736325228210 | 2025-01-08 16:33:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c3b1a95fbd144df3a932ca492405493d | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 437 | 1736325258198 | 2025-01-08 16:34:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 353bd4cb28f24142baa4960818b78644 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325238166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.042, jvmUsedMemory=0.7932, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2231, diskUsed=230.0597, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 437 | 1736325258200 | 2025-01-08 16:34:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 353bd4cb28f24142baa4960818b78644 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325248166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2627, jvmUsedMemory=0.8001, jvmMaxMemory=3.5557, jvmMemoryUsage=0.225, diskUsed=230.0599, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 437 | 1736325258201 | 2025-01-08 16:34:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 353bd4cb28f24142baa4960818b78644 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325258167, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9141, jvmUsedMemory=0.8047, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2263, diskUsed=230.0599, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 437 | 1736325258221 | 2025-01-08 16:34:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 353bd4cb28f24142baa4960818b78644 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 437 | 1736325258221 | 2025-01-08 16:34:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 353bd4cb28f24142baa4960818b78644 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 476 | 1736325288197 | 2025-01-08 16:34:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ac76b49f23ab4925b2a81e669ea2229a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325268166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0869, jvmUsedMemory=0.8132, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2287, diskUsed=230.058, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 476 | 1736325288198 | 2025-01-08 16:34:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ac76b49f23ab4925b2a81e669ea2229a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325278162, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.166, jvmUsedMemory=0.8203, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2307, diskUsed=230.0584, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 476 | 1736325288198 | 2025-01-08 16:34:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ac76b49f23ab4925b2a81e669ea2229a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325288163, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9063, jvmUsedMemory=0.8254, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2321, diskUsed=230.0584, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 476 | 1736325288206 | 2025-01-08 16:34:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | ac76b49f23ab4925b2a81e669ea2229a | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 476 | 1736325288207 | 2025-01-08 16:34:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | ac76b49f23ab4925b2a81e669ea2229a | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 477 | 1736325318200 | 2025-01-08 16:35:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 4bb3f73a7bbc4b5ea00bc42e16b35f63 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325298167, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2759, jvmUsedMemory=0.8331, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2343, diskUsed=230.0584, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 477 | 1736325318203 | 2025-01-08 16:35:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 4bb3f73a7bbc4b5ea00bc42e16b35f63 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325308164, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.3179, jvmUsedMemory=0.8394, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2361, diskUsed=230.0585, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 477 | 1736325318203 | 2025-01-08 16:35:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 4bb3f73a7bbc4b5ea00bc42e16b35f63 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325318166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6533, jvmUsedMemory=0.8436, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2373, diskUsed=230.0611, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 477 | 1736325318224 | 2025-01-08 16:35:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 4bb3f73a7bbc4b5ea00bc42e16b35f63 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 477 | 1736325318224 | 2025-01-08 16:35:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 4bb3f73a7bbc4b5ea00bc42e16b35f63 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 555 | 1736325348189 | 2025-01-08 16:35:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 2ae64adf3e0643edbb1b0737f2882fb6 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325328166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3247, jvmUsedMemory=0.8504, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2392, diskUsed=230.0621, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 555 | 1736325348192 | 2025-01-08 16:35:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 2ae64adf3e0643edbb1b0737f2882fb6 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325338165, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.2822, jvmUsedMemory=0.8567, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2409, diskUsed=230.0627, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 555 | 1736325348192 | 2025-01-08 16:35:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 2ae64adf3e0643edbb1b0737f2882fb6 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325348167, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.7036, jvmUsedMemory=0.8634, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2428, diskUsed=230.0627, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 555 | 1736325348205 | 2025-01-08 16:35:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2ae64adf3e0643edbb1b0737f2882fb6 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 555 | 1736325348206 | 2025-01-08 16:35:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2ae64adf3e0643edbb1b0737f2882fb6 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 594 | 1736325378189 | 2025-01-08 16:36:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7ff287be985b4be8b03e7e465eed5f10 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325358163, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2075, jvmUsedMemory=0.8716, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2451, diskUsed=230.0627, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 594 | 1736325378191 | 2025-01-08 16:36:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7ff287be985b4be8b03e7e465eed5f10 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325368165, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1143, jvmUsedMemory=0.8789, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2472, diskUsed=230.0618, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 594 | 1736325378192 | 2025-01-08 16:36:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7ff287be985b4be8b03e7e465eed5f10 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325378167, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1958, jvmUsedMemory=0.8844, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2487, diskUsed=230.0614, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 594 | 1736325378210 | 2025-01-08 16:36:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 7ff287be985b4be8b03e7e465eed5f10 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 594 | 1736325378211 | 2025-01-08 16:36:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 7ff287be985b4be8b03e7e465eed5f10 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 595 | 1736325408189 | 2025-01-08 16:36:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 4f7c14f43c0e47f2a148b89cc8551921 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325388167, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.146, jvmUsedMemory=0.8974, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2524, diskUsed=230.0635, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 595 | 1736325408191 | 2025-01-08 16:36:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 4f7c14f43c0e47f2a148b89cc8551921 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325398167, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1104, jvmUsedMemory=0.9013, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2535, diskUsed=230.0634, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 595 | 1736325408192 | 2025-01-08 16:36:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 4f7c14f43c0e47f2a148b89cc8551921 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325408167, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.7134, jvmUsedMemory=0.9063, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2549, diskUsed=230.0645, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 595 | 1736325408209 | 2025-01-08 16:36:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 4f7c14f43c0e47f2a148b89cc8551921 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 595 | 1736325408209 | 2025-01-08 16:36:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 4f7c14f43c0e47f2a148b89cc8551921 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 632 | 1736325438163 | 2025-01-08 16:37:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 352652aa7ca443758e067813907a4001 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325418156, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=8.3516, jvmUsedMemory=0.9097, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2558, diskUsed=230.0649, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=4)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 632 | 1736325438165 | 2025-01-08 16:37:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 352652aa7ca443758e067813907a4001 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325428138, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.1475, jvmUsedMemory=0.913, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2568, diskUsed=230.068, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 632 | 1736325438166 | 2025-01-08 16:37:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 352652aa7ca443758e067813907a4001 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325438130, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.4355, jvmUsedMemory=0.916, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2576, diskUsed=230.0735, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 632 | 1736325438180 | 2025-01-08 16:37:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 352652aa7ca443758e067813907a4001 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 632 | 1736325438180 | 2025-01-08 16:37:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 352652aa7ca443758e067813907a4001 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 692 | 1736325468146 | 2025-01-08 16:37:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 17492e5be57147d1bec9d236c7d2d815 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325448125, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.5259, jvmUsedMemory=0.9242, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2599, diskUsed=230.075, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 692 | 1736325468148 | 2025-01-08 16:37:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 17492e5be57147d1bec9d236c7d2d815 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325458128, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.3838, jvmUsedMemory=0.9296, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2614, diskUsed=230.0755, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 692 | 1736325468149 | 2025-01-08 16:37:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 17492e5be57147d1bec9d236c7d2d815 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325468126, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.6294, jvmUsedMemory=0.1551, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0436, diskUsed=230.0757, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 692 | 1736325468166 | 2025-01-08 16:37:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 17492e5be57147d1bec9d236c7d2d815 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 692 | 1736325468166 | 2025-01-08 16:37:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 17492e5be57147d1bec9d236c7d2d815 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1736325484145 | 2025-01-08 16:38:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 6580434574cd4a938c6ee001c2846320 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736325484145 | 2025-01-08 16:38:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | ba0e87c718cd4e999cd1d336c627601a | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1736325484147 | 2025-01-08 16:38:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | ba0e87c718cd4e999cd1d336c627601a | - | - | - | - | 2 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736325484148 | 2025-01-08 16:38:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 6580434574cd4a938c6ee001c2846320 | - | - | - | - | 4 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 730 | 1736325484184 | 2025-01-08 16:38:04 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | b8ecd8559129465db19e09d43aa7d726 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 731 | 1736325484198 | 2025-01-08 16:38:04 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3f9751ac2dca4cea89f1f291b99ab2e5 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736325478124, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0771, jvmUsedMemory=0.1638, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0461, diskUsed=230.0766, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 731 | 1736325484208 | 2025-01-08 16:38:04 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 3f9751ac2dca4cea89f1f291b99ab2e5 | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 125 | 1736326179521 | 2025-01-08 16:49:39 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 2d810c8cdfda4ba9acf92010d74bbad4 | - | - | - | - | 112 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 125 | 1736326179521 | 2025-01-08 16:49:39 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 2d810c8cdfda4ba9acf92010d74bbad4 | - | - | - | - | 112 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 256 | 1736326200197 | 2025-01-08 16:50:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | eebecf4187e546739de0d07c2e61f8c6 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326180069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4082, jvmUsedMemory=0.3643, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1024, diskUsed=229.062, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 256 | 1736326200212 | 2025-01-08 16:50:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | eebecf4187e546739de0d07c2e61f8c6 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326190070, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5181, jvmUsedMemory=0.5184, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1458, diskUsed=229.0621, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 256 | 1736326200212 | 2025-01-08 16:50:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | eebecf4187e546739de0d07c2e61f8c6 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326200071, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0508, jvmUsedMemory=0.529, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1488, diskUsed=229.063, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 125 | 1736326200255 | 2025-01-08 16:50:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2d810c8cdfda4ba9acf92010d74bbad4 | - | - | - | - | 20846 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 125 | 1736326200255 | 2025-01-08 16:50:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2d810c8cdfda4ba9acf92010d74bbad4 | - | - | - | - | 20846 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 125 | 1736326200258 | 2025-01-08 16:50:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 2d810c8cdfda4ba9acf92010d74bbad4 | - | - | - | - | 20849 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 125 | 1736326200259 | 2025-01-08 16:50:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 2d810c8cdfda4ba9acf92010d74bbad4 | - | - | - | - | 20850 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 125 | 1736326200260 | 2025-01-08 16:50:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 2d810c8cdfda4ba9acf92010d74bbad4 | - | - | - | - | 20851 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 304 | 1736326230099 | 2025-01-08 16:50:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 86202c14b0044072912016f85413fab9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326210070, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7349, jvmUsedMemory=0.5487, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1543, diskUsed=229.0631, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 304 | 1736326230100 | 2025-01-08 16:50:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 86202c14b0044072912016f85413fab9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326220071, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0024, jvmUsedMemory=0.6382, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1795, diskUsed=229.0693, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 304 | 1736326230100 | 2025-01-08 16:50:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 86202c14b0044072912016f85413fab9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326230068, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6206, jvmUsedMemory=0.6597, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1855, diskUsed=229.0706, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 304 | 1736326230112 | 2025-01-08 16:50:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 86202c14b0044072912016f85413fab9 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 304 | 1736326230112 | 2025-01-08 16:50:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 86202c14b0044072912016f85413fab9 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 256 | 1736326260089 | 2025-01-08 16:51:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | eebecf4187e546739de0d07c2e61f8c6 | - | - | - | - | 59881 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326240069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2173, jvmUsedMemory=0.6778, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1906, diskUsed=229.0713, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 256 | 1736326260090 | 2025-01-08 16:51:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | eebecf4187e546739de0d07c2e61f8c6 | - | - | - | - | 59881 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326250071, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4844, jvmUsedMemory=0.6849, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1926, diskUsed=229.0714, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 256 | 1736326260091 | 2025-01-08 16:51:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | eebecf4187e546739de0d07c2e61f8c6 | - | - | - | - | 59882 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326260068, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3359, jvmUsedMemory=0.6944, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1953, diskUsed=229.0714, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 256 | 1736326260113 | 2025-01-08 16:51:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | eebecf4187e546739de0d07c2e61f8c6 | - | - | - | - | 59904 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 256 | 1736326260113 | 2025-01-08 16:51:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | eebecf4187e546739de0d07c2e61f8c6 | - | - | - | - | 59904 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 385 | 1736326290090 | 2025-01-08 16:51:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 38e5bfd4169b4567baf66f1ad925927c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326270068, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3638, jvmUsedMemory=0.707, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1988, diskUsed=229.0708, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 385 | 1736326290091 | 2025-01-08 16:51:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 38e5bfd4169b4567baf66f1ad925927c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326280067, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2944, jvmUsedMemory=0.7146, jvmMaxMemory=3.5557, jvmMemoryUsage=0.201, diskUsed=229.0717, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 385 | 1736326290092 | 2025-01-08 16:51:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 38e5bfd4169b4567baf66f1ad925927c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326290069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0146, jvmUsedMemory=0.7222, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2031, diskUsed=229.0719, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 348 | 1736326290108 | 2025-01-08 16:51:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 1c9d30d1a1cc4ebdba088b41f9355021 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 348 | 1736326290109 | 2025-01-08 16:51:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 1c9d30d1a1cc4ebdba088b41f9355021 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 385 | 1736326320098 | 2025-01-08 16:52:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 38e5bfd4169b4567baf66f1ad925927c | - | - | - | - | 30009 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326300071, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8647, jvmUsedMemory=0.7303, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2054, diskUsed=229.0721, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 385 | 1736326320100 | 2025-01-08 16:52:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 38e5bfd4169b4567baf66f1ad925927c | - | - | - | - | 30010 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326310069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7314, jvmUsedMemory=0.7395, jvmMaxMemory=3.5557, jvmMemoryUsage=0.208, diskUsed=229.0722, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 385 | 1736326320100 | 2025-01-08 16:52:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 38e5bfd4169b4567baf66f1ad925927c | - | - | - | - | 30011 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326320071, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5386, jvmUsedMemory=0.758, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2132, diskUsed=229.0744, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 385 | 1736326320118 | 2025-01-08 16:52:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 38e5bfd4169b4567baf66f1ad925927c | - | - | - | - | 30028 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 385 | 1736326320118 | 2025-01-08 16:52:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 38e5bfd4169b4567baf66f1ad925927c | - | - | - | - | 30028 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 422 | 1736326350098 | 2025-01-08 16:52:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | b0e17bca22f94851b016d649f45fc0f0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326330069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.769, jvmUsedMemory=0.7676, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2159, diskUsed=229.0747, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 422 | 1736326350099 | 2025-01-08 16:52:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | b0e17bca22f94851b016d649f45fc0f0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326340069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1177, jvmUsedMemory=0.7742, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2177, diskUsed=229.0747, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 422 | 1736326350100 | 2025-01-08 16:52:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | b0e17bca22f94851b016d649f45fc0f0 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326350071, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.939, jvmUsedMemory=0.7808, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2196, diskUsed=229.0747, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 422 | 1736326350111 | 2025-01-08 16:52:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | b0e17bca22f94851b016d649f45fc0f0 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 422 | 1736326350115 | 2025-01-08 16:52:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | b0e17bca22f94851b016d649f45fc0f0 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 461 | 1736326380101 | 2025-01-08 16:53:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c5b2572f64bb4073a1fede4f8c06a000 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326360071, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0952, jvmUsedMemory=0.7939, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2233, diskUsed=229.0762, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 461 | 1736326380103 | 2025-01-08 16:53:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c5b2572f64bb4073a1fede4f8c06a000 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326370071, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0674, jvmUsedMemory=0.8011, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2253, diskUsed=229.0763, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 461 | 1736326380104 | 2025-01-08 16:53:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c5b2572f64bb4073a1fede4f8c06a000 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326380071, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9697, jvmUsedMemory=0.8071, jvmMaxMemory=3.5557, jvmMemoryUsage=0.227, diskUsed=229.0763, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 461 | 1736326380126 | 2025-01-08 16:53:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c5b2572f64bb4073a1fede4f8c06a000 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 461 | 1736326380127 | 2025-01-08 16:53:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c5b2572f64bb4073a1fede4f8c06a000 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 499 | 1736326410099 | 2025-01-08 16:53:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 67776a4210b14d7daf3c3391af26209c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326390067, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9063, jvmUsedMemory=0.8148, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2292, diskUsed=229.0763, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 499 | 1736326410101 | 2025-01-08 16:53:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 67776a4210b14d7daf3c3391af26209c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326400069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6865, jvmUsedMemory=0.8209, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2309, diskUsed=229.0773, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 499 | 1736326410102 | 2025-01-08 16:53:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 67776a4210b14d7daf3c3391af26209c | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326410068, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5068, jvmUsedMemory=0.8279, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2328, diskUsed=229.0676, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 499 | 1736326410126 | 2025-01-08 16:53:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 67776a4210b14d7daf3c3391af26209c | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 499 | 1736326410127 | 2025-01-08 16:53:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 67776a4210b14d7daf3c3391af26209c | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 534 | 1736326440100 | 2025-01-08 16:54:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0984b31144a4447985a19f285e1d80e3 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326420069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5757, jvmUsedMemory=0.8355, jvmMaxMemory=3.5557, jvmMemoryUsage=0.235, diskUsed=229.0677, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 534 | 1736326440102 | 2025-01-08 16:54:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0984b31144a4447985a19f285e1d80e3 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326430069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7207, jvmUsedMemory=0.8436, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2373, diskUsed=229.0641, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 534 | 1736326440102 | 2025-01-08 16:54:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0984b31144a4447985a19f285e1d80e3 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326440070, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.603, jvmUsedMemory=0.8512, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2394, diskUsed=229.0641, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 534 | 1736326440117 | 2025-01-08 16:54:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0984b31144a4447985a19f285e1d80e3 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 534 | 1736326440117 | 2025-01-08 16:54:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0984b31144a4447985a19f285e1d80e3 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 575 | 1736326470107 | 2025-01-08 16:54:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6f6cfb88409d435a8e7aa74c224a71f8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326450071, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4302, jvmUsedMemory=0.8584, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2414, diskUsed=229.0612, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 575 | 1736326470109 | 2025-01-08 16:54:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6f6cfb88409d435a8e7aa74c224a71f8 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326460070, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4375, jvmUsedMemory=0.8645, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2431, diskUsed=229.0614, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 575 | 1736326470110 | 2025-01-08 16:54:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6f6cfb88409d435a8e7aa74c224a71f8 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326470068, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3765, jvmUsedMemory=0.8701, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2447, diskUsed=229.0614, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 575 | 1736326470127 | 2025-01-08 16:54:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 6f6cfb88409d435a8e7aa74c224a71f8 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 575 | 1736326470127 | 2025-01-08 16:54:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 6f6cfb88409d435a8e7aa74c224a71f8 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 609 | 1736326500089 | 2025-01-08 16:55:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 5f4218253aab4819a0d22738eb4ae39c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326480070, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.2446, jvmUsedMemory=0.8837, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2485, diskUsed=229.0616, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 609 | 1736326500090 | 2025-01-08 16:55:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 5f4218253aab4819a0d22738eb4ae39c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326490067, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0615, jvmUsedMemory=0.8934, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2513, diskUsed=229.0621, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 609 | 1736326500091 | 2025-01-08 16:55:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 5f4218253aab4819a0d22738eb4ae39c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326500066, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3462, jvmUsedMemory=0.9093, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2557, diskUsed=229.0647, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 609 | 1736326500103 | 2025-01-08 16:55:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 5f4218253aab4819a0d22738eb4ae39c | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 609 | 1736326500104 | 2025-01-08 16:55:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 5f4218253aab4819a0d22738eb4ae39c | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 608 | 1736326530098 | 2025-01-08 16:55:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f9888264cf0d4f0c9a8df4e9835a7123 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326510070, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0654, jvmUsedMemory=0.92, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2587, diskUsed=229.0652, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 608 | 1736326530100 | 2025-01-08 16:55:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f9888264cf0d4f0c9a8df4e9835a7123 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326520066, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0679, jvmUsedMemory=0.9261, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2604, diskUsed=229.0663, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 608 | 1736326530100 | 2025-01-08 16:55:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f9888264cf0d4f0c9a8df4e9835a7123 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326530067, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7495, jvmUsedMemory=0.9321, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2622, diskUsed=229.0673, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 608 | 1736326530117 | 2025-01-08 16:55:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f9888264cf0d4f0c9a8df4e9835a7123 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 608 | 1736326530117 | 2025-01-08 16:55:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f9888264cf0d4f0c9a8df4e9835a7123 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 684 | 1736326560088 | 2025-01-08 16:56:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | bce1b723ef9245c6ac2e75328d588cad | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326540069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1016, jvmUsedMemory=0.9398, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2643, diskUsed=229.0673, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 684 | 1736326560091 | 2025-01-08 16:56:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | bce1b723ef9245c6ac2e75328d588cad | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326550069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9316, jvmUsedMemory=0.9458, jvmMaxMemory=3.5557, jvmMemoryUsage=0.266, diskUsed=229.0673, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 684 | 1736326560091 | 2025-01-08 16:56:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | bce1b723ef9245c6ac2e75328d588cad | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326560069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7144, jvmUsedMemory=0.9523, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2678, diskUsed=229.0674, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 684 | 1736326560103 | 2025-01-08 16:56:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | bce1b723ef9245c6ac2e75328d588cad | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 684 | 1736326560104 | 2025-01-08 16:56:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | bce1b723ef9245c6ac2e75328d588cad | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 718 | 1736326590097 | 2025-01-08 16:56:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6bfbb1aa14a64607a6bde184db390324 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326570070, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2446, jvmUsedMemory=0.9634, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2709, diskUsed=229.0664, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 718 | 1736326590098 | 2025-01-08 16:56:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6bfbb1aa14a64607a6bde184db390324 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326580069, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2129, jvmUsedMemory=0.9744, jvmMaxMemory=3.5557, jvmMemoryUsage=0.274, diskUsed=229.0668, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 718 | 1736326590098 | 2025-01-08 16:56:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6bfbb1aa14a64607a6bde184db390324 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326590070, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0195, jvmUsedMemory=0.9893, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2782, diskUsed=229.0688, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 718 | 1736326590104 | 2025-01-08 16:56:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 6bfbb1aa14a64607a6bde184db390324 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 718 | 1736326590105 | 2025-01-08 16:56:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 6bfbb1aa14a64607a6bde184db390324 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 792 | 1736326620110 | 2025-01-08 16:57:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 44f769fc27b94801818d70eeb1a4a867 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326600070, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0161, jvmUsedMemory=0.9974, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2805, diskUsed=229.07, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 792 | 1736326620111 | 2025-01-08 16:57:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 44f769fc27b94801818d70eeb1a4a867 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326610067, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9268, jvmUsedMemory=1.003, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2821, diskUsed=229.0701, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 792 | 1736326620111 | 2025-01-08 16:57:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 44f769fc27b94801818d70eeb1a4a867 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326620085, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3384, jvmUsedMemory=1.0105, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2842, diskUsed=229.0701, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 792 | 1736326620118 | 2025-01-08 16:57:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 44f769fc27b94801818d70eeb1a4a867 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 792 | 1736326620118 | 2025-01-08 16:57:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 44f769fc27b94801818d70eeb1a4a867 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 791 | 1736326650127 | 2025-01-08 16:57:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 63e0b4d2b7514582a1e951be3c482050 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326630090, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2729, jvmUsedMemory=1.0187, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2865, diskUsed=229.0691, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 791 | 1736326650128 | 2025-01-08 16:57:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 63e0b4d2b7514582a1e951be3c482050 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326640095, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3105, jvmUsedMemory=1.0247, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2882, diskUsed=229.07, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 791 | 1736326650128 | 2025-01-08 16:57:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 63e0b4d2b7514582a1e951be3c482050 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326650095, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1826, jvmUsedMemory=0.1558, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0438, diskUsed=229.0713, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 791 | 1736326650136 | 2025-01-08 16:57:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 63e0b4d2b7514582a1e951be3c482050 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 791 | 1736326650136 | 2025-01-08 16:57:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 63e0b4d2b7514582a1e951be3c482050 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 719 | 1736326680119 | 2025-01-08 16:58:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a02d6e61e7c843379e1cca52c3818c70 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326660098, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0869, jvmUsedMemory=0.1626, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0457, diskUsed=229.0713, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 719 | 1736326680120 | 2025-01-08 16:58:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a02d6e61e7c843379e1cca52c3818c70 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326670097, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3066, jvmUsedMemory=0.1682, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0473, diskUsed=229.0713, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 719 | 1736326680120 | 2025-01-08 16:58:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a02d6e61e7c843379e1cca52c3818c70 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326680097, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9517, jvmUsedMemory=0.1719, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0483, diskUsed=229.0723, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 719 | 1736326680138 | 2025-01-08 16:58:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a02d6e61e7c843379e1cca52c3818c70 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 719 | 1736326680139 | 2025-01-08 16:58:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a02d6e61e7c843379e1cca52c3818c70 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 894 | 1736326710130 | 2025-01-08 16:58:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 982ff84b9825466bbc6f883b76c70370 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326690103, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.124, jvmUsedMemory=0.1767, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0497, diskUsed=229.0723, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 894 | 1736326710131 | 2025-01-08 16:58:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 982ff84b9825466bbc6f883b76c70370 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326700101, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0244, jvmUsedMemory=0.1803, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0507, diskUsed=229.0724, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 894 | 1736326710132 | 2025-01-08 16:58:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 982ff84b9825466bbc6f883b76c70370 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326710101, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7988, jvmUsedMemory=0.184, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0518, diskUsed=229.0724, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 894 | 1736326710143 | 2025-01-08 16:58:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 982ff84b9825466bbc6f883b76c70370 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 894 | 1736326710143 | 2025-01-08 16:58:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 982ff84b9825466bbc6f883b76c70370 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 861 | 1736326740123 | 2025-01-08 16:59:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 07fd0ee86b8543c7b77f7b9917ff0201 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326720103, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9219, jvmUsedMemory=0.1886, jvmMaxMemory=3.5557, jvmMemoryUsage=0.053, diskUsed=229.0725, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 861 | 1736326740127 | 2025-01-08 16:59:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 07fd0ee86b8543c7b77f7b9917ff0201 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326730100, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9331, jvmUsedMemory=0.1917, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0539, diskUsed=229.0748, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 861 | 1736326740127 | 2025-01-08 16:59:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 07fd0ee86b8543c7b77f7b9917ff0201 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326740100, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5503, jvmUsedMemory=0.1956, jvmMaxMemory=3.5557, jvmMemoryUsage=0.055, diskUsed=229.0756, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 861 | 1736326740158 | 2025-01-08 16:59:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 07fd0ee86b8543c7b77f7b9917ff0201 | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 861 | 1736326740158 | 2025-01-08 16:59:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 07fd0ee86b8543c7b77f7b9917ff0201 | - | - | - | - | 34 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 929 | 1736326770131 | 2025-01-08 16:59:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 934ee29173374c2abc1579612edc021a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-82 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326750101, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0039, jvmUsedMemory=0.2006, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0564, diskUsed=229.0747, diskTotal=460.4317, diskUsage=0.4975, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 929 | 1736326770134 | 2025-01-08 16:59:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 934ee29173374c2abc1579612edc021a | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-82 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326760099, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9038, jvmUsedMemory=0.2036, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0572, diskUsed=230.0757, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 929 | 1736326770135 | 2025-01-08 16:59:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 934ee29173374c2abc1579612edc021a | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-82 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326770098, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4565, jvmUsedMemory=0.2078, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0584, diskUsed=230.0758, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 929 | 1736326770159 | 2025-01-08 16:59:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 934ee29173374c2abc1579612edc021a | - | - | - | - | 28 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-82 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 929 | 1736326770159 | 2025-01-08 16:59:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 934ee29173374c2abc1579612edc021a | - | - | - | - | 29 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-82 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 962 | 1736326800120 | 2025-01-08 17:00:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 379dad2ad2d84fc2aa5fbdc5cd08d99c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326780100, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7192, jvmUsedMemory=0.2156, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0606, diskUsed=230.0758, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 962 | 1736326800122 | 2025-01-08 17:00:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 379dad2ad2d84fc2aa5fbdc5cd08d99c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326790101, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3008, jvmUsedMemory=0.217, jvmMaxMemory=3.5557, jvmMemoryUsage=0.061, diskUsed=230.0759, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 962 | 1736326800122 | 2025-01-08 17:00:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 379dad2ad2d84fc2aa5fbdc5cd08d99c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326800098, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0137, jvmUsedMemory=0.2231, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0627, diskUsed=230.0759, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 962 | 1736326800137 | 2025-01-08 17:00:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 379dad2ad2d84fc2aa5fbdc5cd08d99c | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 962 | 1736326800137 | 2025-01-08 17:00:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 379dad2ad2d84fc2aa5fbdc5cd08d99c | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 963 | 1736326830120 | 2025-01-08 17:00:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 04bc2e8126664f0a9bf0eadc2dd67a67 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326810102, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6235, jvmUsedMemory=0.2283, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0642, diskUsed=230.076, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 963 | 1736326830122 | 2025-01-08 17:00:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 04bc2e8126664f0a9bf0eadc2dd67a67 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326820099, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6006, jvmUsedMemory=0.2358, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0663, diskUsed=230.0772, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 963 | 1736326830126 | 2025-01-08 17:00:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 04bc2e8126664f0a9bf0eadc2dd67a67 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326830098, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3823, jvmUsedMemory=0.2405, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0676, diskUsed=230.0771, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 963 | 1736326830144 | 2025-01-08 17:00:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 04bc2e8126664f0a9bf0eadc2dd67a67 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 963 | 1736326830145 | 2025-01-08 17:00:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 04bc2e8126664f0a9bf0eadc2dd67a67 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1070 | 1736326860131 | 2025-01-08 17:01:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | d55331d96cd74ed4bfac5caa3b5737c5 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326840102, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9355, jvmUsedMemory=0.2455, jvmMaxMemory=3.5557, jvmMemoryUsage=0.069, diskUsed=230.0771, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1070 | 1736326860132 | 2025-01-08 17:01:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | d55331d96cd74ed4bfac5caa3b5737c5 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326850100, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0249, jvmUsedMemory=0.2484, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0699, diskUsed=230.0782, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1070 | 1736326860133 | 2025-01-08 17:01:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | d55331d96cd74ed4bfac5caa3b5737c5 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326860099, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5591, jvmUsedMemory=0.2527, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0711, diskUsed=230.0782, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1070 | 1736326860148 | 2025-01-08 17:01:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | d55331d96cd74ed4bfac5caa3b5737c5 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1070 | 1736326860148 | 2025-01-08 17:01:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | d55331d96cd74ed4bfac5caa3b5737c5 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 999 | 1736326890121 | 2025-01-08 17:01:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ee021014984441cb9fe56a924916702e | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326870102, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3989, jvmUsedMemory=0.2576, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0725, diskUsed=230.0734, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 999 | 1736326890123 | 2025-01-08 17:01:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ee021014984441cb9fe56a924916702e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326880103, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.103, jvmUsedMemory=0.2685, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0755, diskUsed=230.0764, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 999 | 1736326890123 | 2025-01-08 17:01:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ee021014984441cb9fe56a924916702e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326890102, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7793, jvmUsedMemory=0.2729, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0767, diskUsed=230.0777, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 999 | 1736326890141 | 2025-01-08 17:01:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | ee021014984441cb9fe56a924916702e | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 999 | 1736326890141 | 2025-01-08 17:01:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | ee021014984441cb9fe56a924916702e | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1104 | 1736326920131 | 2025-01-08 17:02:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | cccfcd110ead4274aabc258cf7488b11 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326900102, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5854, jvmUsedMemory=0.2775, jvmMaxMemory=3.5557, jvmMemoryUsage=0.078, diskUsed=230.0777, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1104 | 1736326920132 | 2025-01-08 17:02:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | cccfcd110ead4274aabc258cf7488b11 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326910101, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.417, jvmUsedMemory=0.281, jvmMaxMemory=3.5557, jvmMemoryUsage=0.079, diskUsed=230.0777, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1104 | 1736326920132 | 2025-01-08 17:02:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | cccfcd110ead4274aabc258cf7488b11 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326920099, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2661, jvmUsedMemory=0.2853, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0802, diskUsed=230.0777, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1104 | 1736326920142 | 2025-01-08 17:02:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | cccfcd110ead4274aabc258cf7488b11 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1104 | 1736326920142 | 2025-01-08 17:02:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | cccfcd110ead4274aabc258cf7488b11 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1736326938818 | 2025-01-08 17:02:18 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 50cd9e6c69844ca58ef39ca2fb9919b5 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1736326938817 | 2025-01-08 17:02:18 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 02b4b0fb252e471d8ff66798038cc045 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736326938819 | 2025-01-08 17:02:18 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 50cd9e6c69844ca58ef39ca2fb9919b5 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736326938831 | 2025-01-08 17:02:18 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 02b4b0fb252e471d8ff66798038cc045 | - | - | - | - | 13 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 1104 | 1736326938891 | 2025-01-08 17:02:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | cccfcd110ead4274aabc258cf7488b11 | - | - | - | - | 18760 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 1104 | 1736326938917 | 2025-01-08 17:02:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | Actor | aroundReceive | cccfcd110ead4274aabc258cf7488b11 | - | - | - | - | 18791 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326930105, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0645, jvmUsedMemory=0.2899, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0815, diskUsed=230.0767, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1139 | 1736326938956 | 2025-01-08 17:02:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 5a8b00d091bd4374a58f98863bec09db | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 122 | 1736326974005 | 2025-01-08 17:02:54 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 87ac702a7f674c0fa47d57e4b72c1095 | - | - | - | - | 90 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 122 | 1736326974005 | 2025-01-08 17:02:54 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 87ac702a7f674c0fa47d57e4b72c1095 | - | - | - | - | 91 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 266 | 1736326994619 | 2025-01-08 17:03:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f72c298b490e43519c3fcc0304f2be0f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326974511, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.9888, jvmUsedMemory=0.4122, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1159, diskUsed=230.0697, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 266 | 1736326994622 | 2025-01-08 17:03:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f72c298b490e43519c3fcc0304f2be0f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326984510, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.6025, jvmUsedMemory=0.6238, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1754, diskUsed=230.0791, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 266 | 1736326994623 | 2025-01-08 17:03:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f72c298b490e43519c3fcc0304f2be0f | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736326994511, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.8945, jvmUsedMemory=0.6521, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1834, diskUsed=230.0811, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 122 | 1736326994680 | 2025-01-08 17:03:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 87ac702a7f674c0fa47d57e4b72c1095 | - | - | - | - | 20766 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 122 | 1736326994682 | 2025-01-08 17:03:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 87ac702a7f674c0fa47d57e4b72c1095 | - | - | - | - | 20767 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 122 | 1736326994684 | 2025-01-08 17:03:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 87ac702a7f674c0fa47d57e4b72c1095 | - | - | - | - | 20769 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 122 | 1736326994697 | 2025-01-08 17:03:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 87ac702a7f674c0fa47d57e4b72c1095 | - | - | - | - | 20782 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 122 | 1736326994697 | 2025-01-08 17:03:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 87ac702a7f674c0fa47d57e4b72c1095 | - | - | - | - | 20782 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 311 | 1736327024538 | 2025-01-08 17:03:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7e8adcbf2dfe43d782cf29515a1f4591 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327004514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2954, jvmUsedMemory=0.6807, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1914, diskUsed=230.0842, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 311 | 1736327024540 | 2025-01-08 17:03:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7e8adcbf2dfe43d782cf29515a1f4591 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327014515, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6348, jvmUsedMemory=0.6897, jvmMaxMemory=3.5557, jvmMemoryUsage=0.194, diskUsed=230.0815, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 311 | 1736327024540 | 2025-01-08 17:03:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7e8adcbf2dfe43d782cf29515a1f4591 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327024510, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1489, jvmUsedMemory=0.694, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1952, diskUsed=230.0816, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 311 | 1736327024562 | 2025-01-08 17:03:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 7e8adcbf2dfe43d782cf29515a1f4591 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 311 | 1736327024562 | 2025-01-08 17:03:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 7e8adcbf2dfe43d782cf29515a1f4591 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 357 | 1736327054538 | 2025-01-08 17:04:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3f242cb159c1490db27d91b58864b532 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327034514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8242, jvmUsedMemory=0.7046, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1982, diskUsed=230.0814, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 357 | 1736327054539 | 2025-01-08 17:04:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3f242cb159c1490db27d91b58864b532 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327044514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6968, jvmUsedMemory=0.7136, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2007, diskUsed=230.0821, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 357 | 1736327054539 | 2025-01-08 17:04:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3f242cb159c1490db27d91b58864b532 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327054513, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5024, jvmUsedMemory=0.7192, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2023, diskUsed=230.0821, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 357 | 1736327054555 | 2025-01-08 17:04:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3f242cb159c1490db27d91b58864b532 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 357 | 1736327054558 | 2025-01-08 17:04:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3f242cb159c1490db27d91b58864b532 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 393 | 1736327084536 | 2025-01-08 17:04:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 9dcef891b16a486d9c37b8ec7fdcee4c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327064514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1909, jvmUsedMemory=0.7269, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2044, diskUsed=230.083, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1736327084539 | 2025-01-08 17:04:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9dcef891b16a486d9c37b8ec7fdcee4c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327074510, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0874, jvmUsedMemory=0.7338, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2064, diskUsed=230.0832, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1736327084540 | 2025-01-08 17:04:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 9dcef891b16a486d9c37b8ec7fdcee4c | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327084513, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2466, jvmUsedMemory=0.7385, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2077, diskUsed=230.0834, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 312 | 1736327084562 | 2025-01-08 17:04:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 40f08eb2ce334c6c85c18e274fc4a262 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 312 | 1736327084562 | 2025-01-08 17:04:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 40f08eb2ce334c6c85c18e274fc4a262 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1736327099356 | 2025-01-08 17:04:59 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | d52bde07b50a49bdbd43de5cdf6c51bf | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1736327099356 | 2025-01-08 17:04:59 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 805d8d9ba55c4605a046d1d730fb73e6 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736327099357 | 2025-01-08 17:04:59 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | d52bde07b50a49bdbd43de5cdf6c51bf | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736327099360 | 2025-01-08 17:04:59 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 805d8d9ba55c4605a046d1d730fb73e6 | - | - | - | - | 4 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 393 | 1736327099385 | 2025-01-08 17:04:59 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 9dcef891b16a486d9c37b8ec7fdcee4c | - | - | - | - | 14849 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 393 | 1736327099399 | 2025-01-08 17:04:59 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 9dcef891b16a486d9c37b8ec7fdcee4c | - | - | - | - | 14862 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327094516, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9741, jvmUsedMemory=0.7487, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2106, diskUsed=230.0833, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 419 | 1736327099415 | 2025-01-08 17:04:59 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 79df2552a30840ab8d23565f0f5b35a8 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1736327127665 | 2025-01-08 17:05:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | dce7060810ab47febff65e67bd7a17ce | - | - | - | - | 124 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 124 | 1736327127665 | 2025-01-08 17:05:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | dce7060810ab47febff65e67bd7a17ce | - | - | - | - | 124 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 267 | 1736327148572 | 2025-01-08 17:05:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 2a8c2d741d0e4f5d912bd480699b1563 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327128412, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3032, jvmUsedMemory=0.4041, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1136, diskUsed=230.0837, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 267 | 1736327148579 | 2025-01-08 17:05:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 2a8c2d741d0e4f5d912bd480699b1563 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327138415, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3301, jvmUsedMemory=0.6336, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1782, diskUsed=230.0857, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 267 | 1736327148579 | 2025-01-08 17:05:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 2a8c2d741d0e4f5d912bd480699b1563 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327148416, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3652, jvmUsedMemory=0.6519, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1833, diskUsed=230.0889, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 124 | 1736327148633 | 2025-01-08 17:05:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | dce7060810ab47febff65e67bd7a17ce | - | - | - | - | 21093 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1736327148634 | 2025-01-08 17:05:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | dce7060810ab47febff65e67bd7a17ce | - | - | - | - | 21093 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1736327148645 | 2025-01-08 17:05:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | dce7060810ab47febff65e67bd7a17ce | - | - | - | - | 21104 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1736327148648 | 2025-01-08 17:05:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | dce7060810ab47febff65e67bd7a17ce | - | - | - | - | 21107 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1736327148648 | 2025-01-08 17:05:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | dce7060810ab47febff65e67bd7a17ce | - | - | - | - | 21107 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1736327151567 | 2025-01-08 17:05:51 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 7310311185454a36aa6445bcf986d349 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1736327151567 | 2025-01-08 17:05:51 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 4c9185dabf0d4bb98ec7353bb65acade | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736327151568 | 2025-01-08 17:05:51 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 7310311185454a36aa6445bcf986d349 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736327151571 | 2025-01-08 17:05:51 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 4c9185dabf0d4bb98ec7353bb65acade | - | - | - | - | 4 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 124 | 1736327151601 | 2025-01-08 17:05:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ActorCell | receiveMessage | dce7060810ab47febff65e67bd7a17ce | - | - | - | - | 24061 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 127 | 1736327177207 | 2025-01-08 17:06:17 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | f913c4fc0e334c46a26d27adb96ef049 | - | - | - | - | 102 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 127 | 1736327177207 | 2025-01-08 17:06:17 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | f913c4fc0e334c46a26d27adb96ef049 | - | - | - | - | 102 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 269 | 1736327198033 | 2025-01-08 17:06:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 9b7e09ebb5ef4d19bc24f8cd5282f4ff | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327177903, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3242, jvmUsedMemory=1.2542, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3527, diskUsed=230.0876, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1736327198039 | 2025-01-08 17:06:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9b7e09ebb5ef4d19bc24f8cd5282f4ff | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327187906, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9663, jvmUsedMemory=0.3196, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0899, diskUsed=230.0904, diskTotal=460.4317, diskUsage=0.4997, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1736327198040 | 2025-01-08 17:06:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 9b7e09ebb5ef4d19bc24f8cd5282f4ff | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327197906, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5898, jvmUsedMemory=0.3355, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0943, diskUsed=230.1164, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 127 | 1736327198084 | 2025-01-08 17:06:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f913c4fc0e334c46a26d27adb96ef049 | - | - | - | - | 20979 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1736327198084 | 2025-01-08 17:06:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f913c4fc0e334c46a26d27adb96ef049 | - | - | - | - | 20979 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1736327198086 | 2025-01-08 17:06:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | f913c4fc0e334c46a26d27adb96ef049 | - | - | - | - | 20981 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1736327198092 | 2025-01-08 17:06:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | f913c4fc0e334c46a26d27adb96ef049 | - | - | - | - | 20987 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1736327198093 | 2025-01-08 17:06:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | f913c4fc0e334c46a26d27adb96ef049 | - | - | - | - | 20988 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 270 | 1736327227929 | 2025-01-08 17:07:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 738c1786caa648afb80ce9be8c528de2 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327207905, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.646, jvmUsedMemory=0.3572, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1005, diskUsed=230.1157, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1736327227931 | 2025-01-08 17:07:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 738c1786caa648afb80ce9be8c528de2 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327217906, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3926, jvmUsedMemory=0.3625, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1019, diskUsed=230.1157, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1736327227932 | 2025-01-08 17:07:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 738c1786caa648afb80ce9be8c528de2 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327227905, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1045, jvmUsedMemory=0.3731, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1049, diskUsed=230.1148, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1736327227956 | 2025-01-08 17:07:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 738c1786caa648afb80ce9be8c528de2 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 270 | 1736327227957 | 2025-01-08 17:07:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 738c1786caa648afb80ce9be8c528de2 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 311 | 1736327257928 | 2025-01-08 17:07:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 303ec603322f4e059f747a06b68b1184 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327237902, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2285, jvmUsedMemory=0.3826, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1076, diskUsed=230.1148, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 311 | 1736327257930 | 2025-01-08 17:07:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 303ec603322f4e059f747a06b68b1184 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327247905, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9653, jvmUsedMemory=0.3946, jvmMaxMemory=3.5557, jvmMemoryUsage=0.111, diskUsed=230.1177, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 311 | 1736327257930 | 2025-01-08 17:07:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 303ec603322f4e059f747a06b68b1184 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327257902, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6626, jvmUsedMemory=0.4113, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1157, diskUsed=230.1191, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 311 | 1736327257949 | 2025-01-08 17:07:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 303ec603322f4e059f747a06b68b1184 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 311 | 1736327257950 | 2025-01-08 17:07:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 303ec603322f4e059f747a06b68b1184 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 393 | 1736327287930 | 2025-01-08 17:08:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 655a453c6b41476aa69c7022cfa907a0 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327267902, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5537, jvmUsedMemory=0.4185, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1177, diskUsed=230.1191, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1736327287933 | 2025-01-08 17:08:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 655a453c6b41476aa69c7022cfa907a0 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327277905, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3945, jvmUsedMemory=0.4239, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1192, diskUsed=230.1196, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1736327287933 | 2025-01-08 17:08:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 655a453c6b41476aa69c7022cfa907a0 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327287904, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3335, jvmUsedMemory=0.4292, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1207, diskUsed=230.1187, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 357 | 1736327287951 | 2025-01-08 17:08:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | f2065d7d20234585aab477e8ab474a26 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 357 | 1736327287951 | 2025-01-08 17:08:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | f2065d7d20234585aab477e8ab474a26 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 393 | 1736327317928 | 2025-01-08 17:08:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 655a453c6b41476aa69c7022cfa907a0 | - | - | - | - | 29998 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327297906, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.2817, jvmUsedMemory=0.436, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1226, diskUsed=230.1187, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1736327317930 | 2025-01-08 17:08:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 655a453c6b41476aa69c7022cfa907a0 | - | - | - | - | 29999 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327307905, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6382, jvmUsedMemory=0.4409, jvmMaxMemory=3.5557, jvmMemoryUsage=0.124, diskUsed=230.1197, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1736327317930 | 2025-01-08 17:08:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 655a453c6b41476aa69c7022cfa907a0 | - | - | - | - | 30000 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327317905, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7607, jvmUsedMemory=0.4484, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1261, diskUsed=230.1197, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1736327317953 | 2025-01-08 17:08:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 655a453c6b41476aa69c7022cfa907a0 | - | - | - | - | 30022 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 393 | 1736327317953 | 2025-01-08 17:08:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 655a453c6b41476aa69c7022cfa907a0 | - | - | - | - | 30022 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 429 | 1736327347928 | 2025-01-08 17:09:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | bab0cd21c1e94a7bb2b09ea5045ea3af | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327327903, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8774, jvmUsedMemory=0.4573, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1286, diskUsed=230.1197, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1736327347930 | 2025-01-08 17:09:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | bab0cd21c1e94a7bb2b09ea5045ea3af | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327337907, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7485, jvmUsedMemory=0.4627, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1301, diskUsed=230.1207, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1736327347930 | 2025-01-08 17:09:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | bab0cd21c1e94a7bb2b09ea5045ea3af | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327347907, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6265, jvmUsedMemory=0.4694, jvmMaxMemory=3.5557, jvmMemoryUsage=0.132, diskUsed=230.1197, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1736327347943 | 2025-01-08 17:09:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | bab0cd21c1e94a7bb2b09ea5045ea3af | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 429 | 1736327347943 | 2025-01-08 17:09:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | bab0cd21c1e94a7bb2b09ea5045ea3af | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 468 | 1736327377928 | 2025-01-08 17:09:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 47d85382a4364216b99e4e95e41d0453 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327357907, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4497, jvmUsedMemory=0.4836, jvmMaxMemory=3.5557, jvmMemoryUsage=0.136, diskUsed=230.1201, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 468 | 1736327377930 | 2025-01-08 17:09:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 47d85382a4364216b99e4e95e41d0453 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327367907, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5947, jvmUsedMemory=0.4908, jvmMaxMemory=3.5557, jvmMemoryUsage=0.138, diskUsed=230.1172, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 468 | 1736327377930 | 2025-01-08 17:09:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 47d85382a4364216b99e4e95e41d0453 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327377906, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9575, jvmUsedMemory=0.4971, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1398, diskUsed=230.1171, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 468 | 1736327377955 | 2025-01-08 17:09:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 47d85382a4364216b99e4e95e41d0453 | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 468 | 1736327377955 | 2025-01-08 17:09:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 47d85382a4364216b99e4e95e41d0453 | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 469 | 1736327407929 | 2025-01-08 17:10:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 12dc5e2085334c4baaa9e746c3638f65 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327387905, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1045, jvmUsedMemory=0.5046, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1419, diskUsed=230.1173, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 469 | 1736327407930 | 2025-01-08 17:10:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 12dc5e2085334c4baaa9e746c3638f65 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327397905, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9277, jvmUsedMemory=0.5414, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1523, diskUsed=230.1213, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 469 | 1736327407930 | 2025-01-08 17:10:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 12dc5e2085334c4baaa9e746c3638f65 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327407907, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8711, jvmUsedMemory=0.553, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1555, diskUsed=230.122, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 469 | 1736327407943 | 2025-01-08 17:10:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 12dc5e2085334c4baaa9e746c3638f65 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 469 | 1736327407944 | 2025-01-08 17:10:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 12dc5e2085334c4baaa9e746c3638f65 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 546 | 1736327437919 | 2025-01-08 17:10:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7f66e351162841fdb25b18f0356cc6e4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327417903, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5249, jvmUsedMemory=0.6134, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1725, diskUsed=230.1268, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 546 | 1736327437920 | 2025-01-08 17:10:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7f66e351162841fdb25b18f0356cc6e4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327427902, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3701, jvmUsedMemory=0.6188, jvmMaxMemory=3.5557, jvmMemoryUsage=0.174, diskUsed=230.1278, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 546 | 1736327437920 | 2025-01-08 17:10:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7f66e351162841fdb25b18f0356cc6e4 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327437907, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.54, jvmUsedMemory=0.6241, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1755, diskUsed=230.1278, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 546 | 1736327437936 | 2025-01-08 17:10:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 7f66e351162841fdb25b18f0356cc6e4 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 546 | 1736327437936 | 2025-01-08 17:10:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 7f66e351162841fdb25b18f0356cc6e4 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 623 | 1736327467930 | 2025-01-08 17:11:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a698e3ffdfcd44699fdd3990781889f2 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327447905, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2227, jvmUsedMemory=0.6319, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1777, diskUsed=230.1278, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 623 | 1736327467932 | 2025-01-08 17:11:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a698e3ffdfcd44699fdd3990781889f2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327457907, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4219, jvmUsedMemory=0.6471, jvmMaxMemory=3.5557, jvmMemoryUsage=0.182, diskUsed=230.1297, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 623 | 1736327467932 | 2025-01-08 17:11:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a698e3ffdfcd44699fdd3990781889f2 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327467907, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1226, jvmUsedMemory=0.6542, jvmMaxMemory=3.5557, jvmMemoryUsage=0.184, diskUsed=230.1288, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 623 | 1736327467948 | 2025-01-08 17:11:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a698e3ffdfcd44699fdd3990781889f2 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 623 | 1736327467948 | 2025-01-08 17:11:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a698e3ffdfcd44699fdd3990781889f2 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1736327490391 | 2025-01-08 17:11:30 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 768471723e514460aecd7e87447b1725 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1736327490391 | 2025-01-08 17:11:30 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 342bd9990d324d36ad556af253f8d27f | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736327490392 | 2025-01-08 17:11:30 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 768471723e514460aecd7e87447b1725 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736327490394 | 2025-01-08 17:11:30 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 342bd9990d324d36ad556af253f8d27f | - | - | - | - | 3 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 623 | 1736327490436 | 2025-01-08 17:11:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | a698e3ffdfcd44699fdd3990781889f2 | - | - | - | - | 22506 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 623 | 1736327490448 | 2025-01-08 17:11:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | Actor | aroundReceive | a698e3ffdfcd44699fdd3990781889f2 | - | - | - | - | 22519 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327477907, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.103, jvmUsedMemory=0.6653, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1871, diskUsed=230.1288, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 623 | 1736327490451 | 2025-01-08 17:11:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | Actor | aroundReceive | a698e3ffdfcd44699fdd3990781889f2 | - | - | - | - | 22520 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327487907, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7793, jvmUsedMemory=0.6711, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1887, diskUsed=230.1297, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 623 | 1736327490461 | 2025-01-08 17:11:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ActorCell | receiveMessage | a698e3ffdfcd44699fdd3990781889f2 | - | - | - | - | 22531 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 126 | 1736327512453 | 2025-01-08 17:11:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 2a90d4d61961449b90857a365a742da7 | - | - | - | - | 87 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 126 | 1736327512453 | 2025-01-08 17:11:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 2a90d4d61961449b90857a365a742da7 | - | - | - | - | 87 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 269 | 1736327533200 | 2025-01-08 17:12:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 49cc6377a55a4ccd8bdb09647a0d5ed4 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327513084, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9473, jvmUsedMemory=0.4617, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1298, diskUsed=230.126, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1736327533206 | 2025-01-08 17:12:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 49cc6377a55a4ccd8bdb09647a0d5ed4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327523083, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1494, jvmUsedMemory=0.6712, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1888, diskUsed=230.1267, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1736327533206 | 2025-01-08 17:12:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 49cc6377a55a4ccd8bdb09647a0d5ed4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327533088, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2734, jvmUsedMemory=0.6883, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1936, diskUsed=230.1279, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 126 | 1736327533252 | 2025-01-08 17:12:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2a90d4d61961449b90857a365a742da7 | - | - | - | - | 20887 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 126 | 1736327533253 | 2025-01-08 17:12:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2a90d4d61961449b90857a365a742da7 | - | - | - | - | 20887 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 126 | 1736327533255 | 2025-01-08 17:12:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 2a90d4d61961449b90857a365a742da7 | - | - | - | - | 20890 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 126 | 1736327533259 | 2025-01-08 17:12:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 2a90d4d61961449b90857a365a742da7 | - | - | - | - | 20894 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 126 | 1736327533260 | 2025-01-08 17:12:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 2a90d4d61961449b90857a365a742da7 | - | - | - | - | 20894 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 309 | 1736327563118 | 2025-01-08 17:12:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 63d4c6baba6e4af1a8070140141867bc | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327543085, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9971, jvmUsedMemory=0.7101, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1997, diskUsed=230.128, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 309 | 1736327563118 | 2025-01-08 17:12:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 63d4c6baba6e4af1a8070140141867bc | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327553085, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8433, jvmUsedMemory=0.7219, jvmMaxMemory=3.5557, jvmMemoryUsage=0.203, diskUsed=230.13, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 309 | 1736327563118 | 2025-01-08 17:12:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 63d4c6baba6e4af1a8070140141867bc | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327563084, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8179, jvmUsedMemory=0.7264, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2043, diskUsed=230.1301, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 309 | 1736327563128 | 2025-01-08 17:12:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 63d4c6baba6e4af1a8070140141867bc | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 309 | 1736327563128 | 2025-01-08 17:12:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 63d4c6baba6e4af1a8070140141867bc | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1736327584807 | 2025-01-08 17:13:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | e5e37e9d658c491b95cabd439ead2219 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1736327584807 | 2025-01-08 17:13:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 950c7b71adc0405eba1452b18854bc13 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736327584809 | 2025-01-08 17:13:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | e5e37e9d658c491b95cabd439ead2219 | - | - | - | - | 2 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736327584812 | 2025-01-08 17:13:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 950c7b71adc0405eba1452b18854bc13 | - | - | - | - | 5 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 269 | 1736327584873 | 2025-01-08 17:13:04 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 49cc6377a55a4ccd8bdb09647a0d5ed4 | - | - | - | - | 51668 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 308 | 1736327584898 | 2025-01-08 17:13:04 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | cd53e46b50a04139845f0f0be79695bd | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327573090, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6309, jvmUsedMemory=0.7355, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2068, diskUsed=230.1301, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 308 | 1736327584899 | 2025-01-08 17:13:04 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | cd53e46b50a04139845f0f0be79695bd | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327583084, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2197, jvmUsedMemory=0.7425, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2088, diskUsed=230.1306, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 350 | 1736327584921 | 2025-01-08 17:13:04 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 311f28de33ab4304bb5bef2519b00e6d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1736327611874 | 2025-01-08 17:13:31 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | dbaeeaf8416d4b87a9cb775951b49222 | - | - | - | - | 395 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 124 | 1736327611875 | 2025-01-08 17:13:31 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | dbaeeaf8416d4b87a9cb775951b49222 | - | - | - | - | 395 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 225 | 1736327614033 | 2025-01-08 17:13:34 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | ecf5a7d29ce3443ba58b66630cb58f04 | - | - | - | - | 1348 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1077 ms]

warning | 262 | 1736327632607 | 2025-01-08 17:13:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 36e96345753247b698a81798314c0d2c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327612474, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7339, jvmUsedMemory=0.3007, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0846, diskUsed=230.1318, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 262 | 1736327632612 | 2025-01-08 17:13:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 36e96345753247b698a81798314c0d2c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327622474, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.627, jvmUsedMemory=0.5649, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1589, diskUsed=230.1353, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 262 | 1736327632612 | 2025-01-08 17:13:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 36e96345753247b698a81798314c0d2c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327632477, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3765, jvmUsedMemory=0.5814, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1635, diskUsed=230.137, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 124 | 1736327632650 | 2025-01-08 17:13:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | dbaeeaf8416d4b87a9cb775951b49222 | - | - | - | - | 21170 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1736327632650 | 2025-01-08 17:13:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | dbaeeaf8416d4b87a9cb775951b49222 | - | - | - | - | 21170 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1736327632652 | 2025-01-08 17:13:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | dbaeeaf8416d4b87a9cb775951b49222 | - | - | - | - | 21172 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1736327632654 | 2025-01-08 17:13:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | dbaeeaf8416d4b87a9cb775951b49222 | - | - | - | - | 21175 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1736327632655 | 2025-01-08 17:13:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | dbaeeaf8416d4b87a9cb775951b49222 | - | - | - | - | 21175 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 262 | 1736327662504 | 2025-01-08 17:14:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 36e96345753247b698a81798314c0d2c | - | - | - | - | 29892 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327642477, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6323, jvmUsedMemory=0.6112, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1719, diskUsed=230.1372, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 262 | 1736327662504 | 2025-01-08 17:14:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 36e96345753247b698a81798314c0d2c | - | - | - | - | 29892 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327652473, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4893, jvmUsedMemory=0.6219, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1749, diskUsed=230.1382, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 262 | 1736327662504 | 2025-01-08 17:14:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 36e96345753247b698a81798314c0d2c | - | - | - | - | 29892 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327662478, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0322, jvmUsedMemory=0.6292, jvmMaxMemory=3.5557, jvmMemoryUsage=0.177, diskUsed=230.1382, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 262 | 1736327662515 | 2025-01-08 17:14:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 36e96345753247b698a81798314c0d2c | - | - | - | - | 29903 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 262 | 1736327662516 | 2025-01-08 17:14:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 36e96345753247b698a81798314c0d2c | - | - | - | - | 29904 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 304 | 1736327692516 | 2025-01-08 17:14:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | bc36ef2f320047fdb968a30835c33086 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327672473, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0518, jvmUsedMemory=0.6968, jvmMaxMemory=3.5557, jvmMemoryUsage=0.196, diskUsed=230.1427, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 304 | 1736327692517 | 2025-01-08 17:14:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | bc36ef2f320047fdb968a30835c33086 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327682478, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8833, jvmUsedMemory=0.7063, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1986, diskUsed=230.1437, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 304 | 1736327692518 | 2025-01-08 17:14:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | bc36ef2f320047fdb968a30835c33086 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327692478, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3662, jvmUsedMemory=0.7162, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2014, diskUsed=230.1439, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 304 | 1736327692538 | 2025-01-08 17:14:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | bc36ef2f320047fdb968a30835c33086 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 304 | 1736327692538 | 2025-01-08 17:14:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | bc36ef2f320047fdb968a30835c33086 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 388 | 1736327722504 | 2025-01-08 17:15:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e1e463b9539e4e6eadbf346fd32eade2 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327702478, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8481, jvmUsedMemory=0.7325, jvmMaxMemory=3.5557, jvmMemoryUsage=0.206, diskUsed=230.144, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 388 | 1736327722506 | 2025-01-08 17:15:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e1e463b9539e4e6eadbf346fd32eade2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327712478, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.252, jvmUsedMemory=0.744, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2092, diskUsed=230.1454, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 388 | 1736327722506 | 2025-01-08 17:15:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e1e463b9539e4e6eadbf346fd32eade2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327722477, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7515, jvmUsedMemory=0.752, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2115, diskUsed=230.1386, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 348 | 1736327722525 | 2025-01-08 17:15:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | f2033433def14d1d9c4ca502fec1392a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 348 | 1736327722525 | 2025-01-08 17:15:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | f2033433def14d1d9c4ca502fec1392a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 388 | 1736327752507 | 2025-01-08 17:15:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | e1e463b9539e4e6eadbf346fd32eade2 | - | - | - | - | 30003 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327732474, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7217, jvmUsedMemory=0.7664, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2155, diskUsed=230.1386, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 388 | 1736327752509 | 2025-01-08 17:15:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | e1e463b9539e4e6eadbf346fd32eade2 | - | - | - | - | 30005 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327742477, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6709, jvmUsedMemory=0.786, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2211, diskUsed=230.1414, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 388 | 1736327752510 | 2025-01-08 17:15:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | e1e463b9539e4e6eadbf346fd32eade2 | - | - | - | - | 30005 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327752477, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4131, jvmUsedMemory=0.7942, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2234, diskUsed=230.1414, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 388 | 1736327752531 | 2025-01-08 17:15:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | e1e463b9539e4e6eadbf346fd32eade2 | - | - | - | - | 30026 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 388 | 1736327752532 | 2025-01-08 17:15:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | e1e463b9539e4e6eadbf346fd32eade2 | - | - | - | - | 30027 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 427 | 1736327782512 | 2025-01-08 17:16:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | fe74a32e185949138feace36efe0b081 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327762476, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7046, jvmUsedMemory=0.8078, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2272, diskUsed=230.1414, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 427 | 1736327782514 | 2025-01-08 17:16:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | fe74a32e185949138feace36efe0b081 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327772476, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3618, jvmUsedMemory=0.8166, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2297, diskUsed=230.1414, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 427 | 1736327782515 | 2025-01-08 17:16:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | fe74a32e185949138feace36efe0b081 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327782474, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2319, jvmUsedMemory=0.8251, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2321, diskUsed=230.1414, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 427 | 1736327782531 | 2025-01-08 17:16:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | fe74a32e185949138feace36efe0b081 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 427 | 1736327782532 | 2025-01-08 17:16:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | fe74a32e185949138feace36efe0b081 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 466 | 1736327812495 | 2025-01-08 17:16:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | d626f9000ff249859bcc67cb9b41531d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327792474, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0356, jvmUsedMemory=0.8391, jvmMaxMemory=3.5557, jvmMemoryUsage=0.236, diskUsed=230.1407, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 466 | 1736327812497 | 2025-01-08 17:16:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | d626f9000ff249859bcc67cb9b41531d | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327802475, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8823, jvmUsedMemory=0.8494, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2389, diskUsed=230.1407, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 466 | 1736327812498 | 2025-01-08 17:16:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | d626f9000ff249859bcc67cb9b41531d | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327812477, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5928, jvmUsedMemory=0.8572, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2411, diskUsed=230.1427, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 466 | 1736327812515 | 2025-01-08 17:16:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | d626f9000ff249859bcc67cb9b41531d | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 466 | 1736327812516 | 2025-01-08 17:16:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | d626f9000ff249859bcc67cb9b41531d | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 465 | 1736327842504 | 2025-01-08 17:17:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c478d990e39b4a9bb069f9312e7c5c7c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327822477, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6611, jvmUsedMemory=0.8682, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2442, diskUsed=230.1429, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 465 | 1736327842513 | 2025-01-08 17:17:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c478d990e39b4a9bb069f9312e7c5c7c | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327832476, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6328, jvmUsedMemory=0.8778, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2469, diskUsed=230.1419, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 465 | 1736327842515 | 2025-01-08 17:17:22 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c478d990e39b4a9bb069f9312e7c5c7c | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327842479, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5352, jvmUsedMemory=0.8854, jvmMaxMemory=3.5557, jvmMemoryUsage=0.249, diskUsed=230.1429, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 465 | 1736327842537 | 2025-01-08 17:17:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c478d990e39b4a9bb069f9312e7c5c7c | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 465 | 1736327842538 | 2025-01-08 17:17:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c478d990e39b4a9bb069f9312e7c5c7c | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 539 | 1736327872496 | 2025-01-08 17:17:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e8d15e0446624a3487f52baa9287a5dd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327852474, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6992, jvmUsedMemory=0.8969, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2523, diskUsed=230.1439, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 539 | 1736327872498 | 2025-01-08 17:17:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e8d15e0446624a3487f52baa9287a5dd | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327862476, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5913, jvmUsedMemory=0.9065, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2549, diskUsed=230.14, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 539 | 1736327872499 | 2025-01-08 17:17:52 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e8d15e0446624a3487f52baa9287a5dd | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327872477, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5737, jvmUsedMemory=0.9145, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2572, diskUsed=230.1414, diskTotal=460.4317, diskUsage=0.4998, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 539 | 1736327872519 | 2025-01-08 17:17:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | e8d15e0446624a3487f52baa9287a5dd | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 539 | 1736327872520 | 2025-01-08 17:17:52 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | e8d15e0446624a3487f52baa9287a5dd | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1736327878489 | 2025-01-08 17:17:58 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | cb3454c6480149d4b098cced8381548c | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736327878489 | 2025-01-08 17:17:58 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 0784d467445a4913bf92dcacdfeaddc5 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1736327878489 | 2025-01-08 17:17:58 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 0784d467445a4913bf92dcacdfeaddc5 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736327878493 | 2025-01-08 17:17:58 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | cb3454c6480149d4b098cced8381548c | - | - | - | - | 6 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 539 | 1736327878528 | 2025-01-08 17:17:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | e8d15e0446624a3487f52baa9287a5dd | - | - | - | - | 6032 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 122 | 1736327901670 | 2025-01-08 17:18:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 3892c43833114db28482c1e34640cd67 | - | - | - | - | 92 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 122 | 1736327901670 | 2025-01-08 17:18:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 3892c43833114db28482c1e34640cd67 | - | - | - | - | 92 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 262 | 1736327922322 | 2025-01-08 17:18:42 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 515167a6e8164d1eb9a84cb0d88bdccd | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327902216, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6689, jvmUsedMemory=0.4932, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1387, diskUsed=230.1615, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 262 | 1736327922328 | 2025-01-08 17:18:42 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 515167a6e8164d1eb9a84cb0d88bdccd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327912220, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.252, jvmUsedMemory=0.609, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1713, diskUsed=230.1618, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 262 | 1736327922329 | 2025-01-08 17:18:42 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 515167a6e8164d1eb9a84cb0d88bdccd | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327922224, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0459, jvmUsedMemory=0.6154, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1731, diskUsed=230.1619, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 122 | 1736327922375 | 2025-01-08 17:18:42 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3892c43833114db28482c1e34640cd67 | - | - | - | - | 20797 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 122 | 1736327922375 | 2025-01-08 17:18:42 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3892c43833114db28482c1e34640cd67 | - | - | - | - | 20797 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 122 | 1736327922377 | 2025-01-08 17:18:42 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 3892c43833114db28482c1e34640cd67 | - | - | - | - | 20799 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 122 | 1736327922380 | 2025-01-08 17:18:42 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 3892c43833114db28482c1e34640cd67 | - | - | - | - | 20802 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 122 | 1736327922380 | 2025-01-08 17:18:42 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 3892c43833114db28482c1e34640cd67 | - | - | - | - | 20802 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 262 | 1736327952251 | 2025-01-08 17:19:12 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 515167a6e8164d1eb9a84cb0d88bdccd | - | - | - | - | 29924 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327932224, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9648, jvmUsedMemory=0.6314, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1776, diskUsed=230.162, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 262 | 1736327952254 | 2025-01-08 17:19:12 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 515167a6e8164d1eb9a84cb0d88bdccd | - | - | - | - | 29926 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327942228, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5884, jvmUsedMemory=0.6396, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1799, diskUsed=230.1622, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 262 | 1736327952254 | 2025-01-08 17:19:12 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 515167a6e8164d1eb9a84cb0d88bdccd | - | - | - | - | 29926 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327952232, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8115, jvmUsedMemory=0.6437, jvmMaxMemory=3.5557, jvmMemoryUsage=0.181, diskUsed=230.1622, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 262 | 1736327952276 | 2025-01-08 17:19:12 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 515167a6e8164d1eb9a84cb0d88bdccd | - | - | - | - | 29948 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 262 | 1736327952277 | 2025-01-08 17:19:12 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 515167a6e8164d1eb9a84cb0d88bdccd | - | - | - | - | 29949 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 347 | 1736327982253 | 2025-01-08 17:19:42 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 88f3de8e0459464496442b4843cb9f33 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327962231, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8335, jvmUsedMemory=0.6528, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1836, diskUsed=230.1624, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 347 | 1736327982256 | 2025-01-08 17:19:42 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 88f3de8e0459464496442b4843cb9f33 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327972234, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5513, jvmUsedMemory=0.6603, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1857, diskUsed=230.1623, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 347 | 1736327982257 | 2025-01-08 17:19:42 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 88f3de8e0459464496442b4843cb9f33 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327982231, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3125, jvmUsedMemory=0.6648, jvmMaxMemory=3.5557, jvmMemoryUsage=0.187, diskUsed=230.1622, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 347 | 1736327982272 | 2025-01-08 17:19:42 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 88f3de8e0459464496442b4843cb9f33 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 347 | 1736327982274 | 2025-01-08 17:19:42 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 88f3de8e0459464496442b4843cb9f33 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 387 | 1736328012253 | 2025-01-08 17:20:12 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 04ebe7808e154d12a3bbca3512cedbd3 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736327992232, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0303, jvmUsedMemory=0.6709, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1887, diskUsed=230.1622, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 387 | 1736328012255 | 2025-01-08 17:20:12 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 04ebe7808e154d12a3bbca3512cedbd3 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328002233, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7915, jvmUsedMemory=0.6769, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1904, diskUsed=230.1622, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 387 | 1736328012255 | 2025-01-08 17:20:12 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 04ebe7808e154d12a3bbca3512cedbd3 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328012231, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3389, jvmUsedMemory=0.6826, jvmMaxMemory=3.5557, jvmMemoryUsage=0.192, diskUsed=230.1632, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 301 | 1736328012272 | 2025-01-08 17:20:12 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 9d97e95c84ca45dfbdde8f972a3fc5cb | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 301 | 1736328012273 | 2025-01-08 17:20:12 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 9d97e95c84ca45dfbdde8f972a3fc5cb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 427 | 1736328042261 | 2025-01-08 17:20:42 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 842fb336bfba4e43ad8806d0ab809549 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328022233, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4399, jvmUsedMemory=0.6902, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1941, diskUsed=230.1633, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 427 | 1736328042262 | 2025-01-08 17:20:42 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 842fb336bfba4e43ad8806d0ab809549 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328032234, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0347, jvmUsedMemory=0.6958, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1957, diskUsed=230.1605, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 427 | 1736328042262 | 2025-01-08 17:20:42 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 842fb336bfba4e43ad8806d0ab809549 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328042234, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1826, jvmUsedMemory=0.6999, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1969, diskUsed=230.1605, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 426 | 1736328042280 | 2025-01-08 17:20:42 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 9ad23151eb544663a1048303ab25752a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 426 | 1736328042281 | 2025-01-08 17:20:42 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 9ad23151eb544663a1048303ab25752a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1736328072160 | 2025-01-08 17:21:12 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 21b610f406af4f4e85e6a437b2d56446 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736328072160 | 2025-01-08 17:21:12 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 9c931a05fd544327b1352c7f4947712f | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1736328072165 | 2025-01-08 17:21:12 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 9c931a05fd544327b1352c7f4947712f | - | - | - | - | 5 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736328072167 | 2025-01-08 17:21:12 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 21b610f406af4f4e85e6a437b2d56446 | - | - | - | - | 8 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 427 | 1736328072199 | 2025-01-08 17:21:12 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 842fb336bfba4e43ad8806d0ab809549 | - | - | - | - | 29939 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 427 | 1736328072206 | 2025-01-08 17:21:12 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 842fb336bfba4e43ad8806d0ab809549 | - | - | - | - | 29945 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328052234, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.811, jvmUsedMemory=0.7064, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1987, diskUsed=230.1616, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 427 | 1736328072206 | 2025-01-08 17:21:12 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 842fb336bfba4e43ad8806d0ab809549 | - | - | - | - | 29945 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328062231, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4453, jvmUsedMemory=0.712, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2002, diskUsed=230.1616, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 427 | 1736328072215 | 2025-01-08 17:21:12 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 842fb336bfba4e43ad8806d0ab809549 | - | - | - | - | 29955 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1736328108021 | 2025-01-08 17:21:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 80169e2d03cc42eeabd89289c5fbfaee | - | - | - | - | 98 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 124 | 1736328108021 | 2025-01-08 17:21:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 80169e2d03cc42eeabd89289c5fbfaee | - | - | - | - | 98 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 264 | 1736328128840 | 2025-01-08 17:22:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 83b136e236c04e1e9cec9b3022dfde2c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328108618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.3276, jvmUsedMemory=0.4883, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1373, diskUsed=230.1628, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 264 | 1736328128855 | 2025-01-08 17:22:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 83b136e236c04e1e9cec9b3022dfde2c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328118615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.582, jvmUsedMemory=0.6309, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1774, diskUsed=230.1628, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 264 | 1736328128855 | 2025-01-08 17:22:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 83b136e236c04e1e9cec9b3022dfde2c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328128613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.8252, jvmUsedMemory=0.6401, jvmMaxMemory=3.5557, jvmMemoryUsage=0.18, diskUsed=230.1593, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 124 | 1736328128938 | 2025-01-08 17:22:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 80169e2d03cc42eeabd89289c5fbfaee | - | - | - | - | 21016 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1736328128939 | 2025-01-08 17:22:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 80169e2d03cc42eeabd89289c5fbfaee | - | - | - | - | 21016 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1736328128942 | 2025-01-08 17:22:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 80169e2d03cc42eeabd89289c5fbfaee | - | - | - | - | 21020 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1736328128951 | 2025-01-08 17:22:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 80169e2d03cc42eeabd89289c5fbfaee | - | - | - | - | 21028 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1736328128951 | 2025-01-08 17:22:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 80169e2d03cc42eeabd89289c5fbfaee | - | - | - | - | 21028 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 265 | 1736328158639 | 2025-01-08 17:22:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ef2f8dbdada64b8698c9fbf2f1226ed2 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328138615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.6177, jvmUsedMemory=0.6598, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1856, diskUsed=230.1512, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 265 | 1736328158641 | 2025-01-08 17:22:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ef2f8dbdada64b8698c9fbf2f1226ed2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328148616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.583, jvmUsedMemory=0.7382, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2076, diskUsed=230.1539, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 265 | 1736328158641 | 2025-01-08 17:22:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ef2f8dbdada64b8698c9fbf2f1226ed2 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328158613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.958, jvmUsedMemory=0.7538, jvmMaxMemory=3.5557, jvmMemoryUsage=0.212, diskUsed=230.1549, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 265 | 1736328158667 | 2025-01-08 17:22:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | ef2f8dbdada64b8698c9fbf2f1226ed2 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 265 | 1736328158667 | 2025-01-08 17:22:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | ef2f8dbdada64b8698c9fbf2f1226ed2 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 312 | 1736328188648 | 2025-01-08 17:23:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 962bc26895464755a29b1128f82f4085 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328168616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5024, jvmUsedMemory=0.7774, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2186, diskUsed=230.1564, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 312 | 1736328188651 | 2025-01-08 17:23:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 962bc26895464755a29b1128f82f4085 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328178613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4814, jvmUsedMemory=0.8325, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2341, diskUsed=230.1623, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 312 | 1736328188652 | 2025-01-08 17:23:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 962bc26895464755a29b1128f82f4085 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328188616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.792, jvmUsedMemory=0.8398, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2362, diskUsed=230.1623, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 312 | 1736328188666 | 2025-01-08 17:23:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 962bc26895464755a29b1128f82f4085 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 312 | 1736328188667 | 2025-01-08 17:23:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 962bc26895464755a29b1128f82f4085 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 392 | 1736328218639 | 2025-01-08 17:23:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 176cca71deed460789bb56f1fddb3661 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328198615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6826, jvmUsedMemory=0.8522, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2397, diskUsed=230.1623, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1736328218642 | 2025-01-08 17:23:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 176cca71deed460789bb56f1fddb3661 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328208615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2637, jvmUsedMemory=0.8577, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2412, diskUsed=230.1644, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1736328218642 | 2025-01-08 17:23:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 176cca71deed460789bb56f1fddb3661 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328218615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0688, jvmUsedMemory=0.8631, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2428, diskUsed=230.1644, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 357 | 1736328218661 | 2025-01-08 17:23:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 530bf263c7034690a97083ddeb994031 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 357 | 1736328218661 | 2025-01-08 17:23:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 530bf263c7034690a97083ddeb994031 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 392 | 1736328248637 | 2025-01-08 17:24:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 176cca71deed460789bb56f1fddb3661 | - | - | - | - | 29999 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328228616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9102, jvmUsedMemory=0.8726, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2454, diskUsed=230.1644, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1736328248641 | 2025-01-08 17:24:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 176cca71deed460789bb56f1fddb3661 | - | - | - | - | 30002 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328238615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6895, jvmUsedMemory=0.8785, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2471, diskUsed=230.1644, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1736328248642 | 2025-01-08 17:24:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 176cca71deed460789bb56f1fddb3661 | - | - | - | - | 30003 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328248615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4292, jvmUsedMemory=0.884, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2486, diskUsed=230.1634, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1736328248661 | 2025-01-08 17:24:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 176cca71deed460789bb56f1fddb3661 | - | - | - | - | 30021 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 392 | 1736328248662 | 2025-01-08 17:24:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 176cca71deed460789bb56f1fddb3661 | - | - | - | - | 30022 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 431 | 1736328278645 | 2025-01-08 17:24:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | d127c72bb0ac4176b2c347454f71e6c2 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328258612, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2954, jvmUsedMemory=0.8918, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2508, diskUsed=230.1633, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1736328278646 | 2025-01-08 17:24:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | d127c72bb0ac4176b2c347454f71e6c2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328268617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4575, jvmUsedMemory=0.8982, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2526, diskUsed=230.1633, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1736328278647 | 2025-01-08 17:24:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | d127c72bb0ac4176b2c347454f71e6c2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328278617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1592, jvmUsedMemory=0.9051, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2545, diskUsed=230.1634, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1736328278660 | 2025-01-08 17:24:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | d127c72bb0ac4176b2c347454f71e6c2 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 431 | 1736328278660 | 2025-01-08 17:24:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | d127c72bb0ac4176b2c347454f71e6c2 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 472 | 1736328308650 | 2025-01-08 17:25:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 278480d0bb4348ab8ce18634e41d4eae | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328288617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9004, jvmUsedMemory=0.9141, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2571, diskUsed=230.1633, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 472 | 1736328308653 | 2025-01-08 17:25:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 278480d0bb4348ab8ce18634e41d4eae | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328298616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6079, jvmUsedMemory=0.92, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2587, diskUsed=230.1633, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 472 | 1736328308654 | 2025-01-08 17:25:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 278480d0bb4348ab8ce18634e41d4eae | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328308616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8823, jvmUsedMemory=0.9264, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2605, diskUsed=230.1624, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 472 | 1736328308670 | 2025-01-08 17:25:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 278480d0bb4348ab8ce18634e41d4eae | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 472 | 1736328308670 | 2025-01-08 17:25:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 278480d0bb4348ab8ce18634e41d4eae | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 471 | 1736328338638 | 2025-01-08 17:25:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 185190cc825c4ad38aaa08dfd7ca6c48 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328318617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6729, jvmUsedMemory=0.9333, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2625, diskUsed=230.1624, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 471 | 1736328338640 | 2025-01-08 17:25:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 185190cc825c4ad38aaa08dfd7ca6c48 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328328617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7354, jvmUsedMemory=0.9392, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2641, diskUsed=230.1634, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 471 | 1736328338641 | 2025-01-08 17:25:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 185190cc825c4ad38aaa08dfd7ca6c48 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328338614, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.689, jvmUsedMemory=0.9456, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2659, diskUsed=230.1635, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 471 | 1736328338657 | 2025-01-08 17:25:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 185190cc825c4ad38aaa08dfd7ca6c48 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 471 | 1736328338658 | 2025-01-08 17:25:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 185190cc825c4ad38aaa08dfd7ca6c48 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 549 | 1736328368640 | 2025-01-08 17:26:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 4ed77a2988b34938a558a318400426ac | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328348617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4292, jvmUsedMemory=0.9537, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2682, diskUsed=230.1635, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 549 | 1736328368642 | 2025-01-08 17:26:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 4ed77a2988b34938a558a318400426ac | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328358615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.209, jvmUsedMemory=0.9605, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2701, diskUsed=230.1635, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 549 | 1736328368642 | 2025-01-08 17:26:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 4ed77a2988b34938a558a318400426ac | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328368614, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.0225, jvmUsedMemory=0.9656, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2716, diskUsed=230.1625, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 549 | 1736328368658 | 2025-01-08 17:26:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 4ed77a2988b34938a558a318400426ac | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 549 | 1736328368658 | 2025-01-08 17:26:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 4ed77a2988b34938a558a318400426ac | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 586 | 1736328398639 | 2025-01-08 17:26:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 35382ad5cd064faf91b7149e05d349b1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328378616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=0.8647, jvmUsedMemory=0.9729, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2736, diskUsed=230.1625, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 586 | 1736328398640 | 2025-01-08 17:26:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 35382ad5cd064faf91b7149e05d349b1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328388617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.2056, jvmUsedMemory=0.978, jvmMaxMemory=3.5557, jvmMemoryUsage=0.275, diskUsed=230.1645, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 586 | 1736328398640 | 2025-01-08 17:26:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 35382ad5cd064faf91b7149e05d349b1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328398615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.1001, jvmUsedMemory=0.9834, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2766, diskUsed=230.1645, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 586 | 1736328398809 | 2025-01-08 17:26:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 35382ad5cd064faf91b7149e05d349b1 | - | - | - | - | 170 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 586 | 1736328398809 | 2025-01-08 17:26:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 35382ad5cd064faf91b7149e05d349b1 | - | - | - | - | 170 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 622 | 1736328428638 | 2025-01-08 17:27:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a55c69d3710043e5b97e229e221c4f25 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328408614, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.2314, jvmUsedMemory=0.1701, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0479, diskUsed=230.1654, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 622 | 1736328428640 | 2025-01-08 17:27:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a55c69d3710043e5b97e229e221c4f25 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328418618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0732, jvmUsedMemory=0.1752, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0493, diskUsed=230.1655, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 622 | 1736328428640 | 2025-01-08 17:27:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a55c69d3710043e5b97e229e221c4f25 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328428617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7539, jvmUsedMemory=0.1793, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0504, diskUsed=230.1646, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 622 | 1736328428658 | 2025-01-08 17:27:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a55c69d3710043e5b97e229e221c4f25 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 622 | 1736328428658 | 2025-01-08 17:27:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a55c69d3710043e5b97e229e221c4f25 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 623 | 1736328458649 | 2025-01-08 17:27:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 06a2dc1c8ddc4088997ca40d98a431d9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328438620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5576, jvmUsedMemory=0.1838, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0517, diskUsed=230.1647, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 623 | 1736328458650 | 2025-01-08 17:27:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 06a2dc1c8ddc4088997ca40d98a431d9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328448616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3911, jvmUsedMemory=0.188, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0529, diskUsed=230.1657, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 623 | 1736328458651 | 2025-01-08 17:27:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 06a2dc1c8ddc4088997ca40d98a431d9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328458618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4038, jvmUsedMemory=0.1911, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0537, diskUsed=230.1675, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 623 | 1736328458675 | 2025-01-08 17:27:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 06a2dc1c8ddc4088997ca40d98a431d9 | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 623 | 1736328458675 | 2025-01-08 17:27:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 06a2dc1c8ddc4088997ca40d98a431d9 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 697 | 1736328488640 | 2025-01-08 17:28:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 51b5af21d63644bfb8ac2f5e3368cf04 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328468618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4819, jvmUsedMemory=0.1957, jvmMaxMemory=3.5557, jvmMemoryUsage=0.055, diskUsed=230.1675, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 697 | 1736328488642 | 2025-01-08 17:28:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 51b5af21d63644bfb8ac2f5e3368cf04 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328478618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5547, jvmUsedMemory=0.198, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0557, diskUsed=230.1685, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 697 | 1736328488643 | 2025-01-08 17:28:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 51b5af21d63644bfb8ac2f5e3368cf04 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328488618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9048, jvmUsedMemory=0.2027, jvmMaxMemory=3.5557, jvmMemoryUsage=0.057, diskUsed=230.1676, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 697 | 1736328488660 | 2025-01-08 17:28:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 51b5af21d63644bfb8ac2f5e3368cf04 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 697 | 1736328488661 | 2025-01-08 17:28:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 51b5af21d63644bfb8ac2f5e3368cf04 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 695 | 1736328518640 | 2025-01-08 17:28:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c4a1e0931b164d6fa0f42d3ba6843e53 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328498617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2871, jvmUsedMemory=0.2072, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0583, diskUsed=230.1686, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 695 | 1736328518641 | 2025-01-08 17:28:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c4a1e0931b164d6fa0f42d3ba6843e53 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328508618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3833, jvmUsedMemory=0.2103, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0591, diskUsed=230.1687, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 695 | 1736328518642 | 2025-01-08 17:28:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c4a1e0931b164d6fa0f42d3ba6843e53 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328518617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1704, jvmUsedMemory=0.2143, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0603, diskUsed=230.1687, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 695 | 1736328518656 | 2025-01-08 17:28:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c4a1e0931b164d6fa0f42d3ba6843e53 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 695 | 1736328518657 | 2025-01-08 17:28:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c4a1e0931b164d6fa0f42d3ba6843e53 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 798 | 1736328548640 | 2025-01-08 17:29:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c6ab49b5b8e74bc39d03d7c2064be1be | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-82 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328528619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0703, jvmUsedMemory=0.2192, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0616, diskUsed=230.1687, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 798 | 1736328548642 | 2025-01-08 17:29:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c6ab49b5b8e74bc39d03d7c2064be1be | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-82 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328538619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5137, jvmUsedMemory=0.2217, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0624, diskUsed=230.1687, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 798 | 1736328548642 | 2025-01-08 17:29:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c6ab49b5b8e74bc39d03d7c2064be1be | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-82 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328548617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2739, jvmUsedMemory=0.2255, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0634, diskUsed=230.1677, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 798 | 1736328548658 | 2025-01-08 17:29:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c6ab49b5b8e74bc39d03d7c2064be1be | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-82 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 798 | 1736328548659 | 2025-01-08 17:29:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c6ab49b5b8e74bc39d03d7c2064be1be | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-82 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 799 | 1736328578641 | 2025-01-08 17:29:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c3efc66db5f24e09b7dca3c68ceef977 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328558616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.145, jvmUsedMemory=0.2313, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0651, diskUsed=230.1688, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 799 | 1736328578643 | 2025-01-08 17:29:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c3efc66db5f24e09b7dca3c68ceef977 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328568617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.209, jvmUsedMemory=0.2349, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0661, diskUsed=230.1689, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 799 | 1736328578643 | 2025-01-08 17:29:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c3efc66db5f24e09b7dca3c68ceef977 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328578620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3364, jvmUsedMemory=0.2387, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0671, diskUsed=230.1702, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 799 | 1736328578661 | 2025-01-08 17:29:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c3efc66db5f24e09b7dca3c68ceef977 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 799 | 1736328578661 | 2025-01-08 17:29:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c3efc66db5f24e09b7dca3c68ceef977 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 765 | 1736328608640 | 2025-01-08 17:30:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | dc95383db082440da95430326946e05d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328588619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0566, jvmUsedMemory=0.2429, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0683, diskUsed=230.1702, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 765 | 1736328608642 | 2025-01-08 17:30:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | dc95383db082440da95430326946e05d | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328598615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9673, jvmUsedMemory=0.246, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0692, diskUsed=230.1702, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 765 | 1736328608643 | 2025-01-08 17:30:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | dc95383db082440da95430326946e05d | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328608615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6646, jvmUsedMemory=0.2514, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0707, diskUsed=230.1693, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 765 | 1736328608662 | 2025-01-08 17:30:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | dc95383db082440da95430326946e05d | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 765 | 1736328608662 | 2025-01-08 17:30:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | dc95383db082440da95430326946e05d | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 868 | 1736328638639 | 2025-01-08 17:30:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 9edf866728ba47b483f0f6b3927fdc1e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-86 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328618615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5557, jvmUsedMemory=0.2565, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0721, diskUsed=230.1693, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 868 | 1736328638640 | 2025-01-08 17:30:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9edf866728ba47b483f0f6b3927fdc1e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-86 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328628619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3896, jvmUsedMemory=0.2589, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0728, diskUsed=230.1703, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 868 | 1736328638641 | 2025-01-08 17:30:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 9edf866728ba47b483f0f6b3927fdc1e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-86 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328638618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3965, jvmUsedMemory=0.263, jvmMaxMemory=3.5557, jvmMemoryUsage=0.074, diskUsed=230.1706, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 868 | 1736328638657 | 2025-01-08 17:30:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 9edf866728ba47b483f0f6b3927fdc1e | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-86 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 868 | 1736328638658 | 2025-01-08 17:30:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 9edf866728ba47b483f0f6b3927fdc1e | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-86 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 903 | 1736328668641 | 2025-01-08 17:31:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 503a41b0df014f24bb119caa4b8bccc6 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328648615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3413, jvmUsedMemory=0.2679, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0753, diskUsed=230.1706, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 903 | 1736328668643 | 2025-01-08 17:31:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 503a41b0df014f24bb119caa4b8bccc6 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328658619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5225, jvmUsedMemory=0.2712, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0763, diskUsed=230.1717, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 903 | 1736328668643 | 2025-01-08 17:31:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 503a41b0df014f24bb119caa4b8bccc6 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328668617, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5825, jvmUsedMemory=0.2742, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0771, diskUsed=230.1707, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 903 | 1736328668663 | 2025-01-08 17:31:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 503a41b0df014f24bb119caa4b8bccc6 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 903 | 1736328668664 | 2025-01-08 17:31:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 503a41b0df014f24bb119caa4b8bccc6 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 938 | 1736328698640 | 2025-01-08 17:31:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 1b46569919f5437da096b556510d6094 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328678616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7393, jvmUsedMemory=0.2789, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0784, diskUsed=230.1708, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 938 | 1736328698641 | 2025-01-08 17:31:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 1b46569919f5437da096b556510d6094 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328688618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4712, jvmUsedMemory=0.2822, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0794, diskUsed=230.1718, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 938 | 1736328698642 | 2025-01-08 17:31:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 1b46569919f5437da096b556510d6094 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328698618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7666, jvmUsedMemory=0.2858, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0804, diskUsed=230.1718, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 938 | 1736328698663 | 2025-01-08 17:31:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 1b46569919f5437da096b556510d6094 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 938 | 1736328698663 | 2025-01-08 17:31:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 1b46569919f5437da096b556510d6094 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 973 | 1736328728641 | 2025-01-08 17:32:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3360b6dd5f1c45d68c733f75351fe20b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-94 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328708620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4946, jvmUsedMemory=0.293, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0824, diskUsed=230.1718, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 973 | 1736328728643 | 2025-01-08 17:32:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3360b6dd5f1c45d68c733f75351fe20b | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-94 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328718620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6519, jvmUsedMemory=0.2956, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0831, diskUsed=230.1718, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 973 | 1736328728644 | 2025-01-08 17:32:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3360b6dd5f1c45d68c733f75351fe20b | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-94 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328728620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8525, jvmUsedMemory=0.2994, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0842, diskUsed=230.1714, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 973 | 1736328728661 | 2025-01-08 17:32:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3360b6dd5f1c45d68c733f75351fe20b | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-94 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 973 | 1736328728661 | 2025-01-08 17:32:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3360b6dd5f1c45d68c733f75351fe20b | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-94 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 972 | 1736328758641 | 2025-01-08 17:32:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 99eb490c061941ce9b726c09b90a4e43 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328738620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8076, jvmUsedMemory=0.3037, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0854, diskUsed=230.1715, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 972 | 1736328758644 | 2025-01-08 17:32:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 99eb490c061941ce9b726c09b90a4e43 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328748619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8301, jvmUsedMemory=0.3087, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0868, diskUsed=230.1729, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 972 | 1736328758645 | 2025-01-08 17:32:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 99eb490c061941ce9b726c09b90a4e43 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328758620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0029, jvmUsedMemory=0.3124, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0879, diskUsed=230.1739, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 972 | 1736328758662 | 2025-01-08 17:32:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 99eb490c061941ce9b726c09b90a4e43 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 972 | 1736328758662 | 2025-01-08 17:32:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 99eb490c061941ce9b726c09b90a4e43 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1042 | 1736328788641 | 2025-01-08 17:33:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 58cd7a497efc418287c0f75c8053e46b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328768619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9155, jvmUsedMemory=0.3175, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0893, diskUsed=230.1749, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1042 | 1736328788643 | 2025-01-08 17:33:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 58cd7a497efc418287c0f75c8053e46b | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328778621, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7739, jvmUsedMemory=0.3203, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0901, diskUsed=230.1749, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1042 | 1736328788644 | 2025-01-08 17:33:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 58cd7a497efc418287c0f75c8053e46b | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328788621, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7217, jvmUsedMemory=0.3234, jvmMaxMemory=3.5557, jvmMemoryUsage=0.091, diskUsed=230.1739, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1042 | 1736328788671 | 2025-01-08 17:33:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 58cd7a497efc418287c0f75c8053e46b | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1042 | 1736328788672 | 2025-01-08 17:33:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 58cd7a497efc418287c0f75c8053e46b | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1079 | 1736328818643 | 2025-01-08 17:33:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 40735998b40446c4883869d1c2d2cec7 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328798620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.604, jvmUsedMemory=0.3297, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0927, diskUsed=230.1739, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1079 | 1736328818644 | 2025-01-08 17:33:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 40735998b40446c4883869d1c2d2cec7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328808619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7505, jvmUsedMemory=0.3345, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0941, diskUsed=230.1749, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1079 | 1736328818645 | 2025-01-08 17:33:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 40735998b40446c4883869d1c2d2cec7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328818621, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5605, jvmUsedMemory=0.3381, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0951, diskUsed=230.1749, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1079 | 1736328818664 | 2025-01-08 17:33:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 40735998b40446c4883869d1c2d2cec7 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1079 | 1736328818664 | 2025-01-08 17:33:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 40735998b40446c4883869d1c2d2cec7 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1077 | 1736328848643 | 2025-01-08 17:34:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 23f74b6db70b48b199a804752f0fe674 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-100 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328828616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.394, jvmUsedMemory=0.3424, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0963, diskUsed=230.1749, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1077 | 1736328848645 | 2025-01-08 17:34:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 23f74b6db70b48b199a804752f0fe674 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-100 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328838620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.2529, jvmUsedMemory=0.3457, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0972, diskUsed=230.1748, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1077 | 1736328848646 | 2025-01-08 17:34:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 23f74b6db70b48b199a804752f0fe674 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-100 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328848620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8892, jvmUsedMemory=0.3492, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0982, diskUsed=230.1742, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1077 | 1736328848664 | 2025-01-08 17:34:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 23f74b6db70b48b199a804752f0fe674 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-100 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1077 | 1736328848664 | 2025-01-08 17:34:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 23f74b6db70b48b199a804752f0fe674 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-100 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1146 | 1736328878642 | 2025-01-08 17:34:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 085fe33400974618969823d143c2ad43 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-105 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328858621, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6787, jvmUsedMemory=0.3545, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0997, diskUsed=230.1742, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1146 | 1736328878644 | 2025-01-08 17:34:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 085fe33400974618969823d143c2ad43 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-105 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328868621, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5737, jvmUsedMemory=0.3573, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1005, diskUsed=230.1762, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1146 | 1736328878644 | 2025-01-08 17:34:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 085fe33400974618969823d143c2ad43 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-105 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328878620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4048, jvmUsedMemory=0.3606, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1014, diskUsed=230.1763, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1146 | 1736328878663 | 2025-01-08 17:34:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 085fe33400974618969823d143c2ad43 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-105 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1146 | 1736328878663 | 2025-01-08 17:34:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 085fe33400974618969823d143c2ad43 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-105 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1181 | 1736328908642 | 2025-01-08 17:35:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 2a073ae85be54098b5ca0d8adb721b43 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328888622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.563, jvmUsedMemory=0.365, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1027, diskUsed=230.1763, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1181 | 1736328908643 | 2025-01-08 17:35:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 2a073ae85be54098b5ca0d8adb721b43 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328898621, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3223, jvmUsedMemory=0.3674, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1033, diskUsed=230.1762, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1181 | 1736328908644 | 2025-01-08 17:35:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 2a073ae85be54098b5ca0d8adb721b43 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328908620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.1924, jvmUsedMemory=0.3706, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1042, diskUsed=230.1753, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1181 | 1736328908657 | 2025-01-08 17:35:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2a073ae85be54098b5ca0d8adb721b43 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1181 | 1736328908658 | 2025-01-08 17:35:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2a073ae85be54098b5ca0d8adb721b43 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1213 | 1736328938644 | 2025-01-08 17:35:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | bc367c0a22bd46a794018021447b7b8e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328918619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4146, jvmUsedMemory=0.375, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1055, diskUsed=230.1779, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1213 | 1736328938646 | 2025-01-08 17:35:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | bc367c0a22bd46a794018021447b7b8e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328928622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5718, jvmUsedMemory=0.3778, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1063, diskUsed=230.1788, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1213 | 1736328938646 | 2025-01-08 17:35:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | bc367c0a22bd46a794018021447b7b8e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328938622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4575, jvmUsedMemory=0.3811, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1072, diskUsed=230.1796, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1213 | 1736328938661 | 2025-01-08 17:35:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | bc367c0a22bd46a794018021447b7b8e | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1213 | 1736328938661 | 2025-01-08 17:35:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | bc367c0a22bd46a794018021447b7b8e | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1214 | 1736328968641 | 2025-01-08 17:36:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 336e351fa2ba4f4c9bf608e1e0e6813c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328948620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0054, jvmUsedMemory=0.3857, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1085, diskUsed=230.1795, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1214 | 1736328968643 | 2025-01-08 17:36:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 336e351fa2ba4f4c9bf608e1e0e6813c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328958621, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.623, jvmUsedMemory=0.389, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1094, diskUsed=230.1795, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1214 | 1736328968644 | 2025-01-08 17:36:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 336e351fa2ba4f4c9bf608e1e0e6813c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328968622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5938, jvmUsedMemory=0.392, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1103, diskUsed=230.1785, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1214 | 1736328968659 | 2025-01-08 17:36:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 336e351fa2ba4f4c9bf608e1e0e6813c | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1214 | 1736328968660 | 2025-01-08 17:36:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 336e351fa2ba4f4c9bf608e1e0e6813c | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1280 | 1736328998645 | 2025-01-08 17:36:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | b1a2163da02049e4864f75aad3705e69 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328978622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3481, jvmUsedMemory=0.3972, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1117, diskUsed=230.1785, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1280 | 1736328998648 | 2025-01-08 17:36:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | b1a2163da02049e4864f75aad3705e69 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328988622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9863, jvmUsedMemory=0.4, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1125, diskUsed=230.1792, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1280 | 1736328998648 | 2025-01-08 17:36:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | b1a2163da02049e4864f75aad3705e69 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736328998622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0488, jvmUsedMemory=0.4028, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1133, diskUsed=230.1792, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1280 | 1736328998667 | 2025-01-08 17:36:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | b1a2163da02049e4864f75aad3705e69 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1280 | 1736328998668 | 2025-01-08 17:36:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | b1a2163da02049e4864f75aad3705e69 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1349 | 1736329028652 | 2025-01-08 17:37:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 9c6ce35d470a4e46a2ad9d3e40991a50 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329008619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9731, jvmUsedMemory=0.409, jvmMaxMemory=3.5557, jvmMemoryUsage=0.115, diskUsed=230.1803, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1349 | 1736329028656 | 2025-01-08 17:37:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9c6ce35d470a4e46a2ad9d3e40991a50 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329018622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7432, jvmUsedMemory=0.4111, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1156, diskUsed=230.1815, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1349 | 1736329028657 | 2025-01-08 17:37:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 9c6ce35d470a4e46a2ad9d3e40991a50 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329028623, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6284, jvmUsedMemory=0.4139, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1164, diskUsed=230.1815, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1349 | 1736329028672 | 2025-01-08 17:37:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 9c6ce35d470a4e46a2ad9d3e40991a50 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1349 | 1736329028673 | 2025-01-08 17:37:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 9c6ce35d470a4e46a2ad9d3e40991a50 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1282 | 1736329058643 | 2025-01-08 17:37:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 80b44200a1c54f2094c3f86b628e96e9 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-115 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329038619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4512, jvmUsedMemory=0.4185, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1177, diskUsed=230.1827, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1282 | 1736329058644 | 2025-01-08 17:37:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 80b44200a1c54f2094c3f86b628e96e9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-115 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329048619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5415, jvmUsedMemory=0.428, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1204, diskUsed=230.1827, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1282 | 1736329058645 | 2025-01-08 17:37:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 80b44200a1c54f2094c3f86b628e96e9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-115 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329058622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4517, jvmUsedMemory=0.4313, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1213, diskUsed=230.1828, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1282 | 1736329058655 | 2025-01-08 17:37:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 80b44200a1c54f2094c3f86b628e96e9 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-115 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1282 | 1736329058655 | 2025-01-08 17:37:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 80b44200a1c54f2094c3f86b628e96e9 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-115 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1384 | 1736329088646 | 2025-01-08 17:38:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8cee0cab1fb74c0db92a84de1b92feff | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-119 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329068620, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6089, jvmUsedMemory=0.4364, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1227, diskUsed=230.1828, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1384 | 1736329088648 | 2025-01-08 17:38:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8cee0cab1fb74c0db92a84de1b92feff | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-119 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329078622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5215, jvmUsedMemory=0.4393, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1235, diskUsed=230.1828, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1384 | 1736329088648 | 2025-01-08 17:38:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8cee0cab1fb74c0db92a84de1b92feff | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-119 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329088622, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.521, jvmUsedMemory=0.4425, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1244, diskUsed=230.1818, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1384 | 1736329088671 | 2025-01-08 17:38:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8cee0cab1fb74c0db92a84de1b92feff | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-119 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1384 | 1736329088671 | 2025-01-08 17:38:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8cee0cab1fb74c0db92a84de1b92feff | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-119 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1383 | 1736329118653 | 2025-01-08 17:38:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8afc3d56c20d475684e014c0dc8481ef | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-120 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329098623, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.668, jvmUsedMemory=0.4473, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1258, diskUsed=230.1818, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1383 | 1736329118656 | 2025-01-08 17:38:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8afc3d56c20d475684e014c0dc8481ef | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-120 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329108623, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6318, jvmUsedMemory=0.4505, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1267, diskUsed=230.1828, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1383 | 1736329118656 | 2025-01-08 17:38:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8afc3d56c20d475684e014c0dc8481ef | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-120 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329118619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5278, jvmUsedMemory=0.454, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1277, diskUsed=230.1828, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1383 | 1736329118673 | 2025-01-08 17:38:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8afc3d56c20d475684e014c0dc8481ef | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-120 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1383 | 1736329118673 | 2025-01-08 17:38:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8afc3d56c20d475684e014c0dc8481ef | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-120 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1454 | 1736329148647 | 2025-01-08 17:39:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c20e9de657334531a0504410843d1e5d | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-125 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329128616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.2925, jvmUsedMemory=0.4577, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1287, diskUsed=230.1828, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1454 | 1736329148649 | 2025-01-08 17:39:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c20e9de657334531a0504410843d1e5d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-125 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329138613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4683, jvmUsedMemory=0.4607, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1296, diskUsed=230.183, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1454 | 1736329148650 | 2025-01-08 17:39:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c20e9de657334531a0504410843d1e5d | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-125 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329148621, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4761, jvmUsedMemory=0.4638, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1304, diskUsed=230.1821, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1454 | 1736329148666 | 2025-01-08 17:39:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c20e9de657334531a0504410843d1e5d | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-125 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1454 | 1736329148667 | 2025-01-08 17:39:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c20e9de657334531a0504410843d1e5d | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-125 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1453 | 1736329178637 | 2025-01-08 17:39:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f14550771c724578bc7176bb443cbc5e | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329158611, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5562, jvmUsedMemory=0.469, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1319, diskUsed=230.1823, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1453 | 1736329178638 | 2025-01-08 17:39:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f14550771c724578bc7176bb443cbc5e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329168615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4702, jvmUsedMemory=0.4718, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1327, diskUsed=230.1833, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1453 | 1736329178639 | 2025-01-08 17:39:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f14550771c724578bc7176bb443cbc5e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329178615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7046, jvmUsedMemory=0.4754, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1337, diskUsed=230.1833, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1453 | 1736329178653 | 2025-01-08 17:39:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f14550771c724578bc7176bb443cbc5e | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1453 | 1736329178654 | 2025-01-08 17:39:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f14550771c724578bc7176bb443cbc5e | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1736329188896 | 2025-01-08 17:39:48 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 6a3832ed0b174f30a71c8ad7843e006e | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1736329188896 | 2025-01-08 17:39:48 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 5e79359d446f4a539a4f8e67439426d1 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736329188907 | 2025-01-08 17:39:48 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 6a3832ed0b174f30a71c8ad7843e006e | - | - | - | - | 12 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736329188909 | 2025-01-08 17:39:48 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 5e79359d446f4a539a4f8e67439426d1 | - | - | - | - | 13 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 1453 | 1736329188948 | 2025-01-08 17:39:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | f14550771c724578bc7176bb443cbc5e | - | - | - | - | 10310 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 1453 | 1736329188958 | 2025-01-08 17:39:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | Actor | aroundReceive | f14550771c724578bc7176bb443cbc5e | - | - | - | - | 10320 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329188613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2363, jvmUsedMemory=0.4817, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1355, diskUsed=230.1854, diskTotal=460.4317, diskUsage=0.4999, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1453 | 1736329188971 | 2025-01-08 17:39:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | f14550771c724578bc7176bb443cbc5e | - | - | - | - | 10333 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 125 | 1736329370502 | 2025-01-08 17:42:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 2fe99018cd7944108453cbcb1cac6492 | - | - | - | - | 105 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 125 | 1736329370502 | 2025-01-08 17:42:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 2fe99018cd7944108453cbcb1cac6492 | - | - | - | - | 105 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 263 | 1736329391334 | 2025-01-08 17:43:11 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 29fa630a675f463fa887c48051c83d2c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329371215, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1089, jvmUsedMemory=0.4518, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1271, diskUsed=230.2106, diskTotal=460.4317, diskUsage=0.5, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 263 | 1736329391343 | 2025-01-08 17:43:11 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 29fa630a675f463fa887c48051c83d2c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329381219, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9507, jvmUsedMemory=0.603, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1696, diskUsed=230.2106, diskTotal=460.4317, diskUsage=0.5, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 263 | 1736329391344 | 2025-01-08 17:43:11 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 29fa630a675f463fa887c48051c83d2c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329391215, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.644, jvmUsedMemory=0.6095, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1714, diskUsed=230.2097, diskTotal=460.4317, diskUsage=0.5, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 125 | 1736329391391 | 2025-01-08 17:43:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2fe99018cd7944108453cbcb1cac6492 | - | - | - | - | 20994 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 125 | 1736329391391 | 2025-01-08 17:43:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2fe99018cd7944108453cbcb1cac6492 | - | - | - | - | 20994 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 125 | 1736329391395 | 2025-01-08 17:43:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 2fe99018cd7944108453cbcb1cac6492 | - | - | - | - | 20998 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 125 | 1736329391398 | 2025-01-08 17:43:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 2fe99018cd7944108453cbcb1cac6492 | - | - | - | - | 21001 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 125 | 1736329391398 | 2025-01-08 17:43:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 2fe99018cd7944108453cbcb1cac6492 | - | - | - | - | 21001 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 320 | 1736329421253 | 2025-01-08 17:43:41 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 454a1dfdaac544e08dc3ca402b6d65bd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329401215, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.499, jvmUsedMemory=0.6297, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1771, diskUsed=230.2069, diskTotal=460.4317, diskUsage=0.5, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 320 | 1736329421257 | 2025-01-08 17:43:41 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 454a1dfdaac544e08dc3ca402b6d65bd | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329411222, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2837, jvmUsedMemory=0.8128, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2286, diskUsed=230.2081, diskTotal=460.4317, diskUsage=0.5, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 320 | 1736329421259 | 2025-01-08 17:43:41 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 454a1dfdaac544e08dc3ca402b6d65bd | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329421221, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2266, jvmUsedMemory=0.1816, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0511, diskUsed=230.2084, diskTotal=460.4317, diskUsage=0.5, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 320 | 1736329421275 | 2025-01-08 17:43:41 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 454a1dfdaac544e08dc3ca402b6d65bd | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 320 | 1736329421275 | 2025-01-08 17:43:41 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 454a1dfdaac544e08dc3ca402b6d65bd | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 370 | 1736329451246 | 2025-01-08 17:44:11 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3fd9267a0c3a40a1a5dae5da7fff3519 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329431220, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0376, jvmUsedMemory=0.1905, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0536, diskUsed=230.2092, diskTotal=460.4317, diskUsage=0.5, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 370 | 1736329451248 | 2025-01-08 17:44:11 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3fd9267a0c3a40a1a5dae5da7fff3519 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329441216, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.252, jvmUsedMemory=0.201, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0565, diskUsed=230.2151, diskTotal=460.4317, diskUsage=0.5, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 370 | 1736329451249 | 2025-01-08 17:44:11 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3fd9267a0c3a40a1a5dae5da7fff3519 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329451219, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0591, jvmUsedMemory=0.2044, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0575, diskUsed=230.2146, diskTotal=460.4317, diskUsage=0.5, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 370 | 1736329451267 | 2025-01-08 17:44:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3fd9267a0c3a40a1a5dae5da7fff3519 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 370 | 1736329451268 | 2025-01-08 17:44:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3fd9267a0c3a40a1a5dae5da7fff3519 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1736329475031 | 2025-01-08 17:44:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | c40cf24b28a64e6ca89fb37c2736382b | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1736329475031 | 2025-01-08 17:44:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 322c3a9678594084a8e43f69416c1dd0 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736329475033 | 2025-01-08 17:44:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | c40cf24b28a64e6ca89fb37c2736382b | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736329475037 | 2025-01-08 17:44:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 322c3a9678594084a8e43f69416c1dd0 | - | - | - | - | 5 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 369 | 1736329475074 | 2025-01-08 17:44:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | e09b67d7f1924d55b4b297972b11ef4a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 263 | 1736329475084 | 2025-01-08 17:44:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 29fa630a675f463fa887c48051c83d2c | - | - | - | - | 83742 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329461219, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4346, jvmUsedMemory=0.2095, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0589, diskUsed=230.2147, diskTotal=460.4317, diskUsage=0.5, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 263 | 1736329475086 | 2025-01-08 17:44:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 29fa630a675f463fa887c48051c83d2c | - | - | - | - | 83743 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329471217, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9795, jvmUsedMemory=0.2156, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0606, diskUsed=230.2151, diskTotal=460.4317, diskUsage=0.5, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 369 | 1736329475099 | 2025-01-08 17:44:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | e09b67d7f1924d55b4b297972b11ef4a | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1736329502387 | 2025-01-08 17:45:02 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 5a0d6794c5e2483cbe2a9f4804b50fc2 | - | - | - | - | 449 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 127 | 1736329502388 | 2025-01-08 17:45:02 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 5a0d6794c5e2483cbe2a9f4804b50fc2 | - | - | - | - | 449 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 135 | 1736329523794 | 2025-01-08 17:45:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 52777ec77d5847fd8733f5a4135b4638 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329503545, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=8.6362, jvmUsedMemory=0.3159, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0888, diskUsed=230.2167, diskTotal=460.4317, diskUsage=0.5, extra=null, score=5)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 135 | 1736329523802 | 2025-01-08 17:45:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 52777ec77d5847fd8733f5a4135b4638 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329513543, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.4614, jvmUsedMemory=0.4729, jvmMaxMemory=3.5557, jvmMemoryUsage=0.133, diskUsed=230.2129, diskTotal=460.4317, diskUsage=0.5, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 135 | 1736329523803 | 2025-01-08 17:45:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 52777ec77d5847fd8733f5a4135b4638 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329523540, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.5479, jvmUsedMemory=0.7428, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2089, diskUsed=230.2164, diskTotal=460.4317, diskUsage=0.5, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 136 | 1736329523855 | 2025-01-08 17:45:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | febac8317d374cdd82ac892bea8344f9 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-12 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 136 | 1736329523855 | 2025-01-08 17:45:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | febac8317d374cdd82ac892bea8344f9 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-12 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 136 | 1736329523860 | 2025-01-08 17:45:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | febac8317d374cdd82ac892bea8344f9 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-12 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 136 | 1736329523864 | 2025-01-08 17:45:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | febac8317d374cdd82ac892bea8344f9 | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-12 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 136 | 1736329523865 | 2025-01-08 17:45:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | febac8317d374cdd82ac892bea8344f9 | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-12 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 333 | 1736329553569 | 2025-01-08 17:45:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a7b92221810349a89dd68c8a3738039e | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329533540, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.1685, jvmUsedMemory=0.7715, jvmMaxMemory=3.5557, jvmMemoryUsage=0.217, diskUsed=230.2169, diskTotal=460.4317, diskUsage=0.5, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 333 | 1736329553574 | 2025-01-08 17:45:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a7b92221810349a89dd68c8a3738039e | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329543541, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.4785, jvmUsedMemory=0.7808, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2196, diskUsed=230.2175, diskTotal=460.4317, diskUsage=0.5, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 333 | 1736329553574 | 2025-01-08 17:45:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a7b92221810349a89dd68c8a3738039e | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1736329553539, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=9.5596, jvmUsedMemory=0.7914, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2226, diskUsed=231.2207, diskTotal=460.4317, diskUsage=0.5022, extra=null, score=3)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 333 | 1736329553585 | 2025-01-08 17:45:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a7b92221810349a89dd68c8a3738039e | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 333 | 1736329553586 | 2025-01-08 17:45:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a7b92221810349a89dd68c8a3738039e | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1736329555800 | 2025-01-08 17:45:55 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 4c4ab78a6c8842c7b67dfed967c0b643 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1736329555800 | 2025-01-08 17:45:55 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 62936094871e43839e5262dd2a1f8df5 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1736329555801 | 2025-01-08 17:45:55 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 62936094871e43839e5262dd2a1f8df5 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1736329555803 | 2025-01-08 17:45:55 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 4c4ab78a6c8842c7b67dfed967c0b643 | - | - | - | - | 3 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 333 | 1736329555862 | 2025-01-08 17:45:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | a7b92221810349a89dd68c8a3738039e | - | - | - | - | 2288 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

