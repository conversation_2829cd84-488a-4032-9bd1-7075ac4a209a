warning | 123 | 1735199933688 | 2024-12-26 15:58:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 2d4c2a36ab5e4e319fb5b7d77c866703 | - | - | - | - | 117 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 123 | 1735199933688 | 2024-12-26 15:58:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 2d4c2a36ab5e4e319fb5b7d77c866703 | - | - | - | - | 117 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 265 | 1735199954461 | 2024-12-26 15:59:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 16d690dc96484a03a96a60b89c42a0eb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735199934325, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9604, jvmUsedMemory=0.2253, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0634, diskUsed=228.665, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 265 | 1735199954475 | 2024-12-26 15:59:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 16d690dc96484a03a96a60b89c42a0eb | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735199944328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0942, jvmUsedMemory=0.3885, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1093, diskUsed=228.6651, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 265 | 1735199954476 | 2024-12-26 15:59:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 16d690dc96484a03a96a60b89c42a0eb | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735199954324, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6982, jvmUsedMemory=0.3961, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1114, diskUsed=228.6672, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 265 | 1735199954524 | 2024-12-26 15:59:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 16d690dc96484a03a96a60b89c42a0eb | - | - | - | - | 50 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 265 | 1735199954524 | 2024-12-26 15:59:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 16d690dc96484a03a96a60b89c42a0eb | - | - | - | - | 50 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 265 | 1735199954526 | 2024-12-26 15:59:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 16d690dc96484a03a96a60b89c42a0eb | - | - | - | - | 52 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 265 | 1735199954528 | 2024-12-26 15:59:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 16d690dc96484a03a96a60b89c42a0eb | - | - | - | - | 54 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 265 | 1735199954528 | 2024-12-26 15:59:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 16d690dc96484a03a96a60b89c42a0eb | - | - | - | - | 54 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 266 | 1735199984365 | 2024-12-26 15:59:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | eb154396eff04383a72b6c9a5ff111f0 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735199964326, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5103, jvmUsedMemory=0.4158, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1169, diskUsed=228.6672, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 266 | 1735199984367 | 2024-12-26 15:59:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | eb154396eff04383a72b6c9a5ff111f0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735199974324, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4185, jvmUsedMemory=0.4248, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1195, diskUsed=228.6673, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 266 | 1735199984367 | 2024-12-26 15:59:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | eb154396eff04383a72b6c9a5ff111f0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735199984328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0527, jvmUsedMemory=0.4301, jvmMaxMemory=3.5557, jvmMemoryUsage=0.121, diskUsed=228.6694, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 266 | 1735199984388 | 2024-12-26 15:59:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | eb154396eff04383a72b6c9a5ff111f0 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 266 | 1735199984388 | 2024-12-26 15:59:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | eb154396eff04383a72b6c9a5ff111f0 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 353 | 1735200014355 | 2024-12-26 16:00:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | d10fb238ab0946678a273dfb56a23fab | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735199994327, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.583, jvmUsedMemory=0.4397, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1237, diskUsed=228.6694, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 353 | 1735200014356 | 2024-12-26 16:00:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | d10fb238ab0946678a273dfb56a23fab | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200004328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1338, jvmUsedMemory=0.4522, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1272, diskUsed=228.6693, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 353 | 1735200014357 | 2024-12-26 16:00:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | d10fb238ab0946678a273dfb56a23fab | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200014327, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0264, jvmUsedMemory=0.4584, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1289, diskUsed=228.6702, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 353 | 1735200014377 | 2024-12-26 16:00:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | d10fb238ab0946678a273dfb56a23fab | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 353 | 1735200014377 | 2024-12-26 16:00:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | d10fb238ab0946678a273dfb56a23fab | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 392 | 1735200044366 | 2024-12-26 16:00:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 07260e584b654870ba73f286188f21f0 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200024325, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.3765, jvmUsedMemory=0.467, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1313, diskUsed=228.6703, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1735200044369 | 2024-12-26 16:00:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 07260e584b654870ba73f286188f21f0 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200034327, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.3052, jvmUsedMemory=0.4755, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1337, diskUsed=228.6693, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1735200044370 | 2024-12-26 16:00:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 07260e584b654870ba73f286188f21f0 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200044325, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.79, jvmUsedMemory=0.4812, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1353, diskUsed=228.6703, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 352 | 1735200044392 | 2024-12-26 16:00:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 47ea4b2a6aee4ee99a11cf4b44f86502 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 352 | 1735200044393 | 2024-12-26 16:00:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 47ea4b2a6aee4ee99a11cf4b44f86502 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 392 | 1735200074345 | 2024-12-26 16:01:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 07260e584b654870ba73f286188f21f0 | - | - | - | - | 29979 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200054327, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7544, jvmUsedMemory=0.5069, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1425, diskUsed=228.6703, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1735200074348 | 2024-12-26 16:01:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 07260e584b654870ba73f286188f21f0 | - | - | - | - | 29983 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200064324, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.478, jvmUsedMemory=0.514, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1446, diskUsed=228.6705, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1735200074364 | 2024-12-26 16:01:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 07260e584b654870ba73f286188f21f0 | - | - | - | - | 29997 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200074327, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=14.4478, jvmUsedMemory=0.5201, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1463, diskUsed=228.6705, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=0)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1735200074375 | 2024-12-26 16:01:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 07260e584b654870ba73f286188f21f0 | - | - | - | - | 30008 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 392 | 1735200074375 | 2024-12-26 16:01:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 07260e584b654870ba73f286188f21f0 | - | - | - | - | 30008 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 431 | 1735200104353 | 2024-12-26 16:01:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6c289e37617c4f0a8cd574453708f6c0 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200084325, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=12.3862, jvmUsedMemory=0.5303, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1491, diskUsed=228.6705, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=1)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1735200104355 | 2024-12-26 16:01:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6c289e37617c4f0a8cd574453708f6c0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200094326, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=10.8691, jvmUsedMemory=0.5365, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1509, diskUsed=228.6706, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=3)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1735200104356 | 2024-12-26 16:01:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6c289e37617c4f0a8cd574453708f6c0 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200104328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=9.2715, jvmUsedMemory=0.5421, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1525, diskUsed=228.6679, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=4)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1735200104380 | 2024-12-26 16:01:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 6c289e37617c4f0a8cd574453708f6c0 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 431 | 1735200104381 | 2024-12-26 16:01:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 6c289e37617c4f0a8cd574453708f6c0 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 470 | 1735200134363 | 2024-12-26 16:02:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e029a85b9bd0415188d023fe027a668c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200114335, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.8457, jvmUsedMemory=0.5508, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1549, diskUsed=228.6652, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 470 | 1735200134364 | 2024-12-26 16:02:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e029a85b9bd0415188d023fe027a668c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200124328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.8667, jvmUsedMemory=0.558, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1569, diskUsed=228.6653, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 470 | 1735200134364 | 2024-12-26 16:02:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e029a85b9bd0415188d023fe027a668c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200134328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.8906, jvmUsedMemory=0.5613, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1579, diskUsed=228.6649, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 470 | 1735200134377 | 2024-12-26 16:02:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | e029a85b9bd0415188d023fe027a668c | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 470 | 1735200134378 | 2024-12-26 16:02:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | e029a85b9bd0415188d023fe027a668c | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 171 | 1735200151733 | 2024-12-26 16:02:31 | v2/manageMultiLive/roomData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | roomData | 53fa615462954a48acdc1a69cca9cdc9 | - | - | - | - | 251 | 0 | - | - | - | - | http-nio-8087-exec-5 c.t.g.c.w.SpringExceptionHandler 出错了啊！请骚候再试[j01000]
java.lang.NullPointerException: null
	at cn.taqu.gonghui.system.service.multilive.impl.MultiLiveStatsServiceImpl.buildParamsToRequestRoomData(MultiLiveStatsServiceImpl.java:391)
	at cn.taqu.gonghui.system.service.multilive.impl.MultiLiveStatsServiceImpl.searchAdminRoomData(MultiLiveStatsServiceImpl.java:433)
	at cn.taqu.gonghui.live.controller.ManageMultiLiveController.multiRecreationRoomData(ManageMultiLiveController.java:132)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:54)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:214)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.taqu.core.web.filter.SoaFilter.doFilterInternal(SoaFilter.java:38)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.taqu.core.web.filter.healthy.HealthyFilter.doFilterInternal(HealthyFilter.java:45)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

java.lang.NullPointerException: null
	at cn.taqu.gonghui.system.service.multilive.impl.MultiLiveStatsServiceImpl.buildParamsToRequestRoomData(MultiLiveStatsServiceImpl.java:391)
	at cn.taqu.gonghui.system.service.multilive.impl.MultiLiveStatsServiceImpl.searchAdminRoomData(MultiLiveStatsServiceImpl.java:433)
	at cn.taqu.gonghui.live.controller.ManageMultiLiveController.multiRecreationRoomData(ManageMultiLiveController.java:132)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:54)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:214)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.taqu.core.web.filter.SoaFilter.doFilterInternal(SoaFilter.java:38)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.taqu.core.web.filter.healthy.HealthyFilter.doFilterInternal(HealthyFilter.java:45)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
warning | 545 | 1735200164348 | 2024-12-26 16:02:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 5a3755f428a046588f9d133898708327 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200144328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.2119, jvmUsedMemory=0.5689, jvmMaxMemory=3.5557, jvmMemoryUsage=0.16, diskUsed=228.665, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 545 | 1735200164349 | 2024-12-26 16:02:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 5a3755f428a046588f9d133898708327 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200154328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.564, jvmUsedMemory=0.6034, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1697, diskUsed=228.6641, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 545 | 1735200164350 | 2024-12-26 16:02:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 5a3755f428a046588f9d133898708327 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200164328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9419, jvmUsedMemory=0.6098, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1715, diskUsed=228.6654, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 545 | 1735200164359 | 2024-12-26 16:02:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 5a3755f428a046588f9d133898708327 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 545 | 1735200164359 | 2024-12-26 16:02:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 5a3755f428a046588f9d133898708327 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 508 | 1735200194365 | 2024-12-26 16:03:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 28131bce64374c1f8c00497cc506d403 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200174329, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4893, jvmUsedMemory=0.6167, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1734, diskUsed=228.6655, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 508 | 1735200194367 | 2024-12-26 16:03:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 28131bce64374c1f8c00497cc506d403 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200184329, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4199, jvmUsedMemory=0.625, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1758, diskUsed=228.6666, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 508 | 1735200194369 | 2024-12-26 16:03:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 28131bce64374c1f8c00497cc506d403 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200194328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3486, jvmUsedMemory=0.6312, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1775, diskUsed=228.6687, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 508 | 1735200194401 | 2024-12-26 16:03:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 28131bce64374c1f8c00497cc506d403 | - | - | - | - | 35 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 508 | 1735200194402 | 2024-12-26 16:03:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 28131bce64374c1f8c00497cc506d403 | - | - | - | - | 36 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 585 | 1735200224354 | 2024-12-26 16:03:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 27719efb878946e8a4af5de89de4df9f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200204329, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0605, jvmUsedMemory=0.6376, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1793, diskUsed=228.6697, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 585 | 1735200224356 | 2024-12-26 16:03:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 27719efb878946e8a4af5de89de4df9f | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200214327, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1592, jvmUsedMemory=0.6445, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1812, diskUsed=228.6688, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 585 | 1735200224356 | 2024-12-26 16:03:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 27719efb878946e8a4af5de89de4df9f | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200224328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9673, jvmUsedMemory=0.6658, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1873, diskUsed=228.6652, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 585 | 1735200224376 | 2024-12-26 16:03:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 27719efb878946e8a4af5de89de4df9f | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 585 | 1735200224377 | 2024-12-26 16:03:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 27719efb878946e8a4af5de89de4df9f | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 629 | 1735200254357 | 2024-12-26 16:04:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 857176c4cf8145719d2fc37ac160db52 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200234329, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5107, jvmUsedMemory=0.6767, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1903, diskUsed=228.6688, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 629 | 1735200254360 | 2024-12-26 16:04:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 857176c4cf8145719d2fc37ac160db52 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200244329, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7456, jvmUsedMemory=0.681, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1915, diskUsed=228.6689, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 629 | 1735200254360 | 2024-12-26 16:04:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 857176c4cf8145719d2fc37ac160db52 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200254329, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8579, jvmUsedMemory=0.6862, jvmMaxMemory=3.5557, jvmMemoryUsage=0.193, diskUsed=228.6689, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 629 | 1735200254384 | 2024-12-26 16:04:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 857176c4cf8145719d2fc37ac160db52 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 629 | 1735200254385 | 2024-12-26 16:04:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 857176c4cf8145719d2fc37ac160db52 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 668 | 1735200284356 | 2024-12-26 16:04:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0560feb56658490a8659b0de19bd73eb | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200264328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3442, jvmUsedMemory=0.6944, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1953, diskUsed=228.6699, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 668 | 1735200284357 | 2024-12-26 16:04:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0560feb56658490a8659b0de19bd73eb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200274327, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2173, jvmUsedMemory=0.7006, jvmMaxMemory=3.5557, jvmMemoryUsage=0.197, diskUsed=228.6687, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 668 | 1735200284358 | 2024-12-26 16:04:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0560feb56658490a8659b0de19bd73eb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200284329, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.876, jvmUsedMemory=0.7065, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1987, diskUsed=228.669, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 668 | 1735200284371 | 2024-12-26 16:04:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0560feb56658490a8659b0de19bd73eb | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 668 | 1735200284372 | 2024-12-26 16:04:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0560feb56658490a8659b0de19bd73eb | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 702 | 1735200314367 | 2024-12-26 16:05:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3f27c0997ddc44b2bd8af4fd3373c1c2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200294328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6675, jvmUsedMemory=0.7136, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2007, diskUsed=228.6689, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 702 | 1735200314369 | 2024-12-26 16:05:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3f27c0997ddc44b2bd8af4fd3373c1c2 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200304328, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5093, jvmUsedMemory=0.721, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2028, diskUsed=228.6722, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 702 | 1735200314370 | 2024-12-26 16:05:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3f27c0997ddc44b2bd8af4fd3373c1c2 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200314329, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1968, jvmUsedMemory=0.7258, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2041, diskUsed=228.6723, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 702 | 1735200314397 | 2024-12-26 16:05:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3f27c0997ddc44b2bd8af4fd3373c1c2 | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 702 | 1735200314398 | 2024-12-26 16:05:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3f27c0997ddc44b2bd8af4fd3373c1c2 | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 737 | 1735200344358 | 2024-12-26 16:05:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8583de90d2b747da9d77c145709f7095 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200324327, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8647, jvmUsedMemory=0.7329, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2061, diskUsed=228.6724, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 737 | 1735200344361 | 2024-12-26 16:05:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8583de90d2b747da9d77c145709f7095 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200334329, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8916, jvmUsedMemory=0.7393, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2079, diskUsed=228.6716, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 737 | 1735200344361 | 2024-12-26 16:05:44 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8583de90d2b747da9d77c145709f7095 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200344327, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6802, jvmUsedMemory=0.7459, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2098, diskUsed=228.6749, diskTotal=460.4317, diskUsage=0.4967, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 737 | 1735200344394 | 2024-12-26 16:05:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8583de90d2b747da9d77c145709f7095 | - | - | - | - | 36 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 737 | 1735200344395 | 2024-12-26 16:05:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8583de90d2b747da9d77c145709f7095 | - | - | - | - | 36 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 771 | 1735200374356 | 2024-12-26 16:06:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7c67b7e6c3f84158ad202ff53d06b472 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200354325, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5264, jvmUsedMemory=0.754, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2121, diskUsed=228.675, diskTotal=460.4317, diskUsage=0.4967, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 771 | 1735200374359 | 2024-12-26 16:06:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7c67b7e6c3f84158ad202ff53d06b472 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200364329, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6978, jvmUsedMemory=0.7612, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2141, diskUsed=228.6742, diskTotal=460.4317, diskUsage=0.4967, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 771 | 1735200374359 | 2024-12-26 16:06:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7c67b7e6c3f84158ad202ff53d06b472 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200374325, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=10.8701, jvmUsedMemory=0.7688, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2162, diskUsed=228.6754, diskTotal=460.4317, diskUsage=0.4967, extra=null, score=2)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 771 | 1735200374380 | 2024-12-26 16:06:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 7c67b7e6c3f84158ad202ff53d06b472 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 771 | 1735200374380 | 2024-12-26 16:06:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 7c67b7e6c3f84158ad202ff53d06b472 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1735200385422 | 2024-12-26 16:06:25 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | fdf9d9a011b64a2d9f9049e499a64bdc | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1735200385421 | 2024-12-26 16:06:25 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | c4a4f410a8b644c8a07d30c1ce97bfe0 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1735200385423 | 2024-12-26 16:06:25 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | fdf9d9a011b64a2d9f9049e499a64bdc | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1735200385431 | 2024-12-26 16:06:25 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | c4a4f410a8b644c8a07d30c1ce97bfe0 | - | - | - | - | 9 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 771 | 1735200385488 | 2024-12-26 16:06:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 7c67b7e6c3f84158ad202ff53d06b472 | - | - | - | - | 11132 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 772 | 1735200385523 | 2024-12-26 16:06:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | d3e4277942a04712a81a784d4fa937c0 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200384326, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=9.2788, jvmUsedMemory=0.7754, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2181, diskUsed=228.6744, diskTotal=460.4317, diskUsage=0.4967, extra=null, score=4)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 124 | 1735200413253 | 2024-12-26 16:06:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 09e117c86cd847188c6015a62a2f06e3 | - | - | - | - | 101 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 124 | 1735200413254 | 2024-12-26 16:06:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 09e117c86cd847188c6015a62a2f06e3 | - | - | - | - | 101 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 274 | 1735200434095 | 2024-12-26 16:07:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3fb4c9dd1c9f487ab3134990d0b6dd1f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200413950, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.5547, jvmUsedMemory=0.4517, jvmMaxMemory=3.5557, jvmMemoryUsage=0.127, diskUsed=228.6347, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 274 | 1735200434106 | 2024-12-26 16:07:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3fb4c9dd1c9f487ab3134990d0b6dd1f | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200423951, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.7134, jvmUsedMemory=0.6294, jvmMaxMemory=3.5557, jvmMemoryUsage=0.177, diskUsed=228.6349, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 274 | 1735200434107 | 2024-12-26 16:07:14 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3fb4c9dd1c9f487ab3134990d0b6dd1f | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200433953, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.6812, jvmUsedMemory=0.636, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1789, diskUsed=228.6369, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 124 | 1735200434167 | 2024-12-26 16:07:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 09e117c86cd847188c6015a62a2f06e3 | - | - | - | - | 21015 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1735200434168 | 2024-12-26 16:07:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 09e117c86cd847188c6015a62a2f06e3 | - | - | - | - | 21015 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1735200434171 | 2024-12-26 16:07:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 09e117c86cd847188c6015a62a2f06e3 | - | - | - | - | 21018 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1735200434178 | 2024-12-26 16:07:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 09e117c86cd847188c6015a62a2f06e3 | - | - | - | - | 21025 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1735200434178 | 2024-12-26 16:07:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 09e117c86cd847188c6015a62a2f06e3 | - | - | - | - | 21025 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 275 | 1735200463994 | 2024-12-26 16:07:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3b106fefebaf484a92e2a12455ba435f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200443953, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.0283, jvmUsedMemory=0.6547, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1841, diskUsed=228.6372, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 275 | 1735200463995 | 2024-12-26 16:07:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3b106fefebaf484a92e2a12455ba435f | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200453951, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.7227, jvmUsedMemory=0.9098, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2559, diskUsed=228.6377, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 275 | 1735200463996 | 2024-12-26 16:07:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3b106fefebaf484a92e2a12455ba435f | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200463952, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5376, jvmUsedMemory=0.9147, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2572, diskUsed=228.6391, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 275 | 1735200464017 | 2024-12-26 16:07:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3b106fefebaf484a92e2a12455ba435f | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 275 | 1735200464018 | 2024-12-26 16:07:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3b106fefebaf484a92e2a12455ba435f | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 319 | 1735200493984 | 2024-12-26 16:08:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3e83fcd9dcc840fb9dfe80bef46bfa88 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200473952, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4414, jvmUsedMemory=0.9251, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2602, diskUsed=229.6418, diskTotal=460.4317, diskUsage=0.4988, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 319 | 1735200493985 | 2024-12-26 16:08:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3e83fcd9dcc840fb9dfe80bef46bfa88 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200483954, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9854, jvmUsedMemory=0.934, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2627, diskUsed=229.6365, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 319 | 1735200493986 | 2024-12-26 16:08:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3e83fcd9dcc840fb9dfe80bef46bfa88 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200493951, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5322, jvmUsedMemory=0.9393, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2642, diskUsed=229.6341, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 319 | 1735200494002 | 2024-12-26 16:08:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3e83fcd9dcc840fb9dfe80bef46bfa88 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 319 | 1735200494003 | 2024-12-26 16:08:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3e83fcd9dcc840fb9dfe80bef46bfa88 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 404 | 1735200523982 | 2024-12-26 16:08:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6236cd2ff2274032911a5af2c05ba601 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200503951, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2158, jvmUsedMemory=0.9473, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2664, diskUsed=229.6352, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 404 | 1735200523983 | 2024-12-26 16:08:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6236cd2ff2274032911a5af2c05ba601 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200513954, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0283, jvmUsedMemory=0.9553, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2687, diskUsed=229.6342, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 404 | 1735200523983 | 2024-12-26 16:08:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6236cd2ff2274032911a5af2c05ba601 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200523949, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6362, jvmUsedMemory=0.9601, jvmMaxMemory=3.5557, jvmMemoryUsage=0.27, diskUsed=229.6342, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 364 | 1735200523995 | 2024-12-26 16:08:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | abb520695cb741dabea900c3f25f386f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 364 | 1735200523995 | 2024-12-26 16:08:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | abb520695cb741dabea900c3f25f386f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 405 | 1735200553983 | 2024-12-26 16:09:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f62e9d7f523c4b23aa17d99161e380f3 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200533954, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4253, jvmUsedMemory=0.9693, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2726, diskUsed=229.6342, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 405 | 1735200553984 | 2024-12-26 16:09:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f62e9d7f523c4b23aa17d99161e380f3 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200543953, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9263, jvmUsedMemory=0.9764, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2746, diskUsed=229.6342, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 405 | 1735200553984 | 2024-12-26 16:09:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f62e9d7f523c4b23aa17d99161e380f3 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200553951, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.396, jvmUsedMemory=0.1623, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0456, diskUsed=229.6352, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 405 | 1735200553994 | 2024-12-26 16:09:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f62e9d7f523c4b23aa17d99161e380f3 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 405 | 1735200553995 | 2024-12-26 16:09:13 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f62e9d7f523c4b23aa17d99161e380f3 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 443 | 1735200583974 | 2024-12-26 16:09:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 5e29727f4f424343997f5b5c9f394e80 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200563951, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9473, jvmUsedMemory=0.1756, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0494, diskUsed=229.6353, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 443 | 1735200583982 | 2024-12-26 16:09:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 5e29727f4f424343997f5b5c9f394e80 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200573954, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1821, jvmUsedMemory=0.1827, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0514, diskUsed=229.6349, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 443 | 1735200583983 | 2024-12-26 16:09:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 5e29727f4f424343997f5b5c9f394e80 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200583952, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2402, jvmUsedMemory=0.1855, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0522, diskUsed=229.6362, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 443 | 1735200583997 | 2024-12-26 16:09:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 5e29727f4f424343997f5b5c9f394e80 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 443 | 1735200583997 | 2024-12-26 16:09:43 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 5e29727f4f424343997f5b5c9f394e80 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 485 | 1735200613975 | 2024-12-26 16:10:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7e2f4aeea9bd445ab0c30d21bd9c384d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200593950, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0488, jvmUsedMemory=0.1919, jvmMaxMemory=3.5557, jvmMemoryUsage=0.054, diskUsed=229.6374, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 485 | 1735200613979 | 2024-12-26 16:10:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7e2f4aeea9bd445ab0c30d21bd9c384d | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200603954, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6597, jvmUsedMemory=0.1963, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0552, diskUsed=229.6378, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 485 | 1735200613979 | 2024-12-26 16:10:13 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7e2f4aeea9bd445ab0c30d21bd9c384d | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200613955, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3301, jvmUsedMemory=0.1993, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0561, diskUsed=229.6384, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 485 | 1735200614001 | 2024-12-26 16:10:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 7e2f4aeea9bd445ab0c30d21bd9c384d | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 485 | 1735200614002 | 2024-12-26 16:10:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 7e2f4aeea9bd445ab0c30d21bd9c384d | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 483 | 1735200643993 | 2024-12-26 16:10:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ad4eb44714db44238ad1f6835c4489a4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200623955, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0449, jvmUsedMemory=0.2048, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0576, diskUsed=229.6384, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 483 | 1735200643997 | 2024-12-26 16:10:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ad4eb44714db44238ad1f6835c4489a4 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200633952, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3516, jvmUsedMemory=0.2093, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0589, diskUsed=229.6349, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 483 | 1735200643998 | 2024-12-26 16:10:43 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ad4eb44714db44238ad1f6835c4489a4 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200643954, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2104, jvmUsedMemory=0.2116, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0595, diskUsed=229.636, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 483 | 1735200644012 | 2024-12-26 16:10:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | ad4eb44714db44238ad1f6835c4489a4 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 483 | 1735200644012 | 2024-12-26 16:10:44 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | ad4eb44714db44238ad1f6835c4489a4 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1735200662247 | 2024-12-26 16:11:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 953555165d4f418780df0dbfae6b9c60 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1735200662247 | 2024-12-26 16:11:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 49f2dc98c2e0498abf2596dedece5dac | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1735200662250 | 2024-12-26 16:11:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 953555165d4f418780df0dbfae6b9c60 | - | - | - | - | 4 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1735200662256 | 2024-12-26 16:11:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 49f2dc98c2e0498abf2596dedece5dac | - | - | - | - | 10 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 558 | 1735200662311 | 2024-12-26 16:11:02 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 2d30ac82777842a39a340338726da2fc | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 524 | 1735200662322 | 2024-12-26 16:11:02 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 83785203b0af41639b595baa195af041 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200653952, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0303, jvmUsedMemory=0.2163, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0608, diskUsed=229.6373, diskTotal=460.4317, diskUsage=0.4987, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 582 | 1735200662335 | 2024-12-26 16:11:02 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 87e2a6e87f3542f3b6bb3a5483565712 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1735200690746 | 2024-12-26 16:11:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 11052cc7aa094ecfb3fa922d60bb478d | - | - | - | - | 111 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 124 | 1735200690747 | 2024-12-26 16:11:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 11052cc7aa094ecfb3fa922d60bb478d | - | - | - | - | 111 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 275 | 1735200711538 | 2024-12-26 16:11:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ca8f59b8acc8461f9b833866a174ef5f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200691391, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8354, jvmUsedMemory=0.4107, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1155, diskUsed=228.637, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 275 | 1735200711547 | 2024-12-26 16:11:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ca8f59b8acc8461f9b833866a174ef5f | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200701394, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.3501, jvmUsedMemory=0.6009, jvmMaxMemory=3.5557, jvmMemoryUsage=0.169, diskUsed=228.6362, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 275 | 1735200711552 | 2024-12-26 16:11:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ca8f59b8acc8461f9b833866a174ef5f | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200711392, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.001, jvmUsedMemory=0.6097, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1715, diskUsed=228.6267, diskTotal=460.4317, diskUsage=0.4965, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 124 | 1735200711604 | 2024-12-26 16:11:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 11052cc7aa094ecfb3fa922d60bb478d | - | - | - | - | 20968 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1735200711604 | 2024-12-26 16:11:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 11052cc7aa094ecfb3fa922d60bb478d | - | - | - | - | 20969 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 124 | 1735200711610 | 2024-12-26 16:11:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 11052cc7aa094ecfb3fa922d60bb478d | - | - | - | - | 20974 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1735200711614 | 2024-12-26 16:11:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 11052cc7aa094ecfb3fa922d60bb478d | - | - | - | - | 20978 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 124 | 1735200711614 | 2024-12-26 16:11:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 11052cc7aa094ecfb3fa922d60bb478d | - | - | - | - | 20978 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 316 | 1735200741419 | 2024-12-26 16:12:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0dceb51c77c04523877304c4599c7f87 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200721394, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4653, jvmUsedMemory=0.6306, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1774, diskUsed=228.628, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 316 | 1735200741421 | 2024-12-26 16:12:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0dceb51c77c04523877304c4599c7f87 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200731393, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8354, jvmUsedMemory=0.6367, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1791, diskUsed=228.6301, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 316 | 1735200741421 | 2024-12-26 16:12:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0dceb51c77c04523877304c4599c7f87 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735200741394, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5264, jvmUsedMemory=0.6427, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1808, diskUsed=228.6302, diskTotal=460.4317, diskUsage=0.4966, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 316 | 1735200741441 | 2024-12-26 16:12:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0dceb51c77c04523877304c4599c7f87 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 316 | 1735200741442 | 2024-12-26 16:12:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0dceb51c77c04523877304c4599c7f87 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1735200742393 | 2024-12-26 16:12:22 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 0bfdf3cb47e94f7584dbd7c2bfebe9b1 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1735200742393 | 2024-12-26 16:12:22 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 0bfdf3cb47e94f7584dbd7c2bfebe9b1 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1735200742393 | 2024-12-26 16:12:22 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | fb9aebe7bf3f4b23adf6573eb7d0dd2e | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 40 | 1735200742404 | 2024-12-26 16:12:22 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | fb9aebe7bf3f4b23adf6573eb7d0dd2e | - | - | - | - | 11 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 316 | 1735200742429 | 2024-12-26 16:12:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 0dceb51c77c04523877304c4599c7f87 | - | - | - | - | 1010 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

