error | 253 | 1740538274498 | 2025-02-26 10:51:14 | v2/SpringExceptionHandler/unsatisfiedServletRequestParameterException | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | 192.168.120.91 | - | 2 | SpringExceptionHandler | unsatisfiedServletRequestParameterException | 8fc1f30ec6c64b149c5489657350391a | - | - | - | - | 4208 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.w.SpringExceptionHandler SpringExceptionHandler.java:142 sevice=managerRoom&method=adminReportingRoom 错误码[405]:出错了啊！请骚候再试[j01002]

error | 254 | 1740538283396 | 2025-02-26 10:51:23 | v2/SpringExceptionHandler/unsatisfiedServletRequestParameterException | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | 192.168.120.91 | - | 2 | SpringExceptionHandler | unsatisfiedServletRequestParameterException | a72abc3ae519450881a1e2993afd6f45 | - | - | - | - | 308 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.c.w.SpringExceptionHandler SpringExceptionHandler.java:142 sevice=managerRoom&method=adminReportingRoom 错误码[405]:出错了啊！请骚候再试[j01002]

error | 266 | 1740538962124 | 2025-02-26 11:02:42 | v2/managerRoom/adminReportingRoom | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | 192.168.120.91 | - | 2 | managerRoom | adminReportingRoom | 99999999999 | - | - | - | - | 2537 | 0 | - | - | - | - | http-nio-8087-exec-15 c.t.g.c.s.DataManager DataManager.java:84 Request API error: api:/backendTechnology/chatroom/adminReportingRoom, params:{"dt":["20250219","20250225"]}, datePageRequest:{"page":1,"pageSize":20}, sortMap:{"dt":"DESC","chat_room_uuid":"asc"}, 范围条件过滤，如日期范围等，过滤条件的值应为二元组，如：[2022-03-01, null]，即使空值也要传 null，以保证对应位置是起始值或是截止值

error | 1 | 1740548940465 | 2025-02-26 13:49:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.641 | 192.168.120.91 | - | 2 | Application | main | 523f05d05ee1464ca5c135abe5794a24 | - | - | - | - | 115915 | 0 | - | - | - | - | main c.t.g.c.c.EncryptDecryptClient EncryptDecryptClient.java:124 decrypt请求失败,encryptedContent=sm3yWaNeVw2bimTjx7GySwFIJyjrjPK6fH9I16U55rKltCLnXaQBeMPBixy2SuO7MRYXa8yGwsX6rgr4SB0OMKAYFb9jR8fArYZxa1wyvvfwtlj0WnhLAflYxKsoaResponse={"code":"decryptErr","dataBlank":true,"dataNotBlank":false,"msg":"illegal base64 data at input byte 120"}

error | 1 | 1740549201102 | 2025-02-26 13:53:21 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | 192.168.120.91 | - | 2 | Application | main | fd14949d72cf43049b0116af2350a2d9 | - | - | - | - | 119586 | 0 | - | - | - | - | main c.t.g.c.c.EncryptDecryptClient EncryptDecryptClient.java:124 decrypt请求失败,encryptedContent=sm3yWaNeVw2bimTjx7GySwFIJyjrjPK6fH9I16U55rKltCLnXaQBeMPBixy2SuO7MRYXa8yGwsX6rgr4SB0OMKAYFb9jR8fArYZxa1wyvvfwtlj0WnhLAflYxKsoaResponse={"code":"decryptErr","dataBlank":true,"dataNotBlank":false,"msg":"illegal base64 data at input byte 120"}

