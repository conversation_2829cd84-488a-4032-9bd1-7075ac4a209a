warning | 123 | 1735614567947 | 2024-12-31 11:09:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 68bf0498acc9462283106139fd2a3663 | - | - | - | - | 169 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 123 | 1735614567948 | 2024-12-31 11:09:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 68bf0498acc9462283106139fd2a3663 | - | - | - | - | 169 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 277 | 1735614588832 | 2024-12-31 11:09:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e962540af51c425e827b7abe6990e1b7 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614568632, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.3423, jvmUsedMemory=1.2715, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3576, diskUsed=228.4125, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 277 | 1735614588842 | 2024-12-31 11:09:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e962540af51c425e827b7abe6990e1b7 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614578634, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.209, jvmUsedMemory=0.3461, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0973, diskUsed=228.4116, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 277 | 1735614588842 | 2024-12-31 11:09:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e962540af51c425e827b7abe6990e1b7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614588635, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5679, jvmUsedMemory=0.3606, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1014, diskUsed=228.4137, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 123 | 1735614588898 | 2024-12-31 11:09:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 68bf0498acc9462283106139fd2a3663 | - | - | - | - | 21119 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1735614588898 | 2024-12-31 11:09:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 68bf0498acc9462283106139fd2a3663 | - | - | - | - | 21119 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1735614588903 | 2024-12-31 11:09:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 68bf0498acc9462283106139fd2a3663 | - | - | - | - | 21124 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1735614588905 | 2024-12-31 11:09:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 68bf0498acc9462283106139fd2a3663 | - | - | - | - | 21126 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1735614588905 | 2024-12-31 11:09:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 68bf0498acc9462283106139fd2a3663 | - | - | - | - | 21127 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 319 | 1735614618661 | 2024-12-31 11:10:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 876c368265494c55b7238ea863b33602 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614598635, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4927, jvmUsedMemory=0.3781, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1063, diskUsed=228.4139, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 319 | 1735614618662 | 2024-12-31 11:10:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 876c368265494c55b7238ea863b33602 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614608635, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9556, jvmUsedMemory=0.3907, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1099, diskUsed=228.4138, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 319 | 1735614618663 | 2024-12-31 11:10:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 876c368265494c55b7238ea863b33602 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614618635, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6675, jvmUsedMemory=0.3958, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1113, diskUsed=228.4138, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 319 | 1735614618681 | 2024-12-31 11:10:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 876c368265494c55b7238ea863b33602 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 319 | 1735614618682 | 2024-12-31 11:10:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 876c368265494c55b7238ea863b33602 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 277 | 1735614648661 | 2024-12-31 11:10:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | e962540af51c425e827b7abe6990e1b7 | - | - | - | - | 59821 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614628637, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3242, jvmUsedMemory=0.4053, jvmMaxMemory=3.5557, jvmMemoryUsage=0.114, diskUsed=228.4139, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 277 | 1735614648662 | 2024-12-31 11:10:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | e962540af51c425e827b7abe6990e1b7 | - | - | - | - | 59821 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614638635, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5142, jvmUsedMemory=0.4142, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1165, diskUsed=228.413, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 277 | 1735614648662 | 2024-12-31 11:10:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | e962540af51c425e827b7abe6990e1b7 | - | - | - | - | 59821 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614648637, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.207, jvmUsedMemory=0.4214, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1185, diskUsed=228.4149, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 277 | 1735614648670 | 2024-12-31 11:10:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | e962540af51c425e827b7abe6990e1b7 | - | - | - | - | 59829 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 277 | 1735614648670 | 2024-12-31 11:10:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | e962540af51c425e827b7abe6990e1b7 | - | - | - | - | 59829 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 364 | 1735614678663 | 2024-12-31 11:11:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f7bd03c2d502499d81b9a3ca900c48fb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614658637, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7876, jvmUsedMemory=0.4306, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1211, diskUsed=228.415, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 364 | 1735614678664 | 2024-12-31 11:11:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f7bd03c2d502499d81b9a3ca900c48fb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614668638, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3584, jvmUsedMemory=0.4395, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1236, diskUsed=228.4152, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 364 | 1735614678664 | 2024-12-31 11:11:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f7bd03c2d502499d81b9a3ca900c48fb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614678634, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0693, jvmUsedMemory=0.4456, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1253, diskUsed=228.4154, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 365 | 1735614678673 | 2024-12-31 11:11:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 6b56305f97d2428a9e9d385439f16684 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 365 | 1735614678674 | 2024-12-31 11:11:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 6b56305f97d2428a9e9d385439f16684 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 409 | 1735614708665 | 2024-12-31 11:11:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 2ba08166e3d24b43b88ecc84b5d60c70 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614688637, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8242, jvmUsedMemory=0.4537, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1276, diskUsed=228.4127, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 409 | 1735614708666 | 2024-12-31 11:11:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 2ba08166e3d24b43b88ecc84b5d60c70 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614698637, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.8809, jvmUsedMemory=0.4602, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1294, diskUsed=228.4124, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 409 | 1735614708666 | 2024-12-31 11:11:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 2ba08166e3d24b43b88ecc84b5d60c70 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614708636, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4375, jvmUsedMemory=0.4649, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1307, diskUsed=228.4136, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 409 | 1735614708678 | 2024-12-31 11:11:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2ba08166e3d24b43b88ecc84b5d60c70 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 409 | 1735614708679 | 2024-12-31 11:11:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2ba08166e3d24b43b88ecc84b5d60c70 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 482 | 1735614738654 | 2024-12-31 11:12:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 4e2b9a259700454684cf017c4c552a55 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614718637, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.8652, jvmUsedMemory=0.472, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1328, diskUsed=228.4137, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 482 | 1735614738657 | 2024-12-31 11:12:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 4e2b9a259700454684cf017c4c552a55 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614728639, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.6328, jvmUsedMemory=0.4781, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1345, diskUsed=228.4166, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 482 | 1735614738657 | 2024-12-31 11:12:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 4e2b9a259700454684cf017c4c552a55 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614738640, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.7666, jvmUsedMemory=0.4833, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1359, diskUsed=228.4166, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 482 | 1735614738673 | 2024-12-31 11:12:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 4e2b9a259700454684cf017c4c552a55 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 482 | 1735614738674 | 2024-12-31 11:12:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 4e2b9a259700454684cf017c4c552a55 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 446 | 1735614768665 | 2024-12-31 11:12:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 2f0c330646b94180b25cbd09b074e9dd | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614748640, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.2803, jvmUsedMemory=0.4934, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1388, diskUsed=228.4156, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 446 | 1735614768667 | 2024-12-31 11:12:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 2f0c330646b94180b25cbd09b074e9dd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614758637, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4683, jvmUsedMemory=0.4999, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1406, diskUsed=228.4146, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 446 | 1735614768667 | 2024-12-31 11:12:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 2f0c330646b94180b25cbd09b074e9dd | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614768641, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9282, jvmUsedMemory=0.5051, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1421, diskUsed=228.4164, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 446 | 1735614768679 | 2024-12-31 11:12:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2f0c330646b94180b25cbd09b074e9dd | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 446 | 1735614768680 | 2024-12-31 11:12:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2f0c330646b94180b25cbd09b074e9dd | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 522 | 1735614798664 | 2024-12-31 11:13:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | eabc7cb73ba048848f7034b8b9d02369 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614778639, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4712, jvmUsedMemory=0.5139, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1445, diskUsed=228.4167, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 522 | 1735614798666 | 2024-12-31 11:13:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | eabc7cb73ba048848f7034b8b9d02369 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614788640, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2573, jvmUsedMemory=0.5215, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1467, diskUsed=228.4169, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 522 | 1735614798666 | 2024-12-31 11:13:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | eabc7cb73ba048848f7034b8b9d02369 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614798639, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1172, jvmUsedMemory=0.5262, jvmMaxMemory=3.5557, jvmMemoryUsage=0.148, diskUsed=228.4158, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 522 | 1735614798683 | 2024-12-31 11:13:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | eabc7cb73ba048848f7034b8b9d02369 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 522 | 1735614798684 | 2024-12-31 11:13:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | eabc7cb73ba048848f7034b8b9d02369 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 560 | 1735614828666 | 2024-12-31 11:13:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7538dd71c3574ae8be567cc4bd955856 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614808639, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5571, jvmUsedMemory=0.5339, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1501, diskUsed=228.4159, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 560 | 1735614828667 | 2024-12-31 11:13:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7538dd71c3574ae8be567cc4bd955856 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614818642, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0835, jvmUsedMemory=0.5419, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1524, diskUsed=228.415, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 560 | 1735614828668 | 2024-12-31 11:13:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7538dd71c3574ae8be567cc4bd955856 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614828640, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8428, jvmUsedMemory=0.547, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1538, diskUsed=228.4169, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 560 | 1735614828680 | 2024-12-31 11:13:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 7538dd71c3574ae8be567cc4bd955856 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 560 | 1735614828681 | 2024-12-31 11:13:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 7538dd71c3574ae8be567cc4bd955856 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 599 | 1735614858670 | 2024-12-31 11:14:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 52b939e8848144938486846e2d45ac76 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614838642, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6455, jvmUsedMemory=0.5548, jvmMaxMemory=3.5557, jvmMemoryUsage=0.156, diskUsed=228.417, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 599 | 1735614858671 | 2024-12-31 11:14:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 52b939e8848144938486846e2d45ac76 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614848641, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6323, jvmUsedMemory=0.5623, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1582, diskUsed=228.417, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 599 | 1735614858672 | 2024-12-31 11:14:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 52b939e8848144938486846e2d45ac76 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614858643, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8164, jvmUsedMemory=0.5681, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1598, diskUsed=228.4179, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 599 | 1735614858679 | 2024-12-31 11:14:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 52b939e8848144938486846e2d45ac76 | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 599 | 1735614858680 | 2024-12-31 11:14:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 52b939e8848144938486846e2d45ac76 | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 634 | 1735614888668 | 2024-12-31 11:14:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c9c9c2d8ae5442629ebadfc4b5b609ad | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614868643, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5366, jvmUsedMemory=0.579, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1628, diskUsed=228.4137, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 634 | 1735614888669 | 2024-12-31 11:14:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c9c9c2d8ae5442629ebadfc4b5b609ad | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614878643, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2998, jvmUsedMemory=0.5846, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1644, diskUsed=228.413, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 634 | 1735614888670 | 2024-12-31 11:14:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c9c9c2d8ae5442629ebadfc4b5b609ad | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614888644, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5415, jvmUsedMemory=0.5892, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1657, diskUsed=228.4139, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 634 | 1735614888683 | 2024-12-31 11:14:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c9c9c2d8ae5442629ebadfc4b5b609ad | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 634 | 1735614888683 | 2024-12-31 11:14:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c9c9c2d8ae5442629ebadfc4b5b609ad | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1735614902681 | 2024-12-31 11:15:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 55650d6cf1c14740a7b32df4e373358a | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1735614902681 | 2024-12-31 11:15:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 14e61fe19f2f479bb1dab2aa97994034 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1735614902682 | 2024-12-31 11:15:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 14e61fe19f2f479bb1dab2aa97994034 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1735614902683 | 2024-12-31 11:15:02 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 55650d6cf1c14740a7b32df4e373358a | - | - | - | - | 3 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 633 | 1735614902738 | 2024-12-31 11:15:02 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 9fec6b3653ee425c99823cfaf1b9c7af | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 633 | 1735614902771 | 2024-12-31 11:15:02 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9fec6b3653ee425c99823cfaf1b9c7af | - | - | - | - | 35 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735614898643, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5381, jvmUsedMemory=0.5955, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1675, diskUsed=228.414, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 633 | 1735614902796 | 2024-12-31 11:15:02 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 9fec6b3653ee425c99823cfaf1b9c7af | - | - | - | - | 59 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 126 | 1735615159642 | 2024-12-31 11:19:19 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | f18d2e0fa99b4af69592d7d197deb44d | - | - | - | - | 118 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 126 | 1735615159643 | 2024-12-31 11:19:19 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | f18d2e0fa99b4af69592d7d197deb44d | - | - | - | - | 119 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 257 | 1735615180776 | 2024-12-31 11:19:40 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7d67a3a722ba413aa18734334647d999 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615160297, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9502, jvmUsedMemory=0.2477, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0697, diskUsed=227.4316, diskTotal=460.4317, diskUsage=0.494, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 257 | 1735615180783 | 2024-12-31 11:19:40 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7d67a3a722ba413aa18734334647d999 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615170298, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0117, jvmUsedMemory=0.3929, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1105, diskUsed=227.4316, diskTotal=460.4317, diskUsage=0.494, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 257 | 1735615180784 | 2024-12-31 11:19:40 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7d67a3a722ba413aa18734334647d999 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615180301, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7759, jvmUsedMemory=0.4008, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1127, diskUsed=228.4311, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 126 | 1735615180833 | 2024-12-31 11:19:40 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f18d2e0fa99b4af69592d7d197deb44d | - | - | - | - | 21309 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 126 | 1735615180833 | 2024-12-31 11:19:40 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f18d2e0fa99b4af69592d7d197deb44d | - | - | - | - | 21309 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 126 | 1735615180835 | 2024-12-31 11:19:40 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | f18d2e0fa99b4af69592d7d197deb44d | - | - | - | - | 21311 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 126 | 1735615180837 | 2024-12-31 11:19:40 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | f18d2e0fa99b4af69592d7d197deb44d | - | - | - | - | 21313 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 126 | 1735615180837 | 2024-12-31 11:19:40 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | f18d2e0fa99b4af69592d7d197deb44d | - | - | - | - | 21313 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 300 | 1735615216300 | 2024-12-31 11:20:16 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6f5b5b4fbc74454e8713c3b9307c4f71 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615190301, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8413, jvmUsedMemory=0.4204, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1182, diskUsed=228.4311, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 301 | 1735615216349 | 2024-12-31 11:20:16 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | f4027d8525b5498f87bebe48d26599ec | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 301 | 1735615216350 | 2024-12-31 11:20:16 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | f4027d8525b5498f87bebe48d26599ec | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 300 | 1735615241666 | 2024-12-31 11:20:41 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6f5b5b4fbc74454e8713c3b9307c4f71 | - | - | - | - | 25366 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615220301, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.3965, jvmUsedMemory=0.533, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1499, diskUsed=228.437, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 300 | 1735615241667 | 2024-12-31 11:20:41 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6f5b5b4fbc74454e8713c3b9307c4f71 | - | - | - | - | 25366 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615241660, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4023, jvmUsedMemory=0.538, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1513, diskUsed=228.4371, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 300 | 1735615241681 | 2024-12-31 11:20:41 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 6f5b5b4fbc74454e8713c3b9307c4f71 | - | - | - | - | 25380 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 300 | 1735615241681 | 2024-12-31 11:20:41 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 6f5b5b4fbc74454e8713c3b9307c4f71 | - | - | - | - | 25381 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 159 | 1735615307781 | 2024-12-31 11:21:47 | v2/ThreadPoolExecutor$Worker/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor$Worker | run | 31ca4e4c1c4940b3a663869566f50f86 | - | - | - | - | 1 | 0 | - | - | - | - | HikariPool-1 housekeeper c.z.h.p.HikariPool HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m27s337ms).

warning | 44 | 1735615307856 | 2024-12-31 11:21:47 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 82db6d202bea412e8d1c114ae17fc0da | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1735615307857 | 2024-12-31 11:21:47 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 82db6d202bea412e8d1c114ae17fc0da | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1735615307857 | 2024-12-31 11:21:47 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 663300131c994eed89308e8f0fed1a0b | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 40 | 1735615307861 | 2024-12-31 11:21:47 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 663300131c994eed89308e8f0fed1a0b | - | - | - | - | 5 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 299 | 1735615307931 | 2024-12-31 11:21:47 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | ae51ee73989742e7944275485b596f73 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 336 | 1735615307968 | 2024-12-31 11:21:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 35b3d98559284b9994de38987fdbdeaf | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615307782, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5688, jvmUsedMemory=0.5469, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1538, diskUsed=228.4398, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 336 | 1735615307969 | 2024-12-31 11:21:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 35b3d98559284b9994de38987fdbdeaf | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615307821, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5688, jvmUsedMemory=0.5633, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1584, diskUsed=228.4398, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 336 | 1735615307969 | 2024-12-31 11:21:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 35b3d98559284b9994de38987fdbdeaf | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615307821, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5688, jvmUsedMemory=0.5633, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1584, diskUsed=228.4398, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 336 | 1735615307969 | 2024-12-31 11:21:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 35b3d98559284b9994de38987fdbdeaf | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615307822, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5688, jvmUsedMemory=0.5639, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1586, diskUsed=228.4398, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 336 | 1735615307969 | 2024-12-31 11:21:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 35b3d98559284b9994de38987fdbdeaf | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615307822, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5688, jvmUsedMemory=0.5639, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1586, diskUsed=228.4398, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 380 | 1735615307970 | 2024-12-31 11:21:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 02ebbd2f09fc4a8597ba09febc6d03ec | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615307822, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5688, jvmUsedMemory=0.5639, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1586, diskUsed=228.4398, diskTotal=460.4317, diskUsage=0.4961, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 381 | 1735615308032 | 2024-12-31 11:21:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 8d766296198e4f128a757a56d3ac7425 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 122 | 1735615390256 | 2024-12-31 11:23:10 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 4646c4ae48774ef2a28e6e61653cc505 | - | - | - | - | 108 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 122 | 1735615390257 | 2024-12-31 11:23:10 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 4646c4ae48774ef2a28e6e61653cc505 | - | - | - | - | 109 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 155 | 1735615487035 | 2024-12-31 11:24:47 | v2/ThreadPoolExecutor$Worker/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor$Worker | run | 01cec43b10534afab11be9f426e05500 | - | - | - | - | 13 | 0 | - | - | - | - | HikariPool-1 housekeeper c.z.h.p.HikariPool HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m36s12ms).

warning | 44 | 1735615487134 | 2024-12-31 11:24:47 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 696f0a95a64b4d74ad82febcaf83ee90 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1735615487134 | 2024-12-31 11:24:47 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 696f0a95a64b4d74ad82febcaf83ee90 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 269 | 1735615487125 | 2024-12-31 11:24:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 4b97651d32ec493aa51e4f7ab21d1dce | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615390913, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3101, jvmUsedMemory=0.2375, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0668, diskUsed=228.4438, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1735615487135 | 2024-12-31 11:24:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 4b97651d32ec493aa51e4f7ab21d1dce | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615487035, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8306, jvmUsedMemory=0.4779, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1344, diskUsed=228.4529, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 40 | 1735615487138 | 2024-12-31 11:24:47 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | a9a67ae540904a8785e96a07c6eb53cf | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 269 | 1735615487137 | 2024-12-31 11:24:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 4b97651d32ec493aa51e4f7ab21d1dce | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615487092, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8306, jvmUsedMemory=0.4904, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1379, diskUsed=228.4529, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1735615487139 | 2024-12-31 11:24:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 4b97651d32ec493aa51e4f7ab21d1dce | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615487093, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8306, jvmUsedMemory=0.491, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1381, diskUsed=228.4529, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1735615487139 | 2024-12-31 11:24:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 4b97651d32ec493aa51e4f7ab21d1dce | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615487093, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8306, jvmUsedMemory=0.491, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1381, diskUsed=228.4529, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 40 | 1735615487140 | 2024-12-31 11:24:47 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | a9a67ae540904a8785e96a07c6eb53cf | - | - | - | - | 2 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 269 | 1735615487144 | 2024-12-31 11:24:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 4b97651d32ec493aa51e4f7ab21d1dce | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615487093, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8306, jvmUsedMemory=0.491, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1381, diskUsed=228.4529, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1735615487147 | 2024-12-31 11:24:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | Actor | aroundReceive | 4b97651d32ec493aa51e4f7ab21d1dce | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615487093, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8306, jvmUsedMemory=0.4916, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1382, diskUsed=228.4529, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1735615487148 | 2024-12-31 11:24:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | Actor | aroundReceive | 4b97651d32ec493aa51e4f7ab21d1dce | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615487094, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8306, jvmUsedMemory=0.4916, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1382, diskUsed=228.4529, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1735615487148 | 2024-12-31 11:24:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | Actor | aroundReceive | 4b97651d32ec493aa51e4f7ab21d1dce | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615487094, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8306, jvmUsedMemory=0.4916, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1382, diskUsed=228.4529, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1735615487148 | 2024-12-31 11:24:47 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | Actor | aroundReceive | 4b97651d32ec493aa51e4f7ab21d1dce | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615487094, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8306, jvmUsedMemory=0.4921, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1384, diskUsed=228.4529, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 123 | 1735615695015 | 2024-12-31 11:28:15 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 4fbc132f061149edba227f1979652f39 | - | - | - | - | 111 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 123 | 1735615695015 | 2024-12-31 11:28:15 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 4fbc132f061149edba227f1979652f39 | - | - | - | - | 111 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 255 | 1735615715682 | 2024-12-31 11:28:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6404b9245ac346c4bcfa7c35d833cfc2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615695559, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7803, jvmUsedMemory=0.456, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1282, diskUsed=228.4603, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 255 | 1735615715691 | 2024-12-31 11:28:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6404b9245ac346c4bcfa7c35d833cfc2 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615705559, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.894, jvmUsedMemory=0.5922, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1665, diskUsed=228.4603, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 255 | 1735615715692 | 2024-12-31 11:28:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6404b9245ac346c4bcfa7c35d833cfc2 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615715561, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2422, jvmUsedMemory=0.6024, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1694, diskUsed=228.4603, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 123 | 1735615715743 | 2024-12-31 11:28:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 4fbc132f061149edba227f1979652f39 | - | - | - | - | 20840 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1735615715744 | 2024-12-31 11:28:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 4fbc132f061149edba227f1979652f39 | - | - | - | - | 20840 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1735615715747 | 2024-12-31 11:28:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 4fbc132f061149edba227f1979652f39 | - | - | - | - | 20843 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1735615715749 | 2024-12-31 11:28:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 4fbc132f061149edba227f1979652f39 | - | - | - | - | 20845 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1735615715750 | 2024-12-31 11:28:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 4fbc132f061149edba227f1979652f39 | - | - | - | - | 20846 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 255 | 1735615745580 | 2024-12-31 11:29:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 6404b9245ac346c4bcfa7c35d833cfc2 | - | - | - | - | 29890 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615725561, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2305, jvmUsedMemory=0.6213, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1747, diskUsed=228.4606, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 255 | 1735615745581 | 2024-12-31 11:29:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 6404b9245ac346c4bcfa7c35d833cfc2 | - | - | - | - | 29891 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615735562, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6533, jvmUsedMemory=0.6271, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1764, diskUsed=228.4606, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 255 | 1735615745581 | 2024-12-31 11:29:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 6404b9245ac346c4bcfa7c35d833cfc2 | - | - | - | - | 29891 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615745563, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5649, jvmUsedMemory=0.6347, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1785, diskUsed=228.4473, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 255 | 1735615745599 | 2024-12-31 11:29:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 6404b9245ac346c4bcfa7c35d833cfc2 | - | - | - | - | 29910 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 255 | 1735615745600 | 2024-12-31 11:29:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 6404b9245ac346c4bcfa7c35d833cfc2 | - | - | - | - | 29910 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-41 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 296 | 1735615775583 | 2024-12-31 11:29:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | b73e032373f443cf8ad347d1d6e20d3a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615755561, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4106, jvmUsedMemory=0.645, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1814, diskUsed=228.4483, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 296 | 1735615775585 | 2024-12-31 11:29:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | b73e032373f443cf8ad347d1d6e20d3a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615765564, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8857, jvmUsedMemory=0.6513, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1832, diskUsed=228.4484, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 296 | 1735615775585 | 2024-12-31 11:29:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | b73e032373f443cf8ad347d1d6e20d3a | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735615775561, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.2729, jvmUsedMemory=0.6579, jvmMaxMemory=3.5557, jvmMemoryUsage=0.185, diskUsed=228.449, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 296 | 1735615775604 | 2024-12-31 11:29:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | b73e032373f443cf8ad347d1d6e20d3a | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 296 | 1735615775606 | 2024-12-31 11:29:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | b73e032373f443cf8ad347d1d6e20d3a | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 156 | 1735615803192 | 2024-12-31 11:30:03 | v2/ThreadPoolExecutor$Worker/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor$Worker | run | 51af6211f08343c28d0c5a84422914d6 | - | - | - | - | 1 | 0 | - | - | - | - | HikariPool-1 housekeeper c.z.h.p.HikariPool HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=47s536ms).

warning | 44 | 1735615803263 | 2024-12-31 11:30:03 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 25b95a9526754000bc2c1d9a12e4604a | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1735615803263 | 2024-12-31 11:30:03 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | eb0674ab26884d5698c1d44d772f82e8 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1735615803264 | 2024-12-31 11:30:03 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 25b95a9526754000bc2c1d9a12e4604a | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1735615803266 | 2024-12-31 11:30:03 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | eb0674ab26884d5698c1d44d772f82e8 | - | - | - | - | 4 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 298 | 1735615803349 | 2024-12-31 11:30:03 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 186d09a908674ff68259bdc7c96879c9 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 123 | 1735623603096 | 2024-12-31 13:40:03 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 484bfbe9e26b479187f4fed4118923b9 | - | - | - | - | 111 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 123 | 1735623603097 | 2024-12-31 13:40:03 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 484bfbe9e26b479187f4fed4118923b9 | - | - | - | - | 112 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 260 | 1735623623874 | 2024-12-31 13:40:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 60caa1c1331b41d1b6f4f656b93249f5 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623603731, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6978, jvmUsedMemory=0.4597, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1293, diskUsed=227.5679, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 260 | 1735623623880 | 2024-12-31 13:40:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 60caa1c1331b41d1b6f4f656b93249f5 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623613735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.436, jvmUsedMemory=0.6011, jvmMaxMemory=3.5557, jvmMemoryUsage=0.169, diskUsed=227.5689, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 260 | 1735623623881 | 2024-12-31 13:40:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 60caa1c1331b41d1b6f4f656b93249f5 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623623735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3687, jvmUsedMemory=0.6087, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1712, diskUsed=227.569, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 131 | 1735623623935 | 2024-12-31 13:40:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 1ea1504295184e4c9c94f4a3c98fb56f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 131 | 1735623623936 | 2024-12-31 13:40:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 1ea1504295184e4c9c94f4a3c98fb56f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 131 | 1735623623941 | 2024-12-31 13:40:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 1ea1504295184e4c9c94f4a3c98fb56f | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 131 | 1735623623947 | 2024-12-31 13:40:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 1ea1504295184e4c9c94f4a3c98fb56f | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 131 | 1735623623947 | 2024-12-31 13:40:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 1ea1504295184e4c9c94f4a3c98fb56f | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 318 | 1735623653774 | 2024-12-31 13:40:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 9b5c30edeb544ac9879d7c09f27701f8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623633735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.084, jvmUsedMemory=0.7046, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1982, diskUsed=227.569, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 318 | 1735623653800 | 2024-12-31 13:40:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9b5c30edeb544ac9879d7c09f27701f8 | - | - | - | - | 31 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623643734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3975, jvmUsedMemory=0.7099, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1997, diskUsed=227.5692, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 318 | 1735623653817 | 2024-12-31 13:40:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 9b5c30edeb544ac9879d7c09f27701f8 | - | - | - | - | 54 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623653733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5439, jvmUsedMemory=0.7153, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2012, diskUsed=227.5701, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 318 | 1735623653845 | 2024-12-31 13:40:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 9b5c30edeb544ac9879d7c09f27701f8 | - | - | - | - | 69 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 318 | 1735623653847 | 2024-12-31 13:40:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 9b5c30edeb544ac9879d7c09f27701f8 | - | - | - | - | 71 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 317 | 1735623683761 | 2024-12-31 13:41:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c3a477278cc249e290ee252979355b04 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623663731, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.54, jvmUsedMemory=0.7275, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2046, diskUsed=227.5702, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 317 | 1735623683764 | 2024-12-31 13:41:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c3a477278cc249e290ee252979355b04 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623673735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6968, jvmUsedMemory=0.7345, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2066, diskUsed=227.5703, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 317 | 1735623683764 | 2024-12-31 13:41:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c3a477278cc249e290ee252979355b04 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623683734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4287, jvmUsedMemory=0.7394, jvmMaxMemory=3.5557, jvmMemoryUsage=0.208, diskUsed=227.5704, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 317 | 1735623683797 | 2024-12-31 13:41:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c3a477278cc249e290ee252979355b04 | - | - | - | - | 35 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 317 | 1735623683797 | 2024-12-31 13:41:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c3a477278cc249e290ee252979355b04 | - | - | - | - | 35 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 398 | 1735623713764 | 2024-12-31 13:41:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f413f881aa4e4f4f8b3f721ae71c8ac7 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623693735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3687, jvmUsedMemory=0.7498, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2109, diskUsed=227.5703, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 398 | 1735623713766 | 2024-12-31 13:41:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f413f881aa4e4f4f8b3f721ae71c8ac7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623703735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0039, jvmUsedMemory=0.7542, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2121, diskUsed=227.5666, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 398 | 1735623713767 | 2024-12-31 13:41:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f413f881aa4e4f4f8b3f721ae71c8ac7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623713731, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9165, jvmUsedMemory=0.7592, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2135, diskUsed=227.5668, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 361 | 1735623713784 | 2024-12-31 13:41:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | ddf1c456f3834a6181f471a08a5561f6 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 361 | 1735623713785 | 2024-12-31 13:41:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | ddf1c456f3834a6181f471a08a5561f6 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 398 | 1735623743753 | 2024-12-31 13:42:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | f413f881aa4e4f4f8b3f721ae71c8ac7 | - | - | - | - | 29989 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623723735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9287, jvmUsedMemory=0.7677, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2159, diskUsed=227.5668, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 398 | 1735623743755 | 2024-12-31 13:42:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | f413f881aa4e4f4f8b3f721ae71c8ac7 | - | - | - | - | 29990 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623733735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0864, jvmUsedMemory=0.7742, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2177, diskUsed=227.5679, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 398 | 1735623743755 | 2024-12-31 13:42:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | f413f881aa4e4f4f8b3f721ae71c8ac7 | - | - | - | - | 29990 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623743733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3545, jvmUsedMemory=0.7787, jvmMaxMemory=3.5557, jvmMemoryUsage=0.219, diskUsed=227.5684, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 398 | 1735623743770 | 2024-12-31 13:42:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | f413f881aa4e4f4f8b3f721ae71c8ac7 | - | - | - | - | 30005 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 398 | 1735623743771 | 2024-12-31 13:42:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | f413f881aa4e4f4f8b3f721ae71c8ac7 | - | - | - | - | 30006 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 434 | 1735623773753 | 2024-12-31 13:42:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | b1788e80af2f40ac8b73f571e5314840 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623753732, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0654, jvmUsedMemory=0.7886, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2218, diskUsed=227.594, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 434 | 1735623773754 | 2024-12-31 13:42:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | b1788e80af2f40ac8b73f571e5314840 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623763736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2632, jvmUsedMemory=0.794, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2233, diskUsed=227.5953, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 434 | 1735623773755 | 2024-12-31 13:42:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | b1788e80af2f40ac8b73f571e5314840 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623773736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2349, jvmUsedMemory=0.7989, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2247, diskUsed=227.5904, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 434 | 1735623773768 | 2024-12-31 13:42:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | b1788e80af2f40ac8b73f571e5314840 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 434 | 1735623773772 | 2024-12-31 13:42:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | b1788e80af2f40ac8b73f571e5314840 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 436 | 1735623803762 | 2024-12-31 13:43:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3756718bd44c4578a2c270b3d861fd22 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623783733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8452, jvmUsedMemory=0.8065, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2268, diskUsed=227.5913, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 436 | 1735623803763 | 2024-12-31 13:43:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3756718bd44c4578a2c270b3d861fd22 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623793733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5547, jvmUsedMemory=0.8139, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2289, diskUsed=227.5915, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 436 | 1735623803763 | 2024-12-31 13:43:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3756718bd44c4578a2c270b3d861fd22 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623803733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1616, jvmUsedMemory=0.8198, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2306, diskUsed=227.5915, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 436 | 1735623803777 | 2024-12-31 13:43:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3756718bd44c4578a2c270b3d861fd22 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 436 | 1735623803778 | 2024-12-31 13:43:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3756718bd44c4578a2c270b3d861fd22 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 511 | 1735623833754 | 2024-12-31 13:43:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7f1be69b61124c7ea63b272d6cb78c85 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623813731, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2163, jvmUsedMemory=0.8288, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2331, diskUsed=227.5915, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 511 | 1735623833759 | 2024-12-31 13:43:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7f1be69b61124c7ea63b272d6cb78c85 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623823732, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.269, jvmUsedMemory=0.8333, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2344, diskUsed=227.5927, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 511 | 1735623833759 | 2024-12-31 13:43:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7f1be69b61124c7ea63b272d6cb78c85 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623833734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3198, jvmUsedMemory=0.8386, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2359, diskUsed=227.5918, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 511 | 1735623833843 | 2024-12-31 13:43:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 7f1be69b61124c7ea63b272d6cb78c85 | - | - | - | - | 86 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 511 | 1735623833843 | 2024-12-31 13:43:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 7f1be69b61124c7ea63b272d6cb78c85 | - | - | - | - | 86 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 512 | 1735623863764 | 2024-12-31 13:44:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3cb246d8b35749429334a481dbe8ba29 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623843731, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7119, jvmUsedMemory=0.8465, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2381, diskUsed=227.5674, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 512 | 1735623863767 | 2024-12-31 13:44:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3cb246d8b35749429334a481dbe8ba29 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623853736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7495, jvmUsedMemory=0.8534, jvmMaxMemory=3.5557, jvmMemoryUsage=0.24, diskUsed=227.5675, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 512 | 1735623863768 | 2024-12-31 13:44:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3cb246d8b35749429334a481dbe8ba29 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623863732, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.249, jvmUsedMemory=0.8584, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2414, diskUsed=227.5685, diskTotal=460.4317, diskUsage=0.4943, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 512 | 1735623863791 | 2024-12-31 13:44:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3cb246d8b35749429334a481dbe8ba29 | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 512 | 1735623863792 | 2024-12-31 13:44:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3cb246d8b35749429334a481dbe8ba29 | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 588 | 1735623893774 | 2024-12-31 13:44:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e51e93eaa45f4760ad4c8840fbd80193 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623873733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1177, jvmUsedMemory=0.867, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2438, diskUsed=227.5427, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 588 | 1735623893777 | 2024-12-31 13:44:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e51e93eaa45f4760ad4c8840fbd80193 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623883732, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4258, jvmUsedMemory=0.8715, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2451, diskUsed=227.5426, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 588 | 1735623893778 | 2024-12-31 13:44:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e51e93eaa45f4760ad4c8840fbd80193 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623893731, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1196, jvmUsedMemory=0.8768, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2466, diskUsed=227.5417, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 588 | 1735623893798 | 2024-12-31 13:44:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | e51e93eaa45f4760ad4c8840fbd80193 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 588 | 1735623893799 | 2024-12-31 13:44:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | e51e93eaa45f4760ad4c8840fbd80193 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 589 | 1735623923753 | 2024-12-31 13:45:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 020255d1d79f451081a326f2bfd63788 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623903734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0205, jvmUsedMemory=0.8882, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2498, diskUsed=227.5417, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 589 | 1735623923755 | 2024-12-31 13:45:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 020255d1d79f451081a326f2bfd63788 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623913732, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7832, jvmUsedMemory=0.892, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2509, diskUsed=227.5425, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 589 | 1735623923761 | 2024-12-31 13:45:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 020255d1d79f451081a326f2bfd63788 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623923735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7554, jvmUsedMemory=0.8991, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2529, diskUsed=227.5438, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 589 | 1735623923780 | 2024-12-31 13:45:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 020255d1d79f451081a326f2bfd63788 | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 589 | 1735623923780 | 2024-12-31 13:45:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 020255d1d79f451081a326f2bfd63788 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 661 | 1735623953763 | 2024-12-31 13:45:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0f6f1472058d4dd8a178ea6466248738 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623933734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1001, jvmUsedMemory=0.9087, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2556, diskUsed=227.5438, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 661 | 1735623953764 | 2024-12-31 13:45:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0f6f1472058d4dd8a178ea6466248738 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623943735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8569, jvmUsedMemory=0.9132, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2568, diskUsed=227.5449, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 661 | 1735623953764 | 2024-12-31 13:45:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0f6f1472058d4dd8a178ea6466248738 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623953735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8657, jvmUsedMemory=0.9177, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2581, diskUsed=227.5439, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 661 | 1735623953776 | 2024-12-31 13:45:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0f6f1472058d4dd8a178ea6466248738 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 661 | 1735623953776 | 2024-12-31 13:45:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0f6f1472058d4dd8a178ea6466248738 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 695 | 1735623983786 | 2024-12-31 13:46:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 06110e0032cb4378a842efc5fb108f39 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623963736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8408, jvmUsedMemory=0.9263, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2605, diskUsed=227.545, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 695 | 1735623983791 | 2024-12-31 13:46:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 06110e0032cb4378a842efc5fb108f39 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623973733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3237, jvmUsedMemory=0.9319, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2621, diskUsed=227.546, diskTotal=460.4317, diskUsage=0.4942, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 695 | 1735623983792 | 2024-12-31 13:46:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 06110e0032cb4378a842efc5fb108f39 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623983734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3662, jvmUsedMemory=0.9373, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2636, diskUsed=227.494, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 695 | 1735623983815 | 2024-12-31 13:46:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 06110e0032cb4378a842efc5fb108f39 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 695 | 1735623983816 | 2024-12-31 13:46:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 06110e0032cb4378a842efc5fb108f39 | - | - | - | - | 28 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 696 | 1735624013754 | 2024-12-31 13:46:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | dfa70adf4c1f472f892cdf786175e53f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735623993733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0083, jvmUsedMemory=0.9445, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2656, diskUsed=227.4944, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 696 | 1735624013754 | 2024-12-31 13:46:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | dfa70adf4c1f472f892cdf786175e53f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624003731, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5454, jvmUsedMemory=0.949, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2669, diskUsed=227.4945, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 696 | 1735624013755 | 2024-12-31 13:46:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | dfa70adf4c1f472f892cdf786175e53f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624013736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7686, jvmUsedMemory=0.9544, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2684, diskUsed=227.4959, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 696 | 1735624013765 | 2024-12-31 13:46:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | dfa70adf4c1f472f892cdf786175e53f | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 696 | 1735624013765 | 2024-12-31 13:46:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | dfa70adf4c1f472f892cdf786175e53f | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 767 | 1735624043765 | 2024-12-31 13:47:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 897cb1188fda41d2b499bd0b625059b8 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624023736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5825, jvmUsedMemory=0.9661, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2717, diskUsed=227.4959, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 767 | 1735624043773 | 2024-12-31 13:47:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 897cb1188fda41d2b499bd0b625059b8 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624033735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.627, jvmUsedMemory=0.1647, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0463, diskUsed=227.4969, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 767 | 1735624043773 | 2024-12-31 13:47:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 897cb1188fda41d2b499bd0b625059b8 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624043733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5176, jvmUsedMemory=0.1672, jvmMaxMemory=3.5557, jvmMemoryUsage=0.047, diskUsed=227.4932, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 767 | 1735624043788 | 2024-12-31 13:47:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 897cb1188fda41d2b499bd0b625059b8 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 767 | 1735624043788 | 2024-12-31 13:47:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 897cb1188fda41d2b499bd0b625059b8 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 766 | 1735624073763 | 2024-12-31 13:47:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | aadd05d0303d4b769d23b6c4a7fba68b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624053735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2104, jvmUsedMemory=0.1742, jvmMaxMemory=3.5557, jvmMemoryUsage=0.049, diskUsed=227.4932, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 766 | 1735624073765 | 2024-12-31 13:47:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | aadd05d0303d4b769d23b6c4a7fba68b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624063737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0303, jvmUsedMemory=0.177, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0498, diskUsed=227.4932, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 766 | 1735624073765 | 2024-12-31 13:47:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | aadd05d0303d4b769d23b6c4a7fba68b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624073735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7979, jvmUsedMemory=0.1798, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0506, diskUsed=227.4924, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 766 | 1735624073789 | 2024-12-31 13:47:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | aadd05d0303d4b769d23b6c4a7fba68b | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 766 | 1735624073790 | 2024-12-31 13:47:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | aadd05d0303d4b769d23b6c4a7fba68b | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 873 | 1735624103754 | 2024-12-31 13:48:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0ec1d33f02d14715bc4091838657fc3e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624083732, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7422, jvmUsedMemory=0.1862, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0524, diskUsed=227.4924, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 873 | 1735624103762 | 2024-12-31 13:48:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0ec1d33f02d14715bc4091838657fc3e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624093734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4741, jvmUsedMemory=0.189, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0531, diskUsed=227.4933, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 873 | 1735624103763 | 2024-12-31 13:48:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0ec1d33f02d14715bc4091838657fc3e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624103736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3945, jvmUsedMemory=0.1915, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0539, diskUsed=227.4935, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 873 | 1735624103775 | 2024-12-31 13:48:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0ec1d33f02d14715bc4091838657fc3e | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 873 | 1735624103776 | 2024-12-31 13:48:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0ec1d33f02d14715bc4091838657fc3e | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 911 | 1735624133764 | 2024-12-31 13:48:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 744cb97ba50540508a92cba3c5447fad | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624113735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6348, jvmUsedMemory=0.1982, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0557, diskUsed=227.4935, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 911 | 1735624133766 | 2024-12-31 13:48:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 744cb97ba50540508a92cba3c5447fad | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624123734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7642, jvmUsedMemory=0.2007, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0564, diskUsed=227.4938, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 911 | 1735624133766 | 2024-12-31 13:48:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 744cb97ba50540508a92cba3c5447fad | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624133737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6396, jvmUsedMemory=0.2035, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0572, diskUsed=227.4946, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 911 | 1735624133782 | 2024-12-31 13:48:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 744cb97ba50540508a92cba3c5447fad | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 911 | 1735624133783 | 2024-12-31 13:48:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 744cb97ba50540508a92cba3c5447fad | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 912 | 1735624163754 | 2024-12-31 13:49:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | aa2178e0318443ea87282f1e9c8e7649 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624143736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0566, jvmUsedMemory=0.2098, jvmMaxMemory=3.5557, jvmMemoryUsage=0.059, diskUsed=227.4945, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 912 | 1735624163757 | 2024-12-31 13:49:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | aa2178e0318443ea87282f1e9c8e7649 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624153733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8198, jvmUsedMemory=0.2133, jvmMaxMemory=3.5557, jvmMemoryUsage=0.06, diskUsed=227.4965, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 912 | 1735624163762 | 2024-12-31 13:49:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | aa2178e0318443ea87282f1e9c8e7649 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624163736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2954, jvmUsedMemory=0.2164, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0608, diskUsed=228.4977, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 912 | 1735624163780 | 2024-12-31 13:49:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | aa2178e0318443ea87282f1e9c8e7649 | - | - | - | - | 31 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 912 | 1735624163787 | 2024-12-31 13:49:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | aa2178e0318443ea87282f1e9c8e7649 | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 946 | 1735624193764 | 2024-12-31 13:49:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 63b6b0b1034949ca872bf685d5658442 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-89 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624173736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.416, jvmUsedMemory=0.2223, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0625, diskUsed=228.4978, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 946 | 1735624193765 | 2024-12-31 13:49:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 63b6b0b1034949ca872bf685d5658442 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-89 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624183736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4121, jvmUsedMemory=0.2251, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0633, diskUsed=228.4978, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 946 | 1735624193770 | 2024-12-31 13:49:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 63b6b0b1034949ca872bf685d5658442 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-89 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624193734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.041, jvmUsedMemory=0.2291, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0644, diskUsed=228.4968, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 946 | 1735624193787 | 2024-12-31 13:49:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 63b6b0b1034949ca872bf685d5658442 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-89 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 946 | 1735624193788 | 2024-12-31 13:49:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 63b6b0b1034949ca872bf685d5658442 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-89 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 983 | 1735624223753 | 2024-12-31 13:50:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a342a69a3c984f78a1b993cda7f0dc15 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-92 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624203732, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8804, jvmUsedMemory=0.2357, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0663, diskUsed=228.4978, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 983 | 1735624223757 | 2024-12-31 13:50:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a342a69a3c984f78a1b993cda7f0dc15 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-92 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624213732, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7383, jvmUsedMemory=0.2383, jvmMaxMemory=3.5557, jvmMemoryUsage=0.067, diskUsed=228.4978, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 983 | 1735624223758 | 2024-12-31 13:50:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a342a69a3c984f78a1b993cda7f0dc15 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-92 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624223737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5503, jvmUsedMemory=0.241, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0678, diskUsed=228.4979, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 983 | 1735624223775 | 2024-12-31 13:50:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a342a69a3c984f78a1b993cda7f0dc15 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-92 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 983 | 1735624223775 | 2024-12-31 13:50:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a342a69a3c984f78a1b993cda7f0dc15 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-92 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 985 | 1735624253764 | 2024-12-31 13:50:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ca585998347a41b09565253bc2a56b59 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-94 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624233737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6255, jvmUsedMemory=0.2471, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0695, diskUsed=228.4989, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 985 | 1735624253766 | 2024-12-31 13:50:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ca585998347a41b09565253bc2a56b59 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-94 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624243738, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5225, jvmUsedMemory=0.2544, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0715, diskUsed=228.4989, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 985 | 1735624253767 | 2024-12-31 13:50:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ca585998347a41b09565253bc2a56b59 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-94 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624253736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4355, jvmUsedMemory=0.257, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0723, diskUsed=228.498, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 985 | 1735624253780 | 2024-12-31 13:50:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | ca585998347a41b09565253bc2a56b59 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-94 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 985 | 1735624253781 | 2024-12-31 13:50:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | ca585998347a41b09565253bc2a56b59 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-94 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1056 | 1735624283755 | 2024-12-31 13:51:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3a6f8295dc0b4447b01bbbd5b8aa8196 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624263737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4482, jvmUsedMemory=0.2629, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0739, diskUsed=228.498, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1056 | 1735624283760 | 2024-12-31 13:51:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3a6f8295dc0b4447b01bbbd5b8aa8196 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624273739, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6064, jvmUsedMemory=0.2659, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0748, diskUsed=228.4989, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1056 | 1735624283762 | 2024-12-31 13:51:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3a6f8295dc0b4447b01bbbd5b8aa8196 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624283734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.207, jvmUsedMemory=0.2683, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0755, diskUsed=228.5096, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1056 | 1735624283784 | 2024-12-31 13:51:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3a6f8295dc0b4447b01bbbd5b8aa8196 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1056 | 1735624283785 | 2024-12-31 13:51:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3a6f8295dc0b4447b01bbbd5b8aa8196 | - | - | - | - | 28 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1055 | 1735624313764 | 2024-12-31 13:51:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | edecc7fd0ebb4352a43ae1fccc3ec6db | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624293738, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0947, jvmUsedMemory=0.2739, jvmMaxMemory=3.5557, jvmMemoryUsage=0.077, diskUsed=228.5096, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1055 | 1735624313767 | 2024-12-31 13:51:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | edecc7fd0ebb4352a43ae1fccc3ec6db | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624303736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9194, jvmUsedMemory=0.2764, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0777, diskUsed=228.5096, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1055 | 1735624313768 | 2024-12-31 13:51:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | edecc7fd0ebb4352a43ae1fccc3ec6db | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624313737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6304, jvmUsedMemory=0.2794, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0786, diskUsed=228.5096, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1055 | 1735624313784 | 2024-12-31 13:51:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | edecc7fd0ebb4352a43ae1fccc3ec6db | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1055 | 1735624313784 | 2024-12-31 13:51:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | edecc7fd0ebb4352a43ae1fccc3ec6db | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1121 | 1735624343773 | 2024-12-31 13:52:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e8745777223f4072a8b894a8370b6772 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-104 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624323735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3057, jvmUsedMemory=0.2857, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0803, diskUsed=228.5096, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1121 | 1735624343775 | 2024-12-31 13:52:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e8745777223f4072a8b894a8370b6772 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-104 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624333736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3188, jvmUsedMemory=0.2888, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0812, diskUsed=228.5105, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1121 | 1735624343777 | 2024-12-31 13:52:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e8745777223f4072a8b894a8370b6772 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-104 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624343734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8652, jvmUsedMemory=0.2914, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0819, diskUsed=228.5115, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1121 | 1735624343790 | 2024-12-31 13:52:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | e8745777223f4072a8b894a8370b6772 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-104 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1121 | 1735624343791 | 2024-12-31 13:52:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | e8745777223f4072a8b894a8370b6772 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-104 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1119 | 1735624373754 | 2024-12-31 13:52:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 84d8006fcc4349a4acc10a0f80e096b1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624353734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5781, jvmUsedMemory=0.2972, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0836, diskUsed=228.5115, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1119 | 1735624373755 | 2024-12-31 13:52:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 84d8006fcc4349a4acc10a0f80e096b1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624363734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1816, jvmUsedMemory=0.3012, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0847, diskUsed=228.5115, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1119 | 1735624373755 | 2024-12-31 13:52:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 84d8006fcc4349a4acc10a0f80e096b1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624373737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0732, jvmUsedMemory=0.3035, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0854, diskUsed=228.5106, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1119 | 1735624373763 | 2024-12-31 13:52:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 84d8006fcc4349a4acc10a0f80e096b1 | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1119 | 1735624373763 | 2024-12-31 13:52:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 84d8006fcc4349a4acc10a0f80e096b1 | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1155 | 1735624403755 | 2024-12-31 13:53:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 60784ab2cf7e4c898743c3fccecc08fa | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-106 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624383733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0928, jvmUsedMemory=0.3089, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0869, diskUsed=228.5106, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1155 | 1735624403757 | 2024-12-31 13:53:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 60784ab2cf7e4c898743c3fccecc08fa | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-106 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624393738, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6968, jvmUsedMemory=0.3117, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0877, diskUsed=228.5019, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1155 | 1735624403758 | 2024-12-31 13:53:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 60784ab2cf7e4c898743c3fccecc08fa | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-106 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624403733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.105, jvmUsedMemory=0.3145, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0885, diskUsed=228.5021, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1155 | 1735624403770 | 2024-12-31 13:53:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 60784ab2cf7e4c898743c3fccecc08fa | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-106 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1155 | 1735624403770 | 2024-12-31 13:53:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 60784ab2cf7e4c898743c3fccecc08fa | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-106 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1224 | 1735624433761 | 2024-12-31 13:53:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 00370dbf7fda4a10995547f8f9a3383f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624413735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7007, jvmUsedMemory=0.3213, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0904, diskUsed=228.5021, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1224 | 1735624433762 | 2024-12-31 13:53:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 00370dbf7fda4a10995547f8f9a3383f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624423734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6665, jvmUsedMemory=0.3254, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0915, diskUsed=228.5022, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1224 | 1735624433762 | 2024-12-31 13:53:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 00370dbf7fda4a10995547f8f9a3383f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624433737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4097, jvmUsedMemory=0.3341, jvmMaxMemory=3.5557, jvmMemoryUsage=0.094, diskUsed=228.5013, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1224 | 1735624433776 | 2024-12-31 13:53:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 00370dbf7fda4a10995547f8f9a3383f | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1224 | 1735624433776 | 2024-12-31 13:53:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 00370dbf7fda4a10995547f8f9a3383f | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1223 | 1735624463786 | 2024-12-31 13:54:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 17cf1053e94f42ca8e83c4dd9f77c7e9 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624443735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8267, jvmUsedMemory=0.3401, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0956, diskUsed=227.5016, diskTotal=460.4317, diskUsage=0.4941, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1223 | 1735624463788 | 2024-12-31 13:54:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 17cf1053e94f42ca8e83c4dd9f77c7e9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624453734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7793, jvmUsedMemory=0.343, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0965, diskUsed=228.5052, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1223 | 1735624463789 | 2024-12-31 13:54:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 17cf1053e94f42ca8e83c4dd9f77c7e9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624463736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8667, jvmUsedMemory=0.3454, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0971, diskUsed=228.5056, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1223 | 1735624463811 | 2024-12-31 13:54:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 17cf1053e94f42ca8e83c4dd9f77c7e9 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1223 | 1735624463812 | 2024-12-31 13:54:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 17cf1053e94f42ca8e83c4dd9f77c7e9 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1296 | 1735624493759 | 2024-12-31 13:54:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 428369f458cf4a43a507ac2d2b156097 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-116 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624473733, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4253, jvmUsedMemory=0.3503, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0985, diskUsed=228.5028, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1296 | 1735624493768 | 2024-12-31 13:54:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 428369f458cf4a43a507ac2d2b156097 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-116 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624483734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1929, jvmUsedMemory=0.3539, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0995, diskUsed=228.503, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1296 | 1735624493768 | 2024-12-31 13:54:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 428369f458cf4a43a507ac2d2b156097 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-116 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624493738, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0024, jvmUsedMemory=0.3567, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1003, diskUsed=228.5031, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1296 | 1735624493788 | 2024-12-31 13:54:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 428369f458cf4a43a507ac2d2b156097 | - | - | - | - | 28 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-116 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1296 | 1735624493789 | 2024-12-31 13:54:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 428369f458cf4a43a507ac2d2b156097 | - | - | - | - | 28 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-116 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1329 | 1735624523756 | 2024-12-31 13:55:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ef6e4a387d084fe1a52e3e8eeb5ca46f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624503736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0688, jvmUsedMemory=0.3622, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1019, diskUsed=228.5031, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1329 | 1735624523758 | 2024-12-31 13:55:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ef6e4a387d084fe1a52e3e8eeb5ca46f | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624513735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5967, jvmUsedMemory=0.3643, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1025, diskUsed=228.5041, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1329 | 1735624523759 | 2024-12-31 13:55:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ef6e4a387d084fe1a52e3e8eeb5ca46f | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624523739, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4375, jvmUsedMemory=0.3682, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1035, diskUsed=228.5063, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1329 | 1735624523785 | 2024-12-31 13:55:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | ef6e4a387d084fe1a52e3e8eeb5ca46f | - | - | - | - | 29 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1329 | 1735624523786 | 2024-12-31 13:55:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | ef6e4a387d084fe1a52e3e8eeb5ca46f | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1365 | 1735624553756 | 2024-12-31 13:55:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 433a35bf32704b4ea6920fb9643ce4b8 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-120 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624533734, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2163, jvmUsedMemory=0.381, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1071, diskUsed=228.5064, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1365 | 1735624553759 | 2024-12-31 13:55:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 433a35bf32704b4ea6920fb9643ce4b8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-120 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624543737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0352, jvmUsedMemory=0.3835, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1079, diskUsed=228.5065, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1365 | 1735624553760 | 2024-12-31 13:55:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 433a35bf32704b4ea6920fb9643ce4b8 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-120 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624553739, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7954, jvmUsedMemory=0.3869, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1088, diskUsed=228.5057, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1365 | 1735624553791 | 2024-12-31 13:55:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 433a35bf32704b4ea6920fb9643ce4b8 | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-120 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1365 | 1735624553791 | 2024-12-31 13:55:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 433a35bf32704b4ea6920fb9643ce4b8 | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-120 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1364 | 1735624583757 | 2024-12-31 13:56:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 5416c13efb364743b67711ab5c4f4a9e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-119 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624563735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8262, jvmUsedMemory=0.3928, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1105, diskUsed=228.5057, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1364 | 1735624583759 | 2024-12-31 13:56:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 5416c13efb364743b67711ab5c4f4a9e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-119 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624575753, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3252, jvmUsedMemory=0.407, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1145, diskUsed=228.5068, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1364 | 1735624583760 | 2024-12-31 13:56:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 5416c13efb364743b67711ab5c4f4a9e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-119 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624583736, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2988, jvmUsedMemory=0.4105, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1155, diskUsed=228.507, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1364 | 1735624583782 | 2024-12-31 13:56:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 5416c13efb364743b67711ab5c4f4a9e | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-119 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1364 | 1735624583783 | 2024-12-31 13:56:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 5416c13efb364743b67711ab5c4f4a9e | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-119 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1435 | 1735624613764 | 2024-12-31 13:56:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 92b0dc5843124c8f8fc3aba2fd910502 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624593739, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9448, jvmUsedMemory=0.4153, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1168, diskUsed=228.5079, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1435 | 1735624613766 | 2024-12-31 13:56:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 92b0dc5843124c8f8fc3aba2fd910502 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624603738, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9404, jvmUsedMemory=0.4191, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1179, diskUsed=228.5063, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1435 | 1735624613767 | 2024-12-31 13:56:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 92b0dc5843124c8f8fc3aba2fd910502 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624613735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8623, jvmUsedMemory=0.4219, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1187, diskUsed=228.5056, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1435 | 1735624613775 | 2024-12-31 13:56:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 92b0dc5843124c8f8fc3aba2fd910502 | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1435 | 1735624613775 | 2024-12-31 13:56:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 92b0dc5843124c8f8fc3aba2fd910502 | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1472 | 1735624643765 | 2024-12-31 13:57:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | d8dbfd17851a4782b763c6bec708b43c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624623737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.729, jvmUsedMemory=0.4278, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1203, diskUsed=228.5072, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1472 | 1735624643768 | 2024-12-31 13:57:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | d8dbfd17851a4782b763c6bec708b43c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624633737, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7769, jvmUsedMemory=0.4304, jvmMaxMemory=3.5557, jvmMemoryUsage=0.121, diskUsed=228.5075, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1472 | 1735624643769 | 2024-12-31 13:57:23 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | d8dbfd17851a4782b763c6bec708b43c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624643735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7368, jvmUsedMemory=0.4332, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1218, diskUsed=228.5076, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1472 | 1735624643787 | 2024-12-31 13:57:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | d8dbfd17851a4782b763c6bec708b43c | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1472 | 1735624643788 | 2024-12-31 13:57:23 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | d8dbfd17851a4782b763c6bec708b43c | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1510 | 1735624673757 | 2024-12-31 13:57:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 2f8f1aa6215842cd8939e10c962b9463 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-132 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624653735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6167, jvmUsedMemory=0.4386, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1234, diskUsed=228.5076, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1510 | 1735624673770 | 2024-12-31 13:57:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 2f8f1aa6215842cd8939e10c962b9463 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-132 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624663735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3677, jvmUsedMemory=0.4421, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1243, diskUsed=228.5076, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1510 | 1735624673770 | 2024-12-31 13:57:53 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 2f8f1aa6215842cd8939e10c962b9463 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-132 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624673738, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4644, jvmUsedMemory=0.4444, jvmMaxMemory=3.5557, jvmMemoryUsage=0.125, diskUsed=228.5067, diskTotal=460.4317, diskUsage=0.4963, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1510 | 1735624673781 | 2024-12-31 13:57:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2f8f1aa6215842cd8939e10c962b9463 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-132 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1510 | 1735624673782 | 2024-12-31 13:57:53 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2f8f1aa6215842cd8939e10c962b9463 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-132 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1735624680696 | 2024-12-31 13:58:00 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | ed75912579484bf785c172d9454ee874 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1735624680696 | 2024-12-31 13:58:00 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | d79893f3ac6c45d3829e508acacd1dfa | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1735624680698 | 2024-12-31 13:58:00 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | d79893f3ac6c45d3829e508acacd1dfa | - | - | - | - | 2 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1735624680708 | 2024-12-31 13:58:00 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | ed75912579484bf785c172d9454ee874 | - | - | - | - | 12 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 123 | 1735624707574 | 2024-12-31 13:58:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 5514faddd3c148eea28ced07badf6e99 | - | - | - | - | 106 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 123 | 1735624707574 | 2024-12-31 13:58:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 5514faddd3c148eea28ced07badf6e99 | - | - | - | - | 106 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 253 | 1735624728280 | 2024-12-31 13:58:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 64c69e422b35414f811cdaf0d658da10 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624708136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5122, jvmUsedMemory=0.266, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0748, diskUsed=228.4517, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 253 | 1735624728287 | 2024-12-31 13:58:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 64c69e422b35414f811cdaf0d658da10 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624718135, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5801, jvmUsedMemory=0.4101, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1153, diskUsed=228.4518, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 253 | 1735624728288 | 2024-12-31 13:58:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 64c69e422b35414f811cdaf0d658da10 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-39 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624728136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2627, jvmUsedMemory=0.4186, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1177, diskUsed=228.4518, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 123 | 1735624728327 | 2024-12-31 13:58:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 5514faddd3c148eea28ced07badf6e99 | - | - | - | - | 20859 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1735624728328 | 2024-12-31 13:58:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 5514faddd3c148eea28ced07badf6e99 | - | - | - | - | 20860 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1735624728330 | 2024-12-31 13:58:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 5514faddd3c148eea28ced07badf6e99 | - | - | - | - | 20862 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1735624728334 | 2024-12-31 13:58:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 5514faddd3c148eea28ced07badf6e99 | - | - | - | - | 20866 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1735624728334 | 2024-12-31 13:58:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 5514faddd3c148eea28ced07badf6e99 | - | - | - | - | 20866 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 297 | 1735624758171 | 2024-12-31 13:59:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | dfaec965054a4980acc2334dcb3ad409 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624738141, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3145, jvmUsedMemory=0.441, jvmMaxMemory=3.5557, jvmMemoryUsage=0.124, diskUsed=228.4513, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 297 | 1735624758174 | 2024-12-31 13:59:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | dfaec965054a4980acc2334dcb3ad409 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624748137, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8135, jvmUsedMemory=0.4524, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1272, diskUsed=228.4535, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 297 | 1735624758174 | 2024-12-31 13:59:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | dfaec965054a4980acc2334dcb3ad409 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624758139, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7192, jvmUsedMemory=0.458, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1288, diskUsed=228.4545, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 297 | 1735624758206 | 2024-12-31 13:59:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | dfaec965054a4980acc2334dcb3ad409 | - | - | - | - | 34 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 297 | 1735624758207 | 2024-12-31 13:59:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | dfaec965054a4980acc2334dcb3ad409 | - | - | - | - | 35 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-40 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 348 | 1735624788165 | 2024-12-31 13:59:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f55d379196bc4a90ab3019eeae6b8c67 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624768136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.147, jvmUsedMemory=0.4704, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1323, diskUsed=228.4545, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 348 | 1735624788166 | 2024-12-31 13:59:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f55d379196bc4a90ab3019eeae6b8c67 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624778136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8101, jvmUsedMemory=0.484, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1361, diskUsed=228.4545, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 348 | 1735624788167 | 2024-12-31 13:59:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f55d379196bc4a90ab3019eeae6b8c67 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624788136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4512, jvmUsedMemory=0.4925, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1385, diskUsed=228.4547, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 348 | 1735624788180 | 2024-12-31 13:59:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f55d379196bc4a90ab3019eeae6b8c67 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 348 | 1735624788181 | 2024-12-31 13:59:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f55d379196bc4a90ab3019eeae6b8c67 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 389 | 1735624818157 | 2024-12-31 14:00:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e3432243e41542ac935b84f2fff99c85 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624798136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6216, jvmUsedMemory=0.5008, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1409, diskUsed=228.4539, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 389 | 1735624818159 | 2024-12-31 14:00:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e3432243e41542ac935b84f2fff99c85 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624808137, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2983, jvmUsedMemory=0.5139, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1445, diskUsed=228.454, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 389 | 1735624818159 | 2024-12-31 14:00:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e3432243e41542ac935b84f2fff99c85 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624818140, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.252, jvmUsedMemory=0.5204, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1464, diskUsed=228.4551, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 389 | 1735624818169 | 2024-12-31 14:00:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | e3432243e41542ac935b84f2fff99c85 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 389 | 1735624818170 | 2024-12-31 14:00:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | e3432243e41542ac935b84f2fff99c85 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 390 | 1735624848155 | 2024-12-31 14:00:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 416a26e6b26a4357b1ec6449bdebd41f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624828136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4946, jvmUsedMemory=0.5303, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1491, diskUsed=228.4552, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 390 | 1735624848156 | 2024-12-31 14:00:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 416a26e6b26a4357b1ec6449bdebd41f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624838136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.418, jvmUsedMemory=0.5382, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1514, diskUsed=228.4635, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 390 | 1735624848156 | 2024-12-31 14:00:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 416a26e6b26a4357b1ec6449bdebd41f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624848142, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7344, jvmUsedMemory=0.5439, jvmMaxMemory=3.5557, jvmMemoryUsage=0.153, diskUsed=228.4542, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 390 | 1735624848171 | 2024-12-31 14:00:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 416a26e6b26a4357b1ec6449bdebd41f | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 390 | 1735624848171 | 2024-12-31 14:00:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 416a26e6b26a4357b1ec6449bdebd41f | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 430 | 1735624878165 | 2024-12-31 14:01:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f8d7b11ee1ca4e199e90f1e2b00763cf | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624858140, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5474, jvmUsedMemory=0.5535, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1557, diskUsed=228.4535, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 430 | 1735624878167 | 2024-12-31 14:01:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f8d7b11ee1ca4e199e90f1e2b00763cf | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624868141, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.543, jvmUsedMemory=0.562, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1581, diskUsed=228.4535, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 430 | 1735624878167 | 2024-12-31 14:01:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f8d7b11ee1ca4e199e90f1e2b00763cf | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624878140, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6128, jvmUsedMemory=0.5696, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1602, diskUsed=228.4545, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 430 | 1735624878183 | 2024-12-31 14:01:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f8d7b11ee1ca4e199e90f1e2b00763cf | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 430 | 1735624878184 | 2024-12-31 14:01:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f8d7b11ee1ca4e199e90f1e2b00763cf | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 431 | 1735624908165 | 2024-12-31 14:01:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 83d300c5624c4be8aca3ab72d6eb2b56 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624888141, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.438, jvmUsedMemory=0.5794, jvmMaxMemory=3.5557, jvmMemoryUsage=0.163, diskUsed=228.4545, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1735624908167 | 2024-12-31 14:01:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 83d300c5624c4be8aca3ab72d6eb2b56 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624898141, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5303, jvmUsedMemory=0.5862, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1649, diskUsed=228.4556, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1735624908167 | 2024-12-31 14:01:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 83d300c5624c4be8aca3ab72d6eb2b56 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624908140, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2148, jvmUsedMemory=0.5925, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1666, diskUsed=228.4556, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1735624908178 | 2024-12-31 14:01:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 83d300c5624c4be8aca3ab72d6eb2b56 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 431 | 1735624908179 | 2024-12-31 14:01:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 83d300c5624c4be8aca3ab72d6eb2b56 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 506 | 1735624938167 | 2024-12-31 14:02:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a377a1c057ff45faa0dd1e840613056c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624918140, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2422, jvmUsedMemory=0.6016, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1692, diskUsed=228.4546, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 506 | 1735624938184 | 2024-12-31 14:02:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a377a1c057ff45faa0dd1e840613056c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624928136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2046, jvmUsedMemory=0.609, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1713, diskUsed=228.4546, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 506 | 1735624938184 | 2024-12-31 14:02:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a377a1c057ff45faa0dd1e840613056c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624938136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.939, jvmUsedMemory=0.6164, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1733, diskUsed=228.4556, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 506 | 1735624938193 | 2024-12-31 14:02:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a377a1c057ff45faa0dd1e840613056c | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 506 | 1735624938193 | 2024-12-31 14:02:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a377a1c057ff45faa0dd1e840613056c | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 504 | 1735624968159 | 2024-12-31 14:02:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 1e11183bcdcf4920b07d95755bc503bc | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624948136, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9482, jvmUsedMemory=0.6238, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1754, diskUsed=228.4556, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 504 | 1735624968162 | 2024-12-31 14:02:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 1e11183bcdcf4920b07d95755bc503bc | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624958138, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.2725, jvmUsedMemory=0.6311, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1775, diskUsed=228.4556, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 504 | 1735624968163 | 2024-12-31 14:02:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 1e11183bcdcf4920b07d95755bc503bc | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624968138, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=10.0649, jvmUsedMemory=0.6357, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1788, diskUsed=228.4557, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=3)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 504 | 1735624968179 | 2024-12-31 14:02:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 1e11183bcdcf4920b07d95755bc503bc | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 504 | 1735624968181 | 2024-12-31 14:02:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 1e11183bcdcf4920b07d95755bc503bc | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 576 | 1735624998189 | 2024-12-31 14:03:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8850f6dc51a7403ea485e588904684d8 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624978137, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=9.3276, jvmUsedMemory=0.6453, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1815, diskUsed=228.4558, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=4)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 576 | 1735624998191 | 2024-12-31 14:03:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8850f6dc51a7403ea485e588904684d8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624988154, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=8.0469, jvmUsedMemory=0.6532, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1837, diskUsed=228.4559, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=5)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 576 | 1735624998191 | 2024-12-31 14:03:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8850f6dc51a7403ea485e588904684d8 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735624998162, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.0303, jvmUsedMemory=0.6594, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1855, diskUsed=228.4559, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 576 | 1735624998212 | 2024-12-31 14:03:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8850f6dc51a7403ea485e588904684d8 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 576 | 1735624998213 | 2024-12-31 14:03:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8850f6dc51a7403ea485e588904684d8 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 577 | 1735625028186 | 2024-12-31 14:03:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c34ba60115184f6181e7f9b75dd42f20 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625008161, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.4233, jvmUsedMemory=0.6714, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1888, diskUsed=228.4523, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 577 | 1735625028187 | 2024-12-31 14:03:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c34ba60115184f6181e7f9b75dd42f20 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625018164, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.5088, jvmUsedMemory=0.6782, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1907, diskUsed=228.4523, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 577 | 1735625028188 | 2024-12-31 14:03:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c34ba60115184f6181e7f9b75dd42f20 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625028166, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.9751, jvmUsedMemory=0.6855, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1928, diskUsed=228.4525, diskTotal=460.4317, diskUsage=0.4962, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 577 | 1735625028202 | 2024-12-31 14:03:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c34ba60115184f6181e7f9b75dd42f20 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 577 | 1735625028203 | 2024-12-31 14:03:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c34ba60115184f6181e7f9b75dd42f20 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 613 | 1735625058199 | 2024-12-31 14:04:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a9115e91fd034cf597265ebf27a84038 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625038167, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.29, jvmUsedMemory=0.6929, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1949, diskUsed=229.4536, diskTotal=460.4317, diskUsage=0.4983, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 613 | 1735625058213 | 2024-12-31 14:04:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a9115e91fd034cf597265ebf27a84038 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625048168, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2642, jvmUsedMemory=0.7009, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1971, diskUsed=229.4647, diskTotal=460.4317, diskUsage=0.4984, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 613 | 1735625058215 | 2024-12-31 14:04:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a9115e91fd034cf597265ebf27a84038 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625058168, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9219, jvmUsedMemory=0.7068, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1988, diskUsed=229.4661, diskTotal=460.4317, diskUsage=0.4984, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 613 | 1735625058224 | 2024-12-31 14:04:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a9115e91fd034cf597265ebf27a84038 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 613 | 1735625058225 | 2024-12-31 14:04:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a9115e91fd034cf597265ebf27a84038 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 687 | 1735625088198 | 2024-12-31 14:04:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a190f43e6ecd46f3b00c2d34eb35c0b6 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625068171, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.3755, jvmUsedMemory=0.717, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2016, diskUsed=229.468, diskTotal=460.4317, diskUsage=0.4984, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 687 | 1735625088200 | 2024-12-31 14:04:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a190f43e6ecd46f3b00c2d34eb35c0b6 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625078171, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.936, jvmUsedMemory=0.7238, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2036, diskUsed=229.4814, diskTotal=460.4317, diskUsage=0.4984, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 687 | 1735625088200 | 2024-12-31 14:04:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a190f43e6ecd46f3b00c2d34eb35c0b6 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625088170, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7178, jvmUsedMemory=0.7295, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2052, diskUsed=229.483, diskTotal=460.4317, diskUsage=0.4984, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 687 | 1735625088223 | 2024-12-31 14:04:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a190f43e6ecd46f3b00c2d34eb35c0b6 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 687 | 1735625088223 | 2024-12-31 14:04:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a190f43e6ecd46f3b00c2d34eb35c0b6 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 719 | 1735625118199 | 2024-12-31 14:05:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 1b26ff5503ab4d2786cfd556cb1657d2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625098169, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3799, jvmUsedMemory=0.7371, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2073, diskUsed=229.4829, diskTotal=460.4317, diskUsage=0.4984, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 719 | 1735625118201 | 2024-12-31 14:05:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 1b26ff5503ab4d2786cfd556cb1657d2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625108169, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5293, jvmUsedMemory=0.7445, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2094, diskUsed=229.5004, diskTotal=460.4317, diskUsage=0.4984, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 719 | 1735625118202 | 2024-12-31 14:05:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 1b26ff5503ab4d2786cfd556cb1657d2 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625118172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0664, jvmUsedMemory=0.7501, jvmMaxMemory=3.5557, jvmMemoryUsage=0.211, diskUsed=229.5114, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 719 | 1735625118226 | 2024-12-31 14:05:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 1b26ff5503ab4d2786cfd556cb1657d2 | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 719 | 1735625118226 | 2024-12-31 14:05:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 1b26ff5503ab4d2786cfd556cb1657d2 | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 790 | 1735625148198 | 2024-12-31 14:05:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 494a6fa990e8447e889d6d4636fc7b1f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625128168, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9756, jvmUsedMemory=0.7594, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2136, diskUsed=229.5115, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 790 | 1735625148199 | 2024-12-31 14:05:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 494a6fa990e8447e889d6d4636fc7b1f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625138168, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8926, jvmUsedMemory=0.7673, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2158, diskUsed=229.5078, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 790 | 1735625148199 | 2024-12-31 14:05:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 494a6fa990e8447e889d6d4636fc7b1f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625148171, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0625, jvmUsedMemory=0.773, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2174, diskUsed=229.5079, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 790 | 1735625148219 | 2024-12-31 14:05:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 494a6fa990e8447e889d6d4636fc7b1f | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 790 | 1735625148219 | 2024-12-31 14:05:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 494a6fa990e8447e889d6d4636fc7b1f | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 791 | 1735625178191 | 2024-12-31 14:06:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | b95ae2476ea74cc38f3b380988bb04a3 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625158170, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0396, jvmUsedMemory=0.7818, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2199, diskUsed=229.503, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 791 | 1735625178193 | 2024-12-31 14:06:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | b95ae2476ea74cc38f3b380988bb04a3 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625168171, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7192, jvmUsedMemory=0.7886, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2218, diskUsed=229.5043, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 791 | 1735625178196 | 2024-12-31 14:06:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | b95ae2476ea74cc38f3b380988bb04a3 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625178180, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.063, jvmUsedMemory=0.7943, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2234, diskUsed=229.5305, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 791 | 1735625178224 | 2024-12-31 14:06:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | b95ae2476ea74cc38f3b380988bb04a3 | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 791 | 1735625178224 | 2024-12-31 14:06:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | b95ae2476ea74cc38f3b380988bb04a3 | - | - | - | - | 34 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 754 | 1735625208200 | 2024-12-31 14:06:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 2ed4d49937834d6289416f998fd74f65 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625188172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8252, jvmUsedMemory=0.8008, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2252, diskUsed=229.5315, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 754 | 1735625208202 | 2024-12-31 14:06:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 2ed4d49937834d6289416f998fd74f65 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625198169, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7842, jvmUsedMemory=0.8087, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2275, diskUsed=229.5315, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 754 | 1735625208204 | 2024-12-31 14:06:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 2ed4d49937834d6289416f998fd74f65 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625208169, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.897, jvmUsedMemory=0.8158, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2294, diskUsed=229.5312, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 754 | 1735625208219 | 2024-12-31 14:06:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2ed4d49937834d6289416f998fd74f65 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 754 | 1735625208220 | 2024-12-31 14:06:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2ed4d49937834d6289416f998fd74f65 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 894 | 1735625238202 | 2024-12-31 14:07:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 42dec61a1a254809b0370b879b98932f | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625218169, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6045, jvmUsedMemory=0.8261, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2323, diskUsed=229.5306, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 894 | 1735625238206 | 2024-12-31 14:07:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 42dec61a1a254809b0370b879b98932f | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625228171, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2036, jvmUsedMemory=0.8357, jvmMaxMemory=3.5557, jvmMemoryUsage=0.235, diskUsed=229.5314, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 894 | 1735625238207 | 2024-12-31 14:07:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 42dec61a1a254809b0370b879b98932f | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625238168, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1782, jvmUsedMemory=0.8413, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2366, diskUsed=229.5331, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 894 | 1735625238233 | 2024-12-31 14:07:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 42dec61a1a254809b0370b879b98932f | - | - | - | - | 31 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 894 | 1735625238233 | 2024-12-31 14:07:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 42dec61a1a254809b0370b879b98932f | - | - | - | - | 31 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 895 | 1735625268199 | 2024-12-31 14:07:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8fda3bfb2dc548d2b27222208ca5004f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-79 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625248170, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1567, jvmUsedMemory=0.8521, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2397, diskUsed=229.5343, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 895 | 1735625268201 | 2024-12-31 14:07:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8fda3bfb2dc548d2b27222208ca5004f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-79 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625258170, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9722, jvmUsedMemory=0.8595, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2417, diskUsed=229.5339, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 895 | 1735625268201 | 2024-12-31 14:07:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8fda3bfb2dc548d2b27222208ca5004f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-79 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625268173, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8896, jvmUsedMemory=0.8657, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2435, diskUsed=229.5362, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 895 | 1735625268221 | 2024-12-31 14:07:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8fda3bfb2dc548d2b27222208ca5004f | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-79 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 895 | 1735625268221 | 2024-12-31 14:07:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8fda3bfb2dc548d2b27222208ca5004f | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-79 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 965 | 1735625298191 | 2024-12-31 14:08:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 30f3e8411a404018bed9418728f51fed | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625278169, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7871, jvmUsedMemory=0.8748, jvmMaxMemory=3.5557, jvmMemoryUsage=0.246, diskUsed=229.5367, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 965 | 1735625298193 | 2024-12-31 14:08:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 30f3e8411a404018bed9418728f51fed | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625288174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1006, jvmUsedMemory=0.8821, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2481, diskUsed=229.539, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 965 | 1735625298194 | 2024-12-31 14:08:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 30f3e8411a404018bed9418728f51fed | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625298174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.79, jvmUsedMemory=0.8881, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2498, diskUsed=229.54, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 965 | 1735625298217 | 2024-12-31 14:08:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 30f3e8411a404018bed9418728f51fed | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 965 | 1735625298218 | 2024-12-31 14:08:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 30f3e8411a404018bed9418728f51fed | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 930 | 1735625328201 | 2024-12-31 14:08:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c273b23de6594e948f33d8946dfd85af | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-81 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625308174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5942, jvmUsedMemory=0.8978, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2525, diskUsed=229.5402, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 930 | 1735625328204 | 2024-12-31 14:08:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c273b23de6594e948f33d8946dfd85af | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-81 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625318171, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.041, jvmUsedMemory=0.9051, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2546, diskUsed=229.5402, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 930 | 1735625328206 | 2024-12-31 14:08:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c273b23de6594e948f33d8946dfd85af | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-81 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625328172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8677, jvmUsedMemory=0.9108, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2562, diskUsed=229.54, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 930 | 1735625328230 | 2024-12-31 14:08:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c273b23de6594e948f33d8946dfd85af | - | - | - | - | 28 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-81 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 930 | 1735625328231 | 2024-12-31 14:08:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c273b23de6594e948f33d8946dfd85af | - | - | - | - | 29 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-81 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 964 | 1735625358202 | 2024-12-31 14:09:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | bc9a12af93a6411ba856a86148934760 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625338172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6602, jvmUsedMemory=0.9182, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2582, diskUsed=229.5403, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 964 | 1735625358205 | 2024-12-31 14:09:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | bc9a12af93a6411ba856a86148934760 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625348173, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2505, jvmUsedMemory=0.9277, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2609, diskUsed=229.5403, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 964 | 1735625358206 | 2024-12-31 14:09:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | bc9a12af93a6411ba856a86148934760 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625358170, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4258, jvmUsedMemory=0.9345, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2628, diskUsed=229.5404, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 964 | 1735625358222 | 2024-12-31 14:09:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | bc9a12af93a6411ba856a86148934760 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 964 | 1735625358222 | 2024-12-31 14:09:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | bc9a12af93a6411ba856a86148934760 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1036 | 1735625388202 | 2024-12-31 14:09:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e4721e48b77f471fb380e22ec82ad578 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625368171, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3662, jvmUsedMemory=0.9431, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2652, diskUsed=229.5404, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1036 | 1735625388205 | 2024-12-31 14:09:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e4721e48b77f471fb380e22ec82ad578 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625378175, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3091, jvmUsedMemory=0.951, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2675, diskUsed=229.5414, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1036 | 1735625388205 | 2024-12-31 14:09:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e4721e48b77f471fb380e22ec82ad578 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625388175, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1138, jvmUsedMemory=0.9572, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2692, diskUsed=229.5414, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1036 | 1735625388228 | 2024-12-31 14:09:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | e4721e48b77f471fb380e22ec82ad578 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1036 | 1735625388228 | 2024-12-31 14:09:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | e4721e48b77f471fb380e22ec82ad578 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1074 | 1735625418190 | 2024-12-31 14:10:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 131cc8c0cd0744d5b7cf97ab5930e1c5 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625398175, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3423, jvmUsedMemory=0.966, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2717, diskUsed=229.5404, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1074 | 1735625418191 | 2024-12-31 14:10:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 131cc8c0cd0744d5b7cf97ab5930e1c5 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625408175, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.3091, jvmUsedMemory=0.9728, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2736, diskUsed=229.5414, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1074 | 1735625418191 | 2024-12-31 14:10:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 131cc8c0cd0744d5b7cf97ab5930e1c5 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625418172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.1997, jvmUsedMemory=0.9791, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2754, diskUsed=229.5424, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1074 | 1735625418207 | 2024-12-31 14:10:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 131cc8c0cd0744d5b7cf97ab5930e1c5 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1074 | 1735625418207 | 2024-12-31 14:10:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 131cc8c0cd0744d5b7cf97ab5930e1c5 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1106 | 1735625448202 | 2024-12-31 14:10:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 96e8cf361ad8458ebeea3849e93453d3 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625428175, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.0693, jvmUsedMemory=0.9884, jvmMaxMemory=3.5557, jvmMemoryUsage=0.278, diskUsed=229.5424, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1106 | 1735625448203 | 2024-12-31 14:10:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 96e8cf361ad8458ebeea3849e93453d3 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625438175, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.6641, jvmUsedMemory=0.9982, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2807, diskUsed=229.5424, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1106 | 1735625448204 | 2024-12-31 14:10:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 96e8cf361ad8458ebeea3849e93453d3 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625448175, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.9468, jvmUsedMemory=1.0039, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2823, diskUsed=229.5425, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1106 | 1735625448224 | 2024-12-31 14:10:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 96e8cf361ad8458ebeea3849e93453d3 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1106 | 1735625448225 | 2024-12-31 14:10:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 96e8cf361ad8458ebeea3849e93453d3 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1141 | 1735625478233 | 2024-12-31 14:11:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | eda5fa5064254aa9a78174260b98b323 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-95 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625458178, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.8677, jvmUsedMemory=1.0123, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2847, diskUsed=229.5417, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1141 | 1735625478238 | 2024-12-31 14:11:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | eda5fa5064254aa9a78174260b98b323 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-95 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625468173, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.1924, jvmUsedMemory=1.0203, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2869, diskUsed=229.5417, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1141 | 1735625478238 | 2024-12-31 14:11:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | eda5fa5064254aa9a78174260b98b323 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-95 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625478176, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8486, jvmUsedMemory=1.0265, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2887, diskUsed=229.5427, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1141 | 1735625478265 | 2024-12-31 14:11:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | eda5fa5064254aa9a78174260b98b323 | - | - | - | - | 31 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-95 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1141 | 1735625478265 | 2024-12-31 14:11:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | eda5fa5064254aa9a78174260b98b323 | - | - | - | - | 31 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-95 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1142 | 1735625508212 | 2024-12-31 14:11:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 053aac0ce19b4b65ab27094106fb3613 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625488172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6309, jvmUsedMemory=1.0327, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2904, diskUsed=229.5427, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1142 | 1735625508215 | 2024-12-31 14:11:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 053aac0ce19b4b65ab27094106fb3613 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625498172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2324, jvmUsedMemory=1.0395, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2924, diskUsed=229.5437, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1142 | 1735625508216 | 2024-12-31 14:11:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 053aac0ce19b4b65ab27094106fb3613 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625508172, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8086, jvmUsedMemory=1.0452, jvmMaxMemory=3.5557, jvmMemoryUsage=0.294, diskUsed=229.5436, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1142 | 1735625508234 | 2024-12-31 14:11:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 053aac0ce19b4b65ab27094106fb3613 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1142 | 1735625508235 | 2024-12-31 14:11:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 053aac0ce19b4b65ab27094106fb3613 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1206 | 1735625538192 | 2024-12-31 14:12:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 55f4e0cf04b94e63859e3bb5c28be89e | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625518174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4565, jvmUsedMemory=1.0544, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2965, diskUsed=229.5427, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1206 | 1735625538200 | 2024-12-31 14:12:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 55f4e0cf04b94e63859e3bb5c28be89e | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625528180, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.46, jvmUsedMemory=1.0623, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2988, diskUsed=229.5437, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1206 | 1735625538204 | 2024-12-31 14:12:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 55f4e0cf04b94e63859e3bb5c28be89e | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625538174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.709, jvmUsedMemory=1.068, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3004, diskUsed=229.5437, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1206 | 1735625538225 | 2024-12-31 14:12:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 55f4e0cf04b94e63859e3bb5c28be89e | - | - | - | - | 32 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1206 | 1735625538225 | 2024-12-31 14:12:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 55f4e0cf04b94e63859e3bb5c28be89e | - | - | - | - | 32 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1207 | 1735625568204 | 2024-12-31 14:12:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c298d68168b04d018c084df7a39dacc4 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-99 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625548177, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6924, jvmUsedMemory=1.0753, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3024, diskUsed=229.5447, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1207 | 1735625568206 | 2024-12-31 14:12:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c298d68168b04d018c084df7a39dacc4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-99 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625558177, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5117, jvmUsedMemory=1.0827, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3045, diskUsed=229.545, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1207 | 1735625568206 | 2024-12-31 14:12:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c298d68168b04d018c084df7a39dacc4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-99 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625568178, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2856, jvmUsedMemory=1.0897, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3065, diskUsed=229.545, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1207 | 1735625568229 | 2024-12-31 14:12:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c298d68168b04d018c084df7a39dacc4 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-99 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1207 | 1735625568230 | 2024-12-31 14:12:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c298d68168b04d018c084df7a39dacc4 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-99 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1311 | 1735625598206 | 2024-12-31 14:13:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f03a1129474e420b96101ffa3dd865a8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-106 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625578176, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6099, jvmUsedMemory=1.0991, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3091, diskUsed=229.5441, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1311 | 1735625598209 | 2024-12-31 14:13:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f03a1129474e420b96101ffa3dd865a8 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-106 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625588175, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2881, jvmUsedMemory=1.1067, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3113, diskUsed=229.5441, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1311 | 1735625598210 | 2024-12-31 14:13:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f03a1129474e420b96101ffa3dd865a8 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-106 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625598174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0957, jvmUsedMemory=1.113, jvmMaxMemory=3.5557, jvmMemoryUsage=0.313, diskUsed=229.5452, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1311 | 1735625598240 | 2024-12-31 14:13:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f03a1129474e420b96101ffa3dd865a8 | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-106 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1311 | 1735625598241 | 2024-12-31 14:13:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f03a1129474e420b96101ffa3dd865a8 | - | - | - | - | 34 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-106 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1312 | 1735625628206 | 2024-12-31 14:13:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 008d96bd8808472e8881a8a00e3a4c3c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625608177, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3945, jvmUsedMemory=1.1244, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3162, diskUsed=229.5462, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1312 | 1735625628208 | 2024-12-31 14:13:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 008d96bd8808472e8881a8a00e3a4c3c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625618177, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2471, jvmUsedMemory=1.13, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3178, diskUsed=229.5473, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1312 | 1735625628208 | 2024-12-31 14:13:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 008d96bd8808472e8881a8a00e3a4c3c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625628178, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2949, jvmUsedMemory=1.1357, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3194, diskUsed=229.5474, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1312 | 1735625628247 | 2024-12-31 14:13:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 008d96bd8808472e8881a8a00e3a4c3c | - | - | - | - | 40 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1312 | 1735625628247 | 2024-12-31 14:13:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 008d96bd8808472e8881a8a00e3a4c3c | - | - | - | - | 41 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1380 | 1735625658207 | 2024-12-31 14:14:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 45f85a53de8e46779fe58492f23e72aa | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625638175, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1626, jvmUsedMemory=1.1431, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3215, diskUsed=229.5465, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1380 | 1735625658210 | 2024-12-31 14:14:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 45f85a53de8e46779fe58492f23e72aa | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625648174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9834, jvmUsedMemory=1.1499, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3234, diskUsed=229.5465, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1380 | 1735625658211 | 2024-12-31 14:14:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 45f85a53de8e46779fe58492f23e72aa | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625658178, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6777, jvmUsedMemory=1.1556, jvmMaxMemory=3.5557, jvmMemoryUsage=0.325, diskUsed=229.5487, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1380 | 1735625658234 | 2024-12-31 14:14:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 45f85a53de8e46779fe58492f23e72aa | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1380 | 1735625658235 | 2024-12-31 14:14:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 45f85a53de8e46779fe58492f23e72aa | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1381 | 1735625688206 | 2024-12-31 14:14:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e3d6286e09124e0a83448e26236f9b28 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625668174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.874, jvmUsedMemory=1.1644, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3275, diskUsed=229.5497, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1381 | 1735625688208 | 2024-12-31 14:14:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e3d6286e09124e0a83448e26236f9b28 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625678175, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6592, jvmUsedMemory=1.175, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3305, diskUsed=229.5499, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1381 | 1735625688209 | 2024-12-31 14:14:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e3d6286e09124e0a83448e26236f9b28 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625688178, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4771, jvmUsedMemory=1.1807, jvmMaxMemory=3.5557, jvmMemoryUsage=0.332, diskUsed=229.5501, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1381 | 1735625688224 | 2024-12-31 14:14:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | e3d6286e09124e0a83448e26236f9b28 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1381 | 1735625688225 | 2024-12-31 14:14:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | e3d6286e09124e0a83448e26236f9b28 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-110 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1416 | 1735625718194 | 2024-12-31 14:15:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 4de90fbd5df044448402d9305cefa88d | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625698179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4766, jvmUsedMemory=1.1895, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3345, diskUsed=229.5491, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1416 | 1735625718195 | 2024-12-31 14:15:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 4de90fbd5df044448402d9305cefa88d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625708179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4829, jvmUsedMemory=1.1963, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3364, diskUsed=229.5516, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1416 | 1735625718196 | 2024-12-31 14:15:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 4de90fbd5df044448402d9305cefa88d | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625718174, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4946, jvmUsedMemory=1.2028, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3383, diskUsed=229.5526, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1416 | 1735625718211 | 2024-12-31 14:15:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 4de90fbd5df044448402d9305cefa88d | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1416 | 1735625718212 | 2024-12-31 14:15:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 4de90fbd5df044448402d9305cefa88d | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1415 | 1735625748205 | 2024-12-31 14:15:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0968ffd34c834384a47829b31a2d443d | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-112 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625728178, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.498, jvmUsedMemory=1.2115, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3407, diskUsed=229.5526, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1415 | 1735625748207 | 2024-12-31 14:15:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0968ffd34c834384a47829b31a2d443d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-112 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625738177, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4146, jvmUsedMemory=1.2197, jvmMaxMemory=3.5557, jvmMemoryUsage=0.343, diskUsed=229.5526, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1415 | 1735625748208 | 2024-12-31 14:15:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0968ffd34c834384a47829b31a2d443d | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-112 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625748177, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.2769, jvmUsedMemory=1.2259, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3448, diskUsed=229.5526, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1415 | 1735625748236 | 2024-12-31 14:15:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0968ffd34c834384a47829b31a2d443d | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-112 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1415 | 1735625748237 | 2024-12-31 14:15:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0968ffd34c834384a47829b31a2d443d | - | - | - | - | 31 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-112 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1485 | 1735625778195 | 2024-12-31 14:16:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8ad40bed42c44e9ebd265ca9459476ec | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625758176, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.1538, jvmUsedMemory=1.2345, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3472, diskUsed=229.5517, diskTotal=460.4317, diskUsage=0.4986, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1485 | 1735625778196 | 2024-12-31 14:16:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8ad40bed42c44e9ebd265ca9459476ec | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625768176, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5171, jvmUsedMemory=1.2441, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3499, diskUsed=229.5419, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1485 | 1735625778197 | 2024-12-31 14:16:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8ad40bed42c44e9ebd265ca9459476ec | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625778180, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5063, jvmUsedMemory=1.2498, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3515, diskUsed=229.543, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1485 | 1735625778211 | 2024-12-31 14:16:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8ad40bed42c44e9ebd265ca9459476ec | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1485 | 1735625778211 | 2024-12-31 14:16:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8ad40bed42c44e9ebd265ca9459476ec | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-117 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1486 | 1735625808196 | 2024-12-31 14:16:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c053e1ef83a4416b858665b9d138c6b8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-118 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625788176, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0469, jvmUsedMemory=1.2569, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3535, diskUsed=229.543, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1486 | 1735625808199 | 2024-12-31 14:16:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c053e1ef83a4416b858665b9d138c6b8 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-118 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625798179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8789, jvmUsedMemory=1.2648, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3557, diskUsed=229.5431, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1486 | 1735625808199 | 2024-12-31 14:16:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c053e1ef83a4416b858665b9d138c6b8 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-118 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625808179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7432, jvmUsedMemory=1.2713, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3575, diskUsed=229.5431, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1486 | 1735625808214 | 2024-12-31 14:16:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c053e1ef83a4416b858665b9d138c6b8 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-118 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1486 | 1735625808214 | 2024-12-31 14:16:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c053e1ef83a4416b858665b9d138c6b8 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-118 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1555 | 1735625838196 | 2024-12-31 14:17:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 760876910a164ce8a41bbf958e0db03e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-121 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625818181, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8433, jvmUsedMemory=1.2799, jvmMaxMemory=3.5557, jvmMemoryUsage=0.36, diskUsed=229.542, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1555 | 1735625838197 | 2024-12-31 14:17:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 760876910a164ce8a41bbf958e0db03e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-121 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625828179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7866, jvmUsedMemory=1.2878, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3622, diskUsed=229.5392, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1555 | 1735625838198 | 2024-12-31 14:17:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 760876910a164ce8a41bbf958e0db03e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-121 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625838180, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5977, jvmUsedMemory=1.2923, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3635, diskUsed=229.5393, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1555 | 1735625838216 | 2024-12-31 14:17:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 760876910a164ce8a41bbf958e0db03e | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-121 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1555 | 1735625838217 | 2024-12-31 14:17:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 760876910a164ce8a41bbf958e0db03e | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-121 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1590 | 1735625868207 | 2024-12-31 14:17:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 4bb14d05eea44ed092186948b719e0f0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625848180, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8188, jvmUsedMemory=1.3004, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3657, diskUsed=229.5404, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1590 | 1735625868210 | 2024-12-31 14:17:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 4bb14d05eea44ed092186948b719e0f0 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625858180, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7788, jvmUsedMemory=1.3106, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3686, diskUsed=229.5406, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1590 | 1735625868210 | 2024-12-31 14:17:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 4bb14d05eea44ed092186948b719e0f0 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625868179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2607, jvmUsedMemory=1.3163, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3702, diskUsed=229.5367, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1590 | 1735625868227 | 2024-12-31 14:17:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 4bb14d05eea44ed092186948b719e0f0 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1590 | 1735625868227 | 2024-12-31 14:17:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 4bb14d05eea44ed092186948b719e0f0 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1625 | 1735625898202 | 2024-12-31 14:18:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | dee2cf51b4644c26bce5cc2962f4b3e9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625878177, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9126, jvmUsedMemory=0.1596, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0449, diskUsed=229.5358, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1625 | 1735625898204 | 2024-12-31 14:18:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | dee2cf51b4644c26bce5cc2962f4b3e9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625888177, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9927, jvmUsedMemory=0.1674, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0471, diskUsed=229.5358, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1625 | 1735625898205 | 2024-12-31 14:18:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | dee2cf51b4644c26bce5cc2962f4b3e9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625898179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6797, jvmUsedMemory=0.1701, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0478, diskUsed=229.5369, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1625 | 1735625898220 | 2024-12-31 14:18:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | dee2cf51b4644c26bce5cc2962f4b3e9 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1625 | 1735625898221 | 2024-12-31 14:18:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | dee2cf51b4644c26bce5cc2962f4b3e9 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1656 | 1735625928218 | 2024-12-31 14:18:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | df8dfcbd442d45b0b7325b7069231694 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-128 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625908179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8213, jvmUsedMemory=0.1769, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0497, diskUsed=229.537, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1656 | 1735625928220 | 2024-12-31 14:18:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | df8dfcbd442d45b0b7325b7069231694 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-128 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625918179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9219, jvmUsedMemory=0.181, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0509, diskUsed=229.5374, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1656 | 1735625928220 | 2024-12-31 14:18:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | df8dfcbd442d45b0b7325b7069231694 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-128 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625928180, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7861, jvmUsedMemory=0.1838, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0517, diskUsed=229.5376, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1656 | 1735625928243 | 2024-12-31 14:18:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | df8dfcbd442d45b0b7325b7069231694 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-128 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1656 | 1735625928244 | 2024-12-31 14:18:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | df8dfcbd442d45b0b7325b7069231694 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-128 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1657 | 1735625958197 | 2024-12-31 14:19:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8311fdb960ec47c29ff89df34bb2ff2f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625938177, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8989, jvmUsedMemory=0.1882, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0529, diskUsed=229.5367, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1657 | 1735625958198 | 2024-12-31 14:19:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8311fdb960ec47c29ff89df34bb2ff2f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625948180, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6802, jvmUsedMemory=0.1926, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0542, diskUsed=229.5368, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1657 | 1735625958200 | 2024-12-31 14:19:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8311fdb960ec47c29ff89df34bb2ff2f | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625958182, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5688, jvmUsedMemory=0.1953, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0549, diskUsed=229.5376, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1657 | 1735625958212 | 2024-12-31 14:19:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8311fdb960ec47c29ff89df34bb2ff2f | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1657 | 1735625958213 | 2024-12-31 14:19:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8311fdb960ec47c29ff89df34bb2ff2f | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1759 | 1735625988198 | 2024-12-31 14:19:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 13cdf9ae42d1427a9f6ce6df843d1654 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-134 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625968178, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5415, jvmUsedMemory=0.2008, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0565, diskUsed=229.5388, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1759 | 1735625988200 | 2024-12-31 14:19:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 13cdf9ae42d1427a9f6ce6df843d1654 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-134 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625978178, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5317, jvmUsedMemory=0.206, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0579, diskUsed=229.5399, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1759 | 1735625988202 | 2024-12-31 14:19:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 13cdf9ae42d1427a9f6ce6df843d1654 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-134 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625988180, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5229, jvmUsedMemory=0.2096, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0589, diskUsed=229.5398, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1759 | 1735625988219 | 2024-12-31 14:19:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 13cdf9ae42d1427a9f6ce6df843d1654 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-134 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1759 | 1735625988220 | 2024-12-31 14:19:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 13cdf9ae42d1427a9f6ce6df843d1654 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-134 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1724 | 1735626018210 | 2024-12-31 14:20:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 87d08338cd694e9f8bc906b3aa0eac8d | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-131 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735625998178, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5225, jvmUsedMemory=0.2146, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0604, diskUsed=229.5389, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1724 | 1735626018212 | 2024-12-31 14:20:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 87d08338cd694e9f8bc906b3aa0eac8d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-131 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626008179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3618, jvmUsedMemory=0.2187, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0615, diskUsed=229.5398, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1724 | 1735626018212 | 2024-12-31 14:20:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 87d08338cd694e9f8bc906b3aa0eac8d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-131 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626018179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2192, jvmUsedMemory=0.2218, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0624, diskUsed=229.54, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1724 | 1735626018231 | 2024-12-31 14:20:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 87d08338cd694e9f8bc906b3aa0eac8d | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-131 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1724 | 1735626018231 | 2024-12-31 14:20:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 87d08338cd694e9f8bc906b3aa0eac8d | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-131 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1793 | 1735626048219 | 2024-12-31 14:20:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 9b0cb71a8f414c3191e9458902b4697d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-138 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626028181, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.958, jvmUsedMemory=0.2268, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0638, diskUsed=229.54, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1793 | 1735626048222 | 2024-12-31 14:20:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9b0cb71a8f414c3191e9458902b4697d | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-138 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626038182, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5981, jvmUsedMemory=0.2309, jvmMaxMemory=3.5557, jvmMemoryUsage=0.065, diskUsed=229.54, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1793 | 1735626048222 | 2024-12-31 14:20:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 9b0cb71a8f414c3191e9458902b4697d | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-138 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626048179, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4253, jvmUsedMemory=0.2337, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0657, diskUsed=229.54, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1793 | 1735626048236 | 2024-12-31 14:20:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 9b0cb71a8f414c3191e9458902b4697d | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-138 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1793 | 1735626048236 | 2024-12-31 14:20:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 9b0cb71a8f414c3191e9458902b4697d | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-138 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1826 | 1735626078209 | 2024-12-31 14:21:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ac1a5ef98b04498fafbe5a176eff60d8 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-141 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626058184, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2729, jvmUsedMemory=0.24, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0675, diskUsed=229.539, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1825 | 1735626078218 | 2024-12-31 14:21:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 172194349faf4c32971f2feffe1c28d2 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-140 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1825 | 1735626078226 | 2024-12-31 14:21:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 172194349faf4c32971f2feffe1c28d2 | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-140 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1826 | 1735626078227 | 2024-12-31 14:21:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ac1a5ef98b04498fafbe5a176eff60d8 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-141 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626068184, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.397, jvmUsedMemory=0.2442, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0687, diskUsed=229.541, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1826 | 1735626078227 | 2024-12-31 14:21:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ac1a5ef98b04498fafbe5a176eff60d8 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-141 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626078184, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5693, jvmUsedMemory=0.247, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0695, diskUsed=229.541, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1857 | 1735626108199 | 2024-12-31 14:21:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 9512fad6a98e4cb3a0f7da516cd111a0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-142 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626088181, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7822, jvmUsedMemory=0.2513, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0707, diskUsed=229.5412, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1857 | 1735626108201 | 2024-12-31 14:21:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9512fad6a98e4cb3a0f7da516cd111a0 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-142 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626098181, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.748, jvmUsedMemory=0.2562, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0721, diskUsed=229.5437, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1857 | 1735626108202 | 2024-12-31 14:21:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 9512fad6a98e4cb3a0f7da516cd111a0 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-142 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626108181, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.314, jvmUsedMemory=0.2595, jvmMaxMemory=3.5557, jvmMemoryUsage=0.073, diskUsed=229.5437, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1857 | 1735626108219 | 2024-12-31 14:21:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 9512fad6a98e4cb3a0f7da516cd111a0 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-142 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1857 | 1735626108220 | 2024-12-31 14:21:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 9512fad6a98e4cb3a0f7da516cd111a0 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-142 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1890 | 1735626138213 | 2024-12-31 14:22:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | cc7b696f8dd3497aaa485b829d182ec9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-144 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626118184, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7974, jvmUsedMemory=0.2639, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0742, diskUsed=229.544, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1890 | 1735626138214 | 2024-12-31 14:22:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | cc7b696f8dd3497aaa485b829d182ec9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-144 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626128183, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3667, jvmUsedMemory=0.2672, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0751, diskUsed=229.5449, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1890 | 1735626138215 | 2024-12-31 14:22:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | cc7b696f8dd3497aaa485b829d182ec9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-144 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735626138186, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3228, jvmUsedMemory=0.2699, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0759, diskUsed=229.5455, diskTotal=460.4317, diskUsage=0.4985, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1890 | 1735626138235 | 2024-12-31 14:22:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | cc7b696f8dd3497aaa485b829d182ec9 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-144 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1890 | 1735626138240 | 2024-12-31 14:22:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | cc7b696f8dd3497aaa485b829d182ec9 | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-144 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

