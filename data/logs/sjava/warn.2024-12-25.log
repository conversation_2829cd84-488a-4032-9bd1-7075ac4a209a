warning | 123 | 1735093444544 | 2024-12-25 10:24:04 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 84afe26bb2424b86873fe93ae8cdb7cf | - | - | - | - | 113 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 123 | 1735093444544 | 2024-12-25 10:24:04 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 84afe26bb2424b86873fe93ae8cdb7cf | - | - | - | - | 113 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 275 | 1735093465414 | 2024-12-25 10:24:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 1827b097610f41fc9986f91e47115733 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093445260, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0122, jvmUsedMemory=0.4384, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1233, diskUsed=226.2352, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 275 | 1735093465419 | 2024-12-25 10:24:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 1827b097610f41fc9986f91e47115733 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093455262, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9131, jvmUsedMemory=0.6582, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1851, diskUsed=226.2352, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 275 | 1735093465420 | 2024-12-25 10:24:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 1827b097610f41fc9986f91e47115733 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093465260, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.9932, jvmUsedMemory=0.6665, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1874, diskUsed=226.2354, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 123 | 1735093465464 | 2024-12-25 10:24:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 84afe26bb2424b86873fe93ae8cdb7cf | - | - | - | - | 21034 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1735093465465 | 2024-12-25 10:24:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 84afe26bb2424b86873fe93ae8cdb7cf | - | - | - | - | 21034 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1735093465468 | 2024-12-25 10:24:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 84afe26bb2424b86873fe93ae8cdb7cf | - | - | - | - | 21038 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1735093465471 | 2024-12-25 10:24:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 84afe26bb2424b86873fe93ae8cdb7cf | - | - | - | - | 21040 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1735093465471 | 2024-12-25 10:24:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 84afe26bb2424b86873fe93ae8cdb7cf | - | - | - | - | 21040 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 276 | 1735093495288 | 2024-12-25 10:24:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 74adfe2f324d4c508a1c1b7a7429aa17 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093475259, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6929, jvmUsedMemory=0.687, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1932, diskUsed=226.2354, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 276 | 1735093495289 | 2024-12-25 10:24:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 74adfe2f324d4c508a1c1b7a7429aa17 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093485261, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7651, jvmUsedMemory=0.8539, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2402, diskUsed=226.2353, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 276 | 1735093495291 | 2024-12-25 10:24:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 74adfe2f324d4c508a1c1b7a7429aa17 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093495259, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8008, jvmUsedMemory=0.2589, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0728, diskUsed=226.2357, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 276 | 1735093495312 | 2024-12-25 10:24:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 74adfe2f324d4c508a1c1b7a7429aa17 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 276 | 1735093495312 | 2024-12-25 10:24:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 74adfe2f324d4c508a1c1b7a7429aa17 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 365 | 1735093525278 | 2024-12-25 10:25:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 509212b07f5a465ab83e77b57695a1fd | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093505260, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7573, jvmUsedMemory=0.2692, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0757, diskUsed=226.2368, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 365 | 1735093525280 | 2024-12-25 10:25:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 509212b07f5a465ab83e77b57695a1fd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093515261, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.333, jvmUsedMemory=0.2754, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0775, diskUsed=227.2415, diskTotal=460.4317, diskUsage=0.4935, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 365 | 1735093525280 | 2024-12-25 10:25:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 509212b07f5a465ab83e77b57695a1fd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093525261, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7778, jvmUsedMemory=0.2781, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0782, diskUsed=227.2433, diskTotal=460.4317, diskUsage=0.4935, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 365 | 1735093525317 | 2024-12-25 10:25:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 509212b07f5a465ab83e77b57695a1fd | - | - | - | - | 39 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 365 | 1735093525318 | 2024-12-25 10:25:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 509212b07f5a465ab83e77b57695a1fd | - | - | - | - | 39 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 400 | 1735093555288 | 2024-12-25 10:25:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 88e1d61aeb844fb282de5c5b415115b5 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093535261, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.437, jvmUsedMemory=0.2855, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0803, diskUsed=227.2426, diskTotal=460.4317, diskUsage=0.4935, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 400 | 1735093555289 | 2024-12-25 10:25:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 88e1d61aeb844fb282de5c5b415115b5 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093545256, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3628, jvmUsedMemory=0.2899, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0815, diskUsed=227.2439, diskTotal=460.4317, diskUsage=0.4935, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 400 | 1735093555290 | 2024-12-25 10:25:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 88e1d61aeb844fb282de5c5b415115b5 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093555257, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3867, jvmUsedMemory=0.2934, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0825, diskUsed=227.2436, diskTotal=460.4317, diskUsage=0.4935, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 364 | 1735093555305 | 2024-12-25 10:25:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 52b90180e835470b83d970b7a011894e | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 364 | 1735093555306 | 2024-12-25 10:25:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 52b90180e835470b83d970b7a011894e | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 400 | 1735093585252 | 2024-12-25 10:26:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 88e1d61aeb844fb282de5c5b415115b5 | - | - | - | - | 29965 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093565261, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.314, jvmUsedMemory=0.2999, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0843, diskUsed=227.2349, diskTotal=460.4317, diskUsage=0.4935, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 400 | 1735093585254 | 2024-12-25 10:26:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 88e1d61aeb844fb282de5c5b415115b5 | - | - | - | - | 29966 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093575254, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4189, jvmUsedMemory=0.3027, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0851, diskUsed=227.2359, diskTotal=460.4317, diskUsage=0.4935, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 400 | 1735093585254 | 2024-12-25 10:26:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 88e1d61aeb844fb282de5c5b415115b5 | - | - | - | - | 29966 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093585235, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.207, jvmUsedMemory=0.3051, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0858, diskUsed=227.2438, diskTotal=460.4317, diskUsage=0.4935, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 400 | 1735093585272 | 2024-12-25 10:26:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 88e1d61aeb844fb282de5c5b415115b5 | - | - | - | - | 29984 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 400 | 1735093585273 | 2024-12-25 10:26:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 88e1d61aeb844fb282de5c5b415115b5 | - | - | - | - | 29985 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 428 | 1735093615251 | 2024-12-25 10:26:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f552a49729b24262be8163ea4ee62f85 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093595228, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3159, jvmUsedMemory=0.3125, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0879, diskUsed=227.2636, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 428 | 1735093615253 | 2024-12-25 10:26:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f552a49729b24262be8163ea4ee62f85 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093605225, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.187, jvmUsedMemory=0.3155, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0887, diskUsed=227.2655, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 428 | 1735093615254 | 2024-12-25 10:26:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f552a49729b24262be8163ea4ee62f85 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093615224, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0908, jvmUsedMemory=0.3181, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0895, diskUsed=227.2649, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 428 | 1735093615276 | 2024-12-25 10:26:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f552a49729b24262be8163ea4ee62f85 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 428 | 1735093615276 | 2024-12-25 10:26:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f552a49729b24262be8163ea4ee62f85 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 429 | 1735093645258 | 2024-12-25 10:27:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 44035e4ade99485982b4dc46bdbafd17 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093625221, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5923, jvmUsedMemory=0.3238, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0911, diskUsed=227.2659, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1735093645259 | 2024-12-25 10:27:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 44035e4ade99485982b4dc46bdbafd17 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093635221, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4878, jvmUsedMemory=0.3281, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0923, diskUsed=227.2659, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1735093645260 | 2024-12-25 10:27:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 44035e4ade99485982b4dc46bdbafd17 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093645220, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2715, jvmUsedMemory=0.3327, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0936, diskUsed=227.2659, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 429 | 1735093645274 | 2024-12-25 10:27:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 44035e4ade99485982b4dc46bdbafd17 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 429 | 1735093645275 | 2024-12-25 10:27:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 44035e4ade99485982b4dc46bdbafd17 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 504 | 1735093675239 | 2024-12-25 10:27:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | dd517cc7efbb49648b0e5d11febbf7b6 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093655217, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0693, jvmUsedMemory=0.3384, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0952, diskUsed=227.2673, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 504 | 1735093675240 | 2024-12-25 10:27:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | dd517cc7efbb49648b0e5d11febbf7b6 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093665232, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.7979, jvmUsedMemory=0.3414, jvmMaxMemory=3.5557, jvmMemoryUsage=0.096, diskUsed=227.2679, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 504 | 1735093675240 | 2024-12-25 10:27:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | dd517cc7efbb49648b0e5d11febbf7b6 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093675217, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4346, jvmUsedMemory=0.344, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0968, diskUsed=227.2671, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 504 | 1735093675258 | 2024-12-25 10:27:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | dd517cc7efbb49648b0e5d11febbf7b6 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 504 | 1735093675259 | 2024-12-25 10:27:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | dd517cc7efbb49648b0e5d11febbf7b6 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 540 | 1735093705249 | 2024-12-25 10:28:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 75d69092fe704d4ba3dcbe665839bed7 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093685220, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1396, jvmUsedMemory=0.3501, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0985, diskUsed=227.2635, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 540 | 1735093705251 | 2024-12-25 10:28:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 75d69092fe704d4ba3dcbe665839bed7 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093695221, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2651, jvmUsedMemory=0.3539, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0995, diskUsed=227.2642, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 540 | 1735093705251 | 2024-12-25 10:28:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 75d69092fe704d4ba3dcbe665839bed7 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093705220, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0703, jvmUsedMemory=0.3577, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1006, diskUsed=227.2657, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 540 | 1735093705268 | 2024-12-25 10:28:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 75d69092fe704d4ba3dcbe665839bed7 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 540 | 1735093705269 | 2024-12-25 10:28:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 75d69092fe704d4ba3dcbe665839bed7 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 580 | 1735093735238 | 2024-12-25 10:28:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 78b2c6ea27784e2a8995c48de4078da9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093715220, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7578, jvmUsedMemory=0.3631, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1021, diskUsed=227.2667, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 580 | 1735093735242 | 2024-12-25 10:28:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 78b2c6ea27784e2a8995c48de4078da9 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093725219, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7271, jvmUsedMemory=0.366, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1029, diskUsed=227.2653, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 580 | 1735093735244 | 2024-12-25 10:28:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 78b2c6ea27784e2a8995c48de4078da9 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093735217, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.541, jvmUsedMemory=0.3703, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1042, diskUsed=227.2643, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 580 | 1735093735260 | 2024-12-25 10:28:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 78b2c6ea27784e2a8995c48de4078da9 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 580 | 1735093735260 | 2024-12-25 10:28:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 78b2c6ea27784e2a8995c48de4078da9 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 616 | 1735093765247 | 2024-12-25 10:29:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 24419b364fe4464b930f27f999b92163 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093745220, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3838, jvmUsedMemory=0.3807, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1071, diskUsed=227.2652, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 616 | 1735093765248 | 2024-12-25 10:29:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 24419b364fe4464b930f27f999b92163 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093755218, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3311, jvmUsedMemory=0.3839, jvmMaxMemory=3.5557, jvmMemoryUsage=0.108, diskUsed=227.2654, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 616 | 1735093765248 | 2024-12-25 10:29:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 24419b364fe4464b930f27f999b92163 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093765219, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9722, jvmUsedMemory=0.3868, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1088, diskUsed=227.2654, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 616 | 1735093765270 | 2024-12-25 10:29:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 24419b364fe4464b930f27f999b92163 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 616 | 1735093765271 | 2024-12-25 10:29:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 24419b364fe4464b930f27f999b92163 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 617 | 1735093795247 | 2024-12-25 10:29:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 2ec1b563a58f49bbbbe53f82c586dc57 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093775215, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.896, jvmUsedMemory=0.3935, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1107, diskUsed=227.2654, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 617 | 1735093795248 | 2024-12-25 10:29:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 2ec1b563a58f49bbbbe53f82c586dc57 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093785219, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.979, jvmUsedMemory=0.3967, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1116, diskUsed=227.2654, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 617 | 1735093795249 | 2024-12-25 10:29:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 2ec1b563a58f49bbbbe53f82c586dc57 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093795215, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1963, jvmUsedMemory=0.3996, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1124, diskUsed=227.2645, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 617 | 1735093795272 | 2024-12-25 10:29:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2ec1b563a58f49bbbbe53f82c586dc57 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 617 | 1735093795272 | 2024-12-25 10:29:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2ec1b563a58f49bbbbe53f82c586dc57 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 694 | 1735093825247 | 2024-12-25 10:30:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 5dea1774bebd42bab8bdf9927b3e2b40 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093805215, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3193, jvmUsedMemory=0.4065, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1143, diskUsed=227.2655, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 694 | 1735093825249 | 2024-12-25 10:30:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 5dea1774bebd42bab8bdf9927b3e2b40 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093815220, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2026, jvmUsedMemory=0.4097, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1152, diskUsed=227.2653, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 694 | 1735093825250 | 2024-12-25 10:30:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 5dea1774bebd42bab8bdf9927b3e2b40 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093825218, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1255, jvmUsedMemory=0.4127, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1161, diskUsed=227.2654, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 694 | 1735093825271 | 2024-12-25 10:30:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 5dea1774bebd42bab8bdf9927b3e2b40 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 694 | 1735093825272 | 2024-12-25 10:30:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 5dea1774bebd42bab8bdf9927b3e2b40 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 692 | 1735093855245 | 2024-12-25 10:30:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 537a8379b2c8434c95fe67fc3e564c41 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093835218, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0127, jvmUsedMemory=0.4177, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1175, diskUsed=227.2654, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 692 | 1735093855246 | 2024-12-25 10:30:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 537a8379b2c8434c95fe67fc3e564c41 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093845218, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0229, jvmUsedMemory=0.4216, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1186, diskUsed=227.2654, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 692 | 1735093855247 | 2024-12-25 10:30:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 537a8379b2c8434c95fe67fc3e564c41 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093855214, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8779, jvmUsedMemory=0.4248, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1195, diskUsed=227.2644, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 692 | 1735093855257 | 2024-12-25 10:30:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 537a8379b2c8434c95fe67fc3e564c41 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 692 | 1735093855258 | 2024-12-25 10:30:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 537a8379b2c8434c95fe67fc3e564c41 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 797 | 1735093885236 | 2024-12-25 10:31:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | bafdc206717c42318a8777bc2f06936f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093865213, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6626, jvmUsedMemory=0.4301, jvmMaxMemory=3.5557, jvmMemoryUsage=0.121, diskUsed=227.2654, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 797 | 1735093885239 | 2024-12-25 10:31:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | bafdc206717c42318a8777bc2f06936f | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093875216, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8071, jvmUsedMemory=0.4342, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1221, diskUsed=227.2654, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 797 | 1735093885239 | 2024-12-25 10:31:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | bafdc206717c42318a8777bc2f06936f | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093885218, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0093, jvmUsedMemory=0.4369, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1229, diskUsed=227.2666, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 797 | 1735093885259 | 2024-12-25 10:31:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | bafdc206717c42318a8777bc2f06936f | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 797 | 1735093885259 | 2024-12-25 10:31:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | bafdc206717c42318a8777bc2f06936f | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 837 | 1735093915245 | 2024-12-25 10:31:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 964afe55a9564382b14903352f6988eb | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-81 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093895217, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3022, jvmUsedMemory=0.442, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1243, diskUsed=227.2897, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 837 | 1735093915255 | 2024-12-25 10:31:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 964afe55a9564382b14903352f6988eb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-81 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093905217, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1689, jvmUsedMemory=0.4463, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1255, diskUsed=227.2917, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 837 | 1735093915255 | 2024-12-25 10:31:55 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 964afe55a9564382b14903352f6988eb | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-81 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093915215, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9824, jvmUsedMemory=0.4496, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1264, diskUsed=227.2876, diskTotal=460.4317, diskUsage=0.4936, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 837 | 1735093915288 | 2024-12-25 10:31:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 964afe55a9564382b14903352f6988eb | - | - | - | - | 35 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-81 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 837 | 1735093915289 | 2024-12-25 10:31:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 964afe55a9564382b14903352f6988eb | - | - | - | - | 35 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-81 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 836 | 1735093945258 | 2024-12-25 10:32:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a73f08df7c304843b42dcfe7c1d3cb97 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093925213, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8242, jvmUsedMemory=0.4558, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1282, diskUsed=227.2967, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 836 | 1735093945273 | 2024-12-25 10:32:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a73f08df7c304843b42dcfe7c1d3cb97 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093935217, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7769, jvmUsedMemory=0.4595, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1292, diskUsed=227.2984, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 836 | 1735093945274 | 2024-12-25 10:32:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a73f08df7c304843b42dcfe7c1d3cb97 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093945216, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0923, jvmUsedMemory=0.4624, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1301, diskUsed=227.2994, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 836 | 1735093945295 | 2024-12-25 10:32:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a73f08df7c304843b42dcfe7c1d3cb97 | - | - | - | - | 36 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 836 | 1735093945295 | 2024-12-25 10:32:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a73f08df7c304843b42dcfe7c1d3cb97 | - | - | - | - | 36 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-80 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1735093961973 | 2024-12-25 10:32:41 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 29bd9895232049a09c01d8315a241408 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1735093961973 | 2024-12-25 10:32:41 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | d78538d148d34a3f82a886e3eb5c8878 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1735093961974 | 2024-12-25 10:32:41 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 29bd9895232049a09c01d8315a241408 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1735093961983 | 2024-12-25 10:32:41 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | d78538d148d34a3f82a886e3eb5c8878 | - | - | - | - | 10 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 873 | 1735093962030 | 2024-12-25 10:32:42 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 8764621cef69473785bf3516f76a9b09 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 872 | 1735093962045 | 2024-12-25 10:32:42 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 85b0669c1abc4a7a94e9655b3a84c472 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093955213, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2119, jvmUsedMemory=0.4687, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1318, diskUsed=227.3014, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 124 | 1735093987507 | 2024-12-25 10:33:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | e54797d3bfe047a7ad28d0d94929e89e | - | - | - | - | 124 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 124 | 1735093987508 | 2024-12-25 10:33:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | e54797d3bfe047a7ad28d0d94929e89e | - | - | - | - | 124 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 270 | 1735094008328 | 2024-12-25 10:33:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 69ecde7075c445458c6294871686b875 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093988124, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2744, jvmUsedMemory=0.4508, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1268, diskUsed=227.3283, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1735094008339 | 2024-12-25 10:33:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 69ecde7075c445458c6294871686b875 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735093998127, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8438, jvmUsedMemory=0.5694, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1601, diskUsed=227.3297, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1735094008340 | 2024-12-25 10:33:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 69ecde7075c445458c6294871686b875 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735094008125, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3672, jvmUsedMemory=0.6468, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1819, diskUsed=227.3298, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 270 | 1735094008405 | 2024-12-25 10:33:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 69ecde7075c445458c6294871686b875 | - | - | - | - | 66 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 270 | 1735094008406 | 2024-12-25 10:33:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 69ecde7075c445458c6294871686b875 | - | - | - | - | 67 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 270 | 1735094008410 | 2024-12-25 10:33:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 69ecde7075c445458c6294871686b875 | - | - | - | - | 71 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 270 | 1735094008418 | 2024-12-25 10:33:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 69ecde7075c445458c6294871686b875 | - | - | - | - | 79 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 270 | 1735094008419 | 2024-12-25 10:33:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 69ecde7075c445458c6294871686b875 | - | - | - | - | 80 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 131 | 1735094038144 | 2024-12-25 10:33:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | b3d22e527b034b298084c8e9e28540f3 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735094018127, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9292, jvmUsedMemory=0.7945, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2235, diskUsed=227.3299, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 131 | 1735094038152 | 2024-12-25 10:33:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | b3d22e527b034b298084c8e9e28540f3 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735094028125, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9941, jvmUsedMemory=0.8879, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2497, diskUsed=227.3311, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 131 | 1735094038153 | 2024-12-25 10:33:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | b3d22e527b034b298084c8e9e28540f3 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735094038122, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.834, jvmUsedMemory=0.8928, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2511, diskUsed=227.3302, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 131 | 1735094038168 | 2024-12-25 10:33:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | b3d22e527b034b298084c8e9e28540f3 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 131 | 1735094038169 | 2024-12-25 10:33:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | b3d22e527b034b298084c8e9e28540f3 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 360 | 1735094068158 | 2024-12-25 10:34:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 4a06435352184875a4399cae4e2f6574 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735094048127, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6255, jvmUsedMemory=0.9043, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2543, diskUsed=227.3314, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 360 | 1735094068159 | 2024-12-25 10:34:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 4a06435352184875a4399cae4e2f6574 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735094058124, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5225, jvmUsedMemory=0.9111, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2563, diskUsed=227.3287, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 360 | 1735094068159 | 2024-12-25 10:34:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 4a06435352184875a4399cae4e2f6574 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1735094068122, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.916, jvmUsedMemory=0.9164, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2577, diskUsed=227.3277, diskTotal=460.4317, diskUsage=0.4937, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 360 | 1735094068174 | 2024-12-25 10:34:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 4a06435352184875a4399cae4e2f6574 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 360 | 1735094068175 | 2024-12-25 10:34:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 4a06435352184875a4399cae4e2f6574 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1735094075508 | 2024-12-25 10:34:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 88cc09249c974afb83d38c1597d6fe2d | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1735094075508 | 2024-12-25 10:34:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | e71f345537d448189229e31a2d7d093b | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1735094075509 | 2024-12-25 10:34:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | e71f345537d448189229e31a2d7d093b | - | - | - | - | 3 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1735094075512 | 2024-12-25 10:34:35 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 88cc09249c974afb83d38c1597d6fe2d | - | - | - | - | 4 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 360 | 1735094075552 | 2024-12-25 10:34:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 4a06435352184875a4399cae4e2f6574 | - | - | - | - | 7394 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

