info | 1 | 1750045080710 | 2025-06-16 11:38:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 47762 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1750045080693 | 2025-06-16 11:38:00 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 674b11ffc4e4466c8113d6818a04bdb5 | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1750045080723 | 2025-06-16 11:38:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1750045081282 | 2025-06-16 11:38:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 567 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1750045081292 | 2025-06-16 11:38:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 576 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1750045081294 | 2025-06-16 11:38:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 578 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1750045081295 | 2025-06-16 11:38:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 579 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1750045081297 | 2025-06-16 11:38:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 581 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1750045081354 | 2025-06-16 11:38:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 638 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1750045081394 | 2025-06-16 11:38:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 678 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1750045081396 | 2025-06-16 11:38:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 680 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1750045081397 | 2025-06-16 11:38:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 681 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1750045081478 | 2025-06-16 11:38:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 762 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1750045083535 | 2025-06-16 11:38:03 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 2819 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1750045083537 | 2025-06-16 11:38:03 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 2821 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1750045083581 | 2025-06-16 11:38:03 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 2865 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 39 ms. Found 0 JPA repository interfaces.

info | 1 | 1750045083591 | 2025-06-16 11:38:03 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 2876 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1750045083592 | 2025-06-16 11:38:03 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 2877 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1750045083621 | 2025-06-16 11:38:03 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 2905 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.

info | 1 | 1750045084418 | 2025-06-16 11:38:04 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 3705 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$e8e0440b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1750045084437 | 2025-06-16 11:38:04 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 3721 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$67e68983] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1750045084496 | 2025-06-16 11:38:04 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 3780 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$e64f944c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1750045084500 | 2025-06-16 11:38:04 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 3784 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1750045084562 | 2025-06-16 11:38:04 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 3846 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1750045084565 | 2025-06-16 11:38:04 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 3849 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1750045085164 | 2025-06-16 11:38:05 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 4449 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1750045085173 | 2025-06-16 11:38:05 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 4457 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1750045085173 | 2025-06-16 11:38:05 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 4457 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1750045085281 | 2025-06-16 11:38:05 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 4565 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1750045100277 | 2025-06-16 11:38:20 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 19565 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1750045100396 | 2025-06-16 11:38:20 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 19680 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1750045100446 | 2025-06-16 11:38:20 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 19730 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1750045100624 | 2025-06-16 11:38:20 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 19909 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1750045100733 | 2025-06-16 11:38:20 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 20017 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1750045100947 | 2025-06-16 11:38:20 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 20231 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1750045100957 | 2025-06-16 11:38:20 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 54c1ab4db6704940997f36f4dcc81e99 | - | - | - | - | 20241 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

