warning | 127 | 1731481987539 | 2024-11-13 15:13:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | b54269cf4c7a4b96a1fba09159f4a390 | - | - | - | - | 137 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 127 | 1731481987540 | 2024-11-13 15:13:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | b54269cf4c7a4b96a1fba09159f4a390 | - | - | - | - | 138 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 229 | 1731481990109 | 2024-11-13 15:13:10 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | e5be10b13de348039ec2a4731eed1531 | - | - | - | - | 1480 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1347 ms]

warning | 272 | 1731482008560 | 2024-11-13 15:13:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | d5d6f0cc68564adba580d39c8b21b3e4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731481988338, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6084, jvmUsedMemory=1.27, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3572, diskUsed=225.2596, diskTotal=460.4317, diskUsage=0.4892, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 272 | 1731482008566 | 2024-11-13 15:13:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | d5d6f0cc68564adba580d39c8b21b3e4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731481998342, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=7.5596, jvmUsedMemory=0.3172, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0892, diskUsed=226.2596, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=6)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 272 | 1731482008566 | 2024-11-13 15:13:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | d5d6f0cc68564adba580d39c8b21b3e4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482008338, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=6.6372, jvmUsedMemory=0.3257, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0916, diskUsed=226.2604, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=7)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 127 | 1731482008601 | 2024-11-13 15:13:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | b54269cf4c7a4b96a1fba09159f4a390 | - | - | - | - | 21199 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1731482008602 | 2024-11-13 15:13:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | b54269cf4c7a4b96a1fba09159f4a390 | - | - | - | - | 21200 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1731482008604 | 2024-11-13 15:13:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | b54269cf4c7a4b96a1fba09159f4a390 | - | - | - | - | 21202 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1731482008607 | 2024-11-13 15:13:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | b54269cf4c7a4b96a1fba09159f4a390 | - | - | - | - | 21205 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1731482008607 | 2024-11-13 15:13:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | b54269cf4c7a4b96a1fba09159f4a390 | - | - | - | - | 21205 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 273 | 1731482038363 | 2024-11-13 15:13:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e32751bdae1b4cc192cf0c8e2521714d | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482018340, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.6899, jvmUsedMemory=0.3526, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0992, diskUsed=226.2486, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 273 | 1731482038365 | 2024-11-13 15:13:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e32751bdae1b4cc192cf0c8e2521714d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482028340, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.1221, jvmUsedMemory=0.3593, jvmMaxMemory=3.5557, jvmMemoryUsage=0.101, diskUsed=226.2488, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 273 | 1731482038366 | 2024-11-13 15:13:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e32751bdae1b4cc192cf0c8e2521714d | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482038341, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.2305, jvmUsedMemory=0.3659, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1029, diskUsed=226.2498, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 273 | 1731482038377 | 2024-11-13 15:13:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | e32751bdae1b4cc192cf0c8e2521714d | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 273 | 1731482038377 | 2024-11-13 15:13:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | e32751bdae1b4cc192cf0c8e2521714d | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 315 | 1731482068363 | 2024-11-13 15:14:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 47eb889a54a540e7ab3d3505be55e3a6 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482048342, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.6597, jvmUsedMemory=0.3781, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1063, diskUsed=226.2508, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 315 | 1731482068365 | 2024-11-13 15:14:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 47eb889a54a540e7ab3d3505be55e3a6 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482058339, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4102, jvmUsedMemory=0.3852, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1083, diskUsed=226.2498, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 315 | 1731482068366 | 2024-11-13 15:14:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 47eb889a54a540e7ab3d3505be55e3a6 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482068343, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0327, jvmUsedMemory=0.3919, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1102, diskUsed=226.2519, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 315 | 1731482068386 | 2024-11-13 15:14:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 47eb889a54a540e7ab3d3505be55e3a6 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 315 | 1731482068387 | 2024-11-13 15:14:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 47eb889a54a540e7ab3d3505be55e3a6 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 392 | 1731482098367 | 2024-11-13 15:14:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 59393061bfbc41ffb5f03a0fd31cf26b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482078343, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5659, jvmUsedMemory=0.402, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1131, diskUsed=226.2529, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 357 | 1731482098379 | 2024-11-13 15:14:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | a4cf438340224b1e92872274aff89194 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 392 | 1731482098386 | 2024-11-13 15:14:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 59393061bfbc41ffb5f03a0fd31cf26b | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482088343, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2383, jvmUsedMemory=0.4073, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1145, diskUsed=226.2529, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 357 | 1731482098386 | 2024-11-13 15:14:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | a4cf438340224b1e92872274aff89194 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 392 | 1731482098387 | 2024-11-13 15:14:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 59393061bfbc41ffb5f03a0fd31cf26b | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482098344, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8936, jvmUsedMemory=0.412, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1159, diskUsed=226.2539, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1731482128365 | 2024-11-13 15:15:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 66b597433b8244c9bf0f45c6b136d3db | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482108342, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4438, jvmUsedMemory=0.4251, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1196, diskUsed=226.2549, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1731482128367 | 2024-11-13 15:15:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 66b597433b8244c9bf0f45c6b136d3db | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482118340, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.834, jvmUsedMemory=0.4303, jvmMaxMemory=3.5557, jvmMemoryUsage=0.121, diskUsed=226.2539, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1731482128368 | 2024-11-13 15:15:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 66b597433b8244c9bf0f45c6b136d3db | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482128341, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6318, jvmUsedMemory=0.4361, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1227, diskUsed=226.2557, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1731482128388 | 2024-11-13 15:15:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 66b597433b8244c9bf0f45c6b136d3db | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 393 | 1731482128388 | 2024-11-13 15:15:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 66b597433b8244c9bf0f45c6b136d3db | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 431 | 1731482158364 | 2024-11-13 15:15:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 664641281ba9412b9b2407a6ab2f5115 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482138344, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1465, jvmUsedMemory=0.4448, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1251, diskUsed=226.2589, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1731482158366 | 2024-11-13 15:15:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 664641281ba9412b9b2407a6ab2f5115 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482148340, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2832, jvmUsedMemory=0.4505, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1267, diskUsed=226.2589, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1731482158366 | 2024-11-13 15:15:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 664641281ba9412b9b2407a6ab2f5115 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482158342, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2456, jvmUsedMemory=0.4563, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1283, diskUsed=226.2599, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1731482158381 | 2024-11-13 15:15:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 664641281ba9412b9b2407a6ab2f5115 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 431 | 1731482158382 | 2024-11-13 15:15:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 664641281ba9412b9b2407a6ab2f5115 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 469 | 1731482188385 | 2024-11-13 15:16:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 5dd39a770ea14aefaa6c9fd0b0d5a741 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482168344, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9063, jvmUsedMemory=0.4659, jvmMaxMemory=3.5557, jvmMemoryUsage=0.131, diskUsed=226.2609, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 469 | 1731482188386 | 2024-11-13 15:16:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 5dd39a770ea14aefaa6c9fd0b0d5a741 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482178345, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6929, jvmUsedMemory=0.5299, jvmMaxMemory=3.5557, jvmMemoryUsage=0.149, diskUsed=226.261, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 469 | 1731482188387 | 2024-11-13 15:16:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 5dd39a770ea14aefaa6c9fd0b0d5a741 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482188345, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3584, jvmUsedMemory=0.5366, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1509, diskUsed=226.2572, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 469 | 1731482188403 | 2024-11-13 15:16:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 5dd39a770ea14aefaa6c9fd0b0d5a741 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 469 | 1731482188404 | 2024-11-13 15:16:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 5dd39a770ea14aefaa6c9fd0b0d5a741 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 468 | 1731482231235 | 2024-11-13 15:17:11 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3bb78a25785f4a89a406766fa73e81e7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482198341, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5498, jvmUsedMemory=0.5443, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1531, diskUsed=226.2592, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 468 | 1731482231270 | 2024-11-13 15:17:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 3bb78a25785f4a89a406766fa73e81e7 | - | - | - | - | 35 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 468 | 1731482231273 | 2024-11-13 15:17:11 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 3bb78a25785f4a89a406766fa73e81e7 | - | - | - | - | 38 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 552 | 1731482258367 | 2024-11-13 15:17:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 79b8f13fd59747d3abb04a8cfd175624 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482238342, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2939, jvmUsedMemory=0.6081, jvmMaxMemory=3.5557, jvmMemoryUsage=0.171, diskUsed=226.2636, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 552 | 1731482258369 | 2024-11-13 15:17:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 79b8f13fd59747d3abb04a8cfd175624 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482248346, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2354, jvmUsedMemory=0.6153, jvmMaxMemory=3.5557, jvmMemoryUsage=0.173, diskUsed=226.264, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 552 | 1731482258369 | 2024-11-13 15:17:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 79b8f13fd59747d3abb04a8cfd175624 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731482258346, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5127, jvmUsedMemory=0.6214, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1748, diskUsed=226.266, diskTotal=460.4317, diskUsage=0.4914, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 552 | 1731482258385 | 2024-11-13 15:17:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 79b8f13fd59747d3abb04a8cfd175624 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 552 | 1731482258386 | 2024-11-13 15:17:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 79b8f13fd59747d3abb04a8cfd175624 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1731482284796 | 2024-11-13 15:18:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | ecb48630ed48448ea58ef22e4776dc9f | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1731482284797 | 2024-11-13 15:18:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 93db8245608e4fe58eb9c57649b80c55 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1731482284802 | 2024-11-13 15:18:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | ecb48630ed48448ea58ef22e4776dc9f | - | - | - | - | 5 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1731482284807 | 2024-11-13 15:18:04 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 93db8245608e4fe58eb9c57649b80c55 | - | - | - | - | 10 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 630 | 1731482284860 | 2024-11-13 15:18:04 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | d10bc45d42bd49ec99d546c61d95da2e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 134 | 1731483900702 | 2024-11-13 15:45:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | bced89d74ddf4745a7374544a3be3420 | - | - | - | - | 221 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 134 | 1731483900703 | 2024-11-13 15:45:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | bced89d74ddf4745a7374544a3be3420 | - | - | - | - | 222 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 236 | 1731483903506 | 2024-11-13 15:45:03 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | f2959ff2e674450481bfb46b806719d9 | - | - | - | - | 1444 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1283 ms]

warning | 271 | 1731483921968 | 2024-11-13 15:45:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 035781acbe9d47c4842bb328725e54d8 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731483901609, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5669, jvmUsedMemory=0.3082, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0867, diskUsed=225.8686, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 271 | 1731483921980 | 2024-11-13 15:45:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 035781acbe9d47c4842bb328725e54d8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731483911605, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6333, jvmUsedMemory=0.466, jvmMaxMemory=3.5557, jvmMemoryUsage=0.131, diskUsed=225.8686, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 271 | 1731483921981 | 2024-11-13 15:45:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 035781acbe9d47c4842bb328725e54d8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731483921606, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4492, jvmUsedMemory=0.4727, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1329, diskUsed=225.8686, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 134 | 1731483922025 | 2024-11-13 15:45:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | bced89d74ddf4745a7374544a3be3420 | - | - | - | - | 21544 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 134 | 1731483922026 | 2024-11-13 15:45:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | bced89d74ddf4745a7374544a3be3420 | - | - | - | - | 21545 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 134 | 1731483922027 | 2024-11-13 15:45:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | bced89d74ddf4745a7374544a3be3420 | - | - | - | - | 21546 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 134 | 1731483922030 | 2024-11-13 15:45:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | bced89d74ddf4745a7374544a3be3420 | - | - | - | - | 21549 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 134 | 1731483922030 | 2024-11-13 15:45:22 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | bced89d74ddf4745a7374544a3be3420 | - | - | - | - | 21549 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 315 | 1731483951633 | 2024-11-13 15:45:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 604ff998b67d4d199334e41306d59237 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731483931606, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.146, jvmUsedMemory=0.497, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1398, diskUsed=225.8696, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 315 | 1731483951634 | 2024-11-13 15:45:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 604ff998b67d4d199334e41306d59237 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731483941610, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1904, jvmUsedMemory=0.5058, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1423, diskUsed=225.8698, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 315 | 1731483951634 | 2024-11-13 15:45:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 604ff998b67d4d199334e41306d59237 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731483951611, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0132, jvmUsedMemory=0.5121, jvmMaxMemory=3.5557, jvmMemoryUsage=0.144, diskUsed=225.8698, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 315 | 1731483951651 | 2024-11-13 15:45:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 604ff998b67d4d199334e41306d59237 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 315 | 1731483951651 | 2024-11-13 15:45:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 604ff998b67d4d199334e41306d59237 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 272 | 1731483981633 | 2024-11-13 15:46:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8c14f521bec34f3cbb9a71eb4a348720 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731483961609, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7769, jvmUsedMemory=0.5246, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1475, diskUsed=225.8707, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 272 | 1731483981634 | 2024-11-13 15:46:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8c14f521bec34f3cbb9a71eb4a348720 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731483971607, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.583, jvmUsedMemory=0.5313, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1494, diskUsed=225.8707, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 272 | 1731483981634 | 2024-11-13 15:46:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8c14f521bec34f3cbb9a71eb4a348720 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731483981606, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4863, jvmUsedMemory=0.5384, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1514, diskUsed=225.8707, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 272 | 1731483981651 | 2024-11-13 15:46:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8c14f521bec34f3cbb9a71eb4a348720 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 272 | 1731483981652 | 2024-11-13 15:46:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8c14f521bec34f3cbb9a71eb4a348720 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 392 | 1731484011636 | 2024-11-13 15:46:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 47c1147ded5943fe88cd8edb391baa5f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731483991607, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3311, jvmUsedMemory=0.5499, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1547, diskUsed=225.8707, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1731484011637 | 2024-11-13 15:46:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 47c1147ded5943fe88cd8edb391baa5f | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484001606, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6733, jvmUsedMemory=0.5551, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1561, diskUsed=225.8717, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1731484011641 | 2024-11-13 15:46:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 47c1147ded5943fe88cd8edb391baa5f | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484011612, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0049, jvmUsedMemory=0.5603, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1576, diskUsed=225.8719, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 356 | 1731484011658 | 2024-11-13 15:46:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 0640fd18f78e41adbac4a788b08e6673 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 356 | 1731484011658 | 2024-11-13 15:46:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 0640fd18f78e41adbac4a788b08e6673 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 431 | 1731484041633 | 2024-11-13 15:47:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | d8fd91746dc042a88b7b8e4edfb77693 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484021609, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2314, jvmUsedMemory=0.5729, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1611, diskUsed=225.8719, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1731484041635 | 2024-11-13 15:47:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | d8fd91746dc042a88b7b8e4edfb77693 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484031610, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1221, jvmUsedMemory=0.5791, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1629, diskUsed=225.8719, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1731484041636 | 2024-11-13 15:47:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | d8fd91746dc042a88b7b8e4edfb77693 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484041611, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1025, jvmUsedMemory=0.5856, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1647, diskUsed=225.871, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1731484041651 | 2024-11-13 15:47:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | d8fd91746dc042a88b7b8e4edfb77693 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 431 | 1731484041652 | 2024-11-13 15:47:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | d8fd91746dc042a88b7b8e4edfb77693 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 393 | 1731484071634 | 2024-11-13 15:47:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0028b5c5ffa94c3b960d969be80d83dd | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484051609, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1792, jvmUsedMemory=0.595, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1674, diskUsed=225.872, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1731484071635 | 2024-11-13 15:47:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0028b5c5ffa94c3b960d969be80d83dd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484061613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2983, jvmUsedMemory=0.6016, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1692, diskUsed=225.873, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1731484071636 | 2024-11-13 15:47:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0028b5c5ffa94c3b960d969be80d83dd | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484071608, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2588, jvmUsedMemory=0.6077, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1709, diskUsed=225.873, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 393 | 1731484071650 | 2024-11-13 15:47:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0028b5c5ffa94c3b960d969be80d83dd | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 393 | 1731484071650 | 2024-11-13 15:47:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0028b5c5ffa94c3b960d969be80d83dd | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 467 | 1731484101646 | 2024-11-13 15:48:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 4ba8ab5f94eb4fe0adbbe038886532ba | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484081613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0713, jvmUsedMemory=0.6188, jvmMaxMemory=3.5557, jvmMemoryUsage=0.174, diskUsed=225.8728, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1731484101649 | 2024-11-13 15:48:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 4ba8ab5f94eb4fe0adbbe038886532ba | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484091612, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1338, jvmUsedMemory=0.6254, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1759, diskUsed=225.8729, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1731484101650 | 2024-11-13 15:48:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 4ba8ab5f94eb4fe0adbbe038886532ba | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484101614, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9658, jvmUsedMemory=0.6306, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1773, diskUsed=225.8719, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1731484101670 | 2024-11-13 15:48:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 4ba8ab5f94eb4fe0adbbe038886532ba | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 467 | 1731484101670 | 2024-11-13 15:48:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 4ba8ab5f94eb4fe0adbbe038886532ba | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 542 | 1731484131637 | 2024-11-13 15:48:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 9fdaa4d16ea549e6a694f4a9f4620ad7 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484111611, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0571, jvmUsedMemory=0.6398, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1799, diskUsed=225.8729, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 542 | 1731484131639 | 2024-11-13 15:48:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9fdaa4d16ea549e6a694f4a9f4620ad7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484121613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9678, jvmUsedMemory=0.646, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1817, diskUsed=225.8739, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 542 | 1731484131640 | 2024-11-13 15:48:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 9fdaa4d16ea549e6a694f4a9f4620ad7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-60 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484131609, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4595, jvmUsedMemory=0.6512, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1832, diskUsed=225.8739, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 543 | 1731484131655 | 2024-11-13 15:48:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 6cc431db30f1408d97a8b72329253df1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 543 | 1731484131656 | 2024-11-13 15:48:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 6cc431db30f1408d97a8b72329253df1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 507 | 1731484161638 | 2024-11-13 15:49:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 76cf622500274dcf8d118a375dd169c9 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484141614, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5356, jvmUsedMemory=0.6614, jvmMaxMemory=3.5557, jvmMemoryUsage=0.186, diskUsed=225.8749, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 507 | 1731484161641 | 2024-11-13 15:49:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 76cf622500274dcf8d118a375dd169c9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484151613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2256, jvmUsedMemory=0.6687, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1881, diskUsed=225.8749, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 507 | 1731484161642 | 2024-11-13 15:49:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 76cf622500274dcf8d118a375dd169c9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484161610, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1777, jvmUsedMemory=0.6744, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1897, diskUsed=225.8749, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 507 | 1731484161660 | 2024-11-13 15:49:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 76cf622500274dcf8d118a375dd169c9 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 507 | 1731484161660 | 2024-11-13 15:49:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 76cf622500274dcf8d118a375dd169c9 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 619 | 1731484191646 | 2024-11-13 15:49:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | fe636c677c194e178771ea4f6621c3bf | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484171615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1563, jvmUsedMemory=0.6826, jvmMaxMemory=3.5557, jvmMemoryUsage=0.192, diskUsed=225.875, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 619 | 1731484191648 | 2024-11-13 15:49:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | fe636c677c194e178771ea4f6621c3bf | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484181615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5063, jvmUsedMemory=0.6888, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1937, diskUsed=225.8751, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 619 | 1731484191649 | 2024-11-13 15:49:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | fe636c677c194e178771ea4f6621c3bf | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484191615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4404, jvmUsedMemory=0.6945, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1953, diskUsed=225.8751, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 619 | 1731484191661 | 2024-11-13 15:49:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | fe636c677c194e178771ea4f6621c3bf | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 619 | 1731484191661 | 2024-11-13 15:49:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | fe636c677c194e178771ea4f6621c3bf | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 582 | 1731484221649 | 2024-11-13 15:50:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 204ce41ca5b948f09a7f21558b5460ec | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484201614, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.625, jvmUsedMemory=0.7055, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1984, diskUsed=225.8751, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 582 | 1731484221652 | 2024-11-13 15:50:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 204ce41ca5b948f09a7f21558b5460ec | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484211613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7495, jvmUsedMemory=0.7117, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2002, diskUsed=225.8751, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 582 | 1731484221654 | 2024-11-13 15:50:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 204ce41ca5b948f09a7f21558b5460ec | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484221612, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6274, jvmUsedMemory=0.7178, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2019, diskUsed=225.8762, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 582 | 1731484221670 | 2024-11-13 15:50:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 204ce41ca5b948f09a7f21558b5460ec | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 582 | 1731484221671 | 2024-11-13 15:50:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 204ce41ca5b948f09a7f21558b5460ec | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 620 | 1731484251637 | 2024-11-13 15:50:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7b899539c048456e849c96649f6136c3 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484231613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2231, jvmUsedMemory=0.7258, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2041, diskUsed=225.8761, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 620 | 1731484251639 | 2024-11-13 15:50:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7b899539c048456e849c96649f6136c3 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484241614, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9546, jvmUsedMemory=0.731, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2056, diskUsed=225.8763, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 620 | 1731484251641 | 2024-11-13 15:50:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7b899539c048456e849c96649f6136c3 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484251612, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0347, jvmUsedMemory=0.7362, jvmMaxMemory=3.5557, jvmMemoryUsage=0.207, diskUsed=225.8763, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 620 | 1731484251660 | 2024-11-13 15:50:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 7b899539c048456e849c96649f6136c3 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 620 | 1731484251662 | 2024-11-13 15:50:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 7b899539c048456e849c96649f6136c3 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 689 | 1731484281639 | 2024-11-13 15:51:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 9653fddc61a94bcaae5f169c03eac8e5 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484261615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2627, jvmUsedMemory=0.7464, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2099, diskUsed=225.8763, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 689 | 1731484281640 | 2024-11-13 15:51:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9653fddc61a94bcaae5f169c03eac8e5 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484271612, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2178, jvmUsedMemory=0.754, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2121, diskUsed=225.8764, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 689 | 1731484281640 | 2024-11-13 15:51:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 9653fddc61a94bcaae5f169c03eac8e5 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484281613, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2642, jvmUsedMemory=0.7592, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2135, diskUsed=225.8755, diskTotal=460.4317, diskUsage=0.4906, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 689 | 1731484281656 | 2024-11-13 15:51:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 9653fddc61a94bcaae5f169c03eac8e5 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 689 | 1731484281657 | 2024-11-13 15:51:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 9653fddc61a94bcaae5f169c03eac8e5 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-69 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 728 | 1731484311639 | 2024-11-13 15:51:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 757a6bc1569b42629cc2f90a4fb30e72 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484291615, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9956, jvmUsedMemory=0.7698, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2165, diskUsed=226.8784, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 728 | 1731484311641 | 2024-11-13 15:51:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 757a6bc1569b42629cc2f90a4fb30e72 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484301616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7617, jvmUsedMemory=0.7755, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2181, diskUsed=226.8782, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 728 | 1731484311641 | 2024-11-13 15:51:51 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 757a6bc1569b42629cc2f90a4fb30e72 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484311618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.125, jvmUsedMemory=0.7812, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2197, diskUsed=226.8784, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 728 | 1731484311671 | 2024-11-13 15:51:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 757a6bc1569b42629cc2f90a4fb30e72 | - | - | - | - | 32 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 728 | 1731484311671 | 2024-11-13 15:51:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 757a6bc1569b42629cc2f90a4fb30e72 | - | - | - | - | 32 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 174 | 1731484335173 | 2024-11-13 15:52:15 | v2/orgStatistic/findConsortiaPerDayList | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.9 | *************** | - | 2 | orgStatistic | findConsortiaPerDayList | 9678b1a1e9d74945acb650db5666be13 | - | - | - | - | 6551 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.c.w.SpringExceptionHandler 出错了啊！请骚候再试[j01000]
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Unknown column 'shell_amount' in 'field list'
### The error may exist in file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/mapper/live/LiveConsortiaStatisticsDayMapper.xml]
### The error may involve cn.taqu.gonghui.live.mapper.LiveConsortiaStatisticsDayMapper.queryByCondition-Inline
### The error occurred while setting parameters
### SQL: SELECT     consortia_id consortiaId,     day_time dayTime,     host_num hostNum,     amount amount,     flower flower,     total_live_time totalLiveTime,     viewer viewer,     send send,     fans fans,     message message,     game_amount gameAmount,     shell_amount shellAmount,     system_activity_amount systemActivityAmount     FROM     live_consortia_statistics_day      WHERE  consortia_id in         (           ?         )                       and day_time  >=  ?                       and day_time  <=  ?
### Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Unknown column 'shell_amount' in 'field list'
; bad SQL grammar []; nested exception is com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Unknown column 'shell_amount' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy170.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy293.queryByCondition(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at cn.taqu.core.jdbc.datasource.DataSourceInterceptor.proceed(DataSourceInterceptor.java:38)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy294.queryByCondition(Unknown Source)
	at cn.taqu.gonghui.system.service.impl.OrgStatisticsServiceImpl.getOrgDailyStatisticsByUser(OrgStatisticsServiceImpl.java:569)
	at cn.taqu.gonghui.controller.user.OrgStatisticsController.findConsortiaPerDayList(OrgStatisticsController.java:104)
	at cn.taqu.gonghui.controller.user.OrgStatisticsController$$FastClassBySpringCGLIB$$777d8ac5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.security.access.intercept.aopalliance.MethodSecurityInterceptor.invoke(MethodSecurityInterceptor.java:61)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at cn.taqu.gonghui.controller.user.OrgStatisticsController$$EnhancerBySpringCGLIB$$bfd86efe.findConsortiaPerDayList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:54)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.taqu.core.web.filter.SoaFilter.doFilterInternal(SoaFilter.java:38)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.taqu.core.web.filter.healthy.HealthyFilter.doFilterInternal(HealthyFilter.java:45)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Unknown column 'shell_amount' in 'field list'
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:411)
	at com.mysql.jdbc.Util.getInstance(Util.java:386)
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:1053)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:4096)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:4028)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2490)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:2651)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2734)
	at com.mysql.jdbc.PreparedStatement.executeInternal(PreparedStatement.java:2155)
	at com.mysql.jdbc.PreparedStatement.execute(PreparedStatement.java:1379)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3409)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3407)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3407)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy357.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at cn.taqu.core.jdbc.datasource.SqlExecuteTimeCountInterceptor.intercept(SqlExecuteTimeCountInterceptor.java:50)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy356.query(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy356.query(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:69)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:64)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy355.query(Unknown Source)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:108)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy355.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 164 common frames omitted

org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Unknown column 'shell_amount' in 'field list'
### The error may exist in file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/mapper/live/LiveConsortiaStatisticsDayMapper.xml]
### The error may involve cn.taqu.gonghui.live.mapper.LiveConsortiaStatisticsDayMapper.queryByCondition-Inline
### The error occurred while setting parameters
### SQL: SELECT     consortia_id consortiaId,     day_time dayTime,     host_num hostNum,     amount amount,     flower flower,     total_live_time totalLiveTime,     viewer viewer,     send send,     fans fans,     message message,     game_amount gameAmount,     shell_amount shellAmount,     system_activity_amount systemActivityAmount     FROM     live_consortia_statistics_day      WHERE  consortia_id in         (           ?         )                       and day_time  >=  ?                       and day_time  <=  ?
### Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Unknown column 'shell_amount' in 'field list'
; bad SQL grammar []; nested exception is com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Unknown column 'shell_amount' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy170.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:173)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:78)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy293.queryByCondition(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at cn.taqu.core.jdbc.datasource.DataSourceInterceptor.proceed(DataSourceInterceptor.java:38)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at com.sun.proxy.$Proxy294.queryByCondition(Unknown Source)
	at cn.taqu.gonghui.system.service.impl.OrgStatisticsServiceImpl.getOrgDailyStatisticsByUser(OrgStatisticsServiceImpl.java:569)
	at cn.taqu.gonghui.controller.user.OrgStatisticsController.findConsortiaPerDayList(OrgStatisticsController.java:104)
	at cn.taqu.gonghui.controller.user.OrgStatisticsController$$FastClassBySpringCGLIB$$777d8ac5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.security.access.intercept.aopalliance.MethodSecurityInterceptor.invoke(MethodSecurityInterceptor.java:61)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at cn.taqu.gonghui.controller.user.OrgStatisticsController$$EnhancerBySpringCGLIB$$bfd86efe.findConsortiaPerDayList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:197)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:141)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:894)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1060)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:962)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter.doFilterInternal(JwtAuthenticationTokenFilter.java:54)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.taqu.core.web.filter.SoaFilter.doFilterInternal(SoaFilter.java:38)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at cn.taqu.core.web.filter.healthy.HealthyFilter.doFilterInternal(HealthyFilter.java:45)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1707)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Unknown column 'shell_amount' in 'field list'
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:411)
	at com.mysql.jdbc.Util.getInstance(Util.java:386)
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:1053)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:4096)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:4028)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2490)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:2651)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2734)
	at com.mysql.jdbc.PreparedStatement.executeInternal(PreparedStatement.java:2155)
	at com.mysql.jdbc.PreparedStatement.execute(PreparedStatement.java:1379)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3409)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3407)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3407)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy357.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at cn.taqu.core.jdbc.datasource.SqlExecuteTimeCountInterceptor.intercept(SqlExecuteTimeCountInterceptor.java:50)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy356.query(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy356.query(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:69)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.core.executor.MybatisCachingExecutor.query(MybatisCachingExecutor.java:165)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:64)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy355.query(Unknown Source)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:108)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy355.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 164 more
warning | 804 | 1731484341639 | 2024-11-13 15:52:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ed48f862b1f344119a9ff9ea0c774a6b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484321616, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8779, jvmUsedMemory=0.7896, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2221, diskUsed=226.8784, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 804 | 1731484341640 | 2024-11-13 15:52:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ed48f862b1f344119a9ff9ea0c774a6b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484331618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8164, jvmUsedMemory=0.8576, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2412, diskUsed=226.8785, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 804 | 1731484341640 | 2024-11-13 15:52:21 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ed48f862b1f344119a9ff9ea0c774a6b | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484341618, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6904, jvmUsedMemory=0.8776, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2468, diskUsed=226.8778, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 804 | 1731484341655 | 2024-11-13 15:52:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | ed48f862b1f344119a9ff9ea0c774a6b | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 804 | 1731484341655 | 2024-11-13 15:52:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | ed48f862b1f344119a9ff9ea0c774a6b | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1731484358186 | 2024-11-13 15:52:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 0ec90c76a24e4571938ac9e0659cd1f0 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1731484358186 | 2024-11-13 15:52:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 1e5c047406d940edb5dc2f463ad0467e | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1731484358187 | 2024-11-13 15:52:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 0ec90c76a24e4571938ac9e0659cd1f0 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1731484358193 | 2024-11-13 15:52:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 1e5c047406d940edb5dc2f463ad0467e | - | - | - | - | 7 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 804 | 1731484358256 | 2024-11-13 15:52:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | ed48f862b1f344119a9ff9ea0c774a6b | - | - | - | - | 16618 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 763 | 1731484358279 | 2024-11-13 15:52:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 128a1a74ddc24ed8ace95065a339ad64 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484351619, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5835, jvmUsedMemory=0.8854, jvmMaxMemory=3.5557, jvmMemoryUsage=0.249, diskUsed=226.8788, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 125 | 1731484387267 | 2024-11-13 15:53:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 25552188df274a7193af24c3705a5d60 | - | - | - | - | 108 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 125 | 1731484387267 | 2024-11-13 15:53:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 25552188df274a7193af24c3705a5d60 | - | - | - | - | 108 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 226 | 1731484389845 | 2024-11-13 15:53:09 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 03e49bdf35f14a73a11c8de1751c1794 | - | - | - | - | 1565 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1423 ms]

warning | 265 | 1731484408264 | 2024-11-13 15:53:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 44956b41c72d42c6ad3581894c6cdfad | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484388121, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8936, jvmUsedMemory=0.6309, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1774, diskUsed=226.886, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 265 | 1731484408275 | 2024-11-13 15:53:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 44956b41c72d42c6ad3581894c6cdfad | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484398124, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4482, jvmUsedMemory=0.8495, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2389, diskUsed=226.8853, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 265 | 1731484408276 | 2024-11-13 15:53:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 44956b41c72d42c6ad3581894c6cdfad | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484408119, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0547, jvmUsedMemory=0.869, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2444, diskUsed=226.8857, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 125 | 1731484408328 | 2024-11-13 15:53:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 25552188df274a7193af24c3705a5d60 | - | - | - | - | 21169 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 125 | 1731484408328 | 2024-11-13 15:53:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 25552188df274a7193af24c3705a5d60 | - | - | - | - | 21169 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 125 | 1731484408332 | 2024-11-13 15:53:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 25552188df274a7193af24c3705a5d60 | - | - | - | - | 21173 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 125 | 1731484408336 | 2024-11-13 15:53:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 25552188df274a7193af24c3705a5d60 | - | - | - | - | 21178 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 125 | 1731484408337 | 2024-11-13 15:53:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 25552188df274a7193af24c3705a5d60 | - | - | - | - | 21178 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 266 | 1731484438159 | 2024-11-13 15:53:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 86a6d194adde42298fab8c7d27bd613b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484418124, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2671, jvmUsedMemory=0.8898, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2503, diskUsed=226.8869, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 266 | 1731484438160 | 2024-11-13 15:53:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 86a6d194adde42298fab8c7d27bd613b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484428124, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3857, jvmUsedMemory=0.8946, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2516, diskUsed=226.889, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 266 | 1731484438160 | 2024-11-13 15:53:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 86a6d194adde42298fab8c7d27bd613b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484438125, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0186, jvmUsedMemory=0.8998, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2531, diskUsed=226.89, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 266 | 1731484438176 | 2024-11-13 15:53:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 86a6d194adde42298fab8c7d27bd613b | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 266 | 1731484438177 | 2024-11-13 15:53:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 86a6d194adde42298fab8c7d27bd613b | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1731484449313 | 2024-11-13 15:54:09 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 7e547940e6424b538511c91ebf1241d4 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1731484449313 | 2024-11-13 15:54:09 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 646ce85400e744eca0d6a9117c136caa | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1731484449317 | 2024-11-13 15:54:09 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 7e547940e6424b538511c91ebf1241d4 | - | - | - | - | 4 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1731484449317 | 2024-11-13 15:54:09 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 646ce85400e744eca0d6a9117c136caa | - | - | - | - | 4 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 266 | 1731484449355 | 2024-11-13 15:54:09 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | 86a6d194adde42298fab8c7d27bd613b | - | - | - | - | 11198 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 121 | 1731484480124 | 2024-11-13 15:54:40 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | a764a9bef92143a39bf7036dac2df883 | - | - | - | - | 287 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 121 | 1731484480124 | 2024-11-13 15:54:40 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | a764a9bef92143a39bf7036dac2df883 | - | - | - | - | 287 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 227 | 1731484482256 | 2024-11-13 15:54:42 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 619f481f0c8a4f7bb7186810bab95cce | - | - | - | - | 1395 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1230 ms]

warning | 268 | 1731484500834 | 2024-11-13 15:55:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | b1d590991a404633b2a7774bd40463d7 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484480709, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7212, jvmUsedMemory=0.319, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0897, diskUsed=226.8904, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1731484500841 | 2024-11-13 15:55:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | b1d590991a404633b2a7774bd40463d7 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484490711, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5425, jvmUsedMemory=0.5597, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1574, diskUsed=226.8904, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1731484500842 | 2024-11-13 15:55:00 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | b1d590991a404633b2a7774bd40463d7 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484500707, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3916, jvmUsedMemory=0.5799, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1631, diskUsed=226.8915, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 121 | 1731484500883 | 2024-11-13 15:55:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a764a9bef92143a39bf7036dac2df883 | - | - | - | - | 21047 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 121 | 1731484500884 | 2024-11-13 15:55:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a764a9bef92143a39bf7036dac2df883 | - | - | - | - | 21047 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 121 | 1731484500885 | 2024-11-13 15:55:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | a764a9bef92143a39bf7036dac2df883 | - | - | - | - | 21048 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 121 | 1731484500888 | 2024-11-13 15:55:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | a764a9bef92143a39bf7036dac2df883 | - | - | - | - | 21051 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 121 | 1731484500888 | 2024-11-13 15:55:00 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | a764a9bef92143a39bf7036dac2df883 | - | - | - | - | 21051 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 298 | 1731484530707 | 2024-11-13 15:55:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7848d79f34e449278aaf57a7d428a98a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484510694, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5708, jvmUsedMemory=0.6042, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1699, diskUsed=226.8916, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 298 | 1731484530708 | 2024-11-13 15:55:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7848d79f34e449278aaf57a7d428a98a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484520686, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3354, jvmUsedMemory=0.6089, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1712, diskUsed=226.8915, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 298 | 1731484530709 | 2024-11-13 15:55:30 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7848d79f34e449278aaf57a7d428a98a | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484530678, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1294, jvmUsedMemory=0.6221, jvmMaxMemory=3.5557, jvmMemoryUsage=0.175, diskUsed=226.8915, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 298 | 1731484530733 | 2024-11-13 15:55:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 7848d79f34e449278aaf57a7d428a98a | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 298 | 1731484530733 | 2024-11-13 15:55:30 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 7848d79f34e449278aaf57a7d428a98a | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1731484558178 | 2024-11-13 15:55:58 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | e5d95d3713714c1da0919eada3c437a1 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1731484558182 | 2024-11-13 15:55:58 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 814cba8d88064231a9d8c733e2b4f6e2 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1731484558183 | 2024-11-13 15:55:58 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 814cba8d88064231a9d8c733e2b4f6e2 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1731484558183 | 2024-11-13 15:55:58 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | e5d95d3713714c1da0919eada3c437a1 | - | - | - | - | 5 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 299 | 1731484558229 | 2024-11-13 15:55:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 0736543bc02b454d8b55b731cce35069 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 299 | 1731484558261 | 2024-11-13 15:55:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0736543bc02b454d8b55b731cce35069 | - | - | - | - | 33 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484540671, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1152, jvmUsedMemory=0.6315, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1776, diskUsed=226.8916, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 299 | 1731484558263 | 2024-11-13 15:55:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0736543bc02b454d8b55b731cce35069 | - | - | - | - | 34 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731484550670, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0171, jvmUsedMemory=0.6417, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1805, diskUsed=226.8916, diskTotal=460.4317, diskUsage=0.4928, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 299 | 1731484558288 | 2024-11-13 15:55:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0736543bc02b454d8b55b731cce35069 | - | - | - | - | 59 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1731497887748 | 2024-11-13 19:38:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | f0863ec395ec4f2593b28e19ff95abf9 | - | - | - | - | 116 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 127 | 1731497887749 | 2024-11-13 19:38:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | f0863ec395ec4f2593b28e19ff95abf9 | - | - | - | - | 117 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 271 | 1731497908495 | 2024-11-13 19:38:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | df350051491f4229af31a685de08b63a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731497888373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3237, jvmUsedMemory=0.598, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1682, diskUsed=226.5743, diskTotal=460.4317, diskUsage=0.4921, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 271 | 1731497908502 | 2024-11-13 19:38:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | df350051491f4229af31a685de08b63a | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731497898376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9722, jvmUsedMemory=0.1515, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0426, diskUsed=226.5746, diskTotal=460.4317, diskUsage=0.4921, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 271 | 1731497908504 | 2024-11-13 19:38:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | df350051491f4229af31a685de08b63a | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731497908376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8223, jvmUsedMemory=0.1577, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0443, diskUsed=226.575, diskTotal=460.4317, diskUsage=0.4921, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 127 | 1731497908546 | 2024-11-13 19:38:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f0863ec395ec4f2593b28e19ff95abf9 | - | - | - | - | 20914 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1731497908546 | 2024-11-13 19:38:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f0863ec395ec4f2593b28e19ff95abf9 | - | - | - | - | 20915 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1731497908549 | 2024-11-13 19:38:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | f0863ec395ec4f2593b28e19ff95abf9 | - | - | - | - | 20917 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1731497908552 | 2024-11-13 19:38:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | f0863ec395ec4f2593b28e19ff95abf9 | - | - | - | - | 20920 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1731497908552 | 2024-11-13 19:38:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | f0863ec395ec4f2593b28e19ff95abf9 | - | - | - | - | 20920 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1731497913421 | 2024-11-13 19:38:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 1c2364a20bc14440bfe027091648aff2 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1731497913421 | 2024-11-13 19:38:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | da3bbcf25dce40c9aaa9d1ec8dba8a00 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1731497913421 | 2024-11-13 19:38:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 1c2364a20bc14440bfe027091648aff2 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1731497913423 | 2024-11-13 19:38:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | da3bbcf25dce40c9aaa9d1ec8dba8a00 | - | - | - | - | 2 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 127 | 1731497913449 | 2024-11-13 19:38:33 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ActorCell | receiveMessage | f0863ec395ec4f2593b28e19ff95abf9 | - | - | - | - | 25817 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

