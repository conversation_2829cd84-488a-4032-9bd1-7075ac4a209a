info | 16 | 1753666108220 | 2025-07-28 09:28:28 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | df91736e660641e3a5b2b18c47ccd0cb | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753666108228 | 2025-07-28 09:28:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 27715 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1753666108243 | 2025-07-28 09:28:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753666108872 | 2025-07-28 09:28:28 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 635 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753666108875 | 2025-07-28 09:28:28 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 638 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753666108961 | 2025-07-28 09:28:28 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 725 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753666109004 | 2025-07-28 09:28:29 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 767 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753666109051 | 2025-07-28 09:28:29 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 814 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753666110115 | 2025-07-28 09:28:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 1878 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753666110258 | 2025-07-28 09:28:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 2021 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753666110261 | 2025-07-28 09:28:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 2024 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753666110261 | 2025-07-28 09:28:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 2025 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753666111848 | 2025-07-28 09:28:31 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 3611 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753666114165 | 2025-07-28 09:28:34 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 5928 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666114166 | 2025-07-28 09:28:34 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 5929 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753666114217 | 2025-07-28 09:28:34 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 5980 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 45 ms. Found 0 JPA repository interfaces.

info | 1 | 1753666114227 | 2025-07-28 09:28:34 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 5991 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666114228 | 2025-07-28 09:28:34 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 5991 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753666114257 | 2025-07-28 09:28:34 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 6020 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.

info | 1 | 1753666115445 | 2025-07-28 09:28:35 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 7212 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$a39d77fb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666115470 | 2025-07-28 09:28:35 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 7233 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$22a3bd73] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666115534 | 2025-07-28 09:28:35 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 7297 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$a10cc83c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666115539 | 2025-07-28 09:28:35 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 7302 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666115593 | 2025-07-28 09:28:35 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 7356 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666115597 | 2025-07-28 09:28:35 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 7360 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666116148 | 2025-07-28 09:28:36 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 7911 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666116162 | 2025-07-28 09:28:36 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 7925 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753666116163 | 2025-07-28 09:28:36 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 7926 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753666116275 | 2025-07-28 09:28:36 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 8038 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753666134323 | 2025-07-28 09:28:54 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 26086 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753666134404 | 2025-07-28 09:28:54 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 26167 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753666134443 | 2025-07-28 09:28:54 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 26207 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753666134542 | 2025-07-28 09:28:54 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 26305 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753666134656 | 2025-07-28 09:28:54 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 26419 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753666134807 | 2025-07-28 09:28:54 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 26571 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753666134816 | 2025-07-28 09:28:54 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 26579 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753666139198 | 2025-07-28 09:28:59 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 30961 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753666139508 | 2025-07-28 09:28:59 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 31271 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753666139531 | 2025-07-28 09:28:59 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 31294 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753666139770 | 2025-07-28 09:28:59 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | da9ea6e6ed744754b4d87c03816b9e74 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753666140001 | 2025-07-28 09:29:00 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | da9ea6e6ed744754b4d87c03816b9e74 | - | - | - | - | 231 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 214 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753666140010 | 2025-07-28 09:29:00 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | da9ea6e6ed744754b4d87c03816b9e74 | - | - | - | - | 240 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753666140137 | 2025-07-28 09:29:00 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | da9ea6e6ed744754b4d87c03816b9e74 | - | - | - | - | 367 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 125 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753666140144 | 2025-07-28 09:29:00 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | da9ea6e6ed744754b4d87c03816b9e74 | - | - | - | - | 374 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753666140151 | 2025-07-28 09:29:00 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | da9ea6e6ed744754b4d87c03816b9e74 | - | - | - | - | 381 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753666140158 | 2025-07-28 09:29:00 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | da9ea6e6ed744754b4d87c03816b9e74 | - | - | - | - | 389 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753666140259 | 2025-07-28 09:29:00 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | da9ea6e6ed744754b4d87c03816b9e74 | - | - | - | - | 489 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 98 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753666143923 | 2025-07-28 09:29:03 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 35686 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753666144683 | 2025-07-28 09:29:04 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36446 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@5ad34ed2 with [org.springframework.security.web.session.DisableEncodeUrlFilter@51a5b1c3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4cdf35fd, org.springframework.security.web.context.SecurityContextPersistenceFilter@75ecbcb4, org.springframework.security.web.header.HeaderWriterFilter@25a1888, org.springframework.security.web.authentication.logout.LogoutFilter@c8a5e67, org.springframework.web.filter.CorsFilter@24378e1d, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@41ef5f05, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@4b52e709, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@73051634, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@cab0abd, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@b37956f, org.springframework.security.web.session.SessionManagementFilter@6de74550, org.springframework.security.web.access.ExceptionTranslationFilter@e44af47, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7e6c7163]

info | 1 | 1753666144699 | 2025-07-28 09:29:04 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36462 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753666144784 | 2025-07-28 09:29:04 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36548 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753666144786 | 2025-07-28 09:29:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36549 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753666144788 | 2025-07-28 09:29:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36551 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753666144790 | 2025-07-28 09:29:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36554 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753666144793 | 2025-07-28 09:29:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36557 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753666144794 | 2025-07-28 09:29:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36557 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753666144794 | 2025-07-28 09:29:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36557 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753666145115 | 2025-07-28 09:29:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36878 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753666145226 | 2025-07-28 09:29:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36989 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753666145226 | 2025-07-28 09:29:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36989 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753666145226 | 2025-07-28 09:29:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36989 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753666145227 | 2025-07-28 09:29:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36990 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753666145227 | 2025-07-28 09:29:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36990 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753666145227 | 2025-07-28 09:29:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 36990 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:tap0 (tap0)

info | 1 | 1753666145347 | 2025-07-28 09:29:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37111 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753666145474 | 2025-07-28 09:29:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37238 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753666145476 | 2025-07-28 09:29:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37239 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753666145492 | 2025-07-28 09:29:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37256 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@55e397e3, tech.powerjob.worker.actors.ProcessorTrackerActor@1bf6f5bb, tech.powerjob.worker.actors.WorkerActor@63fb5355])

info | 1 | 1753666145532 | 2025-07-28 09:29:05 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37296 | 0 | - | - | - | - | main o.r.Reflections Reflections took 26 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753666145539 | 2025-07-28 09:29:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37302 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1753666145540 | 2025-07-28 09:29:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37303 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@63a6211

info | 1 | 1753666145541 | 2025-07-28 09:29:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37304 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@3fdb9eb9

info | 1 | 1753666145541 | 2025-07-28 09:29:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37304 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753666145541 | 2025-07-28 09:29:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37304 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753666145544 | 2025-07-28 09:29:05 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 37307 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 125 | 1753666145985 | 2025-07-28 09:29:05 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753666146351 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38114 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753666146352 | 2025-07-28 09:29:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38115 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753666146352 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38115 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38117 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753666146354 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38118 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753666146356 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38119 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753666146358 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38121 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753666146358 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38121 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753666146359 | 2025-07-28 09:29:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38122 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 817.9 ms

info | 1 | 1753666146490 | 2025-07-28 09:29:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38253 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753666146495 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38258 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753666146496 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38259 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753666146500 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38263 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753666146692 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38455 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753666146693 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38456 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/61976023491a4890bff5772881255ff4/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753666146699 | 2025-07-28 09:29:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38462 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/61976023491a4890bff5772881255ff4/] on JVM exit successfully

info | 1 | 1753666146714 | 2025-07-28 09:29:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38477 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753666146715 | 2025-07-28 09:29:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38478 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.930 s, congratulations!

info | 158 | 1753666146718 | 2025-07-28 09:29:06 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 9b107a95146f4eafa415715bd81798c3 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 158 | 1753666146719 | 2025-07-28 09:29:06 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 9b107a95146f4eafa415715bd81798c3 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753666146761 | 2025-07-28 09:29:06 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | TomcatWebServer | start | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38524 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666146792 | 2025-07-28 09:29:06 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38555 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753666146810 | 2025-07-28 09:29:06 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38573 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753666146810 | 2025-07-28 09:29:06 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38573 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753666146835 | 2025-07-28 09:29:06 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38598 | 0 | - | - | - | - | main c.t.g.Application Started Application in 39.111 seconds (JVM running for 39.749)

info | 1 | 1753666146857 | 2025-07-28 09:29:06 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.641 | ************** | - | 2 | Application | main | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38620 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753666146857 | 2025-07-28 09:29:06 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.643 | ************** | - | 2 | Application | main | 82b3266965fa46d5a7d24dcbbdb1c244 | - | - | - | - | 38620 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 228 | 1753666146859 | 2025-07-28 09:29:06 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 613e406952d743bda361545dceeab9d4 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 158 | 1753666190693 | 2025-07-28 09:29:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 9b107a95146f4eafa415715bd81798c3 | - | - | - | - | 43984 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1753666190705 | 2025-07-28 09:29:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 9b107a95146f4eafa415715bd81798c3 | - | - | - | - | 43989 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1753666190710 | 2025-07-28 09:29:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 9b107a95146f4eafa415715bd81798c3 | - | - | - | - | 43993 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1753666190711 | 2025-07-28 09:29:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 9b107a95146f4eafa415715bd81798c3 | - | - | - | - | 43993 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 29 | 1753666190819 | 2025-07-28 09:29:50 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 554030c46faa4c34b5ac359be5f416b3 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 29 | 1753666190860 | 2025-07-28 09:29:50 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 554030c46faa4c34b5ac359be5f416b3 | - | - | - | - | 41 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 29 | 1753666190862 | 2025-07-28 09:29:50 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | 554030c46faa4c34b5ac359be5f416b3 | - | - | - | - | 43 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 16 | 1753666212437 | 2025-07-28 09:30:12 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | e2288de110fa4a0383c635e00bb005e5 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753666212442 | 2025-07-28 09:30:12 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 28435 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1753666212456 | 2025-07-28 09:30:12 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753666212954 | 2025-07-28 09:30:12 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 504 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753666212958 | 2025-07-28 09:30:12 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 507 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753666212999 | 2025-07-28 09:30:12 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 548 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753666213019 | 2025-07-28 09:30:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 569 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753666213072 | 2025-07-28 09:30:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 621 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753666214110 | 2025-07-28 09:30:14 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 1660 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753666214248 | 2025-07-28 09:30:14 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 1798 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753666214251 | 2025-07-28 09:30:14 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 1800 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753666214252 | 2025-07-28 09:30:14 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 1801 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753666215726 | 2025-07-28 09:30:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 3275 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753666217832 | 2025-07-28 09:30:17 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 5381 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666217833 | 2025-07-28 09:30:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 5382 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753666217884 | 2025-07-28 09:30:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 5433 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 45 ms. Found 0 JPA repository interfaces.

info | 1 | 1753666217892 | 2025-07-28 09:30:17 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 5442 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666217894 | 2025-07-28 09:30:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 5443 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753666217922 | 2025-07-28 09:30:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 5471 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.

info | 1 | 1753666218676 | 2025-07-28 09:30:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 6228 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$a39d77fb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666218695 | 2025-07-28 09:30:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 6244 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$22a3bd73] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666218755 | 2025-07-28 09:30:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 6304 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$a10cc83c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666218759 | 2025-07-28 09:30:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 6308 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666218817 | 2025-07-28 09:30:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 6367 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666218821 | 2025-07-28 09:30:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 6370 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666219392 | 2025-07-28 09:30:19 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 6941 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666219404 | 2025-07-28 09:30:19 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 6953 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753666219404 | 2025-07-28 09:30:19 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 6954 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753666219499 | 2025-07-28 09:30:19 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 7048 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753666238167 | 2025-07-28 09:30:38 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 25716 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753666238246 | 2025-07-28 09:30:38 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 25795 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753666238281 | 2025-07-28 09:30:38 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 25831 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753666238373 | 2025-07-28 09:30:38 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 25922 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753666238452 | 2025-07-28 09:30:38 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 26001 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753666238570 | 2025-07-28 09:30:38 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 26119 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753666238577 | 2025-07-28 09:30:38 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 26126 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753666243297 | 2025-07-28 09:30:43 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 30846 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753666243626 | 2025-07-28 09:30:43 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 31175 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753666243664 | 2025-07-28 09:30:43 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 31214 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753666243890 | 2025-07-28 09:30:43 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 97b00e3b1c7e4359a5f317a203307565 | - | - | - | - | 1 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753666244176 | 2025-07-28 09:30:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 97b00e3b1c7e4359a5f317a203307565 | - | - | - | - | 286 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753666244185 | 2025-07-28 09:30:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 97b00e3b1c7e4359a5f317a203307565 | - | - | - | - | 295 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753666244321 | 2025-07-28 09:30:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 97b00e3b1c7e4359a5f317a203307565 | - | - | - | - | 431 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 133 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753666244327 | 2025-07-28 09:30:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 97b00e3b1c7e4359a5f317a203307565 | - | - | - | - | 437 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753666244335 | 2025-07-28 09:30:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 97b00e3b1c7e4359a5f317a203307565 | - | - | - | - | 445 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753666244342 | 2025-07-28 09:30:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 97b00e3b1c7e4359a5f317a203307565 | - | - | - | - | 452 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753666244449 | 2025-07-28 09:30:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 97b00e3b1c7e4359a5f317a203307565 | - | - | - | - | 559 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 105 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753666247726 | 2025-07-28 09:30:47 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 35275 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753666248611 | 2025-07-28 09:30:48 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36160 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@168c8773 with [org.springframework.security.web.session.DisableEncodeUrlFilter@277e2871, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3ddcf73c, org.springframework.security.web.context.SecurityContextPersistenceFilter@5a6dbab7, org.springframework.security.web.header.HeaderWriterFilter@5dbb410b, org.springframework.security.web.authentication.logout.LogoutFilter@45cceeeb, org.springframework.web.filter.CorsFilter@2e26eed3, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@15168d6f, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@352f955d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2ad6214d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@57f39200, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@476a3b94, org.springframework.security.web.session.SessionManagementFilter@1127ec2d, org.springframework.security.web.access.ExceptionTranslationFilter@727782da, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@29e66bc1]

info | 1 | 1753666248628 | 2025-07-28 09:30:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36177 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753666248722 | 2025-07-28 09:30:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36271 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753666248724 | 2025-07-28 09:30:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36273 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753666248725 | 2025-07-28 09:30:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36274 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753666248727 | 2025-07-28 09:30:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36276 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753666248730 | 2025-07-28 09:30:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36279 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753666248730 | 2025-07-28 09:30:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36279 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753666248730 | 2025-07-28 09:30:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36279 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753666248995 | 2025-07-28 09:30:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36544 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753666249094 | 2025-07-28 09:30:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36644 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753666249095 | 2025-07-28 09:30:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36644 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753666249095 | 2025-07-28 09:30:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36644 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753666249095 | 2025-07-28 09:30:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36644 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753666249095 | 2025-07-28 09:30:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36645 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753666249096 | 2025-07-28 09:30:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36645 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:tap0 (tap0)

info | 1 | 1753666249209 | 2025-07-28 09:30:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36759 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753666249341 | 2025-07-28 09:30:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36890 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753666249342 | 2025-07-28 09:30:49 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36891 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753666249358 | 2025-07-28 09:30:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36907 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@57c903e2, tech.powerjob.worker.actors.ProcessorTrackerActor@69fe55fb, tech.powerjob.worker.actors.WorkerActor@275c1aed])

info | 1 | 1753666249398 | 2025-07-28 09:30:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36947 | 0 | - | - | - | - | main o.r.Reflections Reflections took 27 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753666249404 | 2025-07-28 09:30:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36953 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1753666249405 | 2025-07-28 09:30:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36954 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@5696e114

info | 1 | 1753666249405 | 2025-07-28 09:30:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36954 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753666249405 | 2025-07-28 09:30:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36954 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753666249408 | 2025-07-28 09:30:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 36957 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 126 | 1753666249819 | 2025-07-28 09:30:49 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753666250235 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37784 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753666250236 | 2025-07-28 09:30:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37785 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753666250236 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37785 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753666250236 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37785 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753666250236 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753666250237 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753666250237 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753666250237 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753666250237 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753666250237 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753666250237 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753666250237 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753666250237 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753666250237 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753666250237 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37786 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753666250239 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37788 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753666250241 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37790 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753666250241 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37790 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753666250241 | 2025-07-28 09:30:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37791 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 836.4 ms

info | 1 | 1753666250320 | 2025-07-28 09:30:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37869 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753666250325 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37874 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753666250325 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37874 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753666250328 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 37877 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753666250509 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38058 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753666250509 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38058 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/dc8a2963e7b241a18c8a15ff2f5d8e15/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753666250515 | 2025-07-28 09:30:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38064 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/dc8a2963e7b241a18c8a15ff2f5d8e15/] on JVM exit successfully

info | 1 | 1753666250529 | 2025-07-28 09:30:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38079 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753666250530 | 2025-07-28 09:30:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38080 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.808 s, congratulations!

info | 159 | 1753666250535 | 2025-07-28 09:30:50 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 694d078eb6364efab3676e7cc6afa583 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 159 | 1753666250536 | 2025-07-28 09:30:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 694d078eb6364efab3676e7cc6afa583 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753666250590 | 2025-07-28 09:30:50 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | TomcatWebServer | start | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38139 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666250627 | 2025-07-28 09:30:50 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38176 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753666250655 | 2025-07-28 09:30:50 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38204 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753666250656 | 2025-07-28 09:30:50 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38205 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753666250681 | 2025-07-28 09:30:50 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38230 | 0 | - | - | - | - | main c.t.g.Application Started Application in 38.715 seconds (JVM running for 39.197)

info | 1 | 1753666250705 | 2025-07-28 09:30:50 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38255 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753666250706 | 2025-07-28 09:30:50 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.641 | ************** | - | 2 | Application | main | 6f800c58d5494e1bac870c2c134abd61 | - | - | - | - | 38255 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 229 | 1753666250711 | 2025-07-28 09:30:50 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | c396da529ab9449296e08d186cd73579 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 29 | 1753666253584 | 2025-07-28 09:30:53 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 8a6ef1f16aac4564a71e9d1a89d040cb | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 1 | 1753666256912 | 2025-07-28 09:30:56 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 3 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 28740 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 16 | 1753666256909 | 2025-07-28 09:30:56 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 9102203f22974ceea8594e887f5d3411 | - | - | - | - | 3 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753666256927 | 2025-07-28 09:30:56 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753666257454 | 2025-07-28 09:30:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 531 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753666257457 | 2025-07-28 09:30:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 535 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753666257500 | 2025-07-28 09:30:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 577 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753666257520 | 2025-07-28 09:30:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 597 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753666257567 | 2025-07-28 09:30:57 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 644 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753666258312 | 2025-07-28 09:30:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 1390 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753666258459 | 2025-07-28 09:30:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 1536 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753666258462 | 2025-07-28 09:30:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 1539 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753666258462 | 2025-07-28 09:30:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 1539 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753666259777 | 2025-07-28 09:30:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 2854 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753666261756 | 2025-07-28 09:31:01 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 4833 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666261757 | 2025-07-28 09:31:01 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 4834 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753666261801 | 2025-07-28 09:31:01 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 4878 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 39 ms. Found 0 JPA repository interfaces.

info | 1 | 1753666261810 | 2025-07-28 09:31:01 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 4887 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666261811 | 2025-07-28 09:31:01 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 4888 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753666261839 | 2025-07-28 09:31:01 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 4917 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.

info | 1 | 1753666262609 | 2025-07-28 09:31:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 5687 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$57f151d8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666262631 | 2025-07-28 09:31:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 5708 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d6f79750] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666262710 | 2025-07-28 09:31:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 5787 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$5560a219] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666262714 | 2025-07-28 09:31:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 5791 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666262778 | 2025-07-28 09:31:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 5855 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666262784 | 2025-07-28 09:31:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 5861 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666263330 | 2025-07-28 09:31:03 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 6407 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666263338 | 2025-07-28 09:31:03 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 6415 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753666263338 | 2025-07-28 09:31:03 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 6415 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753666263428 | 2025-07-28 09:31:03 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 7023f7a5a9d3419d9c6b16d3dec10f01 | - | - | - | - | 6505 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 16 | 1753666320161 | 2025-07-28 09:32:00 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | c2c397d6b1f84280bb38963d177c6555 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753666320166 | 2025-07-28 09:32:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 29117 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1753666320181 | 2025-07-28 09:32:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753666320696 | 2025-07-28 09:32:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 522 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753666320731 | 2025-07-28 09:32:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 556 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753666320745 | 2025-07-28 09:32:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 570 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753666320788 | 2025-07-28 09:32:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 613 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753666320840 | 2025-07-28 09:32:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 666 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753666321603 | 2025-07-28 09:32:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 1428 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753666321740 | 2025-07-28 09:32:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 1566 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753666321743 | 2025-07-28 09:32:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 1568 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753666321743 | 2025-07-28 09:32:01 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 1568 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753666322995 | 2025-07-28 09:32:02 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 2821 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753666325038 | 2025-07-28 09:32:05 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 4863 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666325041 | 2025-07-28 09:32:05 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 4866 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753666325093 | 2025-07-28 09:32:05 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 4918 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 45 ms. Found 0 JPA repository interfaces.

info | 1 | 1753666325102 | 2025-07-28 09:32:05 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 4927 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666325103 | 2025-07-28 09:32:05 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 4929 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753666325134 | 2025-07-28 09:32:05 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 4959 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.

info | 1 | 1753666325998 | 2025-07-28 09:32:05 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 5824 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$8ad66ceb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666326016 | 2025-07-28 09:32:06 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 5842 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$9dcb263] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666326075 | 2025-07-28 09:32:06 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 5900 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$8845bd2c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666326080 | 2025-07-28 09:32:06 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 5905 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666326140 | 2025-07-28 09:32:06 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 5965 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666326146 | 2025-07-28 09:32:06 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 5971 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666326723 | 2025-07-28 09:32:06 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 6548 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666326740 | 2025-07-28 09:32:06 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 6565 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753666326740 | 2025-07-28 09:32:06 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 6565 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753666326831 | 2025-07-28 09:32:06 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 6656 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753666345471 | 2025-07-28 09:32:25 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 25296 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753666345546 | 2025-07-28 09:32:25 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 25371 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753666345574 | 2025-07-28 09:32:25 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 25399 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753666345664 | 2025-07-28 09:32:25 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 25489 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753666345745 | 2025-07-28 09:32:25 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 25570 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753666345873 | 2025-07-28 09:32:25 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 25699 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753666345880 | 2025-07-28 09:32:25 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 25705 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753666350210 | 2025-07-28 09:32:30 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 30035 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753666350619 | 2025-07-28 09:32:30 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 30444 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753666350647 | 2025-07-28 09:32:30 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 30472 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753666350846 | 2025-07-28 09:32:30 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c2e2f8888cfa475faac5cd53280c2162 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753666351052 | 2025-07-28 09:32:31 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c2e2f8888cfa475faac5cd53280c2162 | - | - | - | - | 205 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753666351062 | 2025-07-28 09:32:31 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c2e2f8888cfa475faac5cd53280c2162 | - | - | - | - | 215 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753666351172 | 2025-07-28 09:32:31 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c2e2f8888cfa475faac5cd53280c2162 | - | - | - | - | 325 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 108 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753666351178 | 2025-07-28 09:32:31 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c2e2f8888cfa475faac5cd53280c2162 | - | - | - | - | 331 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753666351206 | 2025-07-28 09:32:31 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c2e2f8888cfa475faac5cd53280c2162 | - | - | - | - | 360 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 26 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753666351217 | 2025-07-28 09:32:31 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c2e2f8888cfa475faac5cd53280c2162 | - | - | - | - | 370 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753666351319 | 2025-07-28 09:32:31 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c2e2f8888cfa475faac5cd53280c2162 | - | - | - | - | 473 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 100 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753666355011 | 2025-07-28 09:32:35 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 34837 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753666355912 | 2025-07-28 09:32:35 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 35737 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@176d0921 with [org.springframework.security.web.session.DisableEncodeUrlFilter@383878c5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7b41a3f9, org.springframework.security.web.context.SecurityContextPersistenceFilter@3f196671, org.springframework.security.web.header.HeaderWriterFilter@b159e06, org.springframework.security.web.authentication.logout.LogoutFilter@1002f8d9, org.springframework.web.filter.CorsFilter@47ba55fb, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@777898ff, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@2554a03c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2c4766e3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1d87905e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5a0f91b6, org.springframework.security.web.session.SessionManagementFilter@379ee84e, org.springframework.security.web.access.ExceptionTranslationFilter@384e3e54, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3481e4d]

info | 1 | 1753666355930 | 2025-07-28 09:32:35 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 35755 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753666356016 | 2025-07-28 09:32:36 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 35841 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753666356019 | 2025-07-28 09:32:36 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 35844 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753666356020 | 2025-07-28 09:32:36 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 35845 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753666356022 | 2025-07-28 09:32:36 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 35848 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753666356026 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 35851 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753666356026 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 35852 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753666356027 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 35852 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753666356284 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36109 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753666356372 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36197 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753666356372 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36197 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753666356373 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36198 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753666356373 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36198 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753666356373 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36198 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753666356373 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36198 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:tap0 (tap0)

info | 1 | 1753666356492 | 2025-07-28 09:32:36 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36317 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753666356610 | 2025-07-28 09:32:36 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36435 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753666356611 | 2025-07-28 09:32:36 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36436 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753666356621 | 2025-07-28 09:32:36 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36446 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@a16f80e, tech.powerjob.worker.actors.ProcessorTrackerActor@6061284, tech.powerjob.worker.actors.WorkerActor@6232fc95])

info | 1 | 1753666356656 | 2025-07-28 09:32:36 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36481 | 0 | - | - | - | - | main o.r.Reflections Reflections took 23 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753666356665 | 2025-07-28 09:32:36 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36490 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1753666356666 | 2025-07-28 09:32:36 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36491 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@30573e40

info | 1 | 1753666356666 | 2025-07-28 09:32:36 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36492 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753666356667 | 2025-07-28 09:32:36 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36492 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753666356671 | 2025-07-28 09:32:36 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 36496 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 128 | 1753666357192 | 2025-07-28 09:32:37 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753666357570 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37395 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753666357571 | 2025-07-28 09:32:37 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753666357571 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753666357571 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753666357571 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753666357571 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753666357571 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753666357571 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37397 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753666357572 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37397 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753666357572 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37397 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753666357572 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37397 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753666357572 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37397 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753666357572 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37397 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753666357572 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37397 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753666357572 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37397 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753666357573 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37398 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753666357576 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37401 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753666357576 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37401 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753666357577 | 2025-07-28 09:32:37 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37402 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 910.2 ms

info | 1 | 1753666357670 | 2025-07-28 09:32:37 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37495 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753666357675 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37500 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753666357675 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37501 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753666357680 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37505 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753666357897 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37722 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753666357897 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37722 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/0861d8aad397470cb4b20cb60e9e95ef/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753666357904 | 2025-07-28 09:32:37 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37729 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/0861d8aad397470cb4b20cb60e9e95ef/] on JVM exit successfully

info | 1 | 1753666357919 | 2025-07-28 09:32:37 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37744 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753666357920 | 2025-07-28 09:32:37 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37745 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.904 s, congratulations!

info | 162 | 1753666357924 | 2025-07-28 09:32:37 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 58585aa053dd41c9aab2831156749853 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 162 | 1753666357925 | 2025-07-28 09:32:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 58585aa053dd41c9aab2831156749853 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753666357964 | 2025-07-28 09:32:37 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | TomcatWebServer | start | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37789 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666357993 | 2025-07-28 09:32:37 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37819 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753666358011 | 2025-07-28 09:32:38 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37837 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753666358012 | 2025-07-28 09:32:38 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37837 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753666358047 | 2025-07-28 09:32:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37872 | 0 | - | - | - | - | main c.t.g.Application Started Application in 38.347 seconds (JVM running for 38.813)

info | 1 | 1753666358068 | 2025-07-28 09:32:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37893 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753666358068 | 2025-07-28 09:32:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.641 | ************** | - | 2 | Application | main | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 37893 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 232 | 1753666358074 | 2025-07-28 09:32:38 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | b2a40898fe9a4796a12798713b4e910a | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 162 | 1753666370685 | 2025-07-28 09:32:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 58585aa053dd41c9aab2831156749853 | - | - | - | - | 12762 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753666372983 | 2025-07-28 09:32:52 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.643 | ************** | - | 2 | Application | main | 222cfed5e93b45a28df2276f66b9f4a2 | - | - | - | - | 52809 | 0 | - | - | - | - | main c.t.c.c.c.SoaClient register http client : [http://mp-finance-trade-api.test1.hbmonitor.com/mp-finance-trade-api/api] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 166 | 1753666434368 | 2025-07-28 09:33:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4ee88a37ed634261a1628a340d9cc2ee | - | - | - | - | 2 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1753666434379 | 2025-07-28 09:33:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4ee88a37ed634261a1628a340d9cc2ee | - | - | - | - | 10 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1753666434387 | 2025-07-28 09:33:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4ee88a37ed634261a1628a340d9cc2ee | - | - | - | - | 18 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1753666434388 | 2025-07-28 09:33:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4ee88a37ed634261a1628a340d9cc2ee | - | - | - | - | 20 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1753666434391 | 2025-07-28 09:33:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4ee88a37ed634261a1628a340d9cc2ee | - | - | - | - | 23 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1753666434394 | 2025-07-28 09:33:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ab3f7a2382d3423091dda14ae44b6b03 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753666452345 | 2025-07-28 09:34:12 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 29936 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 16 | 1753666452339 | 2025-07-28 09:34:12 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 284d606fef4448858d4e36716d30f986 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753666452357 | 2025-07-28 09:34:12 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753666452901 | 2025-07-28 09:34:12 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 549 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753666452913 | 2025-07-28 09:34:12 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 561 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753666452955 | 2025-07-28 09:34:12 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 603 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753666453033 | 2025-07-28 09:34:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 681 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753666453037 | 2025-07-28 09:34:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 685 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753666453745 | 2025-07-28 09:34:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 1393 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753666453859 | 2025-07-28 09:34:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 1508 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753666453863 | 2025-07-28 09:34:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 1511 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753666453863 | 2025-07-28 09:34:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 1511 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753666454911 | 2025-07-28 09:34:14 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 2560 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753666457065 | 2025-07-28 09:34:17 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 4713 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666457066 | 2025-07-28 09:34:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 4714 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753666457113 | 2025-07-28 09:34:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 4761 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 40 ms. Found 0 JPA repository interfaces.

info | 1 | 1753666457123 | 2025-07-28 09:34:17 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 4771 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666457124 | 2025-07-28 09:34:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 4772 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753666457152 | 2025-07-28 09:34:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 4800 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.

info | 1 | 1753666457956 | 2025-07-28 09:34:17 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 5607 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$f63344f8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666457975 | 2025-07-28 09:34:17 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 5623 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$75398a70] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666458033 | 2025-07-28 09:34:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 5682 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$f3a29539] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666458038 | 2025-07-28 09:34:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 5686 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666458098 | 2025-07-28 09:34:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 5747 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666458103 | 2025-07-28 09:34:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 5751 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666458650 | 2025-07-28 09:34:18 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 6298 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666458662 | 2025-07-28 09:34:18 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 6310 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753666458662 | 2025-07-28 09:34:18 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 6310 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753666458769 | 2025-07-28 09:34:18 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 6417 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753666477365 | 2025-07-28 09:34:37 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 25013 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753666477443 | 2025-07-28 09:34:37 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 25091 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753666477477 | 2025-07-28 09:34:37 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 25126 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753666477585 | 2025-07-28 09:34:37 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 25234 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753666477686 | 2025-07-28 09:34:37 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 25335 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753666477854 | 2025-07-28 09:34:37 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 25502 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753666477863 | 2025-07-28 09:34:37 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 25511 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753666482019 | 2025-07-28 09:34:42 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 29668 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753666482278 | 2025-07-28 09:34:42 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 29926 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753666482299 | 2025-07-28 09:34:42 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 29947 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753666482515 | 2025-07-28 09:34:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 51db78b50c8c4da4992bb5339d13f8df | - | - | - | - | 1 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753666482543 | 2025-07-28 09:34:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 51db78b50c8c4da4992bb5339d13f8df | - | - | - | - | 28 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753666482552 | 2025-07-28 09:34:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 51db78b50c8c4da4992bb5339d13f8df | - | - | - | - | 37 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753666482672 | 2025-07-28 09:34:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 51db78b50c8c4da4992bb5339d13f8df | - | - | - | - | 157 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 118 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753666482678 | 2025-07-28 09:34:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 51db78b50c8c4da4992bb5339d13f8df | - | - | - | - | 163 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753666482687 | 2025-07-28 09:34:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 51db78b50c8c4da4992bb5339d13f8df | - | - | - | - | 172 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753666482695 | 2025-07-28 09:34:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 51db78b50c8c4da4992bb5339d13f8df | - | - | - | - | 181 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753666482819 | 2025-07-28 09:34:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 51db78b50c8c4da4992bb5339d13f8df | - | - | - | - | 305 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 121 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753666486089 | 2025-07-28 09:34:46 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 33737 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753666486901 | 2025-07-28 09:34:46 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 34549 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@7ecaad0a with [org.springframework.security.web.session.DisableEncodeUrlFilter@42b8bc3a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d52a67f, org.springframework.security.web.context.SecurityContextPersistenceFilter@2a0c26d9, org.springframework.security.web.header.HeaderWriterFilter@3d6614d4, org.springframework.security.web.authentication.logout.LogoutFilter@24ceafbe, org.springframework.web.filter.CorsFilter@844e2fb, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@493ec25e, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@78071dc3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@62cf74d8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@25d4cf47, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@530e4d34, org.springframework.security.web.session.SessionManagementFilter@5bd3ef5c, org.springframework.security.web.access.ExceptionTranslationFilter@109009dc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6ef57cba]

info | 1 | 1753666486919 | 2025-07-28 09:34:46 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 34567 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753666487015 | 2025-07-28 09:34:47 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 34663 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753666487017 | 2025-07-28 09:34:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 34665 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753666487018 | 2025-07-28 09:34:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 34667 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753666487023 | 2025-07-28 09:34:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 34672 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753666487027 | 2025-07-28 09:34:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 34676 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753666487028 | 2025-07-28 09:34:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 34676 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753666487028 | 2025-07-28 09:34:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 34676 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753666487387 | 2025-07-28 09:34:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35036 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753666487560 | 2025-07-28 09:34:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35208 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753666487560 | 2025-07-28 09:34:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35208 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753666487560 | 2025-07-28 09:34:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35208 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753666487560 | 2025-07-28 09:34:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35208 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753666487561 | 2025-07-28 09:34:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35209 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753666487561 | 2025-07-28 09:34:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35209 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:tap0 (tap0)

info | 1 | 1753666487775 | 2025-07-28 09:34:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35424 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753666488024 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35672 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753666488025 | 2025-07-28 09:34:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35674 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753666488036 | 2025-07-28 09:34:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35685 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@6e012cdd, tech.powerjob.worker.actors.ProcessorTrackerActor@3e1e994, tech.powerjob.worker.actors.WorkerActor@723967b8])

info | 1 | 1753666488077 | 2025-07-28 09:34:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35725 | 0 | - | - | - | - | main o.r.Reflections Reflections took 29 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753666488084 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35732 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1753666488085 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35733 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@56d61161

info | 1 | 1753666488086 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35734 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@57e3ba47

info | 1 | 1753666488086 | 2025-07-28 09:34:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35734 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753666488086 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35734 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753666488089 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 35738 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 127 | 1753666488528 | 2025-07-28 09:34:48 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753666488909 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36557 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753666488909 | 2025-07-28 09:34:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36557 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753666488910 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36558 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753666488910 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36558 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753666488910 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36558 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753666488910 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36558 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753666488910 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36558 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753666488910 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36558 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753666488910 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36558 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753666488910 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36558 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753666488910 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36559 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753666488911 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36559 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753666488911 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36559 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753666488911 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36559 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753666488911 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36559 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753666488912 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36560 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753666488914 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36562 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753666488915 | 2025-07-28 09:34:48 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36563 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753666488915 | 2025-07-28 09:34:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36564 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 829.4 ms

info | 1 | 1753666489128 | 2025-07-28 09:34:49 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36776 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753666489136 | 2025-07-28 09:34:49 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36785 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753666489137 | 2025-07-28 09:34:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36785 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753666489144 | 2025-07-28 09:34:49 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 36793 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753666489356 | 2025-07-28 09:34:49 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37005 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753666489357 | 2025-07-28 09:34:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37005 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/9606add685a44e7eaf3a4f11cd2da0e6/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753666489364 | 2025-07-28 09:34:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37012 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/9606add685a44e7eaf3a4f11cd2da0e6/] on JVM exit successfully

info | 1 | 1753666489383 | 2025-07-28 09:34:49 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37031 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753666489384 | 2025-07-28 09:34:49 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37032 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 2.369 s, congratulations!

info | 156 | 1753666489389 | 2025-07-28 09:34:49 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 232b53600dae4642b0921e4f31f00615 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 156 | 1753666489390 | 2025-07-28 09:34:49 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 232b53600dae4642b0921e4f31f00615 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753666489449 | 2025-07-28 09:34:49 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | TomcatWebServer | start | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37097 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666489479 | 2025-07-28 09:34:49 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37127 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753666489491 | 2025-07-28 09:34:49 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37139 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753666489491 | 2025-07-28 09:34:49 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37139 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753666489519 | 2025-07-28 09:34:49 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37167 | 0 | - | - | - | - | main c.t.g.Application Started Application in 37.81 seconds (JVM running for 38.356)

info | 1 | 1753666489537 | 2025-07-28 09:34:49 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.641 | ************** | - | 2 | Application | main | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37185 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753666489537 | 2025-07-28 09:34:49 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.643 | ************** | - | 2 | Application | main | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 37185 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 226 | 1753666489542 | 2025-07-28 09:34:49 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 9d18e44fa0e24f2f8e734fb0c651286a | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 156 | 1753666501429 | 2025-07-28 09:35:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 232b53600dae4642b0921e4f31f00615 | - | - | - | - | 12040 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753666501462 | 2025-07-28 09:35:01 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.645 | ************** | - | 2 | Application | main | 7c2b334e787e471aa0b858f6c75f2a78 | - | - | - | - | 49110 | 0 | - | - | - | - | main c.t.c.c.c.SoaClient register http client : [http://mp-finance-trade-api.test1.hbmonitor.com/mp-finance-trade-api/api] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 29 | 1753666502197 | 2025-07-28 09:35:02 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | e232d45c088a4cef863efa7be5a2a841 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 29 | 1753666502250 | 2025-07-28 09:35:02 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | e232d45c088a4cef863efa7be5a2a841 | - | - | - | - | 53 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 29 | 1753666502253 | 2025-07-28 09:35:02 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | e232d45c088a4cef863efa7be5a2a841 | - | - | - | - | 56 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 29 | 1753666502273 | 2025-07-28 09:35:02 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | e232d45c088a4cef863efa7be5a2a841 | - | - | - | - | 76 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753666777745 | 2025-07-28 09:39:37 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 31938 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 16 | 1753666777738 | 2025-07-28 09:39:37 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 29aa79031cb34d4a96e9447a770c393c | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753666777760 | 2025-07-28 09:39:37 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753666778298 | 2025-07-28 09:39:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 544 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753666778314 | 2025-07-28 09:39:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 559 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753666778319 | 2025-07-28 09:39:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 564 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753666778326 | 2025-07-28 09:39:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 572 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753666778378 | 2025-07-28 09:39:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 623 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753666779108 | 2025-07-28 09:39:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 1354 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753666779243 | 2025-07-28 09:39:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 1489 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753666779247 | 2025-07-28 09:39:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 1492 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753666779247 | 2025-07-28 09:39:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 1492 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753666780332 | 2025-07-28 09:39:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 2578 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753666782463 | 2025-07-28 09:39:42 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 4708 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666782464 | 2025-07-28 09:39:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 4709 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753666782512 | 2025-07-28 09:39:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 4757 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 41 ms. Found 0 JPA repository interfaces.

info | 1 | 1753666782522 | 2025-07-28 09:39:42 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 4767 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753666782523 | 2025-07-28 09:39:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 4768 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753666782551 | 2025-07-28 09:39:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 4796 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.

info | 1 | 1753666783361 | 2025-07-28 09:39:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 5610 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b7882c7f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666783381 | 2025-07-28 09:39:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 5626 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$368e71f7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666783444 | 2025-07-28 09:39:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 5689 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$b4f77cc0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666783448 | 2025-07-28 09:39:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 5693 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666783514 | 2025-07-28 09:39:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 5759 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666783517 | 2025-07-28 09:39:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 5762 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753666784056 | 2025-07-28 09:39:44 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 6301 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666784069 | 2025-07-28 09:39:44 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 6315 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753666784070 | 2025-07-28 09:39:44 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 6315 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753666784163 | 2025-07-28 09:39:44 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 6408 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753666801991 | 2025-07-28 09:40:01 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 24236 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753666802070 | 2025-07-28 09:40:02 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 24315 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753666802101 | 2025-07-28 09:40:02 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 24346 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753666802221 | 2025-07-28 09:40:02 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 24466 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753666802291 | 2025-07-28 09:40:02 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 24537 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753666802399 | 2025-07-28 09:40:02 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 24644 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753666802405 | 2025-07-28 09:40:02 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 24651 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753666806412 | 2025-07-28 09:40:06 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 28657 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753666806923 | 2025-07-28 09:40:06 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 29168 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753666806948 | 2025-07-28 09:40:06 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 29193 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753666807193 | 2025-07-28 09:40:07 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c887e6c5ec2644dd9acbe5e5fa5137ff | - | - | - | - | 1 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753666807215 | 2025-07-28 09:40:07 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c887e6c5ec2644dd9acbe5e5fa5137ff | - | - | - | - | 23 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753666807224 | 2025-07-28 09:40:07 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c887e6c5ec2644dd9acbe5e5fa5137ff | - | - | - | - | 31 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753666807355 | 2025-07-28 09:40:07 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c887e6c5ec2644dd9acbe5e5fa5137ff | - | - | - | - | 162 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 129 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753666807362 | 2025-07-28 09:40:07 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c887e6c5ec2644dd9acbe5e5fa5137ff | - | - | - | - | 169 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753666807370 | 2025-07-28 09:40:07 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c887e6c5ec2644dd9acbe5e5fa5137ff | - | - | - | - | 177 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753666807379 | 2025-07-28 09:40:07 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c887e6c5ec2644dd9acbe5e5fa5137ff | - | - | - | - | 186 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753666807508 | 2025-07-28 09:40:07 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c887e6c5ec2644dd9acbe5e5fa5137ff | - | - | - | - | 316 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 127 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753666813464 | 2025-07-28 09:40:13 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 35710 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753666814809 | 2025-07-28 09:40:14 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37054 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@46c22594 with [org.springframework.security.web.session.DisableEncodeUrlFilter@5170c12d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@27446428, org.springframework.security.web.context.SecurityContextPersistenceFilter@76378274, org.springframework.security.web.header.HeaderWriterFilter@2c14eb95, org.springframework.security.web.authentication.logout.LogoutFilter@67b8b180, org.springframework.web.filter.CorsFilter@7ec77f27, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@44667128, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@295af581, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@e3ce861, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@60496385, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7ee39bbb, org.springframework.security.web.session.SessionManagementFilter@59f52bca, org.springframework.security.web.access.ExceptionTranslationFilter@1f63fdf4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@142c4dd6]

info | 1 | 1753666814831 | 2025-07-28 09:40:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37076 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753666814935 | 2025-07-28 09:40:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37180 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753666814938 | 2025-07-28 09:40:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37183 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753666814942 | 2025-07-28 09:40:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37187 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753666814947 | 2025-07-28 09:40:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37192 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753666814952 | 2025-07-28 09:40:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37197 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753666814952 | 2025-07-28 09:40:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37198 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753666814953 | 2025-07-28 09:40:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37198 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753666815289 | 2025-07-28 09:40:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37534 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753666815345 | 2025-07-28 09:40:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37590 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753666815345 | 2025-07-28 09:40:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37590 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753666815345 | 2025-07-28 09:40:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37590 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753666815345 | 2025-07-28 09:40:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37590 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753666815345 | 2025-07-28 09:40:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37590 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753666815347 | 2025-07-28 09:40:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37592 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753666815436 | 2025-07-28 09:40:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37681 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753666815437 | 2025-07-28 09:40:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37682 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753666815453 | 2025-07-28 09:40:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37698 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@67968225, tech.powerjob.worker.actors.ProcessorTrackerActor@5d773b96, tech.powerjob.worker.actors.WorkerActor@1c87b428])

info | 1 | 1753666815498 | 2025-07-28 09:40:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37743 | 0 | - | - | - | - | main o.r.Reflections Reflections took 33 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753666815509 | 2025-07-28 09:40:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37754 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1753666815511 | 2025-07-28 09:40:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37756 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@179f9f90

info | 1 | 1753666815511 | 2025-07-28 09:40:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37756 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753666815512 | 2025-07-28 09:40:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37757 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753666815517 | 2025-07-28 09:40:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 37762 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 130 | 1753666816187 | 2025-07-28 09:40:16 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753666816642 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38887 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753666816643 | 2025-07-28 09:40:16 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38888 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753666816644 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38889 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753666816644 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38889 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753666816644 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38889 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753666816644 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38889 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753666816644 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38889 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753666816644 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38889 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753666816644 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38889 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753666816644 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38889 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753666816644 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38890 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753666816647 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38892 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753666816647 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38892 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753666816647 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38892 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753666816648 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38893 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753666816650 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38895 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753666816657 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38902 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753666816657 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38902 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753666816657 | 2025-07-28 09:40:16 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 38903 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.146 s

info | 1 | 1753666816756 | 2025-07-28 09:40:16 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39001 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753666816760 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39005 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753666816760 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39005 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753666816763 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39008 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753666816968 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39213 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753666816968 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39213 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/71b0fd1bde1c48b7851466c8d3db3e29/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753666816974 | 2025-07-28 09:40:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39220 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/71b0fd1bde1c48b7851466c8d3db3e29/] on JVM exit successfully

info | 1 | 1753666816991 | 2025-07-28 09:40:16 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39236 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753666816992 | 2025-07-28 09:40:16 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39237 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 2.057 s, congratulations!

info | 158 | 1753666816996 | 2025-07-28 09:40:16 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | eb99a306032b4492a6aad79c2de42294 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 158 | 1753666816997 | 2025-07-28 09:40:16 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | eb99a306032b4492a6aad79c2de42294 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753666817047 | 2025-07-28 09:40:17 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39292 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753666817084 | 2025-07-28 09:40:17 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39330 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753666817105 | 2025-07-28 09:40:17 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39350 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753666817105 | 2025-07-28 09:40:17 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39350 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753666817131 | 2025-07-28 09:40:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39377 | 0 | - | - | - | - | main c.t.g.Application Started Application in 39.868 seconds (JVM running for 40.292)

info | 1 | 1753666817151 | 2025-07-28 09:40:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39396 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753666817151 | 2025-07-28 09:40:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39396 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 228 | 1753666817157 | 2025-07-28 09:40:17 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 0661a63c89b34518a4c6346008130b49 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 1 | ************* | 2025-07-28 09:40:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.641 | ************** | - | 2 | Application | main | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 39933 | 0 | - | - | - | - | main c.t.g.s.TqTradeCenterService account fee is bghjcieedfgjdfgg 10 0.22

info | 1 | ************* | 2025-07-28 09:40:21 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.643 | ************** | - | 2 | Application | main | 39398b6b64ae4746b8b085ecfa301fc8 | - | - | - | - | 43533 | 0 | - | - | - | - | main c.t.c.c.c.SoaClient register http client : [http://mp-finance-trade-api.test1.hbmonitor.com/mp-finance-trade-api/api] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 158 | ************* | 2025-07-28 09:40:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | eb99a306032b4492a6aad79c2de42294 | - | - | - | - | 14828 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1753666850378 | 2025-07-28 09:40:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 139989d85c004b6d82407f7855a95320 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1753666850386 | 2025-07-28 09:40:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 139989d85c004b6d82407f7855a95320 | - | - | - | - | 10 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666856997 | 2025-07-28 09:40:56 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666866995 | 2025-07-28 09:41:06 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 9998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666876994 | 2025-07-28 09:41:16 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 19998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666887016 | 2025-07-28 09:41:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 30019 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666897025 | 2025-07-28 09:41:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 40028 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666907037 | 2025-07-28 09:41:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 50040 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666917040 | 2025-07-28 09:41:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 60044 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666927043 | 2025-07-28 09:42:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 70047 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666937042 | 2025-07-28 09:42:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 80045 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666947044 | 2025-07-28 09:42:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 90048 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666957044 | 2025-07-28 09:42:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 100048 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666967041 | 2025-07-28 09:42:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 110044 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666977042 | 2025-07-28 09:42:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 120045 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1753666987045 | 2025-07-28 09:43:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 130049 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 242 | 1753666996502 | 2025-07-28 09:43:16 | v2/EtcdClientFactory$EtcdWatchThread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | EtcdClientFactory$EtcdWatchThread | run | 9b3e3d2419c64381b2b6b069e713b64d | - | - | - | - | 0 | 0 | - | - | - | - | Thread-30 c.t.c.c.c.SoaClient register http client : [http://mp-finance-trade-api.test4.hbmonitor.com/mp-finance-trade-api/api] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 242 | 1753666996508 | 2025-07-28 09:43:16 | v2/EtcdClientFactory$EtcdWatchThread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | EtcdClientFactory$EtcdWatchThread | run | 9b3e3d2419c64381b2b6b069e713b64d | - | - | - | - | 6 | 0 | - | - | - | - | Thread-30 c.t.g.s.SOAUtil soa服务[/newgonghui/soa/java/finance]的url修改为[http://mp-finance-trade-api.test4.hbmonitor.com/mp-finance-trade-api/api]

info | 162 | 1753666997041 | 2025-07-28 09:43:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1af928dae42e482bb115c50e532b4950 | - | - | - | - | 140045 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 29 | 1753667002038 | 2025-07-28 09:43:22 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | e08515103c6345e799790f89f897a257 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 29 | 1753667002065 | 2025-07-28 09:43:22 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | e08515103c6345e799790f89f897a257 | - | - | - | - | 27 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 29 | 1753667002066 | 2025-07-28 09:43:22 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | e08515103c6345e799790f89f897a257 | - | - | - | - | 29 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 29 | 1753667002091 | 2025-07-28 09:43:22 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | e08515103c6345e799790f89f897a257 | - | - | - | - | 53 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753667004100 | 2025-07-28 09:43:24 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 33944 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 16 | 1753667004095 | 2025-07-28 09:43:24 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 9e628b86015b4e25aa25a42cb2868654 | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753667004114 | 2025-07-28 09:43:24 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753667004664 | 2025-07-28 09:43:24 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 556 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753667004667 | 2025-07-28 09:43:24 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 559 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753667004673 | 2025-07-28 09:43:24 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 565 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753667004675 | 2025-07-28 09:43:24 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 567 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753667004677 | 2025-07-28 09:43:24 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 570 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753667004726 | 2025-07-28 09:43:24 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 618 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753667004761 | 2025-07-28 09:43:24 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 653 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753667004764 | 2025-07-28 09:43:24 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 656 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753667004764 | 2025-07-28 09:43:24 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 656 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753667004818 | 2025-07-28 09:43:24 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 710 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753667006859 | 2025-07-28 09:43:26 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 2751 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753667006861 | 2025-07-28 09:43:26 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 2753 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753667006910 | 2025-07-28 09:43:26 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 2802 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 43 ms. Found 0 JPA repository interfaces.

info | 1 | 1753667006919 | 2025-07-28 09:43:26 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 2812 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753667006921 | 2025-07-28 09:43:26 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 2813 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753667006954 | 2025-07-28 09:43:26 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 2846 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.

info | 1 | 1753667007804 | 2025-07-28 09:43:27 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 3700 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$7644bccf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667007832 | 2025-07-28 09:43:27 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 3724 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$f54b0247] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667007917 | 2025-07-28 09:43:27 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 3809 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$73b40d10] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667007922 | 2025-07-28 09:43:27 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 3814 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667007984 | 2025-07-28 09:43:27 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 3876 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667007988 | 2025-07-28 09:43:27 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 3880 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667008563 | 2025-07-28 09:43:28 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 4455 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753667008572 | 2025-07-28 09:43:28 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 4465 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753667008573 | 2025-07-28 09:43:28 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 4465 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753667008666 | 2025-07-28 09:43:28 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 4558 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753667025292 | 2025-07-28 09:43:45 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 21184 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753667025347 | 2025-07-28 09:43:45 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 21239 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753667025380 | 2025-07-28 09:43:45 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 21272 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753667025489 | 2025-07-28 09:43:45 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 21381 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753667025587 | 2025-07-28 09:43:45 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 21480 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753667025722 | 2025-07-28 09:43:45 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 21614 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753667025729 | 2025-07-28 09:43:45 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 21621 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753667029376 | 2025-07-28 09:43:49 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 25268 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753667029653 | 2025-07-28 09:43:49 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 25546 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753667029674 | 2025-07-28 09:43:49 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 25566 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753667029875 | 2025-07-28 09:43:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c8b7c0211f284c76afdb5a9fe6bd68bd | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753667029901 | 2025-07-28 09:43:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c8b7c0211f284c76afdb5a9fe6bd68bd | - | - | - | - | 27 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753667029911 | 2025-07-28 09:43:49 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c8b7c0211f284c76afdb5a9fe6bd68bd | - | - | - | - | 36 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753667030030 | 2025-07-28 09:43:50 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c8b7c0211f284c76afdb5a9fe6bd68bd | - | - | - | - | 156 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 117 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753667030036 | 2025-07-28 09:43:50 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c8b7c0211f284c76afdb5a9fe6bd68bd | - | - | - | - | 161 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753667030045 | 2025-07-28 09:43:50 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c8b7c0211f284c76afdb5a9fe6bd68bd | - | - | - | - | 170 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753667030052 | 2025-07-28 09:43:50 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c8b7c0211f284c76afdb5a9fe6bd68bd | - | - | - | - | 177 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753667030186 | 2025-07-28 09:43:50 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | c8b7c0211f284c76afdb5a9fe6bd68bd | - | - | - | - | 311 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 132 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753667033294 | 2025-07-28 09:43:53 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 29187 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753667034034 | 2025-07-28 09:43:54 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 29926 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@3f2be1a2 with [org.springframework.security.web.session.DisableEncodeUrlFilter@2d363251, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@69634bd4, org.springframework.security.web.context.SecurityContextPersistenceFilter@722d2bc6, org.springframework.security.web.header.HeaderWriterFilter@ee100ba, org.springframework.security.web.authentication.logout.LogoutFilter@10db1a9d, org.springframework.web.filter.CorsFilter@48e114b4, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@17698f4c, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@15e5c42c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@64a65c71, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@25fab989, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@75a301c3, org.springframework.security.web.session.SessionManagementFilter@1ba45a7d, org.springframework.security.web.access.ExceptionTranslationFilter@1021a5dc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1cfb1d4e]

info | 1 | 1753667034065 | 2025-07-28 09:43:54 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 29957 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753667034133 | 2025-07-28 09:43:54 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30025 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753667034134 | 2025-07-28 09:43:54 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30026 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753667034135 | 2025-07-28 09:43:54 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30027 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753667034137 | 2025-07-28 09:43:54 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30029 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753667034140 | 2025-07-28 09:43:54 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30032 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753667034140 | 2025-07-28 09:43:54 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30032 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753667034140 | 2025-07-28 09:43:54 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30032 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753667034323 | 2025-07-28 09:43:54 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30215 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753667034405 | 2025-07-28 09:43:54 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30297 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753667034406 | 2025-07-28 09:43:54 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30298 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753667034406 | 2025-07-28 09:43:54 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30298 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753667034406 | 2025-07-28 09:43:54 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30298 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753667034406 | 2025-07-28 09:43:54 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30298 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753667034408 | 2025-07-28 09:43:54 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30300 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753667034495 | 2025-07-28 09:43:54 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30388 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753667034496 | 2025-07-28 09:43:54 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30389 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753667034507 | 2025-07-28 09:43:54 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30400 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@6ac05035, tech.powerjob.worker.actors.ProcessorTrackerActor@15466b6c, tech.powerjob.worker.actors.WorkerActor@26b2b702])

info | 1 | 1753667034543 | 2025-07-28 09:43:54 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30435 | 0 | - | - | - | - | main o.r.Reflections Reflections took 25 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753667034549 | 2025-07-28 09:43:54 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30441 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1753667034550 | 2025-07-28 09:43:54 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30442 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@5cdb81f3

info | 1 | 1753667034550 | 2025-07-28 09:43:54 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30442 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753667034551 | 2025-07-28 09:43:54 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30443 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753667034554 | 2025-07-28 09:43:54 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 30446 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 127 | 1753667035018 | 2025-07-28 09:43:55 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753667035380 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31272 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753667035381 | 2025-07-28 09:43:55 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753667035381 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753667035381 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753667035381 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753667035381 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753667035382 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753667035382 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753667035382 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753667035382 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753667035382 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753667035382 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753667035382 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753667035382 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753667035382 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753667035384 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31276 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753667035386 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31278 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753667035387 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31279 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753667035387 | 2025-07-28 09:43:55 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31280 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 837.0 ms

info | 1 | 1753667035456 | 2025-07-28 09:43:55 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31348 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753667035460 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31353 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753667035461 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31353 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753667035464 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31356 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753667035650 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31542 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753667035650 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31542 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/171b4070cd5248a7931a18d44762f885/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753667035658 | 2025-07-28 09:43:55 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31550 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/171b4070cd5248a7931a18d44762f885/] on JVM exit successfully

info | 1 | 1753667035676 | 2025-07-28 09:43:55 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31568 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753667035677 | 2025-07-28 09:43:55 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31569 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.544 s, congratulations!

info | 160 | 1753667035683 | 2025-07-28 09:43:55 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 72362d89c6da412ea5533c85b09817e0 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 160 | 1753667035683 | 2025-07-28 09:43:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 72362d89c6da412ea5533c85b09817e0 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753667035729 | 2025-07-28 09:43:55 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31622 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753667035760 | 2025-07-28 09:43:55 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31653 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753667035778 | 2025-07-28 09:43:55 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31670 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753667035778 | 2025-07-28 09:43:55 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31670 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753667035812 | 2025-07-28 09:43:55 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31704 | 0 | - | - | - | - | main c.t.g.Application Started Application in 32.373 seconds (JVM running for 32.868)

info | 1 | 1753667035830 | 2025-07-28 09:43:55 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31722 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753667035830 | 2025-07-28 09:43:55 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 31722 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 230 | 1753667035835 | 2025-07-28 09:43:55 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 000c6b20801f4defa660907835f30b9a | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 1 | ************* | 2025-07-28 09:43:56 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.641 | ************** | - | 2 | Application | main | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 32182 | 0 | - | - | - | - | main c.t.g.s.TqTradeCenterService account fee is bghjcieedfgjdfgg 10 0.22

info | 1 | ************* | 2025-07-28 09:44:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.643 | ************** | - | 2 | Application | main | 5ddd5180303742098d44b54c7da37979 | - | - | - | - | 36388 | 0 | - | - | - | - | main c.t.c.c.c.SoaClient register http client : [http://mp-finance-trade-api.test4.hbmonitor.com/mp-finance-trade-api/api] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 160 | ************* | 2025-07-28 09:44:19 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 72362d89c6da412ea5533c85b09817e0 | - | - | - | - | 24232 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1753667059916 | 2025-07-28 09:44:19 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 72362d89c6da412ea5533c85b09817e0 | - | - | - | - | 24234 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 29 | 1753667060099 | 2025-07-28 09:44:20 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 1b926296aab64890891a90dcf94e0c13 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 16 | 1753667142018 | 2025-07-28 09:45:42 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 77dc48aa6e8f4f77a5194f075eced1d7 | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753667142025 | 2025-07-28 09:45:42 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 34840 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1753667142037 | 2025-07-28 09:45:42 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753667142622 | 2025-07-28 09:45:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 591 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753667142624 | 2025-07-28 09:45:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 593 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753667142627 | 2025-07-28 09:45:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 596 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753667142629 | 2025-07-28 09:45:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 598 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753667142633 | 2025-07-28 09:45:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 602 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753667142688 | 2025-07-28 09:45:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 657 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753667142725 | 2025-07-28 09:45:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 695 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753667142728 | 2025-07-28 09:45:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 697 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753667142728 | 2025-07-28 09:45:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 697 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753667142781 | 2025-07-28 09:45:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 750 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753667144923 | 2025-07-28 09:45:44 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 2893 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753667144925 | 2025-07-28 09:45:44 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 2894 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753667144977 | 2025-07-28 09:45:44 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 2947 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 46 ms. Found 0 JPA repository interfaces.

info | 1 | 1753667145012 | 2025-07-28 09:45:45 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 2981 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753667145014 | 2025-07-28 09:45:45 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 2983 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753667145046 | 2025-07-28 09:45:45 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 3015 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.

info | 1 | 1753667145820 | 2025-07-28 09:45:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 3792 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$57ab50b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667145843 | 2025-07-28 09:45:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 3813 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d6b1962c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667145908 | 2025-07-28 09:45:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 3877 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$551aa0f5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667145912 | 2025-07-28 09:45:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 3881 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667145977 | 2025-07-28 09:45:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 3946 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667145982 | 2025-07-28 09:45:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 3951 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667146533 | 2025-07-28 09:45:46 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 4502 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753667146546 | 2025-07-28 09:45:46 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 4515 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753667146547 | 2025-07-28 09:45:46 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 4516 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753667146642 | 2025-07-28 09:45:46 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 4611 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753667163404 | 2025-07-28 09:46:03 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 21373 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753667163484 | 2025-07-28 09:46:03 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 21454 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753667163521 | 2025-07-28 09:46:03 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 21490 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753667163633 | 2025-07-28 09:46:03 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 21602 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753667163712 | 2025-07-28 09:46:03 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 21681 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753667163834 | 2025-07-28 09:46:03 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 21803 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753667163841 | 2025-07-28 09:46:03 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 21810 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753667167445 | 2025-07-28 09:46:07 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 25415 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753667167768 | 2025-07-28 09:46:07 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 25738 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753667167790 | 2025-07-28 09:46:07 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 25759 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753667168056 | 2025-07-28 09:46:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 32ff8b89b97f43e99eee05c964e7ebba | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753667168079 | 2025-07-28 09:46:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 32ff8b89b97f43e99eee05c964e7ebba | - | - | - | - | 23 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753667168090 | 2025-07-28 09:46:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 32ff8b89b97f43e99eee05c964e7ebba | - | - | - | - | 34 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753667168218 | 2025-07-28 09:46:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 32ff8b89b97f43e99eee05c964e7ebba | - | - | - | - | 163 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 127 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753667168225 | 2025-07-28 09:46:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 32ff8b89b97f43e99eee05c964e7ebba | - | - | - | - | 169 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753667168234 | 2025-07-28 09:46:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 32ff8b89b97f43e99eee05c964e7ebba | - | - | - | - | 178 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753667168242 | 2025-07-28 09:46:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 32ff8b89b97f43e99eee05c964e7ebba | - | - | - | - | 186 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753667168353 | 2025-07-28 09:46:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 32ff8b89b97f43e99eee05c964e7ebba | - | - | - | - | 298 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 109 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753667171400 | 2025-07-28 09:46:11 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 29370 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753667172259 | 2025-07-28 09:46:12 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 30228 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@1e9b93ed with [org.springframework.security.web.session.DisableEncodeUrlFilter@d402fe7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@59feea1c, org.springframework.security.web.context.SecurityContextPersistenceFilter@3c1d9114, org.springframework.security.web.header.HeaderWriterFilter@7403b7df, org.springframework.security.web.authentication.logout.LogoutFilter@648cfef9, org.springframework.web.filter.CorsFilter@7adb0c4f, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@625d356e, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@6f81e89b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@12c9fb03, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7a3769ed, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2d471a7d, org.springframework.security.web.session.SessionManagementFilter@3c8fdf2f, org.springframework.security.web.access.ExceptionTranslationFilter@3709c652, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3e5d46fd]

info | 1 | 1753667172325 | 2025-07-28 09:46:12 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 30294 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753667172457 | 2025-07-28 09:46:12 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 30426 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753667172459 | 2025-07-28 09:46:12 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 30428 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753667172462 | 2025-07-28 09:46:12 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 30431 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753667172464 | 2025-07-28 09:46:12 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 30433 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753667172467 | 2025-07-28 09:46:12 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 30436 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753667172467 | 2025-07-28 09:46:12 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 30436 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753667172467 | 2025-07-28 09:46:12 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 30436 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753667172894 | 2025-07-28 09:46:12 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 30863 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753667173153 | 2025-07-28 09:46:13 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31122 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753667173153 | 2025-07-28 09:46:13 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31122 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753667173153 | 2025-07-28 09:46:13 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31123 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753667173154 | 2025-07-28 09:46:13 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31123 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753667173154 | 2025-07-28 09:46:13 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31123 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753667173156 | 2025-07-28 09:46:13 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31125 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753667173413 | 2025-07-28 09:46:13 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31383 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753667173415 | 2025-07-28 09:46:13 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31384 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753667173429 | 2025-07-28 09:46:13 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31398 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@696dbfb8, tech.powerjob.worker.actors.ProcessorTrackerActor@298e6a00, tech.powerjob.worker.actors.WorkerActor@53baf924])

info | 1 | 1753667173470 | 2025-07-28 09:46:13 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31439 | 0 | - | - | - | - | main o.r.Reflections Reflections took 26 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753667173477 | 2025-07-28 09:46:13 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31446 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1753667173478 | 2025-07-28 09:46:13 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31447 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@7107ffd9

info | 1 | 1753667173478 | 2025-07-28 09:46:13 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31447 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753667173479 | 2025-07-28 09:46:13 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31448 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753667173482 | 2025-07-28 09:46:13 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 31451 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 125 | 1753667173952 | 2025-07-28 09:46:13 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753667174312 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32281 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753667174313 | 2025-07-28 09:46:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32282 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753667174313 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32282 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753667174313 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32282 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753667174313 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753667174314 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753667174314 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753667174314 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753667174314 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753667174314 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753667174314 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753667174314 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753667174314 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753667174314 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753667174314 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32283 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753667174316 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32285 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753667174318 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32288 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753667174319 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32288 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753667174319 | 2025-07-28 09:46:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32289 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 841.0 ms

info | 1 | 1753667174562 | 2025-07-28 09:46:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32532 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753667174569 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32538 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753667174569 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32538 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753667174574 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32543 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753667174764 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32734 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753667174765 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32734 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/c9a2adb35b1f4a849d0d8fd0f1fea275/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753667174771 | 2025-07-28 09:46:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32740 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/c9a2adb35b1f4a849d0d8fd0f1fea275/] on JVM exit successfully

info | 1 | 1753667174784 | 2025-07-28 09:46:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32753 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753667174785 | 2025-07-28 09:46:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32754 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 2.328 s, congratulations!

info | 158 | 1753667174789 | 2025-07-28 09:46:14 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 9fab6907e74a4de48cb7b80f061e7922 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 158 | 1753667174789 | 2025-07-28 09:46:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 9fab6907e74a4de48cb7b80f061e7922 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753667174847 | 2025-07-28 09:46:14 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32816 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753667174878 | 2025-07-28 09:46:14 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32847 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753667174903 | 2025-07-28 09:46:14 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32872 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753667174903 | 2025-07-28 09:46:14 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32872 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753667174937 | 2025-07-28 09:46:14 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32906 | 0 | - | - | - | - | main c.t.g.Application Started Application in 33.386 seconds (JVM running for 33.826)

info | 1 | 1753667174964 | 2025-07-28 09:46:14 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32933 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753667174964 | 2025-07-28 09:46:14 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 32933 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 228 | 1753667174969 | 2025-07-28 09:46:14 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | b2016d74975a45feb0a0477c6f045f05 | - | - | - | - | 1 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 1 | ************* | 2025-07-28 09:46:15 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.641 | ************** | - | 2 | Application | main | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 33432 | 0 | - | - | - | - | main c.t.g.s.TqTradeCenterService account fee is bghjcieedfgjdfgg 10 0.22

info | 1 | ************* | 2025-07-28 09:46:19 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.643 | ************** | - | 2 | Application | main | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 37654 | 0 | - | - | - | - | main c.t.c.c.c.SoaClient register http client : [http://mp-finance-trade-api.test4.hbmonitor.com/mp-finance-trade-api/api] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 1 | ************* | 2025-07-28 09:46:20 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.645 | ************** | - | 2 | Application | main | e30d4b9f18d3466e8567b2759a0941c0 | - | - | - | - | 38577 | 0 | - | - | - | - | main c.t.g.s.s.i.FinanceSoaServiceImpl finance getForumAccountChatReward result bghjcieedfgjdfgg 4312

info | 158 | ************* | 2025-07-28 09:46:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 9fab6907e74a4de48cb7b80f061e7922 | - | - | - | - | 9998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 29 | ************* | 2025-07-28 09:46:33 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 659d9373405043eda524076918a27790 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 29 | ************* | 2025-07-28 09:46:33 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 659d9373405043eda524076918a27790 | - | - | - | - | 34 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 29 | 1753667193940 | 2025-07-28 09:46:33 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | 659d9373405043eda524076918a27790 | - | - | - | - | 35 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 29 | 1753667193955 | 2025-07-28 09:46:33 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | 659d9373405043eda524076918a27790 | - | - | - | - | 50 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753667198540 | 2025-07-28 09:46:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 35220 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 16 | 1753667198534 | 2025-07-28 09:46:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 6f64fca184b04a018b17bdb402d98c8f | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753667198553 | 2025-07-28 09:46:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753667199061 | 2025-07-28 09:46:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 514 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753667199065 | 2025-07-28 09:46:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 518 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753667199067 | 2025-07-28 09:46:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 520 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753667199069 | 2025-07-28 09:46:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 523 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753667199071 | 2025-07-28 09:46:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 524 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753667199110 | 2025-07-28 09:46:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 563 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753667199146 | 2025-07-28 09:46:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 599 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753667199148 | 2025-07-28 09:46:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 601 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753667199148 | 2025-07-28 09:46:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 602 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753667199200 | 2025-07-28 09:46:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 653 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753667201199 | 2025-07-28 09:46:41 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 2652 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753667201201 | 2025-07-28 09:46:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 2654 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753667201246 | 2025-07-28 09:46:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 2700 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 41 ms. Found 0 JPA repository interfaces.

info | 1 | 1753667201255 | 2025-07-28 09:46:41 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 2709 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753667201257 | 2025-07-28 09:46:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 2710 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753667201285 | 2025-07-28 09:46:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 2738 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.

info | 1 | 1753667202590 | 2025-07-28 09:46:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 4047 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$819a76f5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667202612 | 2025-07-28 09:46:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 4065 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$a0bc6d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667202672 | 2025-07-28 09:46:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 4125 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$7f09c736] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667202677 | 2025-07-28 09:46:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 4131 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667202740 | 2025-07-28 09:46:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 4193 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667202745 | 2025-07-28 09:46:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 4198 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667203438 | 2025-07-28 09:46:43 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 4892 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753667203496 | 2025-07-28 09:46:43 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 4949 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753667203497 | 2025-07-28 09:46:43 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 4950 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753667203616 | 2025-07-28 09:46:43 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 2b305212d12a40c8b2bcd5815d253dd8 | - | - | - | - | 5069 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 16 | 1753667834938 | 2025-07-28 09:57:14 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 1aa7a4ccd8ba4fbd8e7c8e4c6ea3a901 | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753667834944 | 2025-07-28 09:57:14 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 39234 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1753667834956 | 2025-07-28 09:57:14 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753667835467 | 2025-07-28 09:57:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 521 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753667835474 | 2025-07-28 09:57:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 524 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753667835476 | 2025-07-28 09:57:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 526 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753667835479 | 2025-07-28 09:57:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 529 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753667835481 | 2025-07-28 09:57:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 531 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753667835518 | 2025-07-28 09:57:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 569 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753667835556 | 2025-07-28 09:57:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 606 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753667835558 | 2025-07-28 09:57:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 608 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753667835558 | 2025-07-28 09:57:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 608 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753667835607 | 2025-07-28 09:57:15 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 657 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753667837653 | 2025-07-28 09:57:17 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 2704 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753667837655 | 2025-07-28 09:57:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 2705 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753667837712 | 2025-07-28 09:57:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 2762 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 50 ms. Found 0 JPA repository interfaces.

info | 1 | 1753667837723 | 2025-07-28 09:57:17 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 2773 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753667837724 | 2025-07-28 09:57:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 2774 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753667837754 | 2025-07-28 09:57:17 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 2804 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.

info | 1 | 1753667838572 | 2025-07-28 09:57:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 3626 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$8687717e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667838592 | 2025-07-28 09:57:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 3642 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$58db6f6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667838650 | 2025-07-28 09:57:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 3701 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$83f6c1bf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667838655 | 2025-07-28 09:57:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 3705 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667838718 | 2025-07-28 09:57:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 3768 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667838723 | 2025-07-28 09:57:18 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 3773 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753667839295 | 2025-07-28 09:57:19 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 4345 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753667839311 | 2025-07-28 09:57:19 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 4361 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753667839312 | 2025-07-28 09:57:19 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 4362 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753667839424 | 2025-07-28 09:57:19 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 4474 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753667856588 | 2025-07-28 09:57:36 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 21639 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753667856682 | 2025-07-28 09:57:36 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 21732 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753667856716 | 2025-07-28 09:57:36 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 21767 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753667856828 | 2025-07-28 09:57:36 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 21878 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753667856916 | 2025-07-28 09:57:36 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 21966 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753667857028 | 2025-07-28 09:57:37 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 22078 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753667857034 | 2025-07-28 09:57:37 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 22084 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753667860706 | 2025-07-28 09:57:40 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 25757 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753667861016 | 2025-07-28 09:57:41 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 26066 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753667861040 | 2025-07-28 09:57:41 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 26090 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753667861271 | 2025-07-28 09:57:41 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 270915d0318b41f19f9af0b0f37da25b | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753667861298 | 2025-07-28 09:57:41 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 270915d0318b41f19f9af0b0f37da25b | - | - | - | - | 26 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753667861307 | 2025-07-28 09:57:41 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 270915d0318b41f19f9af0b0f37da25b | - | - | - | - | 35 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753667861479 | 2025-07-28 09:57:41 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 270915d0318b41f19f9af0b0f37da25b | - | - | - | - | 208 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 170 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753667861485 | 2025-07-28 09:57:41 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 270915d0318b41f19f9af0b0f37da25b | - | - | - | - | 213 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753667861493 | 2025-07-28 09:57:41 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 270915d0318b41f19f9af0b0f37da25b | - | - | - | - | 221 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753667861501 | 2025-07-28 09:57:41 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 270915d0318b41f19f9af0b0f37da25b | - | - | - | - | 229 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753667861605 | 2025-07-28 09:57:41 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 270915d0318b41f19f9af0b0f37da25b | - | - | - | - | 334 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 102 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753667865711 | 2025-07-28 09:57:45 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 30761 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753667866422 | 2025-07-28 09:57:46 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31473 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@6a909132 with [org.springframework.security.web.session.DisableEncodeUrlFilter@2491aa04, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@730b7e7e, org.springframework.security.web.context.SecurityContextPersistenceFilter@b879650, org.springframework.security.web.header.HeaderWriterFilter@4d7e510c, org.springframework.security.web.authentication.logout.LogoutFilter@2b0e378c, org.springframework.web.filter.CorsFilter@2203ee34, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@7343da7c, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@7cbeeca7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@74dc7276, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@608de3fb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@12c9fb03, org.springframework.security.web.session.SessionManagementFilter@674daf24, org.springframework.security.web.access.ExceptionTranslationFilter@ffa976f, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@66e63575]

info | 1 | 1753667866443 | 2025-07-28 09:57:46 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31493 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753667866546 | 2025-07-28 09:57:46 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31596 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753667866548 | 2025-07-28 09:57:46 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31598 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753667866550 | 2025-07-28 09:57:46 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31601 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753667866553 | 2025-07-28 09:57:46 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31604 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753667866557 | 2025-07-28 09:57:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31607 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753667866557 | 2025-07-28 09:57:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31607 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753667866557 | 2025-07-28 09:57:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31607 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753667866835 | 2025-07-28 09:57:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31886 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753667866910 | 2025-07-28 09:57:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31960 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753667866910 | 2025-07-28 09:57:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31960 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753667866910 | 2025-07-28 09:57:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31960 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753667866910 | 2025-07-28 09:57:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31961 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753667866911 | 2025-07-28 09:57:46 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31961 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753667866912 | 2025-07-28 09:57:46 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 31962 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753667866975 | 2025-07-28 09:57:46 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32025 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753667866976 | 2025-07-28 09:57:46 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32026 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753667866985 | 2025-07-28 09:57:46 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32036 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@3e848f48, tech.powerjob.worker.actors.ProcessorTrackerActor@3b32fef7, tech.powerjob.worker.actors.WorkerActor@46872fde])

info | 1 | 1753667867017 | 2025-07-28 09:57:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32067 | 0 | - | - | - | - | main o.r.Reflections Reflections took 22 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753667867027 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32077 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1753667867028 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32078 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@4e6b2bb6

info | 1 | 1753667867029 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32079 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@3f0ea21d

info | 1 | 1753667867030 | 2025-07-28 09:57:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32080 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753667867030 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32080 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753667867033 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32084 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 126 | 1753667867553 | 2025-07-28 09:57:47 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753667867945 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32996 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753667867946 | 2025-07-28 09:57:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32996 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753667867946 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32996 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753667867946 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32996 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753667867947 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32997 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753667867949 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 32999 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753667867951 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33001 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753667867952 | 2025-07-28 09:57:47 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33002 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753667867952 | 2025-07-28 09:57:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33002 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 922.5 ms

info | 1 | 1753667868046 | 2025-07-28 09:57:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33097 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753667868052 | 2025-07-28 09:57:48 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33102 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753667868052 | 2025-07-28 09:57:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33102 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753667868057 | 2025-07-28 09:57:48 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33107 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753667868274 | 2025-07-28 09:57:48 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33324 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753667868274 | 2025-07-28 09:57:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33324 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/616031012159468c920f7d6b016c13f8/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753667868281 | 2025-07-28 09:57:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33331 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/616031012159468c920f7d6b016c13f8/] on JVM exit successfully

info | 1 | 1753667868296 | 2025-07-28 09:57:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33346 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753667868297 | 2025-07-28 09:57:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33347 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.751 s, congratulations!

info | 158 | 1753667868301 | 2025-07-28 09:57:48 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 4b15eb83a90a4e69872f6d770b4760e8 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 158 | 1753667868301 | 2025-07-28 09:57:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 4b15eb83a90a4e69872f6d770b4760e8 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753667868344 | 2025-07-28 09:57:48 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | TomcatWebServer | start | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33394 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753667868384 | 2025-07-28 09:57:48 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33434 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753667868400 | 2025-07-28 09:57:48 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33450 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753667868401 | 2025-07-28 09:57:48 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33451 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753667868432 | 2025-07-28 09:57:48 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33482 | 0 | - | - | - | - | main c.t.g.Application Started Application in 33.989 seconds (JVM running for 34.455)

info | 1 | 1753667868453 | 2025-07-28 09:57:48 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33503 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753667868453 | 2025-07-28 09:57:48 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.641 | ************** | - | 2 | Application | main | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 33503 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 228 | 1753667868459 | 2025-07-28 09:57:48 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | d4701c7458b44768987a436e89330d29 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 1 | 1753667869485 | 2025-07-28 09:57:49 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.645 | ************** | - | 2 | Application | main | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 34535 | 0 | - | - | - | - | main c.t.c.c.c.SoaClient register http client : [http://mp-finance-trade-api.test4.hbmonitor.com/mp-finance-trade-api/api] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 163 | 1753667882295 | 2025-07-28 09:58:02 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 1283d94d5def4c20bb4f4310c8aac43f | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | ************* | 2025-07-28 09:58:02 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.647 | ************** | - | 2 | Application | main | 0fbcbbc01e28415490e52e0f8f33a7fb | - | - | - | - | 47365 | 0 | - | - | - | - | main c.t.g.s.s.i.FinanceSoaServiceImpl finance getForumAccountChatReward result bghjcieedfgjdfgg {"balance":4312}

info | 29 | ************* | 2025-07-28 09:58:07 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | bf8ccbf1cead465f88c6c7d024eb1abd | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 29 | ************* | 2025-07-28 09:58:07 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | bf8ccbf1cead465f88c6c7d024eb1abd | - | - | - | - | 37 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 29 | ************* | 2025-07-28 09:58:07 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | bf8ccbf1cead465f88c6c7d024eb1abd | - | - | - | - | 38 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 29 | 1753667887594 | 2025-07-28 09:58:07 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | bf8ccbf1cead465f88c6c7d024eb1abd | - | - | - | - | 53 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

