info | 1 | 1732675292943 | 2024-11-27 10:41:32 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 95202 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1732675292936 | 2024-11-27 10:41:32 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 623a65309dfc4d21984c5cf0f367eb39 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1732675292953 | 2024-11-27 10:41:32 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1732675293477 | 2024-11-27 10:41:33 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 529 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1732675293487 | 2024-11-27 10:41:33 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 540 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1732675293497 | 2024-11-27 10:41:33 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 549 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1732675293506 | 2024-11-27 10:41:33 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 558 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1732675293518 | 2024-11-27 10:41:33 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 570 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1732675293854 | 2024-11-27 10:41:33 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 907 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1732675293955 | 2024-11-27 10:41:33 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 1008 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1732675293959 | 2024-11-27 10:41:33 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 1011 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1732675293959 | 2024-11-27 10:41:33 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 1011 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1732675294657 | 2024-11-27 10:41:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 1710 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1732675297663 | 2024-11-27 10:41:37 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 4715 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732675297664 | 2024-11-27 10:41:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 4716 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1732675297705 | 2024-11-27 10:41:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 4757 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 35 ms. Found 0 JPA repository interfaces.

info | 1 | 1732675297715 | 2024-11-27 10:41:37 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 4767 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732675297716 | 2024-11-27 10:41:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 4768 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1732675297742 | 2024-11-27 10:41:37 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 4794 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1732675298784 | 2024-11-27 10:41:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 5836 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$1515ea38] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675298802 | 2024-11-27 10:41:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 5854 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$941c2fb0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675298863 | 2024-11-27 10:41:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 5915 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$12853a79] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675298867 | 2024-11-27 10:41:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 5920 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675298937 | 2024-11-27 10:41:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 5989 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675298941 | 2024-11-27 10:41:38 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 5993 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675299602 | 2024-11-27 10:41:39 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 6654 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1732675299611 | 2024-11-27 10:41:39 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 6663 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1732675299611 | 2024-11-27 10:41:39 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 6663 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1732675299695 | 2024-11-27 10:41:39 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 6747 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1732675311844 | 2024-11-27 10:41:51 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 18896 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1732675311910 | 2024-11-27 10:41:51 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 18962 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1732675311950 | 2024-11-27 10:41:51 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 19002 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1732675312094 | 2024-11-27 10:41:52 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 19146 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1732675312186 | 2024-11-27 10:41:52 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 19238 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1732675312329 | 2024-11-27 10:41:52 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 19382 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1732675312337 | 2024-11-27 10:41:52 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 19389 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1732675316363 | 2024-11-27 10:41:56 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 23416 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1732675316713 | 2024-11-27 10:41:56 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 23765 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1732675316750 | 2024-11-27 10:41:56 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 23802 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1732675316920 | 2024-11-27 10:41:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 675683beb2fa473592cfa577a4ee2c79 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1732675316977 | 2024-11-27 10:41:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 675683beb2fa473592cfa577a4ee2c79 | - | - | - | - | 57 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 41 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1732675316987 | 2024-11-27 10:41:56 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 675683beb2fa473592cfa577a4ee2c79 | - | - | - | - | 67 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1732675317127 | 2024-11-27 10:41:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 675683beb2fa473592cfa577a4ee2c79 | - | - | - | - | 207 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 137 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1732675317141 | 2024-11-27 10:41:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 675683beb2fa473592cfa577a4ee2c79 | - | - | - | - | 222 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1732675317149 | 2024-11-27 10:41:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 675683beb2fa473592cfa577a4ee2c79 | - | - | - | - | 229 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1732675317157 | 2024-11-27 10:41:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 675683beb2fa473592cfa577a4ee2c79 | - | - | - | - | 237 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1732675317441 | 2024-11-27 10:41:57 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 675683beb2fa473592cfa577a4ee2c79 | - | - | - | - | 521 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 282 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1732675323924 | 2024-11-27 10:42:03 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 30976 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1732675324875 | 2024-11-27 10:42:04 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 31927 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@5b5752a with [org.springframework.security.web.session.DisableEncodeUrlFilter@daadcfc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4da9ddf5, org.springframework.security.web.context.SecurityContextPersistenceFilter@44084477, org.springframework.security.web.header.HeaderWriterFilter@d583496, org.springframework.security.web.authentication.logout.LogoutFilter@65a61862, org.springframework.web.filter.CorsFilter@6f33e4be, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@1a4f3538, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@68a7cd0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@66ed73c1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7fcd7f91, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2838450e, org.springframework.security.web.session.SessionManagementFilter@254cc4c7, org.springframework.security.web.access.ExceptionTranslationFilter@7a5dff85, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1183b7f]

info | 1 | 1732675324892 | 2024-11-27 10:42:04 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 31944 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1732675324974 | 2024-11-27 10:42:04 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 32026 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1732675324976 | 2024-11-27 10:42:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 32028 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1732675324977 | 2024-11-27 10:42:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 32029 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1732675324981 | 2024-11-27 10:42:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 32034 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1732675324985 | 2024-11-27 10:42:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 32037 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1732675324986 | 2024-11-27 10:42:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 32038 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1732675324986 | 2024-11-27 10:42:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 32038 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1732675326558 | 2024-11-27 10:42:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33611 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1732675326559 | 2024-11-27 10:42:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33611 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1732675326559 | 2024-11-27 10:42:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33611 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1732675326559 | 2024-11-27 10:42:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33611 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1732675326560 | 2024-11-27 10:42:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33612 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1732675326564 | 2024-11-27 10:42:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33617 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1732675326658 | 2024-11-27 10:42:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33710 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1732675326659 | 2024-11-27 10:42:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33711 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1732675326679 | 2024-11-27 10:42:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33731 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@263a5cf2, tech.powerjob.worker.actors.ProcessorTrackerActor@3ed56eaf, tech.powerjob.worker.actors.WorkerActor@59de2e13])

info | 1 | 1732675326718 | 2024-11-27 10:42:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33770 | 0 | - | - | - | - | main o.r.Reflections Reflections took 25 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1732675326727 | 2024-11-27 10:42:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33779 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1732675326727 | 2024-11-27 10:42:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33780 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@61e02857

info | 1 | 1732675326728 | 2024-11-27 10:42:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33780 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@3867e848

info | 1 | 1732675326728 | 2024-11-27 10:42:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33780 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1732675326729 | 2024-11-27 10:42:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33781 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1732675326732 | 2024-11-27 10:42:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 33784 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 129 | 1732675327278 | 2024-11-27 10:42:07 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1732675327687 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34739 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1732675327687 | 2024-11-27 10:42:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34739 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34740 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732675327688 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34741 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732675327689 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34741 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732675327691 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34743 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1732675327693 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34745 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1732675327694 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34746 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1732675327694 | 2024-11-27 10:42:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34746 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 965.6 ms

info | 1 | 1732675327820 | 2024-11-27 10:42:07 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34873 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1732675327827 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34879 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1732675327827 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34879 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1732675327832 | 2024-11-27 10:42:07 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 34884 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1732675328051 | 2024-11-27 10:42:08 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35103 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1732675328051 | 2024-11-27 10:42:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35104 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/a8c2d683fc7d47fabe85ff010eab2b41/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1732675328058 | 2024-11-27 10:42:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35110 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/a8c2d683fc7d47fabe85ff010eab2b41/] on JVM exit successfully

info | 1 | 1732675328072 | 2024-11-27 10:42:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35124 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1732675328073 | 2024-11-27 10:42:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35125 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 3.099 s, congratulations!

info | 162 | 1732675328077 | 2024-11-27 10:42:08 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 162 | 1732675328078 | 2024-11-27 10:42:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1732675328121 | 2024-11-27 10:42:08 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35174 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1732675328145 | 2024-11-27 10:42:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35197 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1732675328165 | 2024-11-27 10:42:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35217 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1732675328165 | 2024-11-27 10:42:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35217 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1732675328198 | 2024-11-27 10:42:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35250 | 0 | - | - | - | - | main c.t.g.Application Started Application in 35.716 seconds (JVM running for 36.239)

info | 1 | 1732675328217 | 2024-11-27 10:42:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35269 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1732675328217 | 2024-11-27 10:42:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | b2c6a69795074e7d8b34e3f74d42b355 | - | - | - | - | 35269 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 232 | 1732675328221 | 2024-11-27 10:42:08 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 9fbb6f3edc01478e8455fe8611d4cad1 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 162 | 1732675338074 | 2024-11-27 10:42:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 9997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675348078 | 2024-11-27 10:42:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 20002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675358077 | 2024-11-27 10:42:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 30001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675368078 | 2024-11-27 10:42:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 40004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675378077 | 2024-11-27 10:42:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 50001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675388074 | 2024-11-27 10:43:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 59997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675398074 | 2024-11-27 10:43:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 70000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732675408077 | 2024-11-27 10:43:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b0c8dc5a768243bd81986b9f01c5274c | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732675418078 | 2024-11-27 10:43:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b0c8dc5a768243bd81986b9f01c5274c | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732675428081 | 2024-11-27 10:43:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b0c8dc5a768243bd81986b9f01c5274c | - | - | - | - | 20007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732675438079 | 2024-11-27 10:43:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b0c8dc5a768243bd81986b9f01c5274c | - | - | - | - | 30003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732675448078 | 2024-11-27 10:44:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b0c8dc5a768243bd81986b9f01c5274c | - | - | - | - | 40002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732675458079 | 2024-11-27 10:44:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b0c8dc5a768243bd81986b9f01c5274c | - | - | - | - | 50004 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732675468077 | 2024-11-27 10:44:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b0c8dc5a768243bd81986b9f01c5274c | - | - | - | - | 60000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732675478076 | 2024-11-27 10:44:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b0c8dc5a768243bd81986b9f01c5274c | - | - | - | - | 70000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675488073 | 2024-11-27 10:44:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 159997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1732675498077 | 2024-11-27 10:44:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b91f432de4664a50ad59f95ad300fa15 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1732675500028 | 2024-11-27 10:45:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | 9060825ff5594422bc9ee925e4c85976 | - | - | - | - | 1 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-11-27 10:41:56,712 to 2024-11-27 10:45:00,016
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 170 | 1732675505291 | 2024-11-27 10:45:05 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | StandardWrapper | initServlet | 0902819cf4f9443bbec05ee469d9ca05 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 170 | 1732675505292 | 2024-11-27 10:45:05 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | AuthenticatorBase | invoke | 0902819cf4f9443bbec05ee469d9ca05 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 170 | 1732675505342 | 2024-11-27 10:45:05 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | AuthenticatorBase | invoke | 0902819cf4f9443bbec05ee469d9ca05 | - | - | - | - | 51 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 50 ms

info | 170 | 1732675506389 | 2024-11-27 10:45:06 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | manageChatRoom | roomChatData | 14c627603fbb4dec8866713703e1fa64 | - | - | - | - | 1035 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.s.ChatRoomService 查询参数chatRoomSearch:{"startTime":"2024-11-19","endTime":"2024-11-25","orgId":null,"teamId":null,"export":null,"status":null,"chatUuid":null,"liveNo":null,"nickName":null,"total":null,"type":null,"download":null}

info | 170 | 1732675506392 | 2024-11-27 10:45:06 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | manageChatRoom | roomChatData | 14c627603fbb4dec8866713703e1fa64 | - | - | - | - | 1035 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-19,结束时间：2024-11-25,团队集合:null,hostUuids:[],chatStatus:null

info | 166 | 1732675508075 | 2024-11-27 10:45:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b91f432de4664a50ad59f95ad300fa15 | - | - | - | - | 9998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 170 | 1732675509228 | 2024-11-27 10:45:09 | v2/manageChatRoom/roomChatData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | ************** | - | 2 | manageChatRoom | roomChatData | 14c627603fbb4dec8866713703e1fa64 | - | - | - | - | 3871 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-19,结束时间：2024-11-25,团队集合:null,hostUuids:[],chatStatus:null

info | 232 | 1732675517647 | 2024-11-27 10:45:17 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ChatRoomService | refreshChatCache | 9fbb6f3edc01478e8455fe8611d4cad1 | - | - | - | - | 189426 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 166 | 1732675518075 | 2024-11-27 10:45:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b91f432de4664a50ad59f95ad300fa15 | - | - | - | - | 19997 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1732675528076 | 2024-11-27 10:45:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b91f432de4664a50ad59f95ad300fa15 | - | - | - | - | 30000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1732675538075 | 2024-11-27 10:45:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b91f432de4664a50ad59f95ad300fa15 | - | - | - | - | 39997 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1732675552611 | 2024-11-27 10:45:52 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b91f432de4664a50ad59f95ad300fa15 | - | - | - | - | 54536 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675568468 | 2024-11-27 10:46:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 240407 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675568490 | 2024-11-27 10:46:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 240413 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1732675578078 | 2024-11-27 10:46:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b91f432de4664a50ad59f95ad300fa15 | - | - | - | - | 80001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675588078 | 2024-11-27 10:46:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 260001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675598078 | 2024-11-27 10:46:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 270002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675608073 | 2024-11-27 10:46:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 279997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675618074 | 2024-11-27 10:46:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 289998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675628075 | 2024-11-27 10:47:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 299999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1732675638078 | 2024-11-27 10:47:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 2fad137b84a44777b608844c51966c21 | - | - | - | - | 310003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1732675649204 | 2024-11-27 10:47:29 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b91f432de4664a50ad59f95ad300fa15 | - | - | - | - | 151131 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1732675658076 | 2024-11-27 10:47:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | b0c8dc5a768243bd81986b9f01c5274c | - | - | - | - | 250000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1732675658689 | 2024-11-27 10:47:38 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 85feb7423c924218bcda44365adec349 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1732675658738 | 2024-11-27 10:47:38 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 85feb7423c924218bcda44365adec349 | - | - | - | - | 49 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1732675658740 | 2024-11-27 10:47:38 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | 85feb7423c924218bcda44365adec349 | - | - | - | - | 51 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1732675658750 | 2024-11-27 10:47:38 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | 85feb7423c924218bcda44365adec349 | - | - | - | - | 62 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 15 | 1732675665058 | 2024-11-27 10:47:45 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | dce357c0729b47429822b3b25183c19c | - | - | - | - | 9 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1732675665072 | 2024-11-27 10:47:45 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 9 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 95492 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1732675665091 | 2024-11-27 10:47:45 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 14 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1732675665829 | 2024-11-27 10:47:45 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 753 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1732675665860 | 2024-11-27 10:47:45 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 784 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1732675665922 | 2024-11-27 10:47:45 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 845 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1732675665953 | 2024-11-27 10:47:45 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 876 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1732675665979 | 2024-11-27 10:47:45 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 903 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1732675666590 | 2024-11-27 10:47:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 1513 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1732675666690 | 2024-11-27 10:47:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 1613 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1732675666692 | 2024-11-27 10:47:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 1615 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1732675666693 | 2024-11-27 10:47:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 1616 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1732675667333 | 2024-11-27 10:47:47 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 2257 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1732675669495 | 2024-11-27 10:47:49 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 4418 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732675669496 | 2024-11-27 10:47:49 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 4419 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1732675669536 | 2024-11-27 10:47:49 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 4459 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 35 ms. Found 0 JPA repository interfaces.

info | 1 | 1732675669543 | 2024-11-27 10:47:49 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 4466 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732675669544 | 2024-11-27 10:47:49 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 4467 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1732675669569 | 2024-11-27 10:47:49 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 4492 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1732675670275 | 2024-11-27 10:47:50 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 5202 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$eaf37bd3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675670295 | 2024-11-27 10:47:50 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 5218 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$69f9c14b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675670357 | 2024-11-27 10:47:50 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 5280 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$e862cc14] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675670363 | 2024-11-27 10:47:50 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 5286 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675670417 | 2024-11-27 10:47:50 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 5340 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675670421 | 2024-11-27 10:47:50 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 5344 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732675671383 | 2024-11-27 10:47:51 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 6306 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1732675671395 | 2024-11-27 10:47:51 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 6318 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1732675671395 | 2024-11-27 10:47:51 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 6318 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1732675671498 | 2024-11-27 10:47:51 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 6421 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1732675682584 | 2024-11-27 10:48:02 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 17507 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1732675682651 | 2024-11-27 10:48:02 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 17575 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1732675682716 | 2024-11-27 10:48:02 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 17640 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1732675682875 | 2024-11-27 10:48:02 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 17798 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1732675682970 | 2024-11-27 10:48:02 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 17893 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1732675683135 | 2024-11-27 10:48:03 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 18058 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1732675683149 | 2024-11-27 10:48:03 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 18072 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1732675688432 | 2024-11-27 10:48:08 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 23356 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1732675688758 | 2024-11-27 10:48:08 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 23681 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1732675688781 | 2024-11-27 10:48:08 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 23704 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1732675688948 | 2024-11-27 10:48:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | df4a85ff4be449b191fcb518b3f9a05c | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1732675688973 | 2024-11-27 10:48:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | df4a85ff4be449b191fcb518b3f9a05c | - | - | - | - | 25 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1732675688983 | 2024-11-27 10:48:08 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | df4a85ff4be449b191fcb518b3f9a05c | - | - | - | - | 36 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1732675689229 | 2024-11-27 10:48:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | df4a85ff4be449b191fcb518b3f9a05c | - | - | - | - | 281 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 244 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1732675689237 | 2024-11-27 10:48:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | df4a85ff4be449b191fcb518b3f9a05c | - | - | - | - | 290 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1732675689246 | 2024-11-27 10:48:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | df4a85ff4be449b191fcb518b3f9a05c | - | - | - | - | 298 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1732675689255 | 2024-11-27 10:48:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | df4a85ff4be449b191fcb518b3f9a05c | - | - | - | - | 307 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1732675689358 | 2024-11-27 10:48:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | df4a85ff4be449b191fcb518b3f9a05c | - | - | - | - | 410 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 101 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1732675692764 | 2024-11-27 10:48:12 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 27688 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1732675693737 | 2024-11-27 10:48:13 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 28661 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@45547014 with [org.springframework.security.web.session.DisableEncodeUrlFilter@481c80d9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3f72b182, org.springframework.security.web.context.SecurityContextPersistenceFilter@1460458c, org.springframework.security.web.header.HeaderWriterFilter@57f57af5, org.springframework.security.web.authentication.logout.LogoutFilter@42627e6b, org.springframework.web.filter.CorsFilter@70ce4b5d, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@2e9141d2, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@6dcb0640, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@492f3239, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5f314302, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@b790f3d, org.springframework.security.web.session.SessionManagementFilter@6fa9cf83, org.springframework.security.web.access.ExceptionTranslationFilter@2df593ac, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4056f9e9]

info | 1 | 1732675693758 | 2024-11-27 10:48:13 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 28682 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1732675693866 | 2024-11-27 10:48:13 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 28789 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1732675693869 | 2024-11-27 10:48:13 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 28792 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1732675693870 | 2024-11-27 10:48:13 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 28793 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1732675693873 | 2024-11-27 10:48:13 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 28796 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1732675693876 | 2024-11-27 10:48:13 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 28799 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1732675693877 | 2024-11-27 10:48:13 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 28800 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1732675693877 | 2024-11-27 10:48:13 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 28800 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1732675694094 | 2024-11-27 10:48:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29017 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1732675694094 | 2024-11-27 10:48:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29017 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1732675694094 | 2024-11-27 10:48:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29017 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1732675694094 | 2024-11-27 10:48:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29017 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1732675694094 | 2024-11-27 10:48:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29018 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1732675694096 | 2024-11-27 10:48:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29019 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1732675694216 | 2024-11-27 10:48:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29139 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1732675694217 | 2024-11-27 10:48:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29140 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1732675694224 | 2024-11-27 10:48:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29147 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@273cca1e, tech.powerjob.worker.actors.ProcessorTrackerActor@4cb61589, tech.powerjob.worker.actors.WorkerActor@7db05830])

info | 1 | 1732675694250 | 2024-11-27 10:48:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29174 | 0 | - | - | - | - | main o.r.Reflections Reflections took 19 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1732675694255 | 2024-11-27 10:48:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29178 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1732675694256 | 2024-11-27 10:48:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29179 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@5b863697

info | 1 | 1732675694256 | 2024-11-27 10:48:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29179 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1732675694256 | 2024-11-27 10:48:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29179 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1732675694259 | 2024-11-27 10:48:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 29182 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 127 | 1732675694951 | 2024-11-27 10:48:14 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1732675695471 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30394 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1732675695472 | 2024-11-27 10:48:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30395 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1732675695472 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30395 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1732675695472 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30395 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1732675695473 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30396 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1732675695475 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30398 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1732675695477 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30400 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1732675695477 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30400 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1732675695477 | 2024-11-27 10:48:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30401 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.221 s

info | 1 | 1732675695574 | 2024-11-27 10:48:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30497 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1732675695578 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30502 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1732675695579 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30502 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1732675695582 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30505 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1732675695770 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30694 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1732675695771 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30694 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/2a9c659889fd45489aa8803fe888d420/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1732675695778 | 2024-11-27 10:48:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30701 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/2a9c659889fd45489aa8803fe888d420/] on JVM exit successfully

info | 1 | 1732675695793 | 2024-11-27 10:48:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30717 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1732675695794 | 2024-11-27 10:48:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30717 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.928 s, congratulations!

info | 164 | 1732675695798 | 2024-11-27 10:48:15 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 164 | 1732675695798 | 2024-11-27 10:48:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1732675695847 | 2024-11-27 10:48:15 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | TomcatWebServer | start | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30770 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1732675695875 | 2024-11-27 10:48:15 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30798 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1732675695893 | 2024-11-27 10:48:15 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30816 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1732675695893 | 2024-11-27 10:48:15 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30816 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1732675695932 | 2024-11-27 10:48:15 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | Application | main | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30855 | 0 | - | - | - | - | main c.t.g.Application Started Application in 31.413 seconds (JVM running for 32.343)

info | 1 | 1732675695963 | 2024-11-27 10:48:15 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30886 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1732675695963 | 2024-11-27 10:48:15 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 8481aff9de454b4a8bca1a972a86c037 | - | - | - | - | 30886 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 234 | 1732675695969 | 2024-11-27 10:48:15 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 9228a83dd723459bb0fa73fa775eea73 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 171 | 1732675697426 | 2024-11-27 10:48:17 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | StandardWrapper | initServlet | f40f433dfce148e18205535c3274a588 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 171 | 1732675697428 | 2024-11-27 10:48:17 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | AuthenticatorBase | invoke | f40f433dfce148e18205535c3274a588 | - | - | - | - | 2 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 171 | 1732675697439 | 2024-11-27 10:48:17 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | AuthenticatorBase | invoke | f40f433dfce148e18205535c3274a588 | - | - | - | - | 14 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 10 ms

info | 164 | 1732675705796 | 2024-11-27 10:48:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 9999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732675715799 | 2024-11-27 10:48:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 20001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732675725799 | 2024-11-27 10:48:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732675735800 | 2024-11-27 10:48:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 10002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732675745800 | 2024-11-27 10:49:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 20003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732675755798 | 2024-11-27 10:49:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 30000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732675765799 | 2024-11-27 10:49:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 40001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732675775799 | 2024-11-27 10:49:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 50001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732675785799 | 2024-11-27 10:49:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 60001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732675795800 | 2024-11-27 10:49:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 70003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732675805819 | 2024-11-27 10:50:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 80021 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732675815800 | 2024-11-27 10:50:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 90002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732675825798 | 2024-11-27 10:50:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732675835795 | 2024-11-27 10:50:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732675845800 | 2024-11-27 10:50:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 20002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732675855798 | 2024-11-27 10:50:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 160001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1732675855938 | 2024-11-27 10:50:55 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ChatRoomService | refreshChatCache | 9228a83dd723459bb0fa73fa775eea73 | - | - | - | - | 159971 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 164 | 1732675865799 | 2024-11-27 10:51:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 170001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732675875799 | 2024-11-27 10:51:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 180002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732675885797 | 2024-11-27 10:51:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 190000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732675895799 | 2024-11-27 10:51:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 200001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732675905800 | 2024-11-27 10:51:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 210003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732675915798 | 2024-11-27 10:51:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 220001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732675925798 | 2024-11-27 10:52:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 230001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732675935796 | 2024-11-27 10:52:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 239999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732675945795 | 2024-11-27 10:52:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 119998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732675955798 | 2024-11-27 10:52:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 130002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732675965799 | 2024-11-27 10:52:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 140002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732675975798 | 2024-11-27 10:52:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 150000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732675985798 | 2024-11-27 10:53:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 160000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732675995795 | 2024-11-27 10:53:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 169997 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732676005794 | 2024-11-27 10:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 309996 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732676015795 | 2024-11-27 10:53:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 319998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1732676025796 | 2024-11-27 10:53:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 59bea79bcfc24c5492403b87555a7925 | - | - | - | - | 330001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676035796 | 2024-11-27 10:53:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 210000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676045794 | 2024-11-27 10:54:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 219997 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676055800 | 2024-11-27 10:54:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 230004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676065798 | 2024-11-27 10:54:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 240001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676075798 | 2024-11-27 10:54:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 250001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676085794 | 2024-11-27 10:54:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 259996 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676095795 | 2024-11-27 10:54:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 269999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676105797 | 2024-11-27 10:55:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 280000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676115799 | 2024-11-27 10:55:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 290003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676125802 | 2024-11-27 10:55:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 300005 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676135821 | 2024-11-27 10:55:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 310024 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676145830 | 2024-11-27 10:55:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 420033 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676155839 | 2024-11-27 10:55:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 330041 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676165843 | 2024-11-27 10:56:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 340049 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676175844 | 2024-11-27 10:56:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 350047 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676185842 | 2024-11-27 10:56:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 360045 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676195845 | 2024-11-27 10:56:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 370048 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676205843 | 2024-11-27 10:56:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 380046 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676215843 | 2024-11-27 10:56:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 390047 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676225845 | 2024-11-27 10:57:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 400047 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676235843 | 2024-11-27 10:57:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 410046 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676245844 | 2024-11-27 10:57:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 420047 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676255841 | 2024-11-27 10:57:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 430044 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676265847 | 2024-11-27 10:57:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 540049 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676275843 | 2024-11-27 10:57:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 450046 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676285841 | 2024-11-27 10:58:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 460044 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676295842 | 2024-11-27 10:58:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 470045 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676305841 | 2024-11-27 10:58:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 480043 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676315843 | 2024-11-27 10:58:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 490046 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676325844 | 2024-11-27 10:58:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 600046 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676335841 | 2024-11-27 10:58:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 610043 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676345844 | 2024-11-27 10:59:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 520047 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676355843 | 2024-11-27 10:59:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 630045 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676365847 | 2024-11-27 10:59:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 640049 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676375843 | 2024-11-27 10:59:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 650044 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676385844 | 2024-11-27 10:59:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 660045 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676395845 | 2024-11-27 10:59:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 670048 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1732676400075 | 2024-11-27 11:00:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | 89dddb6ea96f498d935bc6ce525b4e70 | - | - | - | - | 1 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-11-27 10:48:08,757 to 2024-11-27 11:00:00,058
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 169 | 1732676405848 | 2024-11-27 11:00:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 680051 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676415845 | 2024-11-27 11:00:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 690047 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676425847 | 2024-11-27 11:00:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 700049 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676435845 | 2024-11-27 11:00:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 610048 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676445843 | 2024-11-27 11:00:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.77 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 620045 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676455847 | 2024-11-27 11:00:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 730048 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676465847 | 2024-11-27 11:01:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 740050 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676475846 | 2024-11-27 11:01:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 750047 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676485848 | 2024-11-27 11:01:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.79 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 660052 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676495843 | 2024-11-27 11:01:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.81 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 670045 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1732676505843 | 2024-11-27 11:01:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.83 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 666e1a3c579545b08b8c4491cd88c22d | - | - | - | - | 680047 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676515848 | 2024-11-27 11:01:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 790049 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676525847 | 2024-11-27 11:02:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 800049 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1732676535847 | 2024-11-27 11:02:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | ************** | - | 2 | ThreadPoolExecutor | runWorker | ef144fff1eab4d9aa2fa6a294ac3464c | - | - | - | - | 810050 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 15 | 1732676546900 | 2024-11-27 11:02:26 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 4b4683085adb4f8786334960b1d38b67 | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1732676546905 | 2024-11-27 11:02:26 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 96078 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1732676546919 | 2024-11-27 11:02:26 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1732676547558 | 2024-11-27 11:02:27 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 644 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1732676547564 | 2024-11-27 11:02:27 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 650 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1732676547569 | 2024-11-27 11:02:27 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 656 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1732676547576 | 2024-11-27 11:02:27 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 662 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1732676547586 | 2024-11-27 11:02:27 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 672 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1732676547743 | 2024-11-27 11:02:27 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 829 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1732676547797 | 2024-11-27 11:02:27 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 883 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1732676547799 | 2024-11-27 11:02:27 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 885 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1732676547799 | 2024-11-27 11:02:27 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 885 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1732676548110 | 2024-11-27 11:02:28 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 1196 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1732676550196 | 2024-11-27 11:02:30 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 3283 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732676550199 | 2024-11-27 11:02:30 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 3286 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1732676550259 | 2024-11-27 11:02:30 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 3345 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 36 ms. Found 0 JPA repository interfaces.

info | 1 | 1732676550270 | 2024-11-27 11:02:30 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 3356 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1732676550271 | 2024-11-27 11:02:30 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 3357 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1732676550297 | 2024-11-27 11:02:30 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 3383 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.

info | 1 | 1732676550993 | 2024-11-27 11:02:30 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 4080 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$ff0f177a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732676551011 | 2024-11-27 11:02:31 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 4097 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7e155cf2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732676551083 | 2024-11-27 11:02:31 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 4169 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$fc7e67bb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732676551088 | 2024-11-27 11:02:31 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 4174 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732676551146 | 2024-11-27 11:02:31 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 4233 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732676551149 | 2024-11-27 11:02:31 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 4236 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1732676551648 | 2024-11-27 11:02:31 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 4734 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1732676551659 | 2024-11-27 11:02:31 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 4745 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1732676551659 | 2024-11-27 11:02:31 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 4745 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1732676551742 | 2024-11-27 11:02:31 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 4828 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1732676558719 | 2024-11-27 11:02:38 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 11805 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1732676558794 | 2024-11-27 11:02:38 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 11880 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1732676558852 | 2024-11-27 11:02:38 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 11938 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1732676558970 | 2024-11-27 11:02:38 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 12056 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1732676559048 | 2024-11-27 11:02:39 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 12134 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1732676559200 | 2024-11-27 11:02:39 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 12286 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1732676559211 | 2024-11-27 11:02:39 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 12298 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1732676563832 | 2024-11-27 11:02:43 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 16918 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1732676564087 | 2024-11-27 11:02:44 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 17173 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1732676564108 | 2024-11-27 11:02:44 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 17195 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1732676564255 | 2024-11-27 11:02:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2746d591ecb848cdb5993cbc0076a78d | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1732676564281 | 2024-11-27 11:02:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2746d591ecb848cdb5993cbc0076a78d | - | - | - | - | 27 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1732676564292 | 2024-11-27 11:02:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2746d591ecb848cdb5993cbc0076a78d | - | - | - | - | 37 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1732676564479 | 2024-11-27 11:02:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2746d591ecb848cdb5993cbc0076a78d | - | - | - | - | 225 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 185 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1732676564485 | 2024-11-27 11:02:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2746d591ecb848cdb5993cbc0076a78d | - | - | - | - | 230 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1732676564492 | 2024-11-27 11:02:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2746d591ecb848cdb5993cbc0076a78d | - | - | - | - | 238 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1732676564524 | 2024-11-27 11:02:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2746d591ecb848cdb5993cbc0076a78d | - | - | - | - | 269 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1732676564627 | 2024-11-27 11:02:44 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2746d591ecb848cdb5993cbc0076a78d | - | - | - | - | 372 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 97 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1732676568221 | 2024-11-27 11:02:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 21308 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1732676568930 | 2024-11-27 11:02:48 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22016 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@244037d3 with [org.springframework.security.web.session.DisableEncodeUrlFilter@79f9d0bc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@716516ce, org.springframework.security.web.context.SecurityContextPersistenceFilter@2c8ee744, org.springframework.security.web.header.HeaderWriterFilter@6dcb0640, org.springframework.security.web.authentication.logout.LogoutFilter@50f53c99, org.springframework.web.filter.CorsFilter@41758fc9, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@1c93530b, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@301576db, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5e6bfbb7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@209e457f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@154e31a5, org.springframework.security.web.session.SessionManagementFilter@49dba891, org.springframework.security.web.access.ExceptionTranslationFilter@481c80d9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6afc4d00]

info | 1 | 1732676568944 | 2024-11-27 11:02:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22031 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1732676569019 | 2024-11-27 11:02:49 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22105 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1732676569021 | 2024-11-27 11:02:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22107 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1732676569022 | 2024-11-27 11:02:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22108 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1732676569025 | 2024-11-27 11:02:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22111 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1732676569027 | 2024-11-27 11:02:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22114 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1732676569028 | 2024-11-27 11:02:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22114 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1732676569028 | 2024-11-27 11:02:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22114 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1732676569360 | 2024-11-27 11:02:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22447 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1732676569361 | 2024-11-27 11:02:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22447 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1732676569361 | 2024-11-27 11:02:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22447 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1732676569361 | 2024-11-27 11:02:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22447 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1732676569361 | 2024-11-27 11:02:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22447 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1732676569362 | 2024-11-27 11:02:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22448 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1732676569483 | 2024-11-27 11:02:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22569 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1732676569484 | 2024-11-27 11:02:49 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22570 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1732676569494 | 2024-11-27 11:02:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22580 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@107a0444, tech.powerjob.worker.actors.ProcessorTrackerActor@5df073f1, tech.powerjob.worker.actors.WorkerActor@10b6fbab])

info | 1 | 1732676569526 | 2024-11-27 11:02:49 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22612 | 0 | - | - | - | - | main o.r.Reflections Reflections took 23 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1732676569532 | 2024-11-27 11:02:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22618 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1732676569533 | 2024-11-27 11:02:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22619 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@64e99652

info | 1 | 1732676569534 | 2024-11-27 11:02:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22620 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@600fbcf7

info | 1 | 1732676569534 | 2024-11-27 11:02:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22620 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1732676569534 | 2024-11-27 11:02:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22620 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1732676569537 | 2024-11-27 11:02:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 22623 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 127 | 1732676570057 | 2024-11-27 11:02:50 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1732676570409 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23496 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1732676570410 | 2024-11-27 11:02:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23496 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1732676570411 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23497 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1732676570411 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23497 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1732676570411 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23497 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732676570411 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23497 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1732676570411 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23497 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732676570411 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23497 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732676570412 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23498 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1732676570412 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23498 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1732676570412 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23498 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1732676570412 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23498 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1732676570412 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23498 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1732676570412 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23498 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1732676570412 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23498 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1732676570416 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23502 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1732676570418 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23504 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1732676570437 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23523 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1732676570437 | 2024-11-27 11:02:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23523 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 902.9 ms

info | 1 | 1732676570541 | 2024-11-27 11:02:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23628 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1732676570547 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23633 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1732676570547 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23633 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1732676570551 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23637 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1732676570748 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23834 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1732676570748 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23834 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/046c2ffda88b4fdba7e3044da9573c69/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1732676570755 | 2024-11-27 11:02:50 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23841 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/046c2ffda88b4fdba7e3044da9573c69/] on JVM exit successfully

info | 1 | 1732676570770 | 2024-11-27 11:02:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23856 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1732676570771 | 2024-11-27 11:02:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23857 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.751 s, congratulations!

info | 154 | 1732676570776 | 2024-11-27 11:02:50 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 6aef23768491454da088cf6fe0581d6c | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 154 | 1732676570776 | 2024-11-27 11:02:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 6aef23768491454da088cf6fe0581d6c | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1732676570816 | 2024-11-27 11:02:50 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23902 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1732676570837 | 2024-11-27 11:02:50 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23923 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1732676570850 | 2024-11-27 11:02:50 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23936 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1732676570850 | 2024-11-27 11:02:50 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23936 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1732676570882 | 2024-11-27 11:02:50 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23968 | 0 | - | - | - | - | main c.t.g.Application Started Application in 24.508 seconds (JVM running for 25.169)

info | 1 | 1732676570900 | 2024-11-27 11:02:50 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23986 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1732676570900 | 2024-11-27 11:02:50 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 1620e7637862412995ae51cfe4a6a65b | - | - | - | - | 23986 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 224 | 1732676570905 | 2024-11-27 11:02:50 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 43e9e5c83b80464fa8223ed4d767d8ef | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 28 | 1732676575702 | 2024-11-27 11:02:55 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 0403064f63be4eb99cde019b9e710a0d | - | - | - | - | 1 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1732676575755 | 2024-11-27 11:02:55 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 0403064f63be4eb99cde019b9e710a0d | - | - | - | - | 53 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1732676575766 | 2024-11-27 11:02:55 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | 0403064f63be4eb99cde019b9e710a0d | - | - | - | - | 64 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

