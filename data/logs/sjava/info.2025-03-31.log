info | 1 | 1743415638971 | 2025-03-31 18:07:18 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 34434 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1743415638960 | 2025-03-31 18:07:18 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | d18d8ab9c5264e06b3be5e1adc80691d | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1743415638982 | 2025-03-31 18:07:18 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1743415639501 | 2025-03-31 18:07:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 525 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1743415639503 | 2025-03-31 18:07:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 527 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1743415639505 | 2025-03-31 18:07:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 529 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1743415639507 | 2025-03-31 18:07:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 531 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1743415639510 | 2025-03-31 18:07:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 534 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1743415639548 | 2025-03-31 18:07:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 572 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1743415639581 | 2025-03-31 18:07:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 605 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1743415639583 | 2025-03-31 18:07:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 607 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1743415639583 | 2025-03-31 18:07:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 607 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1743415639637 | 2025-03-31 18:07:19 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 661 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1743415641748 | 2025-03-31 18:07:21 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 2772 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1743415641749 | 2025-03-31 18:07:21 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 2773 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1743415641788 | 2025-03-31 18:07:21 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 2812 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.

info | 1 | 1743415641797 | 2025-03-31 18:07:21 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 2821 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1743415641798 | 2025-03-31 18:07:21 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 2822 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1743415641823 | 2025-03-31 18:07:21 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 2847 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1743415642715 | 2025-03-31 18:07:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 3743 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$189051fd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1743415642739 | 2025-03-31 18:07:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 3763 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$97969775] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1743415642814 | 2025-03-31 18:07:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 3838 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$15ffa23e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1743415642819 | 2025-03-31 18:07:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 3843 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1743415642890 | 2025-03-31 18:07:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 3915 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1743415642898 | 2025-03-31 18:07:22 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 3922 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1743415643494 | 2025-03-31 18:07:23 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 4518 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1743415643506 | 2025-03-31 18:07:23 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 4530 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1743415643506 | 2025-03-31 18:07:23 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 4530 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1743415643605 | 2025-03-31 18:07:23 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 4629 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1743415659286 | 2025-03-31 18:07:39 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 20311 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1743415659387 | 2025-03-31 18:07:39 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 20412 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1743415659438 | 2025-03-31 18:07:39 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 20463 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1743415659581 | 2025-03-31 18:07:39 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 20606 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1743415659728 | 2025-03-31 18:07:39 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 20752 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1743415659959 | 2025-03-31 18:07:39 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 20984 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1743415659972 | 2025-03-31 18:07:39 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9c67f5861d0f4eb2bf4c5b76b25d4287 | - | - | - | - | 20996 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

