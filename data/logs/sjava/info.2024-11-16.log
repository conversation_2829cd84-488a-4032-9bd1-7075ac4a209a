info | 15 | 1731719081207 | 2024-11-16 09:04:41 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************* | - | 2 | Thread | run | 4ca0fe2b2776460ba4a995018c6e6dac | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731719081215 | 2024-11-16 09:04:41 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************* | - | 2 | Application | main | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 26406 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1731719081224 | 2024-11-16 09:04:41 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************* | - | 2 | Application | main | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731719081798 | 2024-11-16 09:04:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************* | - | 2 | EtcdApplicationListener | onApplicationEvent | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 578 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731719081808 | 2024-11-16 09:04:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************* | - | 2 | EtcdApplicationListener | onApplicationEvent | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 588 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731719081863 | 2024-11-16 09:04:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************* | - | 2 | EtcdApplicationListener | onApplicationEvent | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 643 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731719081974 | 2024-11-16 09:04:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************* | - | 2 | EtcdApplicationListener | onApplicationEvent | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 754 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731719082029 | 2024-11-16 09:04:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************* | - | 2 | EtcdApplicationListener | onApplicationEvent | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 809 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731719083637 | 2024-11-16 09:04:43 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************* | - | 2 | EtcdApplicationListener | onApplicationEvent | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 2418 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731719083814 | 2024-11-16 09:04:43 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************* | - | 2 | EtcdApplicationListener | onApplicationEvent | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 2595 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731719083818 | 2024-11-16 09:04:43 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************* | - | 2 | EtcdApplicationListener | onApplicationEvent | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 2598 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731719083818 | 2024-11-16 09:04:43 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************* | - | 2 | EtcdApplicationListener | onApplicationEvent | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 2598 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731719086192 | 2024-11-16 09:04:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************* | - | 2 | EtcdApplicationListener | onApplicationEvent | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 4973 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731719088138 | 2024-11-16 09:04:48 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************* | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 6918 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731719088139 | 2024-11-16 09:04:48 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************* | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 6919 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731719088178 | 2024-11-16 09:04:48 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************* | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 6958 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 33 ms. Found 0 JPA repository interfaces.

info | 1 | 1731719088187 | 2024-11-16 09:04:48 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************* | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 6968 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731719088188 | 2024-11-16 09:04:48 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************* | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 6968 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731719088214 | 2024-11-16 09:04:48 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************* | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 6994 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1731719089024 | 2024-11-16 09:04:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************* | - | 2 | AbstractBeanFactory | doGetBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 7808 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$62bd2331] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731719089046 | 2024-11-16 09:04:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************* | - | 2 | AbstractBeanFactory | doGetBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 7826 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e1c368a9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731719089111 | 2024-11-16 09:04:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************* | - | 2 | AbstractBeanFactory | doGetBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 7892 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$602c7372] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731719089116 | 2024-11-16 09:04:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************* | - | 2 | AbstractBeanFactory | doGetBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 7897 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731719089181 | 2024-11-16 09:04:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************* | - | 2 | AbstractBeanFactory | doGetBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 7961 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731719089185 | 2024-11-16 09:04:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************* | - | 2 | AbstractBeanFactory | doGetBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 7965 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731719089767 | 2024-11-16 09:04:49 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************* | - | 2 | LifecycleBase | init | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 8547 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731719089782 | 2024-11-16 09:04:49 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************* | - | 2 | TomcatWebServer | initialize | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 8562 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731719089782 | 2024-11-16 09:04:49 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************* | - | 2 | LifecycleBase | start | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 8562 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731719089860 | 2024-11-16 09:04:49 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************* | - | 2 | StandardContext | startInternal | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 8640 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731719103731 | 2024-11-16 09:05:03 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************* | - | 2 | DataSourceUtils | getConnection | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 22511 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731719103811 | 2024-11-16 09:05:03 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************* | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 22592 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731719103861 | 2024-11-16 09:05:03 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************* | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 22641 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731719103973 | 2024-11-16 09:05:03 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************* | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 22753 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731719104061 | 2024-11-16 09:05:04 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************* | - | 2 | DialectFactoryImpl | determineDialect | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 22841 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731719104180 | 2024-11-16 09:05:04 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************* | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 22960 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731719104188 | 2024-11-16 09:05:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************* | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 22968 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731719109073 | 2024-11-16 09:05:09 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************* | - | 2 | NativeMethodAccessorImpl | invoke | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 27853 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731719109461 | 2024-11-16 09:05:09 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************* | - | 2 | AbstractLifecycle | init | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 28241 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731719109483 | 2024-11-16 09:05:09 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************* | - | 2 | SentinelNacosSourceImport | nacosConfig | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 28264 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 1 | 1731719109649 | 2024-11-16 09:05:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.519 | ************* | - | 2 | RpcClientFactory | lambda$createClient$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 28429 | 0 | - | - | - | - | main o.r.Reflections Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 

info | 1 | 1731719109676 | 2024-11-16 09:05:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.521 | ************* | - | 2 | RpcClientFactory | lambda$createClient$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 28456 | 0 | - | - | - | - | main o.r.Reflections Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 

info | 1 | 1731719109691 | 2024-11-16 09:05:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.523 | ************* | - | 2 | RpcClientFactory | lambda$createClient$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 28471 | 0 | - | - | - | - | main o.r.Reflections Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 

info | 1 | 1731719109815 | 2024-11-16 09:05:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.525 | ************* | - | 2 | RpcClientFactory | lambda$createClient$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 28596 | 0 | - | - | - | - | main o.r.Reflections Reflections took 120 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731719109822 | 2024-11-16 09:05:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************* | - | 2 | RpcClientFactory | lambda$createClient$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 28602 | 0 | - | - | - | - | main o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 1 | 1731719109829 | 2024-11-16 09:05:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************* | - | 2 | RpcClientFactory | lambda$createClient$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 28609 | 0 | - | - | - | - | main o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 1 | 1731719109837 | 2024-11-16 09:05:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************* | - | 2 | RpcClientFactory | lambda$createClient$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 28618 | 0 | - | - | - | - | main o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 1 | 1731719109941 | 2024-11-16 09:05:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.533 | ************* | - | 2 | RpcClientFactory | lambda$createClient$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 28721 | 0 | - | - | - | - | main o.r.Reflections Reflections took 100 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731719112976 | 2024-11-16 09:05:12 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.535 | ************* | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 31757 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731719114183 | 2024-11-16 09:05:14 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.537 | ************* | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 32963 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@262bcb52 with [org.springframework.security.web.session.DisableEncodeUrlFilter@65ff6c2e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2db89dfe, org.springframework.security.web.context.SecurityContextPersistenceFilter@5ba41f44, org.springframework.security.web.header.HeaderWriterFilter@26e40792, org.springframework.security.web.authentication.logout.LogoutFilter@6e7867a3, org.springframework.web.filter.CorsFilter@511f3350, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@17993651, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@626d18e5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@50585d80, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@318da38, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1ae42ea1, org.springframework.security.web.session.SessionManagementFilter@cbf7f90, org.springframework.security.web.access.ExceptionTranslationFilter@5c8d2380, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@10db1a9d]

info | 1 | 1731719114203 | 2024-11-16 09:05:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.539 | ************* | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 32983 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731719114290 | 2024-11-16 09:05:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.541 | ************* | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 33071 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731719114292 | 2024-11-16 09:05:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.543 | ************* | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 33072 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731719114293 | 2024-11-16 09:05:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.545 | ************* | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 33073 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731719114296 | 2024-11-16 09:05:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | ************* | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 33076 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731719114299 | 2024-11-16 09:05:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************* | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 33079 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731719114299 | 2024-11-16 09:05:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.551 | ************* | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 33080 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731719114300 | 2024-11-16 09:05:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.553 | ************* | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 33080 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1731719115794 | 2024-11-16 09:05:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | ************* | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34574 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1731719115794 | 2024-11-16 09:05:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | ************* | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34574 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1731719115794 | 2024-11-16 09:05:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | ************* | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34574 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731719115794 | 2024-11-16 09:05:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.561 | ************* | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34575 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731719115795 | 2024-11-16 09:05:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.563 | ************* | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34575 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731719115795 | 2024-11-16 09:05:15 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.565 | ************* | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34575 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:tap0 (tap0)

info | 1 | 1731719115906 | 2024-11-16 09:05:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.567 | ************* | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34686 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731719116040 | 2024-11-16 09:05:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34820 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731719116041 | 2024-11-16 09:05:16 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.571 | ************* | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34821 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: *************, localBindPort: 27777; externalIp: *************, externalPort: 27777

info | 1 | 1731719116054 | 2024-11-16 09:05:16 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************* | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34834 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=*************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@6da3766e, tech.powerjob.worker.actors.ProcessorTrackerActor@54242d9f, tech.powerjob.worker.actors.WorkerActor@72ccfe4f])

info | 1 | 1731719116092 | 2024-11-16 09:05:16 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.575 | ************* | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34872 | 0 | - | - | - | - | main o.r.Reflections Reflections took 26 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731719116099 | 2024-11-16 09:05:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.577 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34880 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1731719116101 | 2024-11-16 09:05:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.579 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34881 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@641792f0

info | 1 | 1731719116101 | 2024-11-16 09:05:16 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.581 | ************* | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34881 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731719116101 | 2024-11-16 09:05:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.583 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34881 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: *************:27777

info | 1 | 1731719116104 | 2024-11-16 09:05:16 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.585 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 34884 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 125 | 1731719116602 | 2024-11-16 09:05:16 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************* | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731719117034 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.587 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35815 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731719117036 | 2024-11-16 09:05:17 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.589 | ************* | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35816 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731719117036 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35816 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731719117036 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35816 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731719117036 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35816 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731719117036 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35816 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731719117036 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35816 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731719117036 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35816 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731719117036 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.603 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35816 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731719117037 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.605 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35817 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731719117037 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.607 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35817 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731719117037 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.609 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35817 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731719117037 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.611 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35817 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731719117037 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.613 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35817 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731719117037 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.615 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35817 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731719117038 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35819 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731719117041 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35821 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731719117041 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35821 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731719117041 | 2024-11-16 09:05:17 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.623 | ************* | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35822 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 940.3 ms

info | 1 | 1731719117149 | 2024-11-16 09:05:17 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************* | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35929 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731719117155 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.627 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35935 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731719117156 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.629 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35936 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731719117160 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.631 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 35940 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731719117377 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.633 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36157 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731719117377 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.635 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36157 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/7c39b5966dbe453fb4eb171467c7bf35/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731719117383 | 2024-11-16 09:05:17 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.637 | ************* | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36163 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/7c39b5966dbe453fb4eb171467c7bf35/] on JVM exit successfully

info | 1 | 1731719117397 | 2024-11-16 09:05:17 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.639 | ************* | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36178 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731719117398 | 2024-11-16 09:05:17 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.641 | ************* | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36178 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 3.108 s, congratulations!

info | 161 | 1731719117403 | 2024-11-16 09:05:17 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************* | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 161 | 1731719117404 | 2024-11-16 09:05:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731719117452 | 2024-11-16 09:05:17 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.643 | ************* | - | 2 | TomcatWebServer | start | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36232 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731719117540 | 2024-11-16 09:05:17 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.645 | ************* | - | 2 | AbstractApplicationContext | finishRefresh | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36320 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731719117553 | 2024-11-16 09:05:17 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.647 | ************* | - | 2 | AbstractApplicationContext | finishRefresh | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36333 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731719117553 | 2024-11-16 09:05:17 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.649 | ************* | - | 2 | AbstractApplicationContext | finishRefresh | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36333 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731719117580 | 2024-11-16 09:05:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.651 | ************* | - | 2 | Application | main | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36360 | 0 | - | - | - | - | main c.t.g.Application Started Application in 36.8 seconds (JVM running for 37.305)

info | 1 | 1731719117607 | 2024-11-16 09:05:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.653 | ************* | - | 2 | Application | main | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36387 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731719117607 | 2024-11-16 09:05:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.655 | ************* | - | 2 | Application | main | 1a25708e81a9458cb79fec03df2c60e7 | - | - | - | - | 36387 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 231 | 1731719117612 | 2024-11-16 09:05:17 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************* | - | 2 | ChatRoomService | refreshChatCache | 3fe241c198fa424592f9e82b85a66585 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 170 | 1731719120849 | 2024-11-16 09:05:20 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | ************* | - | 2 | StandardWrapper | initServlet | 329dfc66bb854c0c9f074eb2b7c3fddf | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 170 | 1731719120849 | 2024-11-16 09:05:20 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | ************* | - | 2 | AuthenticatorBase | invoke | 329dfc66bb854c0c9f074eb2b7c3fddf | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 170 | 1731719120852 | 2024-11-16 09:05:20 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | ************* | - | 2 | AuthenticatorBase | invoke | 329dfc66bb854c0c9f074eb2b7c3fddf | - | - | - | - | 3 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 3 ms

info | 161 | 1731719140748 | 2024-11-16 09:05:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 23346 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719140751 | 2024-11-16 09:05:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 23349 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719154637 | 2024-11-16 09:05:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719163554 | 2024-11-16 09:06:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 46152 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719167399 | 2024-11-16 09:06:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 12762 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719177404 | 2024-11-16 09:06:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 22766 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731719187403 | 2024-11-16 09:06:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731719197400 | 2024-11-16 09:06:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 9998 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719207404 | 2024-11-16 09:06:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 90002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719217404 | 2024-11-16 09:06:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 62766 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719227399 | 2024-11-16 09:07:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 72762 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719237401 | 2024-11-16 09:07:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 82763 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719247403 | 2024-11-16 09:07:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 130001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719257399 | 2024-11-16 09:07:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 102761 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719267404 | 2024-11-16 09:07:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 112766 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719277404 | 2024-11-16 09:07:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 122766 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719287403 | 2024-11-16 09:08:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 132765 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731719297401 | 2024-11-16 09:08:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 109999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731719307401 | 2024-11-16 09:08:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 119999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719317399 | 2024-11-16 09:08:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 199997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719327408 | 2024-11-16 09:08:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 172770 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719337407 | 2024-11-16 09:08:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 182770 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719347409 | 2024-11-16 09:09:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 192772 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719357415 | 2024-11-16 09:09:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 202778 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719367411 | 2024-11-16 09:09:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 212774 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719377416 | 2024-11-16 09:09:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 222778 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719387416 | 2024-11-16 09:09:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 232779 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719397414 | 2024-11-16 09:09:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 242777 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719407414 | 2024-11-16 09:10:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 252776 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 173 | 1731719412385 | 2024-11-16 09:10:12 | v2/managerRoom/adminReportingRoom | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************* | - | 2 | managerRoom | adminReportingRoom | werjljwewer22345 | - | - | - | - | 4719 | 0 | - | - | - | - | http-nio-8087-exec-5 c.t.c.c.c.SoaClient register http client : [http://g3.test.hbmonitor.com] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 165 | 1731719417414 | 2024-11-16 09:10:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 262776 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719427414 | 2024-11-16 09:10:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 272777 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719437416 | 2024-11-16 09:10:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 282778 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731719447413 | 2024-11-16 09:10:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 260010 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719457412 | 2024-11-16 09:10:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 302774 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719467411 | 2024-11-16 09:11:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 312774 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719477412 | 2024-11-16 09:11:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 322775 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719487416 | 2024-11-16 09:11:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 332778 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719497416 | 2024-11-16 09:11:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 342779 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 231 | 1731719501292 | 2024-11-16 09:11:41 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | ************* | - | 2 | ChatRoomService | refreshChatCache | 3fe241c198fa424592f9e82b85a66585 | - | - | - | - | 383680 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 165 | 1731719507415 | 2024-11-16 09:11:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 352778 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719517413 | 2024-11-16 09:11:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 362776 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719527411 | 2024-11-16 09:12:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 372774 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719537414 | 2024-11-16 09:12:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 382776 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719547414 | 2024-11-16 09:12:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 392777 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719557414 | 2024-11-16 09:12:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 402777 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719567416 | 2024-11-16 09:12:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 412778 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719577412 | 2024-11-16 09:12:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 422774 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719587411 | 2024-11-16 09:13:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 432773 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719597412 | 2024-11-16 09:13:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 442775 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731719607414 | 2024-11-16 09:13:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 452777 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731719625145 | 2024-11-16 09:13:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 437743 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731719628394 | 2024-11-16 09:13:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 440992 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719637412 | 2024-11-16 09:13:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 520009 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719647411 | 2024-11-16 09:14:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 530009 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719657416 | 2024-11-16 09:14:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 540014 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719667416 | 2024-11-16 09:14:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 550013 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719677416 | 2024-11-16 09:14:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 560013 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719687413 | 2024-11-16 09:14:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 570010 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731719697420 | 2024-11-16 09:14:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 580018 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1731719700049 | 2024-11-16 09:15:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************* | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | fcb693e7c04a4e98aa38ff9bb67ee59c | - | - | - | - | 0 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-11-16 09:05:09,460 to 2024-11-16 09:15:00,037
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 166 | 1731720130798 | 2024-11-16 09:22:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 943396 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731720140801 | 2024-11-16 09:22:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 953398 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731720150799 | 2024-11-16 09:22:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 1033396 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731720160795 | 2024-11-16 09:22:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 1043393 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731720170797 | 2024-11-16 09:22:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 1053395 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731720180798 | 2024-11-16 09:23:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 1063396 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731720190797 | 2024-11-16 09:23:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 1073395 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731720200797 | 2024-11-16 09:23:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1013395 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731720210794 | 2024-11-16 09:23:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1023392 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731720220797 | 2024-11-16 09:23:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1033395 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731720230795 | 2024-11-16 09:23:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 1113393 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731720240798 | 2024-11-16 09:24:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 1123395 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731720250794 | 2024-11-16 09:24:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1063392 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731720260798 | 2024-11-16 09:24:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1073396 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731720280146 | 2024-11-16 09:24:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.85 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 1125508 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731720280797 | 2024-11-16 09:24:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1093395 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731720290794 | 2024-11-16 09:24:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1103391 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731720300795 | 2024-11-16 09:25:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.87 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 1146158 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731720310798 | 2024-11-16 09:25:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.89 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 1156161 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 187 | 1731720318443 | 2024-11-16 09:25:18 | v2/manageChatRoom/dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************* | - | 2 | manageChatRoom | dailyOperationData | werjljwewer22345 | - | - | - | - | 1581 | 0 | - | - | - | - | http-nio-8087-exec-19 c.t.g.c.s.DataManager 构建查询条件：起始时间:2024-11-10,结束时间：2024-11-15,团队集合:[],hostUuids:null,chatStatus:null

info | 166 | 1731720320798 | 2024-11-16 09:25:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1133395 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731720330797 | 2024-11-16 09:25:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1143394 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731720340796 | 2024-11-16 09:25:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 1153394 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731720350793 | 2024-11-16 09:25:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.91 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 1196156 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731720360798 | 2024-11-16 09:26:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.93 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 1206161 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731720370795 | 2024-11-16 09:26:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.95 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 1216158 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731720380796 | 2024-11-16 09:26:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.97 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 1226158 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731720390807 | 2024-11-16 09:26:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.99 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 1236170 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731724544113 | 2024-11-16 10:35:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5426711 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724554114 | 2024-11-16 10:35:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5366711 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731724564115 | 2024-11-16 10:36:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5446712 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724574115 | 2024-11-16 10:36:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5386712 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724584110 | 2024-11-16 10:36:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5396707 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724594110 | 2024-11-16 10:36:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5406707 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724604110 | 2024-11-16 10:36:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.77 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5416708 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724614111 | 2024-11-16 10:36:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.79 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5426709 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724624114 | 2024-11-16 10:37:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.81 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5436712 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724634111 | 2024-11-16 10:37:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.83 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5446709 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724644114 | 2024-11-16 10:37:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.85 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5456711 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724654110 | 2024-11-16 10:37:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.87 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5466708 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724664111 | 2024-11-16 10:37:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.89 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5476708 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724674113 | 2024-11-16 10:37:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.91 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5486710 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724684114 | 2024-11-16 10:38:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.93 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5496712 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731724694111 | 2024-11-16 10:38:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5576708 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724704111 | 2024-11-16 10:38:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.95 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5516708 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724714115 | 2024-11-16 10:38:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.97 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5526712 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731724724110 | 2024-11-16 10:38:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5606708 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731724734114 | 2024-11-16 10:38:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5616712 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724744113 | 2024-11-16 10:39:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.99 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5556711 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731724754115 | 2024-11-16 10:39:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.101 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5566712 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731724764114 | 2024-11-16 10:39:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5646712 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731724774112 | 2024-11-16 10:39:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5656710 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731724784110 | 2024-11-16 10:39:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5666708 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731724794114 | 2024-11-16 10:39:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.77 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5676712 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731724804115 | 2024-11-16 10:40:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.79 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5686713 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731725117357 | 2024-11-16 10:45:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.81 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 5999954 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731725177359 | 2024-11-16 10:46:17 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.103 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5989956 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731725187358 | 2024-11-16 10:46:27 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.105 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 5999955 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731725197360 | 2024-11-16 10:46:37 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.107 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 6009957 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731725207359 | 2024-11-16 10:46:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.109 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 6019957 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731725217360 | 2024-11-16 10:46:57 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.111 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 6029957 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731725681936 | 2024-11-16 10:54:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.113 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 6494534 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731725741938 | 2024-11-16 10:55:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.105 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 6624536 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731725751939 | 2024-11-16 10:55:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.107 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 6634536 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731725761934 | 2024-11-16 10:56:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.109 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 6644532 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731725771934 | 2024-11-16 10:56:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.111 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 6654535 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1731725774545 | 2024-11-16 10:56:14 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************* | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | fcb693e7c04a4e98aa38ff9bb67ee59c | - | - | - | - | 6074495 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-11-16 09:15:00,037 to 2024-11-16 10:56:14,544
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 165 | 1731725781938 | 2024-11-16 10:56:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.127 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 6627300 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731725791933 | 2024-11-16 10:56:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.129 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 6637295 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731725801934 | 2024-11-16 10:56:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.121 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 6614531 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731725811934 | 2024-11-16 10:56:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.123 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 6624531 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731725821937 | 2024-11-16 10:57:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.125 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 6634534 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731725831940 | 2024-11-16 10:57:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.127 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 6644538 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731728940817 | 2024-11-16 11:49:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.137 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 9786179 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731728950811 | 2024-11-16 11:49:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.139 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 9796174 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731728960805 | 2024-11-16 11:49:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.141 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 9806167 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731728970805 | 2024-11-16 11:49:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.127 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 9853402 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731728980806 | 2024-11-16 11:49:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.129 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 9863403 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731728990806 | 2024-11-16 11:49:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.131 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 9873403 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731729000801 | 2024-11-16 11:50:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.133 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 9883398 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731729010800 | 2024-11-16 11:50:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.135 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 9893397 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731729020800 | 2024-11-16 11:50:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.137 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 9903398 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731729030803 | 2024-11-16 11:50:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.139 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 9913400 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731730070050 | 2024-11-16 12:07:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.141 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 10952648 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732874019 | 2024-11-16 12:54:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.165 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13719381 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732884019 | 2024-11-16 12:54:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.167 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13729381 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732894020 | 2024-11-16 12:54:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.169 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13739383 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732904019 | 2024-11-16 12:55:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.171 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13749381 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732914018 | 2024-11-16 12:55:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.173 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13759381 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732924020 | 2024-11-16 12:55:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.175 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13769382 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732934017 | 2024-11-16 12:55:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.177 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13779379 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732944021 | 2024-11-16 12:55:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.179 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13789383 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732954018 | 2024-11-16 12:55:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.181 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13799381 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732964021 | 2024-11-16 12:56:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.183 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13809383 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731732974020 | 2024-11-16 12:56:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.185 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13819383 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731732984021 | 2024-11-16 12:56:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.163 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 13866618 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731732994019 | 2024-11-16 12:56:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.165 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 13876616 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1731733004018 | 2024-11-16 12:56:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.167 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 4cf3bc3c7c6342edbe12e4ba2450ecc7 | - | - | - | - | 13886616 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733014020 | 2024-11-16 12:56:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.187 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13859383 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733024018 | 2024-11-16 12:57:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.189 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13869381 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733034021 | 2024-11-16 12:57:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.191 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13879384 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733044019 | 2024-11-16 12:57:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.193 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13889382 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733054018 | 2024-11-16 12:57:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.195 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13899380 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733064020 | 2024-11-16 12:57:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.197 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13909382 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731733074022 | 2024-11-16 12:57:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.141 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 13886619 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731733084022 | 2024-11-16 12:58:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.143 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 13896619 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731733094022 | 2024-11-16 12:58:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.145 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 13906619 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731733104022 | 2024-11-16 12:58:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.147 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 13916620 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731733114018 | 2024-11-16 12:58:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.149 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 13926615 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731733124022 | 2024-11-16 12:58:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.151 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 0db0695f8ad542578a848ab7136d4c58 | - | - | - | - | 13936619 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733134022 | 2024-11-16 12:58:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.199 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13979384 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733144022 | 2024-11-16 12:59:04 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.201 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13989385 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733154019 | 2024-11-16 12:59:14 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.203 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 13999382 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733164022 | 2024-11-16 12:59:24 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.205 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 14009385 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733174022 | 2024-11-16 12:59:34 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.207 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 14019385 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1731733184028 | 2024-11-16 12:59:44 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.209 | ************* | - | 2 | ThreadPoolExecutor | runWorker | 32a5311d911d4f44b4bae8d458087918 | - | - | - | - | 14029390 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

