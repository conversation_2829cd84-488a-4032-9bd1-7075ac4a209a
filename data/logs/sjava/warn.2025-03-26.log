warning | 197 | 1742969995816 | 2025-03-26 14:19:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 4c3b32d1ef3b4614a296b698016acb68 | - | - | - | - | 835 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 197 | 1742969995820 | 2025-03-26 14:19:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 4c3b32d1ef3b4614a296b698016acb68 | - | - | - | - | 839 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 317 | 1742970008335 | 2025-03-26 14:20:08 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | f604eab894844719ab3529ca3bf889c4 | - | - | - | - | 2150 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1060 ms]

warning | 350 | 1742970020915 | 2025-03-26 14:20:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 9f2576ae589647d49ac586cd5bedcb2c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970000426, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5947, jvmUsedMemory=0.6044, jvmMaxMemory=3.5557, jvmMemoryUsage=0.17, diskUsed=233.5201, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 350 | 1742970020942 | 2025-03-26 14:20:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 9f2576ae589647d49ac586cd5bedcb2c | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970010419, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0186, jvmUsedMemory=0.7063, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1986, diskUsed=233.5216, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 350 | 1742970020959 | 2025-03-26 14:20:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 9f2576ae589647d49ac586cd5bedcb2c | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970020417, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.188, jvmUsedMemory=0.7197, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2024, diskUsed=233.5243, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 350 | 1742970021184 | 2025-03-26 14:20:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 9f2576ae589647d49ac586cd5bedcb2c | - | - | - | - | 250 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 350 | 1742970021186 | 2025-03-26 14:20:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 9f2576ae589647d49ac586cd5bedcb2c | - | - | - | - | 252 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 350 | 1742970021197 | 2025-03-26 14:20:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | 9f2576ae589647d49ac586cd5bedcb2c | - | - | - | - | 263 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 350 | 1742970021213 | 2025-03-26 14:20:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ActorCell | receiveMessage | 9f2576ae589647d49ac586cd5bedcb2c | - | - | - | - | 279 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 350 | 1742970021215 | 2025-03-26 14:20:21 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ActorCell | receiveMessage | 9f2576ae589647d49ac586cd5bedcb2c | - | - | - | - | 281 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 417 | 1742970050468 | 2025-03-26 14:20:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 734bce3887a746eeb0f5cbd3ff0560d9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970030416, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4976, jvmUsedMemory=0.7487, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2106, diskUsed=233.5248, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 417 | 1742970050476 | 2025-03-26 14:20:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 734bce3887a746eeb0f5cbd3ff0560d9 | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970040417, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.187, jvmUsedMemory=0.7615, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2142, diskUsed=233.5459, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 417 | 1742970050481 | 2025-03-26 14:20:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 734bce3887a746eeb0f5cbd3ff0560d9 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970050422, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.3726, jvmUsedMemory=0.7737, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2176, diskUsed=233.5499, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 417 | 1742970050614 | 2025-03-26 14:20:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 734bce3887a746eeb0f5cbd3ff0560d9 | - | - | - | - | 145 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 417 | 1742970050616 | 2025-03-26 14:20:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 734bce3887a746eeb0f5cbd3ff0560d9 | - | - | - | - | 147 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 481 | 1742970080476 | 2025-03-26 14:21:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 6711c39687374f74bbd46a0e99ea5804 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970060417, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0005, jvmUsedMemory=0.7908, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2224, diskUsed=233.547, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 481 | 1742970080482 | 2025-03-26 14:21:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 6711c39687374f74bbd46a0e99ea5804 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970070418, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6187, jvmUsedMemory=0.8018, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2255, diskUsed=233.5442, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 481 | 1742970080484 | 2025-03-26 14:21:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 6711c39687374f74bbd46a0e99ea5804 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970080421, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3022, jvmUsedMemory=0.8144, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2291, diskUsed=233.5465, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 481 | 1742970080601 | 2025-03-26 14:21:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 6711c39687374f74bbd46a0e99ea5804 | - | - | - | - | 124 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 481 | 1742970080603 | 2025-03-26 14:21:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 6711c39687374f74bbd46a0e99ea5804 | - | - | - | - | 125 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 541 | 1742970110470 | 2025-03-26 14:21:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | ee094585e1af47f7820d8f5b7d83fe7b | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970090419, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1753, jvmUsedMemory=0.8322, jvmMaxMemory=3.5557, jvmMemoryUsage=0.234, diskUsed=233.5479, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 541 | 1742970110480 | 2025-03-26 14:21:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | ee094585e1af47f7820d8f5b7d83fe7b | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970100422, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6865, jvmUsedMemory=0.8413, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2366, diskUsed=233.5475, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 541 | 1742970110485 | 2025-03-26 14:21:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | ee094585e1af47f7820d8f5b7d83fe7b | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970110417, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.667, jvmUsedMemory=0.8547, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2404, diskUsed=233.5483, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 480 | 1742970110624 | 2025-03-26 14:21:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | a89abe9d5795476bb0e92dc094857812 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 480 | 1742970110626 | 2025-03-26 14:21:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | a89abe9d5795476bb0e92dc094857812 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-45 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 541 | 1742970140475 | 2025-03-26 14:22:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | Actor | aroundReceive | ee094585e1af47f7820d8f5b7d83fe7b | - | - | - | - | 30007 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970120421, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6982, jvmUsedMemory=0.87, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2447, diskUsed=233.5501, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 541 | 1742970140482 | 2025-03-26 14:22:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | Actor | aroundReceive | ee094585e1af47f7820d8f5b7d83fe7b | - | - | - | - | 30013 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970130419, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5103, jvmUsedMemory=0.8834, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2484, diskUsed=233.5516, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 541 | 1742970140487 | 2025-03-26 14:22:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | Actor | aroundReceive | ee094585e1af47f7820d8f5b7d83fe7b | - | - | - | - | 30018 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970140417, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3579, jvmUsedMemory=0.8977, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2525, diskUsed=233.5519, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 541 | 1742970140628 | 2025-03-26 14:22:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ActorCell | receiveMessage | ee094585e1af47f7820d8f5b7d83fe7b | - | - | - | - | 30158 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 541 | 1742970140631 | 2025-03-26 14:22:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ActorCell | receiveMessage | ee094585e1af47f7820d8f5b7d83fe7b | - | - | - | - | 30160 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 610 | 1742970170485 | 2025-03-26 14:22:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 9b0db81971824f5fbb84d8b95122adc2 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970150418, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4692, jvmUsedMemory=0.913, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2568, diskUsed=233.5524, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 610 | 1742970170493 | 2025-03-26 14:22:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 9b0db81971824f5fbb84d8b95122adc2 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970160416, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3232, jvmUsedMemory=0.9265, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2606, diskUsed=233.5541, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 610 | 1742970170498 | 2025-03-26 14:22:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 9b0db81971824f5fbb84d8b95122adc2 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970170421, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0391, jvmUsedMemory=0.1408, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0396, diskUsed=233.5597, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 610 | 1742970170648 | 2025-03-26 14:22:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 9b0db81971824f5fbb84d8b95122adc2 | - | - | - | - | 162 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 610 | 1742970170650 | 2025-03-26 14:22:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 9b0db81971824f5fbb84d8b95122adc2 | - | - | - | - | 164 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 672 | 1742970200582 | 2025-03-26 14:23:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 7fd0c1b3236641ddb8b54b13e6ae4a17 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970180515, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7251, jvmUsedMemory=0.1562, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0439, diskUsed=233.5569, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 672 | 1742970200594 | 2025-03-26 14:23:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 7fd0c1b3236641ddb8b54b13e6ae4a17 | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970190515, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4595, jvmUsedMemory=0.1634, jvmMaxMemory=3.5557, jvmMemoryUsage=0.046, diskUsed=233.5571, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 672 | 1742970200599 | 2025-03-26 14:23:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 7fd0c1b3236641ddb8b54b13e6ae4a17 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970200514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5547, jvmUsedMemory=0.1702, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0479, diskUsed=233.5571, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 672 | 1742970200740 | 2025-03-26 14:23:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 7fd0c1b3236641ddb8b54b13e6ae4a17 | - | - | - | - | 154 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 672 | 1742970200742 | 2025-03-26 14:23:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 7fd0c1b3236641ddb8b54b13e6ae4a17 | - | - | - | - | 156 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 732 | 1742970230581 | 2025-03-26 14:23:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 0ec349283da847acae8a3f19452c0ccf | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970210516, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3301, jvmUsedMemory=0.1776, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0499, diskUsed=233.5582, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 732 | 1742970230593 | 2025-03-26 14:23:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 0ec349283da847acae8a3f19452c0ccf | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970220514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0386, jvmUsedMemory=0.1853, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0521, diskUsed=233.5573, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 732 | 1742970230599 | 2025-03-26 14:23:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 0ec349283da847acae8a3f19452c0ccf | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970230514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5708, jvmUsedMemory=0.1925, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0541, diskUsed=233.5583, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 732 | 1742970230737 | 2025-03-26 14:23:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 0ec349283da847acae8a3f19452c0ccf | - | - | - | - | 152 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 732 | 1742970230739 | 2025-03-26 14:23:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 0ec349283da847acae8a3f19452c0ccf | - | - | - | - | 154 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 787 | 1742970260563 | 2025-03-26 14:24:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | ef3de7859d9c4dcb982a89ca69e8027d | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970240513, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2554, jvmUsedMemory=0.2093, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0589, diskUsed=233.5583, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 787 | 1742970260575 | 2025-03-26 14:24:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | ef3de7859d9c4dcb982a89ca69e8027d | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970250514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2769, jvmUsedMemory=0.2173, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0611, diskUsed=233.5593, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 787 | 1742970260581 | 2025-03-26 14:24:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | ef3de7859d9c4dcb982a89ca69e8027d | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970260513, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2466, jvmUsedMemory=0.226, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0636, diskUsed=233.5622, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 787 | 1742970260721 | 2025-03-26 14:24:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | ef3de7859d9c4dcb982a89ca69e8027d | - | - | - | - | 154 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 787 | 1742970260722 | 2025-03-26 14:24:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | ef3de7859d9c4dcb982a89ca69e8027d | - | - | - | - | 156 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 854 | 1742970290565 | 2025-03-26 14:24:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 67716532175a4128be8de82233097b28 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970270516, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0479, jvmUsedMemory=0.2597, jvmMaxMemory=3.5557, jvmMemoryUsage=0.073, diskUsed=233.5622, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 854 | 1742970290575 | 2025-03-26 14:24:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 67716532175a4128be8de82233097b28 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970280515, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1875, jvmUsedMemory=0.3102, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0872, diskUsed=233.5609, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 854 | 1742970290579 | 2025-03-26 14:24:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 67716532175a4128be8de82233097b28 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970290513, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1646, jvmUsedMemory=0.3177, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0894, diskUsed=233.5649, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 854 | 1742970290716 | 2025-03-26 14:24:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 67716532175a4128be8de82233097b28 | - | - | - | - | 148 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 854 | 1742970290718 | 2025-03-26 14:24:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 67716532175a4128be8de82233097b28 | - | - | - | - | 150 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 929 | 1742970320571 | 2025-03-26 14:25:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | fb540c0b00a4419e9358ebd4131bfde5 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970300512, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0527, jvmUsedMemory=0.3272, jvmMaxMemory=3.5557, jvmMemoryUsage=0.092, diskUsed=233.5643, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 929 | 1742970320582 | 2025-03-26 14:25:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | fb540c0b00a4419e9358ebd4131bfde5 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970310514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8169, jvmUsedMemory=0.3363, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0946, diskUsed=233.5659, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 929 | 1742970320587 | 2025-03-26 14:25:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | fb540c0b00a4419e9358ebd4131bfde5 | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970320516, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9858, jvmUsedMemory=0.3412, jvmMaxMemory=3.5557, jvmMemoryUsage=0.096, diskUsed=233.5668, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 929 | 1742970320728 | 2025-03-26 14:25:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | fb540c0b00a4419e9358ebd4131bfde5 | - | - | - | - | 155 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 929 | 1742970320731 | 2025-03-26 14:25:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | fb540c0b00a4419e9358ebd4131bfde5 | - | - | - | - | 157 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 930 | 1742970350580 | 2025-03-26 14:25:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | d038b28c0abb438f9b72ea9ea3043a33 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970330517, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8276, jvmUsedMemory=0.3502, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0985, diskUsed=233.567, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 930 | 1742970350589 | 2025-03-26 14:25:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | d038b28c0abb438f9b72ea9ea3043a33 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970340515, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9272, jvmUsedMemory=0.3562, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1002, diskUsed=233.5683, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 930 | 1742970350595 | 2025-03-26 14:25:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | d038b28c0abb438f9b72ea9ea3043a33 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970350513, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6304, jvmUsedMemory=0.3641, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1024, diskUsed=233.5692, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 930 | 1742970350705 | 2025-03-26 14:25:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | d038b28c0abb438f9b72ea9ea3043a33 | - | - | - | - | 121 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 930 | 1742970350706 | 2025-03-26 14:25:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | d038b28c0abb438f9b72ea9ea3043a33 | - | - | - | - | 123 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-62 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1041 | 1742970380575 | 2025-03-26 14:26:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | a5ff030db568491d90f79ac48b58ba62 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970360513, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3794, jvmUsedMemory=0.3742, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1052, diskUsed=233.5702, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1041 | 1742970380587 | 2025-03-26 14:26:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | a5ff030db568491d90f79ac48b58ba62 | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970370516, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3267, jvmUsedMemory=0.3828, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1077, diskUsed=233.5713, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1041 | 1742970380592 | 2025-03-26 14:26:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | a5ff030db568491d90f79ac48b58ba62 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970380515, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.356, jvmUsedMemory=0.3898, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1096, diskUsed=233.578, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1041 | 1742970380730 | 2025-03-26 14:26:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | a5ff030db568491d90f79ac48b58ba62 | - | - | - | - | 151 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1041 | 1742970380732 | 2025-03-26 14:26:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | a5ff030db568491d90f79ac48b58ba62 | - | - | - | - | 152 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1102 | 1742970410572 | 2025-03-26 14:26:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 305a5eaac9334fdcb1d7671c1b8d12bc | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970390516, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3008, jvmUsedMemory=0.4012, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1128, diskUsed=233.5913, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1102 | 1742970410579 | 2025-03-26 14:26:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 305a5eaac9334fdcb1d7671c1b8d12bc | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970400518, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.1738, jvmUsedMemory=0.4083, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1148, diskUsed=233.5914, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1102 | 1742970410585 | 2025-03-26 14:26:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 305a5eaac9334fdcb1d7671c1b8d12bc | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970410516, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.1465, jvmUsedMemory=0.4154, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1168, diskUsed=233.5668, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1102 | 1742970410703 | 2025-03-26 14:26:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 305a5eaac9334fdcb1d7671c1b8d12bc | - | - | - | - | 130 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1102 | 1742970410705 | 2025-03-26 14:26:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 305a5eaac9334fdcb1d7671c1b8d12bc | - | - | - | - | 132 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1214 | 1742970440566 | 2025-03-26 14:27:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | ecc78d9ca12443e19bf0484c12ac88bb | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970420517, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.043, jvmUsedMemory=0.4226, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1189, diskUsed=233.5669, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1214 | 1742970440575 | 2025-03-26 14:27:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | ecc78d9ca12443e19bf0484c12ac88bb | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970430514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=0.9619, jvmUsedMemory=0.4314, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1213, diskUsed=233.5679, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1214 | 1742970440581 | 2025-03-26 14:27:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | ecc78d9ca12443e19bf0484c12ac88bb | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970440519, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.2876, jvmUsedMemory=0.4373, jvmMaxMemory=3.5557, jvmMemoryUsage=0.123, diskUsed=233.5637, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1214 | 1742970440716 | 2025-03-26 14:27:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | ecc78d9ca12443e19bf0484c12ac88bb | - | - | - | - | 149 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1214 | 1742970440718 | 2025-03-26 14:27:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | ecc78d9ca12443e19bf0484c12ac88bb | - | - | - | - | 151 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1260 | 1742970470557 | 2025-03-26 14:27:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 704352f21f024998992febceac2bdcfe | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970450513, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.1689, jvmUsedMemory=0.4449, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1251, diskUsed=233.5639, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1260 | 1742970470567 | 2025-03-26 14:27:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 704352f21f024998992febceac2bdcfe | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970460516, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6104, jvmUsedMemory=0.4498, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1265, diskUsed=233.5631, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1260 | 1742970470573 | 2025-03-26 14:27:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 704352f21f024998992febceac2bdcfe | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970470515, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8906, jvmUsedMemory=0.4559, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1282, diskUsed=233.5638, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1260 | 1742970470711 | 2025-03-26 14:27:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 704352f21f024998992febceac2bdcfe | - | - | - | - | 154 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1260 | 1742970470715 | 2025-03-26 14:27:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 704352f21f024998992febceac2bdcfe | - | - | - | - | 156 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1326 | 1742970500565 | 2025-03-26 14:28:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | e6157016dc1745e39ee321023a210974 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970480517, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8203, jvmUsedMemory=0.4673, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1314, diskUsed=233.5566, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1326 | 1742970500572 | 2025-03-26 14:28:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | e6157016dc1745e39ee321023a210974 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970490518, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7739, jvmUsedMemory=0.4759, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1339, diskUsed=233.5566, diskTotal=460.4317, diskUsage=0.5073, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1326 | 1742970500577 | 2025-03-26 14:28:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | e6157016dc1745e39ee321023a210974 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970500515, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7349, jvmUsedMemory=0.4838, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1361, diskUsed=233.5376, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1326 | 1742970500700 | 2025-03-26 14:28:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | e6157016dc1745e39ee321023a210974 | - | - | - | - | 134 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1326 | 1742970500702 | 2025-03-26 14:28:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | e6157016dc1745e39ee321023a210974 | - | - | - | - | 136 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-75 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1383 | 1742970530565 | 2025-03-26 14:28:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 656df1e3d1864a30aad48ad98140854d | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970510514, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8618, jvmUsedMemory=0.4935, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1388, diskUsed=233.5388, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1383 | 1742970530571 | 2025-03-26 14:28:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 656df1e3d1864a30aad48ad98140854d | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970520520, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8091, jvmUsedMemory=0.5018, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1411, diskUsed=233.5412, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1383 | 1742970530575 | 2025-03-26 14:28:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 656df1e3d1864a30aad48ad98140854d | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1742970530520, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.604, jvmUsedMemory=0.5069, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1426, diskUsed=233.5415, diskTotal=460.4317, diskUsage=0.5072, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1383 | 1742970530759 | 2025-03-26 14:28:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 656df1e3d1864a30aad48ad98140854d | - | - | - | - | 193 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1383 | 1742970530761 | 2025-03-26 14:28:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 656df1e3d1864a30aad48ad98140854d | - | - | - | - | 196 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1742970530922 | 2025-03-26 14:28:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 0e4efa0ccd21486c95dbd852c864a3fb | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1742970530923 | 2025-03-26 14:28:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | ef8f926e31254793bb0450e2216c7bc1 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1742970530925 | 2025-03-26 14:28:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | ef8f926e31254793bb0450e2216c7bc1 | - | - | - | - | 2 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1742970530934 | 2025-03-26 14:28:50 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 0e4efa0ccd21486c95dbd852c864a3fb | - | - | - | - | 11 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 1383 | 1742970531033 | 2025-03-26 14:28:51 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | 656df1e3d1864a30aad48ad98140854d | - | - | - | - | 467 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

