warning | 132 | 1731376506247 | 2024-11-12 09:55:06 | v2/ActorCell/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | invoke | e96ec8868557406cbd4794b0ca984dbb | - | - | - | - | 111 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 132 | 1731376506247 | 2024-11-12 09:55:06 | v2/ActorCell/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | invoke | e96ec8868557406cbd4794b0ca984dbb | - | - | - | - | 111 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 42 | 1731376515022 | 2024-11-12 09:55:15 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 13986548274d43fb8563f0ad367f343c | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 38 | 1731376515021 | 2024-11-12 09:55:15 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 1c18d4ef9a1f4e9a9649b03ed7de86d3 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 42 | 1731376515024 | 2024-11-12 09:55:15 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 13986548274d43fb8563f0ad367f343c | - | - | - | - | 2 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 38 | 1731376515024 | 2024-11-12 09:55:15 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 1c18d4ef9a1f4e9a9649b03ed7de86d3 | - | - | - | - | 2 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 140 | 1731376515052 | 2024-11-12 09:55:15 | v2/ActorCell/invoke | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | invoke | fe8aa3ec598d43d4901df2248a45a56c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 44 | 1731376664827 | 2024-11-12 09:57:44 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | c8f6a9dd827d4bd0a19e0a19f220a3e2 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1731376664827 | 2024-11-12 09:57:44 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | a71674d4d9a54e3c9658cd3b7fe63548 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1731376664828 | 2024-11-12 09:57:44 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | c8f6a9dd827d4bd0a19e0a19f220a3e2 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1731376664828 | 2024-11-12 09:57:44 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | a71674d4d9a54e3c9658cd3b7fe63548 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 185 | 1731376816804 | 2024-11-12 10:00:16 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | d279ca4a75144e818187d1cef0ad9aaa | - | - | - | - | 134 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 185 | 1731376816804 | 2024-11-12 10:00:16 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | d279ca4a75144e818187d1cef0ad9aaa | - | - | - | - | 134 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 326 | 1731376837653 | 2024-11-12 10:00:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 373dc394ff7d462ebb14cd5c10506d52 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376817502, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4087, jvmUsedMemory=0.5335, jvmMaxMemory=3.5557, jvmMemoryUsage=0.15, diskUsed=222.6593, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 326 | 1731376837659 | 2024-11-12 10:00:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 373dc394ff7d462ebb14cd5c10506d52 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376827504, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3389, jvmUsedMemory=0.6769, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1904, diskUsed=222.6595, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 326 | 1731376837659 | 2024-11-12 10:00:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 373dc394ff7d462ebb14cd5c10506d52 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376837501, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4399, jvmUsedMemory=0.682, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1918, diskUsed=222.6595, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 185 | 1731376837702 | 2024-11-12 10:00:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | d279ca4a75144e818187d1cef0ad9aaa | - | - | - | - | 21032 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 185 | 1731376837703 | 2024-11-12 10:00:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | d279ca4a75144e818187d1cef0ad9aaa | - | - | - | - | 21033 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 185 | 1731376837705 | 2024-11-12 10:00:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | d279ca4a75144e818187d1cef0ad9aaa | - | - | - | - | 21035 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 185 | 1731376837709 | 2024-11-12 10:00:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | d279ca4a75144e818187d1cef0ad9aaa | - | - | - | - | 21039 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 185 | 1731376837709 | 2024-11-12 10:00:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | d279ca4a75144e818187d1cef0ad9aaa | - | - | - | - | 21039 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 326 | 1731376867537 | 2024-11-12 10:01:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 373dc394ff7d462ebb14cd5c10506d52 | - | - | - | - | 29879 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376847503, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2178, jvmUsedMemory=0.6999, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1968, diskUsed=222.6595, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 326 | 1731376867538 | 2024-11-12 10:01:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 373dc394ff7d462ebb14cd5c10506d52 | - | - | - | - | 29880 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376857500, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0366, jvmUsedMemory=0.7054, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1984, diskUsed=222.6595, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 326 | 1731376867538 | 2024-11-12 10:01:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 373dc394ff7d462ebb14cd5c10506d52 | - | - | - | - | 29880 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376867500, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2451, jvmUsedMemory=0.712, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2002, diskUsed=222.6581, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 326 | 1731376867555 | 2024-11-12 10:01:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 373dc394ff7d462ebb14cd5c10506d52 | - | - | - | - | 29897 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 326 | 1731376867556 | 2024-11-12 10:01:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 373dc394ff7d462ebb14cd5c10506d52 | - | - | - | - | 29898 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 374 | 1731376897538 | 2024-11-12 10:01:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 26f28743ca8649a8a8a8e86c234096c9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376877501, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2871, jvmUsedMemory=0.7189, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2022, diskUsed=222.6581, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 374 | 1731376897541 | 2024-11-12 10:01:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 26f28743ca8649a8a8a8e86c234096c9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376887500, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0952, jvmUsedMemory=0.7258, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2041, diskUsed=222.6581, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 374 | 1731376897541 | 2024-11-12 10:01:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 26f28743ca8649a8a8a8e86c234096c9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376897503, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8525, jvmUsedMemory=0.7317, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2058, diskUsed=222.6581, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 374 | 1731376897559 | 2024-11-12 10:01:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 26f28743ca8649a8a8a8e86c234096c9 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 374 | 1731376897560 | 2024-11-12 10:01:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 26f28743ca8649a8a8a8e86c234096c9 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 467 | 1731376927532 | 2024-11-12 10:02:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 152664a60fc94a12a976c9f4ee5345f0 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376907507, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7212, jvmUsedMemory=0.7399, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2081, diskUsed=222.6562, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1731376927534 | 2024-11-12 10:02:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 152664a60fc94a12a976c9f4ee5345f0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376917504, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9365, jvmUsedMemory=0.7458, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2097, diskUsed=222.6562, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1731376927534 | 2024-11-12 10:02:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 152664a60fc94a12a976c9f4ee5345f0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376927500, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0259, jvmUsedMemory=0.7513, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2113, diskUsed=222.6562, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 420 | 1731376927544 | 2024-11-12 10:02:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | d162074b78314e1282656972dacc60f8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 420 | 1731376927545 | 2024-11-12 10:02:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | d162074b78314e1282656972dacc60f8 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 467 | 1731376957539 | 2024-11-12 10:02:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | 152664a60fc94a12a976c9f4ee5345f0 | - | - | - | - | 30007 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376937500, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6558, jvmUsedMemory=0.7574, jvmMaxMemory=3.5557, jvmMemoryUsage=0.213, diskUsed=222.6563, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1731376957541 | 2024-11-12 10:02:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | 152664a60fc94a12a976c9f4ee5345f0 | - | - | - | - | 30008 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376947502, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3945, jvmUsedMemory=0.763, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2146, diskUsed=222.6564, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1731376957541 | 2024-11-12 10:02:37 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | 152664a60fc94a12a976c9f4ee5345f0 | - | - | - | - | 30008 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376957503, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0259, jvmUsedMemory=0.7699, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2165, diskUsed=222.6564, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1731376957560 | 2024-11-12 10:02:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | 152664a60fc94a12a976c9f4ee5345f0 | - | - | - | - | 30027 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 467 | 1731376957560 | 2024-11-12 10:02:37 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | 152664a60fc94a12a976c9f4ee5345f0 | - | - | - | - | 30027 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 510 | 1731376987537 | 2024-11-12 10:03:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | bcdaa2f66c7c4d33bf042c879758d2a7 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376967506, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9888, jvmUsedMemory=0.7783, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2189, diskUsed=222.6565, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 510 | 1731376987540 | 2024-11-12 10:03:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | bcdaa2f66c7c4d33bf042c879758d2a7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376977506, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6089, jvmUsedMemory=0.7831, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2203, diskUsed=222.6565, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 510 | 1731376987540 | 2024-11-12 10:03:07 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | bcdaa2f66c7c4d33bf042c879758d2a7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731376987503, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5215, jvmUsedMemory=0.7886, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2218, diskUsed=222.6565, diskTotal=460.4317, diskUsage=0.4836, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 510 | 1731376987555 | 2024-11-12 10:03:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | bcdaa2f66c7c4d33bf042c879758d2a7 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 510 | 1731376987555 | 2024-11-12 10:03:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | bcdaa2f66c7c4d33bf042c879758d2a7 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1731376994592 | 2024-11-12 10:03:14 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 4d68eed2629b40c8a22d11383ab2e945 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1731376994592 | 2024-11-12 10:03:14 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 5dee38b4c3e94fb7bc81a5f1c274011f | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1731376994596 | 2024-11-12 10:03:14 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 5dee38b4c3e94fb7bc81a5f1c274011f | - | - | - | - | 3 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1731376994601 | 2024-11-12 10:03:14 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 4d68eed2629b40c8a22d11383ab2e945 | - | - | - | - | 9 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 510 | 1731376994646 | 2024-11-12 10:03:14 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | bcdaa2f66c7c4d33bf042c879758d2a7 | - | - | - | - | 7109 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 124 | 1731377028046 | 2024-11-12 10:03:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 799f1a4b5c5745a9b615b6b1e5091723 | - | - | - | - | 109 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 124 | 1731377028046 | 2024-11-12 10:03:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 799f1a4b5c5745a9b615b6b1e5091723 | - | - | - | - | 109 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 44 | 1731377034788 | 2024-11-12 10:03:54 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | a646227d9a9948ecaa850a6886891a5c | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1731377034788 | 2024-11-12 10:03:54 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 892ef23b359a465ab6b7ce140f10d2bf | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1731377034789 | 2024-11-12 10:03:54 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | a646227d9a9948ecaa850a6886891a5c | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1731377034790 | 2024-11-12 10:03:54 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 892ef23b359a465ab6b7ce140f10d2bf | - | - | - | - | 2 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 132 | 1731377034826 | 2024-11-12 10:03:54 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 0e4ab4f9384d4394a5f15d52abce318b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-11 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 121 | 1731378087704 | 2024-11-12 10:21:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 50d1fc9320fc4b9c9f497227133f746b | - | - | - | - | 114 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 121 | 1731378087705 | 2024-11-12 10:21:27 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 50d1fc9320fc4b9c9f497227133f746b | - | - | - | - | 115 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 222 | 1731378089763 | 2024-11-12 10:21:29 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 1fbc0967487343cebff56696a587b8e2 | - | - | - | - | 1250 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1088 ms]

warning | 255 | 1731378108513 | 2024-11-12 10:21:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | dbcd6dacb5ce46aaaeff99a81acbae36 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378088371, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7095, jvmUsedMemory=0.4552, jvmMaxMemory=3.5557, jvmMemoryUsage=0.128, diskUsed=222.7127, diskTotal=460.4317, diskUsage=0.4837, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 255 | 1731378108517 | 2024-11-12 10:21:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | dbcd6dacb5ce46aaaeff99a81acbae36 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378098374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9556, jvmUsedMemory=0.6261, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1761, diskUsed=223.7119, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 255 | 1731378108517 | 2024-11-12 10:21:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | dbcd6dacb5ce46aaaeff99a81acbae36 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378108371, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0229, jvmUsedMemory=0.6354, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1787, diskUsed=223.7131, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 255 | 1731378108577 | 2024-11-12 10:21:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | dbcd6dacb5ce46aaaeff99a81acbae36 | - | - | - | - | 61 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 255 | 1731378108578 | 2024-11-12 10:21:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | dbcd6dacb5ce46aaaeff99a81acbae36 | - | - | - | - | 61 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 255 | 1731378108580 | 2024-11-12 10:21:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | dbcd6dacb5ce46aaaeff99a81acbae36 | - | - | - | - | 63 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 255 | 1731378108583 | 2024-11-12 10:21:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | dbcd6dacb5ce46aaaeff99a81acbae36 | - | - | - | - | 66 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 255 | 1731378108584 | 2024-11-12 10:21:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | dbcd6dacb5ce46aaaeff99a81acbae36 | - | - | - | - | 67 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 256 | 1731378138399 | 2024-11-12 10:22:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a61c7cfd5d0b4fd8b2b7a489c93e2e2f | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378118374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8525, jvmUsedMemory=0.6613, jvmMaxMemory=3.5557, jvmMemoryUsage=0.186, diskUsed=223.7142, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 256 | 1731378138400 | 2024-11-12 10:22:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a61c7cfd5d0b4fd8b2b7a489c93e2e2f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378128371, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6411, jvmUsedMemory=0.6746, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1897, diskUsed=223.7147, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 256 | 1731378138400 | 2024-11-12 10:22:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a61c7cfd5d0b4fd8b2b7a489c93e2e2f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378138371, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3945, jvmUsedMemory=0.6819, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1918, diskUsed=223.7159, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 256 | 1731378138416 | 2024-11-12 10:22:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a61c7cfd5d0b4fd8b2b7a489c93e2e2f | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 256 | 1731378138417 | 2024-11-12 10:22:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a61c7cfd5d0b4fd8b2b7a489c93e2e2f | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 297 | 1731378168392 | 2024-11-12 10:22:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0793449bde934a2a8ecfb291d1114b88 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378148370, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1797, jvmUsedMemory=0.6951, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1955, diskUsed=223.7161, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 297 | 1731378168394 | 2024-11-12 10:22:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0793449bde934a2a8ecfb291d1114b88 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378158373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2988, jvmUsedMemory=0.7104, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1998, diskUsed=223.7172, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 297 | 1731378168395 | 2024-11-12 10:22:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0793449bde934a2a8ecfb291d1114b88 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378168373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.252, jvmUsedMemory=0.717, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2016, diskUsed=223.7175, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 297 | 1731378168413 | 2024-11-12 10:22:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0793449bde934a2a8ecfb291d1114b88 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 297 | 1731378168413 | 2024-11-12 10:22:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0793449bde934a2a8ecfb291d1114b88 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 378 | 1731378198402 | 2024-11-12 10:23:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e6b0f1c71ab24fb0841bc38a4fa6a221 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378178375, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5874, jvmUsedMemory=0.7283, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2048, diskUsed=223.715, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 378 | 1731378198405 | 2024-11-12 10:23:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e6b0f1c71ab24fb0841bc38a4fa6a221 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378188375, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7305, jvmUsedMemory=0.7382, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2076, diskUsed=223.715, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 378 | 1731378198405 | 2024-11-12 10:23:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e6b0f1c71ab24fb0841bc38a4fa6a221 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378198376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7109, jvmUsedMemory=0.7455, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2097, diskUsed=223.7152, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 342 | 1731378198416 | 2024-11-12 10:23:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 66f02c13dd6240b3a8413ff1b6076403 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 342 | 1731378198416 | 2024-11-12 10:23:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 66f02c13dd6240b3a8413ff1b6076403 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 378 | 1731378228403 | 2024-11-12 10:23:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | e6b0f1c71ab24fb0841bc38a4fa6a221 | - | - | - | - | 30000 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378208371, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4541, jvmUsedMemory=0.7569, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2129, diskUsed=223.7163, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 378 | 1731378228404 | 2024-11-12 10:23:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | e6b0f1c71ab24fb0841bc38a4fa6a221 | - | - | - | - | 30001 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378218375, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6851, jvmUsedMemory=0.7665, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2156, diskUsed=223.7163, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 378 | 1731378228405 | 2024-11-12 10:23:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | Actor | aroundReceive | e6b0f1c71ab24fb0841bc38a4fa6a221 | - | - | - | - | 30001 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378228373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2134, jvmUsedMemory=0.7731, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2174, diskUsed=223.7164, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 378 | 1731378228423 | 2024-11-12 10:23:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | e6b0f1c71ab24fb0841bc38a4fa6a221 | - | - | - | - | 30020 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 378 | 1731378228424 | 2024-11-12 10:23:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | e6b0f1c71ab24fb0841bc38a4fa6a221 | - | - | - | - | 30020 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 416 | 1731378258391 | 2024-11-12 10:24:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 4c59e86f8d794ed3b6f770fe14aafeb2 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378238372, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9463, jvmUsedMemory=0.7844, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2206, diskUsed=223.7165, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 416 | 1731378258392 | 2024-11-12 10:24:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 4c59e86f8d794ed3b6f770fe14aafeb2 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378248373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9541, jvmUsedMemory=0.7957, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2238, diskUsed=223.7157, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 416 | 1731378258392 | 2024-11-12 10:24:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 4c59e86f8d794ed3b6f770fe14aafeb2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378258376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9541, jvmUsedMemory=0.8037, jvmMaxMemory=3.5557, jvmMemoryUsage=0.226, diskUsed=223.7172, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 416 | 1731378258405 | 2024-11-12 10:24:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 4c59e86f8d794ed3b6f770fe14aafeb2 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 416 | 1731378258405 | 2024-11-12 10:24:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 4c59e86f8d794ed3b6f770fe14aafeb2 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 457 | 1731378288390 | 2024-11-12 10:24:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 75882c1be7ec4b39965f96417dc27080 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378268372, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5732, jvmUsedMemory=0.815, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2292, diskUsed=223.7176, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 457 | 1731378288397 | 2024-11-12 10:24:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 75882c1be7ec4b39965f96417dc27080 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378278374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3306, jvmUsedMemory=0.8236, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2316, diskUsed=223.715, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 457 | 1731378288397 | 2024-11-12 10:24:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 75882c1be7ec4b39965f96417dc27080 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378288373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4331, jvmUsedMemory=0.8309, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2337, diskUsed=223.7151, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 457 | 1731378288413 | 2024-11-12 10:24:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 75882c1be7ec4b39965f96417dc27080 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 457 | 1731378288413 | 2024-11-12 10:24:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 75882c1be7ec4b39965f96417dc27080 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-53 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 495 | 1731378318410 | 2024-11-12 10:25:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | decf49dac7474e4fb7a7b9935664b119 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378298374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9263, jvmUsedMemory=0.8409, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2365, diskUsed=223.7152, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 495 | 1731378318411 | 2024-11-12 10:25:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | decf49dac7474e4fb7a7b9935664b119 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378308376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5498, jvmUsedMemory=0.8495, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2389, diskUsed=223.7156, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 495 | 1731378318412 | 2024-11-12 10:25:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | decf49dac7474e4fb7a7b9935664b119 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378318373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7178, jvmUsedMemory=0.8573, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2411, diskUsed=223.7148, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 495 | 1731378318427 | 2024-11-12 10:25:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | decf49dac7474e4fb7a7b9935664b119 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 495 | 1731378318428 | 2024-11-12 10:25:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | decf49dac7474e4fb7a7b9935664b119 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 533 | 1731378348401 | 2024-11-12 10:25:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6841aa48143b4c1cb96678ed7a046dfd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378328376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6807, jvmUsedMemory=0.8673, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2439, diskUsed=223.716, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 533 | 1731378348404 | 2024-11-12 10:25:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6841aa48143b4c1cb96678ed7a046dfd | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378338376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2681, jvmUsedMemory=0.8779, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2469, diskUsed=223.7161, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 533 | 1731378348405 | 2024-11-12 10:25:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6841aa48143b4c1cb96678ed7a046dfd | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378348374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7007, jvmUsedMemory=0.8852, jvmMaxMemory=3.5557, jvmMemoryUsage=0.249, diskUsed=223.7166, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 533 | 1731378348422 | 2024-11-12 10:25:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 6841aa48143b4c1cb96678ed7a046dfd | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 533 | 1731378348428 | 2024-11-12 10:25:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 6841aa48143b4c1cb96678ed7a046dfd | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 531 | 1731378378401 | 2024-11-12 10:26:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 2aaeafd397e442439f4c06f4b1ad1e14 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378358374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2813, jvmUsedMemory=0.8931, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2512, diskUsed=223.718, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 531 | 1731378378402 | 2024-11-12 10:26:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 2aaeafd397e442439f4c06f4b1ad1e14 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378368372, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5513, jvmUsedMemory=0.9024, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2538, diskUsed=223.719, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 531 | 1731378378403 | 2024-11-12 10:26:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 2aaeafd397e442439f4c06f4b1ad1e14 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378378372, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3057, jvmUsedMemory=0.911, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2562, diskUsed=223.7192, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 531 | 1731378378416 | 2024-11-12 10:26:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 2aaeafd397e442439f4c06f4b1ad1e14 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 531 | 1731378378417 | 2024-11-12 10:26:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 2aaeafd397e442439f4c06f4b1ad1e14 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-59 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 607 | 1731378408402 | 2024-11-12 10:26:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a189dcf8a9fc4c6faccf24d84f22aa62 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378388376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7969, jvmUsedMemory=0.9224, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2594, diskUsed=223.7202, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 607 | 1731378408404 | 2024-11-12 10:26:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a189dcf8a9fc4c6faccf24d84f22aa62 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378398377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0293, jvmUsedMemory=0.9336, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2626, diskUsed=223.7212, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 607 | 1731378408405 | 2024-11-12 10:26:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a189dcf8a9fc4c6faccf24d84f22aa62 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378408377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6367, jvmUsedMemory=0.9409, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2646, diskUsed=223.7213, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 607 | 1731378408422 | 2024-11-12 10:26:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a189dcf8a9fc4c6faccf24d84f22aa62 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 607 | 1731378408422 | 2024-11-12 10:26:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a189dcf8a9fc4c6faccf24d84f22aa62 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-64 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 606 | 1731378438403 | 2024-11-12 10:27:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | eb13e2ab7a74485793222dc9be68a3d6 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378418373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5317, jvmUsedMemory=0.9496, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2671, diskUsed=223.7223, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 606 | 1731378438407 | 2024-11-12 10:27:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | eb13e2ab7a74485793222dc9be68a3d6 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378428376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2222, jvmUsedMemory=0.9588, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2697, diskUsed=223.7223, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 606 | 1731378438407 | 2024-11-12 10:27:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | eb13e2ab7a74485793222dc9be68a3d6 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378438377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4888, jvmUsedMemory=0.9661, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2717, diskUsed=223.7214, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 606 | 1731378438429 | 2024-11-12 10:27:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | eb13e2ab7a74485793222dc9be68a3d6 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 606 | 1731378438430 | 2024-11-12 10:27:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | eb13e2ab7a74485793222dc9be68a3d6 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 681 | 1731378468412 | 2024-11-12 10:27:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 1689277a6e22492293a0dbfb665837a5 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378448377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2529, jvmUsedMemory=0.9754, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2743, diskUsed=223.7234, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 681 | 1731378468425 | 2024-11-12 10:27:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 1689277a6e22492293a0dbfb665837a5 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378458377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2202, jvmUsedMemory=0.9834, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2766, diskUsed=223.7234, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 681 | 1731378468426 | 2024-11-12 10:27:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 1689277a6e22492293a0dbfb665837a5 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378468373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7881, jvmUsedMemory=0.99, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2784, diskUsed=223.7234, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 681 | 1731378468445 | 2024-11-12 10:27:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 1689277a6e22492293a0dbfb665837a5 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 681 | 1731378468445 | 2024-11-12 10:27:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 1689277a6e22492293a0dbfb665837a5 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-68 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 714 | 1731378498401 | 2024-11-12 10:28:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0cf6fa2fbb844fc4a4f46b06f6f17dba | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378478373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7593, jvmUsedMemory=0.9994, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2811, diskUsed=223.7235, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 714 | 1731378498404 | 2024-11-12 10:28:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0cf6fa2fbb844fc4a4f46b06f6f17dba | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378488374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4146, jvmUsedMemory=1.008, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2835, diskUsed=223.7245, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 714 | 1731378498406 | 2024-11-12 10:28:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0cf6fa2fbb844fc4a4f46b06f6f17dba | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378498377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5044, jvmUsedMemory=1.0153, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2855, diskUsed=223.7246, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 714 | 1731378498429 | 2024-11-12 10:28:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0cf6fa2fbb844fc4a4f46b06f6f17dba | - | - | - | - | 27 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 714 | 1731378498429 | 2024-11-12 10:28:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0cf6fa2fbb844fc4a4f46b06f6f17dba | - | - | - | - | 28 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 782 | 1731378528401 | 2024-11-12 10:28:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | dda6976202bc47cd8d95b4ee49dfa0ec | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378508374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.147, jvmUsedMemory=1.0259, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2885, diskUsed=223.7247, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 782 | 1731378528403 | 2024-11-12 10:28:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | dda6976202bc47cd8d95b4ee49dfa0ec | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378518374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9634, jvmUsedMemory=1.0371, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2917, diskUsed=223.721, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 782 | 1731378528403 | 2024-11-12 10:28:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | dda6976202bc47cd8d95b4ee49dfa0ec | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378528375, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8086, jvmUsedMemory=1.0437, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2935, diskUsed=223.7223, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 782 | 1731378528422 | 2024-11-12 10:28:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | dda6976202bc47cd8d95b4ee49dfa0ec | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 782 | 1731378528422 | 2024-11-12 10:28:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | dda6976202bc47cd8d95b4ee49dfa0ec | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-76 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 715 | 1731378558402 | 2024-11-12 10:29:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 27d428c4d39247fdba0018091cdd75f4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378538374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4941, jvmUsedMemory=1.0689, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3006, diskUsed=223.7245, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 715 | 1731378558403 | 2024-11-12 10:29:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 27d428c4d39247fdba0018091cdd75f4 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378548373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2573, jvmUsedMemory=1.0768, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3028, diskUsed=223.7246, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 715 | 1731378558404 | 2024-11-12 10:29:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 27d428c4d39247fdba0018091cdd75f4 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378558375, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1372, jvmUsedMemory=1.0841, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3049, diskUsed=223.7249, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 715 | 1731378558412 | 2024-11-12 10:29:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 27d428c4d39247fdba0018091cdd75f4 | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 715 | 1731378558413 | 2024-11-12 10:29:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 27d428c4d39247fdba0018091cdd75f4 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-72 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 816 | 1731378588401 | 2024-11-12 10:29:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 5088b3c7bda3483fa5631a7bde8e6bcf | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378568376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9619, jvmUsedMemory=1.0941, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3077, diskUsed=223.7264, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 816 | 1731378588403 | 2024-11-12 10:29:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 5088b3c7bda3483fa5631a7bde8e6bcf | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378578375, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8872, jvmUsedMemory=1.1027, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3101, diskUsed=223.7267, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 816 | 1731378588404 | 2024-11-12 10:29:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 5088b3c7bda3483fa5631a7bde8e6bcf | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378588377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1509, jvmUsedMemory=1.11, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3122, diskUsed=223.7268, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 816 | 1731378588424 | 2024-11-12 10:29:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 5088b3c7bda3483fa5631a7bde8e6bcf | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 816 | 1731378588424 | 2024-11-12 10:29:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 5088b3c7bda3483fa5631a7bde8e6bcf | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-77 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 817 | 1731378618402 | 2024-11-12 10:30:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 82eceb04b0b54275985f8429b577b576 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378598376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3354, jvmUsedMemory=1.12, jvmMaxMemory=3.5557, jvmMemoryUsage=0.315, diskUsed=223.7268, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 817 | 1731378618403 | 2024-11-12 10:30:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 82eceb04b0b54275985f8429b577b576 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378608378, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1357, jvmUsedMemory=1.1306, jvmMaxMemory=3.5557, jvmMemoryUsage=0.318, diskUsed=223.7288, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 817 | 1731378618404 | 2024-11-12 10:30:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 82eceb04b0b54275985f8429b577b576 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378618373, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7266, jvmUsedMemory=1.1379, jvmMaxMemory=3.5557, jvmMemoryUsage=0.32, diskUsed=223.7298, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 817 | 1731378618424 | 2024-11-12 10:30:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 82eceb04b0b54275985f8429b577b576 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 817 | 1731378618424 | 2024-11-12 10:30:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 82eceb04b0b54275985f8429b577b576 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 920 | 1731378648392 | 2024-11-12 10:30:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8c54b9d9a8614b3b8038345f485ef2f1 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378628374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3066, jvmUsedMemory=1.1465, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3224, diskUsed=223.7298, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 920 | 1731378648400 | 2024-11-12 10:30:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8c54b9d9a8614b3b8038345f485ef2f1 | - | - | - | - | 7 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378638376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6528, jvmUsedMemory=1.1545, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3247, diskUsed=223.73, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 920 | 1731378648400 | 2024-11-12 10:30:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8c54b9d9a8614b3b8038345f485ef2f1 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378648376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0132, jvmUsedMemory=1.1611, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3265, diskUsed=223.73, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 920 | 1731378648416 | 2024-11-12 10:30:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8c54b9d9a8614b3b8038345f485ef2f1 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 920 | 1731378648417 | 2024-11-12 10:30:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8c54b9d9a8614b3b8038345f485ef2f1 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-84 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 887 | 1731378678411 | 2024-11-12 10:31:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | cf97a464888b4ae7aaafcbb106998f42 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378658376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.937, jvmUsedMemory=1.1724, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3297, diskUsed=223.7299, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 887 | 1731378678418 | 2024-11-12 10:31:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | cf97a464888b4ae7aaafcbb106998f42 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378668376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1128, jvmUsedMemory=1.1817, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3323, diskUsed=223.7292, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 887 | 1731378678418 | 2024-11-12 10:31:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | cf97a464888b4ae7aaafcbb106998f42 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378678376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0151, jvmUsedMemory=1.1883, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3342, diskUsed=223.7293, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 887 | 1731378678448 | 2024-11-12 10:31:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | cf97a464888b4ae7aaafcbb106998f42 | - | - | - | - | 32 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 887 | 1731378678449 | 2024-11-12 10:31:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | cf97a464888b4ae7aaafcbb106998f42 | - | - | - | - | 34 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 956 | 1731378708402 | 2024-11-12 10:31:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | bcf89550d07a4245bb270bf3e17ace30 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378688377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0796, jvmUsedMemory=1.1995, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3374, diskUsed=223.7302, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 956 | 1731378708405 | 2024-11-12 10:31:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | bcf89550d07a4245bb270bf3e17ace30 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378698374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6338, jvmUsedMemory=1.2068, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3394, diskUsed=223.7302, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 956 | 1731378708406 | 2024-11-12 10:31:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | bcf89550d07a4245bb270bf3e17ace30 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378708376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2959, jvmUsedMemory=1.2134, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3413, diskUsed=223.7302, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 956 | 1731378708421 | 2024-11-12 10:31:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | bcf89550d07a4245bb270bf3e17ace30 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 956 | 1731378708421 | 2024-11-12 10:31:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | bcf89550d07a4245bb270bf3e17ace30 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 957 | 1731378738404 | 2024-11-12 10:32:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0512852c0e4948d299b76f3aa4a686e9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378718379, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8687, jvmUsedMemory=1.2239, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3442, diskUsed=223.7313, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 957 | 1731378738411 | 2024-11-12 10:32:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0512852c0e4948d299b76f3aa4a686e9 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378728379, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2759, jvmUsedMemory=1.241, jvmMaxMemory=3.5557, jvmMemoryUsage=0.349, diskUsed=223.7308, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 957 | 1731378738411 | 2024-11-12 10:32:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0512852c0e4948d299b76f3aa4a686e9 | - | - | - | - | 8 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378738376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3003, jvmUsedMemory=1.2476, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3509, diskUsed=223.732, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 957 | 1731378738430 | 2024-11-12 10:32:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0512852c0e4948d299b76f3aa4a686e9 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 957 | 1731378738430 | 2024-11-12 10:32:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0512852c0e4948d299b76f3aa4a686e9 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1024 | 1731378768403 | 2024-11-12 10:32:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | c69c14eccff14dffadc11b78c4d6ed05 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378748376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3467, jvmUsedMemory=1.2695, jvmMaxMemory=3.5557, jvmMemoryUsage=0.357, diskUsed=223.7334, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1024 | 1731378768414 | 2024-11-12 10:32:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | c69c14eccff14dffadc11b78c4d6ed05 | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378758376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1328, jvmUsedMemory=1.2794, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3598, diskUsed=223.7336, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1024 | 1731378768415 | 2024-11-12 10:32:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | c69c14eccff14dffadc11b78c4d6ed05 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378768375, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9648, jvmUsedMemory=1.2861, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3617, diskUsed=223.7337, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1024 | 1731378768427 | 2024-11-12 10:32:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | c69c14eccff14dffadc11b78c4d6ed05 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1024 | 1731378768427 | 2024-11-12 10:32:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | c69c14eccff14dffadc11b78c4d6ed05 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-91 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1058 | 1731378798405 | 2024-11-12 10:33:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f8ab8bcb2fdb4a9dbf56d06b65db9cf8 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378778376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9023, jvmUsedMemory=1.2961, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3645, diskUsed=223.7339, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1058 | 1731378798407 | 2024-11-12 10:33:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f8ab8bcb2fdb4a9dbf56d06b65db9cf8 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378788379, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5063, jvmUsedMemory=1.3047, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3669, diskUsed=223.7293, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1058 | 1731378798408 | 2024-11-12 10:33:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f8ab8bcb2fdb4a9dbf56d06b65db9cf8 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378798377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2803, jvmUsedMemory=0.1756, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0494, diskUsed=223.7296, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1058 | 1731378798422 | 2024-11-12 10:33:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f8ab8bcb2fdb4a9dbf56d06b65db9cf8 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1058 | 1731378798422 | 2024-11-12 10:33:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f8ab8bcb2fdb4a9dbf56d06b65db9cf8 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1128 | 1731378828406 | 2024-11-12 10:33:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7caf31af0ddd4f789ce237a276d794a2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378808379, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9966, jvmUsedMemory=0.184, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0518, diskUsed=223.7309, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1128 | 1731378828407 | 2024-11-12 10:33:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 7caf31af0ddd4f789ce237a276d794a2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378818374, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7627, jvmUsedMemory=0.1918, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0539, diskUsed=223.7321, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1128 | 1731378828407 | 2024-11-12 10:33:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 7caf31af0ddd4f789ce237a276d794a2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378828378, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6382, jvmUsedMemory=0.1951, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0549, diskUsed=223.7333, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1128 | 1731378828421 | 2024-11-12 10:33:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 7caf31af0ddd4f789ce237a276d794a2 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1128 | 1731378828422 | 2024-11-12 10:33:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 7caf31af0ddd4f789ce237a276d794a2 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-98 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1129 | 1731378858403 | 2024-11-12 10:34:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ad6721c758e24803a2e5cec7acdcc1c0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-99 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378838377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4595, jvmUsedMemory=0.2009, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0565, diskUsed=223.7343, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1129 | 1731378858404 | 2024-11-12 10:34:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ad6721c758e24803a2e5cec7acdcc1c0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-99 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378848378, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3145, jvmUsedMemory=0.207, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0582, diskUsed=223.7417, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1129 | 1731378858405 | 2024-11-12 10:34:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ad6721c758e24803a2e5cec7acdcc1c0 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-99 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378858379, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5669, jvmUsedMemory=0.2103, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0591, diskUsed=223.7427, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1129 | 1731378858419 | 2024-11-12 10:34:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | ad6721c758e24803a2e5cec7acdcc1c0 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-99 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1129 | 1731378858419 | 2024-11-12 10:34:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | ad6721c758e24803a2e5cec7acdcc1c0 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-99 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 155 | 1731378887737 | 2024-11-12 10:34:47 | v2/ThreadPoolExecutor$Worker/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor$Worker | run | 86ec7cf4a4e5447d8bdce4448656c96a | - | - | - | - | 1 | 0 | - | - | - | - | HikariPool-1 housekeeper c.z.h.p.HikariPool HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=49s180ms).

warning | 1094 | 1731378888392 | 2024-11-12 10:34:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | a3bbc57b787c4daca8ffba76e3698500 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378868375, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5532, jvmUsedMemory=0.2166, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0609, diskUsed=223.7453, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1094 | 1731378888396 | 2024-11-12 10:34:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | a3bbc57b787c4daca8ffba76e3698500 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378887735, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7534, jvmUsedMemory=0.2191, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0616, diskUsed=223.7453, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1094 | 1731378888397 | 2024-11-12 10:34:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | a3bbc57b787c4daca8ffba76e3698500 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378888378, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7534, jvmUsedMemory=0.2268, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0638, diskUsed=223.7453, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1094 | 1731378888405 | 2024-11-12 10:34:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | a3bbc57b787c4daca8ffba76e3698500 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1094 | 1731378888405 | 2024-11-12 10:34:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | a3bbc57b787c4daca8ffba76e3698500 | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1187 | 1731378918394 | 2024-11-12 10:35:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0929229b837b400a95997c18633be36c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-104 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378898376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4033, jvmUsedMemory=0.2354, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0662, diskUsed=223.7453, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1187 | 1731378918400 | 2024-11-12 10:35:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0929229b837b400a95997c18633be36c | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-104 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378908377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7891, jvmUsedMemory=0.2408, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0677, diskUsed=223.7443, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1225 | 1731378918414 | 2024-11-12 10:35:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 5e8394669f564a86bfe00992eb1052cd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1225 | 1731378918419 | 2024-11-12 10:35:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 5e8394669f564a86bfe00992eb1052cd | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1187 | 1731378918419 | 2024-11-12 10:35:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0929229b837b400a95997c18633be36c | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-104 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378918375, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8208, jvmUsedMemory=0.2441, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0686, diskUsed=223.7453, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1226 | 1731378948406 | 2024-11-12 10:35:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 5ea71315dbfb4ea6b49f908d63b56155 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-108 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378928378, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1616, jvmUsedMemory=0.2502, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0704, diskUsed=223.7474, diskTotal=460.4317, diskUsage=0.486, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1226 | 1731378948421 | 2024-11-12 10:35:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 5ea71315dbfb4ea6b49f908d63b56155 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-108 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378938375, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9023, jvmUsedMemory=0.2564, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0721, diskUsed=223.7474, diskTotal=460.4317, diskUsage=0.486, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1226 | 1731378948426 | 2024-11-12 10:35:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 5ea71315dbfb4ea6b49f908d63b56155 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-108 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378948377, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.77, jvmUsedMemory=0.2591, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0729, diskUsed=223.749, diskTotal=460.4317, diskUsage=0.486, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1226 | 1731378948438 | 2024-11-12 10:35:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 5ea71315dbfb4ea6b49f908d63b56155 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-108 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1226 | 1731378948439 | 2024-11-12 10:35:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 5ea71315dbfb4ea6b49f908d63b56155 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-108 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1227 | 1731378978406 | 2024-11-12 10:36:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6244632d5e80483eae14a56a4c6f39d5 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378958380, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2051, jvmUsedMemory=0.2661, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0748, diskUsed=223.7491, diskTotal=460.4317, diskUsage=0.486, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1227 | 1731378978430 | 2024-11-12 10:36:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6244632d5e80483eae14a56a4c6f39d5 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378968380, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9395, jvmUsedMemory=0.2764, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0777, diskUsed=223.7502, diskTotal=460.4317, diskUsage=0.486, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1227 | 1731378978430 | 2024-11-12 10:36:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6244632d5e80483eae14a56a4c6f39d5 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378978380, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8008, jvmUsedMemory=0.2797, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0787, diskUsed=223.7502, diskTotal=460.4317, diskUsage=0.486, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1227 | 1731378978438 | 2024-11-12 10:36:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 6244632d5e80483eae14a56a4c6f39d5 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1227 | 1731378978439 | 2024-11-12 10:36:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 6244632d5e80483eae14a56a4c6f39d5 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-109 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1291 | 1731379008403 | 2024-11-12 10:36:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3c5c937b54f84ceeb930ff46f7d56623 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378988376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1704, jvmUsedMemory=0.2876, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0809, diskUsed=223.7409, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1291 | 1731379008408 | 2024-11-12 10:36:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3c5c937b54f84ceeb930ff46f7d56623 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731378998378, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9165, jvmUsedMemory=0.2932, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0825, diskUsed=223.7419, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1291 | 1731379008408 | 2024-11-12 10:36:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3c5c937b54f84ceeb930ff46f7d56623 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379008380, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1304, jvmUsedMemory=0.2969, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0835, diskUsed=223.7421, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1291 | 1731379008418 | 2024-11-12 10:36:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3c5c937b54f84ceeb930ff46f7d56623 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1291 | 1731379008418 | 2024-11-12 10:36:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3c5c937b54f84ceeb930ff46f7d56623 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-113 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1370 | 1731379049552 | 2024-11-12 10:37:29 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 1b550688eeea4368bf41b577576a05aa | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-123 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379018376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2114, jvmUsedMemory=0.3028, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0852, diskUsed=223.7423, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1370 | 1731379049558 | 2024-11-12 10:37:29 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 1b550688eeea4368bf41b577576a05aa | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-123 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379028381, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.2715, jvmUsedMemory=0.314, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0883, diskUsed=223.7413, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1371 | 1731379049573 | 2024-11-12 10:37:29 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 84836ec6e77e4a81b56198c2666be8d2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1371 | 1731379049574 | 2024-11-12 10:37:29 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 84836ec6e77e4a81b56198c2666be8d2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-124 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1327 | 1731379078405 | 2024-11-12 10:37:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | fce8c943ca42418e941dd3fec5258f98 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-115 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379058381, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.3354, jvmUsedMemory=0.3655, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1028, diskUsed=223.7411, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1327 | 1731379078407 | 2024-11-12 10:37:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | fce8c943ca42418e941dd3fec5258f98 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-115 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379068382, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.043, jvmUsedMemory=0.3692, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1038, diskUsed=223.7423, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1327 | 1731379078407 | 2024-11-12 10:37:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | fce8c943ca42418e941dd3fec5258f98 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-115 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379078380, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.6611, jvmUsedMemory=0.374, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1052, diskUsed=223.7424, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1327 | 1731379078424 | 2024-11-12 10:37:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | fce8c943ca42418e941dd3fec5258f98 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-115 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1327 | 1731379078424 | 2024-11-12 10:37:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | fce8c943ca42418e941dd3fec5258f98 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-115 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1407 | 1731379108406 | 2024-11-12 10:38:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 766091a2a913485a85690026f10bcaab | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379088381, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1777, jvmUsedMemory=0.3812, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1072, diskUsed=223.7424, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1407 | 1731379108407 | 2024-11-12 10:38:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 766091a2a913485a85690026f10bcaab | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379098376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6885, jvmUsedMemory=0.3849, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1083, diskUsed=223.7424, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1407 | 1731379108407 | 2024-11-12 10:38:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 766091a2a913485a85690026f10bcaab | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379108378, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7427, jvmUsedMemory=0.3882, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1092, diskUsed=223.7435, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1407 | 1731379108422 | 2024-11-12 10:38:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 766091a2a913485a85690026f10bcaab | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1407 | 1731379108423 | 2024-11-12 10:38:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 766091a2a913485a85690026f10bcaab | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-126 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1442 | 1731379138396 | 2024-11-12 10:38:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 0dff5a94b695410186b844fa0277a3d3 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379118376, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7148, jvmUsedMemory=0.3969, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1116, diskUsed=223.7433, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1442 | 1731379138398 | 2024-11-12 10:38:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 0dff5a94b695410186b844fa0277a3d3 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379128381, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4209, jvmUsedMemory=0.4002, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1126, diskUsed=223.7443, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1442 | 1731379138398 | 2024-11-12 10:38:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 0dff5a94b695410186b844fa0277a3d3 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379138380, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0825, jvmUsedMemory=0.4039, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1136, diskUsed=223.7433, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1442 | 1731379138417 | 2024-11-12 10:38:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 0dff5a94b695410186b844fa0277a3d3 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1442 | 1731379138418 | 2024-11-12 10:38:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 0dff5a94b695410186b844fa0277a3d3 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-129 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1443 | 1731379179225 | 2024-11-12 10:39:39 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | ef65ca99253c4719ba2c933efd5411d5 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-130 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379148381, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5347, jvmUsedMemory=0.4173, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1174, diskUsed=223.7437, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1443 | 1731379179226 | 2024-11-12 10:39:39 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | ef65ca99253c4719ba2c933efd5411d5 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-130 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379160904, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1885, jvmUsedMemory=0.4191, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1179, diskUsed=223.7456, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1504 | 1731379179242 | 2024-11-12 10:39:39 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | a7e5edac898d4563be45c1348afbaf7c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-139 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1504 | 1731379179242 | 2024-11-12 10:39:39 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | a7e5edac898d4563be45c1348afbaf7c | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-139 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1443 | 1731379225371 | 2024-11-12 10:40:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | ef65ca99253c4719ba2c933efd5411d5 | - | - | - | - | 46147 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-130 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379199811, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2827, jvmUsedMemory=0.4308, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1212, diskUsed=223.7458, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1443 | 1731379225373 | 2024-11-12 10:40:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | Actor | aroundReceive | ef65ca99253c4719ba2c933efd5411d5 | - | - | - | - | 46148 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-130 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379199826, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2827, jvmUsedMemory=0.4332, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1218, diskUsed=223.7458, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1443 | 1731379225374 | 2024-11-12 10:40:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | Actor | aroundReceive | ef65ca99253c4719ba2c933efd5411d5 | - | - | - | - | 46149 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-130 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379209118, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4053, jvmUsedMemory=0.4417, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1242, diskUsed=223.7461, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1443 | 1731379225389 | 2024-11-12 10:40:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | ef65ca99253c4719ba2c933efd5411d5 | - | - | - | - | 46164 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-130 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1443 | 1731379225389 | 2024-11-12 10:40:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | ef65ca99253c4719ba2c933efd5411d5 | - | - | - | - | 46164 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-130 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1503 | 1731379248373 | 2024-11-12 10:40:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 6e0465150a5e425389e655f569aa0d7e | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-138 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379228359, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0049, jvmUsedMemory=0.4471, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1257, diskUsed=223.7452, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1503 | 1731379248374 | 2024-11-12 10:40:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 6e0465150a5e425389e655f569aa0d7e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-138 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379238361, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6226, jvmUsedMemory=0.4515, jvmMaxMemory=3.5557, jvmMemoryUsage=0.127, diskUsed=223.7454, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1503 | 1731379248375 | 2024-11-12 10:40:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 6e0465150a5e425389e655f569aa0d7e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-138 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379248360, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2925, jvmUsedMemory=0.4548, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1279, diskUsed=223.7454, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1503 | 1731379248393 | 2024-11-12 10:40:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 6e0465150a5e425389e655f569aa0d7e | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-138 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1503 | 1731379248393 | 2024-11-12 10:40:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 6e0465150a5e425389e655f569aa0d7e | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-138 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1536 | 1731379278384 | 2024-11-12 10:41:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 8dff908b046948d2a1605f4d70dfc19b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-146 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379258359, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3145, jvmUsedMemory=0.4602, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1294, diskUsed=223.7446, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1536 | 1731379278385 | 2024-11-12 10:41:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 8dff908b046948d2a1605f4d70dfc19b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-146 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379268358, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.958, jvmUsedMemory=0.4659, jvmMaxMemory=3.5557, jvmMemoryUsage=0.131, diskUsed=223.7446, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1536 | 1731379278385 | 2024-11-12 10:41:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 8dff908b046948d2a1605f4d70dfc19b | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-146 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379278354, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.105, jvmUsedMemory=0.4707, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1324, diskUsed=223.7448, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1536 | 1731379278400 | 2024-11-12 10:41:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 8dff908b046948d2a1605f4d70dfc19b | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-146 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1536 | 1731379278400 | 2024-11-12 10:41:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 8dff908b046948d2a1605f4d70dfc19b | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-146 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1600 | 1731379308383 | 2024-11-12 10:41:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | f9e6bfcf62594ec7978e660b05f74d08 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-151 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379288356, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1011, jvmUsedMemory=0.4783, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1345, diskUsed=223.7431, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1600 | 1731379308385 | 2024-11-12 10:41:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | f9e6bfcf62594ec7978e660b05f74d08 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-151 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379298357, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8511, jvmUsedMemory=0.4859, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1367, diskUsed=223.7441, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1600 | 1731379308385 | 2024-11-12 10:41:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | f9e6bfcf62594ec7978e660b05f74d08 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-151 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379308358, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6396, jvmUsedMemory=0.4896, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1377, diskUsed=223.745, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1600 | 1731379308403 | 2024-11-12 10:41:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | f9e6bfcf62594ec7978e660b05f74d08 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-151 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1600 | 1731379308403 | 2024-11-12 10:41:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | f9e6bfcf62594ec7978e660b05f74d08 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-151 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1598 | 1731379338383 | 2024-11-12 10:42:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | e0c2edcf679e4a7b937bc22635e00d3f | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-149 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379318356, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9287, jvmUsedMemory=0.4943, jvmMaxMemory=3.5557, jvmMemoryUsage=0.139, diskUsed=223.745, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1598 | 1731379338385 | 2024-11-12 10:42:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | e0c2edcf679e4a7b937bc22635e00d3f | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-149 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379328354, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9863, jvmUsedMemory=0.5, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1406, diskUsed=223.7462, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1598 | 1731379338386 | 2024-11-12 10:42:18 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | e0c2edcf679e4a7b937bc22635e00d3f | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-149 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379338354, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5679, jvmUsedMemory=0.5037, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1416, diskUsed=223.7466, diskTotal=460.4317, diskUsage=0.4859, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1598 | 1731379338404 | 2024-11-12 10:42:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | e0c2edcf679e4a7b937bc22635e00d3f | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-149 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1598 | 1731379338404 | 2024-11-12 10:42:18 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | e0c2edcf679e4a7b937bc22635e00d3f | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-149 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1668 | 1731379368383 | 2024-11-12 10:42:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 3b114066a6fe4607954ce6d399af5eba | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-153 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379348358, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5474, jvmUsedMemory=0.5156, jvmMaxMemory=3.5557, jvmMemoryUsage=0.145, diskUsed=223.7469, diskTotal=460.4317, diskUsage=0.486, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1668 | 1731379368384 | 2024-11-12 10:42:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 3b114066a6fe4607954ce6d399af5eba | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-153 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379358357, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0015, jvmUsedMemory=0.5206, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1464, diskUsed=223.7472, diskTotal=460.4317, diskUsage=0.486, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1668 | 1731379368384 | 2024-11-12 10:42:48 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 3b114066a6fe4607954ce6d399af5eba | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-153 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379368358, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0806, jvmUsedMemory=0.5256, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1478, diskUsed=223.7483, diskTotal=460.4317, diskUsage=0.486, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1668 | 1731379368394 | 2024-11-12 10:42:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 3b114066a6fe4607954ce6d399af5eba | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-153 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1668 | 1731379368394 | 2024-11-12 10:42:48 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 3b114066a6fe4607954ce6d399af5eba | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-153 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 155 | 1731379525195 | 2024-11-12 10:45:25 | v2/ThreadPoolExecutor$Worker/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor$Worker | run | 86ec7cf4a4e5447d8bdce4448656c96a | - | - | - | - | 637493 | 0 | - | - | - | - | HikariPool-1 housekeeper c.z.h.p.HikariPool HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m45s966ms).

warning | 1670 | 1731379525246 | 2024-11-12 10:45:25 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 7e0344f8789b43d58e5c329fb91b0245 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-155 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731379378358, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6865, jvmUsedMemory=0.5307, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1492, diskUsed=223.7493, diskTotal=460.4317, diskUsage=0.486, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 44 | 1731379525305 | 2024-11-12 10:45:25 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | a116df84d5324aae8bd209816d9f479d | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 1670 | 1731379525305 | 2024-11-12 10:45:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | 7e0344f8789b43d58e5c329fb91b0245 | - | - | - | - | 57 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-155 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 44 | 1731379525306 | 2024-11-12 10:45:25 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | a116df84d5324aae8bd209816d9f479d | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 1670 | 1731379525306 | 2024-11-12 10:45:25 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | 7e0344f8789b43d58e5c329fb91b0245 | - | - | - | - | 58 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-155 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1731379525306 | 2024-11-12 10:45:25 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 258fbe7a58354b869f9a7ed03ce17086 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 40 | 1731379525314 | 2024-11-12 10:45:25 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 258fbe7a58354b869f9a7ed03ce17086 | - | - | - | - | 8 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 123 | 1731389699696 | 2024-11-12 13:34:59 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ActorCell | receiveMessage | cbd090521c994dac87217ce5d9ec3c5c | - | - | - | - | 122 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 123 | 1731389699697 | 2024-11-12 13:34:59 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ActorCell | receiveMessage | cbd090521c994dac87217ce5d9ec3c5c | - | - | - | - | 122 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 269 | 1731389720537 | 2024-11-12 13:35:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | edfebeab29cf46ad886a4c41aa52c07b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731389700389, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7129, jvmUsedMemory=0.2408, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0677, diskUsed=222.7016, diskTotal=460.4317, diskUsage=0.4837, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1731389720547 | 2024-11-12 13:35:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | edfebeab29cf46ad886a4c41aa52c07b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731389710391, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6958, jvmUsedMemory=0.3845, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1081, diskUsed=222.7016, diskTotal=460.4317, diskUsage=0.4837, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 269 | 1731389720548 | 2024-11-12 13:35:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | edfebeab29cf46ad886a4c41aa52c07b | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-46 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731389720388, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3608, jvmUsedMemory=0.394, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1108, diskUsed=222.7017, diskTotal=460.4317, diskUsage=0.4837, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 123 | 1731389720601 | 2024-11-12 13:35:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | cbd090521c994dac87217ce5d9ec3c5c | - | - | - | - | 21026 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1731389720601 | 2024-11-12 13:35:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | cbd090521c994dac87217ce5d9ec3c5c | - | - | - | - | 21026 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 123 | 1731389720604 | 2024-11-12 13:35:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ActorCell | receiveMessage | cbd090521c994dac87217ce5d9ec3c5c | - | - | - | - | 21029 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1731389720606 | 2024-11-12 13:35:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ActorCell | receiveMessage | cbd090521c994dac87217ce5d9ec3c5c | - | - | - | - | 21031 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 123 | 1731389720606 | 2024-11-12 13:35:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ActorCell | receiveMessage | cbd090521c994dac87217ce5d9ec3c5c | - | - | - | - | 21031 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 314 | 1731389750413 | 2024-11-12 13:35:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Actor | aroundReceive | 9b74ed6a987c4164b73dd2dc4ef88206 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731389730392, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9971, jvmUsedMemory=0.4163, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1171, diskUsed=222.7017, diskTotal=460.4317, diskUsage=0.4837, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 314 | 1731389750415 | 2024-11-12 13:35:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Actor | aroundReceive | 9b74ed6a987c4164b73dd2dc4ef88206 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731389740391, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7695, jvmUsedMemory=0.4232, jvmMaxMemory=3.5557, jvmMemoryUsage=0.119, diskUsed=222.7007, diskTotal=460.4317, diskUsage=0.4837, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 314 | 1731389750416 | 2024-11-12 13:35:50 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Actor | aroundReceive | 9b74ed6a987c4164b73dd2dc4ef88206 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=***************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1731389750389, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4971, jvmUsedMemory=0.4307, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1211, diskUsed=222.7018, diskTotal=460.4317, diskUsage=0.4837, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 314 | 1731389750435 | 2024-11-12 13:35:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ActorCell | receiveMessage | 9b74ed6a987c4164b73dd2dc4ef88206 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 314 | 1731389750436 | 2024-11-12 13:35:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ActorCell | receiveMessage | 9b74ed6a987c4164b73dd2dc4ef88206 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-48 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 44 | 1731389763097 | 2024-11-12 13:36:03 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 79927355960b4cd79d098adfd2de8bcb | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1731389763098 | 2024-11-12 13:36:03 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | 79927355960b4cd79d098adfd2de8bcb | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1731389763097 | 2024-11-12 13:36:03 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | eb0fc156aed94d499391d44df0b1e476 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 40 | 1731389763100 | 2024-11-12 13:36:03 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | Thread | run | eb0fc156aed94d499391d44df0b1e476 | - | - | - | - | 3 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 315 | 1731389763157 | 2024-11-12 13:36:03 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ActorCell | receiveMessage | 5418aff0e6ba4ad1acbe014d00970f16 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

