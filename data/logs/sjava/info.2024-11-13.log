info | 1 | 1731481950042 | 2024-11-13 15:12:30 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 34415 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1731481950033 | 2024-11-13 15:12:30 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | ff73495b78234e2c8bd9c97bd03664fb | - | - | - | - | 7 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731481950056 | 2024-11-13 15:12:30 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 8 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731481950573 | 2024-11-13 15:12:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 525 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731481950576 | 2024-11-13 15:12:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 532 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731481950582 | 2024-11-13 15:12:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 535 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731481950586 | 2024-11-13 15:12:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 539 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731481950589 | 2024-11-13 15:12:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 541 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731481950634 | 2024-11-13 15:12:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 586 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731481954154 | 2024-11-13 15:12:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 4106 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731481954159 | 2024-11-13 15:12:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 4112 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731481954160 | 2024-11-13 15:12:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 4112 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731481954227 | 2024-11-13 15:12:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 4179 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731481956192 | 2024-11-13 15:12:36 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 6145 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731481956194 | 2024-11-13 15:12:36 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 6146 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731481956232 | 2024-11-13 15:12:36 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 6185 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 33 ms. Found 0 JPA repository interfaces.

info | 1 | 1731481956242 | 2024-11-13 15:12:36 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 6194 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731481956243 | 2024-11-13 15:12:36 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 6195 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731481956269 | 2024-11-13 15:12:36 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 6221 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1731481957306 | 2024-11-13 15:12:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 7262 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b54d892b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731481957343 | 2024-11-13 15:12:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 7296 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$3453cea3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731481957444 | 2024-11-13 15:12:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 7396 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$b2bcd96c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731481957451 | 2024-11-13 15:12:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 7403 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731481957562 | 2024-11-13 15:12:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 7514 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731481957567 | 2024-11-13 15:12:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 7520 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731481958346 | 2024-11-13 15:12:38 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 8298 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731481958355 | 2024-11-13 15:12:38 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 8307 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731481958355 | 2024-11-13 15:12:38 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 8307 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731481958444 | 2024-11-13 15:12:38 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 8396 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731481973146 | 2024-11-13 15:12:53 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 23099 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731481973225 | 2024-11-13 15:12:53 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 23177 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731481973283 | 2024-11-13 15:12:53 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 23236 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731481973416 | 2024-11-13 15:12:53 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 23368 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731481973532 | 2024-11-13 15:12:53 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 23484 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731481973705 | 2024-11-13 15:12:53 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 23657 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731481973713 | 2024-11-13 15:12:53 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 23665 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731481978036 | 2024-11-13 15:12:58 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 27988 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731481978471 | 2024-11-13 15:12:58 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 28423 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731481978501 | 2024-11-13 15:12:58 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 28453 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1731481978798 | 2024-11-13 15:12:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7e3a7b4aff1443f5a90a0264ea1789e3 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1731481978831 | 2024-11-13 15:12:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7e3a7b4aff1443f5a90a0264ea1789e3 | - | - | - | - | 32 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1731481978840 | 2024-11-13 15:12:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7e3a7b4aff1443f5a90a0264ea1789e3 | - | - | - | - | 41 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1731481978955 | 2024-11-13 15:12:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7e3a7b4aff1443f5a90a0264ea1789e3 | - | - | - | - | 156 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 113 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1731481978962 | 2024-11-13 15:12:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7e3a7b4aff1443f5a90a0264ea1789e3 | - | - | - | - | 163 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1731481978971 | 2024-11-13 15:12:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7e3a7b4aff1443f5a90a0264ea1789e3 | - | - | - | - | 172 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1731481978978 | 2024-11-13 15:12:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7e3a7b4aff1443f5a90a0264ea1789e3 | - | - | - | - | 179 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1731481979106 | 2024-11-13 15:12:59 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 7e3a7b4aff1443f5a90a0264ea1789e3 | - | - | - | - | 307 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 126 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731481983060 | 2024-11-13 15:13:03 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 33012 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731481983926 | 2024-11-13 15:13:03 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 33878 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@8ea6dac with [org.springframework.security.web.session.DisableEncodeUrlFilter@38f563e4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@45400d7c, org.springframework.security.web.context.SecurityContextPersistenceFilter@487ce7c7, org.springframework.security.web.header.HeaderWriterFilter@52c15901, org.springframework.security.web.authentication.logout.LogoutFilter@1efb1a84, org.springframework.web.filter.CorsFilter@703ecfa3, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@6818550d, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@15bc42b2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@619f24c2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@795a3df7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4e60caa4, org.springframework.security.web.session.SessionManagementFilter@36223e06, org.springframework.security.web.access.ExceptionTranslationFilter@16cdf2b2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@558519a7]

info | 1 | 1731481983941 | 2024-11-13 15:13:03 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 33893 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731481984028 | 2024-11-13 15:13:04 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 33981 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731481984030 | 2024-11-13 15:13:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 33982 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731481984031 | 2024-11-13 15:13:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 33983 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731481984033 | 2024-11-13 15:13:04 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 33986 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731481984036 | 2024-11-13 15:13:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 33988 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731481984036 | 2024-11-13 15:13:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 33988 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731481984036 | 2024-11-13 15:13:04 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 33988 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1731481986524 | 2024-11-13 15:13:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36477 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1731481986641 | 2024-11-13 15:13:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36593 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731481986641 | 2024-11-13 15:13:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36594 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731481986642 | 2024-11-13 15:13:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36594 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731481986647 | 2024-11-13 15:13:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36600 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731481986823 | 2024-11-13 15:13:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36775 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731481986824 | 2024-11-13 15:13:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36776 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1731481986839 | 2024-11-13 15:13:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36792 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@51561b18, tech.powerjob.worker.actors.ProcessorTrackerActor@2755f323, tech.powerjob.worker.actors.WorkerActor@73539633])

info | 1 | 1731481986880 | 2024-11-13 15:13:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36832 | 0 | - | - | - | - | main o.r.Reflections Reflections took 26 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731481986887 | 2024-11-13 15:13:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36839 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1731481986888 | 2024-11-13 15:13:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36840 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@61d3ee02

info | 1 | 1731481986888 | 2024-11-13 15:13:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36840 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731481986888 | 2024-11-13 15:13:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36840 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1731481986891 | 2024-11-13 15:13:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 36843 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 127 | 1731481987402 | 2024-11-13 15:13:07 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731481987911 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37863 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731481987912 | 2024-11-13 15:13:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37864 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731481987912 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37864 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731481987912 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37865 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731481987913 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37865 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731481987913 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37865 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731481987913 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37865 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731481987913 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37865 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731481987913 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37865 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731481987913 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37865 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731481987914 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37866 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731481987914 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37866 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731481987914 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37866 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731481987914 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37866 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731481987914 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37866 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731481987916 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37868 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731481987918 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37870 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731481987918 | 2024-11-13 15:13:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37870 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731481987919 | 2024-11-13 15:13:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 37872 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.031 s

info | 1 | 1731481988048 | 2024-11-13 15:13:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38000 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731481988055 | 2024-11-13 15:13:08 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38007 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731481988055 | 2024-11-13 15:13:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38007 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731481988060 | 2024-11-13 15:13:08 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38013 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731481988313 | 2024-11-13 15:13:08 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38265 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731481988313 | 2024-11-13 15:13:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38265 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/41b8e662886e4c41ae551ce75d479239/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731481988319 | 2024-11-13 15:13:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38271 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/41b8e662886e4c41ae551ce75d479239/] on JVM exit successfully

info | 1 | 1731481988335 | 2024-11-13 15:13:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38287 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731481988336 | 2024-11-13 15:13:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38288 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 4.308 s, congratulations!

info | 159 | 1731481988343 | 2024-11-13 15:13:08 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 159 | 1731481988344 | 2024-11-13 15:13:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 2 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731481988499 | 2024-11-13 15:13:08 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38451 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731481988535 | 2024-11-13 15:13:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38487 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731481988556 | 2024-11-13 15:13:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38508 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731481988556 | 2024-11-13 15:13:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38509 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731481988591 | 2024-11-13 15:13:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38544 | 0 | - | - | - | - | main c.t.g.Application Started Application in 39.183 seconds (JVM running for 39.613)

info | 1 | 1731481988625 | 2024-11-13 15:13:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38578 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731481988626 | 2024-11-13 15:13:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | f2b187116f4d4886a9fcff0300ddd273 | - | - | - | - | 38578 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 229 | 1731481988629 | 2024-11-13 15:13:08 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | e5be10b13de348039ec2a4731eed1531 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 159 | 1731481998342 | 2024-11-13 15:13:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 9999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482008338 | 2024-11-13 15:13:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 19996 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482018340 | 2024-11-13 15:13:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 29997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482028340 | 2024-11-13 15:13:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 39997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482038341 | 2024-11-13 15:13:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 49999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482048342 | 2024-11-13 15:14:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 60000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482058339 | 2024-11-13 15:14:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 69997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482068343 | 2024-11-13 15:14:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 80001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482078343 | 2024-11-13 15:14:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 90001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482088343 | 2024-11-13 15:14:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 100001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482098344 | 2024-11-13 15:14:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 110002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1731482100036 | 2024-11-13 15:15:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | 3401dd8f25b946f2a45f83ef66c5bd36 | - | - | - | - | 0 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-11-13 15:12:58,470 to 2024-11-13 15:15:00,029
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 159 | 1731482108342 | 2024-11-13 15:15:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 120000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 159 | 1731482118340 | 2024-11-13 15:15:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 129998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 229 | 1731482120551 | 2024-11-13 15:15:20 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ChatRoomService | refreshChatCache | e5be10b13de348039ec2a4731eed1531 | - | - | - | - | 131922 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 159 | 1731482128341 | 2024-11-13 15:15:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 04df64980a9948fe8aa72717be5b17ce | - | - | - | - | 139998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482138344 | 2024-11-13 15:15:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482148340 | 2024-11-13 15:15:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 9999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482158342 | 2024-11-13 15:15:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 19999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482168344 | 2024-11-13 15:16:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 30008 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1731482176604 | 2024-11-13 15:16:16 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 65a13d7a57ab441db4ed2e596c7e1dd2 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 168 | 1731482176605 | 2024-11-13 15:16:16 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 65a13d7a57ab441db4ed2e596c7e1dd2 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 168 | 1731482176631 | 2024-11-13 15:16:16 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 65a13d7a57ab441db4ed2e596c7e1dd2 | - | - | - | - | 27 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 26 ms

info | 163 | 1731482178345 | 2024-11-13 15:16:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 40002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482188345 | 2024-11-13 15:16:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 50001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482198341 | 2024-11-13 15:16:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 59998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1731482231209 | 2024-11-13 15:17:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | a2e8c2b481ee4ed59c3748c59a5357fc | - | - | - | - | 2 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1731482231225 | 2024-11-13 15:17:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | a2e8c2b481ee4ed59c3748c59a5357fc | - | - | - | - | 10 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1731482231235 | 2024-11-13 15:17:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | a2e8c2b481ee4ed59c3748c59a5357fc | - | - | - | - | 20 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482238342 | 2024-11-13 15:17:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 99998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482248346 | 2024-11-13 15:17:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 110003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482258346 | 2024-11-13 15:17:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 120003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482268346 | 2024-11-13 15:17:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 130003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731482278342 | 2024-11-13 15:17:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7339f129a0cc49ccae4388e3dae7b7b6 | - | - | - | - | 139998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731483868322 | 2024-11-13 15:44:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 35424 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1731483868316 | 2024-11-13 15:44:28 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 7b467eac1add422480c81070af0b9c09 | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731483868336 | 2024-11-13 15:44:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731483868911 | 2024-11-13 15:44:28 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 583 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731483868914 | 2024-11-13 15:44:28 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 585 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731483868917 | 2024-11-13 15:44:28 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 589 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731483868920 | 2024-11-13 15:44:28 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 591 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731483868923 | 2024-11-13 15:44:28 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 594 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731483868968 | 2024-11-13 15:44:28 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 640 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731483869004 | 2024-11-13 15:44:29 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 675 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731483869006 | 2024-11-13 15:44:29 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 677 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731483869006 | 2024-11-13 15:44:29 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 677 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731483869055 | 2024-11-13 15:44:29 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 726 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731483871567 | 2024-11-13 15:44:31 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 3238 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731483871569 | 2024-11-13 15:44:31 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 3240 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731483871611 | 2024-11-13 15:44:31 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 3282 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 36 ms. Found 0 JPA repository interfaces.

info | 1 | 1731483871621 | 2024-11-13 15:44:31 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 3292 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731483871622 | 2024-11-13 15:44:31 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 3293 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731483871646 | 2024-11-13 15:44:31 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 3317 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.

info | 1 | 1731483872520 | 2024-11-13 15:44:32 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 4195 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$c610d59] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731483872552 | 2024-11-13 15:44:32 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 4223 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$8b6752d1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731483872623 | 2024-11-13 15:44:32 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 4294 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$9d05d9a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731483872628 | 2024-11-13 15:44:32 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 4299 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731483872690 | 2024-11-13 15:44:32 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 4361 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731483872695 | 2024-11-13 15:44:32 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 4366 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731483873232 | 2024-11-13 15:44:33 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 4903 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731483873245 | 2024-11-13 15:44:33 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 4916 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731483873245 | 2024-11-13 15:44:33 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 4916 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731483873335 | 2024-11-13 15:44:33 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 5007 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731483887934 | 2024-11-13 15:44:47 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 19605 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731483888008 | 2024-11-13 15:44:48 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 19679 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731483888054 | 2024-11-13 15:44:48 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 19725 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731483888163 | 2024-11-13 15:44:48 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 19834 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731483888250 | 2024-11-13 15:44:48 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 19922 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731483888389 | 2024-11-13 15:44:48 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 20060 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731483888395 | 2024-11-13 15:44:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 20066 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731483891792 | 2024-11-13 15:44:51 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 23464 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731483892059 | 2024-11-13 15:44:52 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 23730 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731483892077 | 2024-11-13 15:44:52 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 23748 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1731483892221 | 2024-11-13 15:44:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6e728f4178234b5ab1d58631121ec9f5 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1731483892244 | 2024-11-13 15:44:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6e728f4178234b5ab1d58631121ec9f5 | - | - | - | - | 22 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1731483892253 | 2024-11-13 15:44:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6e728f4178234b5ab1d58631121ec9f5 | - | - | - | - | 31 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1731483892366 | 2024-11-13 15:44:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6e728f4178234b5ab1d58631121ec9f5 | - | - | - | - | 144 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 111 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1731483892372 | 2024-11-13 15:44:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6e728f4178234b5ab1d58631121ec9f5 | - | - | - | - | 150 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1731483892378 | 2024-11-13 15:44:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6e728f4178234b5ab1d58631121ec9f5 | - | - | - | - | 157 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1731483892385 | 2024-11-13 15:44:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6e728f4178234b5ab1d58631121ec9f5 | - | - | - | - | 163 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1731483892487 | 2024-11-13 15:44:52 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6e728f4178234b5ab1d58631121ec9f5 | - | - | - | - | 265 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 100 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731483897187 | 2024-11-13 15:44:57 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 28858 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731483898019 | 2024-11-13 15:44:58 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 29692 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@15ab7999 with [org.springframework.security.web.session.DisableEncodeUrlFilter@6ed61f9e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3bdbf525, org.springframework.security.web.context.SecurityContextPersistenceFilter@1808fdac, org.springframework.security.web.header.HeaderWriterFilter@619a61c1, org.springframework.security.web.authentication.logout.LogoutFilter@63d042fd, org.springframework.web.filter.CorsFilter@15a54f36, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@45a1f1d0, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@54d8147d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@17b96d56, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@243c1d97, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1a71d8ce, org.springframework.security.web.session.SessionManagementFilter@64c82a3b, org.springframework.security.web.access.ExceptionTranslationFilter@86c162e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@67264fde]

info | 1 | 1731483898043 | 2024-11-13 15:44:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 29714 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731483898139 | 2024-11-13 15:44:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 29810 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731483898141 | 2024-11-13 15:44:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 29812 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731483898144 | 2024-11-13 15:44:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 29815 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731483898147 | 2024-11-13 15:44:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 29818 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731483898150 | 2024-11-13 15:44:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 29821 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731483898150 | 2024-11-13 15:44:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 29821 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731483898150 | 2024-11-13 15:44:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 29821 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1731483899663 | 2024-11-13 15:44:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31335 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1731483899738 | 2024-11-13 15:44:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31410 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731483899739 | 2024-11-13 15:44:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31411 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731483899740 | 2024-11-13 15:44:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31411 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731483899745 | 2024-11-13 15:44:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31417 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731483899876 | 2024-11-13 15:44:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31548 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731483899878 | 2024-11-13 15:44:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31549 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1731483899903 | 2024-11-13 15:44:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31574 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@44b3c545, tech.powerjob.worker.actors.ProcessorTrackerActor@1cbc3d6e, tech.powerjob.worker.actors.WorkerActor@7794503a])

info | 1 | 1731483899948 | 2024-11-13 15:44:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31619 | 0 | - | - | - | - | main o.r.Reflections Reflections took 32 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731483899956 | 2024-11-13 15:44:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31627 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1731483899957 | 2024-11-13 15:44:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31628 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@4c841ccb

info | 1 | 1731483899958 | 2024-11-13 15:44:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31629 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@2e4d2aaf

info | 1 | 1731483899958 | 2024-11-13 15:44:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31629 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731483899959 | 2024-11-13 15:44:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31630 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1731483899962 | 2024-11-13 15:44:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 31634 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 38 | 1731483900013 | 2024-11-13 15:45:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | 2e6b6e6fb0484717a1ae5bcf2847520e | - | - | - | - | 2 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-11-13 15:44:52,058 to 2024-11-13 15:45:00,009
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 134 | 1731483900481 | 2024-11-13 15:45:00 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731483901164 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32836 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731483901166 | 2024-11-13 15:45:01 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32837 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731483901167 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32838 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731483901167 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32838 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731483901167 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32838 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731483901167 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32839 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731483901168 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32839 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731483901168 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32840 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731483901169 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32840 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731483901169 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32840 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731483901169 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32840 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731483901169 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32841 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731483901170 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32841 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731483901170 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32841 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731483901170 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32841 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731483901173 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32844 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731483901177 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32848 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731483901178 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32849 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731483901178 | 2024-11-13 15:45:01 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32851 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.220 s

info | 1 | 1731483901271 | 2024-11-13 15:45:01 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32943 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731483901277 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32948 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731483901277 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32948 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731483901280 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 32952 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731483901577 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33248 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731483901577 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33248 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/34a9941d66ae47c3b7ce81be872aad31/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731483901584 | 2024-11-13 15:45:01 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33255 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/34a9941d66ae47c3b7ce81be872aad31/] on JVM exit successfully

info | 1 | 1731483901603 | 2024-11-13 15:45:01 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33274 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731483901604 | 2024-11-13 15:45:01 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33276 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 3.466 s, congratulations!

info | 166 | 1731483901624 | 2024-11-13 15:45:01 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 166 | 1731483901625 | 2024-11-13 15:45:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 2 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731483901897 | 2024-11-13 15:45:01 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | TomcatWebServer | start | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33568 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731483901955 | 2024-11-13 15:45:01 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33627 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731483901980 | 2024-11-13 15:45:01 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33652 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731483901981 | 2024-11-13 15:45:01 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33652 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731483902015 | 2024-11-13 15:45:02 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33687 | 0 | - | - | - | - | main c.t.g.Application Started Application in 34.303 seconds (JVM running for 35.029)

info | 1 | 1731483902052 | 2024-11-13 15:45:02 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33724 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731483902053 | 2024-11-13 15:45:02 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | Application | main | 7de89238f922488c9f96deb128c39ff0 | - | - | - | - | 33724 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 236 | 1731483902063 | 2024-11-13 15:45:02 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | f2959ff2e674450481bfb46b806719d9 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 166 | 1731483911606 | 2024-11-13 15:45:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 9982 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731483921606 | 2024-11-13 15:45:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 19983 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731483931606 | 2024-11-13 15:45:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 29983 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731483941610 | 2024-11-13 15:45:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 39987 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731483951611 | 2024-11-13 15:45:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 49987 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731483961610 | 2024-11-13 15:46:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 59986 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731483971607 | 2024-11-13 15:46:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 69984 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731483981606 | 2024-11-13 15:46:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 79983 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731483991607 | 2024-11-13 15:46:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 89984 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484001606 | 2024-11-13 15:46:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 99983 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484011612 | 2024-11-13 15:46:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 109989 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484021609 | 2024-11-13 15:47:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 119986 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484031610 | 2024-11-13 15:47:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 129987 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 236 | 1731484035053 | 2024-11-13 15:47:15 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ChatRoomService | refreshChatCache | f2959ff2e674450481bfb46b806719d9 | - | - | - | - | 132991 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 166 | 1731484041611 | 2024-11-13 15:47:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 139988 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484051609 | 2024-11-13 15:47:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 149986 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484061613 | 2024-11-13 15:47:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 159989 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484071608 | 2024-11-13 15:47:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 169991 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484081613 | 2024-11-13 15:48:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 179990 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484091612 | 2024-11-13 15:48:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 189988 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484101614 | 2024-11-13 15:48:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 199991 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484111612 | 2024-11-13 15:48:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 209988 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484121613 | 2024-11-13 15:48:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 219989 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484131609 | 2024-11-13 15:48:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 229987 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484141614 | 2024-11-13 15:49:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 239990 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484151613 | 2024-11-13 15:49:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 249990 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484161610 | 2024-11-13 15:49:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 259987 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484171615 | 2024-11-13 15:49:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 269992 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484181615 | 2024-11-13 15:49:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 279992 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484191615 | 2024-11-13 15:49:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 289991 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484201614 | 2024-11-13 15:50:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 299993 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484211613 | 2024-11-13 15:50:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 309989 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484221612 | 2024-11-13 15:50:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 319989 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484231613 | 2024-11-13 15:50:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 329989 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484241614 | 2024-11-13 15:50:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 339991 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484251612 | 2024-11-13 15:50:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 349989 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484261616 | 2024-11-13 15:51:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 359993 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484271612 | 2024-11-13 15:51:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.77 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 369989 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484281614 | 2024-11-13 15:51:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.79 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 379991 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1731484291615 | 2024-11-13 15:51:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.81 | *************** | - | 2 | ThreadPoolExecutor | runWorker | b7aedd7bd5984a9298331685de825f3b | - | - | - | - | 389991 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 171 | 1731484301617 | 2024-11-13 15:51:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | a86517d4b51a4727a428b1b968e25622 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 171 | 1731484311618 | 2024-11-13 15:51:51 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | a86517d4b51a4727a428b1b968e25622 | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 171 | 1731484321616 | 2024-11-13 15:52:01 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | a86517d4b51a4727a428b1b968e25622 | - | - | - | - | 19999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 174 | 1731484328573 | 2024-11-13 15:52:08 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | f96c254cf78248a794c166d715be8322 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 174 | 1731484328581 | 2024-11-13 15:52:08 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | f96c254cf78248a794c166d715be8322 | - | - | - | - | 8 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 174 | 1731484328610 | 2024-11-13 15:52:08 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | f96c254cf78248a794c166d715be8322 | - | - | - | - | 38 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 29 ms

info | 174 | 1731484329509 | 2024-11-13 15:52:09 | v2/orgStatistic/findConsortiaPerDayList | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | orgStatistic | findConsortiaPerDayList | 9678b1a1e9d74945acb650db5666be13 | - | - | - | - | 887 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.s.i.OrgStatisticsServiceImpl 登录用户Leader{"consortiaId":null,"consortiaIdList":[2342696],"orgId":null,"teamId":null,"agentUuid":null,"agentId":null,"startTime":20241030,"endTime":20241112,"page":1,"pageSize":1000,"export":null,"businessPerson":null}-----{"roleId":29,"roleKey":"manager","type":1,"roleName":null}

info | 171 | 1731484331618 | 2024-11-13 15:52:11 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | a86517d4b51a4727a428b1b968e25622 | - | - | - | - | 30004 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 174 | 1731484335041 | 2024-11-13 15:52:15 | v2/orgStatistic/findConsortiaPerDayList | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | orgStatistic | findConsortiaPerDayList | 9678b1a1e9d74945acb650db5666be13 | - | - | - | - | 6418 | 0 | - | - | - | - | http-nio-8087-exec-1 c.a.d.p.DruidDataSource {dataSource-2,livedb} inited

info | 171 | 1731484341618 | 2024-11-13 15:52:21 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | a86517d4b51a4727a428b1b968e25622 | - | - | - | - | 40002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 170 | 1731484351619 | 2024-11-13 15:52:31 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 6c1a62c4bf0541fdb6f6274ef404f50e | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1731484358276 | 2024-11-13 15:52:38 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | d310b9018cba40acb92755ab1e9b489e | - | - | - | - | 1 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 1 | 1731484360846 | 2024-11-13 15:52:40 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 35684 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1731484360847 | 2024-11-13 15:52:40 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 3f749fa5232a4cf1baf2b341be78479f | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731484360862 | 2024-11-13 15:52:40 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731484361344 | 2024-11-13 15:52:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 487 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731484361347 | 2024-11-13 15:52:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 490 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731484361352 | 2024-11-13 15:52:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 495 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731484361355 | 2024-11-13 15:52:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 499 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731484361359 | 2024-11-13 15:52:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 504 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731484361400 | 2024-11-13 15:52:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 543 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731484361438 | 2024-11-13 15:52:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 581 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731484361440 | 2024-11-13 15:52:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 583 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731484361440 | 2024-11-13 15:52:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 583 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731484361499 | 2024-11-13 15:52:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 642 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731484363388 | 2024-11-13 15:52:43 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 2531 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731484363389 | 2024-11-13 15:52:43 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 2532 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731484363426 | 2024-11-13 15:52:43 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 2569 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 32 ms. Found 0 JPA repository interfaces.

info | 1 | 1731484363434 | 2024-11-13 15:52:43 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 2577 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731484363435 | 2024-11-13 15:52:43 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 2578 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731484363460 | 2024-11-13 15:52:43 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 2603 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1731484364221 | 2024-11-13 15:52:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 3368 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$83f0fc83] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484364246 | 2024-11-13 15:52:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 3389 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$2f741fb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484364316 | 2024-11-13 15:52:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 3460 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$81604cc4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484364320 | 2024-11-13 15:52:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 3464 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484364376 | 2024-11-13 15:52:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 3519 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484364379 | 2024-11-13 15:52:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 3522 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484364932 | 2024-11-13 15:52:44 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 4075 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731484364944 | 2024-11-13 15:52:44 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 4088 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731484364945 | 2024-11-13 15:52:44 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 4088 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731484365025 | 2024-11-13 15:52:45 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 4168 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731484376960 | 2024-11-13 15:52:56 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 16104 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731484377033 | 2024-11-13 15:52:57 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 16177 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731484377067 | 2024-11-13 15:52:57 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 16211 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731484377164 | 2024-11-13 15:52:57 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 16307 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731484377250 | 2024-11-13 15:52:57 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 16393 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731484377370 | 2024-11-13 15:52:57 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 16513 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731484377376 | 2024-11-13 15:52:57 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 16520 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731484381700 | 2024-11-13 15:53:01 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 20844 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731484382004 | 2024-11-13 15:53:02 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 21148 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731484382028 | 2024-11-13 15:53:02 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 21171 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1731484382178 | 2024-11-13 15:53:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6a20b254bade432c883a525f901c9b3f | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1731484382202 | 2024-11-13 15:53:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6a20b254bade432c883a525f901c9b3f | - | - | - | - | 24 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1731484382210 | 2024-11-13 15:53:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6a20b254bade432c883a525f901c9b3f | - | - | - | - | 32 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1731484382332 | 2024-11-13 15:53:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6a20b254bade432c883a525f901c9b3f | - | - | - | - | 154 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 119 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1731484382339 | 2024-11-13 15:53:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6a20b254bade432c883a525f901c9b3f | - | - | - | - | 161 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1731484382346 | 2024-11-13 15:53:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6a20b254bade432c883a525f901c9b3f | - | - | - | - | 168 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1731484382540 | 2024-11-13 15:53:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6a20b254bade432c883a525f901c9b3f | - | - | - | - | 363 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1731484382683 | 2024-11-13 15:53:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 6a20b254bade432c883a525f901c9b3f | - | - | - | - | 506 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 140 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731484385042 | 2024-11-13 15:53:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 24185 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731484385930 | 2024-11-13 15:53:05 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25073 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@15f328aa with [org.springframework.security.web.session.DisableEncodeUrlFilter@276decea, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2b874d46, org.springframework.security.web.context.SecurityContextPersistenceFilter@12029dd2, org.springframework.security.web.header.HeaderWriterFilter@188467fe, org.springframework.security.web.authentication.logout.LogoutFilter@70ba6842, org.springframework.web.filter.CorsFilter@4556a9a7, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@61fab063, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@4a38e868, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6071658d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@76f99d4a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@46822ffd, org.springframework.security.web.session.SessionManagementFilter@1d76ebc4, org.springframework.security.web.access.ExceptionTranslationFilter@169a28e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7e7e5302]

info | 1 | 1731484385955 | 2024-11-13 15:53:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25099 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731484386103 | 2024-11-13 15:53:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25246 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731484386105 | 2024-11-13 15:53:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25248 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731484386106 | 2024-11-13 15:53:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25249 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731484386109 | 2024-11-13 15:53:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25252 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731484386113 | 2024-11-13 15:53:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25256 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731484386114 | 2024-11-13 15:53:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25257 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731484386114 | 2024-11-13 15:53:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25257 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1731484386408 | 2024-11-13 15:53:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25551 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1731484386512 | 2024-11-13 15:53:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25656 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731484386513 | 2024-11-13 15:53:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25656 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731484386513 | 2024-11-13 15:53:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25657 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731484386518 | 2024-11-13 15:53:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25661 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731484386596 | 2024-11-13 15:53:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25739 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731484386598 | 2024-11-13 15:53:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25741 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1731484386613 | 2024-11-13 15:53:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25756 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@4fda9aa4, tech.powerjob.worker.actors.ProcessorTrackerActor@6a6818d1, tech.powerjob.worker.actors.WorkerActor@30f428f6])

info | 1 | 1731484386659 | 2024-11-13 15:53:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25803 | 0 | - | - | - | - | main o.r.Reflections Reflections took 30 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731484386666 | 2024-11-13 15:53:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25809 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1731484386666 | 2024-11-13 15:53:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25809 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@d28b90b

info | 1 | 1731484386666 | 2024-11-13 15:53:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25809 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731484386667 | 2024-11-13 15:53:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25810 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1731484386671 | 2024-11-13 15:53:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 25815 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 125 | 1731484387159 | 2024-11-13 15:53:07 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731484387529 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26672 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731484387530 | 2024-11-13 15:53:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731484387530 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731484387530 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731484387530 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731484387531 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26674 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731484387531 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26674 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731484387531 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26674 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731484387531 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26674 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731484387531 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26674 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731484387531 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26674 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731484387531 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26674 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731484387531 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26674 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731484387531 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26674 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731484387531 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26674 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731484387533 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26676 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731484387536 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26679 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731484387536 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26679 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731484387537 | 2024-11-13 15:53:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26680 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 870.3 ms

info | 1 | 1731484387850 | 2024-11-13 15:53:07 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26993 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731484387855 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26998 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731484387855 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 26998 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731484387859 | 2024-11-13 15:53:07 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27002 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731484388091 | 2024-11-13 15:53:08 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27235 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731484388092 | 2024-11-13 15:53:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27235 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/d6a7460b61fa481d97969108e42ad21f/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731484388098 | 2024-11-13 15:53:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27241 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/d6a7460b61fa481d97969108e42ad21f/] on JVM exit successfully

info | 1 | 1731484388117 | 2024-11-13 15:53:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27260 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731484388118 | 2024-11-13 15:53:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27261 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 2.015 s, congratulations!

info | 156 | 1731484388124 | 2024-11-13 15:53:08 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | cf6920894e1645bfa0bedb5f7dff6f05 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 156 | 1731484388125 | 2024-11-13 15:53:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cf6920894e1645bfa0bedb5f7dff6f05 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731484388179 | 2024-11-13 15:53:08 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27322 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731484388211 | 2024-11-13 15:53:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27354 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731484388224 | 2024-11-13 15:53:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27367 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731484388224 | 2024-11-13 15:53:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27367 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731484388250 | 2024-11-13 15:53:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27394 | 0 | - | - | - | - | main c.t.g.Application Started Application in 28.007 seconds (JVM running for 28.566)

info | 1 | 1731484388274 | 2024-11-13 15:53:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27418 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731484388275 | 2024-11-13 15:53:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 8f7d373ff1e6489e9106d50ad07df367 | - | - | - | - | 27418 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 226 | 1731484388280 | 2024-11-13 15:53:08 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 03e49bdf35f14a73a11c8de1751c1794 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 163 | 1731484395479 | 2024-11-13 15:53:15 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | aac2703972ac48a78f1eb2bafc4879c6 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 163 | 1731484395479 | 2024-11-13 15:53:15 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | aac2703972ac48a78f1eb2bafc4879c6 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 163 | 1731484395486 | 2024-11-13 15:53:15 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | aac2703972ac48a78f1eb2bafc4879c6 | - | - | - | - | 8 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 7 ms

info | 163 | 1731484396017 | 2024-11-13 15:53:16 | v2/orgStatistic/findConsortiaPerDayList | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | orgStatistic | findConsortiaPerDayList | 8dcd4f1376534c94b5e5b975b3b9101f | - | - | - | - | 525 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.s.i.OrgStatisticsServiceImpl 登录用户Leader{"consortiaId":null,"consortiaIdList":[2342696],"orgId":null,"teamId":null,"agentUuid":null,"agentId":null,"startTime":20241030,"endTime":20241112,"page":1,"pageSize":1000,"export":null,"businessPerson":null}-----{"roleId":29,"roleKey":"manager","type":1,"roleName":null}

info | 156 | 1731484398125 | 2024-11-13 15:53:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cf6920894e1645bfa0bedb5f7dff6f05 | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1731484401524 | 2024-11-13 15:53:21 | v2/orgStatistic/findConsortiaPerDayList | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | orgStatistic | findConsortiaPerDayList | 8dcd4f1376534c94b5e5b975b3b9101f | - | - | - | - | 6032 | 0 | - | - | - | - | http-nio-8087-exec-1 c.a.d.p.DruidDataSource {dataSource-2,livedb} inited

info | 156 | 1731484408119 | 2024-11-13 15:53:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cf6920894e1645bfa0bedb5f7dff6f05 | - | - | - | - | 19995 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731484418124 | 2024-11-13 15:53:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cf6920894e1645bfa0bedb5f7dff6f05 | - | - | - | - | 30001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731484428124 | 2024-11-13 15:53:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cf6920894e1645bfa0bedb5f7dff6f05 | - | - | - | - | 40000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731484438125 | 2024-11-13 15:53:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cf6920894e1645bfa0bedb5f7dff6f05 | - | - | - | - | 50002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1731484448124 | 2024-11-13 15:54:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cf6920894e1645bfa0bedb5f7dff6f05 | - | - | - | - | 60000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 15 | 1731484452879 | 2024-11-13 15:54:12 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 7e2173ad13c94eaf94275e4225568fca | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731484452885 | 2024-11-13 15:54:12 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 35736 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1731484452896 | 2024-11-13 15:54:12 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731484453424 | 2024-11-13 15:54:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 533 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731484453426 | 2024-11-13 15:54:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 536 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731484453428 | 2024-11-13 15:54:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 538 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731484453433 | 2024-11-13 15:54:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 542 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731484453434 | 2024-11-13 15:54:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 544 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731484453477 | 2024-11-13 15:54:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 586 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731484453506 | 2024-11-13 15:54:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 616 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731484453508 | 2024-11-13 15:54:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 617 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731484453508 | 2024-11-13 15:54:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 617 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731484453556 | 2024-11-13 15:54:13 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 665 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731484455512 | 2024-11-13 15:54:15 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 2621 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731484455514 | 2024-11-13 15:54:15 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 2623 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731484455552 | 2024-11-13 15:54:15 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 2661 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 33 ms. Found 0 JPA repository interfaces.

info | 1 | 1731484455559 | 2024-11-13 15:54:15 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 2669 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731484455560 | 2024-11-13 15:54:15 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 2669 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731484455583 | 2024-11-13 15:54:15 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 2692 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1731484456358 | 2024-11-13 15:54:16 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 3470 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$95c643d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484456378 | 2024-11-13 15:54:16 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 3487 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$8862a9b5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484456436 | 2024-11-13 15:54:16 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 3545 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$6cbb47e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484456441 | 2024-11-13 15:54:16 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 3550 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484456505 | 2024-11-13 15:54:16 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 3614 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484456509 | 2024-11-13 15:54:16 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 3618 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731484456951 | 2024-11-13 15:54:16 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 4060 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731484456961 | 2024-11-13 15:54:16 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 4070 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731484456961 | 2024-11-13 15:54:16 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 4070 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731484457036 | 2024-11-13 15:54:17 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 4145 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731484470214 | 2024-11-13 15:54:30 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 17324 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731484470314 | 2024-11-13 15:54:30 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 17423 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731484470351 | 2024-11-13 15:54:30 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 17460 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731484470446 | 2024-11-13 15:54:30 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 17555 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731484470530 | 2024-11-13 15:54:30 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 17639 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731484470731 | 2024-11-13 15:54:30 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 17840 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731484470737 | 2024-11-13 15:54:30 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 17846 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731484474706 | 2024-11-13 15:54:34 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 21815 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731484475022 | 2024-11-13 15:54:35 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 22131 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731484475052 | 2024-11-13 15:54:35 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 22161 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1731484475197 | 2024-11-13 15:54:35 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 8c9dc69537494ac99bb7d326ed285222 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1731484475217 | 2024-11-13 15:54:35 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 8c9dc69537494ac99bb7d326ed285222 | - | - | - | - | 20 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1731484475226 | 2024-11-13 15:54:35 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 8c9dc69537494ac99bb7d326ed285222 | - | - | - | - | 28 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1731484475345 | 2024-11-13 15:54:35 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 8c9dc69537494ac99bb7d326ed285222 | - | - | - | - | 147 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 117 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1731484475351 | 2024-11-13 15:54:35 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 8c9dc69537494ac99bb7d326ed285222 | - | - | - | - | 153 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1731484475360 | 2024-11-13 15:54:35 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 8c9dc69537494ac99bb7d326ed285222 | - | - | - | - | 162 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1731484475368 | 2024-11-13 15:54:35 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 8c9dc69537494ac99bb7d326ed285222 | - | - | - | - | 170 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1731484475479 | 2024-11-13 15:54:35 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 8c9dc69537494ac99bb7d326ed285222 | - | - | - | - | 282 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 109 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731484477811 | 2024-11-13 15:54:37 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 24920 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731484478803 | 2024-11-13 15:54:38 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 25912 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@2596a640 with [org.springframework.security.web.session.DisableEncodeUrlFilter@7cee5f2f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@342ca509, org.springframework.security.web.context.SecurityContextPersistenceFilter@1c0e1290, org.springframework.security.web.header.HeaderWriterFilter@7e317103, org.springframework.security.web.authentication.logout.LogoutFilter@514df93c, org.springframework.web.filter.CorsFilter@26159658, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@6b5b5f94, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@5d8b2f90, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6246a249, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@40426682, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4dbf26c7, org.springframework.security.web.session.SessionManagementFilter@244a60a0, org.springframework.security.web.access.ExceptionTranslationFilter@31d90af9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4ef0df44]

info | 1 | 1731484478820 | 2024-11-13 15:54:38 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 25930 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731484478909 | 2024-11-13 15:54:38 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26019 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731484478911 | 2024-11-13 15:54:38 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26020 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731484478912 | 2024-11-13 15:54:38 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26021 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731484478914 | 2024-11-13 15:54:38 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26023 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731484478919 | 2024-11-13 15:54:38 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26028 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731484478919 | 2024-11-13 15:54:38 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26028 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731484478919 | 2024-11-13 15:54:38 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26028 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1731484479131 | 2024-11-13 15:54:39 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26240 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1731484479229 | 2024-11-13 15:54:39 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26338 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731484479229 | 2024-11-13 15:54:39 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26338 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731484479229 | 2024-11-13 15:54:39 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26338 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731484479231 | 2024-11-13 15:54:39 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26340 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731484479358 | 2024-11-13 15:54:39 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26468 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731484479360 | 2024-11-13 15:54:39 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26469 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1731484479373 | 2024-11-13 15:54:39 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26483 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@1ecb9068, tech.powerjob.worker.actors.ProcessorTrackerActor@35b53a35, tech.powerjob.worker.actors.WorkerActor@6ad4a475])

info | 1 | 1731484479416 | 2024-11-13 15:54:39 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26525 | 0 | - | - | - | - | main o.r.Reflections Reflections took 28 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731484479422 | 2024-11-13 15:54:39 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26531 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1731484479423 | 2024-11-13 15:54:39 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26532 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@240c9b52

info | 1 | 1731484479423 | 2024-11-13 15:54:39 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26532 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731484479423 | 2024-11-13 15:54:39 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26532 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1731484479427 | 2024-11-13 15:54:39 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 26536 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 121 | 1731484479837 | 2024-11-13 15:54:39 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731484480369 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27479 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731484480371 | 2024-11-13 15:54:40 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27480 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731484480371 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27480 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731484480371 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27480 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731484480371 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27480 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731484480372 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27481 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731484480372 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27481 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731484480372 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27481 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731484480372 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27481 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731484480372 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27481 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731484480372 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27481 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731484480372 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27481 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731484480372 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27481 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731484480372 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27481 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731484480372 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27481 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731484480374 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27483 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731484480377 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27487 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731484480379 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27488 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731484480379 | 2024-11-13 15:54:40 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27489 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 956.4 ms

info | 1 | 1731484480481 | 2024-11-13 15:54:40 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27590 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731484480486 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27595 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731484480486 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27595 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731484480490 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27599 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731484480685 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27794 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731484480685 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27795 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/32c9c84e105748f893561d51d0d09e8d/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731484480690 | 2024-11-13 15:54:40 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27799 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/32c9c84e105748f893561d51d0d09e8d/] on JVM exit successfully

info | 1 | 1731484480705 | 2024-11-13 15:54:40 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27814 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731484480706 | 2024-11-13 15:54:40 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27815 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.797 s, congratulations!

info | 157 | 1731484480711 | 2024-11-13 15:54:40 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 7a21a967b66744829fc586714590dd93 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 157 | 1731484480712 | 2024-11-13 15:54:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7a21a967b66744829fc586714590dd93 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731484480761 | 2024-11-13 15:54:40 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27870 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731484480781 | 2024-11-13 15:54:40 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27890 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731484480802 | 2024-11-13 15:54:40 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27911 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731484480802 | 2024-11-13 15:54:40 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27911 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731484480833 | 2024-11-13 15:54:40 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27942 | 0 | - | - | - | - | main c.t.g.Application Started Application in 28.395 seconds (JVM running for 28.9)

info | 1 | 1731484480856 | 2024-11-13 15:54:40 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27966 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731484480857 | 2024-11-13 15:54:40 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 0df10568562348c2ac6519aaf6d0b542 | - | - | - | - | 27966 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 227 | 1731484480861 | 2024-11-13 15:54:40 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 619f481f0c8a4f7bb7186810bab95cce | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 164 | 1731484483763 | 2024-11-13 15:54:43 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 956d6dd411bd4fa798dc3a2fa9cb95b7 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 164 | 1731484483765 | 2024-11-13 15:54:43 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 956d6dd411bd4fa798dc3a2fa9cb95b7 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 164 | 1731484483769 | 2024-11-13 15:54:43 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 956d6dd411bd4fa798dc3a2fa9cb95b7 | - | - | - | - | 5 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 4 ms

info | 164 | 1731484484296 | 2024-11-13 15:54:44 | v2/orgStatistic/findConsortiaPerDayList | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | orgStatistic | findConsortiaPerDayList | 43e6cf85fcac4ba9a1a8fbbacfbe1cbe | - | - | - | - | 522 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.s.i.OrgStatisticsServiceImpl 登录用户Leader{"consortiaId":null,"consortiaIdList":[2342696],"orgId":null,"teamId":null,"agentUuid":null,"agentId":null,"startTime":20241030,"endTime":20241112,"page":1,"pageSize":1000,"export":null,"businessPerson":null}-----{"roleId":29,"roleKey":"manager","type":1,"roleName":null}

info | 164 | 1731484489824 | 2024-11-13 15:54:49 | v2/orgStatistic/findConsortiaPerDayList | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | orgStatistic | findConsortiaPerDayList | 43e6cf85fcac4ba9a1a8fbbacfbe1cbe | - | - | - | - | 6050 | 0 | - | - | - | - | http-nio-8087-exec-1 c.a.d.p.DruidDataSource {dataSource-2,livedb} inited

info | 157 | 1731484490711 | 2024-11-13 15:54:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7a21a967b66744829fc586714590dd93 | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731484500707 | 2024-11-13 15:55:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7a21a967b66744829fc586714590dd93 | - | - | - | - | 19997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731484510694 | 2024-11-13 15:55:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7a21a967b66744829fc586714590dd93 | - | - | - | - | 29983 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731484520686 | 2024-11-13 15:55:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7a21a967b66744829fc586714590dd93 | - | - | - | - | 39976 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731484530678 | 2024-11-13 15:55:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7a21a967b66744829fc586714590dd93 | - | - | - | - | 49968 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731484540672 | 2024-11-13 15:55:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7a21a967b66744829fc586714590dd93 | - | - | - | - | 59961 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1731484550670 | 2024-11-13 15:55:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7a21a967b66744829fc586714590dd93 | - | - | - | - | 69959 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1731484558276 | 2024-11-13 15:55:58 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 458571a5370a4c0ba8f418613369959d | - | - | - | - | 1 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 1 | 1731497857644 | 2024-11-13 19:37:37 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 42470 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1731497857630 | 2024-11-13 19:37:37 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | f9eab0e04afc4644b3e4bd43d2a43828 | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1731497857654 | 2024-11-13 19:37:37 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1731497858199 | 2024-11-13 19:37:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 552 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1731497858201 | 2024-11-13 19:37:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 555 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1731497858203 | 2024-11-13 19:37:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 556 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1731497858205 | 2024-11-13 19:37:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 558 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1731497858208 | 2024-11-13 19:37:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 561 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1731497858248 | 2024-11-13 19:37:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 602 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1731497858285 | 2024-11-13 19:37:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 638 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1731497858288 | 2024-11-13 19:37:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 641 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1731497858288 | 2024-11-13 19:37:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 641 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1731497858344 | 2024-11-13 19:37:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 697 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1731497860288 | 2024-11-13 19:37:40 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 2641 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731497860289 | 2024-11-13 19:37:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 2642 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1731497860329 | 2024-11-13 19:37:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 2683 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.

info | 1 | 1731497860339 | 2024-11-13 19:37:40 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 2693 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1731497860340 | 2024-11-13 19:37:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 2694 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1731497860367 | 2024-11-13 19:37:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 2720 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1731497861196 | 2024-11-13 19:37:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 3552 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$9e83e3b5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731497861215 | 2024-11-13 19:37:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 3569 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$1d8a292d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731497861280 | 2024-11-13 19:37:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 3634 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$9bf333f6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731497861285 | 2024-11-13 19:37:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 3638 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731497861334 | 2024-11-13 19:37:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 3687 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731497861337 | 2024-11-13 19:37:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 3691 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1731497861940 | 2024-11-13 19:37:41 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 4293 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1731497861952 | 2024-11-13 19:37:41 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 4305 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1731497861953 | 2024-11-13 19:37:41 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 4306 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1731497862100 | 2024-11-13 19:37:42 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 4453 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1731497874542 | 2024-11-13 19:37:54 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 16896 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1731497874595 | 2024-11-13 19:37:54 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 16948 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1731497874633 | 2024-11-13 19:37:54 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 16986 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1731497874736 | 2024-11-13 19:37:54 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 17089 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1731497874818 | 2024-11-13 19:37:54 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 17171 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1731497874956 | 2024-11-13 19:37:54 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 17309 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1731497874962 | 2024-11-13 19:37:54 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 17315 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1731497878746 | 2024-11-13 19:37:58 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 21099 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1731497879124 | 2024-11-13 19:37:59 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 21477 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1731497879150 | 2024-11-13 19:37:59 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 21503 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1731497879323 | 2024-11-13 19:37:59 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ad3a3a2056d242e6b81efe868fd42ac4 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 29 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1731497879356 | 2024-11-13 19:37:59 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ad3a3a2056d242e6b81efe868fd42ac4 | - | - | - | - | 33 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1731497879366 | 2024-11-13 19:37:59 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ad3a3a2056d242e6b81efe868fd42ac4 | - | - | - | - | 44 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1731497879479 | 2024-11-13 19:37:59 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ad3a3a2056d242e6b81efe868fd42ac4 | - | - | - | - | 156 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 109 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1731497879485 | 2024-11-13 19:37:59 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ad3a3a2056d242e6b81efe868fd42ac4 | - | - | - | - | 162 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1731497879492 | 2024-11-13 19:37:59 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ad3a3a2056d242e6b81efe868fd42ac4 | - | - | - | - | 169 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1731497879499 | 2024-11-13 19:37:59 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ad3a3a2056d242e6b81efe868fd42ac4 | - | - | - | - | 176 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1731497879600 | 2024-11-13 19:37:59 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | ad3a3a2056d242e6b81efe868fd42ac4 | - | - | - | - | 277 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 99 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1731497885481 | 2024-11-13 19:38:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 27835 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1731497886465 | 2024-11-13 19:38:06 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 28819 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@3e7afcc with [org.springframework.security.web.session.DisableEncodeUrlFilter@4d0bbae5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6a18b490, org.springframework.security.web.context.SecurityContextPersistenceFilter@52907b1e, org.springframework.security.web.header.HeaderWriterFilter@1eb7de01, org.springframework.security.web.authentication.logout.LogoutFilter@9cb9484, org.springframework.web.filter.CorsFilter@2ba1bed1, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@b41d473, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@18bca8c2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@393830f3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4dae96a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@261bfdd6, org.springframework.security.web.session.SessionManagementFilter@42923ae8, org.springframework.security.web.access.ExceptionTranslationFilter@a7b9673, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5bef8b67]

info | 1 | 1731497886499 | 2024-11-13 19:38:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 28852 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1731497886586 | 2024-11-13 19:38:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 28940 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1731497886588 | 2024-11-13 19:38:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 28941 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1731497886589 | 2024-11-13 19:38:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 28942 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1731497886591 | 2024-11-13 19:38:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 28944 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1731497886594 | 2024-11-13 19:38:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 28947 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1731497886594 | 2024-11-13 19:38:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 28947 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1731497886595 | 2024-11-13 19:38:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 28948 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1731497886835 | 2024-11-13 19:38:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29189 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1731497886934 | 2024-11-13 19:38:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29287 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1731497886934 | 2024-11-13 19:38:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29288 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1731497886935 | 2024-11-13 19:38:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29288 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1731497886936 | 2024-11-13 19:38:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29290 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1731497887020 | 2024-11-13 19:38:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29373 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1731497887021 | 2024-11-13 19:38:07 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29374 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1731497887031 | 2024-11-13 19:38:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29385 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@3c2c5c75, tech.powerjob.worker.actors.ProcessorTrackerActor@7ede2028, tech.powerjob.worker.actors.WorkerActor@5f382a22])

info | 1 | 1731497887079 | 2024-11-13 19:38:07 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29433 | 0 | - | - | - | - | main o.r.Reflections Reflections took 35 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1731497887090 | 2024-11-13 19:38:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29443 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1731497887093 | 2024-11-13 19:38:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29446 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@2ca2159a

info | 1 | 1731497887095 | 2024-11-13 19:38:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29448 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@35117447

info | 1 | 1731497887096 | 2024-11-13 19:38:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29449 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1731497887098 | 2024-11-13 19:38:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29451 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1731497887106 | 2024-11-13 19:38:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 29459 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 127 | 1731497887632 | 2024-11-13 19:38:07 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1731497888020 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30374 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1731497888021 | 2024-11-13 19:38:08 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30374 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1731497888021 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1731497888022 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30375 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1731497888024 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30377 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1731497888026 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30379 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1731497888026 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30379 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1731497888027 | 2024-11-13 19:38:08 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30383 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 930.6 ms

info | 1 | 1731497888113 | 2024-11-13 19:38:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30466 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1731497888118 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30471 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1731497888119 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30472 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1731497888123 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30476 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1731497888348 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30702 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1731497888349 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30702 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/952f951583db46f6967a24783390e918/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1731497888355 | 2024-11-13 19:38:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30708 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/952f951583db46f6967a24783390e918/] on JVM exit successfully

info | 1 | 1731497888370 | 2024-11-13 19:38:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30723 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1731497888371 | 2024-11-13 19:38:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30724 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.785 s, congratulations!

info | 168 | 1731497888375 | 2024-11-13 19:38:08 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 811afc50f5c846db97e0f1eb9ae90184 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 168 | 1731497888376 | 2024-11-13 19:38:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 811afc50f5c846db97e0f1eb9ae90184 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1731497888413 | 2024-11-13 19:38:08 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | TomcatWebServer | start | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30766 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1731497888440 | 2024-11-13 19:38:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30793 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1731497888452 | 2024-11-13 19:38:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30806 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1731497888453 | 2024-11-13 19:38:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30806 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1731497888484 | 2024-11-13 19:38:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30837 | 0 | - | - | - | - | main c.t.g.Application Started Application in 31.418 seconds (JVM running for 32.045)

info | 1 | 1731497888502 | 2024-11-13 19:38:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30855 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1731497888502 | 2024-11-13 19:38:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | Application | main | d44e45ac379848eb941e27d3579cf5c8 | - | - | - | - | 30855 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 238 | 1731497888506 | 2024-11-13 19:38:08 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | cb24872354c84a1bb9b04b539eef2c64 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 175 | 1731497893152 | 2024-11-13 19:38:13 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 3dffba2697254cd7af3e5ee1d2c8ca70 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 175 | 1731497893152 | 2024-11-13 19:38:13 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 3dffba2697254cd7af3e5ee1d2c8ca70 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 175 | 1731497893155 | 2024-11-13 19:38:13 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 3dffba2697254cd7af3e5ee1d2c8ca70 | - | - | - | - | 3 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 3 ms

info | 168 | 1731497898376 | 2024-11-13 19:38:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 811afc50f5c846db97e0f1eb9ae90184 | - | - | - | - | 10002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1731497908376 | 2024-11-13 19:38:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 811afc50f5c846db97e0f1eb9ae90184 | - | - | - | - | 20001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1731497913458 | 2024-11-13 19:38:33 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | d08b42ca42e64b4d804639310ee1bec1 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1731497913492 | 2024-11-13 19:38:33 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | d08b42ca42e64b4d804639310ee1bec1 | - | - | - | - | 34 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1731497913493 | 2024-11-13 19:38:33 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | d08b42ca42e64b4d804639310ee1bec1 | - | - | - | - | 34 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1731497913503 | 2024-11-13 19:38:33 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | AbstractApplicationContext | destroyBeans | d08b42ca42e64b4d804639310ee1bec1 | - | - | - | - | 44 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

