info | 1 | 1753414317909 | 2025-07-25 11:31:57 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 87436 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 16 | 1753414317901 | 2025-07-25 11:31:57 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 6ee40efff5c94fe9ac0f736431d2d2c7 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753414317921 | 2025-07-25 11:31:57 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753414318469 | 2025-07-25 11:31:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 553 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753414318471 | 2025-07-25 11:31:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 559 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753414318477 | 2025-07-25 11:31:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 561 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753414318479 | 2025-07-25 11:31:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 563 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753414318489 | 2025-07-25 11:31:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 573 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753414318546 | 2025-07-25 11:31:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 630 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753414318590 | 2025-07-25 11:31:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 674 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753414318592 | 2025-07-25 11:31:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 676 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753414318593 | 2025-07-25 11:31:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 677 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753414318687 | 2025-07-25 11:31:58 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 771 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753414320846 | 2025-07-25 11:32:00 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 2930 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753414320848 | 2025-07-25 11:32:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 2932 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753414320899 | 2025-07-25 11:32:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 2983 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 45 ms. Found 0 JPA repository interfaces.

info | 1 | 1753414320911 | 2025-07-25 11:32:00 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 2995 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753414320912 | 2025-07-25 11:32:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 2996 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753414320947 | 2025-07-25 11:32:00 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 3031 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.

info | 1 | 1753414321833 | 2025-07-25 11:32:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 3921 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$fc032273] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414321854 | 2025-07-25 11:32:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 3939 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$7b0967eb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414321915 | 2025-07-25 11:32:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 4000 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$f97272b4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414321921 | 2025-07-25 11:32:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 4005 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414321975 | 2025-07-25 11:32:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 4060 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414321980 | 2025-07-25 11:32:01 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 4064 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414322617 | 2025-07-25 11:32:02 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 4702 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753414322634 | 2025-07-25 11:32:02 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 4718 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753414322635 | 2025-07-25 11:32:02 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 4719 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753414322744 | 2025-07-25 11:32:02 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 4829 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753414337430 | 2025-07-25 11:32:17 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 19514 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753414337507 | 2025-07-25 11:32:17 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 19591 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753414337547 | 2025-07-25 11:32:17 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 19631 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753414337649 | 2025-07-25 11:32:17 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 19733 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753414337750 | 2025-07-25 11:32:17 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 19834 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753414337884 | 2025-07-25 11:32:17 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 19968 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753414337893 | 2025-07-25 11:32:17 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 19977 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753414345092 | 2025-07-25 11:32:25 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 27177 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753414345097 | 2025-07-25 11:32:25 | v2/TomcatWebServer/stopTomcat | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | TomcatWebServer | stopTomcat | 0f99fd3b5d40484c9f95034b65a0cbaa | - | - | - | - | 27181 | 0 | - | - | - | - | main o.a.c.c.StandardService Stopping service [Tomcat]

info | 1 | 1753414361433 | 2025-07-25 11:32:41 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 87729 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 16 | 1753414361409 | 2025-07-25 11:32:41 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | cbf5c9f22d07410ba633f6f044b00d69 | - | - | - | - | 7 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753414361444 | 2025-07-25 11:32:41 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 8 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753414361936 | 2025-07-25 11:32:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 501 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753414361939 | 2025-07-25 11:32:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 503 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753414361941 | 2025-07-25 11:32:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 505 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753414361943 | 2025-07-25 11:32:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 507 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753414361946 | 2025-07-25 11:32:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 510 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753414361983 | 2025-07-25 11:32:41 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 547 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753414362015 | 2025-07-25 11:32:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 579 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753414362017 | 2025-07-25 11:32:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 581 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753414362017 | 2025-07-25 11:32:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 581 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753414362065 | 2025-07-25 11:32:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 629 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753414364028 | 2025-07-25 11:32:44 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 2592 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753414364029 | 2025-07-25 11:32:44 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 2593 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753414364075 | 2025-07-25 11:32:44 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 2639 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 41 ms. Found 0 JPA repository interfaces.

info | 1 | 1753414364087 | 2025-07-25 11:32:44 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 2651 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753414364088 | 2025-07-25 11:32:44 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 2652 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753414364119 | 2025-07-25 11:32:44 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 2683 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.

info | 1 | 1753414364820 | 2025-07-25 11:32:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 3384 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$d31606bd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414364837 | 2025-07-25 11:32:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 3402 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$521c4c35] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414364902 | 2025-07-25 11:32:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 3466 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$d08556fe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414364907 | 2025-07-25 11:32:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 3471 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414364965 | 2025-07-25 11:32:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 3529 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414364969 | 2025-07-25 11:32:44 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 3534 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414365556 | 2025-07-25 11:32:45 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 4120 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753414365565 | 2025-07-25 11:32:45 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 4129 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753414365565 | 2025-07-25 11:32:45 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 4129 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753414365653 | 2025-07-25 11:32:45 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 4217 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753414381638 | 2025-07-25 11:33:01 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 20202 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753414381733 | 2025-07-25 11:33:01 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 20297 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753414381786 | 2025-07-25 11:33:01 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 20351 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753414381938 | 2025-07-25 11:33:01 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 20502 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753414382034 | 2025-07-25 11:33:02 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 20598 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753414382204 | 2025-07-25 11:33:02 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 20768 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753414382214 | 2025-07-25 11:33:02 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 20778 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753414386136 | 2025-07-25 11:33:06 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 24700 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753414386140 | 2025-07-25 11:33:06 | v2/TomcatWebServer/stopTomcat | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | TomcatWebServer | stopTomcat | 9e2eef8f5aec401593b57056b9fea3f8 | - | - | - | - | 24704 | 0 | - | - | - | - | main o.a.c.c.StandardService Stopping service [Tomcat]

info | 1 | 1753414593557 | 2025-07-25 11:36:33 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 89118 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 16 | 1753414593553 | 2025-07-25 11:36:33 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | d4854b5463a54dd296388e61b504a342 | - | - | - | - | 5 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753414593572 | 2025-07-25 11:36:33 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753414594082 | 2025-07-25 11:36:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 516 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753414594085 | 2025-07-25 11:36:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 519 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753414594086 | 2025-07-25 11:36:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 521 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753414594089 | 2025-07-25 11:36:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 523 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753414594092 | 2025-07-25 11:36:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 526 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753414594132 | 2025-07-25 11:36:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 566 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753414594168 | 2025-07-25 11:36:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 602 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753414594169 | 2025-07-25 11:36:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 604 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753414594170 | 2025-07-25 11:36:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 604 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753414594218 | 2025-07-25 11:36:34 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 652 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753414596563 | 2025-07-25 11:36:36 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 2997 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753414596564 | 2025-07-25 11:36:36 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 2998 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753414596613 | 2025-07-25 11:36:36 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 3047 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 42 ms. Found 0 JPA repository interfaces.

info | 1 | 1753414596623 | 2025-07-25 11:36:36 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 3058 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753414596624 | 2025-07-25 11:36:36 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 3059 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753414596657 | 2025-07-25 11:36:36 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 3091 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.

info | 1 | 1753414597550 | 2025-07-25 11:36:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 3988 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$9c2853c4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414597579 | 2025-07-25 11:36:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 4013 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$1b2e993c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414597649 | 2025-07-25 11:36:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 4083 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$9997a405] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414597656 | 2025-07-25 11:36:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 4091 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414597748 | 2025-07-25 11:36:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 4182 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414597755 | 2025-07-25 11:36:37 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 4190 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414598367 | 2025-07-25 11:36:38 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 4802 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753414598381 | 2025-07-25 11:36:38 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 4815 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753414598381 | 2025-07-25 11:36:38 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 4815 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753414598490 | 2025-07-25 11:36:38 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 4925 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753414614157 | 2025-07-25 11:36:54 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 20591 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753414614276 | 2025-07-25 11:36:54 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 20710 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753414614311 | 2025-07-25 11:36:54 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 20745 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753414614416 | 2025-07-25 11:36:54 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 20850 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753414614504 | 2025-07-25 11:36:54 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 20938 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753414614613 | 2025-07-25 11:36:54 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 21047 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753414614621 | 2025-07-25 11:36:54 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 21055 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753414618296 | 2025-07-25 11:36:58 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 24731 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753414618300 | 2025-07-25 11:36:58 | v2/TomcatWebServer/stopTomcat | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | TomcatWebServer | stopTomcat | c4d2e0374f134e809df71bbab25e31a2 | - | - | - | - | 24734 | 0 | - | - | - | - | main o.a.c.c.StandardService Stopping service [Tomcat]

info | 16 | 1753414659733 | 2025-07-25 11:37:39 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | aac5ac838112484cb2e49da83b777a8e | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753414659738 | 2025-07-25 11:37:39 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 89519 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1753414659750 | 2025-07-25 11:37:39 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753414660241 | 2025-07-25 11:37:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 497 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753414660245 | 2025-07-25 11:37:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 500 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753414660250 | 2025-07-25 11:37:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 505 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753414660252 | 2025-07-25 11:37:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 507 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753414660253 | 2025-07-25 11:37:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 508 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753414660289 | 2025-07-25 11:37:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 544 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753414660321 | 2025-07-25 11:37:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 576 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753414660323 | 2025-07-25 11:37:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 578 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753414660323 | 2025-07-25 11:37:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 578 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753414660370 | 2025-07-25 11:37:40 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 625 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753414662478 | 2025-07-25 11:37:42 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 2733 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753414662480 | 2025-07-25 11:37:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 2735 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753414662533 | 2025-07-25 11:37:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 2789 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 48 ms. Found 0 JPA repository interfaces.

info | 1 | 1753414662544 | 2025-07-25 11:37:42 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 2799 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753414662546 | 2025-07-25 11:37:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 2801 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753414662599 | 2025-07-25 11:37:42 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 2855 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 44 ms. Found 0 Redis repository interfaces.

info | 1 | 1753414663516 | 2025-07-25 11:37:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 3774 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$c057830] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414663550 | 2025-07-25 11:37:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 3805 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$8b0bbda8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414663623 | 2025-07-25 11:37:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 3878 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$974c871] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414663628 | 2025-07-25 11:37:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 3883 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414663691 | 2025-07-25 11:37:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 3946 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414663698 | 2025-07-25 11:37:43 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 3954 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753414664256 | 2025-07-25 11:37:44 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 4511 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753414664271 | 2025-07-25 11:37:44 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 4526 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753414664271 | 2025-07-25 11:37:44 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 4526 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753414664363 | 2025-07-25 11:37:44 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 4618 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753414680541 | 2025-07-25 11:38:00 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 20796 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753414680615 | 2025-07-25 11:38:00 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 20870 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753414680650 | 2025-07-25 11:38:00 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 20905 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753414680742 | 2025-07-25 11:38:00 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 20998 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753414680842 | 2025-07-25 11:38:00 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 21097 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753414680957 | 2025-07-25 11:38:00 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 21212 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753414680964 | 2025-07-25 11:38:00 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 21219 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753414684639 | 2025-07-25 11:38:04 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 24894 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753414684988 | 2025-07-25 11:38:04 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 25243 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753414685012 | 2025-07-25 11:38:05 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 25267 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753414685227 | 2025-07-25 11:38:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | e7f8c34b26754f76afef0492389a0e38 | - | - | - | - | 1 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753414685261 | 2025-07-25 11:38:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | e7f8c34b26754f76afef0492389a0e38 | - | - | - | - | 34 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753414685271 | 2025-07-25 11:38:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | e7f8c34b26754f76afef0492389a0e38 | - | - | - | - | 44 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753414685381 | 2025-07-25 11:38:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | e7f8c34b26754f76afef0492389a0e38 | - | - | - | - | 154 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 108 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753414685387 | 2025-07-25 11:38:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | e7f8c34b26754f76afef0492389a0e38 | - | - | - | - | 161 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753414685396 | 2025-07-25 11:38:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | e7f8c34b26754f76afef0492389a0e38 | - | - | - | - | 169 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753414685403 | 2025-07-25 11:38:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | e7f8c34b26754f76afef0492389a0e38 | - | - | - | - | 176 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753414685516 | 2025-07-25 11:38:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | e7f8c34b26754f76afef0492389a0e38 | - | - | - | - | 290 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 111 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753414688596 | 2025-07-25 11:38:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 28851 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753414689489 | 2025-07-25 11:38:09 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 29744 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@1341b7cd with [org.springframework.security.web.session.DisableEncodeUrlFilter@372787b5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2ce8b03b, org.springframework.security.web.context.SecurityContextPersistenceFilter@399a7130, org.springframework.security.web.header.HeaderWriterFilter@6f517348, org.springframework.security.web.authentication.logout.LogoutFilter@198a9243, org.springframework.web.filter.CorsFilter@1eb738e5, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@5647e00b, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@6b6e4c41, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@189a2927, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@18473bda, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@750f713e, org.springframework.security.web.session.SessionManagementFilter@636f7adf, org.springframework.security.web.access.ExceptionTranslationFilter@21742725, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5f389d8f]

info | 1 | 1753414689513 | 2025-07-25 11:38:09 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 29768 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753414689632 | 2025-07-25 11:38:09 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 29887 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753414689634 | 2025-07-25 11:38:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 29889 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753414689636 | 2025-07-25 11:38:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 29892 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753414689639 | 2025-07-25 11:38:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 29895 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753414689643 | 2025-07-25 11:38:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 29898 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753414689644 | 2025-07-25 11:38:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 29899 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753414689644 | 2025-07-25 11:38:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 29899 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753414689996 | 2025-07-25 11:38:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30251 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753414690194 | 2025-07-25 11:38:10 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30450 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753414690195 | 2025-07-25 11:38:10 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30450 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753414690195 | 2025-07-25 11:38:10 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30450 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753414690195 | 2025-07-25 11:38:10 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30451 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753414690196 | 2025-07-25 11:38:10 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30451 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753414690199 | 2025-07-25 11:38:10 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30455 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753414690432 | 2025-07-25 11:38:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30688 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753414690433 | 2025-07-25 11:38:10 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30688 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753414690446 | 2025-07-25 11:38:10 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30701 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@6cbe9c78, tech.powerjob.worker.actors.ProcessorTrackerActor@4fe163ab, tech.powerjob.worker.actors.WorkerActor@3bbfaec8])

info | 1 | 1753414690481 | 2025-07-25 11:38:10 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30736 | 0 | - | - | - | - | main o.r.Reflections Reflections took 25 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753414690487 | 2025-07-25 11:38:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30743 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1753414690488 | 2025-07-25 11:38:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30743 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@4095c586

info | 1 | 1753414690488 | 2025-07-25 11:38:10 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30743 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753414690489 | 2025-07-25 11:38:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30744 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753414690491 | 2025-07-25 11:38:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 30746 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 125 | 1753414690983 | 2025-07-25 11:38:10 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753414691415 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31670 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753414691415 | 2025-07-25 11:38:11 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31670 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31671 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31671 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31671 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31671 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31671 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31671 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31671 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31671 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31671 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31671 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753414691416 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31672 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753414691417 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31672 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753414691417 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31672 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753414691418 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31673 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753414691420 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31675 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753414691421 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31676 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753414691421 | 2025-07-25 11:38:11 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 31677 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 932.4 ms

info | 1 | 1753414691779 | 2025-07-25 11:38:11 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32034 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753414691788 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32043 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753414691788 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32043 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753414691795 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32050 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753414691996 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32252 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753414691997 | 2025-07-25 11:38:11 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32252 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/f13c820318ca4e3b934eeab8e644b89a/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753414692003 | 2025-07-25 11:38:12 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32258 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/f13c820318ca4e3b934eeab8e644b89a/] on JVM exit successfully

info | 1 | 1753414692021 | 2025-07-25 11:38:12 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32276 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753414692022 | 2025-07-25 11:38:12 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32277 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 2.390 s, congratulations!

info | 158 | 1753414692027 | 2025-07-25 11:38:12 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 44a5de27834f4e32b775ba672592aa09 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 158 | 1753414692028 | 2025-07-25 11:38:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 44a5de27834f4e32b775ba672592aa09 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753414692090 | 2025-07-25 11:38:12 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32345 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753414692116 | 2025-07-25 11:38:12 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32372 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753414692133 | 2025-07-25 11:38:12 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32388 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753414692133 | 2025-07-25 11:38:12 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32388 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753414692164 | 2025-07-25 11:38:12 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32419 | 0 | - | - | - | - | main c.t.g.Application Started Application in 32.889 seconds (JVM running for 33.349)

info | 1 | 1753414692181 | 2025-07-25 11:38:12 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32436 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753414692181 | 2025-07-25 11:38:12 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 32436 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 228 | 1753414695128 | 2025-07-25 11:38:15 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 5b2afb355da2451cbd67d85443b83279 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 1 | 1753414695264 | 2025-07-25 11:38:15 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.641 | ************** | - | 2 | Application | main | 3e8b50b24aff4c86a279c830b78970ad | - | - | - | - | 35532 | 0 | - | - | - | - | main c.t.c.c.c.SoaClient register http client : [http://mp-finance-trade-api.test1.hbmonitor.com/mp-finance-trade-api/api] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 158 | 1753414733127 | 2025-07-25 11:38:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 44a5de27834f4e32b775ba672592aa09 | - | - | - | - | 41119 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1753414733164 | 2025-07-25 11:38:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 44a5de27834f4e32b775ba672592aa09 | - | - | - | - | 41138 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1753414733169 | 2025-07-25 11:38:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 44a5de27834f4e32b775ba672592aa09 | - | - | - | - | 41142 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1753414733170 | 2025-07-25 11:38:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 44a5de27834f4e32b775ba672592aa09 | - | - | - | - | 41143 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 29 | 1753414733914 | 2025-07-25 11:38:53 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | eaa786ac250d413c877c235a42a7cb03 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 29 | 1753414733954 | 2025-07-25 11:38:53 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | eaa786ac250d413c877c235a42a7cb03 | - | - | - | - | 41 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 29 | 1753414733956 | 2025-07-25 11:38:53 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | eaa786ac250d413c877c235a42a7cb03 | - | - | - | - | 42 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 29 | 1753414733977 | 2025-07-25 11:38:53 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | eaa786ac250d413c877c235a42a7cb03 | - | - | - | - | 63 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753415438084 | 2025-07-25 11:50:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 94163 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 16 | 1753415438077 | 2025-07-25 11:50:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 8927247164b14a3799b172450b3f708e | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753415438099 | 2025-07-25 11:50:38 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 8 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753415438622 | 2025-07-25 11:50:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 531 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753415438625 | 2025-07-25 11:50:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 535 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753415438629 | 2025-07-25 11:50:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 538 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753415438631 | 2025-07-25 11:50:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 540 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753415438633 | 2025-07-25 11:50:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 542 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753415438679 | 2025-07-25 11:50:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 588 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753415438712 | 2025-07-25 11:50:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 621 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753415438714 | 2025-07-25 11:50:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 623 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753415438714 | 2025-07-25 11:50:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 623 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753415438763 | 2025-07-25 11:50:38 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 673 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753415440842 | 2025-07-25 11:50:40 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 2751 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753415440843 | 2025-07-25 11:50:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 2753 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753415440893 | 2025-07-25 11:50:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 2802 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 42 ms. Found 0 JPA repository interfaces.

info | 1 | 1753415440904 | 2025-07-25 11:50:40 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 2813 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753415440905 | 2025-07-25 11:50:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 2814 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753415440934 | 2025-07-25 11:50:40 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 2843 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.

info | 1 | 1753415441779 | 2025-07-25 11:50:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 3689 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$c0ef4c86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415441797 | 2025-07-25 11:50:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 3706 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$3ff591fe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415441870 | 2025-07-25 11:50:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 3779 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$be5e9cc7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415441876 | 2025-07-25 11:50:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 3785 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415441934 | 2025-07-25 11:50:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 3843 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415441937 | 2025-07-25 11:50:41 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 3846 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415442518 | 2025-07-25 11:50:42 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 4427 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753415442528 | 2025-07-25 11:50:42 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 4437 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753415442529 | 2025-07-25 11:50:42 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 4438 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753415442636 | 2025-07-25 11:50:42 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 4545 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753415457768 | 2025-07-25 11:50:57 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 19677 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753415457843 | 2025-07-25 11:50:57 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 19752 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753415457878 | 2025-07-25 11:50:57 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 19787 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753415457990 | 2025-07-25 11:50:57 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 19899 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753415458063 | 2025-07-25 11:50:58 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 19972 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753415458173 | 2025-07-25 11:50:58 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 20082 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753415458179 | 2025-07-25 11:50:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 20088 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753415461473 | 2025-07-25 11:51:01 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 23382 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753415461816 | 2025-07-25 11:51:01 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 23725 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753415461845 | 2025-07-25 11:51:01 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 23754 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753415462075 | 2025-07-25 11:51:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 0005ed0eba404a7d98bb7d71cd9280a3 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753415462103 | 2025-07-25 11:51:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 0005ed0eba404a7d98bb7d71cd9280a3 | - | - | - | - | 27 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753415462114 | 2025-07-25 11:51:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 0005ed0eba404a7d98bb7d71cd9280a3 | - | - | - | - | 38 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753415462270 | 2025-07-25 11:51:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 0005ed0eba404a7d98bb7d71cd9280a3 | - | - | - | - | 194 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 154 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753415462276 | 2025-07-25 11:51:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 0005ed0eba404a7d98bb7d71cd9280a3 | - | - | - | - | 200 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753415462306 | 2025-07-25 11:51:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 0005ed0eba404a7d98bb7d71cd9280a3 | - | - | - | - | 230 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753415462318 | 2025-07-25 11:51:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 0005ed0eba404a7d98bb7d71cd9280a3 | - | - | - | - | 242 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753415462419 | 2025-07-25 11:51:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 0005ed0eba404a7d98bb7d71cd9280a3 | - | - | - | - | 343 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 98 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753415465340 | 2025-07-25 11:51:05 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 27250 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753415466067 | 2025-07-25 11:51:06 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 27976 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@7eeef604 with [org.springframework.security.web.session.DisableEncodeUrlFilter@3dbadc3c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2b287eb8, org.springframework.security.web.context.SecurityContextPersistenceFilter@3179f8eb, org.springframework.security.web.header.HeaderWriterFilter@494d0fca, org.springframework.security.web.authentication.logout.LogoutFilter@277e2871, org.springframework.web.filter.CorsFilter@2393b885, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@d9347e, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@c8f4128, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4e083478, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2367390f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5d8c86b, org.springframework.security.web.session.SessionManagementFilter@23488e7a, org.springframework.security.web.access.ExceptionTranslationFilter@114629f6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5308a182]

info | 1 | 1753415466090 | 2025-07-25 11:51:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28000 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753415466209 | 2025-07-25 11:51:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28118 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753415466211 | 2025-07-25 11:51:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28120 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753415466212 | 2025-07-25 11:51:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28121 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753415466215 | 2025-07-25 11:51:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28124 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753415466218 | 2025-07-25 11:51:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28127 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753415466219 | 2025-07-25 11:51:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28128 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753415466219 | 2025-07-25 11:51:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28128 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753415466435 | 2025-07-25 11:51:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28345 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753415466519 | 2025-07-25 11:51:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28428 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753415466519 | 2025-07-25 11:51:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28428 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753415466519 | 2025-07-25 11:51:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28429 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753415466520 | 2025-07-25 11:51:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28429 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753415466520 | 2025-07-25 11:51:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28429 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753415466521 | 2025-07-25 11:51:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28431 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753415466589 | 2025-07-25 11:51:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28498 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753415466590 | 2025-07-25 11:51:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28499 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753415466601 | 2025-07-25 11:51:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28510 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@10013c81, tech.powerjob.worker.actors.ProcessorTrackerActor@41896c9c, tech.powerjob.worker.actors.WorkerActor@5a1c721])

info | 1 | 1753415466642 | 2025-07-25 11:51:06 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28551 | 0 | - | - | - | - | main o.r.Reflections Reflections took 30 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753415466658 | 2025-07-25 11:51:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28567 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1753415466660 | 2025-07-25 11:51:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28570 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@25705886

info | 1 | 1753415466661 | 2025-07-25 11:51:06 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28570 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753415466663 | 2025-07-25 11:51:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28573 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753415466671 | 2025-07-25 11:51:06 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 28581 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 126 | 1753415467252 | 2025-07-25 11:51:07 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753415467611 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29520 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753415467611 | 2025-07-25 11:51:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753415467612 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753415467612 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753415467612 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753415467612 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753415467612 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753415467612 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753415467612 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753415467612 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753415467612 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753415467612 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29521 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753415467613 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753415467613 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753415467613 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29522 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753415467614 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29524 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753415467617 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29526 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753415467617 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29526 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753415467617 | 2025-07-25 11:51:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29527 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 956.4 ms

info | 1 | 1753415467694 | 2025-07-25 11:51:07 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29603 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753415467699 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29608 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753415467699 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29608 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753415467704 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29613 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753415467893 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29802 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753415467893 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29802 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/dfbcb3dab34e44f7b7db2be2157090cb/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753415467898 | 2025-07-25 11:51:07 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29807 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/dfbcb3dab34e44f7b7db2be2157090cb/] on JVM exit successfully

info | 1 | 1753415467915 | 2025-07-25 11:51:07 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29824 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753415467916 | 2025-07-25 11:51:07 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29825 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.707 s, congratulations!

info | 160 | 1753415467922 | 2025-07-25 11:51:07 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 51082bc627484935b2b7df61b41309a7 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 160 | 1753415467922 | 2025-07-25 11:51:07 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 51082bc627484935b2b7df61b41309a7 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753415467959 | 2025-07-25 11:51:07 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29868 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753415467989 | 2025-07-25 11:51:07 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29899 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753415468005 | 2025-07-25 11:51:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29914 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753415468005 | 2025-07-25 11:51:08 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29914 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753415468029 | 2025-07-25 11:51:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29938 | 0 | - | - | - | - | main c.t.g.Application Started Application in 30.493 seconds (JVM running for 31.033)

info | 1 | 1753415468046 | 2025-07-25 11:51:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29956 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753415468047 | 2025-07-25 11:51:08 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 3b1c07ec9e8b4e4185754bdee68d7d3e | - | - | - | - | 29956 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 230 | 1753415472527 | 2025-07-25 11:51:12 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 2ea0c4229771405899f320e6ee5a3ee4 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 165 | 1753415605824 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 11 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605854 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 22 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605857 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 24 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605859 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 27 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605860 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 29 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605863 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 30 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605864 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 31 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605865 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 32 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605866 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 34 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605867 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 35 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605869 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 37 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605870 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 37 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1753415605873 | 2025-07-25 11:53:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 98723cc25a294662a3a9b7014bc2a17a | - | - | - | - | 41 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 29 | 1753415606937 | 2025-07-25 11:53:26 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 29 | 1753415606965 | 2025-07-25 11:53:26 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | ******************************** | - | - | - | - | 28 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 29 | 1753415606966 | 2025-07-25 11:53:26 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | ******************************** | - | - | - | - | 29 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 29 | 1753415606976 | 2025-07-25 11:53:26 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | ******************************** | - | - | - | - | 39 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753415609552 | 2025-07-25 11:53:29 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 3 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 95190 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 16 | 1753415609546 | 2025-07-25 11:53:29 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 4e8645c24ec147b6a064469471b23594 | - | - | - | - | 3 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753415609564 | 2025-07-25 11:53:29 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753415610093 | 2025-07-25 11:53:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 533 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753415610095 | 2025-07-25 11:53:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 536 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753415610098 | 2025-07-25 11:53:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 538 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753415610102 | 2025-07-25 11:53:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 542 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753415610105 | 2025-07-25 11:53:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 545 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753415610145 | 2025-07-25 11:53:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 585 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753415610177 | 2025-07-25 11:53:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 617 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753415610178 | 2025-07-25 11:53:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 618 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753415610178 | 2025-07-25 11:53:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 619 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753415610227 | 2025-07-25 11:53:30 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 667 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753415612184 | 2025-07-25 11:53:32 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 2624 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753415612185 | 2025-07-25 11:53:32 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 2625 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753415612226 | 2025-07-25 11:53:32 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 2667 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 37 ms. Found 0 JPA repository interfaces.

info | 1 | 1753415612234 | 2025-07-25 11:53:32 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 2674 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753415612235 | 2025-07-25 11:53:32 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 2675 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753415612260 | 2025-07-25 11:53:32 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 2700 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1753415613049 | 2025-07-25 11:53:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 3492 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b9aefdf0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415613071 | 2025-07-25 11:53:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 3511 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$38b54368] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415613132 | 2025-07-25 11:53:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 3572 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$b71e4e31] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415613137 | 2025-07-25 11:53:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 3578 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415613196 | 2025-07-25 11:53:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 3637 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415613201 | 2025-07-25 11:53:33 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 3641 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415613780 | 2025-07-25 11:53:33 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 4220 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753415613789 | 2025-07-25 11:53:33 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 4229 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753415613789 | 2025-07-25 11:53:33 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 4229 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753415613888 | 2025-07-25 11:53:33 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 4328 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753415629176 | 2025-07-25 11:53:49 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 19616 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753415629253 | 2025-07-25 11:53:49 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 19693 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753415629282 | 2025-07-25 11:53:49 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 19723 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753415629369 | 2025-07-25 11:53:49 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 19809 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753415629455 | 2025-07-25 11:53:49 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 19895 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753415629617 | 2025-07-25 11:53:49 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 20057 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753415629624 | 2025-07-25 11:53:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 20065 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753415633291 | 2025-07-25 11:53:53 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 23731 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753415633597 | 2025-07-25 11:53:53 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 24037 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753415633621 | 2025-07-25 11:53:53 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 24061 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753415633804 | 2025-07-25 11:53:53 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2be50b1988344b1fa8a183ba6e09168c | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753415633829 | 2025-07-25 11:53:53 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2be50b1988344b1fa8a183ba6e09168c | - | - | - | - | 25 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753415633839 | 2025-07-25 11:53:53 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2be50b1988344b1fa8a183ba6e09168c | - | - | - | - | 35 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753415633979 | 2025-07-25 11:53:53 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2be50b1988344b1fa8a183ba6e09168c | - | - | - | - | 175 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 138 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753415633998 | 2025-07-25 11:53:53 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2be50b1988344b1fa8a183ba6e09168c | - | - | - | - | 195 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753415634021 | 2025-07-25 11:53:54 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2be50b1988344b1fa8a183ba6e09168c | - | - | - | - | 217 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753415634041 | 2025-07-25 11:53:54 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2be50b1988344b1fa8a183ba6e09168c | - | - | - | - | 238 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753415634155 | 2025-07-25 11:53:54 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 2be50b1988344b1fa8a183ba6e09168c | - | - | - | - | 351 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 101 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753415636944 | 2025-07-25 11:53:56 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 27384 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753415637687 | 2025-07-25 11:53:57 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28127 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@1cc21bf1 with [org.springframework.security.web.session.DisableEncodeUrlFilter@ef77a7b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1fa6f90f, org.springframework.security.web.context.SecurityContextPersistenceFilter@3fbb2f6d, org.springframework.security.web.header.HeaderWriterFilter@27e0aedb, org.springframework.security.web.authentication.logout.LogoutFilter@6225569, org.springframework.web.filter.CorsFilter@2aa17940, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@308bca8c, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@4c47e5ec, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2ce8b03b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e8b0e73, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@40d67206, org.springframework.security.web.session.SessionManagementFilter@631852a5, org.springframework.security.web.access.ExceptionTranslationFilter@63bb46a2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@d538268]

info | 1 | 1753415637703 | 2025-07-25 11:53:57 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28144 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753415637775 | 2025-07-25 11:53:57 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28215 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753415637777 | 2025-07-25 11:53:57 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28217 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753415637778 | 2025-07-25 11:53:57 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28218 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753415637780 | 2025-07-25 11:53:57 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28222 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753415637785 | 2025-07-25 11:53:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28225 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753415637785 | 2025-07-25 11:53:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28225 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753415637786 | 2025-07-25 11:53:57 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28226 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753415638135 | 2025-07-25 11:53:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28575 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753415638295 | 2025-07-25 11:53:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28736 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753415638296 | 2025-07-25 11:53:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28736 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753415638296 | 2025-07-25 11:53:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28736 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753415638296 | 2025-07-25 11:53:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28736 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753415638296 | 2025-07-25 11:53:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28736 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753415638297 | 2025-07-25 11:53:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28738 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753415638461 | 2025-07-25 11:53:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28901 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753415638462 | 2025-07-25 11:53:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28903 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753415638472 | 2025-07-25 11:53:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28912 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@45ada2d0, tech.powerjob.worker.actors.ProcessorTrackerActor@15f57269, tech.powerjob.worker.actors.WorkerActor@5ef2055f])

info | 1 | 1753415638502 | 2025-07-25 11:53:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28942 | 0 | - | - | - | - | main o.r.Reflections Reflections took 21 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753415638508 | 2025-07-25 11:53:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28948 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1753415638509 | 2025-07-25 11:53:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28949 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@73bac21e

info | 1 | 1753415638509 | 2025-07-25 11:53:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28949 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753415638510 | 2025-07-25 11:53:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28950 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753415638512 | 2025-07-25 11:53:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 28952 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 125 | 1753415638958 | 2025-07-25 11:53:58 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753415639313 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29753 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753415639313 | 2025-07-25 11:53:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29754 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753415639314 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29754 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753415639314 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29754 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753415639314 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29754 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753415639314 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29754 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753415639314 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29754 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753415639314 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29754 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753415639314 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29754 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753415639314 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29754 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753415639314 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29754 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753415639314 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29755 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753415639315 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29755 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753415639315 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29755 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753415639315 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29755 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753415639317 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29757 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753415639319 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29759 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753415639319 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29759 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753415639319 | 2025-07-25 11:53:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29760 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 810.0 ms

info | 1 | 1753415639520 | 2025-07-25 11:53:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29961 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753415639530 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29970 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753415639530 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29970 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753415639538 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 29978 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753415639765 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30206 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753415639766 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30206 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/00fa3c12808645eaa01f80fb933d3a35/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753415639771 | 2025-07-25 11:53:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30211 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/00fa3c12808645eaa01f80fb933d3a35/] on JVM exit successfully

info | 1 | 1753415639789 | 2025-07-25 11:53:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30229 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753415639790 | 2025-07-25 11:53:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30231 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 2.016 s, congratulations!

info | 161 | 1753415639796 | 2025-07-25 11:53:59 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 35b9f8749ee44056a8d63a48c150baeb | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 161 | 1753415639797 | 2025-07-25 11:53:59 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 35b9f8749ee44056a8d63a48c150baeb | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753415639846 | 2025-07-25 11:53:59 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30286 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753415639877 | 2025-07-25 11:53:59 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30317 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753415639896 | 2025-07-25 11:53:59 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30336 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753415639896 | 2025-07-25 11:53:59 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30336 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753415639927 | 2025-07-25 11:53:59 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30367 | 0 | - | - | - | - | main c.t.g.Application Started Application in 30.996 seconds (JVM running for 31.474)

info | 1 | 1753415639948 | 2025-07-25 11:53:59 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30388 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753415639948 | 2025-07-25 11:53:59 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | 9a90a789fff3456c9dcedfc927464d37 | - | - | - | - | 30388 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 231 | 1753415639955 | 2025-07-25 11:53:59 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 99bf11b1c9af4977a70c7975c0fad00c | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 166 | 1753415652597 | 2025-07-25 11:54:12 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | bc23a4e6ebe64293b2fc5b70981ddea6 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 29 | 1753415653675 | 2025-07-25 11:54:13 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 9ad2a4f276c04ec88cc6b5e4339ecc63 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 29 | 1753415653710 | 2025-07-25 11:54:13 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 9ad2a4f276c04ec88cc6b5e4339ecc63 | - | - | - | - | 35 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 29 | 1753415653713 | 2025-07-25 11:54:13 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | 9ad2a4f276c04ec88cc6b5e4339ecc63 | - | - | - | - | 37 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 29 | 1753415653731 | 2025-07-25 11:54:13 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | 9ad2a4f276c04ec88cc6b5e4339ecc63 | - | - | - | - | 55 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 16 | 1753415657802 | 2025-07-25 11:54:17 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | c6999017e0a94ec190f96a612a5fa8ec | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1753415657806 | 2025-07-25 11:54:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 95500 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1753415657819 | 2025-07-25 11:54:17 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1753415658337 | 2025-07-25 11:54:18 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 523 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1753415658339 | 2025-07-25 11:54:18 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 525 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1753415658341 | 2025-07-25 11:54:18 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 527 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1753415658344 | 2025-07-25 11:54:18 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 531 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1753415658348 | 2025-07-25 11:54:18 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 534 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1753415658389 | 2025-07-25 11:54:18 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 575 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1753415658424 | 2025-07-25 11:54:18 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 610 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1753415658426 | 2025-07-25 11:54:18 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 612 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1753415658426 | 2025-07-25 11:54:18 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 612 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1753415658472 | 2025-07-25 11:54:18 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 659 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1753415660417 | 2025-07-25 11:54:20 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 2603 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753415660418 | 2025-07-25 11:54:20 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 2605 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1753415660486 | 2025-07-25 11:54:20 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 2672 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 59 ms. Found 0 JPA repository interfaces.

info | 1 | 1753415660497 | 2025-07-25 11:54:20 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 2684 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1753415660499 | 2025-07-25 11:54:20 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 2685 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1753415660528 | 2025-07-25 11:54:20 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 2714 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.

info | 1 | 1753415661250 | 2025-07-25 11:54:21 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 3436 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$c0ef4c86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415661272 | 2025-07-25 11:54:21 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 3458 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$3ff591fe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415661342 | 2025-07-25 11:54:21 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 3528 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$be5e9cc7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415661346 | 2025-07-25 11:54:21 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 3533 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415661422 | 2025-07-25 11:54:21 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 3608 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415661426 | 2025-07-25 11:54:21 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 3613 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1753415661950 | 2025-07-25 11:54:21 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 4136 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1753415661957 | 2025-07-25 11:54:21 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 4144 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1753415661958 | 2025-07-25 11:54:21 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 4144 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1753415662058 | 2025-07-25 11:54:22 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 4244 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1753415677415 | 2025-07-25 11:54:37 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 19601 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1753415677498 | 2025-07-25 11:54:37 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 19684 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1753415677534 | 2025-07-25 11:54:37 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 19720 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1753415677628 | 2025-07-25 11:54:37 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 19814 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1753415677706 | 2025-07-25 11:54:37 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 19892 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1753415677852 | 2025-07-25 11:54:37 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 20039 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1753415677861 | 2025-07-25 11:54:37 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 20047 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1753415681866 | 2025-07-25 11:54:41 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 24052 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1753415682307 | 2025-07-25 11:54:42 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 24493 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1753415682333 | 2025-07-25 11:54:42 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 24519 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 43 | 1753415682563 | 2025-07-25 11:54:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a43f5ec389134ecda50bfb311a9fbde4 | - | - | - | - | 1 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 

info | 43 | 1753415682592 | 2025-07-25 11:54:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a43f5ec389134ecda50bfb311a9fbde4 | - | - | - | - | 29 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 43 | 1753415682601 | 2025-07-25 11:54:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a43f5ec389134ecda50bfb311a9fbde4 | - | - | - | - | 38 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 43 | 1753415682712 | 2025-07-25 11:54:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a43f5ec389134ecda50bfb311a9fbde4 | - | - | - | - | 149 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 109 ms to scan 311 urls, producing 0 keys and 0 values 

info | 43 | 1753415682718 | 2025-07-25 11:54:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a43f5ec389134ecda50bfb311a9fbde4 | - | - | - | - | 157 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 43 | 1753415682727 | 2025-07-25 11:54:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a43f5ec389134ecda50bfb311a9fbde4 | - | - | - | - | 164 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 43 | 1753415682754 | 2025-07-25 11:54:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a43f5ec389134ecda50bfb311a9fbde4 | - | - | - | - | 192 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 43 | 1753415682869 | 2025-07-25 11:54:42 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | a43f5ec389134ecda50bfb311a9fbde4 | - | - | - | - | 306 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 109 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1753415686399 | 2025-07-25 11:54:46 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 28586 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1753415687617 | 2025-07-25 11:54:47 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 29803 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@7dfc1f89 with [org.springframework.security.web.session.DisableEncodeUrlFilter@61450111, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@e44af47, org.springframework.security.web.context.SecurityContextPersistenceFilter@1ebb3621, org.springframework.security.web.header.HeaderWriterFilter@29347858, org.springframework.security.web.authentication.logout.LogoutFilter@4c47e5ec, org.springframework.web.filter.CorsFilter@1b2d91f8, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@10b5c86f, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@1f474cbe, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2dc9e445, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@65b388ef, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@25a1888, org.springframework.security.web.session.SessionManagementFilter@748b2d3a, org.springframework.security.web.access.ExceptionTranslationFilter@750ff293, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@158f4c22]

info | 1 | 1753415687645 | 2025-07-25 11:54:47 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 29831 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1753415687740 | 2025-07-25 11:54:47 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 29926 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1753415687742 | 2025-07-25 11:54:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 29928 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1753415687742 | 2025-07-25 11:54:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 29928 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1753415687745 | 2025-07-25 11:54:47 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 29931 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1753415687747 | 2025-07-25 11:54:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 29933 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1753415687747 | 2025-07-25 11:54:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 29933 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1753415687747 | 2025-07-25 11:54:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 29933 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en0 (en0)

info | 1 | 1753415687947 | 2025-07-25 11:54:47 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30133 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1753415688056 | 2025-07-25 11:54:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30242 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:awdl0 (awdl0)

info | 1 | 1753415688056 | 2025-07-25 11:54:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30243 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:llw0 (llw0)

info | 1 | 1753415688057 | 2025-07-25 11:54:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30243 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1753415688057 | 2025-07-25 11:54:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30243 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1753415688057 | 2025-07-25 11:54:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30243 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1753415688060 | 2025-07-25 11:54:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30246 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1753415688184 | 2025-07-25 11:54:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30370 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1753415688186 | 2025-07-25 11:54:48 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30372 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1753415688202 | 2025-07-25 11:54:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30389 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@7a853d3d, tech.powerjob.worker.actors.ProcessorTrackerActor@c5a7b77, tech.powerjob.worker.actors.WorkerActor@6a5be26f])

info | 1 | 1753415688245 | 2025-07-25 11:54:48 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30432 | 0 | - | - | - | - | main o.r.Reflections Reflections took 28 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1753415688254 | 2025-07-25 11:54:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30441 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1753415688256 | 2025-07-25 11:54:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30442 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@b2ef2f4

info | 1 | 1753415688256 | 2025-07-25 11:54:48 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30442 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1753415688257 | 2025-07-25 11:54:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30443 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1753415688260 | 2025-07-25 11:54:48 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 30446 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 123 | 1753415688720 | 2025-07-25 11:54:48 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1753415689092 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31278 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1753415689093 | 2025-07-25 11:54:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31279 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1753415689093 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31279 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1753415689093 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31279 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1753415689093 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31279 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753415689093 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31279 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1753415689093 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31279 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753415689093 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31279 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753415689093 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31279 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1753415689093 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31280 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1753415689094 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31280 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1753415689094 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31280 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1753415689094 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31280 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1753415689094 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31280 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1753415689094 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31280 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1753415689095 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31281 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1753415689097 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31284 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1753415689098 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31284 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1753415689098 | 2025-07-25 11:54:49 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31285 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 842.1 ms

info | 1 | 1753415689172 | 2025-07-25 11:54:49 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31358 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1753415689176 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31362 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1753415689176 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31362 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1753415689179 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31365 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1753415689381 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31568 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1753415689382 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31568 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/433ee67e9e0f4411bb7a209bde589995/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1753415689386 | 2025-07-25 11:54:49 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31572 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/433ee67e9e0f4411bb7a209bde589995/] on JVM exit successfully

info | 1 | 1753415689405 | 2025-07-25 11:54:49 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31591 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1753415689406 | 2025-07-25 11:54:49 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31592 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.666 s, congratulations!

info | 156 | 1753415689410 | 2025-07-25 11:54:49 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 295be33f4a39423cb18c614291791a89 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 156 | 1753415689411 | 2025-07-25 11:54:49 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 295be33f4a39423cb18c614291791a89 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1753415689463 | 2025-07-25 11:54:49 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | TomcatWebServer | start | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31650 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1753415689498 | 2025-07-25 11:54:49 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31684 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1753415689509 | 2025-07-25 11:54:49 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31695 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1753415689509 | 2025-07-25 11:54:49 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31695 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1753415689538 | 2025-07-25 11:54:49 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31724 | 0 | - | - | - | - | main c.t.g.Application Started Application in 32.2 seconds (JVM running for 32.672)

info | 1 | 1753415689559 | 2025-07-25 11:54:49 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31745 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1753415689559 | 2025-07-25 11:54:49 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.639 | ************** | - | 2 | Application | main | e8469aaf7aea4206a5123439536d47fe | - | - | - | - | 31745 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 226 | 1753415689565 | 2025-07-25 11:54:49 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | 6d48bfd24997440a816851a4f2668849 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 29 | 1753415693744 | 2025-07-25 11:54:53 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 8c92628ade8541c393fd04f515f8cb34 | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 29 | 1753415693793 | 2025-07-25 11:54:53 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | DefaultListableBeanFactory | destroySingletons | 8c92628ade8541c393fd04f515f8cb34 | - | - | - | - | 49 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 29 | 1753415693794 | 2025-07-25 11:54:53 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | NativeMethodAccessorImpl | invoke0 | 8c92628ade8541c393fd04f515f8cb34 | - | - | - | - | 50 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 29 | 1753415693808 | 2025-07-25 11:54:53 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | AbstractApplicationContext | destroyBeans | 8c92628ade8541c393fd04f515f8cb34 | - | - | - | - | 64 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

