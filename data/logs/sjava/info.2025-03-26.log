info | 15 | 1742969879517 | 2025-03-26 14:17:59 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 42180181c1e04defb8bb84ae4719b800 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1742969879522 | 2025-03-26 14:17:59 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Application | main | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 55347 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1742969879534 | 2025-03-26 14:17:59 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Application | main | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1742969880037 | 2025-03-26 14:18:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 508 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1742969880039 | 2025-03-26 14:18:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 511 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1742969880042 | 2025-03-26 14:18:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 513 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1742969880044 | 2025-03-26 14:18:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 515 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1742969880048 | 2025-03-26 14:18:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 519 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1742969880099 | 2025-03-26 14:18:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 570 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1742969880143 | 2025-03-26 14:18:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 614 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1742969880146 | 2025-03-26 14:18:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 617 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1742969880146 | 2025-03-26 14:18:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 617 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1742969880227 | 2025-03-26 14:18:00 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 698 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1742969882307 | 2025-03-26 14:18:02 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 2778 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1742969882308 | 2025-03-26 14:18:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 2779 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1742969882355 | 2025-03-26 14:18:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 2827 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 41 ms. Found 0 JPA repository interfaces.

info | 1 | 1742969882366 | 2025-03-26 14:18:02 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 2837 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1742969882367 | 2025-03-26 14:18:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 2838 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1742969882396 | 2025-03-26 14:18:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 2867 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1742969883209 | 2025-03-26 14:18:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3683 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$6f7c9687] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1742969883229 | 2025-03-26 14:18:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3700 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$ee82dbff] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1742969883292 | 2025-03-26 14:18:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3763 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$6cebe6c8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1742969883297 | 2025-03-26 14:18:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3768 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1742969883437 | 2025-03-26 14:18:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3908 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1742969883442 | 2025-03-26 14:18:03 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | ************** | - | 2 | AbstractBeanFactory | doGetBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3913 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1742969884038 | 2025-03-26 14:18:04 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | ************** | - | 2 | LifecycleBase | init | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 4509 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1742969884055 | 2025-03-26 14:18:04 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | ************** | - | 2 | TomcatWebServer | initialize | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 4526 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1742969884055 | 2025-03-26 14:18:04 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | ************** | - | 2 | LifecycleBase | start | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 4526 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1742969884144 | 2025-03-26 14:18:04 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | ************** | - | 2 | StandardContext | startInternal | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 4615 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1742969899952 | 2025-03-26 14:18:19 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | ************** | - | 2 | DataSourceUtils | getConnection | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 20424 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1742969900040 | 2025-03-26 14:18:20 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | ************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 20511 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1742969900089 | 2025-03-26 14:18:20 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 20560 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1742969900234 | 2025-03-26 14:18:20 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | ************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 20705 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1742969900314 | 2025-03-26 14:18:20 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | ************** | - | 2 | DialectFactoryImpl | determineDialect | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 20785 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1742969900473 | 2025-03-26 14:18:20 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | ************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 20944 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1742969900482 | 2025-03-26 14:18:20 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 20953 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1742969920975 | 2025-03-26 14:18:40 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | ************** | - | 2 | NativeMethodAccessorImpl | invoke | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 41447 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1742969932344 | 2025-03-26 14:18:52 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | ************** | - | 2 | AbstractLifecycle | init | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 52816 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1742969933197 | 2025-03-26 14:18:53 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | ************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 53669 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1742969934558 | 2025-03-26 14:18:54 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 032f464342034705825358c91c2269e0 | - | - | - | - | 1 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 465 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1742969935032 | 2025-03-26 14:18:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 032f464342034705825358c91c2269e0 | - | - | - | - | 475 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 385 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1742969935423 | 2025-03-26 14:18:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 032f464342034705825358c91c2269e0 | - | - | - | - | 866 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 376 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1742969942033 | 2025-03-26 14:19:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 032f464342034705825358c91c2269e0 | - | - | - | - | 7476 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6584 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1742969942421 | 2025-03-26 14:19:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 032f464342034705825358c91c2269e0 | - | - | - | - | 7863 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 381 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1742969942832 | 2025-03-26 14:19:02 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 032f464342034705825358c91c2269e0 | - | - | - | - | 8274 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 390 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1742969943226 | 2025-03-26 14:19:03 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 032f464342034705825358c91c2269e0 | - | - | - | - | 8669 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 373 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1742969949603 | 2025-03-26 14:19:09 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 032f464342034705825358c91c2269e0 | - | - | - | - | 15046 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6340 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1742969963870 | 2025-03-26 14:19:23 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 84343 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1742969986522 | 2025-03-26 14:19:46 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | ************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 106993 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@257490d2 with [org.springframework.security.web.session.DisableEncodeUrlFilter@1617f122, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@134323ec, org.springframework.security.web.context.SecurityContextPersistenceFilter@19599603, org.springframework.security.web.header.HeaderWriterFilter@1870e28d, org.springframework.security.web.authentication.logout.LogoutFilter@1464416c, org.springframework.web.filter.CorsFilter@492f79d1, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@50c20924, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@6c6e45d2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@514c684d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@64d394e5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6d9dba8d, org.springframework.security.web.session.SessionManagementFilter@33e99277, org.springframework.security.web.access.ExceptionTranslationFilter@3f09b882, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6a1de98d]

info | 1 | 1742969987096 | 2025-03-26 14:19:47 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 107568 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1742969990059 | 2025-03-26 14:19:50 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 110531 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1742969990073 | 2025-03-26 14:19:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 110545 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1742969990082 | 2025-03-26 14:19:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 110554 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1742969990097 | 2025-03-26 14:19:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 110568 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1742969990111 | 2025-03-26 14:19:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 110582 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1742969990113 | 2025-03-26 14:19:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 110584 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1742969990114 | 2025-03-26 14:19:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 110586 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1742969990866 | 2025-03-26 14:19:50 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 111338 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1742969991015 | 2025-03-26 14:19:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 111488 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1742969991028 | 2025-03-26 14:19:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 111501 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1742969991032 | 2025-03-26 14:19:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 111505 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1742969991043 | 2025-03-26 14:19:51 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 111515 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1742969991244 | 2025-03-26 14:19:51 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 111716 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1742969991267 | 2025-03-26 14:19:51 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 111739 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: **************, localBindPort: 27777; externalIp: **************, externalPort: 27777

info | 1 | 1742969991327 | 2025-03-26 14:19:51 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 111799 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=**************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@45d7158d, tech.powerjob.worker.actors.ProcessorTrackerActor@532247f5, tech.powerjob.worker.actors.WorkerActor@54ccb107])

info | 1 | 1742969991920 | 2025-03-26 14:19:51 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | ************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 112392 | 0 | - | - | - | - | main o.r.Reflections Reflections took 498 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1742969991968 | 2025-03-26 14:19:51 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 112439 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1742969991974 | 2025-03-26 14:19:51 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 112446 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@58c74364

info | 1 | 1742969991979 | 2025-03-26 14:19:51 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 112451 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@4e5ac0b4

info | 1 | 1742969991980 | 2025-03-26 14:19:51 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 112452 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1742969991985 | 2025-03-26 14:19:51 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 112457 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: **************:27777

info | 1 | 1742969992046 | 2025-03-26 14:19:52 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 112518 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 197 | 1742969994982 | 2025-03-26 14:19:54 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1742969998330 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118802 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1742969998336 | 2025-03-26 14:19:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118808 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1742969998340 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118812 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1742969998343 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118818 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1742969998349 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118821 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1742969998351 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118823 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1742969998353 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118825 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1742969998356 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118828 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1742969998358 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118830 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1742969998360 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118832 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1742969998362 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118834 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1742969998365 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118837 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1742969998368 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118840 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1742969998370 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118842 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1742969998372 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118844 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1742969998384 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118856 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1742969998397 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118869 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1742969998401 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118873 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1742969998407 | 2025-03-26 14:19:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | ************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 118886 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 6.426 s

info | 1 | 1742969998737 | 2025-03-26 14:19:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 119209 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1742969998780 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 119252 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1742969998782 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 119254 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1742969998799 | 2025-03-26 14:19:58 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 119271 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1742970000262 | 2025-03-26 14:20:00 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 120734 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1742970000264 | 2025-03-26 14:20:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 120735 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/9070f6703d864ab88fd301aeb633f618/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1742970000281 | 2025-03-26 14:20:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | ************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 120752 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/9070f6703d864ab88fd301aeb633f618/] on JVM exit successfully

info | 1 | 1742970000411 | 2025-03-26 14:20:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 120882 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1742970000416 | 2025-03-26 14:20:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | ************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 120888 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 10.36 s, congratulations!

info | 234 | 1742970000438 | 2025-03-26 14:20:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 234 | 1742970000441 | 2025-03-26 14:20:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 3 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1742970001828 | 2025-03-26 14:20:01 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | ************** | - | 2 | TomcatWebServer | start | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 122300 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1742970001905 | 2025-03-26 14:20:01 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 122376 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1742970001975 | 2025-03-26 14:20:01 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 122447 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1742970001977 | 2025-03-26 14:20:01 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | ************** | - | 2 | AbstractApplicationContext | finishRefresh | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 122449 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1742970002299 | 2025-03-26 14:20:02 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | ************** | - | 2 | Application | main | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 122771 | 0 | - | - | - | - | main c.t.g.Application Started Application in 123.18 seconds (JVM running for 123.658)

info | 1 | 1742970002450 | 2025-03-26 14:20:02 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | ************** | - | 2 | Application | main | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 122922 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1742970002451 | 2025-03-26 14:20:02 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | ************** | - | 2 | Application | main | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 122923 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 317 | 1742970006186 | 2025-03-26 14:20:06 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ChatRoomService | refreshChatCache | f604eab894844719ab3529ca3bf889c4 | - | - | - | - | 1 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 234 | 1742970010420 | 2025-03-26 14:20:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 9986 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970020417 | 2025-03-26 14:20:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 19980 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970030416 | 2025-03-26 14:20:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 29979 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970040417 | 2025-03-26 14:20:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 39980 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970050422 | 2025-03-26 14:20:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 49986 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970060418 | 2025-03-26 14:21:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 59984 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970070419 | 2025-03-26 14:21:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 69982 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970080421 | 2025-03-26 14:21:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 79984 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970090419 | 2025-03-26 14:21:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 89983 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 317 | 1742970092009 | 2025-03-26 14:21:32 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ChatRoomService | refreshChatCache | f604eab894844719ab3529ca3bf889c4 | - | - | - | - | 85825 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 234 | 1742970100422 | 2025-03-26 14:21:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 99988 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970110418 | 2025-03-26 14:21:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 109983 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970120421 | 2025-03-26 14:22:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 119985 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970130419 | 2025-03-26 14:22:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 129984 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970140417 | 2025-03-26 14:22:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 139981 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970150418 | 2025-03-26 14:22:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 149983 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 244 | 1742970160416 | 2025-03-26 14:22:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a5128219ae7a4b369be910b6b4f96b86 | - | - | - | - | 2 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 244 | 1742970170421 | 2025-03-26 14:22:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a5128219ae7a4b369be910b6b4f96b86 | - | - | - | - | 10006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970180516 | 2025-03-26 14:23:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 180082 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970190515 | 2025-03-26 14:23:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 190081 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 244 | 1742970200514 | 2025-03-26 14:23:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a5128219ae7a4b369be910b6b4f96b86 | - | - | - | - | 40100 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 244 | 1742970210516 | 2025-03-26 14:23:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a5128219ae7a4b369be910b6b4f96b86 | - | - | - | - | 50104 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 244 | 1742970220514 | 2025-03-26 14:23:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a5128219ae7a4b369be910b6b4f96b86 | - | - | - | - | 60098 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970230514 | 2025-03-26 14:23:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 230080 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970240513 | 2025-03-26 14:24:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 240076 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970250514 | 2025-03-26 14:24:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 250080 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970260513 | 2025-03-26 14:24:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 260078 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 254 | 1742970269882 | 2025-03-26 14:24:29 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | StandardWrapper | initServlet | 6d0ac5fa9aaf41618df235bac4073f27 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 254 | 1742970269884 | 2025-03-26 14:24:29 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | AuthenticatorBase | invoke | 6d0ac5fa9aaf41618df235bac4073f27 | - | - | - | - | 3 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 254 | 1742970269977 | 2025-03-26 14:24:29 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | AuthenticatorBase | invoke | 6d0ac5fa9aaf41618df235bac4073f27 | - | - | - | - | 96 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 91 ms

info | 244 | 1742970270516 | 2025-03-26 14:24:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a5128219ae7a4b369be910b6b4f96b86 | - | - | - | - | 110100 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 244 | 1742970280515 | 2025-03-26 14:24:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a5128219ae7a4b369be910b6b4f96b86 | - | - | - | - | 120099 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 244 | 1742970290513 | 2025-03-26 14:24:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | a5128219ae7a4b369be910b6b4f96b86 | - | - | - | - | 130099 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970300512 | 2025-03-26 14:25:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 300078 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970310514 | 2025-03-26 14:25:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 310080 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970320516 | 2025-03-26 14:25:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 320081 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970330517 | 2025-03-26 14:25:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 330082 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970340516 | 2025-03-26 14:25:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 340080 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970350513 | 2025-03-26 14:25:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 350076 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 243 | 1742970360513 | 2025-03-26 14:26:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ThreadPoolExecutor | runWorker | fa1f3de32f154810829fc15407fc1e20 | - | - | - | - | 3 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 243 | 1742970370516 | 2025-03-26 14:26:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ThreadPoolExecutor | runWorker | fa1f3de32f154810829fc15407fc1e20 | - | - | - | - | 10004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 243 | 1742970380515 | 2025-03-26 14:26:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ThreadPoolExecutor | runWorker | fa1f3de32f154810829fc15407fc1e20 | - | - | - | - | 20003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970390516 | 2025-03-26 14:26:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 390082 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970400518 | 2025-03-26 14:26:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 400084 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970410516 | 2025-03-26 14:26:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 410080 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970420517 | 2025-03-26 14:27:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 420082 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 243 | 1742970430514 | 2025-03-26 14:27:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ThreadPoolExecutor | runWorker | fa1f3de32f154810829fc15407fc1e20 | - | - | - | - | 70001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 243 | 1742970440519 | 2025-03-26 14:27:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ThreadPoolExecutor | runWorker | fa1f3de32f154810829fc15407fc1e20 | - | - | - | - | 80009 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 243 | 1742970450513 | 2025-03-26 14:27:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ThreadPoolExecutor | runWorker | fa1f3de32f154810829fc15407fc1e20 | - | - | - | - | 90001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970460516 | 2025-03-26 14:27:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 460084 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970470516 | 2025-03-26 14:27:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 470080 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 243 | 1742970480517 | 2025-03-26 14:28:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ThreadPoolExecutor | runWorker | fa1f3de32f154810829fc15407fc1e20 | - | - | - | - | 120004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 243 | 1742970490518 | 2025-03-26 14:28:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ThreadPoolExecutor | runWorker | fa1f3de32f154810829fc15407fc1e20 | - | - | - | - | 130008 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 243 | 1742970500515 | 2025-03-26 14:28:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | ************** | - | 2 | ThreadPoolExecutor | runWorker | fa1f3de32f154810829fc15407fc1e20 | - | - | - | - | 140003 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970510515 | 2025-03-26 14:28:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 510078 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970520520 | 2025-03-26 14:28:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 520086 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 234 | 1742970530520 | 2025-03-26 14:28:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | ************** | - | 2 | ThreadPoolExecutor | runWorker | 050171580a3044ff8a2804958bea47bc | - | - | - | - | 530083 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

