debug | 1 | 1742969879534 | 2025-03-26 14:17:59 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Application | main | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application Running with Spring Boot v2.4.5, Spring v5.3.6

debug | 1 | 1742969882548 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.39 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3020 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/chatroom/mapper/ExportRecordMapper.class]

debug | 1 | 1742969882549 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.41 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3020 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/chatroom/mapper/ForumChatRoomInfoMapper.class]

debug | 1 | 1742969882549 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.43 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3020 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/chatroom/mapper/LiveHostConsortiaMapper.class]

debug | 1 | 1742969882549 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.45 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3020 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/chatroom/mapper/RoomMapper.class]

debug | 1 | 1742969882549 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.47 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3020 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/common/mapper/ApprovalFlowLogMapper.class]

debug | 1 | 1742969882549 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.49 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3020 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/common/mapper/ApprovalFlowMapper.class]

debug | 1 | 1742969882549 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.51 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3021 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/common/mapper/ApprovalFlowNodeMapper.class]

debug | 1 | 1742969882550 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.53 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3021 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/common/mapper/AuditItemMapper.class]

debug | 1 | 1742969882550 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.55 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3021 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/common/mapper/AuditOrderMapper.class]

debug | 1 | 1742969882550 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.57 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3021 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/common/mapper/GeneralUserListMapper.class]

debug | 1 | 1742969882550 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.59 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3021 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/common/mapper/OrgFlowLogMapper.class]

debug | 1 | 1742969882550 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.61 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3021 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/EmployeeMapper.class]

debug | 1 | 1742969882550 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.63 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3021 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildAgentManageMapper.class]

debug | 1 | 1742969882550 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.65 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3022 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildAgreementInfoMapper.class]

debug | 1 | 1742969882551 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.67 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3022 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildAgreementMapper.class]

debug | 1 | 1742969882551 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.69 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3022 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildBusinessLicenseMapper.class]

debug | 1 | 1742969882551 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.71 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3022 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildChargePersonMapper.class]

debug | 1 | 1742969882551 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.73 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3022 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildCooperationFlowMapper.class]

debug | 1 | 1742969882551 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.75 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3022 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildHostScreenshotMapper.class]

debug | 1 | 1742969882551 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.77 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3022 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildInfoMapper.class]

debug | 1 | 1742969882551 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.79 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3022 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildLegalPersonMapper.class]

debug | 1 | 1742969882552 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.81 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3023 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildNoticeMapper.class]

debug | 1 | 1742969882552 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.83 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3023 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/GuildOpeningPermitMapper.class]

debug | 1 | 1742969882552 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.85 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3023 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/LiveConsortiaStatisticsDayMapper.class]

debug | 1 | 1742969882552 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.87 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3023 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/LiveHostApplyInfoMapper.class]

debug | 1 | 1742969882552 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.89 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3023 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/LiveHostInfoMapper.class]

debug | 1 | 1742969882552 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.91 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3023 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/LiveHostStatisticsDayMapper.class]

debug | 1 | 1742969882552 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.93 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3023 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/LiveHostStatisticsMonthMapper.class]

debug | 1 | 1742969882552 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.95 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3024 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/LiveNewHostStatisticsMonthMapper.class]

debug | 1 | 1742969882553 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.97 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3024 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/LiveOrgStatisticsMonthMapper.class]

debug | 1 | 1742969882553 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.99 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3024 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/LiveTeamStatisticsDayMapper.class]

debug | 1 | 1742969882553 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.101 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3024 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/live/mapper/OperationSpecialistMapper.class]

debug | 1 | 1742969882553 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.103 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3024 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/AgreementInfoMapper.class]

debug | 1 | 1742969882553 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.105 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3024 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/AgreementMapper.class]

debug | 1 | 1742969882553 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.107 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3025 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/ApplyTypeLogMapper.class]

debug | 1 | 1742969882554 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.109 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3025 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/BackstageOperateLogMapper.class]

debug | 1 | 1742969882554 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.111 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3025 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/BusinessLicenseMapper.class]

debug | 1 | 1742969882554 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.113 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3025 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/ChargePersonMapper.class]

debug | 1 | 1742969882554 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.115 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3025 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/CreditGradeLogMapper.class]

debug | 1 | 1742969882554 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.117 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3025 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/FeishuAuditRelationMapper.class]

debug | 1 | 1742969882554 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.119 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3025 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/HostModifyRecordMapper.class]

debug | 1 | 1742969882554 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.121 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3026 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/HostScreenshotMapper.class]

debug | 1 | 1742969882555 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.123 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3026 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/HostSharingProfitRecordMapper.class]

debug | 1 | 1742969882555 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.125 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3026 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/LegalPersonMapper.class]

debug | 1 | 1742969882555 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.127 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3026 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/MyDataMapper.class]

debug | 1 | 1742969882555 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.129 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3026 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/NoticeMapper.class]

debug | 1 | 1742969882555 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.131 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3026 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/OpeningPermitMapper.class]

debug | 1 | 1742969882555 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.133 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3026 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/OperationPersonMapper.class]

debug | 1 | 1742969882555 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.135 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3027 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/OperatorLogMapper.class]

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.137 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3027 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/OrgAccountLogMapper.class]

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.139 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3027 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/OrgBankLogMapper.class]

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.141 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3027 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/OrgCompanyLogMapper.class]

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.143 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3027 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/OrgCooperationFlowMapper.class]

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.145 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3027 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/OrgNameLogMapper.class]

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.147 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3027 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/OrgOperatorLogMapper.class]

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.149 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3027 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/OrganizationMapper.class]

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.151 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3028 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/PerformanceMapper.class]

debug | 1 | 1742969882557 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.153 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3028 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/QuestionFeedbackMapper.class]

debug | 1 | 1742969882557 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.155 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3028 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/RecommendApplyCardMapper.class]

debug | 1 | 1742969882557 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.157 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3028 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/RecommendApplyCardUseLogMapper.class]

debug | 1 | 1742969882557 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.159 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3028 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/RegionMapper.class]

debug | 1 | 1742969882557 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.161 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3028 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/SysMenuMapper.class]

debug | 1 | 1742969882557 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.163 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3028 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/SysRoleMapper.class]

debug | 1 | 1742969882557 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.165 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3028 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/SysRoleMenuMapper.class]

debug | 1 | 1742969882557 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.167 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3029 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/SysSharingProfitMapper.class]

debug | 1 | 1742969882558 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.169 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3029 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/SysUserMapper.class]

debug | 1 | 1742969882558 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.171 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3029 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/SysUserRoleMapper.class]

debug | 1 | 1742969882558 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.173 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3029 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/TargetInfoPersonMapper.class]

debug | 1 | 1742969882558 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.175 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3029 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/TeamEmployeeMapper.class]

debug | 1 | 1742969882558 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.177 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3029 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/TeamHostMapper.class]

debug | 1 | 1742969882558 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.179 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3029 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/TeamHostOperateLogMapper.class]

debug | 1 | 1742969882558 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.181 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3029 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/TeamMapper.class]

debug | 1 | 1742969882558 | 2025-03-26 14:18:02 | v2/PostProcessorRegistrationDelegate/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.183 | ************** | - | 2 | PostProcessorRegistrationDelegate | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3030 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Identified candidate component class: file [/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes/cn/taqu/gonghui/system/mapper/TeamOperateInfoMapper.class]

debug | 1 | 1742969882559 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.185 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3030 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'exportRecordMapper' and 'cn.taqu.gonghui.chatroom.mapper.ExportRecordMapper' mapperInterface

debug | 1 | 1742969882560 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.187 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3031 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'exportRecordMapper'.

debug | 1 | 1742969882560 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.189 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3031 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'forumChatRoomInfoMapper' and 'cn.taqu.gonghui.chatroom.mapper.ForumChatRoomInfoMapper' mapperInterface

debug | 1 | 1742969882560 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.191 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3031 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'forumChatRoomInfoMapper'.

debug | 1 | 1742969882560 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.193 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3031 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'liveHostConsortiaMapper' and 'cn.taqu.gonghui.chatroom.mapper.LiveHostConsortiaMapper' mapperInterface

debug | 1 | 1742969882560 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.195 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3031 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'liveHostConsortiaMapper'.

debug | 1 | 1742969882560 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.197 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3031 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'roomMapper' and 'cn.taqu.gonghui.chatroom.mapper.RoomMapper' mapperInterface

debug | 1 | 1742969882560 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.199 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3031 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'roomMapper'.

debug | 1 | 1742969882561 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.201 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3032 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'approvalFlowLogMapper' and 'cn.taqu.gonghui.common.mapper.ApprovalFlowLogMapper' mapperInterface

debug | 1 | 1742969882561 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.203 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3032 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'approvalFlowLogMapper'.

debug | 1 | 1742969882561 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.205 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3032 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'approvalFlowMapper' and 'cn.taqu.gonghui.common.mapper.ApprovalFlowMapper' mapperInterface

debug | 1 | 1742969882561 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.207 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3032 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'approvalFlowMapper'.

debug | 1 | 1742969882561 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.209 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3032 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'approvalFlowNodeMapper' and 'cn.taqu.gonghui.common.mapper.ApprovalFlowNodeMapper' mapperInterface

debug | 1 | 1742969882561 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.211 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3032 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'approvalFlowNodeMapper'.

debug | 1 | 1742969882561 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.213 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3032 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'auditItemMapper' and 'cn.taqu.gonghui.common.mapper.AuditItemMapper' mapperInterface

debug | 1 | 1742969882561 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.215 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3032 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'auditItemMapper'.

debug | 1 | 1742969882561 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.217 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3032 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'auditOrderMapper' and 'cn.taqu.gonghui.common.mapper.AuditOrderMapper' mapperInterface

debug | 1 | 1742969882561 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.219 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3033 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'auditOrderMapper'.

debug | 1 | 1742969882562 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.221 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3033 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'generalUserListMapper' and 'cn.taqu.gonghui.common.mapper.GeneralUserListMapper' mapperInterface

debug | 1 | 1742969882562 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.223 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3033 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'generalUserListMapper'.

debug | 1 | 1742969882562 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.225 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3033 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'orgFlowLogMapper' and 'cn.taqu.gonghui.common.mapper.OrgFlowLogMapper' mapperInterface

debug | 1 | 1742969882562 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.227 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3033 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'orgFlowLogMapper'.

debug | 1 | 1742969882562 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.229 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3033 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'employeeMapper' and 'cn.taqu.gonghui.live.mapper.EmployeeMapper' mapperInterface

debug | 1 | 1742969882562 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.231 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3033 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'employeeMapper'.

debug | 1 | 1742969882562 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.233 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3033 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildAgentManageMapper' and 'cn.taqu.gonghui.live.mapper.GuildAgentManageMapper' mapperInterface

debug | 1 | 1742969882562 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.235 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3033 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildAgentManageMapper'.

debug | 1 | 1742969882562 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.237 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3033 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildAgreementInfoMapper' and 'cn.taqu.gonghui.live.mapper.GuildAgreementInfoMapper' mapperInterface

debug | 1 | 1742969882562 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.239 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3034 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildAgreementInfoMapper'.

debug | 1 | 1742969882563 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.241 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3034 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildAgreementMapper' and 'cn.taqu.gonghui.live.mapper.GuildAgreementMapper' mapperInterface

debug | 1 | 1742969882563 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.243 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3034 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildAgreementMapper'.

debug | 1 | 1742969882563 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.245 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3034 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildBusinessLicenseMapper' and 'cn.taqu.gonghui.live.mapper.GuildBusinessLicenseMapper' mapperInterface

debug | 1 | 1742969882563 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.247 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3034 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildBusinessLicenseMapper'.

debug | 1 | 1742969882563 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.249 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3034 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildChargePersonMapper' and 'cn.taqu.gonghui.live.mapper.GuildChargePersonMapper' mapperInterface

debug | 1 | 1742969882563 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.251 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3034 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildChargePersonMapper'.

debug | 1 | 1742969882563 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.253 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3034 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildCooperationFlowMapper' and 'cn.taqu.gonghui.live.mapper.GuildCooperationFlowMapper' mapperInterface

debug | 1 | 1742969882563 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.255 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3034 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildCooperationFlowMapper'.

debug | 1 | 1742969882563 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.257 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3034 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildHostScreenshotMapper' and 'cn.taqu.gonghui.live.mapper.GuildHostScreenshotMapper' mapperInterface

debug | 1 | 1742969882563 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.259 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildHostScreenshotMapper'.

debug | 1 | 1742969882564 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.261 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildInfoMapper' and 'cn.taqu.gonghui.live.mapper.GuildInfoMapper' mapperInterface

debug | 1 | 1742969882564 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.263 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildInfoMapper'.

debug | 1 | 1742969882564 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.265 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildLegalPersonMapper' and 'cn.taqu.gonghui.live.mapper.GuildLegalPersonMapper' mapperInterface

debug | 1 | 1742969882564 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.267 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildLegalPersonMapper'.

debug | 1 | 1742969882564 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.269 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildNoticeMapper' and 'cn.taqu.gonghui.live.mapper.GuildNoticeMapper' mapperInterface

debug | 1 | 1742969882564 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.271 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildNoticeMapper'.

debug | 1 | 1742969882564 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.273 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'guildOpeningPermitMapper' and 'cn.taqu.gonghui.live.mapper.GuildOpeningPermitMapper' mapperInterface

debug | 1 | 1742969882564 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.275 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'guildOpeningPermitMapper'.

debug | 1 | 1742969882564 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.277 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'liveConsortiaStatisticsDayMapper' and 'cn.taqu.gonghui.live.mapper.LiveConsortiaStatisticsDayMapper' mapperInterface

debug | 1 | 1742969882564 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.279 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3035 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'liveConsortiaStatisticsDayMapper'.

debug | 1 | 1742969882565 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.281 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3036 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'liveHostApplyInfoMapper' and 'cn.taqu.gonghui.live.mapper.LiveHostApplyInfoMapper' mapperInterface

debug | 1 | 1742969882565 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.283 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3036 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'liveHostApplyInfoMapper'.

debug | 1 | 1742969882565 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.285 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3036 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'liveHostInfoMapper' and 'cn.taqu.gonghui.live.mapper.LiveHostInfoMapper' mapperInterface

debug | 1 | 1742969882566 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.287 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3037 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'liveHostInfoMapper'.

debug | 1 | 1742969882566 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.289 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3037 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'liveHostStatisticsDayMapper' and 'cn.taqu.gonghui.live.mapper.LiveHostStatisticsDayMapper' mapperInterface

debug | 1 | 1742969882566 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.291 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3037 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'liveHostStatisticsDayMapper'.

debug | 1 | 1742969882566 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.293 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3037 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'liveHostStatisticsMonthMapper' and 'cn.taqu.gonghui.live.mapper.LiveHostStatisticsMonthMapper' mapperInterface

debug | 1 | 1742969882566 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.295 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3037 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'liveHostStatisticsMonthMapper'.

debug | 1 | 1742969882566 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.297 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3037 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'liveNewHostStatisticsMonthMapper' and 'cn.taqu.gonghui.live.mapper.LiveNewHostStatisticsMonthMapper' mapperInterface

debug | 1 | 1742969882566 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.299 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3037 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'liveNewHostStatisticsMonthMapper'.

debug | 1 | 1742969882566 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.301 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'liveOrgStatisticsMonthMapper' and 'cn.taqu.gonghui.live.mapper.LiveOrgStatisticsMonthMapper' mapperInterface

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.303 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'liveOrgStatisticsMonthMapper'.

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.305 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'liveTeamStatisticsDayMapper' and 'cn.taqu.gonghui.live.mapper.LiveTeamStatisticsDayMapper' mapperInterface

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.307 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'liveTeamStatisticsDayMapper'.

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.309 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'operationSpecialistMapper' and 'cn.taqu.gonghui.live.mapper.OperationSpecialistMapper' mapperInterface

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.311 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'operationSpecialistMapper'.

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.313 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'agreementInfoMapper' and 'cn.taqu.gonghui.system.mapper.AgreementInfoMapper' mapperInterface

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.315 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'agreementInfoMapper'.

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.317 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'agreementMapper' and 'cn.taqu.gonghui.system.mapper.AgreementMapper' mapperInterface

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.319 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'agreementMapper'.

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.321 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'applyTypeLogMapper' and 'cn.taqu.gonghui.system.mapper.ApplyTypeLogMapper' mapperInterface

debug | 1 | 1742969882567 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.323 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3038 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'applyTypeLogMapper'.

debug | 1 | 1742969882568 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.325 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3039 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'backstageOperateLogMapper' and 'cn.taqu.gonghui.system.mapper.BackstageOperateLogMapper' mapperInterface

debug | 1 | 1742969882568 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.327 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3039 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'backstageOperateLogMapper'.

debug | 1 | 1742969882568 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.329 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3039 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'businessLicenseMapper' and 'cn.taqu.gonghui.system.mapper.BusinessLicenseMapper' mapperInterface

debug | 1 | 1742969882568 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.331 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3039 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'businessLicenseMapper'.

debug | 1 | 1742969882568 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.333 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3039 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'chargePersonMapper' and 'cn.taqu.gonghui.system.mapper.ChargePersonMapper' mapperInterface

debug | 1 | 1742969882568 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.335 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3039 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'chargePersonMapper'.

debug | 1 | 1742969882568 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.337 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3039 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'creditGradeLogMapper' and 'cn.taqu.gonghui.system.mapper.CreditGradeLogMapper' mapperInterface

debug | 1 | 1742969882568 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.339 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3039 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'creditGradeLogMapper'.

debug | 1 | 1742969882568 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.341 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3039 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'feishuAuditRelationMapper' and 'cn.taqu.gonghui.system.mapper.FeishuAuditRelationMapper' mapperInterface

debug | 1 | 1742969882568 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.343 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3039 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'feishuAuditRelationMapper'.

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.345 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'hostModifyRecordMapper' and 'cn.taqu.gonghui.system.mapper.HostModifyRecordMapper' mapperInterface

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.347 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'hostModifyRecordMapper'.

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.349 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'hostScreenshotMapper' and 'cn.taqu.gonghui.system.mapper.HostScreenshotMapper' mapperInterface

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.351 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'hostScreenshotMapper'.

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.353 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'hostSharingProfitRecordMapper' and 'cn.taqu.gonghui.system.mapper.HostSharingProfitRecordMapper' mapperInterface

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.355 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'hostSharingProfitRecordMapper'.

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.357 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'legalPersonMapper' and 'cn.taqu.gonghui.system.mapper.LegalPersonMapper' mapperInterface

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.359 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'legalPersonMapper'.

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.361 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'myDataMapper' and 'cn.taqu.gonghui.system.mapper.MyDataMapper' mapperInterface

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.363 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'myDataMapper'.

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.365 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3040 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'noticeMapper' and 'cn.taqu.gonghui.system.mapper.NoticeMapper' mapperInterface

debug | 1 | 1742969882569 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.367 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'noticeMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.369 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'openingPermitMapper' and 'cn.taqu.gonghui.system.mapper.OpeningPermitMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.371 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'openingPermitMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.373 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'operationPersonMapper' and 'cn.taqu.gonghui.system.mapper.OperationPersonMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.375 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'operationPersonMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.377 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'operatorLogMapper' and 'cn.taqu.gonghui.system.mapper.OperatorLogMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.379 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'operatorLogMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.381 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'orgAccountLogMapper' and 'cn.taqu.gonghui.system.mapper.OrgAccountLogMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.383 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'orgAccountLogMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.385 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'orgBankLogMapper' and 'cn.taqu.gonghui.system.mapper.OrgBankLogMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.387 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3041 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'orgBankLogMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.389 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'orgCompanyLogMapper' and 'cn.taqu.gonghui.system.mapper.OrgCompanyLogMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.391 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'orgCompanyLogMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.393 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'orgCooperationFlowMapper' and 'cn.taqu.gonghui.system.mapper.OrgCooperationFlowMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.395 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'orgCooperationFlowMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.397 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'orgNameLogMapper' and 'cn.taqu.gonghui.system.mapper.OrgNameLogMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.399 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'orgNameLogMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.401 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'orgOperatorLogMapper' and 'cn.taqu.gonghui.system.mapper.OrgOperatorLogMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.403 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'orgOperatorLogMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.405 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'organizationMapper' and 'cn.taqu.gonghui.system.mapper.OrganizationMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.407 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'organizationMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.409 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'performanceMapper' and 'cn.taqu.gonghui.system.mapper.PerformanceMapper' mapperInterface

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.411 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3042 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'performanceMapper'.

debug | 1 | ************* | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.413 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'questionFeedbackMapper' and 'cn.taqu.gonghui.system.mapper.QuestionFeedbackMapper' mapperInterface

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.415 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'questionFeedbackMapper'.

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.417 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'recommendApplyCardMapper' and 'cn.taqu.gonghui.system.mapper.RecommendApplyCardMapper' mapperInterface

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.419 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'recommendApplyCardMapper'.

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.421 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'recommendApplyCardUseLogMapper' and 'cn.taqu.gonghui.system.mapper.RecommendApplyCardUseLogMapper' mapperInterface

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.423 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'recommendApplyCardUseLogMapper'.

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.425 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'regionMapper' and 'cn.taqu.gonghui.system.mapper.RegionMapper' mapperInterface

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.427 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'regionMapper'.

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.429 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'sysMenuMapper' and 'cn.taqu.gonghui.system.mapper.SysMenuMapper' mapperInterface

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.431 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'sysMenuMapper'.

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.433 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'sysRoleMapper' and 'cn.taqu.gonghui.system.mapper.SysRoleMapper' mapperInterface

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.435 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'sysRoleMapper'.

debug | 1 | 1742969882572 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.437 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3043 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'sysRoleMenuMapper' and 'cn.taqu.gonghui.system.mapper.SysRoleMenuMapper' mapperInterface

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.439 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3044 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'sysRoleMenuMapper'.

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.441 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3044 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'sysSharingProfitMapper' and 'cn.taqu.gonghui.system.mapper.SysSharingProfitMapper' mapperInterface

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.443 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3044 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'sysSharingProfitMapper'.

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.445 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3044 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'sysUserMapper' and 'cn.taqu.gonghui.system.mapper.SysUserMapper' mapperInterface

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.447 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3044 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'sysUserMapper'.

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.449 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3044 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'sysUserRoleMapper' and 'cn.taqu.gonghui.system.mapper.SysUserRoleMapper' mapperInterface

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.451 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3044 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'sysUserRoleMapper'.

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.453 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3044 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'targetInfoPersonMapper' and 'cn.taqu.gonghui.system.mapper.TargetInfoPersonMapper' mapperInterface

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.455 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3044 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'targetInfoPersonMapper'.

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.457 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3044 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'teamEmployeeMapper' and 'cn.taqu.gonghui.system.mapper.TeamEmployeeMapper' mapperInterface

debug | 1 | 1742969882573 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.459 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3045 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'teamEmployeeMapper'.

debug | 1 | 1742969882574 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.461 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3045 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'teamHostMapper' and 'cn.taqu.gonghui.system.mapper.TeamHostMapper' mapperInterface

debug | 1 | 1742969882574 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.463 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3045 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'teamHostMapper'.

debug | 1 | 1742969882574 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.465 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3045 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'teamHostOperateLogMapper' and 'cn.taqu.gonghui.system.mapper.TeamHostOperateLogMapper' mapperInterface

debug | 1 | 1742969882574 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.467 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3045 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'teamHostOperateLogMapper'.

debug | 1 | 1742969882574 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.469 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3045 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'teamMapper' and 'cn.taqu.gonghui.system.mapper.TeamMapper' mapperInterface

debug | 1 | 1742969882574 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.471 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3045 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'teamMapper'.

debug | 1 | 1742969882574 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.473 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3045 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Creating MapperFactoryBean with name 'teamOperateInfoMapper' and 'cn.taqu.gonghui.system.mapper.TeamOperateInfoMapper' mapperInterface

debug | 1 | 1742969882574 | 2025-03-26 14:18:02 | v2/AbstractApplicationContext/invokeBeanFactoryPostProcessors | online | - | 1 | - | - | cli | j47 | 0.475 | ************** | - | 2 | AbstractApplicationContext | invokeBeanFactoryPostProcessors | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 3045 | 0 | - | - | - | - | main o.m.s.m.ClassPathMapperScanner Enabling autowire by type for MapperFactoryBean with name 'teamOperateInfoMapper'.

debug | 1 | 1742969884514 | 2025-03-26 14:18:04 | v2/ContainerBase$StartChild/call | online | - | 1 | - | - | cli | j47 | 0.497 | ************** | - | 2 | ContainerBase$StartChild | call | 8f112d653a4e4a52b5f0b48880ea6d9a | - | - | - | - | 4986 | 0 | - | - | - | - | main c.t.g.c.c.JwtAuthenticationTokenFilter Filter 'jwtAuthenticationTokenFilter' configured for use

debug | 254 | 1742970271547 | 2025-03-26 14:24:31 | v2/manageTeam/detail | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | ************** | - | 2 | manageTeam | detail | 7a645d2d5f0b4f848fbec87dc04e8d06 | - | - | - | - | 1555 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.a.ControllerLogAspect appCode=null,cloned=1,请求参数：["10"]

debug | 254 | 1742970272103 | 2025-03-26 14:24:32 | v2/manageTeam/detail | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | ************** | - | 2 | manageTeam | detail | 7a645d2d5f0b4f848fbec87dc04e8d06 | - | - | - | - | 2111 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.a.ControllerLogAspect 返回参数：{"code":"0","data":{"createBy":"admin","createTime":1704779802,"inviteCode":"","isDefault":0,"orgId":1001034,"signKey":"liveTeam1","status":1,"teamId":10,"teamName":"徐欢公会周结","type":1,"updateTime":1704779802},"extra":"","msg":"","success":true}

