info | 15 | 1735803105452 | 2025-01-02 15:31:45 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 7bcbcce789974a98bd8d22317e382c45 | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735803105464 | 2025-01-02 15:31:45 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 12005 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1735803105474 | 2025-01-02 15:31:45 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735803106369 | 2025-01-02 15:31:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 903 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735803106374 | 2025-01-02 15:31:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 907 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735803106386 | 2025-01-02 15:31:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 919 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735803106390 | 2025-01-02 15:31:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 923 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735803106393 | 2025-01-02 15:31:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 926 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735803106460 | 2025-01-02 15:31:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 993 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735803106494 | 2025-01-02 15:31:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 1028 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735803106497 | 2025-01-02 15:31:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 1030 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735803106497 | 2025-01-02 15:31:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 1031 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735803106548 | 2025-01-02 15:31:46 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 1082 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735803108768 | 2025-01-02 15:31:48 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 3302 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735803108770 | 2025-01-02 15:31:48 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 3303 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735803108813 | 2025-01-02 15:31:48 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 3346 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 36 ms. Found 0 JPA repository interfaces.

info | 1 | 1735803108823 | 2025-01-02 15:31:48 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 3356 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735803108824 | 2025-01-02 15:31:48 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 3358 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735803108867 | 2025-01-02 15:31:48 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 3400 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.

info | 1 | 1735803109708 | 2025-01-02 15:31:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 4244 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$7bc34311] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803109732 | 2025-01-02 15:31:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 4265 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$fac98889] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803109808 | 2025-01-02 15:31:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 4341 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$79329352] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803109813 | 2025-01-02 15:31:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 4346 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803109871 | 2025-01-02 15:31:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 4404 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803109875 | 2025-01-02 15:31:49 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 4408 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803110431 | 2025-01-02 15:31:50 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 4965 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735803110440 | 2025-01-02 15:31:50 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 4973 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735803110440 | 2025-01-02 15:31:50 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 4973 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735803110549 | 2025-01-02 15:31:50 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 5082 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735803117547 | 2025-01-02 15:31:57 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 12081 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735803117619 | 2025-01-02 15:31:57 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 12152 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735803117659 | 2025-01-02 15:31:57 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 12192 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735803117766 | 2025-01-02 15:31:57 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 12299 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735803117858 | 2025-01-02 15:31:57 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 12391 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735803117980 | 2025-01-02 15:31:57 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 12514 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735803117987 | 2025-01-02 15:31:57 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 12520 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735803123131 | 2025-01-02 15:32:03 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 17664 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735803123418 | 2025-01-02 15:32:03 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 17951 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735803123440 | 2025-01-02 15:32:03 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 17973 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735803123606 | 2025-01-02 15:32:03 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b7592a404d074094b39b50d4821fec14 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735803123640 | 2025-01-02 15:32:03 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b7592a404d074094b39b50d4821fec14 | - | - | - | - | 34 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735803123658 | 2025-01-02 15:32:03 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b7592a404d074094b39b50d4821fec14 | - | - | - | - | 52 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735803123771 | 2025-01-02 15:32:03 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b7592a404d074094b39b50d4821fec14 | - | - | - | - | 166 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 111 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735803123777 | 2025-01-02 15:32:03 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b7592a404d074094b39b50d4821fec14 | - | - | - | - | 172 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735803123784 | 2025-01-02 15:32:03 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b7592a404d074094b39b50d4821fec14 | - | - | - | - | 179 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735803123791 | 2025-01-02 15:32:03 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b7592a404d074094b39b50d4821fec14 | - | - | - | - | 185 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735803123918 | 2025-01-02 15:32:03 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b7592a404d074094b39b50d4821fec14 | - | - | - | - | 312 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 125 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735803126481 | 2025-01-02 15:32:06 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 21015 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735803127426 | 2025-01-02 15:32:07 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 21960 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@86c162e with [org.springframework.security.web.session.DisableEncodeUrlFilter@74899df1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@619a61c1, org.springframework.security.web.context.SecurityContextPersistenceFilter@4ce82818, org.springframework.security.web.header.HeaderWriterFilter@69292ca6, org.springframework.security.web.authentication.logout.LogoutFilter@730ed75d, org.springframework.web.filter.CorsFilter@1b5b656b, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@8d0a300, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@7480b7cd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7fae2be3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5decdb3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1cb50215, org.springframework.security.web.session.SessionManagementFilter@39f97fea, org.springframework.security.web.access.ExceptionTranslationFilter@1f811284, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@17e04cc5]

info | 1 | 1735803127444 | 2025-01-02 15:32:07 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 21977 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735803127650 | 2025-01-02 15:32:07 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22183 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735803127653 | 2025-01-02 15:32:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22186 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735803127655 | 2025-01-02 15:32:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22188 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735803127660 | 2025-01-02 15:32:07 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22194 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735803127664 | 2025-01-02 15:32:07 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22197 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735803127664 | 2025-01-02 15:32:07 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22197 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735803127664 | 2025-01-02 15:32:07 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22197 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735803128077 | 2025-01-02 15:32:08 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22612 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735803128080 | 2025-01-02 15:32:08 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22613 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735803128087 | 2025-01-02 15:32:08 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22620 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735803128087 | 2025-01-02 15:32:08 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 22620 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735803128484 | 2025-01-02 15:32:08 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 23018 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735803128637 | 2025-01-02 15:32:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 23170 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735803128638 | 2025-01-02 15:32:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 23171 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735803128656 | 2025-01-02 15:32:08 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 23190 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@71b6ccab, tech.powerjob.worker.actors.ProcessorTrackerActor@172058f3, tech.powerjob.worker.actors.WorkerActor@77feef20])

info | 1 | 1735803128696 | 2025-01-02 15:32:08 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 23230 | 0 | - | - | - | - | main o.r.Reflections Reflections took 27 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735803128704 | 2025-01-02 15:32:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 23237 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1735803128705 | 2025-01-02 15:32:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 23238 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@44d6039d

info | 1 | 1735803128705 | 2025-01-02 15:32:08 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 23238 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735803128706 | 2025-01-02 15:32:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 23239 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735803128709 | 2025-01-02 15:32:08 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 23242 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 123 | 1735803129318 | 2025-01-02 15:32:09 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735803129738 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24271 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735803129739 | 2025-01-02 15:32:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24272 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735803129739 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24272 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735803129739 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24272 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735803129739 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24272 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735803129740 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735803129740 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735803129740 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735803129740 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735803129740 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735803129740 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735803129740 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24273 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735803129740 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735803129741 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735803129741 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24274 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735803129742 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24276 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735803129745 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24278 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735803129746 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24279 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735803129746 | 2025-01-02 15:32:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24280 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 1.041 s

info | 1 | 1735803129883 | 2025-01-02 15:32:09 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24416 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735803129888 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24422 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735803129889 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24422 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735803129893 | 2025-01-02 15:32:09 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24426 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735803130135 | 2025-01-02 15:32:10 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24668 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735803130135 | 2025-01-02 15:32:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24668 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/e1a949991a3a482e9fedd9e6abf83820/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735803130143 | 2025-01-02 15:32:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24676 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/e1a949991a3a482e9fedd9e6abf83820/] on JVM exit successfully

info | 1 | 1735803130158 | 2025-01-02 15:32:10 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24691 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735803130159 | 2025-01-02 15:32:10 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24692 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 2.509 s, congratulations!

info | 158 | 1735803130167 | 2025-01-02 15:32:10 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | dd60f9faf0cb4e8c8b3287ff69b9298b | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 158 | 1735803130168 | 2025-01-02 15:32:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | dd60f9faf0cb4e8c8b3287ff69b9298b | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735803130265 | 2025-01-02 15:32:10 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24798 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735803130298 | 2025-01-02 15:32:10 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24831 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735803130321 | 2025-01-02 15:32:10 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24854 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735803130322 | 2025-01-02 15:32:10 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24855 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735803130352 | 2025-01-02 15:32:10 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24885 | 0 | - | - | - | - | main c.t.g.Application Started Application in 25.403 seconds (JVM running for 25.87)

info | 1 | 1735803130375 | 2025-01-02 15:32:10 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24909 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735803130376 | 2025-01-02 15:32:10 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 2eaaa9d33e2b49cfbfba4a1294c645a2 | - | - | - | - | 24909 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 228 | 1735803130380 | 2025-01-02 15:32:10 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | c99e2bbfe8f34a979501bac039c9d167 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 158 | 1735803140161 | 2025-01-02 15:32:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | dd60f9faf0cb4e8c8b3287ff69b9298b | - | - | - | - | 9995 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 228 | 1735803140823 | 2025-01-02 15:32:20 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ChatRoomService | refreshChatCache | c99e2bbfe8f34a979501bac039c9d167 | - | - | - | - | 10443 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 162 | 1735803150163 | 2025-01-02 15:32:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 9e722a6525c64725b55ffd5fdac63297 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735803160164 | 2025-01-02 15:32:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 9e722a6525c64725b55ffd5fdac63297 | - | - | - | - | 10002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735803170161 | 2025-01-02 15:32:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 9e722a6525c64725b55ffd5fdac63297 | - | - | - | - | 19999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 167 | 1735803170850 | 2025-01-02 15:32:50 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | b5940706201f4fb6adfcef6492818ef2 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 167 | 1735803170851 | 2025-01-02 15:32:50 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | b5940706201f4fb6adfcef6492818ef2 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 167 | 1735803170862 | 2025-01-02 15:32:50 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | b5940706201f4fb6adfcef6492818ef2 | - | - | - | - | 12 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 11 ms

info | 167 | 1735803171086 | 2025-01-02 15:32:51 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | 1168b50ab51549d79a6e0fe8e71d6d4e | - | - | - | - | 214 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=1168b50ab51549d79a6e0fe8e71d6d4e, request data=[1,3,**********,**********,null,null,[],null,0]

info | 167 | 1735803171096 | 2025-01-02 15:32:51 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | 1168b50ab51549d79a6e0fe8e71d6d4e | - | - | - | - | 224 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.c.c.c.SoaClient register http client : [http://live-api.test3.hbmonitor.com/v1/Soa/jService] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 167 | 1735803171406 | 2025-01-02 15:32:51 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | manageMultiLive | hostData | 1168b50ab51549d79a6e0fe8e71d6d4e | - | - | - | - | 534 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2025-01-01\",\"host_name\":\"沙雕公共1\",\"host_uuid\":\"bhcdffbchghijhcj\",\"consortia_id\":\"2342696\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1,\"shell_amount\":0,\"total_score\":0,\"room_shell_amount\":0,\"room_total_score\":0,\"meet_shell_amount\":0,\"meet_total_score\":0,\"shell_ratio\":\"0%\"}],\"total\":\"1\"}","dataBlank":false,"dataNotBlank":true}

info | 162 | 1735803198037 | 2025-01-02 15:33:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 9e722a6525c64725b55ffd5fdac63297 | - | - | - | - | 47901 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735803198081 | 2025-01-02 15:33:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 9e722a6525c64725b55ffd5fdac63297 | - | - | - | - | 47918 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735803200160 | 2025-01-02 15:33:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 837eb424421c4617ab0201143db27628 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735803210160 | 2025-01-02 15:33:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 837eb424421c4617ab0201143db27628 | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735803220161 | 2025-01-02 15:33:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 837eb424421c4617ab0201143db27628 | - | - | - | - | 20002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1735803222770 | 2025-01-02 15:33:42 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | b65d9fd04c8f4c1ba181370ef9c94be5 | - | - | - | - | 20 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=b65d9fd04c8f4c1ba181370ef9c94be5, request data=[1,3,**********,**********,null,null,[],null,0]

info | 162 | 1735803234614 | 2025-01-02 15:33:54 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 9e722a6525c64725b55ffd5fdac63297 | - | - | - | - | 84459 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 168 | 1735803234825 | 2025-01-02 15:33:54 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | b65d9fd04c8f4c1ba181370ef9c94be5 | - | - | - | - | 12074 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2025-01-01\",\"host_name\":\"沙雕公共1\",\"host_uuid\":\"bhcdffbchghijhcj\",\"consortia_id\":\"2342696\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1,\"shell_amount\":0,\"total_score\":0,\"room_shell_amount\":0,\"room_total_score\":0,\"meet_shell_amount\":0,\"meet_total_score\":0,\"shell_ratio\":\"0%\"}],\"total\":\"1\"}","dataBlank":false,"dataNotBlank":true}

info | 158 | 1735803240161 | 2025-01-02 15:34:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | dd60f9faf0cb4e8c8b3287ff69b9298b | - | - | - | - | 109996 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735803250160 | 2025-01-02 15:34:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | dd60f9faf0cb4e8c8b3287ff69b9298b | - | - | - | - | 119993 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1735803255151 | 2025-01-02 15:34:15 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | f6cb6a4de62846e7bd66ce9724f06448 | - | - | - | - | 1 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1735803255187 | 2025-01-02 15:34:15 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | f6cb6a4de62846e7bd66ce9724f06448 | - | - | - | - | 36 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1735803255190 | 2025-01-02 15:34:15 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | f6cb6a4de62846e7bd66ce9724f06448 | - | - | - | - | 39 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1735803255213 | 2025-01-02 15:34:15 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | AbstractApplicationContext | destroyBeans | f6cb6a4de62846e7bd66ce9724f06448 | - | - | - | - | 62 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

info | 15 | 1735803399001 | 2025-01-02 15:36:39 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 2a99aaa526e44deebce3e8426ed26cd8 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735803399011 | 2025-01-02 15:36:39 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 47740526016f40498312118e2f42a03e | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 12167 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1735803399019 | 2025-01-02 15:36:39 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 47740526016f40498312118e2f42a03e | - | - | - | - | 5 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735803399602 | 2025-01-02 15:36:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47740526016f40498312118e2f42a03e | - | - | - | - | 588 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735803399604 | 2025-01-02 15:36:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47740526016f40498312118e2f42a03e | - | - | - | - | 590 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735803399606 | 2025-01-02 15:36:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47740526016f40498312118e2f42a03e | - | - | - | - | 593 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735803399608 | 2025-01-02 15:36:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47740526016f40498312118e2f42a03e | - | - | - | - | 595 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735803399610 | 2025-01-02 15:36:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47740526016f40498312118e2f42a03e | - | - | - | - | 596 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735803399651 | 2025-01-02 15:36:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47740526016f40498312118e2f42a03e | - | - | - | - | 637 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735803399683 | 2025-01-02 15:36:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47740526016f40498312118e2f42a03e | - | - | - | - | 669 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735803399685 | 2025-01-02 15:36:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47740526016f40498312118e2f42a03e | - | - | - | - | 671 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735803399686 | 2025-01-02 15:36:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47740526016f40498312118e2f42a03e | - | - | - | - | 672 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735803399734 | 2025-01-02 15:36:39 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 47740526016f40498312118e2f42a03e | - | - | - | - | 720 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735803401856 | 2025-01-02 15:36:41 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 47740526016f40498312118e2f42a03e | - | - | - | - | 2842 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735803401857 | 2025-01-02 15:36:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47740526016f40498312118e2f42a03e | - | - | - | - | 2843 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735803401896 | 2025-01-02 15:36:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47740526016f40498312118e2f42a03e | - | - | - | - | 2882 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 33 ms. Found 0 JPA repository interfaces.

info | 1 | 1735803401905 | 2025-01-02 15:36:41 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 47740526016f40498312118e2f42a03e | - | - | - | - | 2891 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735803401906 | 2025-01-02 15:36:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47740526016f40498312118e2f42a03e | - | - | - | - | 2892 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735803401930 | 2025-01-02 15:36:41 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 47740526016f40498312118e2f42a03e | - | - | - | - | 2916 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.

info | 1 | 1735803402706 | 2025-01-02 15:36:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 3695 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$52a56300] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803402742 | 2025-01-02 15:36:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 3729 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$d1aba878] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803402822 | 2025-01-02 15:36:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 3809 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$5014b341] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803402828 | 2025-01-02 15:36:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 3815 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803402893 | 2025-01-02 15:36:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 3879 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803402898 | 2025-01-02 15:36:42 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 3884 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735803403477 | 2025-01-02 15:36:43 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 47740526016f40498312118e2f42a03e | - | - | - | - | 4463 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735803403486 | 2025-01-02 15:36:43 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 47740526016f40498312118e2f42a03e | - | - | - | - | 4472 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735803403486 | 2025-01-02 15:36:43 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 47740526016f40498312118e2f42a03e | - | - | - | - | 4472 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735803403569 | 2025-01-02 15:36:43 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 47740526016f40498312118e2f42a03e | - | - | - | - | 4555 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735803410468 | 2025-01-02 15:36:50 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 47740526016f40498312118e2f42a03e | - | - | - | - | 11454 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735803410540 | 2025-01-02 15:36:50 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 47740526016f40498312118e2f42a03e | - | - | - | - | 11527 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735803410584 | 2025-01-02 15:36:50 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 47740526016f40498312118e2f42a03e | - | - | - | - | 11570 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735803410706 | 2025-01-02 15:36:50 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 47740526016f40498312118e2f42a03e | - | - | - | - | 11692 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735803410788 | 2025-01-02 15:36:50 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 47740526016f40498312118e2f42a03e | - | - | - | - | 11774 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735803410931 | 2025-01-02 15:36:50 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 47740526016f40498312118e2f42a03e | - | - | - | - | 11918 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735803410951 | 2025-01-02 15:36:50 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 11938 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735803414421 | 2025-01-02 15:36:54 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 47740526016f40498312118e2f42a03e | - | - | - | - | 15408 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735803414745 | 2025-01-02 15:36:54 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 47740526016f40498312118e2f42a03e | - | - | - | - | 15731 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735803414767 | 2025-01-02 15:36:54 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 47740526016f40498312118e2f42a03e | - | - | - | - | 15753 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 1 | 1735803414967 | 2025-01-02 15:36:54 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 15953 | 0 | - | - | - | - | main o.r.Reflections Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 

info | 1 | 1735803414994 | 2025-01-02 15:36:54 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 15980 | 0 | - | - | - | - | main o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 1 | 1735803415003 | 2025-01-02 15:36:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 15990 | 0 | - | - | - | - | main o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 1 | 1735803415124 | 2025-01-02 15:36:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 16110 | 0 | - | - | - | - | main o.r.Reflections Reflections took 119 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735803415130 | 2025-01-02 15:36:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 16117 | 0 | - | - | - | - | main o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 1 | 1735803415138 | 2025-01-02 15:36:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 16124 | 0 | - | - | - | - | main o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 1 | 1735803415145 | 2025-01-02 15:36:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 16132 | 0 | - | - | - | - | main o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 1 | 1735803415243 | 2025-01-02 15:36:55 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 16229 | 0 | - | - | - | - | main o.r.Reflections Reflections took 95 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735803417698 | 2025-01-02 15:36:57 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47740526016f40498312118e2f42a03e | - | - | - | - | 18684 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735803418425 | 2025-01-02 15:36:58 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 47740526016f40498312118e2f42a03e | - | - | - | - | 19411 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@dd3b6af with [org.springframework.security.web.session.DisableEncodeUrlFilter@1ff55ec8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@318da38, org.springframework.security.web.context.SecurityContextPersistenceFilter@4e187926, org.springframework.security.web.header.HeaderWriterFilter@e8c8077, org.springframework.security.web.authentication.logout.LogoutFilter@53142bd7, org.springframework.web.filter.CorsFilter@6aa4e927, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@114a18d6, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@6cc86309, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6d2316ec, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2172104e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7c3ed6f1, org.springframework.security.web.session.SessionManagementFilter@4334b9fd, org.springframework.security.web.access.ExceptionTranslationFilter@773c7356, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@dc22668]

info | 1 | 1735803418446 | 2025-01-02 15:36:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47740526016f40498312118e2f42a03e | - | - | - | - | 19432 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735803418535 | 2025-01-02 15:36:58 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47740526016f40498312118e2f42a03e | - | - | - | - | 19521 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735803418537 | 2025-01-02 15:36:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 19523 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735803418538 | 2025-01-02 15:36:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 19524 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735803418541 | 2025-01-02 15:36:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 19550 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735803418581 | 2025-01-02 15:36:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47740526016f40498312118e2f42a03e | - | - | - | - | 19567 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735803418581 | 2025-01-02 15:36:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47740526016f40498312118e2f42a03e | - | - | - | - | 19567 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735803418581 | 2025-01-02 15:36:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47740526016f40498312118e2f42a03e | - | - | - | - | 19567 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735803418831 | 2025-01-02 15:36:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47740526016f40498312118e2f42a03e | - | - | - | - | 19817 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735803418831 | 2025-01-02 15:36:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47740526016f40498312118e2f42a03e | - | - | - | - | 19817 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735803418831 | 2025-01-02 15:36:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47740526016f40498312118e2f42a03e | - | - | - | - | 19817 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735803418831 | 2025-01-02 15:36:58 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47740526016f40498312118e2f42a03e | - | - | - | - | 19817 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735803418950 | 2025-01-02 15:36:58 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 19936 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735803419079 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 20065 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735803419080 | 2025-01-02 15:36:59 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47740526016f40498312118e2f42a03e | - | - | - | - | 20066 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735803419094 | 2025-01-02 15:36:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 20080 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@1556ae16, tech.powerjob.worker.actors.ProcessorTrackerActor@6a093719, tech.powerjob.worker.actors.WorkerActor@6aabf6f5])

info | 1 | 1735803419131 | 2025-01-02 15:36:59 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 47740526016f40498312118e2f42a03e | - | - | - | - | 20117 | 0 | - | - | - | - | main o.r.Reflections Reflections took 25 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735803419137 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 20124 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1735803419138 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 20124 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@439eb934

info | 1 | 1735803419139 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 20125 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@373754a

info | 1 | 1735803419139 | 2025-01-02 15:36:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 20125 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735803419139 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 20125 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735803419142 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 20128 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 124 | 1735803419581 | 2025-01-02 15:36:59 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735803419940 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 20926 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735803419941 | 2025-01-02 15:36:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 20927 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735803419941 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20927 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735803419941 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20927 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735803419942 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 47740526016f40498312118e2f42a03e | - | - | - | - | 20928 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735803419944 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 20930 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735803419946 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 20932 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735803419947 | 2025-01-02 15:36:59 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 20933 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735803419947 | 2025-01-02 15:36:59 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 47740526016f40498312118e2f42a03e | - | - | - | - | 20934 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 808.0 ms

info | 1 | 1735803420135 | 2025-01-02 15:37:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47740526016f40498312118e2f42a03e | - | - | - | - | 21121 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735803420141 | 2025-01-02 15:37:00 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 21128 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735803420142 | 2025-01-02 15:37:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 21128 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735803420147 | 2025-01-02 15:37:00 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 21133 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735803420366 | 2025-01-02 15:37:00 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 21352 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735803420366 | 2025-01-02 15:37:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 21352 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/08493c4f3cab43b5a4fe95577394ece9/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735803420373 | 2025-01-02 15:37:00 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 47740526016f40498312118e2f42a03e | - | - | - | - | 21359 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/08493c4f3cab43b5a4fe95577394ece9/] on JVM exit successfully

info | 1 | 1735803420390 | 2025-01-02 15:37:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47740526016f40498312118e2f42a03e | - | - | - | - | 21376 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735803420391 | 2025-01-02 15:37:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.639 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 47740526016f40498312118e2f42a03e | - | - | - | - | 21377 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.856 s, congratulations!

info | 156 | 1735803420395 | 2025-01-02 15:37:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | ba496cb93bf840f8b3e01806c2f1d96d | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 156 | 1735803420395 | 2025-01-02 15:37:00 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ba496cb93bf840f8b3e01806c2f1d96d | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735803420444 | 2025-01-02 15:37:00 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.641 | *************** | - | 2 | TomcatWebServer | start | 47740526016f40498312118e2f42a03e | - | - | - | - | 21431 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735803420469 | 2025-01-02 15:37:00 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.643 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 47740526016f40498312118e2f42a03e | - | - | - | - | 21455 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735803420496 | 2025-01-02 15:37:00 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.645 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 47740526016f40498312118e2f42a03e | - | - | - | - | 21482 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735803420496 | 2025-01-02 15:37:00 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.647 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 47740526016f40498312118e2f42a03e | - | - | - | - | 21482 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735803420524 | 2025-01-02 15:37:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.649 | *************** | - | 2 | Application | main | 47740526016f40498312118e2f42a03e | - | - | - | - | 21510 | 0 | - | - | - | - | main c.t.g.Application Started Application in 21.94 seconds (JVM running for 22.571)

info | 1 | 1735803420547 | 2025-01-02 15:37:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.651 | *************** | - | 2 | Application | main | 47740526016f40498312118e2f42a03e | - | - | - | - | 21533 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735803420547 | 2025-01-02 15:37:00 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.653 | *************** | - | 2 | Application | main | 47740526016f40498312118e2f42a03e | - | - | - | - | 21533 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 226 | 1735803420554 | 2025-01-02 15:37:00 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 8f0821bd4a6f439d8fa8309dc2b962eb | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 156 | 1735803430393 | 2025-01-02 15:37:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ba496cb93bf840f8b3e01806c2f1d96d | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 226 | 1735803432719 | 2025-01-02 15:37:12 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ChatRoomService | refreshChatCache | 8f0821bd4a6f439d8fa8309dc2b962eb | - | - | - | - | 12165 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 163 | 1735803439554 | 2025-01-02 15:37:19 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 9093d9718c2d4c738a9b3c12ed27fd6d | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 163 | 1735803439555 | 2025-01-02 15:37:19 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 9093d9718c2d4c738a9b3c12ed27fd6d | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 163 | 1735803439564 | 2025-01-02 15:37:19 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 9093d9718c2d4c738a9b3c12ed27fd6d | - | - | - | - | 10 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 9 ms

info | 163 | 1735803439751 | 2025-01-02 15:37:19 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | c8ccfce58676459cac5db6c97e67efec | - | - | - | - | 182 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=c8ccfce58676459cac5db6c97e67efec, request data=[1,3,**********,**********,null,null,[],null,0]

info | 163 | 1735803439758 | 2025-01-02 15:37:19 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | c8ccfce58676459cac5db6c97e67efec | - | - | - | - | 189 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.c.c.c.SoaClient register http client : [http://live-api.test3.hbmonitor.com/v1/Soa/jService] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 156 | 1735803465501 | 2025-01-02 15:37:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ba496cb93bf840f8b3e01806c2f1d96d | - | - | - | - | 45127 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735803465528 | 2025-01-02 15:37:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ba496cb93bf840f8b3e01806c2f1d96d | - | - | - | - | 45133 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735803465529 | 2025-01-02 15:37:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | ba496cb93bf840f8b3e01806c2f1d96d | - | - | - | - | 45134 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735803465837 | 2025-01-02 15:37:45 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | manageMultiLive | hostData | c8ccfce58676459cac5db6c97e67efec | - | - | - | - | 26268 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2025-01-01\",\"host_name\":\"沙雕公共1\",\"host_uuid\":\"bhcdffbchghijhcj\",\"consortia_id\":\"2342696\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1,\"shell_amount\":0,\"total_score\":0,\"room_shell_amount\":0,\"room_total_score\":0,\"meet_shell_amount\":0,\"meet_total_score\":0,\"shell_ratio\":\"0%\"}],\"total\":\"1\"}","dataBlank":false,"dataNotBlank":true}

info | 161 | 1735803470393 | 2025-01-02 15:37:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 3539b4a3da63493fb6323b5853bfe2d5 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1735803473079 | 2025-01-02 15:37:53 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | ef33c01540834892a0d31e0fd6a02eef | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1735803473110 | 2025-01-02 15:37:53 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | ef33c01540834892a0d31e0fd6a02eef | - | - | - | - | 31 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1735803473112 | 2025-01-02 15:37:53 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | ef33c01540834892a0d31e0fd6a02eef | - | - | - | - | 33 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 28 | 1735803473130 | 2025-01-02 15:37:53 | v2/AbstractApplicationContext/destroyBeans | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | AbstractApplicationContext | destroyBeans | ef33c01540834892a0d31e0fd6a02eef | - | - | - | - | 52 | 0 | - | - | - | - | SpringContextShutdownHook o.s.o.j.LocalContainerEntityManagerFactoryBean Closing JPA EntityManagerFactory for persistence unit 'default'

