info | 15 | 1735614545414 | 2024-12-31 11:09:05 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | c30da64e07954390b55f5f594e8c86ec | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735614545426 | 2024-12-31 11:09:05 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 86940 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1735614545439 | 2024-12-31 11:09:05 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735614546259 | 2024-12-31 11:09:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 827 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735614546262 | 2024-12-31 11:09:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 831 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735614546265 | 2024-12-31 11:09:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 833 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735614546267 | 2024-12-31 11:09:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 835 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735614546269 | 2024-12-31 11:09:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 837 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735614546320 | 2024-12-31 11:09:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 888 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735614546370 | 2024-12-31 11:09:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 938 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735614546374 | 2024-12-31 11:09:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 942 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735614546375 | 2024-12-31 11:09:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 943 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735614546432 | 2024-12-31 11:09:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 1001 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735614548891 | 2024-12-31 11:09:08 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 3459 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735614548892 | 2024-12-31 11:09:08 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 3461 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735614548937 | 2024-12-31 11:09:08 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 3505 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.

info | 1 | 1735614548947 | 2024-12-31 11:09:08 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 3516 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735614548948 | 2024-12-31 11:09:08 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 3516 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735614548974 | 2024-12-31 11:09:08 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 3542 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1735614549766 | 2024-12-31 11:09:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 4337 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$69a3f647] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735614549787 | 2024-12-31 11:09:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 4355 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$e8aa3bbf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735614549851 | 2024-12-31 11:09:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 4419 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$67134688] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735614549855 | 2024-12-31 11:09:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 4424 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735614549915 | 2024-12-31 11:09:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 4483 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735614549919 | 2024-12-31 11:09:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 4487 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735614550467 | 2024-12-31 11:09:10 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 5035 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735614550482 | 2024-12-31 11:09:10 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 5050 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735614550483 | 2024-12-31 11:09:10 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 5051 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735614550587 | 2024-12-31 11:09:10 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 5155 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735614557525 | 2024-12-31 11:09:17 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 12093 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735614557601 | 2024-12-31 11:09:17 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 12169 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735614557641 | 2024-12-31 11:09:17 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 12209 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735614557750 | 2024-12-31 11:09:17 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 12319 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735614557839 | 2024-12-31 11:09:17 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 12408 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735614557961 | 2024-12-31 11:09:17 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 12529 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735614557969 | 2024-12-31 11:09:17 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 12537 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735614562068 | 2024-12-31 11:09:22 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 16636 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735614562502 | 2024-12-31 11:09:22 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 17070 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735614562532 | 2024-12-31 11:09:22 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 17101 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 1 | 1735614562764 | 2024-12-31 11:09:22 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 17332 | 0 | - | - | - | - | main o.r.Reflections Reflections took 55 ms to scan 1 urls, producing 3 keys and 6 values 

info | 1 | 1735614562819 | 2024-12-31 11:09:22 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 17387 | 0 | - | - | - | - | main o.r.Reflections Reflections took 27 ms to scan 1 urls, producing 4 keys and 9 values 

info | 1 | 1735614562837 | 2024-12-31 11:09:22 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 17405 | 0 | - | - | - | - | main o.r.Reflections Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 

info | 1 | 1735614562989 | 2024-12-31 11:09:22 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 17558 | 0 | - | - | - | - | main o.r.Reflections Reflections took 148 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735614562996 | 2024-12-31 11:09:22 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 17564 | 0 | - | - | - | - | main o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 1 | 1735614563017 | 2024-12-31 11:09:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 17586 | 0 | - | - | - | - | main o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 

info | 1 | 1735614563087 | 2024-12-31 11:09:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 17655 | 0 | - | - | - | - | main o.r.Reflections Reflections took 67 ms to scan 1 urls, producing 2 keys and 8 values 

info | 1 | 1735614563216 | 2024-12-31 11:09:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 17784 | 0 | - | - | - | - | main o.r.Reflections Reflections took 125 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735614565759 | 2024-12-31 11:09:25 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 20327 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735614566514 | 2024-12-31 11:09:26 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21082 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@38957508 with [org.springframework.security.web.session.DisableEncodeUrlFilter@1e174f2b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@eb428c8, org.springframework.security.web.context.SecurityContextPersistenceFilter@5e399c93, org.springframework.security.web.header.HeaderWriterFilter@49ac275, org.springframework.security.web.authentication.logout.LogoutFilter@150b294b, org.springframework.web.filter.CorsFilter@236e2729, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@36901f79, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@80c17f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7c78560c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1753339a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@13aec952, org.springframework.security.web.session.SessionManagementFilter@40ec894a, org.springframework.security.web.access.ExceptionTranslationFilter@3f32c47a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@770cf7e6]

info | 1 | 1735614566529 | 2024-12-31 11:09:26 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21097 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735614566602 | 2024-12-31 11:09:26 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21170 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735614566604 | 2024-12-31 11:09:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21172 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735614566605 | 2024-12-31 11:09:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21173 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735614566607 | 2024-12-31 11:09:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21175 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735614566609 | 2024-12-31 11:09:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21177 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735614566610 | 2024-12-31 11:09:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21178 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735614566610 | 2024-12-31 11:09:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21178 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735614566920 | 2024-12-31 11:09:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21489 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735614566921 | 2024-12-31 11:09:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21489 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735614566921 | 2024-12-31 11:09:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21489 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735614566921 | 2024-12-31 11:09:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21489 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735614567053 | 2024-12-31 11:09:27 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21621 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735614567196 | 2024-12-31 11:09:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21765 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735614567198 | 2024-12-31 11:09:27 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21766 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735614567215 | 2024-12-31 11:09:27 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21783 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@578f6b1c, tech.powerjob.worker.actors.ProcessorTrackerActor@165a1ac4, tech.powerjob.worker.actors.WorkerActor@29d5a7ac])

info | 1 | 1735614567255 | 2024-12-31 11:09:27 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21823 | 0 | - | - | - | - | main o.r.Reflections Reflections took 26 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735614567261 | 2024-12-31 11:09:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21829 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1735614567262 | 2024-12-31 11:09:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21830 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@641059e2

info | 1 | 1735614567263 | 2024-12-31 11:09:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21831 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@5f12058

info | 1 | 1735614567263 | 2024-12-31 11:09:27 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21831 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735614567263 | 2024-12-31 11:09:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21831 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735614567266 | 2024-12-31 11:09:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 21834 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 123 | 1735614567779 | 2024-12-31 11:09:27 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735614568235 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22804 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735614568236 | 2024-12-31 11:09:28 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22804 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735614568237 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22805 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735614568237 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22805 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735614568237 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22805 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735614568237 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22805 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735614568237 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22805 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735614568237 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22805 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735614568237 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22805 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735614568238 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22806 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735614568238 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22806 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735614568238 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22806 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735614568238 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22806 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735614568238 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22806 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735614568238 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22806 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735614568240 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22808 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735614568242 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22811 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735614568243 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22811 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735614568243 | 2024-12-31 11:09:28 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22812 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 980.6 ms

info | 1 | 1735614568371 | 2024-12-31 11:09:28 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22939 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735614568377 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22945 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735614568378 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22946 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735614568382 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 22950 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735614568611 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23179 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735614568611 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23179 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/bbfafc809b3a470782c7b1123bc5905b/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735614568616 | 2024-12-31 11:09:28 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23184 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/bbfafc809b3a470782c7b1123bc5905b/] on JVM exit successfully

info | 1 | 1735614568630 | 2024-12-31 11:09:28 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23198 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735614568631 | 2024-12-31 11:09:28 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.639 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23199 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 2.029 s, congratulations!

info | 157 | 1735614568634 | 2024-12-31 11:09:28 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 157 | 1735614568634 | 2024-12-31 11:09:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735614568680 | 2024-12-31 11:09:28 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.641 | *************** | - | 2 | TomcatWebServer | start | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23249 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735614568715 | 2024-12-31 11:09:28 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.643 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23283 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735614568805 | 2024-12-31 11:09:28 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.645 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23373 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735614568805 | 2024-12-31 11:09:28 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.647 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23373 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735614568842 | 2024-12-31 11:09:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.649 | *************** | - | 2 | Application | main | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23410 | 0 | - | - | - | - | main c.t.g.Application Started Application in 23.885 seconds (JVM running for 24.72)

info | 1 | 1735614568877 | 2024-12-31 11:09:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.651 | *************** | - | 2 | Application | main | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23445 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735614568877 | 2024-12-31 11:09:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.653 | *************** | - | 2 | Application | main | 4bbddc2b314a47f4a3e2b5824ba5cda5 | - | - | - | - | 23445 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 227 | 1735614568883 | 2024-12-31 11:09:28 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | a9627a6a36b84ad89786fd352de84da3 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 164 | 1735614576488 | 2024-12-31 11:09:36 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 76212faa06e94229a8b570e22de8ec2e | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 164 | 1735614576489 | 2024-12-31 11:09:36 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 76212faa06e94229a8b570e22de8ec2e | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 164 | 1735614576495 | 2024-12-31 11:09:36 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 76212faa06e94229a8b570e22de8ec2e | - | - | - | - | 7 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 6 ms

info | 157 | 1735614578634 | 2024-12-31 11:09:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 164 | 1735614579177 | 2024-12-31 11:09:39 | v2/manageMultiLive/liveBizData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | liveBizData | f86d4d77bd424a7a9af0a78649124fc1 | - | - | - | - | 2673 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.s.m.i.MultiLiveStatsServiceImpl 请求数据侧数据信息【/touch/live/MultiLiveDetail】的请求参数为:{"dt":["20211023","20241224"]},响应结果为:{"list":[{"dt":"2024-10-10","multi_live_active_unt_1d":1081,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"1382.12","multi_live_consume_unt_1d":38,"multi_live_active_host_unt_1d":7,"multi_live_meet_host_unt_1d":16,"arpu":"1.28","arppu":"36.37"},{"dt":"2024-10-09","multi_live_active_unt_1d":600,"multi_live_retation_ratio_2d":"10.17%","multi_live_consume_amt_1d":"1017.21","multi_live_consume_unt_1d":21,"multi_live_active_host_unt_1d":6,"multi_live_meet_host_unt_1d":15,"arpu":"1.70","arppu":"48.44"},{"dt":"2024-10-08","multi_live_active_unt_1d":1039,"multi_live_retation_ratio_2d":"5.00%","multi_live_consume_amt_1d":"1550.9","multi_live_consume_unt_1d":25,"multi_live_active_host_unt_1d":8,"multi_live_meet_host_unt_1d":16,"arpu":"1.49","arppu":"62.04"},{"dt":"2024-09-04","multi_live_active_unt_1d":891,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"817.7","multi_live_consume_unt_1d":11,"multi_live_active_host_unt_1d":13,"multi_live_meet_host_unt_1d":9,"arpu":"0.92","arppu":"74.34"},{"dt":"2024-09-03","multi_live_active_unt_1d":62,"multi_live_retation_ratio_2d":"12.90%","multi_live_consume_amt_1d":"323.8","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":5,"multi_live_meet_host_unt_1d":2,"arpu":"5.22","arppu":"107.93"},{"dt":"2024-08-30","multi_live_active_unt_1d":23,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"1","multi_live_consume_unt_1d":1,"multi_live_active_host_unt_1d":3,"multi_live_meet_host_unt_1d":2,"arpu":"0.04","arppu":"1.00"},{"dt":"2024-08-19","multi_live_active_unt_1d":7,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"11.09","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":5,"multi_live_meet_host_unt_1d":5,"arpu":"1.58","arppu":"3.70"},{"dt":"2024-08-14","multi_live_active_unt_1d":20,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"103.02","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":7,"multi_live_meet_host_unt_1d":12,"arpu":"5.15","arppu":"34.34"},{"dt":"2024-08-08","multi_live_active_unt_1d":100,"multi_live_retation_ratio_2d":"50.00%","multi_live_consume_amt_1d":"1000000000","multi_live_consume_unt_1d":60,"multi_live_active_host_unt_1d":20,"multi_live_meet_host_unt_1d":30,"arpu":"10000000.00","arppu":"16666666.67"},{"dt":"2024-08-07","multi_live_active_unt_1d":33,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"122576.3","multi_live_consume_unt_1d":7,"multi_live_active_host_unt_1d":40,"multi_live_meet_host_unt_1d":21,"arpu":"3714.43","arppu":"17510.90"}],"total":17,"pageSize":10,"page":1}

info | 227 | 1735614579506 | 2024-12-31 11:09:39 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ChatRoomService | refreshChatCache | a9627a6a36b84ad89786fd352de84da3 | - | - | - | - | 10623 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 157 | 1735614588635 | 2024-12-31 11:09:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 20002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614598635 | 2024-12-31 11:09:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 30002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614608635 | 2024-12-31 11:10:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 40002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614618635 | 2024-12-31 11:10:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 50001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614628637 | 2024-12-31 11:10:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 60004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614638635 | 2024-12-31 11:10:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 70002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614648637 | 2024-12-31 11:10:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 80003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614658637 | 2024-12-31 11:10:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 90003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614668638 | 2024-12-31 11:11:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 100005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614678634 | 2024-12-31 11:11:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 110000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614688637 | 2024-12-31 11:11:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 120004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614698637 | 2024-12-31 11:11:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 130004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614708636 | 2024-12-31 11:11:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 140002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614718638 | 2024-12-31 11:11:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 150004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614728639 | 2024-12-31 11:12:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 160005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614738640 | 2024-12-31 11:12:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 170007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614748640 | 2024-12-31 11:12:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 180008 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614758637 | 2024-12-31 11:12:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 190004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614768641 | 2024-12-31 11:12:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 200007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735614778639 | 2024-12-31 11:12:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4f244c3439524dceac7e826d9f0fcf5e | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735614788641 | 2024-12-31 11:13:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4f244c3439524dceac7e826d9f0fcf5e | - | - | - | - | 10004 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735614798639 | 2024-12-31 11:13:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4f244c3439524dceac7e826d9f0fcf5e | - | - | - | - | 20001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735614808639 | 2024-12-31 11:13:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4f244c3439524dceac7e826d9f0fcf5e | - | - | - | - | 30000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735614818643 | 2024-12-31 11:13:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4f244c3439524dceac7e826d9f0fcf5e | - | - | - | - | 40004 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735614828640 | 2024-12-31 11:13:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4f244c3439524dceac7e826d9f0fcf5e | - | - | - | - | 50001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735614838642 | 2024-12-31 11:13:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4f244c3439524dceac7e826d9f0fcf5e | - | - | - | - | 60003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614848641 | 2024-12-31 11:14:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 280007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614858643 | 2024-12-31 11:14:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 290009 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735614868643 | 2024-12-31 11:14:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 62f88de88c9b42b5b5570ff2b2f07320 | - | - | - | - | 300010 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735614878643 | 2024-12-31 11:14:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4f244c3439524dceac7e826d9f0fcf5e | - | - | - | - | 100005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735614888644 | 2024-12-31 11:14:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4f244c3439524dceac7e826d9f0fcf5e | - | - | - | - | 110005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735614898643 | 2024-12-31 11:14:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 4f244c3439524dceac7e826d9f0fcf5e | - | - | - | - | 120006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1735614900034 | 2024-12-31 11:15:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | e0d4d71515d7422a86fdc1e35cbf2d1b | - | - | - | - | 1 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-12-31 11:09:22,501 to 2024-12-31 11:15:00,024
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 1 | 1735615139200 | 2024-12-31 11:18:59 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 87347 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1735615139194 | 2024-12-31 11:18:59 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | d114319bddbd4c8d88616f69603872ae | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735615139214 | 2024-12-31 11:18:59 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735615139739 | 2024-12-31 11:18:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 531 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735615139741 | 2024-12-31 11:18:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 533 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735615139744 | 2024-12-31 11:18:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 536 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735615139746 | 2024-12-31 11:18:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 538 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735615139747 | 2024-12-31 11:18:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 539 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735615139783 | 2024-12-31 11:18:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 575 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735615139814 | 2024-12-31 11:18:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 606 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735615139816 | 2024-12-31 11:18:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 608 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735615139817 | 2024-12-31 11:18:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 609 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735615139867 | 2024-12-31 11:18:59 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 659 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735615142014 | 2024-12-31 11:19:02 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 2806 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735615142015 | 2024-12-31 11:19:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 2807 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735615142054 | 2024-12-31 11:19:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 2847 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 33 ms. Found 0 JPA repository interfaces.

info | 1 | 1735615142064 | 2024-12-31 11:19:02 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 2856 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735615142065 | 2024-12-31 11:19:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 2857 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735615142090 | 2024-12-31 11:19:02 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 2882 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.

info | 1 | 1735615142817 | 2024-12-31 11:19:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 3613 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$b9aefdf0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615142838 | 2024-12-31 11:19:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 3630 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$38b54368] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615142906 | 2024-12-31 11:19:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 3698 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$b71e4e31] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615142911 | 2024-12-31 11:19:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 3703 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615142973 | 2024-12-31 11:19:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 3765 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615142976 | 2024-12-31 11:19:02 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 3768 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615143531 | 2024-12-31 11:19:03 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 4324 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735615143539 | 2024-12-31 11:19:03 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 4331 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735615143540 | 2024-12-31 11:19:03 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 4332 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735615143626 | 2024-12-31 11:19:03 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 4419 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735615150583 | 2024-12-31 11:19:10 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 11376 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735615150659 | 2024-12-31 11:19:10 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 11451 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735615150700 | 2024-12-31 11:19:10 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 11492 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735615150806 | 2024-12-31 11:19:10 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 11598 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735615150897 | 2024-12-31 11:19:10 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 11689 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735615151013 | 2024-12-31 11:19:11 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 11805 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735615151021 | 2024-12-31 11:19:11 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 11813 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735615154593 | 2024-12-31 11:19:14 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 15385 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735615154862 | 2024-12-31 11:19:14 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 15654 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735615154883 | 2024-12-31 11:19:14 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 15675 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735615155043 | 2024-12-31 11:19:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | af33d6fdb0d14e7589694345ae0bf617 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735615155067 | 2024-12-31 11:19:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | af33d6fdb0d14e7589694345ae0bf617 | - | - | - | - | 24 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735615155077 | 2024-12-31 11:19:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | af33d6fdb0d14e7589694345ae0bf617 | - | - | - | - | 34 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735615155203 | 2024-12-31 11:19:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | af33d6fdb0d14e7589694345ae0bf617 | - | - | - | - | 160 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 123 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735615155209 | 2024-12-31 11:19:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | af33d6fdb0d14e7589694345ae0bf617 | - | - | - | - | 166 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735615155217 | 2024-12-31 11:19:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | af33d6fdb0d14e7589694345ae0bf617 | - | - | - | - | 174 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735615155226 | 2024-12-31 11:19:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | af33d6fdb0d14e7589694345ae0bf617 | - | - | - | - | 183 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735615155335 | 2024-12-31 11:19:15 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | af33d6fdb0d14e7589694345ae0bf617 | - | - | - | - | 292 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 106 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735615157910 | 2024-12-31 11:19:17 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 18702 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735615158629 | 2024-12-31 11:19:18 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19422 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@643d449a with [org.springframework.security.web.session.DisableEncodeUrlFilter@17fdbefb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@57d86841, org.springframework.security.web.context.SecurityContextPersistenceFilter@60015b68, org.springframework.security.web.header.HeaderWriterFilter@154e31a5, org.springframework.security.web.authentication.logout.LogoutFilter@4ef0df44, org.springframework.web.filter.CorsFilter@3aa1669a, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@4068dbed, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@218b7797, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7e4bd8b7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@46241f9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2f9ceea8, org.springframework.security.web.session.SessionManagementFilter@5690126f, org.springframework.security.web.access.ExceptionTranslationFilter@716516ce, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@146a81e4]

info | 1 | 1735615158644 | 2024-12-31 11:19:18 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19436 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735615158720 | 2024-12-31 11:19:18 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19513 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735615158722 | 2024-12-31 11:19:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19514 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735615158723 | 2024-12-31 11:19:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19515 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735615158725 | 2024-12-31 11:19:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19517 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735615158727 | 2024-12-31 11:19:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19520 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735615158728 | 2024-12-31 11:19:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19520 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735615158728 | 2024-12-31 11:19:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19520 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735615158874 | 2024-12-31 11:19:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19666 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735615158874 | 2024-12-31 11:19:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19666 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735615158874 | 2024-12-31 11:19:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19667 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735615158875 | 2024-12-31 11:19:18 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19667 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735615158929 | 2024-12-31 11:19:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19722 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735615158989 | 2024-12-31 11:19:18 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19781 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735615158990 | 2024-12-31 11:19:18 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19782 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735615159000 | 2024-12-31 11:19:19 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19792 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@71b85c62, tech.powerjob.worker.actors.ProcessorTrackerActor@7701253, tech.powerjob.worker.actors.WorkerActor@6be6d315])

info | 1 | 1735615159029 | 2024-12-31 11:19:19 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19822 | 0 | - | - | - | - | main o.r.Reflections Reflections took 20 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735615159036 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19828 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1735615159037 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19829 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@2ca18d8a

info | 1 | 1735615159037 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19829 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@465f49f2

info | 1 | 1735615159038 | 2024-12-31 11:19:19 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19830 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735615159038 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19830 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735615159041 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 19833 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 126 | 1735615159524 | 2024-12-31 11:19:19 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735615159966 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20758 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735615159967 | 2024-12-31 11:19:19 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20759 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735615159967 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20759 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735615159967 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20759 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735615159967 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20759 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735615159967 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20759 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735615159968 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20760 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735615159968 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20760 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735615159968 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20760 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735615159968 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20760 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735615159968 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20760 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735615159968 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20760 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735615159968 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20760 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735615159968 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20761 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735615159969 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20761 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735615159970 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20762 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735615159972 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20764 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735615159972 | 2024-12-31 11:19:19 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20765 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735615159973 | 2024-12-31 11:19:19 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20766 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 935.6 ms

info | 1 | 1735615160036 | 2024-12-31 11:19:20 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20829 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735615160041 | 2024-12-31 11:19:20 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20833 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735615160041 | 2024-12-31 11:19:20 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20833 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735615160045 | 2024-12-31 11:19:20 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 20837 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735615160265 | 2024-12-31 11:19:20 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21057 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735615160266 | 2024-12-31 11:19:20 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21058 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/e69bb3b60a6347c8994af25884bc239d/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735615160275 | 2024-12-31 11:19:20 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21067 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/e69bb3b60a6347c8994af25884bc239d/] on JVM exit successfully

info | 1 | 1735615160294 | 2024-12-31 11:19:20 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21086 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735615160295 | 2024-12-31 11:19:20 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21087 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.575 s, congratulations!

info | 156 | 1735615160301 | 2024-12-31 11:19:20 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 646329e1ac4f4795a9da52d0ac51f6da | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 156 | 1735615160303 | 2024-12-31 11:19:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 646329e1ac4f4795a9da52d0ac51f6da | - | - | - | - | 41 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735615160686 | 2024-12-31 11:19:20 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | TomcatWebServer | start | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21478 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735615160849 | 2024-12-31 11:19:20 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21641 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735615160881 | 2024-12-31 11:19:20 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21673 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735615160881 | 2024-12-31 11:19:20 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21673 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735615160939 | 2024-12-31 11:19:20 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21731 | 0 | - | - | - | - | main c.t.g.Application Started Application in 22.162 seconds (JVM running for 22.746)

info | 1 | 1735615160971 | 2024-12-31 11:19:20 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21763 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735615160971 | 2024-12-31 11:19:20 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | Application | main | 9606d88c13344ca1b631a8ca58938ac3 | - | - | - | - | 21763 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 226 | 1735615160978 | 2024-12-31 11:19:20 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 8cdccc19fbef4b37ad5d066bbd905ebd | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 156 | 1735615170298 | 2024-12-31 11:19:30 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 646329e1ac4f4795a9da52d0ac51f6da | - | - | - | - | 9998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 226 | 1735615171474 | 2024-12-31 11:19:31 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ChatRoomService | refreshChatCache | 8cdccc19fbef4b37ad5d066bbd905ebd | - | - | - | - | 10497 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 160 | 1735615180301 | 2024-12-31 11:19:40 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735615190301 | 2024-12-31 11:19:50 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1735615193083 | 2024-12-31 11:19:53 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 93910e09b89448feafb8d6f7a45038dd | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 165 | 1735615193084 | 2024-12-31 11:19:53 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 93910e09b89448feafb8d6f7a45038dd | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 165 | 1735615193089 | 2024-12-31 11:19:53 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 93910e09b89448feafb8d6f7a45038dd | - | - | - | - | 7 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 5 ms

info | 156 | 1735615216281 | 2024-12-31 11:20:16 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 646329e1ac4f4795a9da52d0ac51f6da | - | - | - | - | 55982 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735615216301 | 2024-12-31 11:20:16 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 646329e1ac4f4795a9da52d0ac51f6da | - | - | - | - | 56000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1735615216410 | 2024-12-31 11:20:16 | v2/manageMultiLive/liveBizData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | liveBizData | e4bf349f97454a47b4addd0c78264766 | - | - | - | - | 23314 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.s.m.i.MultiLiveStatsServiceImpl 请求数据侧数据信息【/touch/live/MultiLiveDetail】的请求参数为:{"dt":["20211023","20241224"]},响应结果为:{"list":[{"dt":"2024-10-10","multi_live_active_unt_1d":1081,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"1382.12","multi_live_consume_unt_1d":38,"multi_live_active_host_unt_1d":7,"multi_live_meet_host_unt_1d":16,"arpu":"1.28","arppu":"36.37"},{"dt":"2024-10-09","multi_live_active_unt_1d":600,"multi_live_retation_ratio_2d":"10.17%","multi_live_consume_amt_1d":"1017.21","multi_live_consume_unt_1d":21,"multi_live_active_host_unt_1d":6,"multi_live_meet_host_unt_1d":15,"arpu":"1.70","arppu":"48.44"},{"dt":"2024-10-08","multi_live_active_unt_1d":1039,"multi_live_retation_ratio_2d":"5.00%","multi_live_consume_amt_1d":"1550.9","multi_live_consume_unt_1d":25,"multi_live_active_host_unt_1d":8,"multi_live_meet_host_unt_1d":16,"arpu":"1.49","arppu":"62.04"},{"dt":"2024-09-04","multi_live_active_unt_1d":891,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"817.7","multi_live_consume_unt_1d":11,"multi_live_active_host_unt_1d":13,"multi_live_meet_host_unt_1d":9,"arpu":"0.92","arppu":"74.34"},{"dt":"2024-09-03","multi_live_active_unt_1d":62,"multi_live_retation_ratio_2d":"12.90%","multi_live_consume_amt_1d":"323.8","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":5,"multi_live_meet_host_unt_1d":2,"arpu":"5.22","arppu":"107.93"},{"dt":"2024-08-30","multi_live_active_unt_1d":23,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"1","multi_live_consume_unt_1d":1,"multi_live_active_host_unt_1d":3,"multi_live_meet_host_unt_1d":2,"arpu":"0.04","arppu":"1.00"},{"dt":"2024-08-19","multi_live_active_unt_1d":7,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"11.09","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":5,"multi_live_meet_host_unt_1d":5,"arpu":"1.58","arppu":"3.70"},{"dt":"2024-08-14","multi_live_active_unt_1d":20,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"103.02","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":7,"multi_live_meet_host_unt_1d":12,"arpu":"5.15","arppu":"34.34"},{"dt":"2024-08-08","multi_live_active_unt_1d":100,"multi_live_retation_ratio_2d":"50.00%","multi_live_consume_amt_1d":"1000000000","multi_live_consume_unt_1d":60,"multi_live_active_host_unt_1d":20,"multi_live_meet_host_unt_1d":30,"arpu":"10000000.00","arppu":"16666666.67"},{"dt":"2024-08-07","multi_live_active_unt_1d":33,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"122576.3","multi_live_consume_unt_1d":7,"multi_live_active_host_unt_1d":40,"multi_live_meet_host_unt_1d":21,"arpu":"3714.43","arppu":"17510.90"}],"total":17,"pageSize":10,"page":1}

info | 160 | 1735615220301 | 2024-12-31 11:20:20 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 40000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735615241660 | 2024-12-31 11:20:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 61360 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735615241665 | 2024-12-31 11:20:41 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 61363 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735615307782 | 2024-12-31 11:21:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 127483 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 166 | 1735615307784 | 2024-12-31 11:21:47 | v2/manageMultiLive/liveBizData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | liveBizData | 3d2a3a617cad457fbc6ed085f169a7c8 | - | - | - | - | 79866 | 0 | - | - | - | - | http-nio-8087-exec-3 c.t.g.s.s.m.i.MultiLiveStatsServiceImpl 请求数据侧数据信息【/touch/live/MultiLiveDetail】的请求参数为:{"dt":["20211023","20241224"]},响应结果为:{"list":[{"dt":"2024-10-10","multi_live_active_unt_1d":1081,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"1382.12","multi_live_consume_unt_1d":38,"multi_live_active_host_unt_1d":7,"multi_live_meet_host_unt_1d":16,"arpu":"1.28","arppu":"36.37"},{"dt":"2024-10-09","multi_live_active_unt_1d":600,"multi_live_retation_ratio_2d":"10.17%","multi_live_consume_amt_1d":"1017.21","multi_live_consume_unt_1d":21,"multi_live_active_host_unt_1d":6,"multi_live_meet_host_unt_1d":15,"arpu":"1.70","arppu":"48.44"},{"dt":"2024-10-08","multi_live_active_unt_1d":1039,"multi_live_retation_ratio_2d":"5.00%","multi_live_consume_amt_1d":"1550.9","multi_live_consume_unt_1d":25,"multi_live_active_host_unt_1d":8,"multi_live_meet_host_unt_1d":16,"arpu":"1.49","arppu":"62.04"},{"dt":"2024-09-04","multi_live_active_unt_1d":891,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"817.7","multi_live_consume_unt_1d":11,"multi_live_active_host_unt_1d":13,"multi_live_meet_host_unt_1d":9,"arpu":"0.92","arppu":"74.34"},{"dt":"2024-09-03","multi_live_active_unt_1d":62,"multi_live_retation_ratio_2d":"12.90%","multi_live_consume_amt_1d":"323.8","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":5,"multi_live_meet_host_unt_1d":2,"arpu":"5.22","arppu":"107.93"},{"dt":"2024-08-30","multi_live_active_unt_1d":23,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"1","multi_live_consume_unt_1d":1,"multi_live_active_host_unt_1d":3,"multi_live_meet_host_unt_1d":2,"arpu":"0.04","arppu":"1.00"},{"dt":"2024-08-19","multi_live_active_unt_1d":7,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"11.09","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":5,"multi_live_meet_host_unt_1d":5,"arpu":"1.58","arppu":"3.70"},{"dt":"2024-08-14","multi_live_active_unt_1d":20,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"103.02","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":7,"multi_live_meet_host_unt_1d":12,"arpu":"5.15","arppu":"34.34"},{"dt":"2024-08-08","multi_live_active_unt_1d":100,"multi_live_retation_ratio_2d":"50.00%","multi_live_consume_amt_1d":"1000000000","multi_live_consume_unt_1d":60,"multi_live_active_host_unt_1d":20,"multi_live_meet_host_unt_1d":30,"arpu":"10000000.00","arppu":"16666666.67"},{"dt":"2024-08-07","multi_live_active_unt_1d":33,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"122576.3","multi_live_consume_unt_1d":7,"multi_live_active_host_unt_1d":40,"multi_live_meet_host_unt_1d":21,"arpu":"3714.43","arppu":"17510.90"}],"total":17,"pageSize":10,"page":1}

info | 160 | 1735615307821 | 2024-12-31 11:21:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 127519 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735615307821 | 2024-12-31 11:21:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 127519 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735615307822 | 2024-12-31 11:21:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 127520 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735615307822 | 2024-12-31 11:21:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 127520 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 160 | 1735615307822 | 2024-12-31 11:21:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0d5da03af5aa4ebf9dbc0d8eb7d9556f | - | - | - | - | 127520 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 28 | 1735615307979 | 2024-12-31 11:21:47 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | ee0ceecf2a3b401ab138fe13864e7cca | - | - | - | - | 0 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskScheduler Shutting down ExecutorService 'taskScheduler'

info | 28 | 1735615308081 | 2024-12-31 11:21:48 | v2/DefaultListableBeanFactory/destroySingletons | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | DefaultListableBeanFactory | destroySingletons | ee0ceecf2a3b401ab138fe13864e7cca | - | - | - | - | 104 | 0 | - | - | - | - | SpringContextShutdownHook o.s.s.c.ThreadPoolTaskExecutor Shutting down ExecutorService 'applicationTaskExecutor'

info | 28 | 1735615308085 | 2024-12-31 11:21:48 | v2/NativeMethodAccessorImpl/invoke0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | NativeMethodAccessorImpl | invoke0 | ee0ceecf2a3b401ab138fe13864e7cca | - | - | - | - | 107 | 0 | - | - | - | - | SpringContextShutdownHook c.a.j.s.DefaultMetricsManager cache stat canceled

info | 1 | 1735615369343 | 2024-12-31 11:22:49 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 87486 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | 1735615369336 | 2024-12-31 11:22:49 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | b7bd446905a3422fbfe28f4fe91547a2 | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735615369355 | 2024-12-31 11:22:49 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735615369843 | 2024-12-31 11:22:49 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 496 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735615369846 | 2024-12-31 11:22:49 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 498 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735615369848 | 2024-12-31 11:22:49 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 500 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735615369850 | 2024-12-31 11:22:49 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 502 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735615369851 | 2024-12-31 11:22:49 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 503 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735615369893 | 2024-12-31 11:22:49 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 545 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735615369925 | 2024-12-31 11:22:49 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 577 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735615369927 | 2024-12-31 11:22:49 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 579 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735615369927 | 2024-12-31 11:22:49 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 579 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735615369976 | 2024-12-31 11:22:49 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 629 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735615372031 | 2024-12-31 11:22:52 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 2684 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735615372033 | 2024-12-31 11:22:52 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 2685 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735615372078 | 2024-12-31 11:22:52 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 2730 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 38 ms. Found 0 JPA repository interfaces.

info | 1 | 1735615372089 | 2024-12-31 11:22:52 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 2741 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735615372090 | 2024-12-31 11:22:52 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 2742 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735615372118 | 2024-12-31 11:22:52 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 2770 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.

info | 1 | 1735615373090 | 2024-12-31 11:22:53 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 3746 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$ebde3e1a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615373115 | 2024-12-31 11:22:53 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 3767 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$6ae48392] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615373183 | 2024-12-31 11:22:53 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 3835 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$e94d8e5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615373187 | 2024-12-31 11:22:53 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 3839 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615373244 | 2024-12-31 11:22:53 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 3896 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615373249 | 2024-12-31 11:22:53 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 3901 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615373949 | 2024-12-31 11:22:53 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 4601 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735615373964 | 2024-12-31 11:22:53 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 4616 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735615373965 | 2024-12-31 11:22:53 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 4617 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735615374050 | 2024-12-31 11:22:54 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 4702 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735615380997 | 2024-12-31 11:23:00 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 11649 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735615381075 | 2024-12-31 11:23:01 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 11728 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735615381114 | 2024-12-31 11:23:01 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 11766 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735615381223 | 2024-12-31 11:23:01 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 11875 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735615381312 | 2024-12-31 11:23:01 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 11964 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735615381436 | 2024-12-31 11:23:01 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 12089 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735615381444 | 2024-12-31 11:23:01 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 12096 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735615384999 | 2024-12-31 11:23:04 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 15651 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735615385298 | 2024-12-31 11:23:05 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 15950 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735615385328 | 2024-12-31 11:23:05 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 15981 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735615385486 | 2024-12-31 11:23:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | fa19877b7faf4948ae5d57b79b0e5516 | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735615385510 | 2024-12-31 11:23:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | fa19877b7faf4948ae5d57b79b0e5516 | - | - | - | - | 24 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735615385518 | 2024-12-31 11:23:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | fa19877b7faf4948ae5d57b79b0e5516 | - | - | - | - | 32 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735615385630 | 2024-12-31 11:23:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | fa19877b7faf4948ae5d57b79b0e5516 | - | - | - | - | 144 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 110 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735615385636 | 2024-12-31 11:23:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | fa19877b7faf4948ae5d57b79b0e5516 | - | - | - | - | 150 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735615385643 | 2024-12-31 11:23:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | fa19877b7faf4948ae5d57b79b0e5516 | - | - | - | - | 157 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735615385650 | 2024-12-31 11:23:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | fa19877b7faf4948ae5d57b79b0e5516 | - | - | - | - | 165 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735615385758 | 2024-12-31 11:23:05 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | fa19877b7faf4948ae5d57b79b0e5516 | - | - | - | - | 272 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 105 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735615388276 | 2024-12-31 11:23:08 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 18928 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735615389026 | 2024-12-31 11:23:09 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 19678 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@47d5e231 with [org.springframework.security.web.session.DisableEncodeUrlFilter@2e91c245, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@421e8dfa, org.springframework.security.web.context.SecurityContextPersistenceFilter@540eb8f7, org.springframework.security.web.header.HeaderWriterFilter@37f2f6b8, org.springframework.security.web.authentication.logout.LogoutFilter@4fc424b, org.springframework.web.filter.CorsFilter@66c8c875, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@32b72d9d, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@568bac83, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@45f668da, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4103e6b7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3ea71809, org.springframework.security.web.session.SessionManagementFilter@3e1f3e1, org.springframework.security.web.access.ExceptionTranslationFilter@5e59888e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@ad89c4]

info | 1 | 1735615389044 | 2024-12-31 11:23:09 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 19697 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735615389127 | 2024-12-31 11:23:09 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 19779 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735615389129 | 2024-12-31 11:23:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 19781 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735615389130 | 2024-12-31 11:23:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 19782 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735615389132 | 2024-12-31 11:23:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 19784 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735615389135 | 2024-12-31 11:23:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 19787 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735615389135 | 2024-12-31 11:23:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 19787 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735615389135 | 2024-12-31 11:23:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 19787 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735615389396 | 2024-12-31 11:23:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20048 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735615389396 | 2024-12-31 11:23:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20049 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735615389397 | 2024-12-31 11:23:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20049 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735615389397 | 2024-12-31 11:23:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20049 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735615389502 | 2024-12-31 11:23:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20154 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735615389622 | 2024-12-31 11:23:09 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20274 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735615389623 | 2024-12-31 11:23:09 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20275 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735615389637 | 2024-12-31 11:23:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20289 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@5973a046, tech.powerjob.worker.actors.ProcessorTrackerActor@493a1b52, tech.powerjob.worker.actors.WorkerActor@254a4f0e])

info | 1 | 1735615389676 | 2024-12-31 11:23:09 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20329 | 0 | - | - | - | - | main o.r.Reflections Reflections took 25 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735615389684 | 2024-12-31 11:23:09 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20336 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1735615389685 | 2024-12-31 11:23:09 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20337 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@7fed72cd

info | 1 | 1735615389685 | 2024-12-31 11:23:09 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20337 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735615389686 | 2024-12-31 11:23:09 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20338 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735615389689 | 2024-12-31 11:23:09 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 20341 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 122 | 1735615390148 | 2024-12-31 11:23:10 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735615390565 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21217 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735615390566 | 2024-12-31 11:23:10 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21218 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735615390566 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21218 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735615390566 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21218 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735615390566 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21218 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735615390567 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21219 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735615390567 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21219 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735615390567 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21219 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735615390567 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21219 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735615390567 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21219 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735615390567 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21219 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735615390567 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21219 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735615390568 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21220 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735615390568 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21220 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735615390568 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21220 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735615390570 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21222 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735615390572 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21224 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735615390573 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21225 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735615390573 | 2024-12-31 11:23:10 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21226 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 888.0 ms

info | 1 | 1735615390677 | 2024-12-31 11:23:10 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21329 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735615390683 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21335 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735615390683 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21335 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735615390688 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21340 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735615390885 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21537 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735615390886 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21538 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/811df543733848d4b8c517e8bea914ec/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735615390892 | 2024-12-31 11:23:10 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21544 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/811df543733848d4b8c517e8bea914ec/] on JVM exit successfully

info | 1 | 1735615390906 | 2024-12-31 11:23:10 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21559 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735615390911 | 2024-12-31 11:23:10 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21563 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.784 s, congratulations!

info | 152 | 1735615390915 | 2024-12-31 11:23:10 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 152 | 1735615390916 | 2024-12-31 11:23:10 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735615390956 | 2024-12-31 11:23:10 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21608 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735615390977 | 2024-12-31 11:23:10 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21629 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735615390989 | 2024-12-31 11:23:10 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21641 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735615390989 | 2024-12-31 11:23:10 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21642 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735615391017 | 2024-12-31 11:23:11 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21670 | 0 | - | - | - | - | main c.t.g.Application Started Application in 22.088 seconds (JVM running for 22.509)

info | 1 | 1735615391033 | 2024-12-31 11:23:11 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21685 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735615391033 | 2024-12-31 11:23:11 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | af33fbd5bb884f92bf95eb1142043051 | - | - | - | - | 21685 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 222 | 1735615391038 | 2024-12-31 11:23:11 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | efdd4fb3fdb64420a635bc9ca3447d09 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 160 | 1735615395797 | 2024-12-31 11:23:15 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 4c762991d2204d9ea40510a7b2e1d2c1 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 160 | 1735615395798 | 2024-12-31 11:23:15 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 4c762991d2204d9ea40510a7b2e1d2c1 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 160 | 1735615395803 | 2024-12-31 11:23:15 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 4c762991d2204d9ea40510a7b2e1d2c1 | - | - | - | - | 7 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 5 ms

info | 160 | 1735615487069 | 2024-12-31 11:24:47 | v2/manageMultiLive/liveBizData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | liveBizData | 07dcb6fe39be45a29c1689dce5bc6707 | - | - | - | - | 91267 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.s.m.i.MultiLiveStatsServiceImpl 请求数据侧数据信息【/touch/live/MultiLiveDetail】的请求参数为:{"dt":["20211023","20241224"]},响应结果为:{"list":[{"dt":"2024-10-10","multi_live_active_unt_1d":1081,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"1382.12","multi_live_consume_unt_1d":38,"multi_live_active_host_unt_1d":7,"multi_live_meet_host_unt_1d":16,"arpu":"1.28","arppu":"36.37"},{"dt":"2024-10-09","multi_live_active_unt_1d":600,"multi_live_retation_ratio_2d":"10.17%","multi_live_consume_amt_1d":"1017.21","multi_live_consume_unt_1d":21,"multi_live_active_host_unt_1d":6,"multi_live_meet_host_unt_1d":15,"arpu":"1.70","arppu":"48.44"},{"dt":"2024-10-08","multi_live_active_unt_1d":1039,"multi_live_retation_ratio_2d":"5.00%","multi_live_consume_amt_1d":"1550.9","multi_live_consume_unt_1d":25,"multi_live_active_host_unt_1d":8,"multi_live_meet_host_unt_1d":16,"arpu":"1.49","arppu":"62.04"},{"dt":"2024-09-04","multi_live_active_unt_1d":891,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"817.7","multi_live_consume_unt_1d":11,"multi_live_active_host_unt_1d":13,"multi_live_meet_host_unt_1d":9,"arpu":"0.92","arppu":"74.34"},{"dt":"2024-09-03","multi_live_active_unt_1d":62,"multi_live_retation_ratio_2d":"12.90%","multi_live_consume_amt_1d":"323.8","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":5,"multi_live_meet_host_unt_1d":2,"arpu":"5.22","arppu":"107.93"},{"dt":"2024-08-30","multi_live_active_unt_1d":23,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"1","multi_live_consume_unt_1d":1,"multi_live_active_host_unt_1d":3,"multi_live_meet_host_unt_1d":2,"arpu":"0.04","arppu":"1.00"},{"dt":"2024-08-19","multi_live_active_unt_1d":7,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"11.09","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":5,"multi_live_meet_host_unt_1d":5,"arpu":"1.58","arppu":"3.70"},{"dt":"2024-08-14","multi_live_active_unt_1d":20,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"103.02","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":7,"multi_live_meet_host_unt_1d":12,"arpu":"5.15","arppu":"34.34"},{"dt":"2024-08-08","multi_live_active_unt_1d":100,"multi_live_retation_ratio_2d":"50.00%","multi_live_consume_amt_1d":"1000000000","multi_live_consume_unt_1d":60,"multi_live_active_host_unt_1d":20,"multi_live_meet_host_unt_1d":30,"arpu":"10000000.00","arppu":"16666666.67"},{"dt":"2024-08-07","multi_live_active_unt_1d":33,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"122576.3","multi_live_consume_unt_1d":7,"multi_live_active_host_unt_1d":40,"multi_live_meet_host_unt_1d":21,"arpu":"3714.43","arppu":"17510.90"}],"total":17,"pageSize":10,"page":1}

info | 152 | 1735615487035 | 2024-12-31 11:24:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 96170 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1735615487092 | 2024-12-31 11:24:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 96177 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1735615487093 | 2024-12-31 11:24:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 96178 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1735615487093 | 2024-12-31 11:24:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 96178 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1735615487093 | 2024-12-31 11:24:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 96178 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1735615487093 | 2024-12-31 11:24:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 96178 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1735615487094 | 2024-12-31 11:24:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 96179 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1735615487094 | 2024-12-31 11:24:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 96179 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 152 | 1735615487094 | 2024-12-31 11:24:47 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | aff84a0dd536468e99efd5841d6e08c0 | - | - | - | - | 96179 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | **********660 | 2024-12-31 11:27:53 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 87652 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 15 | **********653 | 2024-12-31 11:27:53 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 04c0807d1f14460583b14d510ed972f2 | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | **********671 | 2024-12-31 11:27:53 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735615674229 | 2024-12-31 11:27:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 563 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735615674231 | 2024-12-31 11:27:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 566 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735615674234 | 2024-12-31 11:27:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 568 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735615674236 | 2024-12-31 11:27:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 570 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735615674239 | 2024-12-31 11:27:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 573 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735615674282 | 2024-12-31 11:27:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 616 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735615674313 | 2024-12-31 11:27:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 647 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735615674316 | 2024-12-31 11:27:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 650 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735615674316 | 2024-12-31 11:27:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 650 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735615674366 | 2024-12-31 11:27:54 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 700 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735615676370 | 2024-12-31 11:27:56 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 2705 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735615676372 | 2024-12-31 11:27:56 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 2706 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735615676412 | 2024-12-31 11:27:56 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 2746 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.

info | 1 | 1735615676424 | 2024-12-31 11:27:56 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 2758 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735615676425 | 2024-12-31 11:27:56 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 2759 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735615676451 | 2024-12-31 11:27:56 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 2785 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1735615677301 | 2024-12-31 11:27:57 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 3639 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$e80232e5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615677334 | 2024-12-31 11:27:57 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 3668 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$6708785d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615677414 | 2024-12-31 11:27:57 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 3749 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$e5718326] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615677419 | 2024-12-31 11:27:57 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 3753 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615677478 | 2024-12-31 11:27:57 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 3812 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615677485 | 2024-12-31 11:27:57 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 3820 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735615678064 | 2024-12-31 11:27:58 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 4398 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735615678077 | 2024-12-31 11:27:58 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 4412 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735615678078 | 2024-12-31 11:27:58 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 4412 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735615678160 | 2024-12-31 11:27:58 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 4494 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735615685248 | 2024-12-31 11:28:05 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 11582 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735615685327 | 2024-12-31 11:28:05 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 11661 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735615685364 | 2024-12-31 11:28:05 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 11698 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735615685471 | 2024-12-31 11:28:05 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 11805 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735615685545 | 2024-12-31 11:28:05 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 11879 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735615685678 | 2024-12-31 11:28:05 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 12012 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735615685684 | 2024-12-31 11:28:05 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 12018 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735615689689 | 2024-12-31 11:28:09 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 16023 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735615690125 | 2024-12-31 11:28:10 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 16459 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735615690157 | 2024-12-31 11:28:10 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 16491 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735615690361 | 2024-12-31 11:28:10 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | d17fdef7c25244bbbc51cf35b05c5815 | - | - | - | - | 1 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735615690396 | 2024-12-31 11:28:10 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | d17fdef7c25244bbbc51cf35b05c5815 | - | - | - | - | 36 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735615690407 | 2024-12-31 11:28:10 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | d17fdef7c25244bbbc51cf35b05c5815 | - | - | - | - | 46 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735615690524 | 2024-12-31 11:28:10 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | d17fdef7c25244bbbc51cf35b05c5815 | - | - | - | - | 163 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 108 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735615690529 | 2024-12-31 11:28:10 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | d17fdef7c25244bbbc51cf35b05c5815 | - | - | - | - | 169 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735615690536 | 2024-12-31 11:28:10 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | d17fdef7c25244bbbc51cf35b05c5815 | - | - | - | - | 176 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735615690543 | 2024-12-31 11:28:10 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | d17fdef7c25244bbbc51cf35b05c5815 | - | - | - | - | 182 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735615690637 | 2024-12-31 11:28:10 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | d17fdef7c25244bbbc51cf35b05c5815 | - | - | - | - | 276 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 92 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735615693209 | 2024-12-31 11:28:13 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 19544 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735615693952 | 2024-12-31 11:28:13 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20287 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@216a0542 with [org.springframework.security.web.session.DisableEncodeUrlFilter@3e87d5da, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@52f2593e, org.springframework.security.web.context.SecurityContextPersistenceFilter@3c0790ed, org.springframework.security.web.header.HeaderWriterFilter@5c3bb402, org.springframework.security.web.authentication.logout.LogoutFilter@18f38d7a, org.springframework.web.filter.CorsFilter@4dfb114e, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@2074f4b8, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@1a3bebf9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@54e1c058, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@11908095, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6b8abde9, org.springframework.security.web.session.SessionManagementFilter@35074828, org.springframework.security.web.access.ExceptionTranslationFilter@36f4c790, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2908b236]

info | 1 | 1735615693971 | 2024-12-31 11:28:13 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20305 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735615694101 | 2024-12-31 11:28:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20435 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735615694102 | 2024-12-31 11:28:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20436 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735615694103 | 2024-12-31 11:28:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20437 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735615694106 | 2024-12-31 11:28:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20440 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735615694109 | 2024-12-31 11:28:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20443 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735615694110 | 2024-12-31 11:28:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20444 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735615694110 | 2024-12-31 11:28:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20444 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735615694298 | 2024-12-31 11:28:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20632 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735615694299 | 2024-12-31 11:28:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20633 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735615694299 | 2024-12-31 11:28:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20633 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735615694299 | 2024-12-31 11:28:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20633 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735615694354 | 2024-12-31 11:28:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20689 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735615694419 | 2024-12-31 11:28:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20753 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735615694419 | 2024-12-31 11:28:14 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20754 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735615694432 | 2024-12-31 11:28:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20766 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@375d73bb, tech.powerjob.worker.actors.ProcessorTrackerActor@692c4be9, tech.powerjob.worker.actors.WorkerActor@39dcff06])

info | 1 | 1735615694467 | 2024-12-31 11:28:14 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20802 | 0 | - | - | - | - | main o.r.Reflections Reflections took 23 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735615694476 | 2024-12-31 11:28:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20811 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1735615694477 | 2024-12-31 11:28:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20811 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@4509be44

info | 1 | 1735615694477 | 2024-12-31 11:28:14 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20811 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735615694478 | 2024-12-31 11:28:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20812 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735615694481 | 2024-12-31 11:28:14 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 20815 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 123 | 1735615694904 | 2024-12-31 11:28:14 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735615695263 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21598 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735615695264 | 2024-12-31 11:28:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21598 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735615695264 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21598 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735615695264 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21598 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735615695265 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21599 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735615695266 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21601 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735615695268 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21603 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735615695269 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21603 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735615695270 | 2024-12-31 11:28:15 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21605 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 792.7 ms

info | 1 | 1735615695334 | 2024-12-31 11:28:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21668 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735615695338 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21672 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735615695339 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21673 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735615695342 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21676 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735615695535 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21869 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735615695535 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21870 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/67676f726f73467584a7bd2c270c35d2/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735615695541 | 2024-12-31 11:28:15 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21876 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/67676f726f73467584a7bd2c270c35d2/] on JVM exit successfully

info | 1 | 1735615695556 | 2024-12-31 11:28:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21890 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735615695557 | 2024-12-31 11:28:15 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21891 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.456 s, congratulations!

info | 153 | 1735615695561 | 2024-12-31 11:28:15 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 75c840b2756246f8b9044ffd62946c14 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 153 | 1735615695561 | 2024-12-31 11:28:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 75c840b2756246f8b9044ffd62946c14 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735615695609 | 2024-12-31 11:28:15 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21943 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735615695636 | 2024-12-31 11:28:15 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21970 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735615695655 | 2024-12-31 11:28:15 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21989 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735615695655 | 2024-12-31 11:28:15 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 21990 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735615695687 | 2024-12-31 11:28:15 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 22022 | 0 | - | - | - | - | main c.t.g.Application Started Application in 22.474 seconds (JVM running for 22.888)

info | 1 | 1735615695708 | 2024-12-31 11:28:15 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 22042 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735615695708 | 2024-12-31 11:28:15 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | 06ebe4375b6d4060b25c97895d6d3c2e | - | - | - | - | 22043 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 223 | 1735615695714 | 2024-12-31 11:28:15 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 3dd8fdffe08649e08e8b0b1b67a2bf04 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 153 | 1735615705559 | 2024-12-31 11:28:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 75c840b2756246f8b9044ffd62946c14 | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 223 | 1735615706125 | 2024-12-31 11:28:26 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ChatRoomService | refreshChatCache | 3dd8fdffe08649e08e8b0b1b67a2bf04 | - | - | - | - | 10412 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 153 | 1735615715561 | 2024-12-31 11:28:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 75c840b2756246f8b9044ffd62946c14 | - | - | - | - | 20001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735615725561 | 2024-12-31 11:28:45 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7f8d76428be84491939c0689ceee7dbd | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735615735562 | 2024-12-31 11:28:55 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7f8d76428be84491939c0689ceee7dbd | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735615745563 | 2024-12-31 11:29:05 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7f8d76428be84491939c0689ceee7dbd | - | - | - | - | 20003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735615755561 | 2024-12-31 11:29:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | f066e13aa1cd4064b2b9a10fb71081bd | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 157 | 1735615765564 | 2024-12-31 11:29:25 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | f066e13aa1cd4064b2b9a10fb71081bd | - | - | - | - | 10004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735615775561 | 2024-12-31 11:29:35 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7f8d76428be84491939c0689ceee7dbd | - | - | - | - | 50001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735615778160 | 2024-12-31 11:29:38 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | a81c28d8503147f8812c1d2b5fb93cdc | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-2 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 161 | 1735615778160 | 2024-12-31 11:29:38 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | a81c28d8503147f8812c1d2b5fb93cdc | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 161 | 1735615778166 | 2024-12-31 11:29:38 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | a81c28d8503147f8812c1d2b5fb93cdc | - | - | - | - | 6 | 0 | - | - | - | - | http-nio-8087-exec-2 o.s.w.s.DispatcherServlet Completed initialization in 5 ms

info | 158 | 1735615803194 | 2024-12-31 11:30:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7f8d76428be84491939c0689ceee7dbd | - | - | - | - | 77638 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735615803219 | 2024-12-31 11:30:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 7f8d76428be84491939c0689ceee7dbd | - | - | - | - | 77658 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 161 | 1735615803226 | 2024-12-31 11:30:03 | v2/manageMultiLive/liveBizData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | liveBizData | f2e97f9632014b7ab1a6023dd72b7dfc | - | - | - | - | 25069 | 0 | - | - | - | - | http-nio-8087-exec-2 c.t.g.s.s.m.i.MultiLiveStatsServiceImpl 请求数据侧数据信息【/touch/live/MultiLiveDetail】的请求参数为:{"dt":["20211023","20241224"]},响应结果为:{"list":[{"dt":"2024-10-10","multi_live_active_unt_1d":1081,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"1382.12","multi_live_consume_unt_1d":38,"multi_live_active_host_unt_1d":7,"multi_live_meet_host_unt_1d":16,"arpu":"1.28","arppu":"36.37"},{"dt":"2024-10-09","multi_live_active_unt_1d":600,"multi_live_retation_ratio_2d":"10.17%","multi_live_consume_amt_1d":"1017.21","multi_live_consume_unt_1d":21,"multi_live_active_host_unt_1d":6,"multi_live_meet_host_unt_1d":15,"arpu":"1.70","arppu":"48.44"},{"dt":"2024-10-08","multi_live_active_unt_1d":1039,"multi_live_retation_ratio_2d":"5.00%","multi_live_consume_amt_1d":"1550.9","multi_live_consume_unt_1d":25,"multi_live_active_host_unt_1d":8,"multi_live_meet_host_unt_1d":16,"arpu":"1.49","arppu":"62.04"},{"dt":"2024-09-04","multi_live_active_unt_1d":891,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"817.7","multi_live_consume_unt_1d":11,"multi_live_active_host_unt_1d":13,"multi_live_meet_host_unt_1d":9,"arpu":"0.92","arppu":"74.34"},{"dt":"2024-09-03","multi_live_active_unt_1d":62,"multi_live_retation_ratio_2d":"12.90%","multi_live_consume_amt_1d":"323.8","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":5,"multi_live_meet_host_unt_1d":2,"arpu":"5.22","arppu":"107.93"},{"dt":"2024-08-30","multi_live_active_unt_1d":23,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"1","multi_live_consume_unt_1d":1,"multi_live_active_host_unt_1d":3,"multi_live_meet_host_unt_1d":2,"arpu":"0.04","arppu":"1.00"},{"dt":"2024-08-19","multi_live_active_unt_1d":7,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"11.09","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":5,"multi_live_meet_host_unt_1d":5,"arpu":"1.58","arppu":"3.70"},{"dt":"2024-08-14","multi_live_active_unt_1d":20,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"103.02","multi_live_consume_unt_1d":3,"multi_live_active_host_unt_1d":7,"multi_live_meet_host_unt_1d":12,"arpu":"5.15","arppu":"34.34"},{"dt":"2024-08-08","multi_live_active_unt_1d":100,"multi_live_retation_ratio_2d":"50.00%","multi_live_consume_amt_1d":"1000000000","multi_live_consume_unt_1d":60,"multi_live_active_host_unt_1d":20,"multi_live_meet_host_unt_1d":30,"arpu":"10000000.00","arppu":"16666666.67"},{"dt":"2024-08-07","multi_live_active_unt_1d":33,"multi_live_retation_ratio_2d":"0.00%","multi_live_consume_amt_1d":"122576.3","multi_live_consume_unt_1d":7,"multi_live_active_host_unt_1d":40,"multi_live_meet_host_unt_1d":21,"arpu":"3714.43","arppu":"17510.90"}],"total":17,"pageSize":10,"page":1}

info | 38 | 1735615803243 | 2024-12-31 11:30:03 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | 1982a39da28d47bbba19d93489625be0 | - | - | - | - | 0 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-12-31 11:28:10,121 to 2024-12-31 11:30:03,215
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 15 | 1735623581704 | 2024-12-31 13:39:41 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | b8e2f6c570b34c61b3d6606d8943693a | - | - | - | - | 4 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735623581711 | 2024-12-31 13:39:41 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 4 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 91244 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1735623581722 | 2024-12-31 13:39:41 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735623582275 | 2024-12-31 13:39:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 558 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735623582277 | 2024-12-31 13:39:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 561 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735623582280 | 2024-12-31 13:39:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 564 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735623582282 | 2024-12-31 13:39:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 565 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735623582284 | 2024-12-31 13:39:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 567 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735623582326 | 2024-12-31 13:39:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 609 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735623582360 | 2024-12-31 13:39:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 643 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735623582362 | 2024-12-31 13:39:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 645 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735623582362 | 2024-12-31 13:39:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 646 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735623582412 | 2024-12-31 13:39:42 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 696 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735623584417 | 2024-12-31 13:39:44 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 2700 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735623584418 | 2024-12-31 13:39:44 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 2702 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735623584460 | 2024-12-31 13:39:44 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 2743 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 35 ms. Found 0 JPA repository interfaces.

info | 1 | 1735623584474 | 2024-12-31 13:39:44 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 2757 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735623584475 | 2024-12-31 13:39:44 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 2758 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735623584502 | 2024-12-31 13:39:44 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 2785 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1735623585288 | 2024-12-31 13:39:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 3575 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$421c53e8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735623585311 | 2024-12-31 13:39:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 3594 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$c1229960] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735623585383 | 2024-12-31 13:39:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 3666 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$3f8ba429] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735623585389 | 2024-12-31 13:39:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 3673 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735623585499 | 2024-12-31 13:39:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 3782 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735623585510 | 2024-12-31 13:39:45 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 3793 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735623586191 | 2024-12-31 13:39:46 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 4474 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735623586203 | 2024-12-31 13:39:46 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 4486 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735623586204 | 2024-12-31 13:39:46 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 4487 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735623586308 | 2024-12-31 13:39:46 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 4591 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735623593552 | 2024-12-31 13:39:53 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 11836 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735623593633 | 2024-12-31 13:39:53 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 11916 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735623593671 | 2024-12-31 13:39:53 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 11954 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735623593780 | 2024-12-31 13:39:53 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 12064 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735623593856 | 2024-12-31 13:39:53 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 12139 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735623593985 | 2024-12-31 13:39:53 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 12268 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735623593992 | 2024-12-31 13:39:53 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 12275 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735623597442 | 2024-12-31 13:39:57 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 15725 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735623597861 | 2024-12-31 13:39:57 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 16144 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735623597884 | 2024-12-31 13:39:57 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 16167 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735623598066 | 2024-12-31 13:39:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b024daae29cc400aa0a5277aa626c41d | - | - | - | - | 1 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735623598089 | 2024-12-31 13:39:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b024daae29cc400aa0a5277aa626c41d | - | - | - | - | 23 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735623598099 | 2024-12-31 13:39:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b024daae29cc400aa0a5277aa626c41d | - | - | - | - | 33 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735623598215 | 2024-12-31 13:39:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b024daae29cc400aa0a5277aa626c41d | - | - | - | - | 149 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 114 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735623598221 | 2024-12-31 13:39:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b024daae29cc400aa0a5277aa626c41d | - | - | - | - | 155 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735623598228 | 2024-12-31 13:39:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b024daae29cc400aa0a5277aa626c41d | - | - | - | - | 162 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735623598235 | 2024-12-31 13:39:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b024daae29cc400aa0a5277aa626c41d | - | - | - | - | 169 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735623598333 | 2024-12-31 13:39:58 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | b024daae29cc400aa0a5277aa626c41d | - | - | - | - | 267 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 96 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735623600839 | 2024-12-31 13:40:00 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 19122 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735623601671 | 2024-12-31 13:40:01 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 19955 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@7bb070b7 with [org.springframework.security.web.session.DisableEncodeUrlFilter@d5196fc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@dfb23a6, org.springframework.security.web.context.SecurityContextPersistenceFilter@28791945, org.springframework.security.web.header.HeaderWriterFilter@17e1125, org.springframework.security.web.authentication.logout.LogoutFilter@5a43d7e4, org.springframework.web.filter.CorsFilter@5a1bd78c, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@5b02d7a7, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@2626020c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@607f76a1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6dcfa481, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5e6bfbb7, org.springframework.security.web.session.SessionManagementFilter@44f9e70f, org.springframework.security.web.access.ExceptionTranslationFilter@6e658b67, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4bd97e7e]

info | 1 | 1735623601692 | 2024-12-31 13:40:01 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 19975 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735623601825 | 2024-12-31 13:40:01 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20108 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735623601826 | 2024-12-31 13:40:01 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20109 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735623601827 | 2024-12-31 13:40:01 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20110 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735623601829 | 2024-12-31 13:40:01 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20112 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735623601832 | 2024-12-31 13:40:01 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20115 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735623601832 | 2024-12-31 13:40:01 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20115 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735623601832 | 2024-12-31 13:40:01 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20115 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735623602125 | 2024-12-31 13:40:02 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20409 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735623602126 | 2024-12-31 13:40:02 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20409 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735623602126 | 2024-12-31 13:40:02 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20409 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735623602126 | 2024-12-31 13:40:02 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20409 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735623602236 | 2024-12-31 13:40:02 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20520 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735623602369 | 2024-12-31 13:40:02 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20652 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735623602370 | 2024-12-31 13:40:02 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20654 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735623602390 | 2024-12-31 13:40:02 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20673 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@4ddf9dd2, tech.powerjob.worker.actors.ProcessorTrackerActor@4b35d2db, tech.powerjob.worker.actors.WorkerActor@26199930])

info | 1 | 1735623602444 | 2024-12-31 13:40:02 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20727 | 0 | - | - | - | - | main o.r.Reflections Reflections took 34 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735623602455 | 2024-12-31 13:40:02 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20739 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.akka.AkkaCSInitializer, class tech.powerjob.remote.http.HttpVertxCSInitializer]

info | 1 | 1735623602458 | 2024-12-31 13:40:02 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20741 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@4d443a45

info | 1 | 1735623602458 | 2024-12-31 13:40:02 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20741 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735623602459 | 2024-12-31 13:40:02 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20743 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735623602465 | 2024-12-31 13:40:02 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 20748 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 123 | 1735623602985 | 2024-12-31 13:40:02 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735623603392 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21676 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735623603393 | 2024-12-31 13:40:03 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21676 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735623603394 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21677 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735623603394 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21677 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735623603394 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21677 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735623603394 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21677 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735623603394 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21677 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735623603394 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21677 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735623603394 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21677 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735623603394 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21678 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735623603395 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21678 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735623603395 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21678 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735623603395 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21678 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735623603395 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21678 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735623603395 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21678 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735623603397 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21680 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735623603399 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21682 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735623603400 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21683 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735623603400 | 2024-12-31 13:40:03 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21683 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 941.7 ms

info | 1 | 1735623603501 | 2024-12-31 13:40:03 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21784 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735623603506 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21789 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735623603506 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21789 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735623603510 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21794 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735623603708 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21991 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735623603708 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21991 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/b3c445f41d044a14a2f437a069dabe94/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735623603715 | 2024-12-31 13:40:03 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 21998 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/b3c445f41d044a14a2f437a069dabe94/] on JVM exit successfully

info | 1 | 1735623603729 | 2024-12-31 13:40:03 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 22012 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735623603729 | 2024-12-31 13:40:03 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 22013 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.905 s, congratulations!

info | 158 | 1735623603733 | 2024-12-31 13:40:03 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 42f09b800312464496c05429c1ef682f | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 158 | 1735623603734 | 2024-12-31 13:40:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735623603795 | 2024-12-31 13:40:03 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | TomcatWebServer | start | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 22078 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735623603819 | 2024-12-31 13:40:03 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 22102 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735623603829 | 2024-12-31 13:40:03 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 22112 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735623603830 | 2024-12-31 13:40:03 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 22113 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735623603852 | 2024-12-31 13:40:03 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | Application | main | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 22135 | 0 | - | - | - | - | main c.t.g.Application Started Application in 22.565 seconds (JVM running for 23.183)

info | 1 | 1735623603872 | 2024-12-31 13:40:03 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 22156 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735623603873 | 2024-12-31 13:40:03 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | a9f4dc0dee334d8c8c91805bb26a1495 | - | - | - | - | 22156 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 228 | 1735623603877 | 2024-12-31 13:40:03 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | c074d425919c48608bf72317aeca8bb7 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 158 | 1735623613735 | 2024-12-31 13:40:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 10003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 228 | 1735623614228 | 2024-12-31 13:40:14 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ChatRoomService | refreshChatCache | c074d425919c48608bf72317aeca8bb7 | - | - | - | - | 10351 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 158 | 1735623623735 | 2024-12-31 13:40:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 20003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 165 | 1735623631445 | 2024-12-31 13:40:31 | v2/StandardWrapper/initServlet | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | StandardWrapper | initServlet | 274f0f98ff1443d58783a7f653cc8c02 | - | - | - | - | 0 | 0 | - | - | - | - | http-nio-8087-exec-1 o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring DispatcherServlet 'dispatcherServlet'

info | 165 | 1735623631445 | 2024-12-31 13:40:31 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | AuthenticatorBase | invoke | 274f0f98ff1443d58783a7f653cc8c02 | - | - | - | - | 1 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Initializing Servlet 'dispatcherServlet'

info | 165 | 1735623631450 | 2024-12-31 13:40:31 | v2/AuthenticatorBase/invoke | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | AuthenticatorBase | invoke | 274f0f98ff1443d58783a7f653cc8c02 | - | - | - | - | 6 | 0 | - | - | - | - | http-nio-8087-exec-1 o.s.w.s.DispatcherServlet Completed initialization in 4 ms

info | 165 | 1735623631592 | 2024-12-31 13:40:31 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | hostData | 9e0b9d28d3c84e7ba0c7d9e48f89d65f | - | - | - | - | 136 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.i.MultiLiveSoaServiceImpl soa请求AdminMultiLiveStat/getHostData,traceId=9e0b9d28d3c84e7ba0c7d9e48f89d65f, request data=[0,1,1635000546,1735010546,null,null,[],null,0]

info | 165 | 1735623631600 | 2024-12-31 13:40:31 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.3 | *************** | - | 2 | manageMultiLive | hostData | 9e0b9d28d3c84e7ba0c7d9e48f89d65f | - | - | - | - | 144 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.c.c.c.SoaClient register http client : [http://live-api.test3.hbmonitor.com/v1/Soa/jService] with param : [ConnectParam{maxConnection=10, maxPerRoute=10, requestTimeout=2000, connectTimeout=2000, socketTimeout=2000}].

info | 165 | 1735623631936 | 2024-12-31 13:40:31 | v2/manageMultiLive/hostData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.5 | *************** | - | 2 | manageMultiLive | hostData | 9e0b9d28d3c84e7ba0c7d9e48f89d65f | - | - | - | - | 480 | 0 | - | - | - | - | http-nio-8087-exec-1 c.t.g.s.i.MultiLiveSoaServiceImpl soaResponse={"data":"{\"list\":[{\"time\":\"2024-10-21\",\"host_name\":\"xgh150\",\"host_uuid\":\"chchrm2jiv9o\",\"consortia_id\":\"110648\",\"total_live_duration\":\"6.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1,\"shell_amount\":0,\"total_score\":0,\"room_total_score\":0,\"meet_total_score\":0,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0%\"},{\"time\":\"2024-10-21\",\"host_name\":\"大哥\",\"host_uuid\":\"bfqyp2l343rf\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"19.4\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":0,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":0,\"has_priv\":1,\"shell_amount\":0,\"total_score\":0,\"room_total_score\":0,\"meet_total_score\":0,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0%\"},{\"time\":\"2024-10-21\",\"host_name\":\"wkm\",\"host_uuid\":\"iop44ezeziy\",\"consortia_id\":\"1000309\",\"total_live_duration\":\"30.6\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":994680,\"room_amount\":994680,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":994680,\"consortia_amount\":994680,\"other_consortia_amount\":0,\"has_priv\":1,\"shell_amount\":0,\"total_score\":994680,\"room_total_score\":0,\"meet_total_score\":0,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-21\",\"host_name\":\"xgh4\",\"host_uuid\":\"ccjbobv0oeal\",\"consortia_id\":\"110016\",\"total_live_duration\":\"11.1\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"163.8\",\"valid_up_meet_days\":1,\"total_amount\":20420,\"room_amount\":20000,\"room_split_amount\":0,\"meet_amount\":420,\"bind_room_amount\":20000,\"consortia_amount\":20000,\"other_consortia_amount\":420,\"has_priv\":1,\"shell_amount\":0,\"total_score\":20420,\"room_total_score\":0,\"meet_total_score\":420,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-21\",\"host_name\":\"xgh3\",\"host_uuid\":\"cbhhdszbb1xm\",\"consortia_id\":\"1000535\",\"total_live_duration\":\"574.7\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"8.3\",\"valid_up_meet_days\":1,\"total_amount\":8115100,\"room_amount\":7995100,\"room_split_amount\":0,\"meet_amount\":120000,\"bind_room_amount\":7995100,\"consortia_amount\":7995100,\"other_consortia_amount\":120000,\"has_priv\":1,\"shell_amount\":0,\"total_score\":8115100,\"room_total_score\":0,\"meet_total_score\":120000,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-21\",\"host_name\":\"szx080901\",\"host_uuid\":\"bgjbffbifbjdjgih\",\"consortia_id\":\"40\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"0.0\",\"valid_up_meet_days\":0,\"total_amount\":503,\"room_amount\":503,\"room_split_amount\":0,\"meet_amount\":0,\"bind_room_amount\":0,\"consortia_amount\":503,\"other_consortia_amount\":0,\"has_priv\":0,\"shell_amount\":0,\"total_score\":503,\"room_total_score\":0,\"meet_total_score\":0,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-20\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":10684,\"room_amount\":186,\"room_split_amount\":12,\"meet_amount\":10486,\"bind_room_amount\":10498,\"consortia_amount\":10604,\"other_consortia_amount\":80,\"has_priv\":1,\"shell_amount\":0,\"total_score\":10684,\"room_total_score\":12,\"meet_total_score\":10486,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-20\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5432,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5432,\"bind_room_amount\":0,\"consortia_amount\":0,\"other_consortia_amount\":5432,\"has_priv\":0,\"shell_amount\":0,\"total_score\":5432,\"room_total_score\":0,\"meet_total_score\":5432,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"110002\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5646,\"room_amount\":40,\"room_split_amount\":6,\"meet_amount\":5600,\"bind_room_amount\":0,\"consortia_amount\":46,\"other_consortia_amount\":5600,\"has_priv\":1,\"shell_amount\":0,\"total_score\":5646,\"room_total_score\":6,\"meet_total_score\":5600,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-20\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5520,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5520,\"bind_room_amount\":206,\"consortia_amount\":5458,\"other_consortia_amount\":62,\"has_priv\":0,\"shell_amount\":0,\"total_score\":5520,\"room_total_score\":0,\"meet_total_score\":5520,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-20\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5338,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5338,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":80,\"has_priv\":0,\"shell_amount\":0,\"total_score\":5338,\"room_total_score\":0,\"meet_total_score\":5338,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女3\",\"host_uuid\":\"bggjbjbhjegbgfbe\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1200.0\",\"valid_up_meet_days\":1,\"total_amount\":5418,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":5418,\"bind_room_amount\":5298,\"consortia_amount\":5350,\"other_consortia_amount\":68,\"has_priv\":1,\"shell_amount\":0,\"total_score\":5418,\"room_total_score\":0,\"meet_total_score\":5418,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-20\",\"host_name\":\"qzz测试女2\",\"host_uuid\":\"bggjbjbhhgabgfbd\",\"consortia_id\":\"40\",\"total_live_duration\":\"240.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5392,\"room_amount\":100,\"room_split_amount\":6,\"meet_amount\":5286,\"bind_room_amount\":5206,\"consortia_amount\":106,\"other_consortia_amount\":5286,\"has_priv\":1,\"shell_amount\":0,\"total_score\":5392,\"room_total_score\":6,\"meet_total_score\":5286,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-20\",\"host_name\":\"测试的的太热过饿也行\",\"host_uuid\":\"bggjbjbhfhbhafbc\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"720.0\",\"valid_up_meet_days\":1,\"total_amount\":5452,\"room_amount\":5200,\"room_split_amount\":6,\"meet_amount\":246,\"bind_room_amount\":5206,\"consortia_amount\":5258,\"other_consortia_amount\":194,\"has_priv\":1,\"shell_amount\":0,\"total_score\":5452,\"room_total_score\":6,\"meet_total_score\":246,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-19\",\"host_name\":\"自动创建4430\",\"host_uuid\":\"bhcjfiaiecfbhfib\",\"consortia_id\":\"110016\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"240.0\",\"valid_up_meet_days\":1,\"total_amount\":1512,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1200,\"bind_room_amount\":600,\"consortia_amount\":0,\"other_consortia_amount\":1512,\"has_priv\":1,\"shell_amount\":0,\"total_score\":1512,\"room_total_score\":312,\"meet_total_score\":1200,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-19\",\"host_name\":\"嘿嘿产品\",\"host_uuid\":\"bggjbjbjbifbgfcb\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"960.0\",\"valid_up_meet_days\":1,\"total_amount\":7020,\"room_amount\":640,\"room_split_amount\":5200,\"meet_amount\":1180,\"bind_room_amount\":12544,\"consortia_amount\":12532,\"other_consortia_amount\":-5512,\"has_priv\":1,\"shell_amount\":0,\"total_score\":7020,\"room_total_score\":5200,\"meet_total_score\":1180,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-19\",\"host_name\":\"邱珍珍测试测试测试\",\"host_uuid\":\"bggjbjbijicjdfca\",\"consortia_id\":\"0\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"1440.0\",\"valid_up_meet_days\":1,\"total_amount\":1306,\"room_amount\":0,\"room_split_amount\":0,\"meet_amount\":1306,\"bind_room_amount\":540,\"consortia_amount\":0,\"other_consortia_amount\":1306,\"has_priv\":0,\"shell_amount\":0,\"total_score\":1306,\"room_total_score\":0,\"meet_total_score\":1306,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-19\",\"host_name\":\"qzz测试女7\",\"host_uuid\":\"bggjbjbigahijfbi\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"480.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":122,\"room_amount\":46,\"room_split_amount\":18,\"meet_amount\":58,\"bind_room_amount\":18,\"consortia_amount\":82,\"other_consortia_amount\":40,\"has_priv\":1,\"shell_amount\":0,\"total_score\":122,\"room_total_score\":18,\"meet_total_score\":58,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-19\",\"host_name\":\"lxj2小小\",\"host_uuid\":\"bggjbjbiefaajfbh\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"0.0\",\"valid_live_days\":0,\"valid_host_num\":0,\"total_up_meet_duration\":\"2160.0\",\"valid_up_meet_days\":1,\"total_amount\":1424,\"room_amount\":0,\"room_split_amount\":18,\"meet_amount\":1406,\"bind_room_amount\":896,\"consortia_amount\":1204,\"other_consortia_amount\":220,\"has_priv\":0,\"shell_amount\":0,\"total_score\":1424,\"room_total_score\":18,\"meet_total_score\":1406,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"},{\"time\":\"2024-10-19\",\"host_name\":\"沙雕的产品\",\"host_uuid\":\"bggjbjbibahhhfbf\",\"consortia_id\":\"2342711\",\"total_live_duration\":\"720.0\",\"valid_live_days\":1,\"valid_host_num\":0,\"total_up_meet_duration\":\"1680.0\",\"valid_up_meet_days\":1,\"total_amount\":1888,\"room_amount\":0,\"room_split_amount\":312,\"meet_amount\":1576,\"bind_room_amount\":1906,\"consortia_amount\":1714,\"other_consortia_amount\":174,\"has_priv\":1,\"shell_amount\":0,\"total_score\":1888,\"room_total_score\":312,\"meet_total_score\":1576,\"room_shell_amount\":0,\"meet_shell_amount\":0,\"shell_ratio\":\"0.00%\"}],\"total\":0}","dataBlank":false,"dataNotBlank":true}

info | 158 | 1735623633735 | 2024-12-31 13:40:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 30002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623643734 | 2024-12-31 13:40:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623653734 | 2024-12-31 13:40:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 10000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623663731 | 2024-12-31 13:41:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 19996 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623673735 | 2024-12-31 13:41:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 30000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623683734 | 2024-12-31 13:41:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 40000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623693735 | 2024-12-31 13:41:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 50000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623703735 | 2024-12-31 13:41:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 60001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623713731 | 2024-12-31 13:41:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 69997 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623723735 | 2024-12-31 13:42:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 80000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623733735 | 2024-12-31 13:42:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 90002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623743733 | 2024-12-31 13:42:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 99999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623753734 | 2024-12-31 13:42:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 110000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623763736 | 2024-12-31 13:42:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 120002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623773736 | 2024-12-31 13:42:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 130001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623783733 | 2024-12-31 13:43:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 139999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623793733 | 2024-12-31 13:43:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 149999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623803733 | 2024-12-31 13:43:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 159999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623813731 | 2024-12-31 13:43:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 169996 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623823733 | 2024-12-31 13:43:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 179998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735623833736 | 2024-12-31 13:43:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 2 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735623843731 | 2024-12-31 13:44:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 9993 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735623853736 | 2024-12-31 13:44:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 19999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735623863732 | 2024-12-31 13:44:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 29994 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735623873733 | 2024-12-31 13:44:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 270001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735623883732 | 2024-12-31 13:44:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 279999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735623893731 | 2024-12-31 13:44:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 289999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1735623900023 | 2024-12-31 13:45:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | 5ef65241cc8f47b4aa12bafa75c1d90e | - | - | - | - | 0 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-12-31 13:39:57,860 to 2024-12-31 13:45:00,015
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 158 | 1735623903734 | 2024-12-31 13:45:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 300002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735623913733 | 2024-12-31 13:45:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 310000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735623923735 | 2024-12-31 13:45:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 320003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735623933734 | 2024-12-31 13:45:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 330003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735623943735 | 2024-12-31 13:45:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 340002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623953735 | 2024-12-31 13:45:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 310000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735623963737 | 2024-12-31 13:46:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 360004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735623973733 | 2024-12-31 13:46:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 370001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623983734 | 2024-12-31 13:46:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 340000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735623993733 | 2024-12-31 13:46:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 349998 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735624003731 | 2024-12-31 13:46:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 359997 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735624013736 | 2024-12-31 13:46:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 370002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735624023736 | 2024-12-31 13:47:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 380002 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735624033736 | 2024-12-31 13:47:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 390001 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624043733 | 2024-12-31 13:47:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 210003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624053735 | 2024-12-31 13:47:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 219996 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624063737 | 2024-12-31 13:47:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 229998 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624073735 | 2024-12-31 13:47:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 239997 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735624083732 | 2024-12-31 13:48:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 439997 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624093734 | 2024-12-31 13:48:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 259996 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624103737 | 2024-12-31 13:48:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 270000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735624113735 | 2024-12-31 13:48:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 470000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624123734 | 2024-12-31 13:48:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 520001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624133737 | 2024-12-31 13:48:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 530005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624143736 | 2024-12-31 13:49:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 540003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624153733 | 2024-12-31 13:49:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 550001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624163736 | 2024-12-31 13:49:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 560003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624173736 | 2024-12-31 13:49:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 570004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624183736 | 2024-12-31 13:49:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 580004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624193734 | 2024-12-31 13:49:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 590001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624203732 | 2024-12-31 13:50:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 600000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624213732 | 2024-12-31 13:50:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 610000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624223738 | 2024-12-31 13:50:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 620005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624233738 | 2024-12-31 13:50:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 630006 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624243738 | 2024-12-31 13:50:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 640005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624253736 | 2024-12-31 13:50:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 650004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624263737 | 2024-12-31 13:51:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 429998 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624273739 | 2024-12-31 13:51:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 440005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624283734 | 2024-12-31 13:51:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 450000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624293738 | 2024-12-31 13:51:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 460000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624303736 | 2024-12-31 13:51:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 700004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624313737 | 2024-12-31 13:51:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 710006 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624323735 | 2024-12-31 13:52:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 720003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624333736 | 2024-12-31 13:52:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 730003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624343734 | 2024-12-31 13:52:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 740001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624353734 | 2024-12-31 13:52:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 750001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624363734 | 2024-12-31 13:52:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 760002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624373737 | 2024-12-31 13:52:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 770005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624383733 | 2024-12-31 13:53:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 549994 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624393738 | 2024-12-31 13:53:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 790007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735624403733 | 2024-12-31 13:53:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 759999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735624413735 | 2024-12-31 13:53:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 770000 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 162 | 1735624423734 | 2024-12-31 13:53:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 779999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624433738 | 2024-12-31 13:53:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.77 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 830007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624443736 | 2024-12-31 13:54:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.79 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 840007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624453734 | 2024-12-31 13:54:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.81 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 850002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624463736 | 2024-12-31 13:54:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.83 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 860005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624473734 | 2024-12-31 13:54:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.85 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 870001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624483735 | 2024-12-31 13:54:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 649996 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624493739 | 2024-12-31 13:54:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 660000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624503736 | 2024-12-31 13:55:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 669997 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624513735 | 2024-12-31 13:55:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 679997 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624523739 | 2024-12-31 13:55:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.87 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 920007 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 169 | 1735624529124 | 2024-12-31 13:55:29 | v2/manageMultiLive/roomData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | roomData | 286a25257215497d96a349d1b1bc4035 | - | - | - | - | 2836 | 0 | - | - | - | - | http-nio-8087-exec-5 c.t.g.s.s.m.i.MultiLiveStatsServiceImpl 请求数据侧接口【/touch/live/MultiLiveRoomDetail】的请求参数为:{"dt":["20211023","20241224"]},响应信息为:{"list":[{"dt":"2024-08-07","multi_live_name":"-99","multi_live_uuid":"bghiaiggjecdfedf","consortia_id":1000688,"consortia_name":"青色天马","is_active_multi_live":1,"multi_live_duration_h":4.3,"multi_live_receive_amt_1d":"10000000","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":25,"multi_live_active_cnt_1d":6,"multi_live_meeting_cnt_1d":0,"multi_live_consume_cnt_1d":1,"multi_live_consume_ratio":"16.67%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":0},{"dt":"2024-08-08","multi_live_name":"-99","multi_live_uuid":"bcqzm5c0dum3","consortia_id":1001007,"consortia_name":"入驻流程走一下","is_active_multi_live":0,"multi_live_duration_h":0.79,"multi_live_receive_amt_1d":"703850","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":0,"multi_live_active_cnt_1d":0,"multi_live_meeting_cnt_1d":0,"multi_live_consume_cnt_1d":1,"multi_live_consume_ratio":"0.0%","multi_live_retention_ratio_2d":"0.0%","multi_live_new_active_cnt_1d":0},{"dt":"2024-10-01","multi_live_name":"木子","multi_live_uuid":"bhcfhbcjfhbejddh","consortia_id":1000134,"consortia_name":"美美公会","is_active_multi_live":1,"multi_live_duration_h":179.9,"multi_live_receive_amt_1d":"561481","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":1176,"multi_live_active_cnt_1d":495,"multi_live_meeting_cnt_1d":6,"multi_live_consume_cnt_1d":10,"multi_live_consume_ratio":"2.02%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":360},{"dt":"2024-10-07","multi_live_name":"木子","multi_live_uuid":"bhcfhbcjfhbejddh","consortia_id":1000134,"consortia_name":"美美公会","is_active_multi_live":1,"multi_live_duration_h":184.5,"multi_live_receive_amt_1d":"468496","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":1052,"multi_live_active_cnt_1d":348,"multi_live_meeting_cnt_1d":4,"multi_live_consume_cnt_1d":10,"multi_live_consume_ratio":"2.87%","multi_live_retention_ratio_2d":"6.90%","multi_live_new_active_cnt_1d":255},{"dt":"2024-08-08","multi_live_name":"-99","multi_live_uuid":"chd1ez9d1sqr","consortia_id":0,"consortia_name":"-99","is_active_multi_live":1,"multi_live_duration_h":6.18,"multi_live_receive_amt_1d":"306570","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":0,"multi_live_active_cnt_1d":0,"multi_live_meeting_cnt_1d":2,"multi_live_consume_cnt_1d":2,"multi_live_consume_ratio":"0.0%","multi_live_retention_ratio_2d":"0.0%","multi_live_new_active_cnt_1d":0},{"dt":"2024-08-06","multi_live_name":"-99","multi_live_uuid":"bhccicdgggiabbij","consortia_id":1000688,"consortia_name":"青色天马","is_active_multi_live":0,"multi_live_duration_h":1.74,"multi_live_receive_amt_1d":"292398","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":22,"multi_live_active_cnt_1d":5,"multi_live_meeting_cnt_1d":1,"multi_live_consume_cnt_1d":1,"multi_live_consume_ratio":"20.00%","multi_live_retention_ratio_2d":"80.00%","multi_live_new_active_cnt_1d":2},{"dt":"2024-08-07","multi_live_name":"-99","multi_live_uuid":"bghiaighbcffjedg","consortia_id":1000688,"consortia_name":"青色天马","is_active_multi_live":1,"multi_live_duration_h":5.31,"multi_live_receive_amt_1d":"285800","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":24,"multi_live_active_cnt_1d":15,"multi_live_meeting_cnt_1d":0,"multi_live_consume_cnt_1d":2,"multi_live_consume_ratio":"13.33%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":2},{"dt":"2024-08-07","multi_live_name":"-99","multi_live_uuid":"bhccicdgggiabbij","consortia_id":1000688,"consortia_name":"青色天马","is_active_multi_live":1,"multi_live_duration_h":6.0,"multi_live_receive_amt_1d":"239748","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":28,"multi_live_active_cnt_1d":17,"multi_live_meeting_cnt_1d":2,"multi_live_consume_cnt_1d":2,"multi_live_consume_ratio":"11.76%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":0},{"dt":"2024-08-07","multi_live_name":"-99","multi_live_uuid":"bhccfgjeccdiaigj","consortia_id":1000688,"consortia_name":"青色天马","is_active_multi_live":0,"multi_live_duration_h":1.26,"multi_live_receive_amt_1d":"204540","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":18,"multi_live_active_cnt_1d":9,"multi_live_meeting_cnt_1d":4,"multi_live_consume_cnt_1d":1,"multi_live_consume_ratio":"11.11%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":4},{"dt":"2024-10-21","multi_live_name":"木子","multi_live_uuid":"bhcfhbcjfhbejddh","consortia_id":1000134,"consortia_name":"美美公会","is_active_multi_live":1,"multi_live_duration_h":178.3,"multi_live_receive_amt_1d":"105520","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":749,"multi_live_active_cnt_1d":185,"multi_live_meeting_cnt_1d":4,"multi_live_consume_cnt_1d":3,"multi_live_consume_ratio":"1.62%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":124}],"total":1000,"pageSize":10,"page":1}

info | 158 | 1735624533734 | 2024-12-31 13:55:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.89 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 930001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624543738 | 2024-12-31 13:55:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 709999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624553739 | 2024-12-31 13:55:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 720001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624563735 | 2024-12-31 13:56:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.91 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 960003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 158 | 1735624575753 | 2024-12-31 13:56:15 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.93 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 42f09b800312464496c05429c1ef682f | - | - | - | - | 972020 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 170 | 1735624575764 | 2024-12-31 13:56:15 | v2/manageMultiLive/roomData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | *************** | - | 2 | manageMultiLive | roomData | 62a82807f2194205b987fd15d2d4ebf4 | - | - | - | - | 12218 | 0 | - | - | - | - | http-nio-8087-exec-6 c.t.g.s.s.m.i.MultiLiveStatsServiceImpl 请求数据侧接口【/touch/live/MultiLiveRoomDetail】的请求参数为:{"dt":["20211023","20241224"]},响应信息为:{"list":[{"dt":"2024-08-07","multi_live_name":"-99","multi_live_uuid":"bghiaiggjecdfedf","consortia_id":1000688,"consortia_name":"青色天马","is_active_multi_live":1,"multi_live_duration_h":4.3,"multi_live_receive_amt_1d":"10000000","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":25,"multi_live_active_cnt_1d":6,"multi_live_meeting_cnt_1d":0,"multi_live_consume_cnt_1d":1,"multi_live_consume_ratio":"16.67%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":0},{"dt":"2024-08-08","multi_live_name":"-99","multi_live_uuid":"bcqzm5c0dum3","consortia_id":1001007,"consortia_name":"入驻流程走一下","is_active_multi_live":0,"multi_live_duration_h":0.79,"multi_live_receive_amt_1d":"703850","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":0,"multi_live_active_cnt_1d":0,"multi_live_meeting_cnt_1d":0,"multi_live_consume_cnt_1d":1,"multi_live_consume_ratio":"0.0%","multi_live_retention_ratio_2d":"0.0%","multi_live_new_active_cnt_1d":0},{"dt":"2024-10-01","multi_live_name":"木子","multi_live_uuid":"bhcfhbcjfhbejddh","consortia_id":1000134,"consortia_name":"美美公会","is_active_multi_live":1,"multi_live_duration_h":179.9,"multi_live_receive_amt_1d":"561481","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":1176,"multi_live_active_cnt_1d":495,"multi_live_meeting_cnt_1d":6,"multi_live_consume_cnt_1d":10,"multi_live_consume_ratio":"2.02%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":360},{"dt":"2024-10-07","multi_live_name":"木子","multi_live_uuid":"bhcfhbcjfhbejddh","consortia_id":1000134,"consortia_name":"美美公会","is_active_multi_live":1,"multi_live_duration_h":184.5,"multi_live_receive_amt_1d":"468496","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":1052,"multi_live_active_cnt_1d":348,"multi_live_meeting_cnt_1d":4,"multi_live_consume_cnt_1d":10,"multi_live_consume_ratio":"2.87%","multi_live_retention_ratio_2d":"6.90%","multi_live_new_active_cnt_1d":255},{"dt":"2024-08-08","multi_live_name":"-99","multi_live_uuid":"chd1ez9d1sqr","consortia_id":0,"consortia_name":"-99","is_active_multi_live":1,"multi_live_duration_h":6.18,"multi_live_receive_amt_1d":"306570","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":0,"multi_live_active_cnt_1d":0,"multi_live_meeting_cnt_1d":2,"multi_live_consume_cnt_1d":2,"multi_live_consume_ratio":"0.0%","multi_live_retention_ratio_2d":"0.0%","multi_live_new_active_cnt_1d":0},{"dt":"2024-08-06","multi_live_name":"-99","multi_live_uuid":"bhccicdgggiabbij","consortia_id":1000688,"consortia_name":"青色天马","is_active_multi_live":0,"multi_live_duration_h":1.74,"multi_live_receive_amt_1d":"292398","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":22,"multi_live_active_cnt_1d":5,"multi_live_meeting_cnt_1d":1,"multi_live_consume_cnt_1d":1,"multi_live_consume_ratio":"20.00%","multi_live_retention_ratio_2d":"80.00%","multi_live_new_active_cnt_1d":2},{"dt":"2024-08-07","multi_live_name":"-99","multi_live_uuid":"bghiaighbcffjedg","consortia_id":1000688,"consortia_name":"青色天马","is_active_multi_live":1,"multi_live_duration_h":5.31,"multi_live_receive_amt_1d":"285800","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":24,"multi_live_active_cnt_1d":15,"multi_live_meeting_cnt_1d":0,"multi_live_consume_cnt_1d":2,"multi_live_consume_ratio":"13.33%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":2},{"dt":"2024-08-07","multi_live_name":"-99","multi_live_uuid":"bhccicdgggiabbij","consortia_id":1000688,"consortia_name":"青色天马","is_active_multi_live":1,"multi_live_duration_h":6.0,"multi_live_receive_amt_1d":"239748","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":28,"multi_live_active_cnt_1d":17,"multi_live_meeting_cnt_1d":2,"multi_live_consume_cnt_1d":2,"multi_live_consume_ratio":"11.76%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":0},{"dt":"2024-08-07","multi_live_name":"-99","multi_live_uuid":"bhccfgjeccdiaigj","consortia_id":1000688,"consortia_name":"青色天马","is_active_multi_live":0,"multi_live_duration_h":1.26,"multi_live_receive_amt_1d":"204540","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":18,"multi_live_active_cnt_1d":9,"multi_live_meeting_cnt_1d":4,"multi_live_consume_cnt_1d":1,"multi_live_consume_ratio":"11.11%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":4},{"dt":"2024-10-21","multi_live_name":"木子","multi_live_uuid":"bhcfhbcjfhbejddh","consortia_id":1000134,"consortia_name":"美美公会","is_active_multi_live":1,"multi_live_duration_h":178.3,"multi_live_receive_amt_1d":"105520","multi_live_active_host_cnt_1d":0,"multi_live_hall_exposure_cnt_1d":749,"multi_live_active_cnt_1d":185,"multi_live_meeting_cnt_1d":4,"multi_live_consume_cnt_1d":3,"multi_live_consume_ratio":"1.62%","multi_live_retention_ratio_2d":"0.00%","multi_live_new_active_cnt_1d":124}],"total":1000,"pageSize":10,"page":1}

info | 162 | 1735624583737 | 2024-12-31 13:56:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 2d2a40649db54a4eb54ccd944abb69e6 | - | - | - | - | 940004 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624593740 | 2024-12-31 13:56:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 760003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624603738 | 2024-12-31 13:56:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 770000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624613735 | 2024-12-31 13:56:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 779996 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624623738 | 2024-12-31 13:57:03 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 790000 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624633737 | 2024-12-31 13:57:13 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 799999 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624643735 | 2024-12-31 13:57:23 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 809997 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624653735 | 2024-12-31 13:57:33 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 819996 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624663735 | 2024-12-31 13:57:43 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 829996 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 163 | 1735624673738 | 2024-12-31 13:57:53 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 8a6237dcdda24f83b62f1668ae404b93 | - | - | - | - | 840001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 15 | 1735624685945 | 2024-12-31 13:58:05 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Thread | run | 9573a648081f4cf6a831b7748eba8ab5 | - | - | - | - | 6 | 0 | - | - | - | - | background-preinit o.h.v.i.u.Version HV000001: Hibernate Validator 6.1.7.Final

info | 1 | 1735624685955 | 2024-12-31 13:58:05 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Application | main | ******************************** | - | - | - | - | 6 | 0 | - | - | - | - | main c.t.g.Application Starting Application using Java 1.8.0_391 on chenweideMacBook-Pro.local with PID 91857 (/Users/<USER>/stash/j47/tq-newgonghui-server/target/classes started by chenwei in /Users/<USER>/stash/j47)

info | 1 | 1735624685964 | 2024-12-31 13:58:05 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | Application | main | ******************************** | - | - | - | - | 7 | 0 | - | - | - | - | main c.t.g.Application The following profiles are active: dev

info | 1 | 1735624686502 | 2024-12-31 13:58:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | ******************************** | - | - | - | - | 545 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 gonghuiStringRedisTemplate 成功, primary:{}

info | 1 | 1735624686504 | 2024-12-31 13:58:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | ******************************** | - | - | - | - | 547 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 lockStringRedisTemplate 成功, primary:{}

info | 1 | 1735624686506 | 2024-12-31 13:58:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | ******************************** | - | - | - | - | 549 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 masterStringRedisTemplate 成功, primary:{}

info | 1 | 1735624686507 | 2024-12-31 13:58:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | ******************************** | - | - | - | - | 550 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor 创建 backStringRedisTemplate 成功, primary:{}

info | 1 | 1735624686510 | 2024-12-31 13:58:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | ******************************** | - | - | - | - | 553 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor etcd获取到Application配置

info | 1 | 1735624686548 | 2024-12-31 13:58:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | ******************************** | - | - | - | - | 591 | 0 | - | - | - | - | main c.t.c.e.p.SoaApplicationProcessor [etcd soa application] 配置初始化结束！

info | 1 | 1735624686579 | 2024-12-31 13:58:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | ******************************** | - | - | - | - | 622 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor 数据源BeanDefinition创建成功

info | 1 | 1735624686581 | 2024-12-31 13:58:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | ******************************** | - | - | - | - | 624 | 0 | - | - | - | - | main c.t.c.e.p.DataSourceEtcdProcessor JdbcTemplate BeanDefinition创建成功

info | 1 | 1735624686581 | 2024-12-31 13:58:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | ******************************** | - | - | - | - | 624 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor etcd获取到Biz配置

info | 1 | 1735624686631 | 2024-12-31 13:58:06 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | EtcdApplicationListener | onApplicationEvent | ******************************** | - | - | - | - | 674 | 0 | - | - | - | - | main c.t.c.e.p.AutowireSpringValueProcessor [etcd Biz] 配置初始化结束！

info | 1 | 1735624688797 | 2024-12-31 13:58:08 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | ******************************** | - | - | - | - | 2840 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735624688799 | 2024-12-31 13:58:08 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | ******************************** | - | - | - | - | 2842 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data JPA repositories in DEFAULT mode.

info | 1 | 1735624688840 | 2024-12-31 13:58:08 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | ******************************** | - | - | - | - | 2883 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 35 ms. Found 0 JPA repository interfaces.

info | 1 | 1735624688849 | 2024-12-31 13:58:08 | v2/ConfigurationClassBeanDefinitionReader/loadBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ConfigurationClassBeanDefinitionReader | loadBeanDefinitions | ******************************** | - | - | - | - | 2893 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Multiple Spring Data modules found, entering strict repository configuration mode!

info | 1 | 1735624688850 | 2024-12-31 13:58:08 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | ******************************** | - | - | - | - | 2894 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Bootstrapping Spring Data Redis repositories in DEFAULT mode.

info | 1 | 1735624688877 | 2024-12-31 13:58:08 | v2/ConfigurationClassPostProcessor/processConfigBeanDefinitions | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ConfigurationClassPostProcessor | processConfigBeanDefinitions | ******************************** | - | - | - | - | 2921 | 0 | - | - | - | - | main o.s.d.r.c.RepositoryConfigurationDelegate Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.

info | 1 | 1735624689589 | 2024-12-31 13:58:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.477 | *************** | - | 2 | AbstractBeanFactory | doGetBean | ******************************** | - | - | - | - | 3632 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$EnhancerBySpringCGLIB$$44ae79ec] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735624689608 | 2024-12-31 13:58:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.479 | *************** | - | 2 | AbstractBeanFactory | doGetBean | ******************************** | - | - | - | - | 3652 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$EnhancerBySpringCGLIB$$c3b4bf64] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735624689677 | 2024-12-31 13:58:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.481 | *************** | - | 2 | AbstractBeanFactory | doGetBean | ******************************** | - | - | - | - | 3720 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionConfiguration' of type [cn.taqu.core.configuration.ConversionConfiguration$$EnhancerBySpringCGLIB$$421dca2d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735624689682 | 2024-12-31 13:58:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.483 | *************** | - | 2 | AbstractBeanFactory | doGetBean | ******************************** | - | - | - | - | 3725 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'conversionService' of type [org.springframework.core.convert.support.DefaultConversionService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735624689746 | 2024-12-31 13:58:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.485 | *************** | - | 2 | AbstractBeanFactory | doGetBean | ******************************** | - | - | - | - | 3790 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735624689752 | 2024-12-31 13:58:09 | v2/AbstractBeanFactory/doGetBean | online | - | 1 | - | - | cli | j47 | 0.487 | *************** | - | 2 | AbstractBeanFactory | doGetBean | ******************************** | - | - | - | - | 3795 | 0 | - | - | - | - | main o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker Bean 'sentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)

info | 1 | 1735624690485 | 2024-12-31 13:58:10 | v2/LifecycleBase/init | online | - | 1 | - | - | cli | j47 | 0.489 | *************** | - | 2 | LifecycleBase | init | ******************************** | - | - | - | - | 4528 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Initializing ProtocolHandler ["http-nio-8087"]

info | 1 | 1735624690499 | 2024-12-31 13:58:10 | v2/TomcatWebServer/initialize | online | - | 1 | - | - | cli | j47 | 0.491 | *************** | - | 2 | TomcatWebServer | initialize | ******************************** | - | - | - | - | 4542 | 0 | - | - | - | - | main o.a.c.c.StandardService Starting service [Tomcat]

info | 1 | 1735624690499 | 2024-12-31 13:58:10 | v2/LifecycleBase/start | online | - | 1 | - | - | cli | j47 | 0.493 | *************** | - | 2 | LifecycleBase | start | ******************************** | - | - | - | - | 4543 | 0 | - | - | - | - | main o.a.c.c.StandardEngine Starting Servlet engine: [Apache Tomcat/9.0.45]

info | 1 | 1735624690617 | 2024-12-31 13:58:10 | v2/StandardContext/startInternal | online | - | 1 | - | - | cli | j47 | 0.495 | *************** | - | 2 | StandardContext | startInternal | ******************************** | - | - | - | - | 4660 | 0 | - | - | - | - | main o.a.c.c.C.[.[.[/tq-newgonghui] Initializing Spring embedded WebApplicationContext

info | 1 | 1735624698034 | 2024-12-31 13:58:18 | v2/DataSourceUtils/getConnection | online | - | 1 | - | - | cli | j47 | 0.499 | *************** | - | 2 | DataSourceUtils | getConnection | ******************************** | - | - | - | - | 12078 | 0 | - | - | - | - | main c.a.d.p.DruidDataSource {dataSource-1,default} inited

info | 1 | 1735624698109 | 2024-12-31 13:58:18 | v2/LocalContainerEntityManagerFactoryBean/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.501 | *************** | - | 2 | LocalContainerEntityManagerFactoryBean | afterPropertiesSet | ******************************** | - | - | - | - | 12152 | 0 | - | - | - | - | main o.h.j.i.u.LogHelper HHH000204: Processing PersistenceUnitInfo [name: default]

info | 1 | 1735624698164 | 2024-12-31 13:58:18 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.503 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | ******************************** | - | - | - | - | 12207 | 0 | - | - | - | - | main o.h.Version HHH000412: Hibernate ORM core version 5.4.30.Final

info | 1 | 1735624698281 | 2024-12-31 13:58:18 | v2/SpringHibernateJpaPersistenceProvider/createContainerEntityManagerFactory | online | - | 1 | - | - | cli | j47 | 0.505 | *************** | - | 2 | SpringHibernateJpaPersistenceProvider | createContainerEntityManagerFactory | ******************************** | - | - | - | - | 12324 | 0 | - | - | - | - | main o.h.a.c.Version HCANN000001: Hibernate Commons Annotations {5.1.2.Final}

info | 1 | 1735624698354 | 2024-12-31 13:58:18 | v2/DialectFactoryImpl/determineDialect | online | - | 1 | - | - | cli | j47 | 0.507 | *************** | - | 2 | DialectFactoryImpl | determineDialect | ******************************** | - | - | - | - | 12397 | 0 | - | - | - | - | main o.h.d.Dialect HHH000400: Using dialect: org.hibernate.dialect.MySQL55Dialect

info | 1 | 1735624698471 | 2024-12-31 13:58:18 | v2/SessionFactoryImpl/canAccessTransactionManager | online | - | 1 | - | - | cli | j47 | 0.509 | *************** | - | 2 | SessionFactoryImpl | canAccessTransactionManager | ******************************** | - | - | - | - | 12514 | 0 | - | - | - | - | main o.h.e.t.j.p.i.JtaPlatformInitiator HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]

info | 1 | 1735624698477 | 2024-12-31 13:58:18 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.511 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | ******************************** | - | - | - | - | 12520 | 0 | - | - | - | - | main o.s.o.j.LocalContainerEntityManagerFactoryBean Initialized JPA EntityManagerFactory for persistence unit 'default'

info | 1 | 1735624702320 | 2024-12-31 13:58:22 | v2/NativeMethodAccessorImpl/invoke | online | - | 1 | - | - | cli | j47 | 0.513 | *************** | - | 2 | NativeMethodAccessorImpl | invoke | ******************************** | - | - | - | - | 16363 | 0 | - | - | - | - | main c.a.c.s.SentinelWebAutoConfiguration [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].

info | 1 | 1735624702587 | 2024-12-31 13:58:22 | v2/AbstractLifecycle/init | online | - | 1 | - | - | cli | j47 | 0.515 | *************** | - | 2 | AbstractLifecycle | init | ******************************** | - | - | - | - | 16630 | 0 | - | - | - | - | main c.a.j.s.DefaultMetricsManager cache stat period at 15 MINUTES

info | 1 | 1735624702608 | 2024-12-31 13:58:22 | v2/SentinelNacosSourceImport/nacosConfig | online | - | 1 | - | - | cli | j47 | 0.517 | *************** | - | 2 | SentinelNacosSourceImport | nacosConfig | ******************************** | - | - | - | - | 16651 | 0 | - | - | - | - | main c.t.s.d.NacosConfig 环境:online 使用地址:mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848 nameSpace:sentinel groupid:sentinel_rules

info | 42 | 1735624702810 | 2024-12-31 13:58:22 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 814165a8bdf34e1ab6394453ada74bcb | - | - | - | - | 0 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 80 ms to scan 1 urls, producing 3 keys and 6 values 

info | 42 | 1735624702851 | 2024-12-31 13:58:22 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 814165a8bdf34e1ab6394453ada74bcb | - | - | - | - | 42 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 

info | 42 | 1735624702865 | 2024-12-31 13:58:22 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 814165a8bdf34e1ab6394453ada74bcb | - | - | - | - | 55 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 

info | 42 | 1735624703011 | 2024-12-31 13:58:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 814165a8bdf34e1ab6394453ada74bcb | - | - | - | - | 202 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 144 ms to scan 311 urls, producing 0 keys and 0 values 

info | 42 | 1735624703018 | 2024-12-31 13:58:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 814165a8bdf34e1ab6394453ada74bcb | - | - | - | - | 208 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 

info | 42 | 1735624703029 | 2024-12-31 13:58:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 814165a8bdf34e1ab6394453ada74bcb | - | - | - | - | 219 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 

info | 42 | 1735624703059 | 2024-12-31 13:58:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 814165a8bdf34e1ab6394453ada74bcb | - | - | - | - | 249 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 

info | 42 | 1735624703174 | 2024-12-31 13:58:23 | v2/RpcClientFactory/lambda$createClient$0 | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | RpcClientFactory | lambda$createClient$0 | 814165a8bdf34e1ab6394453ada74bcb | - | - | - | - | 364 | 0 | - | - | - | - | com.alibaba.nacos.client.Worker o.r.Reflections Reflections took 109 ms to scan 311 urls, producing 0 keys and 0 values 

info | 1 | 1735624705806 | 2024-12-31 13:58:25 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.519 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | ******************************** | - | - | - | - | 19849 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskExecutor Initializing ExecutorService 'applicationTaskExecutor'

info | 1 | 1735624706554 | 2024-12-31 13:58:26 | v2/AbstractConfiguredSecurityBuilder/doBuild | online | - | 1 | - | - | cli | j47 | 0.521 | *************** | - | 2 | AbstractConfiguredSecurityBuilder | doBuild | ******************************** | - | - | - | - | 20597 | 0 | - | - | - | - | main o.s.s.w.DefaultSecurityFilterChain Will secure cn.taqu.gonghui.common.configuration.SoaMatcher@4156134d with [org.springframework.security.web.session.DisableEncodeUrlFilter@3c3f2465, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6a5b87dc, org.springframework.security.web.context.SecurityContextPersistenceFilter@f78d16b, org.springframework.security.web.header.HeaderWriterFilter@4c0b35f4, org.springframework.security.web.authentication.logout.LogoutFilter@1cb50215, org.springframework.web.filter.CorsFilter@733cb124, cn.taqu.gonghui.common.configuration.JwtAuthenticationTokenFilter@4bfb91df, cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationFilter@6559faf1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@65293200, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5ce0d1aa, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1464416c, org.springframework.security.web.session.SessionManagementFilter@17abf9bb, org.springframework.security.web.access.ExceptionTranslationFilter@36f120c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4883c638]

info | 1 | 1735624706577 | 2024-12-31 13:58:26 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.523 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | ******************************** | - | - | - | - | 20620 | 0 | - | - | - | - | main o.s.s.c.ThreadPoolTaskScheduler Initializing ExecutorService 'taskScheduler'

info | 1 | 1735624706661 | 2024-12-31 13:58:26 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.525 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | ******************************** | - | - | - | - | 20704 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] start to initialize PowerJobWorker...

info | 1 | 1735624706663 | 2024-12-31 13:58:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.527 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | ******************************** | - | - | - | - | 20706 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter 
 ███████                                          ██          ██
░██░░░░██                                        ░██         ░██
░██   ░██  ██████  ███     ██  █████  ██████     ░██  ██████ ░██
░███████  ██░░░░██░░██  █ ░██ ██░░░██░░██░░█     ░██ ██░░░░██░██████
░██░░░░  ░██   ░██ ░██ ███░██░███████ ░██ ░      ░██░██   ░██░██░░░██
░██      ░██   ░██ ░████░████░██░░░░  ░██    ██  ░██░██   ░██░██  ░██
░██      ░░██████  ███░ ░░░██░░██████░███   ░░█████ ░░██████ ░██████
░░        ░░░░░░  ░░░    ░░░  ░░░░░░ ░░░     ░░░░░   ░░░░░░  ░░░░░

* Maintainer: <EMAIL> & PowerJob-Team
* OfficialWebsite: http://www.powerjob.tech/
* SourceCode: https://github.com/PowerJob/PowerJob



info | 1 | 1735624706664 | 2024-12-31 13:58:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.529 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | ******************************** | - | - | - | - | 20707 | 0 | - | - | - | - | main t.p.w.c.PowerBannerPrinter :: PowerJob Worker ::  (v4.3.9)

info | 1 | 1735624706667 | 2024-12-31 13:58:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.531 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | ******************************** | - | - | - | - | 20710 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] initialize PingPongSocketServer successfully~

info | 1 | 1735624706670 | 2024-12-31 13:58:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.533 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | ******************************** | - | - | - | - | 20713 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi1 (anpi1)

info | 1 | 1735624706670 | 2024-12-31 13:58:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.535 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | ******************************** | - | - | - | - | 20713 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:anpi0 (anpi0)

info | 1 | 1735624706670 | 2024-12-31 13:58:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.537 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | ******************************** | - | - | - | - | 20713 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:en5 (en5)

info | 1 | 1735624706850 | 2024-12-31 13:58:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.539 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | ******************************** | - | - | - | - | 20893 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun0 (utun0)

info | 1 | 1735624706850 | 2024-12-31 13:58:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.541 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | ******************************** | - | - | - | - | 20893 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun1 (utun1)

info | 1 | 1735624706850 | 2024-12-31 13:58:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.543 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | ******************************** | - | - | - | - | 20893 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:utun2 (utun2)

info | 1 | 1735624706851 | 2024-12-31 13:58:26 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.545 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | ******************************** | - | - | - | - | 20894 | 0 | - | - | - | - | main t.p.c.u.NetUtils [Net] try to choose NetworkInterface by NetworkInterfaceChecker, current NetworkInterface: name:bridge100 (bridge100)

info | 1 | 1735624706907 | 2024-12-31 13:58:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.547 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | ******************************** | - | - | - | - | 20951 | 0 | - | - | - | - | main t.p.w.c.u.WorkerNetUtils [WorkerNetUtils] close PingPongSocketServer successfully~

info | 1 | 1735624706969 | 2024-12-31 13:58:26 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.549 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | ******************************** | - | - | - | - | 21012 | 0 | - | - | - | - | main t.p.w.b.d.PowerJobServerDiscoveryService [PowerJobWorker] assert appName(mp-gonghui) succeed, result from server is: 11.

info | 1 | 1735624706970 | 2024-12-31 13:58:26 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.551 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | ******************************** | - | - | - | - | 21013 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] [ADDRESS_INFO] localBindIp: ***************, localBindPort: 27777; externalIp: ***************, externalPort: 27777

info | 1 | 1735624706978 | 2024-12-31 13:58:26 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.553 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | ******************************** | - | - | - | - | 21022 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start remote engine with config: EngineConfig(serverType=WORKER, type=AKKA, bindAddress=***************:27777, actorList=[tech.powerjob.worker.actors.TaskTrackerActor@22116fe6, tech.powerjob.worker.actors.ProcessorTrackerActor@6e496e14, tech.powerjob.worker.actors.WorkerActor@3b8291ff])

info | 1 | 1735624707012 | 2024-12-31 13:58:27 | v2/PowerJobSpringWorker/afterPropertiesSet | online | - | 1 | - | - | cli | j47 | 0.555 | *************** | - | 2 | PowerJobSpringWorker | afterPropertiesSet | ******************************** | - | - | - | - | 21055 | 0 | - | - | - | - | main o.r.Reflections Reflections took 25 ms to scan 6 urls, producing 50 keys and 148 values 

info | 1 | 1735624707017 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.557 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | ******************************** | - | - | - | - | 21060 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] scan subTypeOf CSInitializer: [class tech.powerjob.remote.http.HttpVertxCSInitializer, class tech.powerjob.remote.akka.AkkaCSInitializer]

info | 1 | 1735624707018 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.559 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | ******************************** | - | - | - | - | 21061 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.http.HttpVertxCSInitializer] successfully, type=HTTP, object: tech.powerjob.remote.http.HttpVertxCSInitializer@3eec817a

info | 1 | 1735624707019 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.561 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | ******************************** | - | - | - | - | 21062 | 0 | - | - | - | - | main t.p.r.f.e.i.CSInitializerFactory [CSInitializerFactory] new instance for CSInitializer[class tech.powerjob.remote.akka.AkkaCSInitializer] successfully, type=AKKA, object: tech.powerjob.remote.akka.AkkaCSInitializer@1ba43bc9

info | 1 | 1735624707019 | 2024-12-31 13:58:27 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.563 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | ******************************** | - | - | - | - | 21062 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] try to startup CSInitializer[type=AKKA]

info | 1 | 1735624707019 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.565 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | ******************************** | - | - | - | - | 21062 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] bindAddress: ***************:27777

info | 1 | 1735624707022 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.567 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | ******************************** | - | - | - | - | 21065 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] try to start AKKA System.

info | 123 | 1735624707468 | 2024-12-31 13:58:27 | v2/Mailbox/processMailbox | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | Mailbox | processMailbox | ******************************** | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.e.s.Slf4jLogger Slf4jLogger started

info | 1 | 1735624707852 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.569 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | ******************************** | - | - | - | - | 21895 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] initialize actorSystem[oms] successfully!

info | 1 | 1735624707852 | 2024-12-31 13:58:27 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.571 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] start to bind Handler

info | 1 | 1735624707853 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.573 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportTaskStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorReportTaskStatusReq(tech.powerjob.worker.pojo.request.ProcessorReportTaskStatusReq)

info | 1 | 1735624707853 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.575 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/mapTask, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorMapTaskRequest(tech.powerjob.worker.pojo.request.ProcessorMapTaskRequest)

info | 1 | 1735624707853 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.577 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/runJob, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735624707853 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.579 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/reportProcessorTrackerStatus, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveProcessorTrackerStatusReportReq(tech.powerjob.worker.pojo.request.ProcessorTrackerStatusReportReq)

info | 1 | 1735624707853 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.581 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/stopInstance, handler=public void tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735624707853 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.583 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/taskTracker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.TaskTrackerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735624707853 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.585 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/startTask, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStartTaskReq(tech.powerjob.worker.pojo.request.TaskTrackerStartTaskReq)

info | 1 | 1735624707853 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.587 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/processorTracker/stopInstance, handler=public void tech.powerjob.worker.actors.ProcessorTrackerActor.onReceiveTaskTrackerStopInstanceReq(tech.powerjob.worker.pojo.request.TaskTrackerStopInstanceReq)

info | 1 | 1735624707853 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.589 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/runJob, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerScheduleJobReq(tech.powerjob.common.request.ServerScheduleJobReq)

info | 1 | 1735624707853 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.591 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21896 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/stopInstance, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerStopInstanceReq(tech.powerjob.common.request.ServerStopInstanceReq)

info | 1 | 1735624707854 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.593 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21897 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/queryInstanceStatus, handler=public tech.powerjob.common.response.AskResponse tech.powerjob.worker.actors.WorkerActor.onReceiveServerQueryInstanceStatusReq(tech.powerjob.common.request.ServerQueryInstanceStatusReq)

info | 1 | 1735624707854 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.595 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21897 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/deployContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDeployContainerRequest(tech.powerjob.common.request.ServerDeployContainerRequest)

info | 1 | 1735624707854 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/invokeInitMethods | online | - | 1 | - | - | cli | j47 | 0.597 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | invokeInitMethods | ******************************** | - | - | - | - | 21897 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] PATH=/worker/destroyContainer, handler=public void tech.powerjob.worker.actors.WorkerActor.onReceiveServerDestroyContainerRequest(tech.powerjob.common.request.ServerDestroyContainerRequest)

info | 1 | 1735624707855 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.599 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | ******************************** | - | - | - | - | 21898 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=taskTracker,config={"actorName":"task_tracker","dispatcherName":"task-tracker-dispatcher"}]

info | 1 | 1735624707857 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.601 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | ******************************** | - | - | - | - | 21900 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=processorTracker,config={"actorName":"processor_tracker","dispatcherName":"processor-tracker-dispatcher"}]

info | 1 | 1735624707858 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/initializeBean | online | - | 1 | - | - | cli | j47 | 0.603 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | initializeBean | ******************************** | - | - | - | - | 21901 | 0 | - | - | - | - | main t.p.r.a.AkkaCSInitializer [PowerJob-AKKA] start to process actor[path=worker,config={"actorName":"worker","dispatcherName":"common-dispatcher"}]

info | 1 | 1735624707858 | 2024-12-31 13:58:27 | v2/AbstractBeanFactory/lambda$doGetBean$0 | online | - | 1 | - | - | cli | j47 | 0.605 | *************** | - | 2 | AbstractBeanFactory | lambda$doGetBean$0 | ******************************** | - | - | - | - | 21903 | 0 | - | - | - | - | main t.p.r.f.e.i.PowerJobRemoteEngine [PowerJobRemoteEngine] [AKKA] startup successfully, cost: 839.4 ms

info | 1 | 1735624707927 | 2024-12-31 13:58:27 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.607 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | ******************************** | - | - | - | - | 21970 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobRemoteEngine initialized successfully.

info | 1 | 1735624707931 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.609 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | ******************************** | - | - | - | - | 21974 | 0 | - | - | - | - | main t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 1 | 1735624707931 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.611 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | ******************************** | - | - | - | - | 21974 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] H2 database version: 1.4.200

info | 1 | 1735624707934 | 2024-12-31 13:58:27 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.613 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | ******************************** | - | - | - | - | 21977 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Starting...

info | 1 | 1735624708114 | 2024-12-31 13:58:28 | v2/AbstractAutowireCapableBeanFactory/doCreateBean | online | - | 1 | - | - | cli | j47 | 0.615 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | doCreateBean | ******************************** | - | - | - | - | 22157 | 0 | - | - | - | - | main c.z.h.HikariDataSource HikariPool-1 - Start completed.

info | 1 | 1735624708114 | 2024-12-31 13:58:28 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.617 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | ******************************** | - | - | - | - | 22157 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] init h2 datasource successfully, use url: jdbc:h2:file:/Users/<USER>/powerjob/worker/h2/e4a56e7eba984d00b48e6705d84d53e9/powerjob_worker_db;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false

info | 1 | 1735624708119 | 2024-12-31 13:58:28 | v2/AbstractAutowireCapableBeanFactory/createBean | online | - | 1 | - | - | cli | j47 | 0.619 | *************** | - | 2 | AbstractAutowireCapableBeanFactory | createBean | ******************************** | - | - | - | - | 22162 | 0 | - | - | - | - | main t.p.w.p.d.ConnectionFactory [PowerDatasource] delete worker db file[/Users/<USER>/powerjob/worker/h2/e4a56e7eba984d00b48e6705d84d53e9/] on JVM exit successfully

info | 1 | 1735624708134 | 2024-12-31 13:58:28 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.621 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | ******************************** | - | - | - | - | 22177 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] local storage initialized successfully.

info | 1 | 1735624708134 | 2024-12-31 13:58:28 | v2/DefaultSingletonBeanRegistry/getSingleton | online | - | 1 | - | - | cli | j47 | 0.623 | *************** | - | 2 | DefaultSingletonBeanRegistry | getSingleton | ******************************** | - | - | - | - | 22178 | 0 | - | - | - | - | main t.p.w.PowerJobWorker [PowerJobWorker] PowerJobWorker initialized successfully, using time: 1.473 s, congratulations!

info | 151 | 1735624708139 | 2024-12-31 13:58:28 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/access$301 | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | access$301 | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.c.u.PowerFileUtils [PowerFileUtils] [workspace] use user.home as workspace: /Users/<USER>/powerjob/worker

info | 151 | 1735624708141 | 2024-12-31 13:58:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 2 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 1 | 1735624708190 | 2024-12-31 13:58:28 | v2/TomcatWebServer/start | online | - | 1 | - | - | cli | j47 | 0.625 | *************** | - | 2 | TomcatWebServer | start | ******************************** | - | - | - | - | 22233 | 0 | - | - | - | - | main o.a.c.h.Http11NioProtocol Starting ProtocolHandler ["http-nio-8087"]

info | 1 | 1735624708230 | 2024-12-31 13:58:28 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.627 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | ******************************** | - | - | - | - | 22273 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Context refreshed

info | 1 | 1735624708250 | 2024-12-31 13:58:28 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.629 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | ******************************** | - | - | - | - | 22293 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Found 1 custom documentation plugin(s)

info | 1 | 1735624708250 | 2024-12-31 13:58:28 | v2/AbstractApplicationContext/finishRefresh | online | - | 1 | - | - | cli | j47 | 0.631 | *************** | - | 2 | AbstractApplicationContext | finishRefresh | ******************************** | - | - | - | - | 22293 | 0 | - | - | - | - | main s.d.s.w.p.DocumentationPluginsBootstrapper Skipping initializing disabled plugin bean swagger v2.0

info | 1 | 1735624708279 | 2024-12-31 13:58:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.633 | *************** | - | 2 | Application | main | ******************************** | - | - | - | - | 22322 | 0 | - | - | - | - | main c.t.g.Application Started Application in 22.821 seconds (JVM running for 23.416)

info | 1 | 1735624708314 | 2024-12-31 13:58:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.635 | *************** | - | 2 | Application | main | ******************************** | - | - | - | - | 22357 | 0 | - | - | - | - | main c.t.g.Application 公会服务启动成功

info | 1 | 1735624708314 | 2024-12-31 13:58:28 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.637 | *************** | - | 2 | Application | main | ******************************** | - | - | - | - | 22357 | 0 | - | - | - | - | main c.t.g.Application 加密开关配置为,是否双写：0[0-关闭 1-开启], 优先读取从:2[1-优先从明文 2-只从密文], 是否使用摘要查询:1[0-否  1-是]

info | 221 | 1735624708321 | 2024-12-31 13:58:28 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ChatRoomService | refreshChatCache | 7acdb26a7532433a8cb50a8c54f30a90 | - | - | - | - | 0 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache

info | 151 | 1735624718135 | 2024-12-31 13:58:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 9997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 221 | 1735624718970 | 2024-12-31 13:58:38 | v2/ChatRoomService/refreshChatCache | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ChatRoomService | refreshChatCache | 7acdb26a7532433a8cb50a8c54f30a90 | - | - | - | - | 10650 | 0 | - | - | - | - | task-1 c.t.g.c.s.ChatRoomService 刷新 refreshChatCache 完成

info | 151 | 1735624728136 | 2024-12-31 13:58:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 19997 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735624738141 | 2024-12-31 13:58:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 0 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735624748137 | 2024-12-31 13:59:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 9996 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735624758139 | 2024-12-31 13:59:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 19999 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735624768136 | 2024-12-31 13:59:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 29994 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735624778136 | 2024-12-31 13:59:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 39995 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735624788136 | 2024-12-31 13:59:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 49994 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735624798136 | 2024-12-31 13:59:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 59995 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1735624800027 | 2024-12-31 14:00:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | e05b6fdefe3345b29f91dbe6fcd0a8d1 | - | - | - | - | 0 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-12-31 13:58:22,586 to 2024-12-31 14:00:00,017
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 155 | 1735624808137 | 2024-12-31 14:00:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 69995 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624818140 | 2024-12-31 14:00:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 110002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624828136 | 2024-12-31 14:00:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 119998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624838136 | 2024-12-31 14:00:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 129998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624848142 | 2024-12-31 14:00:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 140004 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624858140 | 2024-12-31 14:00:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 150002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624868141 | 2024-12-31 14:01:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 160003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624878140 | 2024-12-31 14:01:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 170002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624888141 | 2024-12-31 14:01:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 180003 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624898142 | 2024-12-31 14:01:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 190005 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624908141 | 2024-12-31 14:01:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 200002 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624918140 | 2024-12-31 14:01:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 210001 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624928136 | 2024-12-31 14:02:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 219998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624938136 | 2024-12-31 14:02:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 229998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624948136 | 2024-12-31 14:02:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 239998 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624958138 | 2024-12-31 14:02:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 250008 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624968138 | 2024-12-31 14:02:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 260000 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624978138 | 2024-12-31 14:02:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 269999 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624988155 | 2024-12-31 14:03:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 280017 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735624998162 | 2024-12-31 14:03:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 290024 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625008161 | 2024-12-31 14:03:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 300023 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625018164 | 2024-12-31 14:03:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 280022 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625028166 | 2024-12-31 14:03:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 320028 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625038167 | 2024-12-31 14:03:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 330030 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625048170 | 2024-12-31 14:04:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.1 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 1 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625058169 | 2024-12-31 14:04:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 10001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625068171 | 2024-12-31 14:04:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.5 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 20002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625078171 | 2024-12-31 14:04:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 370033 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625088170 | 2024-12-31 14:04:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.7 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 40002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625098169 | 2024-12-31 14:04:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 360028 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625108170 | 2024-12-31 14:05:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 370030 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625118172 | 2024-12-31 14:05:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 380030 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625128168 | 2024-12-31 14:05:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 390027 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625138168 | 2024-12-31 14:05:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.9 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 89998 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625148171 | 2024-12-31 14:05:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.11 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 100001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625158170 | 2024-12-31 14:05:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.13 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 110001 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625168171 | 2024-12-31 14:06:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 430030 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625178180 | 2024-12-31 14:06:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 440040 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625188173 | 2024-12-31 14:06:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 450031 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625198169 | 2024-12-31 14:06:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 460028 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625208169 | 2024-12-31 14:06:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 500031 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625218169 | 2024-12-31 14:06:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 510032 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625228171 | 2024-12-31 14:07:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 520032 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625238168 | 2024-12-31 14:07:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 530030 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625248170 | 2024-12-31 14:07:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 540031 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625258170 | 2024-12-31 14:07:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 550032 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625268173 | 2024-12-31 14:07:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 560035 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625278169 | 2024-12-31 14:07:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 570031 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625288174 | 2024-12-31 14:08:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 550032 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625298174 | 2024-12-31 14:08:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 560032 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625308174 | 2024-12-31 14:08:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 600036 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625318171 | 2024-12-31 14:08:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 610033 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625328172 | 2024-12-31 14:08:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 590030 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625338173 | 2024-12-31 14:08:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 630034 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625348173 | 2024-12-31 14:09:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 610031 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625358170 | 2024-12-31 14:09:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.77 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 650032 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625368171 | 2024-12-31 14:09:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.15 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 320002 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625378175 | 2024-12-31 14:09:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.17 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 330006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625388175 | 2024-12-31 14:09:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.19 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 340007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625398175 | 2024-12-31 14:09:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.21 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 350005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625408175 | 2024-12-31 14:10:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 670034 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625418172 | 2024-12-31 14:10:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 680031 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625428175 | 2024-12-31 14:10:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 690034 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625438175 | 2024-12-31 14:10:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 700033 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625448175 | 2024-12-31 14:10:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 710033 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625458178 | 2024-12-31 14:10:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 720043 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625468174 | 2024-12-31 14:11:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 730034 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625478177 | 2024-12-31 14:11:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 740036 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625488173 | 2024-12-31 14:11:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.23 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 440003 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625498172 | 2024-12-31 14:11:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.79 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 790034 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625508172 | 2024-12-31 14:11:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.81 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 800033 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625518174 | 2024-12-31 14:11:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.83 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 810035 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625528180 | 2024-12-31 14:12:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.85 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 820041 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625538174 | 2024-12-31 14:12:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.25 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 490006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625548177 | 2024-12-31 14:12:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.27 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 500007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625558177 | 2024-12-31 14:12:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.29 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 510007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625568178 | 2024-12-31 14:12:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.31 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 520009 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625578176 | 2024-12-31 14:12:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.33 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 530007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625588175 | 2024-12-31 14:13:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.35 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 540006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625598174 | 2024-12-31 14:13:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.37 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 550005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625608177 | 2024-12-31 14:13:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.39 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 560007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625618177 | 2024-12-31 14:13:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.41 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 570008 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625628178 | 2024-12-31 14:13:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.43 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 580014 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625638176 | 2024-12-31 14:13:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.45 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 590006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625648174 | 2024-12-31 14:14:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.47 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 600004 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625658178 | 2024-12-31 14:14:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.49 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 610009 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625668174 | 2024-12-31 14:14:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.51 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 620005 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625678175 | 2024-12-31 14:14:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 940033 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625688178 | 2024-12-31 14:14:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 950039 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625698179 | 2024-12-31 14:14:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 960038 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 38 | 1735625700049 | 2024-12-31 14:15:00 | v2/ScheduledThreadPoolExecutor$ScheduledFutureTask/run | online | - | 1 | - | - | cli | j47 | 0.3 | *************** | - | 2 | ScheduledThreadPoolExecutor$ScheduledFutureTask | run | e05b6fdefe3345b29f91dbe6fcd0a8d1 | - | - | - | - | 900022 | 0 | - | - | - | - | JetCacheDefaultExecutor c.a.j.s.StatInfoLogger jetcache stat from 2024-12-31 14:00:00,017 to 2024-12-31 14:15:00,047
cache|       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
-----+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------


info | 156 | 1735625708179 | 2024-12-31 14:15:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.53 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 660010 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625718175 | 2024-12-31 14:15:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.87 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 1010036 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625728178 | 2024-12-31 14:15:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.55 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 680008 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625738177 | 2024-12-31 14:15:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.57 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 690008 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625748177 | 2024-12-31 14:15:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.59 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 700008 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625758176 | 2024-12-31 14:15:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.61 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 710007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625768176 | 2024-12-31 14:16:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.63 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 720007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625778180 | 2024-12-31 14:16:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 730011 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625788176 | 2024-12-31 14:16:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 740006 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625798179 | 2024-12-31 14:16:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.89 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 1090042 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625808179 | 2024-12-31 14:16:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.91 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 1100042 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625818181 | 2024-12-31 14:16:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 770011 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625828179 | 2024-12-31 14:17:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.93 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 1120040 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625838180 | 2024-12-31 14:17:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.95 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 1130041 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625848180 | 2024-12-31 14:17:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.97 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 1140043 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625858180 | 2024-12-31 14:17:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.99 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 1150041 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 151 | 1735625868179 | 2024-12-31 14:17:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.101 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 95e0201453434d6682b8a150e096f3b5 | - | - | - | - | 1160041 | 0 | - | - | - | - | powerjob-worker-core-0 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625878177 | 2024-12-31 14:17:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 830008 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625888177 | 2024-12-31 14:18:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 840007 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625898179 | 2024-12-31 14:18:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 850010 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625908179 | 2024-12-31 14:18:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.77 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 860011 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 156 | 1735625918179 | 2024-12-31 14:18:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.79 | *************** | - | 2 | ThreadPoolExecutor | runWorker | 0e34538fbb2c4cc2bbaf5c6bfd522119 | - | - | - | - | 870009 | 0 | - | - | - | - | powerjob-worker-core-2 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625928180 | 2024-12-31 14:18:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.65 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1190039 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625938177 | 2024-12-31 14:18:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.67 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1200036 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625948180 | 2024-12-31 14:19:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.69 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1210039 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625958183 | 2024-12-31 14:19:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.71 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1220041 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625968178 | 2024-12-31 14:19:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.73 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1230037 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625978178 | 2024-12-31 14:19:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.75 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1240037 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625988180 | 2024-12-31 14:19:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.77 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1250038 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735625998178 | 2024-12-31 14:19:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.79 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1260037 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626008180 | 2024-12-31 14:20:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.81 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1270039 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626018179 | 2024-12-31 14:20:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.83 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1280038 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626028181 | 2024-12-31 14:20:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.85 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1290040 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626038183 | 2024-12-31 14:20:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.87 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1300041 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626048179 | 2024-12-31 14:20:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.89 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1310037 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626058184 | 2024-12-31 14:20:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.91 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1320043 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626068184 | 2024-12-31 14:21:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.93 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1330043 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626078184 | 2024-12-31 14:21:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.95 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1340042 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626088181 | 2024-12-31 14:21:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.97 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1350039 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626098181 | 2024-12-31 14:21:38 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.99 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1360040 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626108182 | 2024-12-31 14:21:48 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.101 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1370040 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626118184 | 2024-12-31 14:21:58 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.103 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1380043 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626128183 | 2024-12-31 14:22:08 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.105 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1390041 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626138186 | 2024-12-31 14:22:18 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.107 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1400046 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

info | 155 | 1735626148185 | 2024-12-31 14:22:28 | v2/ThreadPoolExecutor/runWorker | online | - | 1 | - | - | cli | j47 | 0.109 | *************** | - | 2 | ThreadPoolExecutor | runWorker | cef3ba1cf6e54da0b7fef2232a519cd9 | - | - | - | - | 1410043 | 0 | - | - | - | - | powerjob-worker-core-1 t.p.w.b.WorkerHealthReporter [WorkerHealthReporter] report health status,appId:11,appName:mp-gonghui,isOverload:false,maxLightweightTaskNum:1024,currentLightweightTaskNum:0,maxHeavyweightTaskNum:64,currentHeavyweightTaskNum:0

