error | 1 | 1731908052593 | 2024-11-18 13:34:12 | v2/EtcdApplicationListener/onApplicationEvent | online | - | 1 | - | - | cli | j47 | 0.7 | 192.168.120.106 | - | 2 | EtcdApplicationListener | onApplicationEvent | bf623952dde84225b340e0295b5474d0 | - | - | - | - | 356 | 0 | - | - | - | - | main c.t.c.e.p.StringRedisTemplateEtcdProcessor StringRedisTemplateEtcdProcessor.java:88 get etcd redis config error
cn.taqu.core.etcd.lib.EtcdClientException: Error executing request
	at cn.taqu.core.etcd.lib.EtcdClient.unwrap(EtcdClient.java:262)
	at cn.taqu.core.etcd.lib.EtcdClient.syncExecute(EtcdClient.java:253)
	at cn.taqu.core.etcd.lib.EtcdClient.get(EtcdClient.java:98)
	at cn.taqu.core.etcd.processor.StringRedisTemplateEtcdProcessor.initProcess(StringRedisTemplateEtcdProcessor.java:58)
	at cn.taqu.core.etcd.event.EtcdApplicationListener.lambda$onApplicationEvent$0(EtcdApplicationListener.java:148)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at cn.taqu.core.etcd.event.EtcdApplicationListener.onApplicationEvent(EtcdApplicationListener.java:148)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:131)
	at org.springframework.boot.context.event.EventPublishingRunListener.contextLoaded(EventPublishingRunListener.java:100)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$contextLoaded$4(SpringApplicationRunListeners.java:71)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:117)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:111)
	at org.springframework.boot.SpringApplicationRunListeners.contextLoaded(SpringApplicationRunListeners.java:71)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:427)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1340)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at cn.taqu.gonghui.Application.main(Application.java:35)
Caused by: java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.lib.EtcdClient.syncExecute(EtcdClient.java:247)
	... 20 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	at java.lang.Thread.run(Thread.java:750)

cn.taqu.core.etcd.lib.EtcdClientException: Error executing request
	at cn.taqu.core.etcd.lib.EtcdClient.unwrap(EtcdClient.java:262)
	at cn.taqu.core.etcd.lib.EtcdClient.syncExecute(EtcdClient.java:253)
	at cn.taqu.core.etcd.lib.EtcdClient.get(EtcdClient.java:98)
	at cn.taqu.core.etcd.processor.StringRedisTemplateEtcdProcessor.initProcess(StringRedisTemplateEtcdProcessor.java:58)
	at cn.taqu.core.etcd.event.EtcdApplicationListener.lambda$onApplicationEvent$0(EtcdApplicationListener.java:148)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at cn.taqu.core.etcd.event.EtcdApplicationListener.onApplicationEvent(EtcdApplicationListener.java:148)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:131)
	at org.springframework.boot.context.event.EventPublishingRunListener.contextLoaded(EventPublishingRunListener.java:100)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$contextLoaded$4(SpringApplicationRunListeners.java:71)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:117)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:111)
	at org.springframework.boot.SpringApplicationRunListeners.contextLoaded(SpringApplicationRunListeners.java:71)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:427)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1340)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at cn.taqu.gonghui.Application.main(Application.java:35)
java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.lib.EtcdClient.syncExecute(EtcdClient.java:247)
	... 20 more
java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	at java.lang.Thread.run(Thread.java:750)
error | 1 | 1731908052665 | 2024-11-18 13:34:12 | v2/Application/main | online | - | 1 | - | - | cli | j47 | 0.9 | 192.168.120.106 | - | 2 | Application | main | bf623952dde84225b340e0295b5474d0 | - | - | - | - | 429 | 0 | - | - | - | - | main o.s.b.SpringApplication SpringApplication.java:871 Application run failed
java.lang.RuntimeException: cn.taqu.core.etcd.lib.EtcdClientException: Error executing request
	at cn.taqu.core.etcd.processor.StringRedisTemplateEtcdProcessor.initProcess(StringRedisTemplateEtcdProcessor.java:89)
	at cn.taqu.core.etcd.event.EtcdApplicationListener.lambda$onApplicationEvent$0(EtcdApplicationListener.java:148)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at cn.taqu.core.etcd.event.EtcdApplicationListener.onApplicationEvent(EtcdApplicationListener.java:148)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:131)
	at org.springframework.boot.context.event.EventPublishingRunListener.contextLoaded(EventPublishingRunListener.java:100)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$contextLoaded$4(SpringApplicationRunListeners.java:71)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:117)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:111)
	at org.springframework.boot.SpringApplicationRunListeners.contextLoaded(SpringApplicationRunListeners.java:71)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:427)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1340)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at cn.taqu.gonghui.Application.main(Application.java:35)
Caused by: cn.taqu.core.etcd.lib.EtcdClientException: Error executing request
	at cn.taqu.core.etcd.lib.EtcdClient.unwrap(EtcdClient.java:262)
	at cn.taqu.core.etcd.lib.EtcdClient.syncExecute(EtcdClient.java:253)
	at cn.taqu.core.etcd.lib.EtcdClient.get(EtcdClient.java:98)
	at cn.taqu.core.etcd.processor.StringRedisTemplateEtcdProcessor.initProcess(StringRedisTemplateEtcdProcessor.java:58)
	... 18 common frames omitted
Caused by: java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.lib.EtcdClient.syncExecute(EtcdClient.java:247)
	... 20 common frames omitted
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	at java.lang.Thread.run(Thread.java:750)

java.lang.RuntimeException: cn.taqu.core.etcd.lib.EtcdClientException: Error executing request
	at cn.taqu.core.etcd.processor.StringRedisTemplateEtcdProcessor.initProcess(StringRedisTemplateEtcdProcessor.java:89)
	at cn.taqu.core.etcd.event.EtcdApplicationListener.lambda$onApplicationEvent$0(EtcdApplicationListener.java:148)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at cn.taqu.core.etcd.event.EtcdApplicationListener.onApplicationEvent(EtcdApplicationListener.java:148)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:131)
	at org.springframework.boot.context.event.EventPublishingRunListener.contextLoaded(EventPublishingRunListener.java:100)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$contextLoaded$4(SpringApplicationRunListeners.java:71)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:117)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:111)
	at org.springframework.boot.SpringApplicationRunListeners.contextLoaded(SpringApplicationRunListeners.java:71)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:427)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:338)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1340)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at cn.taqu.gonghui.Application.main(Application.java:35)
cn.taqu.core.etcd.lib.EtcdClientException: Error executing request
	at cn.taqu.core.etcd.lib.EtcdClient.unwrap(EtcdClient.java:262)
	at cn.taqu.core.etcd.lib.EtcdClient.syncExecute(EtcdClient.java:253)
	at cn.taqu.core.etcd.lib.EtcdClient.get(EtcdClient.java:98)
	at cn.taqu.core.etcd.processor.StringRedisTemplateEtcdProcessor.initProcess(StringRedisTemplateEtcdProcessor.java:58)
	... 18 more
java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.lib.EtcdClient.syncExecute(EtcdClient.java:247)
	... 20 more
java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	at java.lang.Thread.run(Thread.java:750)
error | 17 | 1731908052681 | 2024-11-18 13:34:12 | v2/EtcdClientFactory$EtcdWatchThread/run | online | - | 1 | - | - | cli | j47 | 0.7 | 192.168.120.106 | - | 2 | EtcdClientFactory$EtcdWatchThread | run | ffb52705559f45e0ba3b692543de1ed8 | - | - | - | - | 88 | 0 | - | - | - | - | Thread-2 c.t.c.e.EtcdClientFactory EtcdClientFactory.java:87 监听etcd目录/newgonghui变化发生异常
java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.EtcdClientFactory$EtcdWatchThread.run(EtcdClientFactory.java:76)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	... 1 common frames omitted

java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.EtcdClientFactory$EtcdWatchThread.run(EtcdClientFactory.java:76)
	at java.lang.Thread.run(Thread.java:750)
java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	... 1 more
error | 17 | 1731908053782 | 2024-11-18 13:34:13 | v2/EtcdClientFactory$EtcdWatchThread/run | online | - | 1 | - | - | cli | j47 | 0.15 | 192.168.120.106 | - | 2 | EtcdClientFactory$EtcdWatchThread | run | ffb52705559f45e0ba3b692543de1ed8 | - | - | - | - | 1189 | 0 | - | - | - | - | Thread-2 c.t.c.e.EtcdClientFactory EtcdClientFactory.java:87 监听etcd目录/newgonghui变化发生异常
java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.EtcdClientFactory$EtcdWatchThread.run(EtcdClientFactory.java:76)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	... 1 common frames omitted

java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.EtcdClientFactory$EtcdWatchThread.run(EtcdClientFactory.java:76)
	at java.lang.Thread.run(Thread.java:750)
java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	... 1 more
error | 17 | 1731908054885 | 2024-11-18 13:34:14 | v2/EtcdClientFactory$EtcdWatchThread/run | online | - | 1 | - | - | cli | j47 | 0.23 | 192.168.120.106 | - | 2 | EtcdClientFactory$EtcdWatchThread | run | ffb52705559f45e0ba3b692543de1ed8 | - | - | - | - | 2292 | 0 | - | - | - | - | Thread-2 c.t.c.e.EtcdClientFactory EtcdClientFactory.java:87 监听etcd目录/newgonghui变化发生异常
java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.EtcdClientFactory$EtcdWatchThread.run(EtcdClientFactory.java:76)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	... 1 common frames omitted

java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.EtcdClientFactory$EtcdWatchThread.run(EtcdClientFactory.java:76)
	at java.lang.Thread.run(Thread.java:750)
java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	... 1 more
error | 17 | 1731908056016 | 2024-11-18 13:34:16 | v2/EtcdClientFactory$EtcdWatchThread/run | online | - | 1 | - | - | cli | j47 | 0.31 | 192.168.120.106 | - | 2 | EtcdClientFactory$EtcdWatchThread | run | ffb52705559f45e0ba3b692543de1ed8 | - | - | - | - | 3423 | 0 | - | - | - | - | Thread-2 c.t.c.e.EtcdClientFactory EtcdClientFactory.java:87 监听etcd目录/newgonghui变化发生异常
java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.EtcdClientFactory$EtcdWatchThread.run(EtcdClientFactory.java:76)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	... 1 common frames omitted

java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.EtcdClientFactory$EtcdWatchThread.run(EtcdClientFactory.java:76)
	at java.lang.Thread.run(Thread.java:750)
java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	... 1 more
error | 17 | 1731908057131 | 2024-11-18 13:34:17 | v2/EtcdClientFactory$EtcdWatchThread/run | online | - | 1 | - | - | cli | j47 | 0.39 | 192.168.120.106 | - | 2 | EtcdClientFactory$EtcdWatchThread | run | ffb52705559f45e0ba3b692543de1ed8 | - | - | - | - | 4539 | 0 | - | - | - | - | Thread-2 c.t.c.e.EtcdClientFactory EtcdClientFactory.java:87 监听etcd目录/newgonghui变化发生异常
java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.EtcdClientFactory$EtcdWatchThread.run(EtcdClientFactory.java:76)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	... 1 common frames omitted

java.util.concurrent.ExecutionException: java.net.ConnectException: Connection refused
	at com.google.common.util.concurrent.AbstractFuture.getDoneValue(AbstractFuture.java:526)
	at com.google.common.util.concurrent.AbstractFuture.get(AbstractFuture.java:507)
	at com.google.common.util.concurrent.AbstractFuture$TrustedFuture.get(AbstractFuture.java:83)
	at cn.taqu.core.etcd.EtcdClientFactory$EtcdWatchThread.run(EtcdClientFactory.java:76)
	at java.lang.Thread.run(Thread.java:750)
java.net.ConnectException: Connection refused
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:715)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvent(DefaultConnectingIOReactor.java:174)
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:148)
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351)
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:189)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.doExecute(CloseableHttpAsyncClientBase.java:67)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase.access$000(CloseableHttpAsyncClientBase.java:38)
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:57)
	... 1 more
error | 170 | 1731908204221 | 2024-11-18 13:36:44 | v2/ chatRoom/ dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | 192.168.120.106 | - | 2 |  chatRoom |  dailyOperationData | aaqwefthfftytfyerter99 | - | - | - | - | 5374 | 0 | - | - | - | - | http-nio-8087-exec-6 c.t.g.c.w.SpringExceptionHandler SpringExceptionHandler.java:139 service=%20chatRoom&method=%20dailyOperationData&distinctRequestId=aaqwefthfftytfyerter99 错误码[405]:出错了啊！请骚候再试[j01002]

error | 171 | 1731908225218 | 2024-11-18 13:37:05 | v2/ chatRoom/ dailyOperationData | online | - | 1 | - | 127.0.0.1 | rest | j47 | 0.1 | 192.168.120.106 | - | 2 |  chatRoom |  dailyOperationData | aaqwefthfftytfyerter99 | - | - | - | - | 2444 | 0 | - | - | - | - | http-nio-8087-exec-7 c.t.g.c.w.SpringExceptionHandler SpringExceptionHandler.java:139 service=%20chatRoom&method=%20dailyOperationData&distinctRequestId=aaqwefthfftytfyerter99 错误码[405]:出错了啊！请骚候再试[j01002]

