warning | 129 | 1732675327399 | 2024-11-27 10:42:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 7ea23b418a434fed98bf137e38b933b9 | - | - | - | - | 121 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 129 | 1732675327399 | 2024-11-27 10:42:07 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 7ea23b418a434fed98bf137e38b933b9 | - | - | - | - | 121 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 232 | 1732675329798 | 2024-11-27 10:42:09 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 9fbb6f3edc01478e8455fe8611d4cad1 | - | - | - | - | 1577 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1425 ms]

warning | 267 | 1732675348211 | 2024-11-27 10:42:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 5dc757306b8e4011a65c7d75e9841d7c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675328075, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4019, jvmUsedMemory=0.3628, jvmMaxMemory=3.5557, jvmMemoryUsage=0.102, diskUsed=214.6384, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 267 | 1732675348223 | 2024-11-27 10:42:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 5dc757306b8e4011a65c7d75e9841d7c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675338074, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2598, jvmUsedMemory=0.5116, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1439, diskUsed=214.6376, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 267 | 1732675348223 | 2024-11-27 10:42:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 5dc757306b8e4011a65c7d75e9841d7c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675348078, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2256, jvmUsedMemory=0.5209, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1465, diskUsed=214.6377, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 267 | 1732675348288 | 2024-11-27 10:42:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 5dc757306b8e4011a65c7d75e9841d7c | - | - | - | - | 67 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 267 | 1732675348289 | 2024-11-27 10:42:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 5dc757306b8e4011a65c7d75e9841d7c | - | - | - | - | 67 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 267 | 1732675348296 | 2024-11-27 10:42:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | 5dc757306b8e4011a65c7d75e9841d7c | - | - | - | - | 74 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 267 | 1732675348301 | 2024-11-27 10:42:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ActorCell | receiveMessage | 5dc757306b8e4011a65c7d75e9841d7c | - | - | - | - | 79 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 267 | 1732675348301 | 2024-11-27 10:42:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ActorCell | receiveMessage | 5dc757306b8e4011a65c7d75e9841d7c | - | - | - | - | 79 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-42 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 268 | 1732675378104 | 2024-11-27 10:42:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 4c62a439fcb5401cb749d963becbc1ea | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675358077, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.8096, jvmUsedMemory=0.5439, jvmMaxMemory=3.5557, jvmMemoryUsage=0.153, diskUsed=214.6386, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1732675378107 | 2024-11-27 10:42:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 4c62a439fcb5401cb749d963becbc1ea | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675368077, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6782, jvmUsedMemory=0.5495, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1545, diskUsed=214.6397, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1732675378108 | 2024-11-27 10:42:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 4c62a439fcb5401cb749d963becbc1ea | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675378077, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7271, jvmUsedMemory=0.5576, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1568, diskUsed=214.6352, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 268 | 1732675378127 | 2024-11-27 10:42:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 4c62a439fcb5401cb749d963becbc1ea | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 268 | 1732675378128 | 2024-11-27 10:42:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 4c62a439fcb5401cb749d963becbc1ea | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-43 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 309 | 1732675408102 | 2024-11-27 10:43:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 69b713e715c14769a7bc37987952e08b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675388074, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4673, jvmUsedMemory=0.5698, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1602, diskUsed=214.6362, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 309 | 1732675408105 | 2024-11-27 10:43:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 69b713e715c14769a7bc37987952e08b | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675398074, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4688, jvmUsedMemory=0.5794, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1629, diskUsed=214.6337, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 309 | 1732675408107 | 2024-11-27 10:43:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 69b713e715c14769a7bc37987952e08b | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675408077, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2427, jvmUsedMemory=0.5855, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1647, diskUsed=214.634, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 309 | 1732675408128 | 2024-11-27 10:43:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 69b713e715c14769a7bc37987952e08b | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 309 | 1732675408129 | 2024-11-27 10:43:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 69b713e715c14769a7bc37987952e08b | - | - | - | - | 26 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-44 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 391 | 1732675438102 | 2024-11-27 10:43:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 5af471a874a14c7b8e38fd39e9115a6c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675418078, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2979, jvmUsedMemory=0.5941, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1671, diskUsed=214.636, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 391 | 1732675438103 | 2024-11-27 10:43:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 5af471a874a14c7b8e38fd39e9115a6c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675428081, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.332, jvmUsedMemory=0.6002, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1688, diskUsed=214.637, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 391 | 1732675438103 | 2024-11-27 10:43:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 5af471a874a14c7b8e38fd39e9115a6c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675438079, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2871, jvmUsedMemory=0.6068, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1707, diskUsed=214.637, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 354 | 1732675438119 | 2024-11-27 10:43:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | 38105b45e2fe48108ceff4154a4e9abd | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 354 | 1732675438120 | 2024-11-27 10:43:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 38105b45e2fe48108ceff4154a4e9abd | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-47 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 392 | 1732675468110 | 2024-11-27 10:44:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 3527ad6280f94cb08fcd05c33caf2ca7 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675448078, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0083, jvmUsedMemory=0.618, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1738, diskUsed=214.6384, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1732675468111 | 2024-11-27 10:44:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 3527ad6280f94cb08fcd05c33caf2ca7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675458077, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8525, jvmUsedMemory=0.6241, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1755, diskUsed=214.6365, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1732675468112 | 2024-11-27 10:44:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 3527ad6280f94cb08fcd05c33caf2ca7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675468076, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1631, jvmUsedMemory=0.6301, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1772, diskUsed=214.6375, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 392 | 1732675468127 | 2024-11-27 10:44:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 3527ad6280f94cb08fcd05c33caf2ca7 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 392 | 1732675468127 | 2024-11-27 10:44:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 3527ad6280f94cb08fcd05c33caf2ca7 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 431 | 1732675498101 | 2024-11-27 10:44:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 8bf6d74681974a4a874d5866fbbd709b | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675478076, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2178, jvmUsedMemory=0.6406, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1802, diskUsed=214.6395, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1732675498105 | 2024-11-27 10:44:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 8bf6d74681974a4a874d5866fbbd709b | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675488073, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2642, jvmUsedMemory=0.6474, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1821, diskUsed=214.6395, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 431 | 1732675498122 | 2024-11-27 10:44:58 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 8bf6d74681974a4a874d5866fbbd709b | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-52 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675498077, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3574, jvmUsedMemory=0.6542, jvmMaxMemory=3.5557, jvmMemoryUsage=0.184, diskUsed=214.6398, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 468 | 1732675498128 | 2024-11-27 10:44:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | e099432580044dfe99dee5a648f099e3 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 468 | 1732675498158 | 2024-11-27 10:44:58 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | e099432580044dfe99dee5a648f099e3 | - | - | - | - | 31 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 467 | 1732675528102 | 2024-11-27 10:45:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 5781361f12ef4a70ae1536f755b4caba | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675508075, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3823, jvmUsedMemory=0.7213, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2029, diskUsed=214.641, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1732675528110 | 2024-11-27 10:45:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 5781361f12ef4a70ae1536f755b4caba | - | - | - | - | 9 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675518075, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3169, jvmUsedMemory=0.7432, jvmMaxMemory=3.5557, jvmMemoryUsage=0.209, diskUsed=214.6423, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1732675528114 | 2024-11-27 10:45:28 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 5781361f12ef4a70ae1536f755b4caba | - | - | - | - | 12 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675528076, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1143, jvmUsedMemory=0.7488, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2106, diskUsed=214.675, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 467 | 1732675528139 | 2024-11-27 10:45:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 5781361f12ef4a70ae1536f755b4caba | - | - | - | - | 37 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 467 | 1732675528144 | 2024-11-27 10:45:28 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 5781361f12ef4a70ae1536f755b4caba | - | - | - | - | 42 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 521 | 1732675568491 | 2024-11-27 10:46:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | e8832741341c414ba6b0abf08de77dd9 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675538075, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7891, jvmUsedMemory=0.7636, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2147, diskUsed=214.6709, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 521 | 1732675568497 | 2024-11-27 10:46:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | e8832741341c414ba6b0abf08de77dd9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675552611, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0708, jvmUsedMemory=0.7816, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2198, diskUsed=214.6715, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 521 | 1732675568497 | 2024-11-27 10:46:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | e8832741341c414ba6b0abf08de77dd9 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675568466, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1714, jvmUsedMemory=0.7879, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2216, diskUsed=214.6727, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 521 | 1732675568515 | 2024-11-27 10:46:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | e8832741341c414ba6b0abf08de77dd9 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 521 | 1732675568515 | 2024-11-27 10:46:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | e8832741341c414ba6b0abf08de77dd9 | - | - | - | - | 20 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-57 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 557 | 1732675598102 | 2024-11-27 10:46:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 5ab30d8bd893442583163a1ce548f3a0 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675578078, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7632, jvmUsedMemory=0.8013, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2254, diskUsed=214.6719, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 557 | 1732675598105 | 2024-11-27 10:46:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 5ab30d8bd893442583163a1ce548f3a0 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675588077, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3379, jvmUsedMemory=0.8069, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2269, diskUsed=214.6729, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 557 | 1732675598107 | 2024-11-27 10:46:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 5ab30d8bd893442583163a1ce548f3a0 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675598077, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2119, jvmUsedMemory=0.8145, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2291, diskUsed=214.6749, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 557 | 1732675598125 | 2024-11-27 10:46:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 5ab30d8bd893442583163a1ce548f3a0 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 557 | 1732675598126 | 2024-11-27 10:46:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 5ab30d8bd893442583163a1ce548f3a0 | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-67 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 597 | 1732675628101 | 2024-11-27 10:47:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | c8a75b4020044ec98db90cc165ea6883 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675608073, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2593, jvmUsedMemory=0.8226, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2314, diskUsed=214.6761, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 597 | 1732675628102 | 2024-11-27 10:47:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | c8a75b4020044ec98db90cc165ea6883 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675618074, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3599, jvmUsedMemory=0.8287, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2331, diskUsed=214.6762, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 597 | 1732675628104 | 2024-11-27 10:47:08 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | c8a75b4020044ec98db90cc165ea6883 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675628075, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0376, jvmUsedMemory=0.838, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2357, diskUsed=214.6771, diskTotal=460.4317, diskUsage=0.4663, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 597 | 1732675628130 | 2024-11-27 10:47:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | c8a75b4020044ec98db90cc165ea6883 | - | - | - | - | 29 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 597 | 1732675628130 | 2024-11-27 10:47:08 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | c8a75b4020044ec98db90cc165ea6883 | - | - | - | - | 29 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 634 | 1732675658121 | 2024-11-27 10:47:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | a3bc0e4cde50490dadb1ef58eef2391c | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675638078, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9575, jvmUsedMemory=0.8486, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2387, diskUsed=214.6773, diskTotal=460.4317, diskUsage=0.4663, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 634 | 1732675658128 | 2024-11-27 10:47:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | a3bc0e4cde50490dadb1ef58eef2391c | - | - | - | - | 10 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675649201, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7422, jvmUsedMemory=0.8527, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2398, diskUsed=214.6801, diskTotal=460.4317, diskUsage=0.4663, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 634 | 1732675658132 | 2024-11-27 10:47:38 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | a3bc0e4cde50490dadb1ef58eef2391c | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675658076, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.1499, jvmUsedMemory=0.8598, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2418, diskUsed=214.6805, diskTotal=460.4317, diskUsage=0.4663, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 634 | 1732675658155 | 2024-11-27 10:47:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | a3bc0e4cde50490dadb1ef58eef2391c | - | - | - | - | 34 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 634 | 1732675658155 | 2024-11-27 10:47:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | a3bc0e4cde50490dadb1ef58eef2391c | - | - | - | - | 34 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1732675658564 | 2024-11-27 10:47:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | b79b883f7dcb47e9813e26ca59ba8793 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1732675658565 | 2024-11-27 10:47:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | d169a42a20ec4ac896fcd07b42fd8ac4 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1732675658565 | 2024-11-27 10:47:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | d169a42a20ec4ac896fcd07b42fd8ac4 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1732675658571 | 2024-11-27 10:47:38 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | b79b883f7dcb47e9813e26ca59ba8793 | - | - | - | - | 7 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 634 | 1732675658628 | 2024-11-27 10:47:38 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | a3bc0e4cde50490dadb1ef58eef2391c | - | - | - | - | 507 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 127 | 1732675695137 | 2024-11-27 10:48:15 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 0ebebd886393454a98056749fce0944f | - | - | - | - | 185 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 127 | 1732675695138 | 2024-11-27 10:48:15 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | 0ebebd886393454a98056749fce0944f | - | - | - | - | 186 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 234 | 1732675697260 | 2024-11-27 10:48:17 | v2/SqlExecuteTimeCountInterceptor/intercept | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | SqlExecuteTimeCountInterceptor | intercept | 9228a83dd723459bb0fa73fa775eea73 | - | - | - | - | 1292 | 0 | - | - | - | - | task-1 c.t.c.j.d.SqlExecuteTimeCountInterceptor [慢sql] 执行 SQL：[ SELECT  id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,is_update,is_group,host_type  FROM  team_host  WHERE  (team_type  =  ?) ]执行耗时[ 1124 ms]

warning | 287 | 1732675715945 | 2024-11-27 10:48:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 97418dbf42224d01a15165bea5f99650 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675695796, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.2378, jvmUsedMemory=0.4127, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1161, diskUsed=214.6785, diskTotal=460.4317, diskUsage=0.4663, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 287 | 1732675715950 | 2024-11-27 10:48:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 97418dbf42224d01a15165bea5f99650 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675705796, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.7969, jvmUsedMemory=0.639, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1797, diskUsed=214.6757, diskTotal=460.4317, diskUsage=0.4662, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 287 | 1732675715951 | 2024-11-27 10:48:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 97418dbf42224d01a15165bea5f99650 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-49 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675715799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=5.1392, jvmUsedMemory=0.652, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1834, diskUsed=215.6772, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=8)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 127 | 1732675716009 | 2024-11-27 10:48:36 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 0ebebd886393454a98056749fce0944f | - | - | - | - | 21057 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1732675716009 | 2024-11-27 10:48:36 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 0ebebd886393454a98056749fce0944f | - | - | - | - | 21057 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], control stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 127 | 1732675716013 | 2024-11-27 10:48:36 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.11 | ************** | - | 2 | ActorCell | receiveMessage | 0ebebd886393454a98056749fce0944f | - | - | - | - | 21062 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1732675716018 | 2024-11-27 10:48:36 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.13 | ************** | - | 2 | ActorCell | receiveMessage | 0ebebd886393454a98056749fce0944f | - | - | - | - | 21066 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Quarantine of [akka://oms-server@*************:10086] ignored because unknown UID. Reason: Outbound control stream restarted. akka.stream.StreamTcpException: The connection has been aborted

warning | 127 | 1732675716018 | 2024-11-27 10:48:36 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.15 | ************** | - | 2 | ActorCell | receiveMessage | 0ebebd886393454a98056749fce0944f | - | - | - | - | 21066 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-4 a.r.a.Association Outbound control stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 286 | 1732675745828 | 2024-11-27 10:49:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 0b2b863be3e14711b9b9131185edaa4a | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675725799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.5024, jvmUsedMemory=0.6726, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1892, diskUsed=215.6777, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 286 | 1732675745833 | 2024-11-27 10:49:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 0b2b863be3e14711b9b9131185edaa4a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675735800, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.4917, jvmUsedMemory=0.6796, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1911, diskUsed=215.6777, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 286 | 1732675745834 | 2024-11-27 10:49:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 0b2b863be3e14711b9b9131185edaa4a | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675745800, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=4.0215, jvmUsedMemory=0.6886, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1937, diskUsed=215.678, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 286 | 1732675745848 | 2024-11-27 10:49:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 0b2b863be3e14711b9b9131185edaa4a | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 286 | 1732675745849 | 2024-11-27 10:49:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 0b2b863be3e14711b9b9131185edaa4a | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-50 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 333 | 1732675775826 | 2024-11-27 10:49:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 7cb5da7b6ae34df195d68190b3f22a80 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675755798, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8511, jvmUsedMemory=0.7012, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1972, diskUsed=215.6772, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 333 | 1732675775827 | 2024-11-27 10:49:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 7cb5da7b6ae34df195d68190b3f22a80 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675765799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3389, jvmUsedMemory=0.7082, jvmMaxMemory=3.5557, jvmMemoryUsage=0.1992, diskUsed=215.6772, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 333 | 1732675775828 | 2024-11-27 10:49:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 7cb5da7b6ae34df195d68190b3f22a80 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675775799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0527, jvmUsedMemory=0.719, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2022, diskUsed=215.6781, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 333 | 1732675775840 | 2024-11-27 10:49:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 7cb5da7b6ae34df195d68190b3f22a80 | - | - | - | - | 13 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 333 | 1732675775840 | 2024-11-27 10:49:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 7cb5da7b6ae34df195d68190b3f22a80 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-51 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 422 | 1732675805844 | 2024-11-27 10:50:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 72ddf5ddffa344e3bcd382932a85695b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675785799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6631, jvmUsedMemory=0.7286, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2049, diskUsed=215.6781, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 422 | 1732675805845 | 2024-11-27 10:50:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 72ddf5ddffa344e3bcd382932a85695b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675795800, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4873, jvmUsedMemory=0.7351, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2067, diskUsed=215.6783, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 422 | 1732675805846 | 2024-11-27 10:50:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 72ddf5ddffa344e3bcd382932a85695b | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-56 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675805819, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1782, jvmUsedMemory=0.7426, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2088, diskUsed=215.6796, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 382 | 1732675805859 | 2024-11-27 10:50:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | 70a19f90855640f0a193b8d04cbed61b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 382 | 1732675805859 | 2024-11-27 10:50:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | 70a19f90855640f0a193b8d04cbed61b | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-54 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 421 | 1732675835828 | 2024-11-27 10:50:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 764b992d86a14561bdfeaff18e38d527 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675815800, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8428, jvmUsedMemory=0.7537, jvmMaxMemory=3.5557, jvmMemoryUsage=0.212, diskUsed=215.6792, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 421 | 1732675835832 | 2024-11-27 10:50:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 764b992d86a14561bdfeaff18e38d527 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675825797, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7798, jvmUsedMemory=0.7607, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2139, diskUsed=215.6802, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 421 | 1732675835833 | 2024-11-27 10:50:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 764b992d86a14561bdfeaff18e38d527 | - | - | - | - | 4 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675835795, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6533, jvmUsedMemory=0.7682, jvmMaxMemory=3.5557, jvmMemoryUsage=0.216, diskUsed=215.6795, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 421 | 1732675835850 | 2024-11-27 10:50:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 764b992d86a14561bdfeaff18e38d527 | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 421 | 1732675835852 | 2024-11-27 10:50:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 764b992d86a14561bdfeaff18e38d527 | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-55 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 463 | 1732675865825 | 2024-11-27 10:51:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 431783f291ac4dfebbee69550b661d80 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675845799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3984, jvmUsedMemory=0.7776, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2187, diskUsed=215.6781, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 463 | 1732675865826 | 2024-11-27 10:51:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 431783f291ac4dfebbee69550b661d80 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675855798, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.564, jvmUsedMemory=0.7846, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2207, diskUsed=215.6782, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 463 | 1732675865827 | 2024-11-27 10:51:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 431783f291ac4dfebbee69550b661d80 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675865799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.3965, jvmUsedMemory=0.791, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2225, diskUsed=215.6806, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 463 | 1732675865836 | 2024-11-27 10:51:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 431783f291ac4dfebbee69550b661d80 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 463 | 1732675865837 | 2024-11-27 10:51:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 431783f291ac4dfebbee69550b661d80 | - | - | - | - | 11 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-58 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 502 | 1732675895827 | 2024-11-27 10:51:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 339908e295e44eb697caf85bbaaac5c2 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675875799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9756, jvmUsedMemory=0.7986, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2246, diskUsed=215.6807, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 502 | 1732675895829 | 2024-11-27 10:51:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 339908e295e44eb697caf85bbaaac5c2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675885797, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4595, jvmUsedMemory=0.8051, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2264, diskUsed=215.6807, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 502 | 1732675895829 | 2024-11-27 10:51:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 339908e295e44eb697caf85bbaaac5c2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675895799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6157, jvmUsedMemory=0.8116, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2283, diskUsed=215.6817, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 502 | 1732675895844 | 2024-11-27 10:51:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 339908e295e44eb697caf85bbaaac5c2 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 502 | 1732675895845 | 2024-11-27 10:51:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 339908e295e44eb697caf85bbaaac5c2 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-61 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 540 | 1732675925827 | 2024-11-27 10:52:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | d5624e798b4f4beea5ae0f00f5a8b249 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675905799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5879, jvmUsedMemory=0.8202, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2307, diskUsed=215.6822, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 540 | 1732675925829 | 2024-11-27 10:52:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | d5624e798b4f4beea5ae0f00f5a8b249 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675915798, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6509, jvmUsedMemory=0.8267, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2325, diskUsed=215.6822, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 540 | 1732675925830 | 2024-11-27 10:52:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | d5624e798b4f4beea5ae0f00f5a8b249 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675925798, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5503, jvmUsedMemory=0.8345, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2347, diskUsed=215.6824, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 540 | 1732675925857 | 2024-11-27 10:52:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | d5624e798b4f4beea5ae0f00f5a8b249 | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 540 | 1732675925858 | 2024-11-27 10:52:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | d5624e798b4f4beea5ae0f00f5a8b249 | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-63 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 578 | 1732675955827 | 2024-11-27 10:52:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | f4fd6ac1ada84d87b1d5bb55b903a72d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675935796, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.7661, jvmUsedMemory=0.8426, jvmMaxMemory=3.5557, jvmMemoryUsage=0.237, diskUsed=215.6825, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 578 | 1732675955829 | 2024-11-27 10:52:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | f4fd6ac1ada84d87b1d5bb55b903a72d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675945795, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.728, jvmUsedMemory=0.8476, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2384, diskUsed=215.6825, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 578 | 1732675955829 | 2024-11-27 10:52:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | f4fd6ac1ada84d87b1d5bb55b903a72d | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675955798, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3818, jvmUsedMemory=0.8546, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2404, diskUsed=215.6854, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 578 | 1732675955846 | 2024-11-27 10:52:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | f4fd6ac1ada84d87b1d5bb55b903a72d | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 578 | 1732675955847 | 2024-11-27 10:52:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | f4fd6ac1ada84d87b1d5bb55b903a72d | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-65 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 579 | 1732675985825 | 2024-11-27 10:53:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | d0fa03dd531147dfaf5f6187a6a99d6d | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675965799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0889, jvmUsedMemory=0.8616, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2423, diskUsed=215.6854, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 579 | 1732675985829 | 2024-11-27 10:53:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | d0fa03dd531147dfaf5f6187a6a99d6d | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675975797, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4624, jvmUsedMemory=0.8672, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2439, diskUsed=215.6854, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 579 | 1732675985829 | 2024-11-27 10:53:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | d0fa03dd531147dfaf5f6187a6a99d6d | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675985798, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1636, jvmUsedMemory=0.8707, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2449, diskUsed=215.6854, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 579 | 1732675985841 | 2024-11-27 10:53:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | d0fa03dd531147dfaf5f6187a6a99d6d | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 579 | 1732675985841 | 2024-11-27 10:53:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | d0fa03dd531147dfaf5f6187a6a99d6d | - | - | - | - | 15 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-66 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 644 | 1732676015826 | 2024-11-27 10:53:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 69080ad2195f4e0fbcdbbab31e3aadfb | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732675995794, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4263, jvmUsedMemory=0.8831, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2484, diskUsed=215.6854, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 644 | 1732676015828 | 2024-11-27 10:53:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 69080ad2195f4e0fbcdbbab31e3aadfb | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676005794, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3667, jvmUsedMemory=0.8856, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2491, diskUsed=215.6854, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 644 | 1732676015829 | 2024-11-27 10:53:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 69080ad2195f4e0fbcdbbab31e3aadfb | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676015794, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.457, jvmUsedMemory=0.8946, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2516, diskUsed=215.6902, diskTotal=460.4317, diskUsage=0.4685, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 644 | 1732676015844 | 2024-11-27 10:53:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 69080ad2195f4e0fbcdbbab31e3aadfb | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 644 | 1732676015844 | 2024-11-27 10:53:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 69080ad2195f4e0fbcdbbab31e3aadfb | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-70 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 645 | 1732676045829 | 2024-11-27 10:54:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 313b0d1899d043e89b7c6d9caff5b188 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676025796, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.46, jvmUsedMemory=0.9016, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2536, diskUsed=215.6903, diskTotal=460.4317, diskUsage=0.4685, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 645 | 1732676045831 | 2024-11-27 10:54:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 313b0d1899d043e89b7c6d9caff5b188 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676035796, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3022, jvmUsedMemory=0.9076, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2553, diskUsed=215.6903, diskTotal=460.4317, diskUsage=0.4685, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 645 | 1732676045831 | 2024-11-27 10:54:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 313b0d1899d043e89b7c6d9caff5b188 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676045794, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.5757, jvmUsedMemory=0.9136, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2569, diskUsed=215.6905, diskTotal=460.4317, diskUsage=0.4685, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 645 | 1732676045846 | 2024-11-27 10:54:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 313b0d1899d043e89b7c6d9caff5b188 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 645 | 1732676045846 | 2024-11-27 10:54:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 313b0d1899d043e89b7c6d9caff5b188 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-71 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 716 | 1732676075836 | 2024-11-27 10:54:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 61bfea515c0b4c48b225fd7a813db92e | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676055799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2593, jvmUsedMemory=0.9217, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2592, diskUsed=215.6906, diskTotal=460.4317, diskUsage=0.4685, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 716 | 1732676075839 | 2024-11-27 10:54:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 61bfea515c0b4c48b225fd7a813db92e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676065798, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.1323, jvmUsedMemory=0.9291, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2613, diskUsed=215.6916, diskTotal=460.4317, diskUsage=0.4685, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 716 | 1732676075839 | 2024-11-27 10:54:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 61bfea515c0b4c48b225fd7a813db92e | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676075798, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9517, jvmUsedMemory=0.9356, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2631, diskUsed=215.6926, diskTotal=460.4317, diskUsage=0.4685, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 716 | 1732676075860 | 2024-11-27 10:54:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 61bfea515c0b4c48b225fd7a813db92e | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 716 | 1732676075860 | 2024-11-27 10:54:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 61bfea515c0b4c48b225fd7a813db92e | - | - | - | - | 23 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-73 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 717 | 1732676105827 | 2024-11-27 10:55:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 2fb5f3e09efa4ec0845c97084ac0bf05 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676085794, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.731, jvmUsedMemory=0.9436, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2654, diskUsed=215.688, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 717 | 1732676105829 | 2024-11-27 10:55:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 2fb5f3e09efa4ec0845c97084ac0bf05 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676095795, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6855, jvmUsedMemory=0.9491, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2669, diskUsed=215.6884, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 717 | 1732676105830 | 2024-11-27 10:55:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 2fb5f3e09efa4ec0845c97084ac0bf05 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676105797, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8677, jvmUsedMemory=0.9551, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2686, diskUsed=215.6884, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 717 | 1732676105853 | 2024-11-27 10:55:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 2fb5f3e09efa4ec0845c97084ac0bf05 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 717 | 1732676105853 | 2024-11-27 10:55:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 2fb5f3e09efa4ec0845c97084ac0bf05 | - | - | - | - | 25 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-74 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 789 | 1732676135860 | 2024-11-27 10:55:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 4197fcfae4814ceaa1d91141a7d2d7f3 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676115799, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.3901, jvmUsedMemory=0.9628, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2708, diskUsed=215.6853, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 789 | 1732676135861 | 2024-11-27 10:55:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 4197fcfae4814ceaa1d91141a7d2d7f3 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676125802, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4165, jvmUsedMemory=0.9683, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2723, diskUsed=215.6813, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 789 | 1732676135862 | 2024-11-27 10:55:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 4197fcfae4814ceaa1d91141a7d2d7f3 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676135821, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0444, jvmUsedMemory=0.9766, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2747, diskUsed=215.6838, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 789 | 1732676135891 | 2024-11-27 10:55:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 4197fcfae4814ceaa1d91141a7d2d7f3 | - | - | - | - | 32 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 789 | 1732676135892 | 2024-11-27 10:55:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 4197fcfae4814ceaa1d91141a7d2d7f3 | - | - | - | - | 32 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-78 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 859 | 1732676165875 | 2024-11-27 10:56:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 1a716a732b6e4393a641c1029ab0c460 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676145830, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7295, jvmUsedMemory=0.9831, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2765, diskUsed=215.6864, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 859 | 1732676165877 | 2024-11-27 10:56:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 1a716a732b6e4393a641c1029ab0c460 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676155839, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5435, jvmUsedMemory=0.9892, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2782, diskUsed=215.6862, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 859 | 1732676165877 | 2024-11-27 10:56:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 1a716a732b6e4393a641c1029ab0c460 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676165843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6802, jvmUsedMemory=0.9962, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2802, diskUsed=215.6832, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 859 | 1732676165889 | 2024-11-27 10:56:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 1a716a732b6e4393a641c1029ab0c460 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 859 | 1732676165889 | 2024-11-27 10:56:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 1a716a732b6e4393a641c1029ab0c460 | - | - | - | - | 14 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-83 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 895 | 1732676195873 | 2024-11-27 10:56:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 6b2ddf298cd04d64a6f2bf111010d2c5 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676175843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8154, jvmUsedMemory=1.0037, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2823, diskUsed=215.6789, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 895 | 1732676195875 | 2024-11-27 10:56:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 6b2ddf298cd04d64a6f2bf111010d2c5 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676185842, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7632, jvmUsedMemory=1.0102, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2841, diskUsed=215.6801, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 895 | 1732676195875 | 2024-11-27 10:56:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 6b2ddf298cd04d64a6f2bf111010d2c5 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676195845, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.645, jvmUsedMemory=1.0162, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2858, diskUsed=215.6811, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 895 | 1732676195901 | 2024-11-27 10:56:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 6b2ddf298cd04d64a6f2bf111010d2c5 | - | - | - | - | 28 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 895 | 1732676195901 | 2024-11-27 10:56:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 6b2ddf298cd04d64a6f2bf111010d2c5 | - | - | - | - | 29 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-85 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 932 | 1732676225885 | 2024-11-27 10:57:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 6d578f3d87e84e88823b8844bdb10163 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676205843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6992, jvmUsedMemory=1.0259, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2885, diskUsed=215.683, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 932 | 1732676225888 | 2024-11-27 10:57:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 6d578f3d87e84e88823b8844bdb10163 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676215843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8315, jvmUsedMemory=1.0341, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2908, diskUsed=215.6831, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 932 | 1732676225888 | 2024-11-27 10:57:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 6d578f3d87e84e88823b8844bdb10163 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676225845, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4399, jvmUsedMemory=1.0409, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2927, diskUsed=215.6845, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 932 | 1732676225916 | 2024-11-27 10:57:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 6d578f3d87e84e88823b8844bdb10163 | - | - | - | - | 32 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 932 | 1732676225918 | 2024-11-27 10:57:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 6d578f3d87e84e88823b8844bdb10163 | - | - | - | - | 32 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-87 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 933 | 1732676255894 | 2024-11-27 10:57:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | bcb8962cb84d4462a16e0361ea75cf93 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676235843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.144, jvmUsedMemory=1.0494, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2951, diskUsed=215.684, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 933 | 1732676255896 | 2024-11-27 10:57:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | bcb8962cb84d4462a16e0361ea75cf93 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676245844, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0415, jvmUsedMemory=1.0558, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2969, diskUsed=215.6843, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=10)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 933 | 1732676255897 | 2024-11-27 10:57:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | bcb8962cb84d4462a16e0361ea75cf93 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676255841, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8096, jvmUsedMemory=1.0623, jvmMaxMemory=3.5557, jvmMemoryUsage=0.2988, diskUsed=215.6853, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 933 | 1732676255912 | 2024-11-27 10:57:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | bcb8962cb84d4462a16e0361ea75cf93 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 933 | 1732676255913 | 2024-11-27 10:57:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | bcb8962cb84d4462a16e0361ea75cf93 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-88 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 967 | 1732676285874 | 2024-11-27 10:58:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 369966f781a94752ba812288e1180a23 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-89 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676265846, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.8867, jvmUsedMemory=1.0698, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3009, diskUsed=215.6855, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 967 | 1732676285877 | 2024-11-27 10:58:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 369966f781a94752ba812288e1180a23 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-89 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676275843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4424, jvmUsedMemory=1.0762, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3027, diskUsed=215.687, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 967 | 1732676285877 | 2024-11-27 10:58:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 369966f781a94752ba812288e1180a23 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-89 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676285841, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0664, jvmUsedMemory=1.0827, jvmMaxMemory=3.5557, jvmMemoryUsage=0.3045, diskUsed=215.6882, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=9)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 967 | 1732676285893 | 2024-11-27 10:58:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 369966f781a94752ba812288e1180a23 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-89 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 967 | 1732676285894 | 2024-11-27 10:58:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 369966f781a94752ba812288e1180a23 | - | - | - | - | 19 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-89 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1003 | 1732676315863 | 2024-11-27 10:58:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | c594355ac8214b43ab145c8bb9289dc7 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-92 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676295842, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.9629, jvmUsedMemory=0.1536, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0432, diskUsed=215.6873, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1003 | 1732676315866 | 2024-11-27 10:58:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | c594355ac8214b43ab145c8bb9289dc7 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-92 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676305841, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6606, jvmUsedMemory=0.1666, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0469, diskUsed=215.6906, diskTotal=460.4317, diskUsage=0.4685, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1003 | 1732676315866 | 2024-11-27 10:58:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | c594355ac8214b43ab145c8bb9289dc7 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-92 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676315843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.0518, jvmUsedMemory=0.1706, jvmMaxMemory=3.5557, jvmMemoryUsage=0.048, diskUsed=215.6905, diskTotal=460.4317, diskUsage=0.4685, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1003 | 1732676315881 | 2024-11-27 10:58:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | c594355ac8214b43ab145c8bb9289dc7 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-92 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1003 | 1732676315881 | 2024-11-27 10:58:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | c594355ac8214b43ab145c8bb9289dc7 | - | - | - | - | 18 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-92 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1004 | 1732676345875 | 2024-11-27 10:59:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | ee9434647aad48f782234d7316c9642c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676325844, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.3765, jvmUsedMemory=0.1749, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0492, diskUsed=215.6868, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1004 | 1732676345876 | 2024-11-27 10:59:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | ee9434647aad48f782234d7316c9642c | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676335841, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.4722, jvmUsedMemory=0.1779, jvmMaxMemory=3.5557, jvmMemoryUsage=0.05, diskUsed=215.6871, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1004 | 1732676345876 | 2024-11-27 10:59:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | ee9434647aad48f782234d7316c9642c | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676345844, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.938, jvmUsedMemory=0.1902, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0535, diskUsed=215.687, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1004 | 1732676345892 | 2024-11-27 10:59:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | ee9434647aad48f782234d7316c9642c | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1004 | 1732676345892 | 2024-11-27 10:59:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | ee9434647aad48f782234d7316c9642c | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-93 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1071 | 1732676375865 | 2024-11-27 10:59:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | aa15991ee38945b6b2c06ac19ea1579d | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676355843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.6333, jvmUsedMemory=0.1948, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0548, diskUsed=215.6857, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1071 | 1732676375867 | 2024-11-27 10:59:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | aa15991ee38945b6b2c06ac19ea1579d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676365847, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4683, jvmUsedMemory=0.1976, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0556, diskUsed=215.6858, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1071 | 1732676375867 | 2024-11-27 10:59:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | aa15991ee38945b6b2c06ac19ea1579d | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676375842, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.4824, jvmUsedMemory=0.2016, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0567, diskUsed=215.6868, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1071 | 1732676375887 | 2024-11-27 10:59:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | aa15991ee38945b6b2c06ac19ea1579d | - | - | - | - | 21 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1071 | 1732676375888 | 2024-11-27 10:59:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | aa15991ee38945b6b2c06ac19ea1579d | - | - | - | - | 22 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-96 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1103 | 1732676405865 | 2024-11-27 11:00:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 16a3d3e0a8ab4acab1e19ec6a48bdae4 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676385844, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.2603, jvmUsedMemory=0.2065, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0581, diskUsed=215.6873, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1103 | 1732676405867 | 2024-11-27 11:00:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 16a3d3e0a8ab4acab1e19ec6a48bdae4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676395845, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9858, jvmUsedMemory=0.2098, jvmMaxMemory=3.5557, jvmMemoryUsage=0.059, diskUsed=215.6873, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1103 | 1732676405867 | 2024-11-27 11:00:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 16a3d3e0a8ab4acab1e19ec6a48bdae4 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676405848, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9746, jvmUsedMemory=0.2148, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0604, diskUsed=215.6873, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1103 | 1732676405882 | 2024-11-27 11:00:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 16a3d3e0a8ab4acab1e19ec6a48bdae4 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1103 | 1732676405882 | 2024-11-27 11:00:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 16a3d3e0a8ab4acab1e19ec6a48bdae4 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-97 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1140 | 1732676435863 | 2024-11-27 11:00:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 5550ffbb1df24152bf44437e562bf2a2 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676415845, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6709, jvmUsedMemory=0.22, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0619, diskUsed=215.6809, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1140 | 1732676435865 | 2024-11-27 11:00:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 5550ffbb1df24152bf44437e562bf2a2 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676425847, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.5669, jvmUsedMemory=0.223, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0627, diskUsed=215.6809, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1140 | 1732676435866 | 2024-11-27 11:00:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 5550ffbb1df24152bf44437e562bf2a2 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676435845, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=2.0337, jvmUsedMemory=0.2275, jvmMaxMemory=3.5557, jvmMemoryUsage=0.064, diskUsed=215.6791, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1140 | 1732676435880 | 2024-11-27 11:00:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 5550ffbb1df24152bf44437e562bf2a2 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1140 | 1732676435880 | 2024-11-27 11:00:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 5550ffbb1df24152bf44437e562bf2a2 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-102 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1173 | 1732676465866 | 2024-11-27 11:01:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 780ba4bdbd96475f9a6afdeacd1aa593 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-103 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676445843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7207, jvmUsedMemory=0.2321, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0653, diskUsed=215.6792, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1173 | 1732676465869 | 2024-11-27 11:01:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 780ba4bdbd96475f9a6afdeacd1aa593 | - | - | - | - | 5 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-103 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676455847, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.603, jvmUsedMemory=0.237, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0667, diskUsed=215.6793, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1173 | 1732676465873 | 2024-11-27 11:01:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 780ba4bdbd96475f9a6afdeacd1aa593 | - | - | - | - | 6 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-103 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676465847, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.9038, jvmUsedMemory=0.241, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0678, diskUsed=215.6793, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1173 | 1732676465891 | 2024-11-27 11:01:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 780ba4bdbd96475f9a6afdeacd1aa593 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-103 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1173 | 1732676465892 | 2024-11-27 11:01:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 780ba4bdbd96475f9a6afdeacd1aa593 | - | - | - | - | 24 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-103 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1138 | 1732676495875 | 2024-11-27 11:01:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 1a33b237bf614be7be5d4d562b37a634 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-100 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676475846, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.8315, jvmUsedMemory=0.2462, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0692, diskUsed=215.6787, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1138 | 1732676495876 | 2024-11-27 11:01:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 1a33b237bf614be7be5d4d562b37a634 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-100 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676485848, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.7095, jvmUsedMemory=0.2489, jvmMaxMemory=3.5557, jvmMemoryUsage=0.07, diskUsed=215.6799, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1138 | 1732676495877 | 2024-11-27 11:01:35 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 1a33b237bf614be7be5d4d562b37a634 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-100 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676495843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.6733, jvmUsedMemory=0.2527, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0711, diskUsed=215.6812, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=12)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1138 | 1732676495891 | 2024-11-27 11:01:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 1a33b237bf614be7be5d4d562b37a634 | - | - | - | - | 16 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-100 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1138 | 1732676495891 | 2024-11-27 11:01:35 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 1a33b237bf614be7be5d4d562b37a634 | - | - | - | - | 17 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-100 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 1241 | 1732676525876 | 2024-11-27 11:02:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | 429e7ac04bfe417ea979b1e454ac0350 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676505843, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.4893, jvmUsedMemory=0.2584, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0727, diskUsed=215.6814, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1241 | 1732676525879 | 2024-11-27 11:02:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Actor | aroundReceive | 429e7ac04bfe417ea979b1e454ac0350 | - | - | - | - | 2 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676515848, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=1.2598, jvmUsedMemory=0.2613, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0735, diskUsed=215.6817, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=13)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1241 | 1732676525880 | 2024-11-27 11:02:05 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | Actor | aroundReceive | 429e7ac04bfe417ea979b1e454ac0350 | - | - | - | - | 3 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676525847, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.5801, jvmUsedMemory=0.2652, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0746, diskUsed=215.6812, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 1241 | 1732676525907 | 2024-11-27 11:02:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | 429e7ac04bfe417ea979b1e454ac0350 | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 a.s.Materializer [outbound connection to [akka://oms-server@*************:10086], message stream] Upstream failed, cause: StreamTcpException: The connection has been aborted

warning | 1241 | 1732676525907 | 2024-11-27 11:02:05 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.9 | ************** | - | 2 | ActorCell | receiveMessage | 429e7ac04bfe417ea979b1e454ac0350 | - | - | - | - | 30 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-107 a.r.a.Association Outbound message stream to [akka://oms-server@*************:10086] failed. Restarting it. akka.stream.StreamTcpException: The connection has been aborted

warning | 40 | 1732676540905 | 2024-11-27 11:02:20 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | d62022599243482da736ba6dcc2a4263 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1732676540905 | 2024-11-27 11:02:20 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 3668c424bcbe4be490f2a99181ff1182 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 44 | 1732676540906 | 2024-11-27 11:02:20 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 3668c424bcbe4be490f2a99181ff1182 | - | - | - | - | 1 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1732676540909 | 2024-11-27 11:02:20 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | d62022599243482da736ba6dcc2a4263 | - | - | - | - | 4 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 1242 | 1732676540968 | 2024-11-27 11:02:20 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | ActorCell | receiveMessage | 4bff912ce49843abbcf7c6df653b2fe7 | - | - | - | - | 1 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-108 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

warning | 1294 | 1732676540994 | 2024-11-27 11:02:20 | v2/Actor/aroundReceive | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Actor | aroundReceive | cd00067fb9144bc6ac6cfb4a72287c91 | - | - | - | - | 0 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-111 t.p.r.a.AkkaTroubleshootingActor [PowerJob-AKKA] receive DeadLetter: DeadLetter(WorkerHeartbeat(workerAddress=**************:27777, appName=mp-gonghui, appId=11, heartbeatTime=1732676535847, containerInfos=[], version=4.3.9, protocol=AKKA, tag=null, client=KingPenguin, extra=null, isOverload=false, lightTaskTrackerNum=0, heavyTaskTrackerNum=0, systemMetrics=SystemMetrics(cpuProcessors=8, cpuLoad=3.2627, jvmUsedMemory=0.2693, jvmMaxMemory=3.5557, jvmMemoryUsage=0.0757, diskUsed=215.6803, diskTotal=460.4317, diskUsage=0.4684, extra=null, score=11)),Actor[akka://oms/deadLetters],Actor[akka://oms/deadLetters])

warning | 127 | 1732676570157 | 2024-11-27 11:02:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | ActorCell | receiveMessage | b7392641e080482d830defcf0ec1c0b6 | - | - | - | - | 101 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Using the 'remote' ActorRefProvider directly, which is a low-level layer. For most use cases, the 'cluster' abstraction on top of remoting is more suitable instead.

warning | 127 | 1732676570158 | 2024-11-27 11:02:50 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.5 | ************** | - | 2 | ActorCell | receiveMessage | b7392641e080482d830defcf0ec1c0b6 | - | - | - | - | 101 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.r.RemoteActorRefProvider Akka Cluster not in use - Using Akka Cluster is recommended if you need remote watch and deploy.

warning | 44 | 1732676575665 | 2024-11-27 11:02:55 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | 8775ff0761ec4ccaa0c5eeec394ad38e | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Start destroying Publisher

warning | 40 | 1732676575665 | 2024-11-27 11:02:55 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.1 | ************** | - | 2 | Thread | run | bb159c515d9e4a98bb13e7d997d751f5 | - | - | - | - | 0 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Start destroying common HttpClient

warning | 44 | 1732676575666 | 2024-11-27 11:02:55 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | 8775ff0761ec4ccaa0c5eeec394ad38e | - | - | - | - | 0 | 0 | - | - | - | - | Thread-10 c.a.n.c.n.NotifyCenter [NotifyCenter] Destruction of the end

warning | 40 | 1732676575668 | 2024-11-27 11:02:55 | v2/Thread/run | online | - | 1 | - | - | cli | j47 | 0.3 | ************** | - | 2 | Thread | run | bb159c515d9e4a98bb13e7d997d751f5 | - | - | - | - | 2 | 0 | - | - | - | - | Thread-7 c.a.n.c.h.HttpClientBeanHolder [HttpClientBeanHolder] Destruction of the end

warning | 127 | 1732676575698 | 2024-11-27 11:02:55 | v2/ActorCell/receiveMessage | online | - | 1 | - | - | cli | j47 | 0.7 | ************** | - | 2 | ActorCell | receiveMessage | b7392641e080482d830defcf0ec1c0b6 | - | - | - | - | 5642 | 0 | - | - | - | - | oms-akka.actor.default-dispatcher-5 a.a.CoordinatedShutdown Could not addJvmShutdownHook, due to: Shutdown in progress

