variables:
  # 项目部署唯一标识(需要变更,可复制原来的.gitlab-ci.yml中的内容)
  APP_NAME: j47
  # 项目sonar名称(需要变更,可复制原来的.gitlab-ci.yml中的内容)
  SONAR_NAME: j47
  # 打包路径(需要变更,可复制原来的.gitlab-ci.yml中的内容)
  TARGET_PATH: tq-newgonghui-server/target

include:
  - project: 'devops/argo-ci'
    ref: master
    file: 'templates/.gitlab-********************maven.yml'
  - project: 'devops/argo-ci'
    ref: master
    file: 'templates/java/gitlab-ci-template-java.yml'
