# 报备房
CREATE TABLE `room`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `room_no`          varchar(32) NOT NULL DEFAULT '' COMMENT '房间编号',
    `audit_order_no`   varchar(32) NOT NULL DEFAULT '' COMMENT '审核单号',
    `org_uuid`         varchar(32) NOT NULL DEFAULT '' COMMENT '机构uuid',
    `team_id`          bigint(20) NOT NULL DEFAULT 0 COMMENT '团队ID',
    `account_uuid`     varchar(64) NOT NULL DEFAULT '' COMMENT '房主 他趣 uuid',
    `mobile`           varchar(16) NOT NULL DEFAULT '' COMMENT '房主手机号',
    `owner_name`       varchar(32) NOT NULL DEFAULT '' COMMENT '房主名字',
    `time_scope_start` varchar(32) NOT NULL DEFAULT '' COMMENT '报备时间范围',
    `type`             int(11) NOT NULL DEFAULT 0 COMMENT '房间类型 1.报备房',
    `source`           int(11) NOT NULL DEFAULT 0 COMMENT '来源 0-用户端 1-管理端',
    `status`           int(11) NOT NULL DEFAULT 0 COMMENT '状态 0 初始化 1 已审核 -1已取消',
    `create_time`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `create_by`        varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_by`        varchar(32) NOT NULL DEFAULT '' COMMENT '最后更新人',
    `remark`           varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_audit_order_no` (`audit_order_no`) COMMENT '审核单号',
    KEY `idx_org_uuid_status` (`org_uuid`, `status`) COMMENT '机构uuid',
    KEY `idx_room_no` (`room_no`) COMMENT '房间编号',
    KEY `idx_team_id_status` (`team_id`, `status`) COMMENT '团队ID',
    KEY `idx_account_uuid` (`account_uuid`) COMMENT '房主 他趣 uuid'
) ENGINE = InnoDB COMMENT ='报备房间';

# 审核单
CREATE TABLE `audit_order`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `no`          varchar(32)  NOT NULL DEFAULT '' COMMENT '编号',
    `type`        int(11)      NOT NULL DEFAULT 0 COMMENT '审核单类型 1.报备房',
    `status`      int(11)      NOT NULL DEFAULT 0 COMMENT '状态 0 待审核 1 部分通过 2 全通过 -1 已拒绝',
    `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `create_by`   varchar(32)  NOT NULL DEFAULT '' COMMENT '创建人',
    `update_by`   varchar(32)  NOT NULL DEFAULT '' COMMENT '最后更新人',
    `remark`      varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_no` (`no`) COMMENT '编号',
    KEY `idx_status` (`status`) COMMENT '状态'
) ENGINE = InnoDB COMMENT ='审核单';

# 审核项
CREATE TABLE `audit_item`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `no`           varchar(32)  NOT NULL DEFAULT '' COMMENT '编号',
    `order_no`     varchar(32)  NOT NULL DEFAULT '' COMMENT '审核单编号',
    `layer`        int(11)      NOT NULL DEFAULT 0 COMMENT '层',
    `auditor_uuid` varchar(32)  NOT NULL DEFAULT '' COMMENT '审核人uuid',
    `status`       int(11)      NOT NULL DEFAULT 0 COMMENT '状态 0 待审核 1 已通过 -1 已拒绝',
    `create_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    `create_by`    varchar(32)  NOT NULL DEFAULT '' COMMENT '创建人',
    `update_by`    varchar(32)  NOT NULL DEFAULT '' COMMENT '最后更新人',
    `remark`       varchar(256) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_auditor_uuid_status` (`auditor_uuid`, `status`) COMMENT '审核人uuid_状态',
    KEY `idx_no` (`no`) COMMENT '编号',
    KEY `idx_order_no` (`order_no`) COMMENT '审核单编号'
) ENGINE = InnoDB COMMENT ='审核项'