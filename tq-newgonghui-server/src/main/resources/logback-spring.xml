<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<property name="log.dir" value="./data/logs/sjava"/>
	<property name = "defaultPattern" value="%t %logger{10} %msg%n"/>
	<property name="errorPattern" value="%t %logger{10} %F:%L %msg%n"/>
<!--	<property name = "defaultEncoder" value="ch.qos.logback.core.encoder.LayoutWrappingEncoder"/>-->
	<property name = "defaultEncoder" value="cn.taqu.gonghui.util.GonghuiPatternLayoutEncoder"/>

	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="${defaultEncoder}">
			<pattern>${defaultPattern}</pattern>
		</encoder>
	</appender>

	<!-- trace日志 -->
	<appender name="traceFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.dir}/trace.%d{yyyy-MM-dd}.log</fileNamePattern>
		</rollingPolicy>
		<encoder class="${defaultEncoder}">
			<pattern>${defaultPattern}</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>TRACE</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>
	<!-- debug日志 -->
	<appender name="debugFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.dir}/debug.%d{yyyy-MM-dd}.log</fileNamePattern>
		</rollingPolicy>
		<encoder class="${defaultEncoder}">
			<pattern>${defaultPattern}</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>DEBUG</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>
	<!-- info日志 -->
	<appender name="infoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.dir}/info.%d{yyyy-MM-dd}.log</fileNamePattern>
		</rollingPolicy>
		<encoder class="${defaultEncoder}">
			<pattern>${defaultPattern}</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>INFO</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>
	<!-- warn日志 -->
	<appender name="warnFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.dir}/warn.%d{yyyy-MM-dd}.log</fileNamePattern>
		</rollingPolicy>
		<encoder class="${defaultEncoder}">
			<pattern>${defaultPattern}</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>WARN</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>
	<!-- error日志 -->
	<appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.dir}/error.%d{yyyy-MM-dd}.log</fileNamePattern>
		</rollingPolicy>
		<encoder class="${defaultEncoder}">
			<pattern>${errorPattern}</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<logger name="cn.taqu" level="INFO"/>
	<!--    <logger name="jdbc.sqltiming" level="ERROR"/>-->
	<!--    <logger name="jdbc.sqlonly" level="WARN"/>-->
	<!--    <logger name="jdbc.resultset" level="ERROR"/>-->
	<!--    <logger name="jdbc.audit" level="ERROR"/>-->
	<!--    <logger name="jdbc.connection" level="ERROR"/>-->
	<logger name="org.elasticsearch" level="INFO"/>
	<logger name="org.springframework.kafka" level="INFO"/>
	<logger name="org.springframework.boot" level="ERROR"/>
	<logger name="org.apache.rocketmq" level="ERROR"/>
	<!-- 配置MyBatis的日志级别 -->
	<logger name="org.mybatis" level="DEBUG" />

	<!-- 如果你还想看到SQL的参数，可以添加以下配置 -->
	<logger name="org.mybatis.spring.SqlSessionTemplate" level="INFO" />
	<logger name="com.baomidou.mybatisplus" level="INFO"  />

	<springProfile name="prod">
		<logger name="cn.taqu" level="INFO"/>
		<root level="INFO">
			<appender-ref ref="errorFile"/>
			<appender-ref ref="warnFile"/>
			<appender-ref ref="infoFile"/>
		</root>
	</springProfile>

	<springProfile name="local,dev">
		<logger name="cn.taqu.gonghui" level="debug"/>
		<logger name="cn.taqu.gonghui.system.mapper" level="Info"/>
		<!--        <logger name="cn.taqu.mp.account.dao.mapper" level="Info"/>-->
		<root level="Info">
			<appender-ref ref="errorFile"/>
			<appender-ref ref="warnFile"/>
			<appender-ref ref="infoFile"/>
			<appender-ref ref="debugFile"/>
			<appender-ref ref="traceFile"/>
			<appender-ref ref="console"/>
		</root>
	</springProfile>

	<springProfile name="test">
		<logger name="cn.taqu.gonghui" level="debug"/>
		<logger name="cn.taqu.gonghui.system.mapper" level="Info"/>
		<root level="info">
			<appender-ref ref="errorFile"/>
			<appender-ref ref="warnFile"/>
			<appender-ref ref="infoFile"/>
			<appender-ref ref="console"/>
		</root>
	</springProfile>

</configuration>
