<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.TeamHostOperateLogMapper" >
  <insert id="insert" parameterType="cn.taqu.gonghui.system.entity.TeamHostOperateLog" >
    insert into team_host_operate_log (host_uuid, content, type,
      create_time,operator)
    values (#{hostUuid}, #{content}, #{type,jdbcType=INTEGER},
      #{createTime,jdbcType=INTEGER},#{operator})
  </insert>

  <select id="queryByCondition" parameterType="cn.taqu.gonghui.system.search.TeamHostOperateLogSearch" resultType="cn.taqu.gonghui.system.vo.TeamHostOperateLogVo">
    select * from team_host_operate_log
    <where>
      <if test="type != null">
        and type = #{type}
      </if>
      <if test="hostUuid != null and hostUuid != ''">
        and host_uuid = #{hostUuid}
      </if>
      <if test="operator != null and operator != ''">
        and operator = #{operator}
      </if>
      <if test="startTime != null">
        and create_time &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        and create_time &lt;= #{endTime}
      </if>
    </where>
    order by create_time desc
  </select>

  <select id="selectOneByUuidAndType" resultType="cn.taqu.gonghui.system.entity.TeamHostOperateLog">
    SELECT content,create_time FROM team_host_operate_log WHERE host_uuid = #{hostUuid} AND type = #{type} AND team_id = #{teamId}
    ORDER BY id DESC LIMIT 1
  </select>

</mapper>
