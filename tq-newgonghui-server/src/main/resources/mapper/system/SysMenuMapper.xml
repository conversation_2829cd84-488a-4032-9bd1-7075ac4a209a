<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.SysMenuMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.SysMenu" >
    <id column="menu_id" property="menuId" jdbcType="BIGINT" />
    <result column="menu_name" property="menuName" jdbcType="VARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="BIGINT" />
    <result column="ancestors" property="ancestors" jdbcType="VARCHAR" />
    <result column="order_num" property="orderNum" jdbcType="INTEGER" />
    <result column="router_path" property="routerPath" jdbcType="VARCHAR" />
    <result column="menu_type" property="menuType" jdbcType="CHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="icon" property="icon" jdbcType="VARCHAR" />
    <result column="visible" property="visible" jdbcType="CHAR" />
    <result column="status" property="status" jdbcType="CHAR" />
    <result column="perms" property="perms" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    menu_id, menu_name, parent_id,ancestors, order_num, router_path, menu_type, type, icon, visible, status,
    perms, create_by, create_time, update_by, update_time, remark
  </sql>
<!-- 自定义查询 -->
  <select id="selectMenuList" parameterType="cn.taqu.gonghui.system.entity.SysMenu" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sys_menu
    <where>
      <if test="menuName != null and menuName != ''">
        AND menu_name like concat('%', #{menuName}, '%')
      </if>
      <if test="visible != null and visible != ''">
        AND visible = #{visible}
      </if>
      <if test="status != null and status != ''">
        AND status = #{status}
      </if>
      <if test="menuType != null and menuType != ''">
        AND menu_type in
          <foreach item="item" index="index" collection="menuType.split(',')" open="(" separator="," close=")">
            #{item}
          </foreach>
      </if>
      <if test="type != null">
        AND type = #{type}
      </if>
    </where>
    order by parent_id, order_num
  </select>

  <select id="selectMenuListByUserId" parameterType="cn.taqu.gonghui.system.entity.SysMenu" resultMap="BaseResultMap">
    select distinct m.menu_id, m.parent_id, m.menu_name, m.router_path, m.visible, m.status, ifnull(m.perms,'') as perms, m.menu_type,m.type,  m.icon, m.order_num, m.create_time
    from sys_menu m
    left join sys_role_menu rm on m.menu_id = rm.menu_id
    left join sys_user_role ur on rm.role_id = ur.role_id
    left join sys_role ro on ur.role_id = ro.role_id
    left join sys_user us on ur.user_id = us.user_id
    where us.user_name = #{userName}
    <if test="menuName != null and menuName != ''">
      AND menu_name like concat('%', #{menuName}, '%')
    </if>
    <if test="visible != null and visible != ''">
      AND visible = #{visible}
    </if>
    <if test="status != null and status != ''">
      AND status = #{status}
    </if>
    order by m.parent_id, m.order_num
  </select>

  <select id="selectMenuPerms" resultType="java.lang.String">
    select distinct m.perms
    from sys_menu m
           left join sys_role_menu rm on m.menu_id = rm.menu_id
           left join sys_user_role ur on rm.role_id = ur.role_id
  </select>

  <select id="selectMenuPermsByUserId" parameterType="java.lang.String" resultType="java.lang.String">
    select distinct m.router_path
    from sys_menu m
           left join sys_role_menu rm on m.menu_id = rm.menu_id
           left join sys_user_role ur on rm.role_id = ur.role_id
           left join sys_role r on r.role_id = ur.role_id
           left join sys_user us on ur.user_id = us.user_id
    where m.menu_type in ('C','F')  and m.status = '1' and r.status = 1 and us.account_uuid = #{accountUuid}
  </select>
  <select id="selectMenuPermsByUserIdAndType" parameterType="java.lang.String" resultType="java.lang.String">
    select distinct m.router_path
    from sys_menu m
           left join sys_role_menu rm on m.menu_id = rm.menu_id
           left join sys_user_role ur on rm.role_id = ur.role_id
           left join sys_role r on r.role_id = ur.role_id
           left join sys_user us on ur.user_id = us.user_id
    where m.menu_type in ('C','F')  and m.status = '1' and r.status = 1 and us.account_uuid = #{accountUuid}
    and r.type= #{type}
  </select>

  <select id="selectMenuTreeAll"  resultMap="BaseResultMap">
    select distinct m.menu_id, m.parent_id, m.menu_name, m.router_path, m.visible, m.status, ifnull(m.perms,'') as perms, m.menu_type,m.type, m.icon, m.order_num, m.create_time
    from sys_menu m where m.status = '1' and m.menu_type in ('M','C')
    and
    m.type in
    <foreach collection="typeList" item="item" index="index"
             separator="," open="(" close=")">
      #{item}
    </foreach>
    order by m.parent_id, m.order_num
  </select>

  <select id="selectMenuTreeByUserId" resultMap="BaseResultMap">
    select distinct m.menu_id, m.parent_id, m.menu_name, m.router_path, m.visible, m.status, ifnull(m.perms,'') as perms, m.menu_type,m.type, m.icon, m.order_num, m.create_time
    from sys_menu m
           left join sys_role_menu rm on m.menu_id = rm.menu_id
           left join sys_user_role ur on rm.role_id = ur.role_id
           left join sys_role ro on ur.role_id = ro.role_id
           left join sys_user u on ur.user_id = u.user_id
    where u.account_uuid = #{accountUuid} and m.type = #{type} and ro.type = #{type} and m.status = '1'  AND ro.status = 1 AND m.menu_type in ('M','C')
    order by m.parent_id, m.order_num
  </select>

  <select id="selectMenuListByRoleId" resultType="java.lang.Long">
    select m.menu_id
    from sys_menu m
    left join sys_role_menu rm on m.menu_id = rm.menu_id
    where rm.role_id = #{roleId}
    order by m.parent_id, m.order_num
  </select>

  <select id="selectMenuById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sys_menu
    where menu_id = #{menuId}
  </select>

  <select id="hasChildByMenuId" resultType="java.lang.Integer">
    select count(1) from sys_menu where parent_id = #{menuId} and status = '1'
  </select>

  <insert id="insertMenu" parameterType="cn.taqu.gonghui.system.entity.SysMenu">
    insert into sys_menu(
    <if test="menuId != null and menuId != 0">menu_id,</if>
    <if test="parentId != null and parentId != 0">parent_id,</if>
    <if test="ancestors != null and ancestors != ''">ancestors,</if>
    <if test="menuName != null and menuName != ''">menu_name,</if>
    <if test="orderNum != null and orderNum != ''">order_num,</if>
    <if test="routerPath != null and routerPath != ''">router_path,</if>
    <if test="menuType != null and menuType != ''">menu_type,</if>
    <if test="type != null and type != ''">type,</if>
    <if test="icon != null">icon,</if>
    <if test="visible != null">visible,</if>
    <if test="status != null">status,</if>
    <if test="perms !=null and perms != ''">perms,</if>
    <if test="remark != null and remark != ''">remark,</if>
    <if test="createBy != null and createBy != ''">create_by,</if>
    create_time
    )values(
    <if test="menuId != null and menuId != 0">#{menuId},</if>
    <if test="parentId != null and parentId != 0">#{parentId},</if>
    <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
    <if test="menuName != null and menuName != ''">#{menuName},</if>
    <if test="orderNum != null and orderNum != ''">#{orderNum},</if>
    <if test="routerPath != null and routerPath != ''">#{routerPath},</if>
    <if test="menuType != null and menuType != ''">#{menuType},</if>
    <if test="type != null and type != ''">#{type},</if>
    <if test="icon != null">#{icon},</if>
    <if test="visible != null">#{visible},</if>
    <if test="status != null">#{status},</if>
    <if test="perms !=null and perms != ''">#{perms},</if>
    <if test="remark != null and remark != ''">#{remark},</if>
    <if test="createBy != null and createBy != ''">#{createBy},</if>
    sysdate()
    )
  </insert>

  <update id="updateMenu" parameterType="cn.taqu.gonghui.system.entity.SysMenu">
    update sys_menu
    <set>
      <if test="menuName != null and menuName != ''">menu_name = #{menuName},</if>
      <if test="parentId != null">parent_id = #{parentId},</if>
      <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
      <if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
      <if test="routerPath != null and routerPath != ''">router_path = #{routerPath},</if>
      <if test="menuType != null and menuType != ''">menu_type = #{menuType},</if>
      <if test="type != null and type != ''">type = #{type},</if>
      <if test="icon != null">icon = #{icon},</if>
      <if test="visible != null">visible = #{visible},</if>
      <if test="status != null">status = #{status},</if>
      <if test="perms !=null">perms = #{perms},</if>
      <if test="remark != null and remark != ''">remark = #{remark},</if>
      <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
      <if test="updateTime != null and updateTime != ''">update_time = #{updateTime},</if>
    </set>
    where menu_id = #{menuId}
  </update>

  <update id="updateChildrenVisible" parameterType="java.lang.Long">
    update sys_menu set visible = '0' where find_in_set(#{menuId}, ancestors)
  </update>

  <delete id="deleteMenuById" parameterType="java.lang.Long">
    delete from sys_menu where menu_id = #{menuId}
  </delete>

  <select id="selectChildrenMenuById" parameterType="Long" resultMap="BaseResultMap">
    select * from sys_menu where find_in_set(#{menuId}, ancestors)
  </select>

  <update id="updateMenuChildren" parameterType="java.util.List">
    update sys_menu set ancestors =
    <foreach collection="list" item="item" index="index"
             separator=" " open="case menu_id" close="end">
      when #{item.menuId} then #{item.ancestors}
    </foreach>
    where menu_id in
    <foreach collection="list" item="item" index="index"
             separator="," open="(" close=")">
      #{item.menuId}
    </foreach>
  </update>

  <select id="checkMenuNameUnique" parameterType="cn.taqu.gonghui.system.entity.SysMenu" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sys_menu
    where menu_name=#{menuName} and parent_id = #{parentId} and type = #{type} limit 1
  </select>

  <select id="getmenuNameByServiceAndMethod" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT menu_name from sys_menu where menu_type = 'F' and INSTR(router_path,#{serviceAndMethod}) limit 1
  </select>

  <select id="getMenuInfoByServiceAndMethod"  resultMap="BaseResultMap">
    SELECT * from sys_menu where menu_type = 'F' and type = #{type} and INSTR(router_path,#{serviceAndMethod}) limit 1
  </select>
</mapper>
