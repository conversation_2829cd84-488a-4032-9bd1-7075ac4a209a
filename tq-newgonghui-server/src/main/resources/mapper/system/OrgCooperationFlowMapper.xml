<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.OrgCooperationFlowMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.OrgCooperationFlow">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_uuid" jdbcType="VARCHAR" property="orgUuid" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_uuid, url, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from org_cooperation_flow
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from org_cooperation_flow
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="cn.taqu.gonghui.system.entity.OrgCooperationFlow">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into org_cooperation_flow (org_uuid, url, create_time, 
      update_time)
    values (#{orgUuid}, #{url}, #{createTime,jdbcType=INTEGER},
      #{updateTime,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.system.entity.OrgCooperationFlow">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into org_cooperation_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgUuid != null">
        org_uuid,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgUuid != null">
        #{orgUuid},
      </if>
      <if test="url != null">
        #{url},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.system.entity.OrgCooperationFlow">
    update org_cooperation_flow
    <set>
      <if test="orgUuid != null">
        org_uuid = #{orgUuid},
      </if>
      <if test="url != null">
        url = #{url},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.system.entity.OrgCooperationFlow">
    update org_cooperation_flow
    set org_uuid = #{orgUuid},
      url = #{url},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <delete id="deleteByUuid" parameterType="java.lang.String">
    delete from org_cooperation_flow
    where org_uuid = #{orgUuid}
  </delete>

  <select id="getAllByOrgUuid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from org_cooperation_flow
    where org_uuid = #{orgUuid}
  </select>
</mapper>