<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.OperatorLogMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.OperatorLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="charge_person_phone" jdbcType="VARCHAR" property="chargePersonPhone" />
    <result column="charge_person_phone_cipher" jdbcType="VARCHAR" property="chargePersonPhoneCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
    <result column="charge_person_phone_digest" jdbcType="VARCHAR" property="chargePersonPhoneDigest" typeHandler="cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler"/>
    <result column="old_content" jdbcType="LONGVARCHAR" property="oldContent" />
    <result column="old_content_cipher" jdbcType="VARCHAR" property="oldContentCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
    <result column="new_content" jdbcType="LONGVARCHAR" property="newContent" />
    <result column="new_content_cipher" jdbcType="VARCHAR" property="newContentCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
    <result column="audit_msg" jdbcType="LONGVARCHAR" property="auditMsg" />
  </resultMap>
  <sql id="Base_Column_List">
    id, operator, create_time, update_time, type, org_name, charge_person_phone,charge_person_phone_cipher,charge_person_phone_digest,
    old_content,old_content_cipher,new_content,new_content_cipher,audit_msg
  </sql>
  <sql id="Blob_Column_List">
    old_content, new_content, audit_msg
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from operator_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from operator_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="cn.taqu.gonghui.system.entity.OperatorLog">
<!--    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">-->
<!--      SELECT LAST_INSERT_ID()-->
<!--    </selectKey>-->
    insert into operator_log (
<!--    operator,-->
    create_time,
    update_time,
    type,
    org_name,
    charge_person_phone,
    charge_person_phone_cipher,
    charge_person_phone_digest,
    old_content,
    old_content_cipher,
    new_content,
    new_content_cipher,
    audit_msg
    )
    values (
<!--    #{operator},-->
    #{createTime,jdbcType=INTEGER},
    #{updateTime,jdbcType=INTEGER},
    #{type,jdbcType=TINYINT},
    #{orgName},
    #{chargePersonPhone,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
    #{chargePersonPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
    #{chargePersonPhoneDigest,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler},
    #{oldContent,jdbcType=LONGVARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
    #{oldContentCipher,jdbcType=LONGVARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
    #{newContent,jdbcType=LONGVARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
    #{newContentCipher,jdbcType=LONGVARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
    #{auditMsg,jdbcType=LONGVARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.system.entity.OperatorLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into operator_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operator != null">
        operator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="chargePersonPhone != null">
        charge_person_phone,
      </if>
      <if test="oldContent != null">
        old_content,
      </if>
      <if test="newContent != null">
        new_content,
      </if>
      <if test="auditMsg != null">
        audit_msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="operator != null">
        #{operator},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="orgName != null">
        #{orgName},
      </if>
      <if test="chargePersonPhone != null">
        #{chargePersonPhone,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="oldContent != null">
        #{oldContent,jdbcType=LONGVARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="newContent != null">
        #{newContent,jdbcType=LONGVARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="auditMsg != null">
        #{auditMsg,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.system.entity.OperatorLog">
    update operator_log
    <set>
      <if test="operator != null">
        operator = #{operator},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName},
      </if>
      <if test="chargePersonPhone != null">
        charge_person_phone = #{chargePersonPhone,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="oldContent != null">
        old_content = #{oldContent,jdbcType=LONGVARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="newContent != null">
        new_content = #{newContent,jdbcType=LONGVARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="auditMsg != null">
        audit_msg = #{auditMsg,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="cn.taqu.gonghui.system.entity.OperatorLog">
    update operator_log
    set operator = #{operator},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER},
      type = #{type,jdbcType=TINYINT},
      org_name = #{orgName},
      charge_person_phone = #{chargePersonPhone},
      old_content = #{oldContent,jdbcType=LONGVARCHAR},
      new_content = #{newContent,jdbcType=LONGVARCHAR},
      audit_msg = #{auditMsg,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.system.entity.OperatorLog">
    update operator_log
    set operator = #{operator},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER},
      type = #{type,jdbcType=TINYINT},
      org_name = #{orgName},
      charge_person_phone = #{chargePersonPhone}
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>