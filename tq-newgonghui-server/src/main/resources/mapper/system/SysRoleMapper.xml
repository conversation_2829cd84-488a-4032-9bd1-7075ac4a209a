<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.SysRoleMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.SysRole" >
    <id column="role_id" property="roleId" jdbcType="BIGINT" />
    <result column="role_name" property="roleName" jdbcType="VARCHAR" />
    <result column="role_key" property="roleKey" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    role_id, role_name, role_key,type, status,
    create_time, update_time,remark,create_by,update_by
  </sql>

  <select id="roleNameIsExisted" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sys_role where role_name = #{roleName} and type = #{type}
  </select>

  <select id="roleKeyIsExisted" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sys_role where  role_key = #{roleKey} and type = #{type}
  </select>


<!-- 以下为数据迁移需要用到的接口 -->
<select id="getAgentRoleId" resultType="java.lang.Long">
  SELECT role_id FROM sys_role WHERE type = 1 and role_key = 'agenter'
</select>

  <!-- 以下为数据迁移需要用到的接口 -->
  <select id="getLeaderRoleId" resultType="java.lang.Long">
    SELECT role_id FROM sys_role WHERE type = 1 and role_key = 'leader'
  </select>

  <!-- 以下为数据迁移需要用到的接口 -->
  <select id="getManagerRoleId" resultType="java.lang.Long">
    SELECT role_id FROM sys_role WHERE  role_key = 'manager' limit 1
  </select>


  <select id="getRoleName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sys_role where role_id = #{roleId} and status = 0
  </select>
</mapper>
