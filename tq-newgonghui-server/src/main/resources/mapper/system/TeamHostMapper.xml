<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.TeamHostMapper">

    <sql id="Base_Column_List">
        id,
        host_uuid,
        org_id,
        team_id,
        team_type,
        employee_id,
        invite_time,
        real_name,
        real_name_cipher,
        status,
        create_time,
        update_time,
        change_time,
        current_sharing_profit_rate,
        new_sharing_profit_rate,
        is_update,
        is_group,
        host_type
    </sql>

    <resultMap id="baseResultMap" type="cn.taqu.gonghui.system.entity.TeamHost">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="host_uuid" jdbcType="VARCHAR" property="hostUuid"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="team_type" jdbcType="INTEGER" property="teamType"/>
        <result column="employee_id" jdbcType="BIGINT" property="employeeId"/>
        <result column="invite_time" jdbcType="BIGINT" property="inviteTime"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="real_name_cipher" jdbcType="VARCHAR" property="realNameCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="INTEGER" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="change_time" jdbcType="BIGINT" property="changeTime"/>
        <result column="current_sharing_profit_rate" jdbcType="VARCHAR" property="currentSharingProfitRate"/>
        <result column="new_sharing_profit_rate" jdbcType="VARCHAR" property="newSharingProfitRate"/>
        <result column="is_update" jdbcType="INTEGER" property="isUpdate"/>
        <result column="is_group" jdbcType="INTEGER" property="isGroup"/>
        <result column="host_type" jdbcType="INTEGER" property="hostType"/>
    </resultMap>
<!--  获取团队下所有艺人  -->
<select id="idsByTeamId" parameterType="java.lang.Long" resultType="java.lang.Long">
    select id from team_host where team_id = #{teamId}
</select>

<!--  将团队下所有艺人归属到该机构下的默认团队  -->
    <update id="hostsBelongToDefaultTeam">
        update team_host set team_id = #{teamId}
        where team_type =#{teamType} and id in
        <foreach collection="hostIds" index="index" item="hostId" separator="," open="(" close=")">
            #{hostId}
        </foreach>
    </update>

    <select id="idsByAgentId" parameterType="java.lang.Long" resultType="cn.taqu.gonghui.system.entity.TeamHost">
        select id, host_uuid from team_host where team_id = #{teamId}
    </select>
    <select id="getHostUuidsByAgenter" parameterType="java.lang.Long" resultType="java.lang.String">
        select host_uuid from team_host where employee_id = #{employeeId} and status = 1
    </select>

    <select id="getHostUuidsByLeader" parameterType="java.lang.Long" resultType="java.lang.String">
        select host_uuid from team_host where team_id = #{teamId} and status = 1
    </select>



    <select id="getOrgIdByHostUuid" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT
            t1.org_uuid
        FROM
            organization t1
                LEFT JOIN team_host t2 ON t1.org_id = t2.org_id
        WHERE
            t2.host_uuid = #{hostUuid}
          AND t1.org_status = 1
          AND t2.team_type =1
    </select>

    <select id="getOneByHostUuid" resultMap="baseResultMap">
<!--            resultType="cn.taqu.gonghui.system.entity.TeamHost">-->
        select <include refid="Base_Column_List"></include>
        from team_host where host_uuid = #{hostUuid} and team_type = #{teamType,jdbcType=TINYINT} limit 1
    </select>

    <update id="updateTeamOrAgenter" parameterType="cn.taqu.gonghui.system.dto.HostTeamOrAgentDto">
        update team_host set employee_id = #{employeeId},team_id = #{teamId} where team_type =1 and host_uuid = #{hostUuid}
    </update>

    <select id="getTeamHostList" resultMap="baseResultMap" >
<!--            resultType="cn.taqu.gonghui.system.entity.TeamHost">-->
        select <include refid="Base_Column_List"></include>
          from team_host where is_update = 1 and status = 1 and team_type =1
                                  and id &gt;= #{idStart,jdbcType=INTEGER}
                                  and id &lt; #{idEnd,jdbcType=INTEGER}
                                  and change_time <![CDATA[ >= ]]> #{startTime}
                                  and change_time <![CDATA[ <= ]]> #{endTime}
    </select>


    <update id="batchUpdateHost" parameterType="java.util.List">
        update team_host
        <trim prefix="set" suffixOverrides=",">
            current_sharing_profit_rate =
            case
            <foreach collection="list" item="item">
            when
            host_uuid = #{item.hostUuid}
            then
            #{item.currentSharingProfitRate}
            </foreach>
            end,
            is_update = case
            <foreach collection="list" item="item">
            when host_uuid = #{item.hostUuid}
            then #{item.isUpdate}
            </foreach>
            end,
        </trim>
        where team_type=1 and host_uuid in
        <foreach collection="list" open="(" separator="," item="item" close=")">#{item.hostUuid}</foreach>
    </update>

    <update id="updateProfitAndIsUpdate" parameterType="cn.taqu.gonghui.system.entity.TeamHost">
        update team_host set new_sharing_profit_rate = #{newSharingProfitRate},is_update = #{isUpdate} where team_type=1 and host_uuid = #{hostUuid}
    </update>

    <update id="updateProfitAndIsUpdateAndChangeTime" parameterType="cn.taqu.gonghui.system.entity.TeamHost">
        update team_host set current_sharing_profit_rate = #{currentSharingProfitRate}, new_sharing_profit_rate = #{newSharingProfitRate},is_update = #{isUpdate},change_time = #{changeTime},host_type = #{hostType} ,is_group = #{isGroup}
        where id = #{id}
    </update>


    <select id="getHostByOrgId" parameterType="cn.taqu.gonghui.system.entity.TeamHost" resultMap="baseResultMap" >
<!--            resultType="cn.taqu.gonghui.system.entity.TeamHost">-->
        select
        <include refid="Base_Column_List">
        </include>
        from team_host where status = 1
        <if test="orgId !=null">
            and org_id = #{orgId}
        </if>
        <if test="employeeId !=null">
            and employee_id = #{employeeId}
        </if>
        <if test="hostUuid !=null">
            and host_uuid = #{hostUuid}
        </if>
    </select>

    <select id="teamNameByHostUuidList" parameterType="java.util.List" resultType="cn.taqu.gonghui.system.vo.TeamHostVo">
        SELECT
        t1.host_uuid hostUuid,
        t1.org_id orgId,
        t2.team_id teamId,
        t2.team_name teamName
        FROM
        team_host t1
        LEFT JOIN team t2 ON t1.team_id = t2.team_id
        where t1.host_uuid in
        <foreach collection="list" item="uuid" open="(" close=")" separator=",">
            #{uuid}
        </foreach>
    </select>

    <select id="orgInfoByHostUuidList" parameterType="java.util.List" resultType="cn.taqu.gonghui.system.vo.TeamHostVo">
        SELECT
        t1.host_uuid hostUuid,
        t1.org_id orgId,
        t1.team_id teamId
        FROM
        team_host t1
        where t1.host_uuid in
        <foreach collection="list" item="uuid" open="(" close=")" separator=",">
            #{uuid}
        </foreach>
    </select>

    <select id="orgNameByHostUuidList" parameterType="java.util.List" resultType="cn.taqu.gonghui.system.vo.TeamHostVo">
        SELECT
        t1.host_uuid hostUuid,
        t1.team_id teamId,
        t2.org_id orgId,
        t2.org_name orgName
        FROM
        team_host t1
        LEFT JOIN organization t2 ON t1.org_id = t2.org_id
        where t1.host_uuid in
        <foreach collection="list" item="uuid" open="(" close=")" separator=",">
            #{uuid}
        </foreach>
    </select>

    <select id="findHostsByTeamId" parameterType="java.lang.Long" resultType="java.lang.String">
        select host_uuid from team_host where team_id = #{teamId}
    </select>

    <update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.system.entity.TeamHost" >
        update team_host
        set host_uuid = #{hostUuid},
            org_id = #{orgId,jdbcType=BIGINT},
            team_id = #{teamId,jdbcType=BIGINT},
            team_type = #{teamType,jdbcType=TINYINT},
            employee_id = #{employeeId,jdbcType=BIGINT},
            invite_time = #{inviteTime,jdbcType=INTEGER},
            real_name = #{realName},
            real_name_cipher = #{realNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
            status = #{status,jdbcType=INTEGER},
            create_time = #{createTime,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=INTEGER},
            change_time = #{changeTime,jdbcType=INTEGER},
            current_sharing_profit_rate = #{currentSharingProfitRate},
            new_sharing_profit_rate = #{newSharingProfitRate},
            is_update = #{isUpdate,jdbcType=INTEGER},
            host_type = #{hostType,jdbcType=INTEGER},
            is_group = #{isGroup,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>



    <select id="getHostListByOrgId"  resultMap="baseResultMap" >
<!--            resultType="cn.taqu.gonghui.system.entity.TeamHost">-->
        select
        <include refid="Base_Column_List">
        </include>
         from team_host where team_type = #{teamType} and  org_id = #{orgId}
    </select>

    <select id="getMaxId" resultType="java.lang.Integer">
        SELECT MAX(id) FROM team_host
    </select>

    <select id="getSharingPorfitRateList" parameterType="java.util.List" resultType="cn.taqu.gonghui.system.entity.TeamHost">
        SELECT
            host_uuid hostUuid,
            current_sharing_profit_rate currentSharingProfitRate
        FROM
            team_host
        where team_type = 1 and host_uuid in
        <foreach collection="list" item="uuid" open="(" close=")" separator=",">
            #{uuid}
        </foreach>
    </select>

    <insert id="insertHost" parameterType="cn.taqu.gonghui.system.entity.TeamHost" useGeneratedKeys="true" keyProperty="id">
        insert into team_host
        (
        host_uuid,
        org_id,
        team_id,
        team_type,
        employee_id,
        invite_time,
        status,
        create_time,
        update_time,
        change_time,
        current_sharing_profit_rate,
        new_sharing_profit_rate,
        is_update
        )
        values
            (
            #{hostUuid},
            #{orgId},
            #{teamId},
            #{teamType},
            #{employeeId},
            #{inviteTime},
            #{status},
            #{createTime},
            #{updateTime},
            #{changeTime},
            #{currentSharingProfitRate},
            #{newSharingProfitRate},
            #{isUpdate}
            )
    </insert>
<!--  以下是数据迁移需要用到的接口  -->
    <insert id="batchInsertHost" parameterType="java.util.List">
        insert into team_host
        (
            id,
            host_uuid,
            org_id,
            team_id,
            team_type,
            employee_id,
            invite_time,
            status,
            create_time,
            update_time,
            change_time,
            current_sharing_profit_rate,
            new_sharing_profit_rate,
            is_update
        )
        values
        <foreach collection="list" item="host" separator=",">
        (
             #{host.id},
             #{host.hostUuid},
             #{host.orgId},
             #{host.teamId},
             #{host.teamType},
             #{host.employeeId},
             #{host.inviteTime},
             #{host.status},
             #{host.createTime},
             #{host.updateTime},
             #{host.changeTime},
             #{host.currentSharingProfitRate},
             #{host.newSharingProfitRate},
             #{host.isUpdate}
        )
        </foreach>
    </insert>

    <delete id="deleteByOrgId" parameterType="java.lang.Long">
        delete from team_host where org_id = #{orgId}
    </delete>

    <delete id="deleteByTeamId" parameterType="java.lang.Long">
        delete from team_host where team_id = #{teamId}
    </delete>

    <update id="updateByHostUuid" parameterType="cn.taqu.gonghui.system.entity.TeamHost">
        update team_host set org_id = #{orgId},team_id = #{teamId} where host_uuid = #{hostUuid}
    </update>

    <update id="batchUpdate" parameterType="java.util.List">
        update team_host
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.orgId !=null and item.orgId !=''">
                        when id = #{item.id}
                        then #{item.orgId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="team_id =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.teamId}
                </foreach>
            </trim>
            <trim prefix="employee_id=case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.employeeId}
                </foreach>
            </trim>
            <trim prefix="status=case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.status}
                </foreach>
            </trim>
            <trim prefix="update_time=case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    when id=#{item.id} then #{item.updateTime}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="getChatListByPage" resultType="cn.taqu.gonghui.chatroom.vo.ChatVo">
        select team_id as teamId,host_uuid as chatUuid from team_host where team_type = 3 and  team_id is not null
    </select>

    <select id="selectHostOneByUuid" resultMap="baseResultMap">
<!--            resultType="cn.taqu.gonghui.system.entity.TeamHost">-->
        SELECT
        <include refid="Base_Column_List">
        </include>
        FROM team_host WHERE team_type = #{teamType} AND host_uuid = #{hostUuid}
        ORDER BY id DESC LIMIT 1
    </select>

    <select id="selectTeamHostByUuid" resultType="cn.taqu.gonghui.system.vo.TeamHostVo">
        SELECT
        t1.host_uuid hostUuid,
        t1.team_id teamId,
        t2.org_id orgId,
        t2.org_name orgName
        FROM
        team_host t1
        LEFT JOIN organization t2 ON t1.org_id = t2.org_id
        where t1.host_uuid  = #{hostUuid}
    </select>

    <select id="selectResetRow" resultType="cn.taqu.gonghui.system.entity.TeamHost">
        SELECT host_uuid,org_id,team_id,team_type,status,current_sharing_profit_rate
        ,new_sharing_profit_rate,is_update FROM team_host
        WHERE host_uuid NOT IN ( SELECT host_uuid FROM team_host_operate_log WHERE type = 6 )
        ORDER BY id ASC LIMIT #{num}
    </select>

    <select id="selectListByHostAndType" resultType="cn.taqu.gonghui.system.dto.HostOrgTeamDto">
        SELECT th.host_uuid,th.org_id,th.team_id,t.team_name,o.org_name,th.team_type,th.status
        FROM team_host th
        LEFT JOIN organization o ON th.org_id = o.org_id
        LEFT JOIN team t ON th.team_id = t.team_id
        WHERE
        <foreach collection="reqList" item="item" open="(" close=")" separator="or">
            (th.host_uuid = #{item.hostUuid} and th.team_type = #{item.teamType})
        </foreach>
    </select>

    <update id="updateClearTxtByRange">
        UPDATE team_host SET real_name='' where id between #{curStartId,jdbcType=BIGINT} and #{curEndId,jdbcType=BIGINT};
    </update>

</mapper>
