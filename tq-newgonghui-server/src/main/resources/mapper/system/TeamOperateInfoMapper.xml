<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.TeamOperateInfoMapper">
    <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.TeamOperateInfo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="old_org_id" property="oldOrgId" jdbcType="BIGINT"/>
        <result column="old_team_id" property="oldTeamId" jdbcType="BIGINT"/>
        <result column="new_org_id" property="newOrgId" jdbcType="BIGINT"/>
        <result column="new_team_id" property="newTeamId" jdbcType="BIGINT"/>
        <result column="team_type" property="teamType" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="host_num" property="hostNum" jdbcType="INTEGER"/>
        <result column="success_num" property="successNum" jdbcType="INTEGER"/>
        <result column="fail_num" property="failNum" jdbcType="INTEGER"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="INTEGER"/>
    </resultMap>

    <select id="findAll" parameterType="java.lang.Integer" resultType="cn.taqu.gonghui.system.entity.TeamOperateInfo">
        select *
        from team_operate_info
        where type = #{type}
        order by create_time desc
    </select>
</mapper>
