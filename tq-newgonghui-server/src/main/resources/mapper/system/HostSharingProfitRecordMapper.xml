<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.HostSharingProfitRecordMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.HostSharingProfitRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="host_uuid" property="hostUuid" jdbcType="CHAR" />
    <result column="account_uuid" property="accountUuid" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="team_id" property="teamId" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="current_sharing_profit_rate" property="currentSharingProfitRate" jdbcType="VARCHAR" />
    <result column="new_sharing_profit_rate" property="newSharingProfitRate" jdbcType="VARCHAR" />
    <result column="change_time" property="changeTime" jdbcType="BIGINT" />
    <result column="effective_time" property="effectiveTime" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, host_uuid, account_uuid, type, org_id, team_id, status, current_sharing_profit_rate,
    new_sharing_profit_rate, change_time, effective_time, create_time, update_time
  </sql>
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.system.entity.HostSharingProfitRecord" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into host_sharing_profit_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="hostUuid != null" >
        host_uuid,
      </if>
      <if test="accountUuid != null" >
        account_uuid,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="teamId != null" >
        team_id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="currentSharingProfitRate != null" >
        current_sharing_profit_rate,
      </if>
      <if test="newSharingProfitRate != null" >
        new_sharing_profit_rate,
      </if>
      <if test="changeTime != null" >
        change_time,
      </if>
      <if test="effectiveTime != null" >
        effective_time,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="tmpHostType != null" >
        tmp_host_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="hostUuid != null" >
        #{hostUuid,jdbcType=CHAR},
      </if>
      <if test="accountUuid != null" >
        #{accountUuid},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="teamId != null" >
        #{teamId,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="currentSharingProfitRate != null" >
        #{currentSharingProfitRate},
      </if>
      <if test="newSharingProfitRate != null" >
        #{newSharingProfitRate},
      </if>
      <if test="changeTime != null" >
        #{changeTime,jdbcType=BIGINT},
      </if>
      <if test="effectiveTime != null" >
        #{effectiveTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="tmpHostType != null" >
        #{tmpHostType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <insert id="batchInsert">
    insert into host_sharing_profit_record
    (host_uuid, account_uuid, type, org_id, team_id, status, current_sharing_profit_rate,new_sharing_profit_rate
    , change_time, effective_time,create_time,update_time)
    values
    <foreach collection="recordList" item="record" separator=",">
      (#{record.hostUuid}, #{record.accountUuid}, #{record.type}, #{record.orgId}, #{record.teamId}, #{record.status}
      ,#{record.currentSharingProfitRate},#{record.newSharingProfitRate}, #{record.changeTime}, #{record.effectiveTime}
      ,#{record.createTime},#{record.updateTime})
    </foreach>
  </insert>
  <update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.system.entity.HostSharingProfitRecord" >
    update host_sharing_profit_record
    set host_uuid = #{hostUuid,jdbcType=CHAR},
      account_uuid = #{accountUuid},
      type = #{type,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=BIGINT},
      team_id = #{teamId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      current_sharing_profit_rate = #{currentSharingProfitRate},
      new_sharing_profit_rate = #{newSharingProfitRate},
      change_time = #{changeTime,jdbcType=BIGINT},
      effective_time = #{effectiveTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>


<!-- 自定义查询 -->
  <select id="getOneRecordBySearch" parameterType="cn.taqu.gonghui.system.search.SharingProfitRecordSearch" resultType="cn.taqu.gonghui.system.entity.HostSharingProfitRecord">
    select * from host_sharing_profit_record
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="teamId != null">
        and team_id = #{teamId}
      </if>
      <if test="orgId != null">
        and org_id = #{orgId}
      </if>
      <if test="hostUuid != null and hostUuid != ''">
        and host_uuid = #{hostUuid}
      </if>
      <if test="startTime != null">
        and create_time <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        and create_time <![CDATA[ <= ]]> #{endTime}
      </if>
    </where>
    order by create_time desc
    limit 1
  </select>

  <select id="getLastedOneRecord" parameterType="cn.taqu.gonghui.system.search.SharingProfitRecordSearch" resultType="cn.taqu.gonghui.system.entity.HostSharingProfitRecord">
    select * from host_sharing_profit_record
    where status != 2 and host_uuid = #{hostUuid} and org_id = #{orgId}
    order by create_time desc
    limit 1
  </select>


  <select id="countBySearch" parameterType="cn.taqu.gonghui.system.search.SharingProfitRecordSearch" resultType="java.lang.Integer">
    select count(*) from host_sharing_profit_record
    <where>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="hostUuid != null and hostUuid != ''">
        and host_uuid = #{hostUuid}
      </if>
      <if test="type != null">
        and type = #{type}
      </if>
      <if test="teamId != null">
        and team_id = #{teamId}
      </if>
      <if test="orgId != null">
        and org_id = #{orgId}
      </if>
      <if test="startTime != null">
        and create_time <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        and create_time <![CDATA[ <= ]]> #{endTime}
      </if>
    </where>
  </select>

  <update id="updateInfo" parameterType="cn.taqu.gonghui.system.entity.HostSharingProfitRecord">
    update host_sharing_profit_record set
    status = #{status},
    effective_time = #{effectiveTime},
    update_time = #{updateTime}
    where id = #{id}
  </update>

  <select id="manageList" parameterType="cn.taqu.gonghui.system.search.SharingProfitRecordSearch" resultType="cn.taqu.gonghui.system.vo.HostSharingProfitRecordVo">
    SELECT
    t1.id id,
    t1.host_uuid hostUuid,
    t1.type type,
    t1.team_id teamId,
    t1.org_id orgId,
    t1.effective_time effectiveTime,
    t2.team_name teamName,
    t4.org_name orgName,
    t1.current_sharing_profit_rate currentSharingProfitRate,
    t1.new_sharing_profit_rate newSharingProfitRate,
    t1.account_uuid accountUuid,
    CASE
    WHEN t1.type = 1 THEN
    t3.user_name
    ELSE
    t1.account_uuid
    END AS accountName,
    t1.change_time changeTime,
    t1. STATUS STATUS
    FROM
    host_sharing_profit_record t1
    LEFT JOIN team t2 ON t1.team_id = t2.team_id
    LEFT JOIN sys_user t3 ON t1.account_uuid = t3.account_uuid
    LEFT JOIN organization t4 ON t1.org_id = t4.org_id
    <where>
      <if test="teamId != null">
        and t1.team_id = #{teamId}
      </if>
      <if test="orgId != null">
        and t1.org_id = #{orgId}
      </if>
      <if test="status != null">
        and t1.status = #{status}
      </if>
      <if test="hostUuid != null and hostUuid != ''">
        and t1.host_uuid = #{hostUuid}
      </if>
      <if test="type != null">
        and t1.type = #{type}
      </if>
      <if test="startTime != null">
        and t1.create_time <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        and t1.create_time <![CDATA[ <= ]]> #{endTime}
      </if>
    </where>
    order by t1.create_time desc
  </select>

  <select id="clientList" parameterType="cn.taqu.gonghui.system.search.SharingProfitRecordSearch" resultType="cn.taqu.gonghui.system.vo.HostSharingProfitRecordVo">
    SELECT
    t1.id id,
    t1.host_uuid hostUuid,
    t1.type type,
    t1.team_id teamId,
    t2.team_name teamName,
    t1.org_id orgId,
    t1.current_sharing_profit_rate currentSharingProfitRate,
    t1.new_sharing_profit_rate newSharingProfitRate,
    t1.account_uuid accountUuid,
    t3.user_name accountName,
    t1.change_time changeTime,
    t1.status status,
    t1.effective_time effectiveTime
    FROM
    host_sharing_profit_record t1
    LEFT JOIN team t2 ON t1.team_id = t2.team_id
    LEFT JOIN sys_user t3 ON t1.account_uuid = t3.account_uuid
    <where>
      <if test="orgId != null">
        and t1.org_id = #{orgId}
      </if>
      <if test="teamId != null">
        and t1.team_id = #{teamId}
      </if>
      <if test="status != null">
        and t1.status = #{status}
      </if>
      <if test="hostUuid != null and hostUuid != ''">
        and t1.host_uuid = #{hostUuid}
      </if>
      <if test="uuidList != null and uuidList.size() > 0">
        and host_uuid in
        <foreach collection="uuidList" item="uuid" index="index" open="(" separator="," close=")">
          #{uuid}
        </foreach>
      </if>
      <if test="accountUuid != null and accountUuid != ''">
        and t1.account_uuid = #{accountUuid}
      </if>
      <if test="type != null">
        and t1.type = #{type}
      </if>
      <if test="accountName != null and accountName != ''">
        and t3.user_name like CONCAT('%',#{accountName},'%')
      </if>
      <if test="startTime != null">
        and t1.create_time <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        and t1.create_time <![CDATA[ <= ]]> #{endTime}
      </if>
    </where>
    order by t1.create_time desc
  </select>

  <select id="detail" resultType="cn.taqu.gonghui.system.vo.SharingResultVo">
    SELECT
      t2.team_name teamName,
      t1.host_uuid hostUuid,
      t1.change_time changeTime,
      t1.current_sharing_profit_rate currentSharingProfitRate,
      t1.new_sharing_profit_rate newSharingProfitRate,
      t1. status status,
      t1.type type,
    t1.tmp_host_type host_type
    FROM
      host_sharing_profit_record t1
        LEFT JOIN team t2 ON t1.team_id = t2.team_id
    where id = #{id} and host_uuid = #{hostUuid}
  </select>

  <select id="idListBySearch" parameterType="cn.taqu.gonghui.system.search.SharingProfitRecordSearch" resultType="cn.taqu.gonghui.system.entity.HostSharingProfitRecord">
    select id,change_time changeTime from host_sharing_profit_record
    where status = 1 and create_time <![CDATA[ >= ]]> #{startTime} and create_time <![CDATA[ <= ]]> #{endTime}
  </select>

  <update id="autoExpire" parameterType="java.util.List">
    update host_sharing_profit_record set status = 2
    where id in
    <foreach collection="list" item="id" index="index"
             separator="," open="(" close=")">
      #{id}
    </foreach>
  </update>
</mapper>
