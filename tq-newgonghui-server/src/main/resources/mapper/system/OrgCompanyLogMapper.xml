<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.OrgCompanyLogMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.OrgCompanyLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="new_enterprise_name" jdbcType="VARCHAR" property="newEnterpriseName" />
    <result column="old_enterprise_name" jdbcType="VARCHAR" property="oldEnterpriseName" />
    <result column="new_legal_person" jdbcType="VARCHAR" property="newLegalPerson" />
    <result column="old_legal_person" jdbcType="VARCHAR" property="oldLegalPerson" />
    <result column="new_legal_person_phone" jdbcType="VARCHAR" property="newLegalPersonPhone" />
    <result column="old_legal_person_phone" jdbcType="VARCHAR" property="oldLegalPersonPhone" />
    <result column="new_legal_personId_card" jdbcType="VARCHAR" property="newLegalPersonidCard" />
    <result column="old_legal_personId_card" jdbcType="VARCHAR" property="oldLegalPersonidCard" />
    <result column="new_legal_person_url_front" jdbcType="VARCHAR" property="newLegalPersonUrlFront" />
    <result column="old_legal_person_url_front" jdbcType="VARCHAR" property="oldLegalPersonUrlFront" />
    <result column="new_legal_person_url_reverse" jdbcType="VARCHAR" property="newLegalPersonUrlReverse" />
    <result column="old_legal_person_url_reverse" jdbcType="VARCHAR" property="oldLegalPersonUrlReverse" />
    <result column="new_legal_person_url_hand" jdbcType="VARCHAR" property="newLegalPersonUrlHand" />
    <result column="old_legal_person_url_hand" jdbcType="VARCHAR" property="oldLegalPersonUrlHand" />
    <result column="new_business_license_url" jdbcType="VARCHAR" property="newBusinessLicenseUrl" />
    <result column="old_business_license_url" jdbcType="VARCHAR" property="oldBusinessLicenseUrl" />
    <result column="new_social_unified_credit_code" jdbcType="VARCHAR" property="newSocialUnifiedCreditCode" />
    <result column="old_social_unified_credit_code" jdbcType="VARCHAR" property="oldSocialUnifiedCreditCode" />
    <result column="new_premises" jdbcType="VARCHAR" property="newPremises" />
    <result column="old_premises" jdbcType="VARCHAR" property="oldPremises" />
    <result column="create_operator" jdbcType="VARCHAR" property="createOperator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="relevance_id" jdbcType="BIGINT" property="relevanceId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, new_enterprise_name, old_enterprise_name, new_legal_person, old_legal_person,
    new_legal_person_phone, old_legal_person_phone, new_legal_personId_card, old_legal_personId_card,
    new_legal_person_url_front, old_legal_person_url_front, new_legal_person_url_reverse,
    old_legal_person_url_reverse, new_legal_person_url_hand, old_legal_person_url_hand,
    new_business_license_url, old_business_license_url, new_social_unified_credit_code,
    old_social_unified_credit_code, new_premises, old_premises, create_operator, create_time,
    relevance_id
  </sql>
  <select id="getLastByRelevanceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    org_company_log
    where relevance_id = #{relevanceId}
    order by id desc limit 1
  </select>
</mapper>