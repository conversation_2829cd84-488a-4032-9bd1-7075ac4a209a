<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.BackstageOperateLogMapper">

  <insert id="batchInsert">
    insert into backstage_operate_log
    (batch_id, operate_type, operator, info, create_time, update_time)
    values
    <foreach collection="logList" item="log" separator=",">
      (#{log.batchId}, #{log.operateType}, #{log.operator}, #{log.info}, #{log.createTime}, #{log.updateTime})
    </foreach>
  </insert>

</mapper>