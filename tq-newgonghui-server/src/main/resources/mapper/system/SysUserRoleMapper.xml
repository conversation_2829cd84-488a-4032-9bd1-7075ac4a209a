<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.SysUserRoleMapper">

    <resultMap type="cn.taqu.gonghui.system.entity.SysUserRole" id="SysUserRoleResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <delete id="deleteUserId" parameterType="java.lang.Long">
        delete
        from sys_user_role
        where user_id = #{userId}
    </delete>

    <delete id="deleteUserIdAndRoleId">
        delete
        from sys_user_role
        where user_id = #{userId}
          and role_id = #{roleId}
    </delete>


    <delete id="deleteUserRoleByRoleId" parameterType="java.lang.Long">
        delete
        from sys_user_role
        where role_id = #{roleId}
    </delete>


    <!--  以下是数据迁移需要用到的接口  -->
    <delete id="batchDeleteByIds" parameterType="java.util.Set">
        delete from sys_user_role where id in
        <foreach item="id" collection="collection" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getListByUserId" parameterType="java.lang.Long" resultMap="SysUserRoleResult">
        select *
        from sys_user_role
        where user_id = #{userId}
    </select>

</mapper>
