<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.NoticeMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.Notice" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="title" property="title" jdbcType="LONGVARCHAR" />
    <result column="content" property="content" jdbcType="LONGVARCHAR" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="notice_type" property="noticeType" jdbcType="TINYINT" />
    <result column="business_type" property="businessType" jdbcType="TINYINT" />
    <result column="file_key" property="fileKey" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, title,content,operator, notice_type,business_type,file_key,create_time, update_time
  </sql>

  <!-- 获取公告列表 -->
  <select id="selectNoticePageList" parameterType="cn.taqu.gonghui.system.search.NoticeSearch" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from notice where 1 = 1
    <if test="title != null and title != ''">
      and title like concat('%',#{title},'%')
    </if>
    <if test="noticeType != null">
      and notice_type = #{noticeType}
    </if>
    <if test="businessType != null">
      and business_type = #{businessType}
    </if>
    order by id desc
  </select>

<!-- 更新 -->
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.system.entity.Notice" >
    update notice
    <set >
      <if test="title != null" >
        title = #{title},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="operator != null" >
        operator = #{operator},
      </if>
      <if test="noticeType != null" >
        notice_type = #{noticeType,jdbcType=TINYINT},
      </if>
      <if test="businessType != null" >
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="fileKey != null" >
        file_key = #{fileKey},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from notice
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insertNotice" parameterType="cn.taqu.gonghui.system.entity.Notice" >
    insert into notice (id,title,content,operator, notice_type, business_type,
    file_key, create_time, update_time)
    values (#{id,jdbcType=BIGINT},#{title,jdbcType=LONGVARCHAR}, #{content,jdbcType=LONGVARCHAR},#{operator}, #{noticeType,jdbcType=TINYINT}, #{businessType,jdbcType=TINYINT},
    #{fileKey}, #{createTime,jdbcType=INTEGER}, #{updateTime,jdbcType=INTEGER})
  </insert>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from notice
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="findNoticeByNoticeType" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    notice
    WHERE 1 = 1
    <if test="noticeType != null and noticeType !=''">
      and  notice_type = #{noticeType,jdbcType=TINYINT}
    </if>
    order by update_time desc limit #{limitNum}
  </select>


  <insert id="insertBatch"  parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into notice (title,content,operator, notice_type, business_type,
    file_key, create_time, update_time)
    values
    <foreach collection="list" item="item" index="index"
             separator=",">
     (
     #{item.title,jdbcType=LONGVARCHAR},
     #{item.content,jdbcType=LONGVARCHAR},
     #{item.operator},
     #{item.noticeType,jdbcType=TINYINT},
     #{item.businessType,jdbcType=TINYINT},
     #{item.fileKey},
     #{item.createTime,jdbcType=INTEGER},
     #{item.updateTime,jdbcType=INTEGER}
    )
    </foreach>

  </insert>
</mapper>
