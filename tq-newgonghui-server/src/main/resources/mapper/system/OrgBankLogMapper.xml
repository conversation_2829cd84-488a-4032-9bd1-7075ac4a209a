<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.OrgBankLogMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.OrgBankLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="old_account" jdbcType="VARCHAR" property="oldAccount" />
    <result column="new_account" jdbcType="VARCHAR" property="newAccount" />
    <result column="old_address" jdbcType="VARCHAR" property="oldAddress" />
    <result column="new_address" jdbcType="VARCHAR" property="newAddress" />
    <result column="old_bank_name" jdbcType="VARCHAR" property="oldBankName" />
    <result column="new_bank_name" jdbcType="VARCHAR" property="newBankName" />
    <result column="old_bank" jdbcType="VARCHAR" property="oldBank" />
    <result column="new_bank" jdbcType="VARCHAR" property="newBank" />
    <result column="old_account_name" jdbcType="VARCHAR" property="oldAccountName" />
    <result column="new_account_name" jdbcType="VARCHAR" property="newAccountName" />
    <result column="create_operator" jdbcType="VARCHAR" property="createOperator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="relevance_id" jdbcType="BIGINT" property="relevanceId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, old_account, new_account, old_address, new_address, old_bank_name, new_bank_name, 
    old_bank, new_bank, old_account_name, new_account_name, create_operator, create_time, relevance_id
  </sql>

  <select id="getLastByRelevanceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    org_bank_log
    where relevance_id = #{relevanceId}
    order by id desc limit 1
  </select>
</mapper>