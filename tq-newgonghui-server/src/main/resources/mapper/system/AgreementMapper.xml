<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.AgreementMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.Agreement" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="agr_id" property="agrId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, org_id, agr_id, create_time
  </sql>
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.system.entity.Agreement" >
    insert into organization_agreement
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="agrId != null" >
        agr_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="agrId != null" >
        #{agrId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>


  <insert id="insertBatch"  parameterType="java.util.List" >
    insert into organization_agreement (agr_id,org_id, create_time)
    values
    <foreach collection="list" item="item" index="index"
             separator=",">
      (
      #{item.agrId,jdbcType=BIGINT},
      #{item.orgId,jdbcType=BIGINT},
      #{item.createTime,jdbcType=INTEGER}
      )
    </foreach>

  </insert>
</mapper>
