<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.OrganizationMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.Organization">
    <id column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="org_uuid" jdbcType="VARCHAR" property="orgUuid" />
    <result column="charge_person" jdbcType="VARCHAR" property="chargePerson" />
    <result column="charge_person_id_card" jdbcType="VARCHAR" property="chargePersonIdCard" />
    <result column="charge_person_phone" jdbcType="VARCHAR" property="chargePersonPhone" />
    <result column="charge_person_email" jdbcType="VARCHAR" property="chargePersonEmail" />
    <result column="charge_person_birthday" jdbcType="INTEGER" property="chargePersonBirthday" />
    <result column="receiving_address" jdbcType="VARCHAR" property="receivingAddress" />
    <result column="legal_person" jdbcType="VARCHAR" property="legalPerson" />
    <result column="legal_person_id_card" jdbcType="VARCHAR" property="legalPersonIdCard" />
    <result column="public_receiving_bank_account" jdbcType="VARCHAR" property="publicReceivingBankAccount" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_bank_name" jdbcType="VARCHAR" property="accountBankName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_id" jdbcType="INTEGER" property="provinceId" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="sub_branch_name" jdbcType="VARCHAR" property="subBranchName" />
    <result column="org_status" jdbcType="INTEGER" property="orgStatus" />
    <result column="apply_status" jdbcType="INTEGER" property="applyStatus" />
    <result column="modify_status" jdbcType="INTEGER" property="modifyStatus" />
    <result column="business_person" jdbcType="VARCHAR" property="businessPerson" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
    <result column="charge_person_vx" jdbcType="VARCHAR" property="chargePersonVx" />
    <result column="form_status" jdbcType="INTEGER" property="formStatus" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="audit_msg" jdbcType="VARCHAR" property="auditMsg" />
    <result column="audit_person" jdbcType="VARCHAR" property="auditPerson" />
    <result column="charge_person_phone_cipher" jdbcType="VARCHAR" property="chargePersonPhoneCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="charge_person_email_cipher" jdbcType="VARCHAR" property="chargePersonEmailCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="charge_person_cipher" jdbcType="VARCHAR" property="chargePersonCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="charge_person_vx_cipher" jdbcType="VARCHAR" property="chargePersonVxCipher" />
    <result column="charge_person_id_card_cipher" jdbcType="VARCHAR" property="chargePersonIdCardCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="receiving_address_cipher" jdbcType="VARCHAR" property="receivingAddressCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="legal_person_cipher" jdbcType="VARCHAR" property="legalPersonCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="legal_person_id_card_cipher" jdbcType="VARCHAR" property="legalPersonIdCardCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="public_receiving_bank_account_cipher" jdbcType="VARCHAR" property="publicReceivingBankAccountCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="account_name_cipher" jdbcType="VARCHAR" property="accountNameCipher" />
    <result column="account_bank_name_cipher" jdbcType="VARCHAR" property="accountBankNameCipher" />
    <result column="sub_branch_name_cipher" jdbcType="VARCHAR" property="subBranchNameCipher" />
    <result column="business_person_cipher" jdbcType="VARCHAR" property="businessPersonCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="join_time" jdbcType="INTEGER" property="joinTime" />
    <result column="join_source" jdbcType="INTEGER" property="joinSource" />
    <result column="live_permissions" jdbcType="INTEGER" property="livePermissions" />
    <result column="quliao_permissions" jdbcType="INTEGER" property="quliaoPermissions" />
    <result column="chat_room__permissions" jdbcType="INTEGER" property="chatRoomPermissions" />
    <result column="social_unified_credit_code" jdbcType="VARCHAR" property="socialUnifiedCreditCode" />
    <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />
    <result column="legal_person_phone" jdbcType="VARCHAR" property="legalPersonPhone" />
    <result column="apply_log" jdbcType="VARCHAR" property="applyLog" />
    <result column="premises" jdbcType="VARCHAR" property="premises" />
    <result column="settlemente_type" jdbcType="INTEGER" property="settlementeType" />
    <result column="live_settlemente_type" jdbcType="INTEGER" property="liveSettlementeType" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="credit_grade" jdbcType="VARCHAR" property="creditGrade" />
    <result column="legal_person_phone_cipher" jdbcType="VARCHAR" property="legalPersonPhoneCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="contact_phone_cipher" jdbcType="VARCHAR" property="contactPhoneCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
  </resultMap>

  <resultMap id="RemitResultMap" type="cn.taqu.gonghui.system.entity.Organization">
    <id column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="public_receiving_bank_account" jdbcType="VARCHAR" property="publicReceivingBankAccount" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_bank_name" jdbcType="VARCHAR" property="accountBankName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_id" jdbcType="INTEGER" property="provinceId" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="sub_branch_name" jdbcType="VARCHAR" property="subBranchName" />
    <result column="remit_modify_status" jdbcType="INTEGER" property="remitModifyStatus" />
    <result column="public_receiving_bank_account_cipher" jdbcType="VARCHAR" property="publicReceivingBankAccountCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    <result column="account_name_cipher" jdbcType="VARCHAR" property="accountNameCipher" />
    <result column="account_bank_name_cipher" jdbcType="VARCHAR" property="accountBankNameCipher" />
    <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />
  </resultMap>
  <resultMap id="OrgLegalPersonPhoneMap" type="cn.taqu.gonghui.system.entity.OrgLegalPersonPhone">
    <id column="org_id" jdbcType="BIGINT" property="orgId"/>
    <result column="legal_person_phone" jdbcType="VARCHAR" property="legalPersonPhone"/>
  </resultMap>

  <sql id="Base_Column_List">
    org_id, org_name, org_uuid, charge_person, charge_person_id_card, charge_person_phone,
    charge_person_email, charge_person_birthday, receiving_address, legal_person, legal_person_id_card,
    public_receiving_bank_account, account_name, account_bank_name, province, province_id,
    city, city_id, sub_branch_name, org_status, apply_status, modify_status, remit_modify_status,business_person, create_time,
    update_time, charge_person_vx, form_status, account_uuid, audit_msg, charge_person_phone_cipher,
    charge_person_email_cipher, charge_person_cipher, charge_person_vx_cipher, charge_person_id_card_cipher,
    receiving_address_cipher, legal_person_cipher, legal_person_id_card_cipher, public_receiving_bank_account_cipher,
    account_name_cipher, account_bank_name_cipher, sub_branch_name_cipher, business_person_cipher,
    join_time, join_source, live_permissions, quliao_permissions, chat_room__permissions,
    social_unified_credit_code, enterprise_name, legal_person_phone, apply_log, premises,
    settlemente_type,live_settlemente_type,contact_phone,content,credit_grade,
    legal_person_phone_cipher,contact_phone_cipher
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from organization
    where org_id = #{orgId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from organization
    where org_id = #{orgId,jdbcType=INTEGER}
  </delete>

<!--  <insert id="insert" useGeneratedKeys="true" keyProperty="org_id" parameterType="cn.taqu.gonghui.system.entity.Organization">-->
<!--    insert into organization (-->
<!--          org_id,-->
<!--    org_name,-->
<!--    org_uuid,-->
<!--    charge_person, charge_person_id_card, charge_person_phone,-->
<!--    charge_person_email, charge_person_birthday,-->
<!--    receiving_address, legal_person, legal_person_id_card,-->
<!--    public_receiving_bank_account, account_name,-->
<!--    account_bank_name, province, province_id,-->
<!--    city, city_id, sub_branch_name,-->
<!--    org_status, apply_status, business_person,-->
<!--    create_time, update_time, charge_person_vx,-->
<!--    form_status, account_uuid, audit_msg,-->
<!--    charge_person_phone_cipher, charge_person_email_cipher,-->
<!--    charge_person_cipher, charge_person_vx_cipher,-->
<!--    charge_person_id_card_cipher, receiving_address_cipher,-->
<!--    legal_person_cipher, legal_person_id_card_cipher,-->
<!--    public_receiving_bank_account_cipher, account_name_cipher,-->
<!--    account_bank_name_cipher, sub_branch_name_cipher,-->
<!--    business_person_cipher, join_time, join_source,-->
<!--    live_permissions, quliao_permissions, chat_room__permissions,-->
<!--    social_unified_credit_code, enterprise_name,-->
<!--    legal_person_phone, apply_log, premises,-->
<!--    settlemente_type,live_settlemente_type,contact_phone,content,-->
<!--    legal_person_phone_cipher,contact_phone_cipher,charge_person_phone_digest,business_person_digest)-->
<!--    values (-->
<!--        #{orgId,jdbcType=INTEGER},-->
<!--    #{orgName},-->
<!--    #{orgUuid},-->
<!--    #{chargePerson,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{chargePersonIdCard,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{chargePersonPhone,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{chargePersonEmail,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{chargePersonBirthday,jdbcType=INTEGER},-->
<!--    #{receivingAddress,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{legalPerson,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{legalPersonIdCard,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{publicReceivingBankAccount,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{accountName},-->
<!--    #{accountBankName},-->
<!--    #{province},-->
<!--    #{provinceId,jdbcType=INTEGER},-->
<!--    #{city},-->
<!--    #{cityId,jdbcType=INTEGER},-->
<!--    #{subBranchName},-->
<!--    #{orgStatus,jdbcType=INTEGER},-->
<!--    #{applyStatus,jdbcType=INTEGER},-->
<!--    #{businessPerson,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{createTime,jdbcType=INTEGER},-->
<!--    #{updateTime,jdbcType=INTEGER},-->
<!--    #{chargePersonVx},-->
<!--    #{formStatus,jdbcType=INTEGER},-->
<!--    #{accountUuid},-->
<!--    #{auditMsg},-->
<!--    #{chargePersonPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{chargePersonEmailCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{chargePersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{chargePersonVxCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{chargePersonIdCardCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{receivingAddressCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{legalPersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{legalPersonIdCardCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{publicReceivingBankAccountCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{accountNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{accountBankNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{subBranchNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{businessPersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{joinTime,jdbcType=INTEGER},-->
<!--    #{joinSource,jdbcType=INTEGER},-->
<!--    #{livePermissions,jdbcType=INTEGER},-->
<!--    #{quliaoPermissions,jdbcType=INTEGER},-->
<!--    #{chatRoomPermissions,jdbcType=INTEGER},-->
<!--    #{socialUnifiedCreditCode},-->
<!--    #{enterpriseName},-->
<!--    #{legalPersonPhone,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{applyLog},-->
<!--    #{premises},-->
<!--    #{settlementeType,jdbcType=INTEGER},-->
<!--    #{liveSettlementeType,jdbcType=INTEGER},-->
<!--    #{contactPhone,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},-->
<!--    #{content},-->
<!--    #{legalPerson,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{contactPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--    #{chargePersonPhoneDigest,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler},-->
<!--    #{businessPersonDigest,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}-->
<!--    )-->
<!--  </insert>-->
<!--  <insert id="insert" parameterType="cn.taqu.gonghui.system.entity.Organization">-->
<!--    insert into organization (-->
<!--&lt;!&ndash;      org_id,&ndash;&gt;-->
<!--      org_name,-->
<!--      org_uuid,-->
<!--      charge_person, charge_person_id_card, charge_person_phone,-->
<!--      charge_person_email, charge_person_birthday,-->
<!--      receiving_address, legal_person, legal_person_id_card,-->
<!--      public_receiving_bank_account, account_name,-->
<!--      account_bank_name, province, province_id,-->
<!--      city, city_id, sub_branch_name,-->
<!--      org_status, apply_status, business_person,-->
<!--      create_time, update_time, charge_person_vx,-->
<!--      form_status, account_uuid, audit_msg,-->
<!--      charge_person_phone_cipher, charge_person_email_cipher,-->
<!--      charge_person_cipher, charge_person_vx_cipher,-->
<!--      charge_person_id_card_cipher, receiving_address_cipher,-->
<!--      legal_person_cipher, legal_person_id_card_cipher,-->
<!--      public_receiving_bank_account_cipher, account_name_cipher,-->
<!--      account_bank_name_cipher, sub_branch_name_cipher,-->
<!--      business_person_cipher, join_time, join_source,-->
<!--      live_permissions, quliao_permissions, chat_room__permissions,-->
<!--      social_unified_credit_code, enterprise_name,-->
<!--      legal_person_phone, apply_log, premises,-->
<!--      settlemente_type,live_settlemente_type,contact_phone,content,-->
<!--      legal_person_phone_cipher,contact_phone_cipher)-->
<!--    values (-->
<!--&lt;!&ndash;    #{orgId,jdbcType=INTEGER},&ndash;&gt;-->
<!--    #{orgName,jdbcType=VARCHAR},-->
<!--    #{orgUuid,jdbcType=VARCHAR},-->
<!--      #{chargePerson,jdbcType=VARCHAR}, #{chargePersonIdCard,jdbcType=VARCHAR}, #{chargePersonPhone,jdbcType=VARCHAR},-->
<!--      #{chargePersonEmail,jdbcType=VARCHAR}, #{chargePersonBirthday,jdbcType=INTEGER},-->
<!--      #{receivingAddress,jdbcType=VARCHAR}, #{legalPerson,jdbcType=VARCHAR}, #{legalPersonIdCard,jdbcType=VARCHAR},-->
<!--      #{publicReceivingBankAccount,jdbcType=VARCHAR}, #{accountName,jdbcType=VARCHAR},-->
<!--      #{accountBankName,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{provinceId,jdbcType=INTEGER},-->
<!--      #{city,jdbcType=VARCHAR}, #{cityId,jdbcType=INTEGER}, #{subBranchName,jdbcType=VARCHAR},-->
<!--      #{orgStatus,jdbcType=INTEGER}, #{applyStatus,jdbcType=INTEGER}, #{businessPerson,jdbcType=VARCHAR},-->
<!--      #{createTime,jdbcType=INTEGER}, #{updateTime,jdbcType=INTEGER}, #{chargePersonVx,jdbcType=VARCHAR},-->
<!--      #{formStatus,jdbcType=INTEGER}, #{accountUuid,jdbcType=VARCHAR}, #{auditMsg,jdbcType=VARCHAR},-->
<!--      #{chargePersonPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{chargePersonEmailCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{chargePersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{chargePersonVxCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{chargePersonIdCardCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{receivingAddressCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{legalPersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{legalPersonIdCardCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{publicReceivingBankAccountCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{accountNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{accountBankNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{subBranchNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{businessPersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{joinTime,jdbcType=INTEGER}, #{joinSource,jdbcType=INTEGER},-->
<!--      #{livePermissions,jdbcType=INTEGER}, #{quliaoPermissions,jdbcType=INTEGER}, #{chatRoomPermissions,jdbcType=INTEGER},-->
<!--      #{socialUnifiedCreditCode,jdbcType=VARCHAR}, #{enterpriseName,jdbcType=VARCHAR},-->
<!--      #{legalPersonPhone,jdbcType=VARCHAR}, #{applyLog,jdbcType=VARCHAR}, #{premises,jdbcType=VARCHAR},-->
<!--      #{settlementeType,jdbcType=INTEGER},#{liveSettlementeType,jdbcType=INTEGER}, #{contactPhone,jdbcType=VARCHAR},#{content,jdbcType=VARCHAR},-->
<!--      #{legalPerson,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},-->
<!--      #{contactPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler}-->
<!--           )-->
<!--  </insert>-->
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.system.entity.Organization" useGeneratedKeys="true" keyProperty="orgId">
    insert into organization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="orgUuid != null">
        org_uuid,
      </if>
      <if test="chargePerson != null">
        charge_person,
      </if>
      <if test="chargePersonIdCard != null">
        charge_person_id_card,
      </if>
      <if test="chargePersonPhone != null">
        charge_person_phone,
      </if>
      <if test="chargePersonEmail != null">
        charge_person_email,
      </if>
      <if test="chargePersonBirthday != null">
        charge_person_birthday,
      </if>
      <if test="receivingAddress != null">
        receiving_address,
      </if>
      <if test="legalPerson != null">
        legal_person,
      </if>
      <if test="legalPersonIdCard != null">
        legal_person_id_card,
      </if>
      <if test="publicReceivingBankAccount != null">
        public_receiving_bank_account,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="accountBankName != null">
        account_bank_name,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="subBranchName != null">
        sub_branch_name,
      </if>
      <if test="orgStatus != null">
        org_status,
      </if>
      <if test="applyStatus != null">
        apply_status,
      </if>
      <if test="businessPerson != null">
        business_person,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="chargePersonVx != null">
        charge_person_vx,
      </if>
      <if test="formStatus != null">
        form_status,
      </if>
      <if test="accountUuid != null">
        account_uuid,
      </if>
      <if test="auditMsg != null">
        audit_msg,
      </if>
      <if test="chargePersonPhoneCipher != null">
        charge_person_phone_cipher,
      </if>
      <if test="chargePersonEmailCipher != null">
        charge_person_email_cipher,
      </if>
      <if test="chargePersonCipher != null">
        charge_person_cipher,
      </if>
      <if test="chargePersonVxCipher != null">
        charge_person_vx_cipher,
      </if>
      <if test="chargePersonIdCardCipher != null">
        charge_person_id_card_cipher,
      </if>
      <if test="receivingAddressCipher != null">
        receiving_address_cipher,
      </if>
      <if test="legalPersonCipher != null">
        legal_person_cipher,
      </if>
      <if test="legalPersonIdCardCipher != null">
        legal_person_id_card_cipher,
      </if>
      <if test="publicReceivingBankAccountCipher != null">
        public_receiving_bank_account_cipher,
      </if>
      <if test="accountNameCipher != null">
        account_name_cipher,
      </if>
      <if test="accountBankNameCipher != null">
        account_bank_name_cipher,
      </if>
      <if test="subBranchNameCipher != null">
        sub_branch_name_cipher,
      </if>
      <if test="businessPersonCipher != null">
        business_person_cipher,
      </if>
      <if test="joinTime != null">
        join_time,
      </if>
      <if test="joinSource != null">
        join_source,
      </if>
      <if test="livePermissions != null">
        live_permissions,
      </if>
      <if test="quliaoPermissions != null">
        quliao_permissions,
      </if>
      <if test="chatRoomPermissions != null">
        chat_room__permissions,
      </if>
      <if test="socialUnifiedCreditCode != null">
        social_unified_credit_code,
      </if>
      <if test="enterpriseName != null">
        enterprise_name,
      </if>
      <if test="legalPersonPhone != null">
        legal_person_phone,
      </if>
      <if test="applyLog != null">
        apply_log,
      </if>
      <if test="premises != null">
        premises,
      </if>
      <if test="settlementeType != null">
        settlemente_type,
      </if>
      <if test="liveSettlementeType != null">
        live_settlemente_type,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="legalPersonPhoneCipher != null">
        legal_person_phone_cipher,
      </if>
      <if test="contactPhoneCipher != null">
        contact_phone_cipher,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        #{orgName},
      </if>
      <if test="orgUuid != null">
        #{orgUuid},
      </if>
      <if test="chargePerson != null">
        #{chargePerson,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="chargePersonIdCard != null">
        #{chargePersonIdCard,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="chargePersonPhone != null">
        #{chargePersonPhone,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="chargePersonEmail != null">
        #{chargePersonEmail,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="chargePersonBirthday != null">
        #{chargePersonBirthday,jdbcType=INTEGER},
      </if>
      <if test="receivingAddress != null">
        #{receivingAddress,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="legalPerson != null">
        #{legalPerson,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="legalPersonIdCard != null">
        #{legalPersonIdCard,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="publicReceivingBankAccount != null">
        #{publicReceivingBankAccount,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="accountName != null">
        #{accountName},
      </if>
      <if test="accountBankName != null">
        #{accountBankName},
      </if>
      <if test="province != null">
        #{province},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="city != null">
        #{city},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="subBranchName != null">
        #{subBranchName},
      </if>
      <if test="orgStatus != null">
        #{orgStatus,jdbcType=INTEGER},
      </if>
      <if test="applyStatus != null">
        #{applyStatus,jdbcType=INTEGER},
      </if>
      <if test="businessPerson != null">
        #{businessPerson,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="chargePersonVx != null">
        #{chargePersonVx},
      </if>
      <if test="formStatus != null">
        #{formStatus,jdbcType=INTEGER},
      </if>
      <if test="accountUuid != null">
        #{accountUuid},
      </if>
      <if test="auditMsg != null">
        #{auditMsg},
      </if>
      <if test="chargePersonPhoneCipher != null">
        #{chargePersonPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="chargePersonEmailCipher != null">
        #{chargePersonEmailCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="chargePersonCipher != null">
        #{chargePersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="chargePersonVxCipher != null">
        #{chargePersonVxCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="chargePersonIdCardCipher != null">
        #{chargePersonIdCardCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="receivingAddressCipher != null">
        #{receivingAddressCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="legalPersonCipher != null">
        #{legalPersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="legalPersonIdCardCipher != null">
        #{legalPersonIdCardCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="publicReceivingBankAccountCipher != null">
        #{publicReceivingBankAccountCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="accountNameCipher != null">
        #{accountNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="accountBankNameCipher != null">
        #{accountBankNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="subBranchNameCipher != null">
        #{subBranchNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="businessPersonCipher != null">
        #{businessPersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="joinTime != null">
        #{joinTime,jdbcType=INTEGER},
      </if>
      <if test="joinSource != null">
        #{joinSource,jdbcType=INTEGER},
      </if>
      <if test="livePermissions != null">
        #{livePermissions},
      </if>
      <if test="quliaoPermissions != null">
        #{quliaoPermissions},
      </if>
      <if test="chatRoomPermissions != null">
        #{chatRoomPermissions},
      </if>
      <if test="socialUnifiedCreditCode != null">
        #{socialUnifiedCreditCode},
      </if>
      <if test="enterpriseName != null">
        #{enterpriseName},
      </if>
      <if test="legalPersonPhone != null">
        #{legalPersonPhone,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="applyLog != null">
        #{applyLog},
      </if>
      <if test="premises != null">
        #{premises},
      </if>
      <if test="settlementeType != null">
        #{settlementeType,jdbcType=INTEGER},
      </if>
      <if test="liveSettlementeType != null">
        #{liveSettlementeType,jdbcType=INTEGER},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="content != null">
        #{content},
      </if>
      <if test="legalPersonPhoneCipher != null">
        #{legalPerson,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="contactPhoneCipher != null">
        #{contactPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="chargePersonPhoneDigest != null">
        #{chargePersonPhoneDigest,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler},
      </if>
      <if test="businessPersonDigest != null">
        #{businessPersonDigest,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.system.entity.Organization">
    update organization
    <set>
      <if test="orgName != null">
        org_name = #{orgName},
      </if>
      <if test="orgUuid != null">
        org_uuid = #{orgUuid},
      </if>
      <if test="chargePerson != null">
        charge_person = #{chargePerson,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="chargePersonIdCard != null">
        charge_person_id_card = #{chargePersonIdCard,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="chargePersonPhone != null">
        charge_person_phone = #{chargePersonPhone,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="chargePersonEmail != null">
        charge_person_email = #{chargePersonEmail,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="chargePersonBirthday != null">
        charge_person_birthday = #{chargePersonBirthday,jdbcType=INTEGER},
      </if>
      <if test="receivingAddress != null">
        receiving_address = #{receivingAddress,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="legalPerson != null">
        legal_person = #{legalPerson,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="legalPersonIdCard != null">
        legal_person_id_card = #{legalPersonIdCard,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="publicReceivingBankAccount != null">
        public_receiving_bank_account = #{publicReceivingBankAccount,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="accountName != null">
        account_name = #{accountName},
      </if>
      <if test="accountBankName != null">
        account_bank_name = #{accountBankName},
      </if>
      <if test="province != null">
        province = #{province},
      </if>
      <if test="provinceId != null">
        province_id = #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="city != null">
        city = #{city},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=INTEGER},
      </if>
      <if test="subBranchName != null">
        sub_branch_name = #{subBranchName},
      </if>
      <if test="orgStatus != null">
        org_status = #{orgStatus,jdbcType=INTEGER},
      </if>
      <if test="applyStatus != null">
        apply_status = #{applyStatus,jdbcType=INTEGER},
      </if>
      <if test="modifyStatus != null">
        modify_status = #{modifyStatus,jdbcType=INTEGER},
      </if>
      <if test="remitModifyStatus != null">
        remit_modify_status = #{remitModifyStatus,jdbcType=INTEGER},
      </if>
      <if test="businessPerson != null">
        business_person = #{businessPerson,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="chargePersonVx != null">
        charge_person_vx = #{chargePersonVx},
      </if>
      <if test="formStatus != null">
        form_status = #{formStatus,jdbcType=INTEGER},
      </if>
      <if test="accountUuid != null">
        account_uuid = #{accountUuid},
      </if>
      <if test="auditMsg != null">
        audit_msg = #{auditMsg},
      </if>
      <if test="auditPerson != null">
        audit_person = #{auditPerson},
      </if>
      <if test="chargePersonPhoneCipher != null">
        charge_person_phone_cipher = #{chargePersonPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="chargePersonEmailCipher != null">
        charge_person_email_cipher = #{chargePersonEmailCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="chargePersonCipher != null">
        charge_person_cipher = #{chargePersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="chargePersonVxCipher != null">
        charge_person_vx_cipher = #{chargePersonVxCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="chargePersonIdCardCipher != null">
        charge_person_id_card_cipher = #{chargePersonIdCardCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="receivingAddressCipher != null">
        receiving_address_cipher = #{receivingAddressCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="legalPersonCipher != null">
        legal_person_cipher = #{legalPersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="legalPersonIdCardCipher != null">
        legal_person_id_card_cipher = #{legalPersonIdCardCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="publicReceivingBankAccountCipher != null">
        public_receiving_bank_account_cipher = #{publicReceivingBankAccountCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="accountNameCipher != null">
        account_name_cipher = #{accountNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="accountBankNameCipher != null">
        account_bank_name_cipher = #{accountBankNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="subBranchNameCipher != null">
        sub_branch_name_cipher = #{subBranchNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="businessPersonCipher != null">
        business_person_cipher = #{businessPersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="joinTime != null">
        join_time = #{joinTime,jdbcType=INTEGER},
      </if>
      <if test="joinSource != null">
        join_source = #{joinSource,jdbcType=INTEGER},
      </if>
      <if test="livePermissions != null">
        live_permissions = #{livePermissions,jdbcType=INTEGER},
      </if>
      <if test="quliaoPermissions != null">
        quliao_permissions = #{quliaoPermissions,jdbcType=INTEGER},
      </if>
      <if test="chatRoomPermissions != null">
        chat_room__permissions = #{chatRoomPermissions,jdbcType=INTEGER},
      </if>
      <if test="socialUnifiedCreditCode != null">
        social_unified_credit_code = #{socialUnifiedCreditCode},
      </if>
      <if test="enterpriseName != null">
        enterprise_name = #{enterpriseName},
      </if>
      <if test="legalPersonPhone != null">
        legal_person_phone = #{legalPersonPhone,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="applyLog != null">
        apply_log = #{applyLog},
      </if>
      <if test="premises != null">
        premises = #{premises},
      </if>
      <if test="settlementeType != null">
        settlemente_type = #{settlementeType,jdbcType=INTEGER},
      </if>
      <if test="liveSettlementeType != null">
        live_settlemente_type = #{liveSettlementeType,jdbcType=INTEGER},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      </if>
      <if test="content != null">
        content = #{content},
      </if>
      <if test="creditGrade != null">
        credit_grade = #{creditGrade,jdbcType=INTEGER},
      </if>
      <if test="contactPhoneCipher != null">
        contact_phone_cipher = #{contactPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="legalPersonPhoneCipher != null">
        legal_person_phone_cipher = #{legalPersonPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      </if>
      <if test="chargePersonPhoneDigest != null">
        charge_person_phone_digest = #{chargePersonPhoneDigest,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler},
      </if>
      <if test="businessPersonDigest != null">
        business_person_digest = #{businessPersonDigest,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler},
      </if>
    </set>
    where org_id = #{orgId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.system.entity.Organization">
    update organization
    set org_name = #{orgName},
      org_uuid = #{orgUuid},
      charge_person = #{chargePerson,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      charge_person_id_card = #{chargePersonIdCard,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      charge_person_phone = #{chargePersonPhone,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      charge_person_email = #{chargePersonEmail,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      charge_person_birthday = #{chargePersonBirthday,jdbcType=INTEGER},
      receiving_address = #{receivingAddress,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      legal_person = #{legalPerson,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      legal_person_id_card = #{legalPersonIdCard,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      public_receiving_bank_account = #{publicReceivingBankAccount,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      account_name = #{accountName},
      account_bank_name = #{accountBankName},
      province = #{province},
      province_id = #{provinceId,jdbcType=INTEGER},
      city = #{city},
      city_id = #{cityId,jdbcType=INTEGER},
      sub_branch_name = #{subBranchName},
      org_status = #{orgStatus,jdbcType=INTEGER},
      apply_status = #{applyStatus,jdbcType=INTEGER},
      business_person = #{businessPerson,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER},
      charge_person_vx = #{chargePersonVx},
      form_status = #{formStatus,jdbcType=INTEGER},
      account_uuid = #{accountUuid},
      audit_msg = #{auditMsg},
      charge_person_phone_cipher = #{chargePersonPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      charge_person_email_cipher = #{chargePersonEmailCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      charge_person_cipher = #{chargePersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      charge_person_vx_cipher = #{chargePersonVxCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      charge_person_id_card_cipher = #{chargePersonIdCardCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      receiving_address_cipher = #{receivingAddressCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      legal_person_cipher = #{legalPersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      legal_person_id_card_cipher = #{legalPersonIdCardCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      public_receiving_bank_account_cipher = #{publicReceivingBankAccountCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      account_name_cipher = #{accountNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      account_bank_name_cipher = #{accountBankNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      sub_branch_name_cipher = #{subBranchNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      business_person_cipher = #{businessPersonCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      join_time = #{joinTime,jdbcType=INTEGER},
      join_source = #{joinSource,jdbcType=INTEGER},
      live_permissions = #{livePermissions,jdbcType=INTEGER},
      quliao_permissions = #{quliaoPermissions,jdbcType=INTEGER},
      chat_room__permissions = #{chatRoomPermissions,jdbcType=INTEGER},
      social_unified_credit_code = #{socialUnifiedCreditCode},
      enterprise_name = #{enterpriseName},
      legal_person_phone = #{legalPersonPhone,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      apply_log = #{applyLog},
      premises = #{premises},
      settlemente_type = #{settlementeType,jdbcType=INTEGER},
      live_settlemente_type = #{liveSettlementeType,jdbcType=INTEGER},
      contact_phone = #{contactPhone,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
      legal_person_phone_cipher = #{legalPersonPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      contact_phone_cipher = #{contactPhoneCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
      charge_person_phone_digest = #{chargePersonPhoneDigest,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler},
      business_person_digest = #{businessPersonDigest,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
    where org_id = #{orgId,jdbcType=INTEGER}
  </update>

  <select id="findOrgInfoPageList" resultMap="BaseResultMap" parameterType="cn.taqu.gonghui.system.search.OrganizationInfoSearch">
  SELECT <include refid="Base_Column_List"/>
  FROM organization where form_status = 6 and apply_status != 4
    <if test="applyStatus != null">
      and apply_status = #{applyStatus,jdbcType=INTEGER}
    </if>
    <if test="mobile != null and mobile != ''">
      <if test="selectByDigest != null and selectByDigest == 0">
        and charge_person_phone like CONCAT('%',#{mobile},'%')
      </if>
      <if test="selectByDigest != null and selectByDigest == 1">
        and charge_person_phone_digest =
        #{mobile,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
      </if>
    </if>
    <if test="orgStatus != null">
      and org_status = #{orgStatus,jdbcType=INTEGER}
    </if>
    <if test="orgName != null">
      and org_name like CONCAT('%',#{orgName},'%')
    </if>
    <if test="enterpriseName != null">
      and enterprise_name like CONCAT('%',#{enterpriseName},'%')
    </if>
    <if test="orgUuid != null">
      and org_uuid = #{orgUuid}
    </if>
    <if test="startTime != null">
      and create_time <![CDATA[ >=  ]]> #{startTime}
    </if>
    <if test="endTime != null">
      and create_time <![CDATA[ <=  ]]> #{endTime}
    </if>
    order by org_id desc
  </select>

  <select id="findPassOrgInfoPageList" resultMap="BaseResultMap" parameterType="cn.taqu.gonghui.system.search.OrganizationInfoSearch">
    SELECT <include refid="Base_Column_List"/>
    FROM organization where 1=1
      <if test="applyStatus != null">
        and apply_status = #{applyStatus,jdbcType=INTEGER}
      </if>
    <if test="orgStatus != null">
      and org_status = #{orgStatus,jdbcType=INTEGER}
    </if>
    <if test="enterpriseName != null">
      and enterprise_name like CONCAT('%',#{enterpriseName},'%')
    </if>
      <if test="orgName != null">
        and org_name like CONCAT('%',#{orgName},'%')
      </if>
      <if test="orgUuid != null">
        and org_uuid = #{orgUuid}
      </if>
      <if test="orgId != null">
        and org_id = #{orgId,jdbcType=INTEGER}
      </if>
    <if test="mobile != null">
      <if test="selectByDigest != null and selectByDigest == 0">
        and charge_person_phone like CONCAT('%',#{mobile},'%')
      </if>
      <if test="selectByDigest != null and selectByDigest == 1">
        and charge_person_phone_digest =
        #{mobile,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
      </if>
    </if>
        <if test="mobile != null">
        </if>
    <if test="livePermissions != null">
      and live_permissions = #{livePermissions,jdbcType=INTEGER}
    </if>
    <if test="chatRoomPermissions != null">
      and chat_room__permissions = #{chatRoomPermissions,jdbcType=INTEGER}
    </if>
    <if test="businessPerson != null">
      <if test="selectByDigest != null and selectByDigest == 0">
        and business_person = #{businessPerson}
      </if>
    <if test="selectByDigest != null and selectByDigest == 1">
      and business_person_digest = #{businessPerson,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
     </if>
    </if>
    order by org_id desc
  </select>

  <select id="getByAccountUuid" resultMap="BaseResultMap" parameterType="java.lang.String">
    SELECT
    <include refid="Base_Column_List"/>
    FROM organization where org_status != 3 and apply_status != 5
      <if test="accountUuid!=null and accountUuid!=''">
        and account_uuid = #{accountUuid}
      </if>
  </select>


  <select id="getOrgInfo" resultMap="BaseResultMap" parameterType="cn.taqu.gonghui.system.entity.Organization">
    SELECT gi.org_id,gi.org_uuid,gi.org_id,gi.org_uuid, gi.org_name,gi.charge_person, gi.charge_person_id_card, gi.charge_person_phone, gi.charge_person_email,
    gi.charge_person_birthday, gi.receiving_address, gi.legal_person,gi.legal_person_id_card,gi.public_receiving_bank_account, gi.account_name,
    gi.account_bank_name,gi.province, gi.province_id,gi.city,gi.city_id,gi.sub_branch_name, gi.org_status,gi.apply_status, gi.modify_status, gi.create_time,
    gi.update_time, gi.business_person,gi.charge_person_vx,gi.form_status,gi.account_uuid,gi.audit_msg, gi.audit_person, gi.charge_person_phone_cipher, gi.contact_phone,
    gi.charge_person_email_cipher, gi.charge_person_cipher,gi.charge_person_vx_cipher, gi.charge_person_id_card_cipher, gi.receiving_address_cipher,gi.legal_person_cipher,
    gi.legal_person_id_card_cipher,gi.public_receiving_bank_account_cipher, gi.account_name_cipher,gi.account_bank_name_cipher,
    gi.sub_branch_name_cipher,gi.business_person_cipher,gi.social_unified_credit_code,gi.enterprise_name,gi.legal_person_phone,gi.apply_log,gi.premises,gi.settlemente_type,gi.live_settlemente_type,
    gi.live_permissions,gi.quliao_permissions,gi.chat_room__permissions,gi.content,gi.contact_phone_cipher,gi.legal_person_phone_cipher,
    GROUP_CONCAT( distinct(bl.url) ) AS business_license_url,GROUP_CONCAT( distinct(op.url) ) AS opening_permit_url, GROUP_CONCAT( distinct(cp.url) ) AS charge_person_url,
    GROUP_CONCAT( distinct(lp.url) ) AS legal_person_url,
    GROUP_CONCAT( distinct(gcf.url)) AS org_cooperation_flow_url,GROUP_CONCAT( distinct(hs.url) ) AS host_screenshot_url
    FROM organization gi  LEFT JOIN charge_person cp ON gi.org_uuid = cp.org_uuid
    LEFT JOIN legal_person lp ON gi.org_uuid = lp.org_uuid LEFT JOIN business_license bl ON gi.org_uuid = bl.org_uuid
    LEFT JOIN opening_permit op ON gi.org_uuid = op.org_uuid LEFT JOIN org_cooperation_flow gcf ON gi.org_uuid = gcf.org_uuid
    LEFT JOIN host_screenshot hs ON gi.org_uuid = hs.org_uuid
    <where>
      <if test="orgId!=null">
        gi.org_id = #{orgId,jdbcType=INTEGER}
      </if>
      <if test="orgUuid!=null">
        gi.org_uuid = #{orgUuid}
      </if>
      <if test="accountUuid!=null">
        gi.account_uuid = #{accountUuid}
      </if>
    </where>
  </select>

  <select id="findGuildInfoUuidAndNameList" resultMap="BaseResultMap">
    SELECT org_name,org_id
    FROM organization
    where org_status = 1 and apply_status = 2
    order by org_id desc
  </select>

  <select id="findOrgUuidAndNameTree" resultMap="BaseResultMap">
    SELECT org_name,org_uuid
    FROM organization
    where org_status = 1 and apply_status = 2
  </select>

  <select id="getByUuid" resultMap="BaseResultMap" parameterType="java.lang.String">
    SELECT
    <include refid="Base_Column_List"/>
    FROM organization
    <where>
      <if test="orgUuid!=null">
        org_uuid = #{orgUuid}
      </if>
    </where>
  </select>

  <select id="getBy" resultMap="BaseResultMap" parameterType="cn.taqu.gonghui.system.entity.Organization">
    SELECT
    <include refid="Base_Column_List"/>
    FROM organization
    <where>
      <if test="accountUuid!=null">
        account_uuid = #{accountUuid}
      </if>
      <if test="orgUuid!=null">
        org_uuid = #{orgUuid}
      </if>
      <if test="orgName != null">
        and org_name = #{orgName}
      </if>
    </where>
  </select>

  <select id="getByAccountUuidGroupConcatUrl" resultMap = "BaseResultMap" parameterType="java.lang.String">
    select gi.org_id, gi.org_uuid,gi.create_time,gi.join_time, gi.org_name, gi.charge_person,
    gi.charge_person_id_card, gi.charge_person_phone,
    gi.charge_person_email, gi.charge_person_birthday,
    gi.receiving_address, gi.legal_person, gi.legal_person_id_card,
    gi.public_receiving_bank_account, gi.account_name,
    gi.account_bank_name, gi.province, gi.province_id, gi.city,gi.contact_phone,
    gi.city_id, gi.sub_branch_name, gi.org_status, gi.apply_status, gi.modify_status, gi.audit_person,
    gi.business_person, gi.charge_person_vx, gi.form_status,
    gi.account_uuid, gi.audit_msg, gi.charge_person_phone_cipher,
    gi.charge_person_email_cipher, gi.charge_person_cipher,
    gi.charge_person_vx_cipher, gi.charge_person_id_card_cipher,
    gi.receiving_address_cipher, gi.legal_person_cipher,
    gi.legal_person_id_card_cipher, gi.public_receiving_bank_account_cipher,
    gi.account_name_cipher, gi.account_bank_name_cipher, gi.sub_branch_name_cipher,
    gi.business_person_cipher,gi.social_unified_credit_code,gi.enterprise_name,gi.legal_person_phone,
    gi.apply_log,gi.premises,gi.settlemente_type,gi.live_settlemente_type,
    gi.content,gi.legal_person_phone_cipher,gi.contact_phone_cipher,
    group_concat( distinct(bl.url) ) as business_license_url,
    group_concat( distinct(op.url) ) as opening_permit_url,
    group_concat( distinct(cp.url) ) as charge_person_url,
    group_concat( distinct(lp.url) ) as legal_person_url,
    group_concat( distinct(gcf.url)) as org_cooperation_flow_url,
    group_concat( distinct(hs.url) ) as host_screenshot_url
    from organization gi
    left join charge_person cp on gi.org_uuid = cp.org_uuid
    left join legal_person lp on gi.org_uuid = lp.org_uuid
    left join business_license bl on gi.org_uuid = bl.org_uuid
    left join opening_permit op on gi.org_uuid = op.org_uuid
    left join org_cooperation_flow gcf on gi.org_uuid = gcf.org_uuid
    left join host_screenshot hs on gi.org_uuid = hs.org_uuid
    where gi.org_status != 2 and gi.org_status != 3
    <if test="orgUuid!=null">
      and gi.org_uuid = #{orgUuid}
      </if>
  </select>


  <select id="getByList" resultMap="BaseResultMap" parameterType="cn.taqu.gonghui.system.entity.Organization">
    SELECT
    <include refid="Base_Column_List"/>
    FROM organization
    where apply_status != 5 and org_status != 2
      <if test="accountUuid!=null">
       and account_uuid = #{accountUuid}
      </if>
      <if test="orgUuid!=null">
        and org_uuid = #{orgUuid}
      </if>
      <if test="orgName != null">
        and org_name = #{orgName}
      </if>
    <if test="remitModifyStatus != null">
      and remit_modify_status = #{remitModifyStatus}
    </if>
  </select>

  <select id="getListByOrgIds" parameterType="java.util.Set" resultType="cn.taqu.gonghui.system.entity.Organization">
    select org_uuid,org_name from organization where org_uuid in
    <foreach collection="collection" item="orgId" open="(" close=")" separator=",">
      #{orgId}
    </foreach>
  </select>

  <select id="orgNameListByOrgIds" parameterType="java.util.Set" resultType="cn.taqu.gonghui.system.entity.Organization">
    select org_id,org_name from organization where org_id in
    <foreach collection="collection" item="orgId" open="(" close=")" separator=",">
      #{orgId}
    </foreach>
  </select>

  <select id="getByOrg" resultType="java.lang.String" parameterType="java.lang.String">
    SELECT org_uuid
    FROM organization
    where org_status = 1 and apply_status = 2 and business_person = #{businessPerson}
  </select>

  <select id="getNewOrgList" resultType="java.lang.String" parameterType="cn.taqu.gonghui.live.search.OrgStatisticsSearch">
    SELECT
    org_uuid
    FROM organization
    where apply_status = 2 and org_status = 1
    <if test="startTime!=null">
      and create_time  <![CDATA[ >= ]]> #{startTime}
    </if>
    <if test="endTime!=null">
      and create_time <![CDATA[ <= ]]> #{endTime}
    </if>
    <if test="businessPerson!=null">
      and business_person = #{businessPerson}
    </if>
  </select>

  <update id="updateLiveSettlementeType">
    update organization set live_settlemente_type = #{liveSettlementeType} where org_id = #{orgId}
  </update>

  <update id="updateContent">
    update organization set content = #{content} where org_id = #{orgId}
  </update>

<!-- 以下为数据迁移需要用到的接口 -->
  <select id="getOrgIdByOrgUuid" parameterType="java.lang.String" resultType="cn.taqu.gonghui.system.entity.Organization">
    SELECT org_id,org_name FROM organization WHERE org_uuid = #{orgUuid}
  </select>


  <select id="getMaxOrgUuid" resultType="long" parameterType="java.lang.String">
    SELECT
    max(org_uuid)
    FROM organization

  </select>


  <select id="getALLByList" resultType="cn.taqu.gonghui.system.entity.Organization">
    select
    <include refid="Base_Column_List"/>
     from organization
  </select>
  <select id="getOrganizationByJointime" resultType="cn.taqu.gonghui.system.entity.Organization">

    select
    <include refid="Base_Column_List"/>
    from organization
    where join_time  &gt;=  #{joinTimeStart}
     and join_time &lt;=  #{joinTimeEnd}
  </select>

  <update id="updateModifyStatusByOrgId">
    update organization set modify_status = #{modifyStatus} where org_id = #{orgId}
  </update>

  <select id="listRemitInfo" resultMap="RemitResultMap" parameterType="cn.taqu.gonghui.system.entity.Organization">
    SELECT
    org_id,
    org_name,
    org_uuid,
    account_name,
    account_bank_name,
    province,
    province_id,
    city,
    city_id,
    sub_branch_name,
    org_status,
    apply_status,
    modify_status,
    remit_modify_status,
    public_receiving_bank_account_cipher,
    account_name_cipher,
    account_bank_name_cipher,
    sub_branch_name_cipher,
    enterprise_name
    FROM organization
    where apply_status != 5 and org_status != 2
    <if test="accountUuid!=null">
      and account_uuid = #{accountUuid}
    </if>
    <if test="orgUuid!=null">
      and org_uuid = #{orgUuid}
    </if>
    <if test="orgName != null">
      and org_name = #{orgName}
    </if>
    <if test="remitModifyStatus != null">
      and remit_modify_status = #{remitModifyStatus}
    </if>
  </select>


  <select id="selectLegalPersonPhoneByRange"  resultMap="OrgLegalPersonPhoneMap">
    select org_id, legal_person_phone
    FROM organization
    WHERE org_id between #{curStartId,jdbcType=BIGINT} and #{curEndId,jdbcType=BIGINT}
  </select>

  <update id="batchUpdateLegalPersonPhoneCipher">
    <foreach collection="list" item="item" separator=";">
      UPDATE organization
      SET legal_person_phone_cipher = #{item.legalPersonPhoneCipher}
      WHERE org_id = #{item.orgId}
    </foreach>
  </update>

  <update id="updateLegalPersonPhoneCipher">
      UPDATE organization
      SET legal_person_phone_cipher = #{legalPersonPhoneCipher}
      WHERE org_id = #{orgId,jdbcType=BIGINT}

  </update>
</mapper>
