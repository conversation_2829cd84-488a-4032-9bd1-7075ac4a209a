<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.QuestionFeedbackMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.QuestionFeedback" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="feedback_uuid" property="feedbackUuid" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="team_id" property="teamId" jdbcType="BIGINT" />
    <result column="page_url" property="pageUrl" jdbcType="VARCHAR" />
    <result column="page_name" property="pageName" jdbcType="VARCHAR" />
    <result column="feedback_time" property="feedbackTime" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="content" property="content" jdbcType="LONGVARCHAR" />
    <result column="image_url" property="imageUrl" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="follow_up_person" property="followUpPerson" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, feedback_uuid, org_id, team_id, page_url, page_name, feedback_time, type, title,
    content, image_url, status, follow_up_person, remark, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from question_feedback
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from question_feedback
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.system.entity.QuestionFeedback" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into question_feedback
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="feedbackUuid != null" >
        feedback_uuid,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="teamId != null" >
        team_id,
      </if>
      <if test="pageUrl != null" >
        page_url,
      </if>
      <if test="pageName != null" >
        page_name,
      </if>
      <if test="feedbackTime != null" >
        feedback_time,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="title != null" >
        title,
      </if>
      <if test="content != null" >
        content,
      </if>
      <if test="imageUrl != null" >
        image_url,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="followUpPerson != null" >
        follow_up_person,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="feedbackUuid != null" >
        #{feedbackUuid},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="teamId != null" >
        #{teamId,jdbcType=BIGINT},
      </if>
      <if test="pageUrl != null" >
        #{pageUrl},
      </if>
      <if test="pageName != null" >
        #{pageName},
      </if>
      <if test="feedbackTime != null" >
        #{feedbackTime,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="title != null" >
        #{title},
      </if>
      <if test="content != null" >
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="imageUrl != null" >
        #{imageUrl},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="followUpPerson != null" >
        #{followUpPerson},
      </if>
      <if test="remark != null" >
        #{remark},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.system.entity.QuestionFeedback" >
    update question_feedback
    <set >
      <if test="feedbackUuid != null" >
        feedback_uuid = #{feedbackUuid},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="teamId != null" >
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="pageUrl != null" >
        page_url = #{pageUrl},
      </if>
      <if test="pageName != null" >
        page_name = #{pageName},
      </if>
      <if test="feedbackTime != null" >
        feedback_time = #{feedbackTime,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="title != null" >
        title = #{title},
      </if>
      <if test="content != null" >
        content = #{content},
      </if>
      <if test="imageUrl != null" >
        image_url = #{imageUrl},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="followUpPerson != null" >
        follow_up_person = #{followUpPerson},
      </if>
      <if test="remark != null" >
        remark = #{remark},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

<!-- 自定义查询 -->
  <select id="manageList" parameterType="cn.taqu.gonghui.system.search.QuestionFeedbackSearch" resultType="cn.taqu.gonghui.system.vo.QuestionFeedbackVo">
    SELECT
      t1.*,
      t2.user_id,
      t2.user_name,
      t2.mobile,
      t3.org_name,
      t4.team_name
    FROM
      question_feedback t1
        LEFT JOIN sys_user t2 ON t1.feedback_uuid = t2.account_uuid
        LEFT JOIN organization t3 ON t1.org_id = t3.org_id
        LEFT JOIN team t4 ON t1.team_id = t4.team_id
    <where>
      <if test="status != null">
        and t1.status = #{status}
      </if>
      <if test="title != null and title != ''">
        and t1.title like CONCAT('%',#{title},'%')
      </if>
      <if test="content != null and content != ''">
        and t1.content like CONCAT('%',#{content},'%')
      </if>
      <if test="startTime != null">
        and t1.feedback_time <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        and t1.feedback_time <![CDATA[ <= ]]> #{endTime}
      </if>
    order by t1.feedback_time desc
    </where>
  </select>

  <select id="userList" parameterType="cn.taqu.gonghui.system.search.QuestionFeedbackSearch" resultType="cn.taqu.gonghui.system.vo.QuestionFeedbackVo">
    SELECT
    t1.*,
    t2.user_id,
    t2.user_name,
    t2.mobile,
    t3.org_name,
    t4.team_name
    FROM
    question_feedback t1
    LEFT JOIN sys_user t2 ON t1.feedback_uuid = t2.account_uuid
    LEFT JOIN organization t3 ON t1.org_id = t3.org_id
    LEFT JOIN team t4 ON t1.team_id = t4.team_id
    where t1.feedback_uuid = #{account_uuid}
    order by t1.feedback_time desc
  </select>

  <update id="changeStatus" parameterType="cn.taqu.gonghui.system.dto.QuestionFeedbackDto">
    update question_feedback set
                                 status = #{status},
                                 follow_up_person = #{followUpPerson},
                                 remark = #{remark},
                                 update_time = #{updateTime}
    where id = #{id}
  </update>
</mapper>
