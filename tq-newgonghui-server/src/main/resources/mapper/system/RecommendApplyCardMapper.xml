<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.RecommendApplyCardMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.RecommendApplyCard" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="card_no" property="cardNo" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="card_create_time" property="cardCreateTime" jdbcType="INTEGER" />
    <result column="card_expire_time" property="cardExpireTime" jdbcType="INTEGER" />
    <result column="effective_days" property="effectiveDays" jdbcType="INTEGER" />
    <result column="use_num" property="useNum" jdbcType="INTEGER" />
    <result column="total_num" property="totalNum" jdbcType="INTEGER" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, card_no, org_id, card_create_time, card_expire_time, effective_days, use_num,
    total_num, create_user, status, create_time, update_time
  </sql>
  <update id="updateStatus">
    update recommend_apply_card set status = #{status} where id = #{id}
  </update>
<!-- 列表分页查询 -->
  <select id="queryByCondition" parameterType="cn.taqu.gonghui.system.search.RecommendApplyCardSearch" resultType="cn.taqu.gonghui.system.vo.RecommendApplyCardVo">
    SELECT
    gi.org_name,
    rac.id,
    rac.card_no,
    rac.org_id,
    rac.card_create_time,
    rac.card_expire_time,
    rac.effective_days,
    rac.use_num,
    rac.total_num,
    rac.create_user,
    rac.status,
    rac.create_time,
    rac.update_time
    FROM
    recommend_apply_card rac
    LEFT JOIN organization gi ON rac.org_id = gi.org_id
    WHERE 1 = 1
    <if test="startTime != null">
      and rac.card_create_time <![CDATA[ >=  ]]> #{startTime}
    </if>
    <if test="endTime != null">
      and rac.card_create_time <![CDATA[ <=  ]]> #{endTime}
    </if>
    <if test="status != null">
      and status = #{status}
    </if>
    <if test="orgId != null">
      and gi.org_id = #{orgId}
    </if>
    ORDER BY id DESC
  </select>

<!-- 以下mapper 为用户端使用 -->
  <select id="countByOrgId" resultType="java.lang.Integer">
    select count(1) from recommend_apply_card where status = #{status} and org_id = #{orgId}
  </select>

  <select id="findListByOrgId" resultMap="BaseResultMap">
    select * from recommend_apply_card where status = #{status} and org_id = #{orgId} order by card_expire_time asc
  </select>

  <select id="getReleaseCard" resultMap="BaseResultMap">
    select * from recommend_apply_card where status = #{status} and org_id = #{orgId} and use_num > 0 and card_expire_time <![CDATA[ >=  ]]> #{currentTime} order by card_expire_time asc
  </select>


  <select id="findUnavailableCard" resultMap="BaseResultMap">
    select * from recommend_apply_card where status = #{status} and card_expire_time <![CDATA[ <=  ]]> #{currentTime} order by card_expire_time asc
  </select>
</mapper>
