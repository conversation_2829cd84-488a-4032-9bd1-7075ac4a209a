<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.RecommendApplyCardUseLogMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.RecommendApplyCardUseLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="card_no" property="cardNo" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="BIGINT" />
    <result column="host_uuid" property="hostUuid" jdbcType="VARCHAR" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
    <result column="host_name" property="hostName" jdbcType="VARCHAR" />
    <result column="use_time" property="useTime" jdbcType="VARCHAR" />
    <result column="banner" property="banner" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, card_no, org_id, host_uuid, operator, create_time, update_time, host_name, use_time,
    banner
  </sql>
<!-- 根据卡编号查询卡的使用记录 -->
  <select id="queryByCondition" parameterType="cn.taqu.gonghui.system.search.RecommendApplyCardUseLogSearch" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from recommend_apply_card_use_log
    where 1 = 1
    <if test="cardNo != null and cardNo != ''">
      and card_no = #{cardNo}
    </if>
    order by id desc
  </select>
</mapper>
