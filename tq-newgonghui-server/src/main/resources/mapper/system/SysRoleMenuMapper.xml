<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.SysRoleMenuMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.SysRoleMenu" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="role_id" property="roleId" jdbcType="BIGINT" />
    <result column="menu_id" property="menuId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
  </resultMap>
<!-- 自定义查询 -->
  <select id="checkMenuExistRole" resultType="java.lang.Integer">
    select count(1) from sys_role_menu where menu_id = #{menuId}
  </select>

  <delete id="deleteRoleMenuByRoleId" parameterType="java.lang.Long">
    delete from sys_role_menu where role_id=#{roleId}
  </delete>

  <delete id="deleteRoleMenu" parameterType="java.lang.Long">
    delete from sys_role_menu where role_id in
    <foreach collection="array" item="roleId" open="(" separator="," close=")">
      #{roleId}
    </foreach>
  </delete>

  <insert id="batchRoleMenu">
    insert into sys_role_menu(id,role_id, menu_id,create_time,update_time) values
    <foreach item="item" index="index" collection="list" separator=",">
      (#{item.id},#{item.roleId},#{item.menuId},#{item.createTime},#{item.updateTime})
    </foreach>
  </insert>
</mapper>
