<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.FeishuAuditRelationMapper">
    <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.FeishuAuditRelation">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="approval_code" jdbcType="VARCHAR" property="approvalCode" />
        <result column="instance_code" jdbcType="VARCHAR" property="instanceCode" />
        <result column="business_id" jdbcType="BIGINT" property="businessId" />
        <result column="business_type" jdbcType="INTEGER" property="businessType" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="create_time" jdbcType="DATE" property="createTime" />
        <result column="modify_time" jdbcType="DATE" property="modifyTime" />
    </resultMap>

    <select id="getByApproval" resultType="cn.taqu.gonghui.system.entity.FeishuAuditRelation">
        select * from
            feishu_audit_relation
        where approval_code = #{approvalCode} and instance_code = #{instanceCode}
    </select>
</mapper>