<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.OrgAccountLogMapper">
    <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.OrgAccountLog">
        <id column="id" jdbcType="BIGINT" property="id" />
        <id column="relevance_id" jdbcType="BIGINT" property="relevanceId" />
        <result column="old_phone" jdbcType="VARCHAR" property="oldPhone" />
        <result column="new_phone" jdbcType="VARCHAR" property="newPhone" />
        <result column="old_principal" jdbcType="VARCHAR" property="oldPrincipal" />
        <result column="new_principal" jdbcType="VARCHAR" property="newPrincipal" />
        <result column="old_mobile_phone" jdbcType="VARCHAR" property="oldMobilePhone" />
        <result column="new_mobile_phone" jdbcType="VARCHAR" property="newMobilePhone" />
        <result column="old_weixin" jdbcType="VARCHAR" property="oldWeixin" />
        <result column="new_weixin" jdbcType="VARCHAR" property="newWeixin" />
        <result column="old_birth" jdbcType="VARCHAR" property="oldBirth" />
        <result column="new_birth" jdbcType="VARCHAR" property="newBirth" />
        <result column="old_identity" jdbcType="VARCHAR" property="oldIdentity" />
        <result column="new_identity" jdbcType="VARCHAR" property="newIdentity" />
        <result column="old_site" jdbcType="VARCHAR" property="oldSite" />
        <result column="new_site" jdbcType="VARCHAR" property="newSite" />
        <result column="old_mailbox" jdbcType="VARCHAR" property="oldMailbox" />
        <result column="new_mailbox" jdbcType="VARCHAR" property="newMailbox" />
        <result column="old_identity_front" jdbcType="VARCHAR" property="oldIdentityFront" />
        <result column="new_identity_front" jdbcType="VARCHAR" property="newIdentityFront" />
        <result column="old_identity_reverse" jdbcType="VARCHAR" property="oldIdentityReverse" />
        <result column="new_identity_reverse" jdbcType="VARCHAR" property="newIdentityReverse" />
        <result column="old_identity_hand" jdbcType="VARCHAR" property="oldIdentityHand" />
        <result column="new_identity_hand" jdbcType="VARCHAR" property="newIdentityHand" />
        <result column="create_operator" jdbcType="VARCHAR" property="createOperator" />
        <result column="create_time" jdbcType="INTEGER" property="createTime" />

        <result column="old_phone_cipher" jdbcType="VARCHAR" property="oldPhoneCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="new_phone_cipher" jdbcType="VARCHAR" property="newPhoneCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="old_principal_cipher" jdbcType="VARCHAR" property="oldPrincipalCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="new_principal_cipher" jdbcType="VARCHAR" property="newPrincipalCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="old_mobile_phone_cipher" jdbcType="VARCHAR" property="oldMobilePhoneCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>

        <result column="new_mobile_phone_cipher" jdbcType="VARCHAR" property="newMobilePhoneCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="old_identity_cipher" jdbcType="VARCHAR" property="oldIdentityCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="new_identity_cipher" jdbcType="VARCHAR" property="newIdentityCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="old_site_cipher" jdbcType="VARCHAR" property="oldSiteCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="new_site_cipher" jdbcType="VARCHAR" property="newSiteCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>

        <result column="old_mailbox_cipher" jdbcType="VARCHAR" property="oldMailboxCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="new_mailbox_cipher" jdbcType="VARCHAR" property="newMailboxCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="old_identity_front_cipher" jdbcType="VARCHAR" property="oldIdentityFrontCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="new_identity_front_cipher" jdbcType="VARCHAR" property="newIdentityFrontCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="old_identity_reverse_cipher" jdbcType="VARCHAR" property="oldIdentityReverseCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>

        <result column="new_identity_reverse_cipher" jdbcType="VARCHAR" property="newIdentityReverseCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="old_identity_hand_cipher" jdbcType="VARCHAR" property="oldIdentityHandCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
        <result column="new_identity_hand_cipher" jdbcType="VARCHAR" property="newIdentityHandCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, relevance_id, old_phone, new_phone, old_principal, new_principal, old_mobile_phone,new_mobile_phone,old_weixin,new_weixin,
        old_birth, new_birth, old_identity, new_identity, old_site, new_site, old_mailbox,new_mailbox,old_identity_front,new_identity_front,
        old_identity_reverse, new_identity_reverse, old_identity_hand, new_identity_hand, create_operator, create_time,

        old_phone_cipher,new_phone_cipher,old_principal_cipher,new_principal_cipher,old_mobile_phone_cipher,
        new_mobile_phone_cipher,old_identity_cipher,new_identity_cipher,old_site_cipher,new_site_cipher,
        old_mailbox_cipher,new_mailbox_cipher,old_identity_front_cipher,new_identity_front_cipher,
        old_identity_reverse_cipher,new_identity_reverse_cipher,old_identity_hand_cipher,new_identity_hand_cipher
    </sql>

    <select id="getLastByRelevanceId" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
            from
        org_account_log
        where relevance_id = #{relevanceId}
        order by id desc limit 1
    </select>

</mapper>