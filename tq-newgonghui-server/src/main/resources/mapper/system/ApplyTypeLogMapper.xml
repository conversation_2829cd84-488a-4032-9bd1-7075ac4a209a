<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.ApplyTypeLogMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.ApplyTypeLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operator_type" jdbcType="INTEGER" property="operatorType" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, org_id, operator, operator_type, msg, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from apply_type_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from apply_type_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="cn.taqu.gonghui.system.entity.ApplyTypeLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into apply_type_log (org_id, operator, operator_type, 
      msg, create_time, update_time
      )
    values (#{orgId,jdbcType=INTEGER}, #{operator,jdbcType=VARCHAR}, #{operatorType,jdbcType=INTEGER},
      #{msg,jdbcType=VARCHAR}, #{createTime,jdbcType=INTEGER}, #{updateTime,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.system.entity.ApplyTypeLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into apply_type_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="msg != null">
        msg,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        #{operator},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=INTEGER},
      </if>
      <if test="msg != null">
        #{msg},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.system.entity.ApplyTypeLog">
    update apply_type_log
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        operator = #{operator},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=INTEGER},
      </if>
      <if test="msg != null">
        msg = #{msg,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.system.entity.ApplyTypeLog">
    update apply_type_log
    set org_id = #{orgId,jdbcType=INTEGER},
      operator = #{operator},
      operator_type = #{operatorType,jdbcType=INTEGER},
      msg = #{msg},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByOrgId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from apply_type_log
    where org_id = #{orgId,jdbcType=INTEGER}
  </select>
</mapper>