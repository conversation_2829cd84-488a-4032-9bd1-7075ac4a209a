<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.AgreementInfoMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.AgreementInfo" >
    <id column="agr_id" property="agrId" jdbcType="BIGINT" />
    <result column="order_level" property="orderLevel" jdbcType="INTEGER" />
    <result column="version" property="version" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="content" property="content" jdbcType="LONGVARCHAR" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    agr_id, order_level, version, type,content, create_time, update_time, operator, title, valid
  </sql>
<!-- 获取协议详情 -->
  <select id="getById" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from organization_agreement_info
    <where>
      <if test="agrId != null">
        and agr_id = #{agrId}
      </if>
      <if test="valid != null">
        and valid = #{valid}
      </if>
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from organization_agreement_info
    where agr_id = #{agrId,jdbcType=BIGINT}
  </delete>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.system.entity.AgreementInfo" >
    update organization_agreement_info
    <set >
      <if test="orderLevel != null" >
        order_level = #{orderLevel,jdbcType=INTEGER},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="operator != null" >
        operator = #{operator},
      </if>
      <if test="title != null" >
        title = #{title},
      </if>
      <if test="valid != null" >
        valid = #{valid,jdbcType=INTEGER},
      </if>
    </set>
    where agr_id = #{agrId,jdbcType=BIGINT}
  </update>

<!-- 查询有效协议列表（无分页） -->
  <select id="findAllOrderByOrderLevelAsc" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from organization_agreement_info
    <where>
      <if test="valid != null">
        and valid = #{valid}
      </if>
      <if test="title != null and title != ''">
        and title like concat('%',#{title},'%')
      </if>
      <if test="types != null and types.size != 0">
        and type in
        <foreach collection="types" item="type" open="(" close=")" separator=",">
          #{type}
        </foreach>
      </if>
    </where>
    order by order_level asc
  </select>

<!-- 根据协议顺序获取协议 -->
  <select id="getByOrderLevel" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from organization_agreement_info
    where valid = 1 and order_level = #{orderLevel}
    order by order_level desc limit 1
  </select>

<!-- 获取机构签署协议记录列表（分页） -->
  <select id="findAgreementSignLogPageList" parameterType="cn.taqu.gonghui.system.search.AgreementSignLogSearch" resultType="cn.taqu.gonghui.system.vo.AgreementSignLogVo">
    SELECT
      a.org_id orgId,
      a.agr_id agrId,
      b.org_name orgName,
      c.valid valid,
      c.order_level orderLevel,
      c.version version
    FROM
      organization_agreement a
        LEFT JOIN organization b ON a.org_id = b.org_id
        LEFT JOIN organization_agreement_info c ON a.agr_id = c.agr_id
    where 1 = 1
    <if test="valid != null">
      and c.valid = #{valid}
    </if>
    <if test="orgId != null and orgId != ''">
      and a.org_id = #{orgId}
    </if>
    <if test="agrId != null">
      and a.agr_id = #{agrId}
    </if>
    order by a.org_id asc,c.order_level asc,c.version desc
  </select>

<!-- 获取有效下拉tree -->
  <select id="findListForSearch" resultMap="BaseResultMap">
    select agr_id,title from organization_agreement_info where valid = 1 order by order_level asc
  </select>

  <select id="agreenmentIsSigned" parameterType="java.lang.Long" resultType="java.lang.Integer">
    select count(*) from organization_agreement where agr_id = #{agrId}
  </select>

<!-- 根据机构id和协议顺序获取协议列表 -->
  <select id="findAgreementByOrgIdAndOrderLevel" resultType="cn.taqu.gonghui.system.vo.AgreementVo">
    SELECT
      a.id id,
      a.org_id orgId,
      c.org_name orgName,
      a.agr_id agrId,
      a.create_time createTime
    FROM
      organization_agreement a
        LEFT JOIN organization_agreement_info b ON a.agr_id = b.agr_id
        LEFT JOIN organization c ON a.org_id = c.org_id
    <where>
      <if test="orgId != null">
        and a.org_id = #{orgId}
      </if>
      <if test="orderLevel != null">
        and b.order_level = #{orderLevel}
      </if>
    </where>
  </select>


<!-- 以下为用户端接口 -->
<!-- 根据机构id查询该机构下签署的所有协议 -->
  <select id="findAgreementByOrgIdGroupByAgrId" parameterType="java.lang.Long" resultType="cn.taqu.gonghui.system.entity.Agreement">
    select * from organization_agreement where org_id = #{orgId} group by agr_id
  </select>

<!-- 根据机构id和协议id查询签署记录 -->
  <select id="findByOrgIdAndAgrId" resultType="cn.taqu.gonghui.system.entity.Agreement">
    select * from organization_agreement where org_id = #{orgId} and agr_id = #{agrId} limit 1
  </select>

<!-- 根据协议id获取协议 -->
  <select id="getByAgrId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select * from organization_agreement_info where agr_id = #{agrId}
  </select>



  <insert id="insertBatch"  parameterType="java.util.List" useGeneratedKeys="true" keyProperty="agrId">
    insert into organization_agreement_info (agr_id,order_level,version,type, content, create_time,update_time,
    operator, title, valid)
    values
    <foreach collection="list" item="item" index="index"
             separator=",">
      (
      #{item.agrId,jdbcType=INTEGER},
      #{item.orderLevel,jdbcType=INTEGER},
      #{item.version,jdbcType=INTEGER},
      #{item.type,jdbcType=TINYINT},
      #{item.content,jdbcType=LONGVARCHAR},
      #{item.createTime,jdbcType=INTEGER},
      #{item.updateTime,jdbcType=INTEGER},
      #{item.operator},
      #{item.title},
      #{item.valid,jdbcType=TINYINT}
      )
    </foreach>

  </insert>
</mapper>
