<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.HostModifyRecordMapper">

    <insert id="batchInsert">
        insert into host_modify_record
        (host_uuid, team_type, batch_id, operate_type, info, reason,operator,create_time,update_time)
        values
        <foreach collection="recordList" item="log" separator=",">
            (#{log.hostUuid}, #{log.teamType}, #{log.batchId}, #{log.operateType}, #{log.info}, #{log.reason}
            ,#{log.operator}, #{log.createTime}, #{log.updateTime})
        </foreach>
    </insert>

    <select id="findAllByHostAndCreateTime" resultType="cn.taqu.gonghui.system.entity.HostModifyRecord">
        select *
        from host_modify_record
        where 1=1
        <if test="hostUuid != null">
            and host_uuid = #{hostUuid}
        </if>
        <if test="createTime != null">
            and create_time > #{createTime,jdbcType=BIGINT}
        </if>
        <if test="status != null">
            and status = #{status,jdbcType=INTEGER}
        </if>
        <if test="teamType != null">
            and team_type = #{teamType,jdbcType=INTEGER}
        </if>
        order by id desc
    </select>

    <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.HostModifyRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="host_uuid" property="hostUuid" jdbcType="VARCHAR"/>
        <result column="team_type" property="teamType" jdbcType="INTEGER"/>
        <result column="batch_id" property="batchId" jdbcType="VARCHAR"/>
        <result column="operate_type" property="operateType" jdbcType="INTEGER"/>
        <result column="info" property="info" jdbcType="VARCHAR"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="fail_msg" property="failMsg" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="INTEGER"/>
    </resultMap>

    <select id="findUuidByBatchId" resultType="java.lang.String">
        select host_uuid
        from host_modify_record
        where batch_id = #{batchId}
          and status = #{status,jdbcType=INTEGER}
    </select>

    <select id="countByHostUuidAndCreateTime" resultType="java.lang.Long">
        select count(1)
        from host_modify_record
        where 1=1
        <if test="hostUuid != null">
            and host_uuid = #{hostUuid}
        </if>
        <if test="teamType != null">
            and team_type = #{teamType,jdbcType=INTEGER}
        </if>
        <if test="status != null">
            and status = #{status,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectByOne" resultType="cn.taqu.gonghui.system.entity.HostModifyRecord">
        SELECT id,host_uuid,info,operate_type FROM host_modify_record
        WHERE 1 = 1
        <if test="hostUuid != null">
            and host_uuid = #{hostUuid}
        </if>
        <if test="operateType != null">
            and operate_type = #{operateType}
        </if>
        ORDER BY id DESC LIMIT 1;
    </select>

</mapper>
