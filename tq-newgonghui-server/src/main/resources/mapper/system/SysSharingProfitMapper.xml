<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.SysSharingProfitMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.SysSharingProfit" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="percent_number" property="percentNumber" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, percent_number, status, create_by, create_time
  </sql>

<!-- 列表查询 -->
  <select id="listByCondition" parameterType="cn.taqu.gonghui.system.search.SysSharingProfitSearch" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from sys_sharing_profit where 1 = 1
    <if test="status != null">
        and status = #{status}
    </if>
    order by percent_number desc
  </select>

  <update id="updateStatus">
    update sys_sharing_profit set status = #{status} where id = #{id}
  </update>

  <select id="percentNumberHasExist"  parameterType="java.lang.String" resultType="java.lang.Integer">
    select count(1) from sys_sharing_profit where percent_number = #{percentNumber}
  </select>

  <select id="getOneById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select * from sys_sharing_profit where id = #{id}
  </select>

  <select id="rateHasUsed" parameterType="java.lang.String" resultType="cn.taqu.gonghui.system.entity.TeamHost">
    select * from team_host where status = 1 and current_sharing_profit_rate = #{rate} limit 1
  </select>
</mapper>
