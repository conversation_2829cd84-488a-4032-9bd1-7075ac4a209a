<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.RegionMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.Region">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sign" jdbcType="VARCHAR" property="sign" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="region_path" jdbcType="VARCHAR" property="regionPath" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
    <result column="is_unavailable" jdbcType="TINYINT" property="isUnavailable" />
    <result column="sort" jdbcType="SMALLINT" property="sort" />
    <result column="postcode" jdbcType="INTEGER" property="postcode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sign, parent_id, region_path, level, region_name, is_unavailable, sort, postcode
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from region
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from region
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="cn.taqu.gonghui.system.entity.Region">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into region (sign, parent_id, region_path, 
      level, region_name, is_unavailable, 
      sort, postcode)
    values (#{sign}, #{parentId,jdbcType=INTEGER}, #{regionPath},
      #{level,jdbcType=INTEGER}, #{regionName}, #{isUnavailable,jdbcType=TINYINT},
      #{sort,jdbcType=SMALLINT}, #{postcode,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.system.entity.Region">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into region
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sign != null">
        sign,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="regionPath != null">
        region_path,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="regionName != null">
        region_name,
      </if>
      <if test="isUnavailable != null">
        is_unavailable,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="postcode != null">
        postcode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sign != null">
        #{sign},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="regionPath != null">
        #{regionPath},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="regionName != null">
        #{regionName},
      </if>
      <if test="isUnavailable != null">
        #{isUnavailable,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=SMALLINT},
      </if>
      <if test="postcode != null">
        #{postcode,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.system.entity.Region">
    update region
    <set>
      <if test="sign != null">
        sign = #{sign},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="regionPath != null">
        region_path = #{regionPath},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="regionName != null">
        region_name = #{regionName},
      </if>
      <if test="isUnavailable != null">
        is_unavailable = #{isUnavailable,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=SMALLINT},
      </if>
      <if test="postcode != null">
        postcode = #{postcode,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.system.entity.Region">
    update region
    set sign = #{sign},
      parent_id = #{parentId,jdbcType=INTEGER},
      region_path = #{regionPath},
      level = #{level,jdbcType=INTEGER},
      region_name = #{regionName},
      is_unavailable = #{isUnavailable,jdbcType=TINYINT},
      sort = #{sort,jdbcType=SMALLINT},
      postcode = #{postcode,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="findRegionList" resultMap="BaseResultMap" parameterType="java.lang.String">
    select  <include refid="Base_Column_List"/> from region where level = 1 or level = 2
  </select>
</mapper>