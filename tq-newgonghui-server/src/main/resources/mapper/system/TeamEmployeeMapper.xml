<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.TeamEmployeeMapper">

    <resultMap id="baseResultMap1" type="cn.taqu.gonghui.system.vo.TeamEmployeeVo">
        <result column="employeeId" jdbcType="BIGINT" property="employeeId" />
        <result column="userName" jdbcType="VARCHAR" property="userName"/>
        <result column="userId" jdbcType="BIGINT" property="userId"/>
        <result column="orgName" jdbcType="VARCHAR" property="orgName"/>
        <result column="teamId" jdbcType="BIGINT" property="teamId"/>
        <result column="teamName" jdbcType="VARCHAR" property="teamName" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="inviteTime" jdbcType="BIGINT" property="inviteTime"/>
        <result column="createTime" jdbcType="BIGINT" property="createTime" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="mobileCipher" jdbcType="VARCHAR" property="mobileCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
        <result column="employeeNameCipher" jdbcType="VARCHAR" property="employeeNameCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />

    </resultMap>

<!--    resultType="cn.taqu.gonghui.system.vo.TeamEmployeeVo"-->
    <select id="selectTeamEmployeeList" resultMap="baseResultMap1"  parameterType="cn.taqu.gonghui.system.search.TeamEmployeeSearch">
        SELECT
        t1.employee_id employeeId,
        t1.employee_name userName,
        t1.user_id userId,
        t1.org_name orgName,
        t3.team_id teamId,
        t3.team_name teamName,
        t1.mobile mobile,
        t1.invite_time inviteTime,
        t1.create_time createTime,
        t2.status status,
        t1.mobile_cipher as mobileCipher,
        t1.employee_name_cipher as employeeNameCipher
        FROM
        team_employee t1
        LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
        LEFT JOIN team t3 ON t1.team_id = t3.team_id
        <where>
            <if test="search.employeeId !=null ">
                and t1.employee_id = #{search.employeeId,jdbcType=BIGINT}
            </if>
            <if test="search.orgId !=null ">
                and t1.org_id = #{search.orgId,jdbcType=BIGINT}
            </if>
            <if test="search.teamId !=null ">
                and t1.team_id = #{search.teamId,jdbcType=BIGINT}
            </if>
            <if test="search.type !=null ">
                and t1.type = #{search.type,jdbcType=TINYINT}
            </if>
            <if test="search.mobile !=null and search.mobile!=''">
                <if test="search.selectByDigest != null and search.selectByDigest == 1">
                    and t1.mobile_digest = #{search.mobile,jdbcType=VARCHAR, typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
                </if>
                <if test="search.selectByDigest != null and search.selectByDigest == 0">
                    and t1.mobile = #{search.mobile}
                </if>
            </if>
            <if test="search.employeeName !=null and search.employeeName!=''">
                <if test="search.selectByDigest != null and search.selectByDigest == 1">
                    and t1.employee_name_digest = #{search.employeeName,jdbcType=VARCHAR, typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
                </if>
                <if test="search.selectByDigest != null and search.selectByDigest == 0">
                    and t1.employee_name = #{search.employeeName}
                </if>
            </if>
            <if test="search.teamType !=null">
                and t3.type = #{search.teamType,jdbcType=TINYINT}
            </if>
            <if test="search.userIdList !=null and search.userIdList.size>0">
                and t2.user_id in
                <foreach collection="search.userIdList" index="index" item="userId" separator="," open="(" close=")">
                    #{userId}
                </foreach>
            </if>

        </where>
        order by t1.create_time desc
    </select>

    <update id="updateEmployeeName">
        update
            team_employee set
            <if test="doubleWrite != 0">
                employee_name = #{employeeName},
            </if>
           employee_name_cipher = #{employeeName,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
           employee_name_digest = #{employeeName,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
        where employee_id = #{employeeId,jdbcType=BIGINT}
    </update>


    <select id="selectLeaderList" resultMap="baseResultMap1" >
<!--            resultType="cn.taqu.gonghui.system.vo.TeamEmployeeVo">-->
        SELECT
            t1.team_id teamId,
            t1.employee_name employeeName,
            t1.employee_name_cipher  employeeNameCipher
        FROM
            team_employee t1
                LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
                LEFT JOIN sys_user_role t3 ON t2.user_id = t3.user_id
                LEFT JOIN sys_role t4 ON t4.role_id = t3.role_id
        WHERE
            t1.org_id = #{orgId}
          and t4.type = #{type} and t4.role_key = 'leader' and t1.status = 1
    </select>

    <select id="selectAgentList" resultMap="baseResultMap1" >
<!--            resultType="cn.taqu.gonghui.system.vo.TeamEmployeeVo">-->
        SELECT
            t1.employee_id employeeId,
            t1.employee_name employeeName,
            t1.employee_name_cipher  employeeNameCipher
        FROM
            team_employee t1
                LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
                LEFT JOIN sys_user_role t3 ON t2.user_id = t3.user_id
                LEFT JOIN sys_role t4 ON t4.role_id = t3.role_id
        WHERE t4.type = #{type} and t4.role_key = 'agenter' and  t1.status = 1
    </select>

    <select id="selectAgentListByOrgId" resultMap="baseResultMap1">
<!--            resultType="cn.taqu.gonghui.system.vo.TeamEmployeeVo">-->
        SELECT
            t1.employee_id employeeId,
            t1.employee_name employeeName,
            t1.employee_name_cipher  employeeNameCipher
        FROM
            team_employee t1
                LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
                LEFT JOIN sys_user_role t3 ON t2.user_id = t3.user_id
                LEFT JOIN sys_role t4 ON t4.role_id = t3.role_id
        <where>
            t1.status = 1
            <if test="orgId != null">
                and t1.org_id = #{orgId}
            </if>
            <if test="type != null">
                and t4.type = #{type}
            </if>
                and t4.role_key = 'agenter'
        </where>
    </select>

    <select id="selectInviteCode" parameterType="java.lang.String" resultType="cn.taqu.gonghui.system.entity.TeamEmployee">
        SELECT
            org_id
        FROM team_employee
        <where>
            <if test="inviteCode!=null">
                and invite_code = #{inviteCode}
            </if>
        </where>
    </select>

    <resultMap id="baseResultMap" type="cn.taqu.gonghui.system.entity.TeamEmployee">
        <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="team_id" jdbcType="BIGINT" property="teamId"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="employee_name" jdbcType="VARCHAR" property="employeeName"/>
        <result column="mobile_cipher" jdbcType="VARCHAR" property="mobileCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
        <result column="employee_name_cipher" jdbcType="VARCHAR" property="employeeNameCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    </resultMap>

    <select id="selectMobile" parameterType="java.lang.String" resultMap="baseResultMap" >
<!--            resultType="cn.taqu.gonghui.system.entity.TeamEmployee">-->
        SELECT

        t1.employee_id ,
        t1.org_id ,
        t1.org_name ,
        t1.team_id ,
        t1.user_id ,
        t1.mobile ,
        t1.employee_name ,
        t1.mobile_cipher ,
        t1.employee_name_cipher
<!--        t1.employee_id employeeId,-->
<!--        t1.org_id orgId,-->
<!--        t1.org_name orgName,-->
<!--        t1.team_id teamId,-->
<!--        t1.user_id userId,-->
<!--        t1.mobile mobile,-->
<!--        t1.employee_name employeeName,-->
<!--        t1.mobile_cipher as mobileCipher,-->
<!--        t1.employee_name_cipher as employeeNameCipher-->
        FROM team_employee t1
        LEFT JOIN team t2 ON t2.team_id = t1.team_id
        <where>
            <if test="mobile!=null">
                <if test="selectByDigest != null and selectByDigest == 1">
                    and t1.mobile_digest = #{mobile,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
                </if>
                <if test="selectByDigest == null or selectByDigest == 0">
                    and t1.mobile = #{mobile}
                </if>

            </if>
            <if test="type!=null">
                and t2.type = #{type,jdbcType=TINYINT}
            </if>
        </where>
    </select>

    <select id="employeeIdsByTeamId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select employee_id from team_employee where team_id = #{teamId} and status = 1
    </select>


    <update id="employeeBelongToDefaultTeam">
        update team_employee set team_id = #{teamId}
        where employee_id in
        <foreach collection="employeeIds" index="index" item="employeeId" separator="," open="(" close=")">
            #{employeeId}
        </foreach>
    </update>

    <select id="getAgenterTree" parameterType="java.lang.Long" resultMap="baseResultMap">
<!--            resultType="cn.taqu.gonghui.system.entity.TeamEmployee">-->
        SELECT
            t1.employee_id,
            t1.employee_name,
            t1.user_id,
            t1.employee_name_cipher
        FROM
            team_employee t1
                LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
                LEFT JOIN sys_user_role t3 ON t2.user_id = t3.user_id
                LEFT JOIN sys_role t4 ON t3.role_id = t4.role_id
        WHERE
            t1.status = 1
          AND t2.status = 1
          AND t1.team_id = #{teamId}
          AND t4.role_key = 'agenter'
    </select>

    <select id="getAgentTreeByAgent" resultMap="baseResultMap" >
<!--            resultType="cn.taqu.gonghui.system.entity.TeamEmployee">-->
        SELECT
            t1.employee_id,
            t1.employee_name,
            t1.user_id,
            t1.employee_name_cipher
        FROM
            team_employee t1
                LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
                LEFT JOIN sys_user_role t3 ON t2.user_id = t3.user_id
                LEFT JOIN sys_role t4 ON t3.role_id = t4.role_id
        WHERE
            t1.status = 1
          AND t4.type=#{type}
          AND t2.account_uuid = #{accountUuid}
          AND t4.role_key = 'agenter'
    </select>

    <select id="getAgentTreeByManager" resultMap="baseResultMap">
<!--            resultType="cn.taqu.gonghui.system.entity.TeamEmployee">-->
        SELECT
            t1.employee_id,
            t1.employee_name,
            t1.user_id,
            t1.employee_name_cipher
        FROM
            team_employee t1
                LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
                LEFT JOIN sys_user_role t3 ON t2.user_id = t3.user_id
                LEFT JOIN sys_role t4 ON t3.role_id = t4.role_id
        WHERE
            t1.status = 1
          AND t1.org_id = #{orgId}
          AND t4.type = #{type}
          AND t4.role_key = 'agenter'
    </select>

    <select id="teamIdByAccountUuid"  resultType="java.lang.Long">
        SELECT
            t1.team_id
        FROM
            team_employee t1
                LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
                LEFT JOIN team t3 ON t3.team_id = t1.team_id

        WHERE
            t2.account_uuid = #{accountUuid}
          AND t3.type= #{type,jdbcType=INTEGER}
    </select>

    <select id="getByOrgId" resultType="cn.taqu.gonghui.system.entity.TeamEmployee">
        SELECT
            employee_id
        FROM
            team_employee
        WHERE
            org_id = #{orgId}
    </select>

    <select id="membersByTeamId" parameterType="java.util.List" resultType="cn.taqu.gonghui.system.vo.CommonVo">
        SELECT
        team_id keyResult,
        count(*) valueResult
        FROM
        team_employee
        WHERE
        STATUS = 1
        AND team_id in
        <foreach collection="teamIds" item="teamId" open="(" close=")" separator=",">
            #{teamId}
        </foreach>
        GROUP BY
        team_id
    </select>

    <select id="hostsByTeamId" parameterType="java.util.List" resultType="cn.taqu.gonghui.system.vo.CommonVo">
        SELECT
        team_id keyResult,
        count(*) valueResult
        FROM
        team_host
        WHERE
        team_id in
        <foreach collection="teamIds" item="teamId" open="(" close=")" separator=",">
            #{teamId}
        </foreach>
        GROUP BY
        team_id
    </select>

    <select id="hostsByEmployeeId"  resultType="cn.taqu.gonghui.system.vo.CommonVo">
        SELECT
        employee_id keyResult,
        count(*) valueResult
        FROM
        team_host
        WHERE
        STATUS = 1
        AND team_type = #{teamType}
        AND employee_id in
        <foreach collection="employeeIds" item="employeeId" open="(" close=")" separator=",">
            #{employeeId}
        </foreach>
        GROUP BY
        employee_id
    </select>

    <select id="accountUuidByEmployeeId" parameterType="java.lang.Long" resultType="java.lang.String">
        SELECT
            t1.account_uuid
        FROM
            sys_user t1
                LEFT JOIN team_employee t2 ON t1.user_id = t2.user_id
        WHERE
            t2.employee_id = #{employeeId}
    </select>

    <select id="seletEmployeeId" resultType="java.lang.Long">
        SELECT
            t1.employee_id
        FROM
            team_employee t1
                LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
                LEFT JOIN team t3 ON t3.team_id = t1.team_id
        WHERE
            t2.account_uuid = #{accountUuid}
         AND t3.type = #{type,jdbcType=TINYINT}
    </select>

    <select id="seletEmployeeByUserIdAndType"  resultType="cn.taqu.gonghui.system.entity.TeamEmployee">
        SELECT
            t1.*
        FROM
            team_employee t1
                LEFT JOIN team t2 ON t1.team_id = t2.team_id
        WHERE
            t1.user_id = #{userId,jdbcType=BIGINT}
          AND t2.type = #{type,jdbcType=TINYINT}
    </select>

    <select id="selectByUuid" resultMap="baseResultMap" >
<!--            resultType="cn.taqu.gonghui.system.entity.TeamEmployee">-->
        SELECT
        t1.org_id,
        t1.org_name,
        t1.team_id,
        t1.employee_name,
        t1.employee_name_cipher
        FROM
        team_employee t1
        LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
        LEFT JOIN team t3 ON t3.team_id = t1.team_id
        WHERE
        t2.account_uuid = #{accountUuid}
        AND t3.type= #{type,jdbcType=INTEGER}
    </select>

    <select id="selectByUserId" resultMap="baseResultMap" >
<!--            resultType="cn.taqu.gonghui.system.entity.TeamEmployee">-->
        SELECT org_id,
               org_name,
               team_id,
               mobile,
               employee_name,
               type,
               employee_name_cipher
        FROM team_employee WHERE user_id = #{userId} LIMIT 1
    </select>

    <update id="updateOrgName" >
        update team_employee set org_name=#{orgName} where org_id= #{orgId}
    </update>

<!--  以下是数据迁移需要用的接口  -->
    <delete id="deleteByOrgId" parameterType="java.lang.Long">
        DELETE FROM team_employee where org_id = #{orgId}
    </delete>


    <delete id="batchDeleteByIds" parameterType="java.util.Set">
        delete from team_employee where employee_id in
        <foreach item="employeeId" collection="collection" open="(" separator="," close=")">
            #{employeeId}
        </foreach>
    </delete>

    <update id="updateClearTxtByRange">
        UPDATE team_employee SET mobile = '', employee_name = '' where employee_id between #{curStartId,jdbcType=BIGINT} and #{curEndId,jdbcType=BIGINT};
    </update>
</mapper>
