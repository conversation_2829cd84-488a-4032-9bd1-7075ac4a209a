<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.system.mapper.SysUserMapper">

	<resultMap type="cn.taqu.gonghui.system.entity.SysUser" id="SysUserResult">
		<id     property="userId"       column="user_id"      />
		<result property="userName"     column="user_name"    />
		<result property="orgId"       column="org_id"      />
		<result property="orgName"       column="org_name"      />
		<result property="accountUuid"  column="account_uuid"  />
		<result property="mobile"  column="mobile"  />
		<result property="userType"  column="user_type"  />
		<result property="status"       column="status"       />
		<result property="createTime"   column="create_time"  />
		<result property="updateTime"   column="update_time"  />
		<result property="mobileCipher" column="mobile_cipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
	<result property="userNameCipher" column="user_name_cipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
	</resultMap>

   <sql id="base">user_id,user_name,org_id,user_type,org_name,account_uuid,mobile,status,create_time,update_time,mobile_cipher,user_name_cipher</sql>

	<update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.system.entity.SysUser" >
        update sys_user
        set org_id = #{orgId,jdbcType=BIGINT},
            user_type = #{userType,jdbcType=INTEGER},
            org_name = #{orgName},
			user_name = #{userName,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler},
            user_name_cipher = #{userName,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler}
        where user_id = #{userId,jdbcType=BIGINT}
    </update>

	<select id="selectUserByMobile" parameterType="java.lang.String" resultMap="SysUserResult">
	    select
	    <include refid="base"/>
	    from
	    sys_user
		<where>
			<if test="selectByDigest != 1">
				and mobile =  #{mobile}
			</if>
			<if test="selectByDigest == 1">
				and mobile_digest = #{mobile,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
			</if>
		</where>
	</select>

	<select id="selectUserByMobileAndOrgId" parameterType="java.lang.String" resultMap="SysUserResult">
		select
		<include refid="base"/>
		from
		sys_user

		<where>
			<if test="status !=null">
				and status =  #{status}
			</if>
			<if test="userType !=null">
				and user_type = #{userType,jdbcType=INTEGER}
			</if>
			<if test="mobile !=null and mobile!=''">
				and mobile_digest = #{mobile,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
			</if>
			<if test="orgId !=null and orgId!=''">
				and org_id = #{orgId}
			</if>
		</where>
	</select>

	<resultMap id="voResultMap" type="cn.taqu.gonghui.system.vo.SysUserVo">
		<result column="userId" jdbcType="BIGINT" property="userId" />
		<result column="userName" jdbcType="VARCHAR" property="userName" />
		<result column="accountUuid" jdbcType="VARCHAR" property="accountUuid" />
		<result column="mobile" jdbcType="VARCHAR" property="mobile" />
		<result column="orgId" jdbcType="BIGINT" property="orgId" />
		<result column="orgName" jdbcType="VARCHAR" property="orgName" />
		<result column="userType" jdbcType="INTEGER" property="userType" />
		<result column="createTime" jdbcType="BIGINT" property="createTime" />
		<result column="updateTime" jdbcType="BIGINT" property="updateTime" />
		<result column="status" jdbcType="INTEGER" property="status" />
		<result column="userId" jdbcType="BIGINT" property="userId" />
		<result column="userNameCipher" jdbcType="VARCHAR" property="userNameCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
		<result column="mobileCipher" jdbcType="VARCHAR" property="mobileCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
	</resultMap>

	<select id="selectUserBySearch"  resultMap="voResultMap" parameterType="cn.taqu.gonghui.system.search.UserSearch">
<!--			resultType="cn.taqu.gonghui.system.vo.SysUserVo" -->

		select
		t1.user_id as userId,
		t1.user_name as userName,
		t1.account_uuid as accountUuid,
		t1.mobile as mobile,
		t1.org_id as orgId,
		t1.org_name as orgName,
		t1.user_type as userType,
		t1.create_time as createTime,
		t1.update_time as updateTime,
		t1.status as status,
		t1.user_name_cipher as userNameCipher,
		t1.mobile_cipher as mobileCipher
		from sys_user t1
		<where>
			<if test="search.userId !=null ">
				and t1.user_id = #{search.userId,jdbcType=BIGINT}
			</if>
			<if test="search.orgId !=null ">
				and t1.org_id = #{search.orgId,jdbcType=BIGINT}
			</if>
			<if test="search.isManager !=null and search.isManager==1">
			       and t1.user_type = 1
		    </if>
			<if test="search.isManager !=null and search.isManager!=1">
				and t1.user_type != 1
			</if>
			<if test="search.mobile !=null and search.mobile!=''">
				and t1.mobile_digest = #{search.mobile,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
			</if>
		</where>
		order by t1.create_time desc
	</select>

	<select id="getTeamNameAndRoleNameByUserId" resultType="cn.taqu.gonghui.system.vo.UserTeamAndRoleVo">
		SELECT
			t3.team_id teamId,
			t3.team_name teamName,
			t3.type businessType
		FROM
			team_employee t2
				LEFT JOIN team t3 ON t2.team_id = t3.team_id
		WHERE
			t2.user_id= #{userId,jdbcType=BIGINT}
	</select>

	<select id="selectOrgManager" resultType="string">
		select user_name from sys_user where org_id = #{orgId,jdbcType=BIGINT} and user_type = 1 limit 1
	</select>

	<select id="selectUserIdsByRoleId" resultType="long">
		select t1.user_id from sys_user t1
		left join sys_user_role t2 on t1.user_id = t2.user_id
		where t1.org_id = #{orgId,jdbcType=BIGINT}
		  and t2.role_id = #{roleId,jdbcType=INTEGER}
	</select>


	<select id="selectUserAgent"  resultMap="SysUserResult" parameterType="cn.taqu.gonghui.system.entity.SysUser">
		select
		<include refid="base"/>
		from sys_user
		<where>
			<if test="status !=null">
				and status =  #{status}
			</if>
			<if test="userType !=null">
				and user_type = #{userType,jdbcType=INTEGER}
			</if>
			<if test="accountUuid !=null and accountUuid!=''">
				and account_uuid = #{accountUuid}
			</if>
		</where>
		order by create_time desc
	</select>

	<select id="getRoleByAccountUuid" resultType="cn.taqu.gonghui.system.vo.RoleVo">
		select
			t3.role_id roleId,
			t3.role_key roleKey,
			t3.type type
		FROM
			 sys_user_role t2
			LEFT JOIN sys_role t3 ON t2.role_id = t3.role_id
		WHERE
		 t3.status = 1
		  AND t2.user_id = #{userId}
	</select>

	<select id="getRoleByUserId" resultType="cn.taqu.gonghui.system.vo.RoleVo">
		select
			t1.role_id roleId,
			t2.role_key roleKey,
			t2.type type,
		    t2.role_name roleName
		FROM
			sys_user_role t1
				LEFT JOIN sys_role t2 ON t1.role_id = t2.role_id
		WHERE
		    t1.user_id = #{userId}
			and t2.type = #{type}
         limit 1
	</select>


	<select id="getEmployeeIdByAccountUuid" resultType="java.lang.Long">
		SELECT
			t1.employee_id
		FROM
			team_employee t1
				LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
		        LEFT JOIN team t3 ON t3.team_id = t1.team_id
		WHERE
			t2.account_uuid = #{accountUuid}
		  AND t3.type = #{type}
		  AND t1.status = 1
	</select>

	<select id="getTeamIdByAccountUuid" resultType="java.lang.Long">
		SELECT
			t1.team_id
		FROM
			team_employee t1
				LEFT JOIN sys_user t2 ON t1.user_id = t2.user_id
				LEFT JOIN team t3 ON t3.team_id = t1.team_id
		WHERE
			t2.account_uuid = #{accountUuid}
		  AND t3.type = #{type}
		  AND t1.status = 1

	</select>


	<select id="selectAccountUuidByEmployeeIds" parameterType="java.util.Set" resultType="java.lang.String">
		SELECT
			t2.account_uuid
		FROM
			team_employee t1
			 inner join sys_user t2 ON t1.user_id = t2.user_id
		<where>
			t1.status = 1
			<if  test="set !=null and set.size()>0">
				and t1.employee_id in
				<foreach collection="set" open="(" separator="," close=")" item="val">
					${val}
				</foreach>
			</if>
		</where>
	</select>



	<select id="userNameListByAccountUuidList" parameterType="java.util.List" resultMap="SysUserResult">
<!--			resultType="cn.taqu.gonghui.system.entity.SysUser">-->
		select
		account_uuid,
		user_name,
		user_name_cipher
		from sys_user where account_uuid in
		<foreach collection="list" item="uuid" open="(" close=")" separator=",">
			#{uuid}
		</foreach>
	</select>

	<update id="updateOrgName" >
        update sys_user set org_name=#{orgName} where org_id= #{orgId}
    </update>

	<select id="userListByOrgId" parameterType="java.lang.Long" resultType="cn.taqu.gonghui.system.entity.SysUser">
		select <include refid="base"/> from sys_user where org_id = #{orgId}
	</select>


<!--  以下是数据迁移需要用到的接口	-->
	<delete id="deleteByOrgId" parameterType="java.lang.Long">
		delete from sys_user where org_id = #{orgId}
	</delete>

	<select id="userListByAccountUuid" parameterType="java.lang.String" resultMap="SysUserResult">
<!--			resultType="cn.taqu.gonghui.system.entity.SysUser">-->
		select
		 <include refid="base">
		 </include>
		from sys_user where account_uuid = #{accountUuid}
	</select>

	<update id="updateNameByUserId">
		update sys_user set
			user_name = #{userName},
			user_name_cipher = #{userName,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler}
        where user_id = #{userId,jdbcType=BIGINT}
	</update>

	<delete id="batchDeleteByIds" parameterType="java.util.Set">
		delete from sys_user where user_id in
		<foreach item="userId" collection="collection" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</delete>

	<select id="userIsHasExisted" parameterType="java.lang.String" resultType="cn.taqu.gonghui.system.entity.SysUser">
		select * from sys_user where account_uuid = #{accountUuid}
	</select>

	<select id="selectOneManager" resultMap="SysUserResult">
<!--			resultType="cn.taqu.gonghui.system.entity.SysUser">-->
		select
		<include refid="base">
		</include>
		<!--			org_name,user_name,account_uuid,user_name,user_type,mobile -->
		 from sys_user
		where org_id = #{orgId,jdbcType=BIGINT} and user_type = 1 and status = 1 limit 1
	</select>

	<update id="updateClearTxtByRange">
		UPDATE sys_user set user_name = '', mobile='' where user_id between #{curStartId,jdbcType=BIGINT} and #{curEndId,jdbcType=BIGINT};
	</update>

</mapper>
