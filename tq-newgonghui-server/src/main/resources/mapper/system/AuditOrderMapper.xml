<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.common.mapper.AuditOrderMapper">


    <select id="listByStatusOrderById" resultType="cn.taqu.gonghui.common.entity.AuditOrder">
        SELECT
        id,
        `no`,
        `type`,
        status,
        create_by,
        create_time,
        update_by,
        modify_time,
        remark
        FROM audit_order
        WHERE status IN
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>

        AND id > #{lastId}
        ORDER BY id
        LIMIT 0,#{limit}
    </select>
</mapper>