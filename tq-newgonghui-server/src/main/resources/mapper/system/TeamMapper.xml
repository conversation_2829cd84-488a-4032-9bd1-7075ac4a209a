<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.system.mapper.TeamMapper">
    <resultMap id="BaseResultMap" type="cn.taqu.gonghui.system.entity.Team">
        <id column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="org_id" property="orgId" jdbcType="BIGINT"/>
        <result column="team_name" property="teamName" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="sign_key" property="signKey" jdbcType="VARCHAR"/>
        <result column="is_default" property="isDefault" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="INTEGER"/>
        <result column="invite_code" property="inviteCode" jdbcType="CHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        team_id
        , org_id, team_name, type,sign_key, is_default, status, create_by, create_time,update_time,invite_code
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from team
        where team_id = #{teamId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from team
        where team_id = #{teamId,jdbcType=BIGINT}
    </delete>
    <insert id="insertTeam" parameterType="cn.taqu.gonghui.system.entity.Team">
        insert into team (team_id, org_id, team_name,
                          type, sign_key, is_default, status,
                          create_by, create_time, update_time, invite_code)
        values (#{teamId,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, #{teamName},
                #{type,jdbcType=INTEGER}, #{signKey}, #{isDefault,jdbcType=INTEGER},
                #{status,jdbcType=INTEGER},
                #{createBy}, #{createTime,jdbcType=INTEGER}, #{updateTime,jdbcType=INTEGER},
                #{inviteCode, jdbcType = CHAR})
    </insert>
    <insert id="insertSelective" parameterType="cn.taqu.gonghui.system.entity.Team">
        insert into team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teamId != null">
                team_id,
            </if>
            <if test="orgId != null">
                org_id,
            </if>
            <if test="teamName != null">
                team_name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="signKey != null">
                sign_key,
            </if>
            <if test="isDefault != null">
                is_default,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="inviteCode != null">
                invite_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teamId != null">
                #{teamId,jdbcType=BIGINT},
            </if>
            <if test="orgId != null">
                #{orgId,jdbcType=BIGINT},
            </if>
            <if test="teamName != null">
                #{teamName},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="signKey != null and signKey != ''">
                #{signKey},
            </if>
            <if test="isDefault != null">
                #{isDefault,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=INTEGER},
            </if>
            <if test="inviteCode != null">
                #{inviteCode},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.system.entity.Team">
        update team
        <set>
            <if test="orgId != null">
                org_id = #{orgId,jdbcType=BIGINT},
            </if>
            <if test="teamName != null">
                team_name = #{teamName},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="signKey != null">
                sign_key = #{signKey},
            </if>
            <if test="isDefault != null">
                is_default = #{isDefault,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=INTEGER},
            </if>
            <if test="inviteCode != null and inviteCode!=''">
                invite_code = #{inviteCode},
            </if>
        </set>
        where team_id = #{teamId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.system.entity.Team">
        update team
        set org_id      = #{orgId,jdbcType=BIGINT},
            team_name   = #{teamName},
            type        = #{type,jdbcType=INTEGER},
            sign_key    = #{signKey},
            is_default  = #{isDefault,jdbcType=INTEGER},
            status      = #{status,jdbcType=INTEGER},
            create_by   = #{createBy},
            create_time = #{createTime,jdbcType=INTEGER},
            update_time = #{updateTime,jdbcType=INTEGER}
        where team_id = #{teamId,jdbcType=BIGINT}
    </update>
    <!-- 自定义查询 -->
    <select id="selectTeamListByCondition" parameterType="cn.taqu.gonghui.system.search.TeamSearch"
            resultType="cn.taqu.gonghui.system.vo.TeamVo">
        SELECT
        a.team_id as teamId,
        a.team_name as teamName,
        a.is_default as isDefault,
        a.type as type,
        a.sign_key as signKey,
        a.org_id as orgId,
        d.org_name as orgName,
        a.status as status,
        a.create_time as createTime,
        a.create_by as createBy,
        a.invite_code as inviteCode
        FROM
        team a
        LEFT JOIN organization d ON a.org_id = d.org_id
        where 1 = 1
        <if test="orgId != null">
            and a.org_id = #{orgId}
        </if>
        <if test="type != null">
            and a.type = #{type}
        </if>
        <if test="signKey != null and signKey != ''">
            and a.sign_key = #{signKey}
        </if>
        <if test="status != null">
            and a.status = #{status}
        </if>
        <if test="teamName != null">
            and a.team_name like concat('%',#{teamName},'%')
        </if>
        <if test="teamDefault != null">
            and a.is_default = #{teamDefault}
        </if>
        <if test="inviteCode != null">
            and a.invite_code = #{inviteCode}
        </if>
        order by a.create_time desc
    </select>

    <!-- 获取有效团队列表 -->
    <select id="selectTeamList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team where status = 1
        <if test="orgId != null">
            and org_id = #{orgId}
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
    </select>

    <select id="teamNameHasExist" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team where team_name = #{teamName} and type = #{type}
    </select>

    <select id="memberHasExist" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(1)
        from team_employee
        where team_id = #{teamId}
          and status = 1
    </select>

    <select id="liveHostHasExist" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(1)
        from team_host
        where team_id = #{teamId}
          and status = 1
    </select>


    <select id="findMembersByTeamId" parameterType="java.lang.Long" resultType="java.lang.String">
        select employee_name
        from team_employee
        where team_id = #{teamId}
          and status = 1
    </select>

    <select id="findHostsByTeamId" parameterType="java.lang.Long" resultType="java.lang.String">
        select host_uuid
        from team_host
        where team_id = #{teamId}
          and status = 1
    </select>

    <select id="findHostNumberByTeamId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from team_host
        where team_id = #{teamId}
          and status = 1
    </select>

    <select id="findEmployeeNumberByTeamId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from team_employee
        where team_id = #{teamId}
          and status = 1
    </select>

    <select id="findTeamLeaders" resultType="string">
        select t3.employee_name
        FROM sys_user_role t1
                 LEFT JOIN sys_role t2 ON t1.role_id = t2.role_id
                 LEFT JOIN team_employee t3 ON t3.user_id = t1.user_id
        WHERE t2.type = #{teamType}
          and t3.team_id = #{teamId}
          and t2.role_key = 'leader'
    </select>

    <!-- ids查询teamList -->
    <select id="teamListByTeamIds" parameterType="list" resultMap="BaseResultMap">
        select * from team
        <where>
            team_id in
            <foreach item="item" collection="list" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <!-- 获取机构下的默认团队 -->
    <select id="getDefaultTeam" resultMap="BaseResultMap">
        select *
        from team
        where is_default = 1
          and org_id = #{orgId}
          and type = #{type}
    </select>

    <update id="updateStatusByTeamId">
        update team
        set status = #{status}
        where team_id = #{teamId}
    </update>

    <!-- 根据机构id获取当前机构开启了哪几种业务类型 -->
    <select id="getTypesByOrgId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT type
        FROM team
        WHERE status = 1
          and org_id = #{orgId}
        GROUP BY type
    </select>

    <select id="getTeamTreeByRole" resultMap="BaseResultMap">
        SELECT t1.*
        FROM team t1
                 LEFT JOIN team_employee t2 ON t1.team_id = t2.team_id
                 LEFT JOIN sys_user t3 ON t2.user_id = t3.user_id
        WHERE t3.account_uuid = #{accountUuid}
          AND t1.type = #{type}
          AND t1.org_id = #{orgId}
    </select>


    <!-- 获取有效团队列表 -->
    <select id="selectTeamListIds" resultType="java.lang.Long">
        select
        team_id
        from team where status = 1 and type = #{type}
        <if test="orgId != null">
            and org_id = #{orgId}
        </if>
    </select>

    <select id="selectTeamListByType" resultType="cn.taqu.gonghui.system.vo.TeamTreeVo">
        SELECT t1.team_id value,
      t1.team_name label,
      t2.org_id orgId,
      t2.org_name orgName,
      t2.live_settlemente_type type,
        t2.join_time  orgJoinTime
        FROM
            team t1
            LEFT JOIN organization t2
        ON t1.org_id = t2.org_id
        WHERE
            t1.type = #{type}
          and t1.status = 1
          and t2.org_name != ''
        LIMIT 0, 1000000
    </select>

    <select id="isWeekType" parameterType="cn.taqu.gonghui.system.search.TeamSearch" resultMap="BaseResultMap">
        select *
        from team
        where status = 1
          and type = 1
          and org_id = #{orgId}
          and team_name like concat('%', #{teamName}, '%')
    </select>

    <!-- 以下为数据迁移需要用到的接口 -->
    <delete id="deleteByOrgId" parameterType="java.lang.Long">
        delete
        from team
        where org_id = #{orgId}
    </delete>

    <!-- 获取有聊天室效团队列表 迁移用-->
    <select id="selectChatTeamList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from team where status = 1 and type = 3
    </select>

    <!-- 获取机构团队id名称信息 -->
    <select id="selectTeamOrgInfoByTeamId" parameterType="java.lang.Long"
            resultType="cn.taqu.gonghui.system.vo.TeamOrgInfoVO">
        SELECT t1.team_id   as teamId,
               t1.team_name as teamName,
               t1.type      as teamType,
               t2.org_id    as orgId,
               t2.org_name  as orgName
        FROM team t1
                 LEFT JOIN organization t2 ON t1.org_id = t2.org_id
        WHERE t1.team_id = #{teamId} limit 1
    </select>

    <select id="selectTeamOrgInfoByTeamIds" parameterType="list" resultType="cn.taqu.gonghui.system.vo.TeamOrgInfoVO">
        select t.team_id as teamId,
        t.team_name as teamName,
        o.org_id as orgId,
        o.org_name as orgName
        from team t
        left join organization o on t.org_id = o.org_id
        <where>
            t.team_id in
            <foreach item="item" collection="list" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectOneTeamLeader" resultType="cn.taqu.gonghui.system.vo.TeamOrgInfoVO">
        select t4.account_uuid,t3.mobile
        FROM sys_user_role t1
                 LEFT JOIN sys_role t2 ON t1.role_id = t2.role_id
                 LEFT JOIN team_employee t3 ON t3.user_id = t1.user_id
                 LEFT JOIN sys_user t4 ON t4.user_id = t3.user_id
        WHERE t2.type = #{teamType}
          and t3.team_id = #{teamId}
          and t2.role_key = 'leader'
        ORDER BY t4.user_id ASC
        LIMIT 1;
    </select>

</mapper>
