<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.live.mapper.LiveHostInfoMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.LiveHostInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="host_uuid" jdbcType="CHAR" property="hostUuid" />
    <result column="cover_url" jdbcType="VARCHAR" property="coverUrl" />
    <result column="width" jdbcType="SMALLINT" property="width" />
    <result column="height" jdbcType="SMALLINT" property="height" />
    <result column="host_intro" jdbcType="VARCHAR" property="hostIntro" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="live_status" jdbcType="TINYINT" property="liveStatus" />
    <result column="live_time" jdbcType="INTEGER" property="liveTime" />
    <result column="stream_id" jdbcType="VARCHAR" property="streamId" />
    <result column="rtmp_url" jdbcType="VARCHAR" property="rtmpUrl" />
    <result column="live_img_url" jdbcType="VARCHAR" property="liveImgUrl" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
    <result column="last_online_time" jdbcType="INTEGER" property="lastOnlineTime" />
    <result column="max_num" jdbcType="INTEGER" property="maxNum" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="live_no" jdbcType="INTEGER" property="liveNo" />
    <result column="theme_id" jdbcType="INTEGER" property="themeId" />
    <result column="tag_id" jdbcType="VARCHAR" property="tagId" />
    <result column="consortia_id" jdbcType="INTEGER" property="consortiaId" />
    <result column="stop_times" jdbcType="INTEGER" property="stopTimes" />
    <result column="warn_times" jdbcType="INTEGER" property="warnTimes" />
    <result column="apply_level" jdbcType="VARCHAR" property="applyLevel" />
    <result column="first_live_time" jdbcType="INTEGER" property="firstLiveTime" />
    <result column="label_id" jdbcType="INTEGER" property="labelId" />
    <result column="recommend_tag_id" jdbcType="VARCHAR" property="recommendTagId" />
    <result column="businessman_uuid" jdbcType="VARCHAR" property="businessmanUuid" />
    <result column="real_name_cipher" jdbcType="VARCHAR" property="realNameCipher" />
    <result column="id_card_cipher" jdbcType="VARCHAR" property="idCardCipher" />
    <result column="real_name_sm3" jdbcType="VARCHAR" property="realNameSm3" />
    <result column="id_card_sm3" jdbcType="VARCHAR" property="idCardSm3" />
    <result column="can_open_pc" jdbcType="TINYINT" property="canOpenPc" />
    <result column="province_id" jdbcType="VARCHAR" property="provinceId" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="personal" jdbcType="TINYINT" property="personal" />
    <result column="punish_times" jdbcType="INTEGER" property="punishTimes" />
    <result column="add_consortia_time" jdbcType="INTEGER" property="addConsortiaTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, host_uuid, cover_url, width, height, host_intro, title, live_status, live_time,
    stream_id, rtmp_url, live_img_url, create_time, update_time, last_online_time, max_num,
    remark, real_name, id_card, live_no, theme_id, tag_id, consortia_id, stop_times,
    warn_times, apply_level, first_live_time, label_id, recommend_tag_id, businessman_uuid,
    real_name_cipher, id_card_cipher, real_name_sm3, id_card_sm3, can_open_pc, province_id,
    city_id, personal, punish_times, add_consortia_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from live_host_info
    where id = #{id,jdbcType=INTEGER}
  </select>


  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.taqu.gonghui.live.entity.LiveHostInfo" useGeneratedKeys="true">
    insert into live_host_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hostUuid != null">
        host_uuid,
      </if>
      <if test="coverUrl != null">
        cover_url,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="hostIntro != null">
        host_intro,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="liveStatus != null">
        live_status,
      </if>
      <if test="liveTime != null">
        live_time,
      </if>
      <if test="streamId != null">
        stream_id,
      </if>
      <if test="rtmpUrl != null">
        rtmp_url,
      </if>
      <if test="liveImgUrl != null">
        live_img_url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="lastOnlineTime != null">
        last_online_time,
      </if>
      <if test="maxNum != null">
        max_num,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="idCard != null">
        id_card,
      </if>
      <if test="liveNo != null">
        live_no,
      </if>
      <if test="themeId != null">
        theme_id,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
      <if test="consortiaId != null">
        consortia_id,
      </if>
      <if test="stopTimes != null">
        stop_times,
      </if>
      <if test="warnTimes != null">
        warn_times,
      </if>
      <if test="applyLevel != null">
        apply_level,
      </if>
      <if test="firstLiveTime != null">
        first_live_time,
      </if>
      <if test="labelId != null">
        label_id,
      </if>
      <if test="recommendTagId != null">
        recommend_tag_id,
      </if>
      <if test="businessmanUuid != null">
        businessman_uuid,
      </if>
      <if test="realNameCipher != null">
        real_name_cipher,
      </if>
      <if test="idCardCipher != null">
        id_card_cipher,
      </if>
      <if test="realNameSm3 != null">
        real_name_sm3,
      </if>
      <if test="idCardSm3 != null">
        id_card_sm3,
      </if>
      <if test="canOpenPc != null">
        can_open_pc,
      </if>
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="personal != null">
        personal,
      </if>
      <if test="punishTimes != null">
        punish_times,
      </if>
      <if test="addConsortiaTime != null">
        add_consortia_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hostUuid != null">
        #{hostUuid},
      </if>
      <if test="coverUrl != null">
        #{coverUrl},
      </if>
      <if test="width != null">
        #{width,jdbcType=SMALLINT},
      </if>
      <if test="height != null">
        #{height,jdbcType=SMALLINT},
      </if>
      <if test="hostIntro != null">
        #{hostIntro},
      </if>
      <if test="title != null">
        #{title},
      </if>
      <if test="liveStatus != null">
        #{liveStatus,jdbcType=TINYINT},
      </if>
      <if test="liveTime != null">
        #{liveTime,jdbcType=INTEGER},
      </if>
      <if test="streamId != null">
        #{streamId},
      </if>
      <if test="rtmpUrl != null">
        #{rtmpUrl},
      </if>
      <if test="liveImgUrl != null">
        #{liveImgUrl},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="lastOnlineTime != null">
        #{lastOnlineTime,jdbcType=INTEGER},
      </if>
      <if test="maxNum != null">
        #{maxNum,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="realName != null">
        #{realName},
      </if>
      <if test="idCard != null">
        #{idCard},
      </if>
      <if test="liveNo != null">
        #{liveNo,jdbcType=INTEGER},
      </if>
      <if test="themeId != null">
        #{themeId,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        #{tagId},
      </if>
      <if test="consortiaId != null">
        #{consortiaId,jdbcType=INTEGER},
      </if>
      <if test="stopTimes != null">
        #{stopTimes,jdbcType=INTEGER},
      </if>
      <if test="warnTimes != null">
        #{warnTimes,jdbcType=INTEGER},
      </if>
      <if test="applyLevel != null">
        #{applyLevel},
      </if>
      <if test="firstLiveTime != null">
        #{firstLiveTime,jdbcType=INTEGER},
      </if>
      <if test="labelId != null">
        #{labelId,jdbcType=INTEGER},
      </if>
      <if test="recommendTagId != null">
        #{recommendTagId},
      </if>
      <if test="businessmanUuid != null">
        #{businessmanUuid},
      </if>
      <if test="realNameCipher != null">
        #{realNameCipher},
      </if>
      <if test="idCardCipher != null">
        #{idCardCipher},
      </if>
      <if test="realNameSm3 != null">
        #{realNameSm3},
      </if>
      <if test="idCardSm3 != null">
        #{idCardSm3},
      </if>
      <if test="canOpenPc != null">
        #{canOpenPc,jdbcType=TINYINT},
      </if>
      <if test="provinceId != null">
        #{provinceId},
      </if>
      <if test="cityId != null">
        #{cityId},
      </if>
      <if test="personal != null">
        #{personal,jdbcType=TINYINT},
      </if>
      <if test="punishTimes != null">
        #{punishTimes,jdbcType=INTEGER},
      </if>
      <if test="addConsortiaTime != null">
        #{addConsortiaTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.live.entity.LiveHostInfo">
    update live_host_info
    <set>
      <if test="hostUuid != null">
        host_uuid = #{hostUuid,jdbcType=CHAR},
      </if>
      <if test="coverUrl != null">
        cover_url = #{coverUrl},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=SMALLINT},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=SMALLINT},
      </if>
      <if test="hostIntro != null">
        host_intro = #{hostIntro},
      </if>
      <if test="title != null">
        title = #{title},
      </if>
      <if test="liveStatus != null">
        live_status = #{liveStatus,jdbcType=TINYINT},
      </if>
      <if test="liveTime != null">
        live_time = #{liveTime,jdbcType=INTEGER},
      </if>
      <if test="streamId != null">
        stream_id = #{streamId},
      </if>
      <if test="rtmpUrl != null">
        rtmp_url = #{rtmpUrl},
      </if>
      <if test="liveImgUrl != null">
        live_img_url = #{liveImgUrl},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="lastOnlineTime != null">
        last_online_time = #{lastOnlineTime,jdbcType=INTEGER},
      </if>
      <if test="maxNum != null">
        max_num = #{maxNum,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark},
      </if>
      <if test="realName != null">
        real_name = #{realName},
      </if>
      <if test="idCard != null">
        id_card = #{idCard},
      </if>
      <if test="liveNo != null">
        live_no = #{liveNo,jdbcType=INTEGER},
      </if>
      <if test="themeId != null">
        theme_id = #{themeId,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        tag_id = #{tagId},
      </if>
      <if test="consortiaId != null">
        consortia_id = #{consortiaId,jdbcType=INTEGER},
      </if>
      <if test="stopTimes != null">
        stop_times = #{stopTimes,jdbcType=INTEGER},
      </if>
      <if test="warnTimes != null">
        warn_times = #{warnTimes,jdbcType=INTEGER},
      </if>
      <if test="applyLevel != null">
        apply_level = #{applyLevel},
      </if>
      <if test="firstLiveTime != null">
        first_live_time = #{firstLiveTime,jdbcType=INTEGER},
      </if>
      <if test="labelId != null">
        label_id = #{labelId,jdbcType=INTEGER},
      </if>
      <if test="recommendTagId != null">
        recommend_tag_id = #{recommendTagId},
      </if>
      <if test="businessmanUuid != null">
        businessman_uuid = #{businessmanUuid},
      </if>
      <if test="realNameCipher != null">
        real_name_cipher = #{realNameCipher},
      </if>
      <if test="idCardCipher != null">
        id_card_cipher = #{idCardCipher},
      </if>
      <if test="realNameSm3 != null">
        real_name_sm3 = #{realNameSm3},
      </if>
      <if test="idCardSm3 != null">
        id_card_sm3 = #{idCardSm3},
      </if>
      <if test="canOpenPc != null">
        can_open_pc = #{canOpenPc,jdbcType=TINYINT},
      </if>
      <if test="provinceId != null">
        province_id = #{provinceId},
      </if>
      <if test="cityId != null">
        city_id = #{cityId},
      </if>
      <if test="personal != null">
        personal = #{personal,jdbcType=TINYINT},
      </if>
      <if test="punishTimes != null">
        punish_times = #{punishTimes,jdbcType=INTEGER},
      </if>
      <if test="addConsortiaTime != null">
        add_consortia_time = #{addConsortiaTime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByHostUuid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from live_host_info
    where host_uuid = #{hostUuid}
  </select>

  <select id="selectByLiveNo" resultType="cn.taqu.gonghui.live.entity.LiveHostInfo">
    select
    <include refid="Base_Column_List" />
    from live_host_info
    where live_no = #{liveNo}
    limit 1
  </select>

  <select id="getHostListByBusinessUuid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from live_host_info
    where businessman_uuid = #{businessUuid}
  </select>

  <select id="getHostListNoBusinessUuid" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from live_host_info
    where (consortia_id = #{consortiaId}) and (businessman_uuid = '' or businessman_uuid is null)
  </select>


  <select id="getHostListByConsortiaId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from live_host_info
    where (consortia_id = #{consortiaId})
  </select>


  <select id="getHostListByConsortiaIdAndId"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from live_host_info
    where consortia_id = #{consortiaId,jdbcType=INTEGER}
    and id &gt;= #{idStart,jdbcType=INTEGER}
    and id &lt; #{idEnd,jdbcType=INTEGER}
  </select>

  <select id="selectByHostUuidList" resultType="cn.taqu.gonghui.live.entity.LiveHostInfo">
    select
    <include refid="Base_Column_List" />
    from live_host_info
    where host_uuid IN
    <foreach collection="hostUuidList" index="index" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

</mapper>
