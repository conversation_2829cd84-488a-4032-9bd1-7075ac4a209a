<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.live.mapper.LiveHostStatisticsMonthMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.LiveHostStatisticsMonth">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="host_uuid" jdbcType="CHAR" property="hostUuid" />
    <result column="consortia_id" jdbcType="INTEGER" property="consortiaId" />
    <result column="host_amount" jdbcType="INTEGER" property="hostAmount" />
    <result column="day_time" jdbcType="INTEGER" property="dayTime" />
    <result column="flower" jdbcType="INTEGER" property="flower" />
    <result column="total_live_time" jdbcType="INTEGER" property="totalLiveTime" />
    <result column="viewer" jdbcType="INTEGER" property="viewer" />
    <result column="send" jdbcType="INTEGER" property="send" />
    <result column="fans" jdbcType="INTEGER" property="fans" />
    <result column="message" jdbcType="INTEGER" property="message" />
    <result column="is_live" jdbcType="INTEGER" property="isLive" />
    <result column="is_valid_live" jdbcType="INTEGER" property="isValidLive" />
    <result column="is_disable" jdbcType="INTEGER" property="isDisable" />
    <result column="is_still" jdbcType="INTEGER" property="isStill" />
    <result column="is_no_live" jdbcType="INTEGER" property="isNoLive" />
    <result column="is_zero" jdbcType="INTEGER" property="isZero" />
    <result column="is_new" jdbcType="INTEGER" property="isNew" />
  </resultMap>
  <sql id="Base_Column_List">
    id, host_uuid, consortia_id, host_amount, day_time, flower, total_live_time, viewer, 
    send, fans, message, is_live, is_valid_live, is_disable, is_still, is_no_live, is_zero, 
    is_new
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from live_host_statistics_month
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from live_host_statistics_month
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="cn.taqu.gonghui.live.entity.LiveHostStatisticsMonth">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into live_host_statistics_month (host_uuid, consortia_id, host_amount, 
      day_time, flower, total_live_time, 
      viewer, send, fans, 
      message, is_live, is_valid_live, 
      is_disable, is_still, is_no_live, 
      is_zero, is_new)
    values (#{hostUuid,jdbcType=CHAR}, #{consortiaId,jdbcType=INTEGER}, #{hostAmount,jdbcType=INTEGER}, 
      #{dayTime,jdbcType=INTEGER}, #{flower,jdbcType=INTEGER}, #{totalLiveTime,jdbcType=INTEGER}, 
      #{viewer,jdbcType=INTEGER}, #{send,jdbcType=INTEGER}, #{fans,jdbcType=INTEGER}, 
      #{message,jdbcType=INTEGER}, #{isLive,jdbcType=INTEGER}, #{isValidLive,jdbcType=INTEGER}, 
      #{isDisable,jdbcType=INTEGER}, #{isStill,jdbcType=INTEGER}, #{isNoLive,jdbcType=INTEGER}, 
      #{isZero,jdbcType=INTEGER}, #{isNew,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.live.entity.LiveHostStatisticsMonth">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into live_host_statistics_month
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hostUuid != null">
        host_uuid,
      </if>
      <if test="consortiaId != null">
        consortia_id,
      </if>
      <if test="hostAmount != null">
        host_amount,
      </if>
      <if test="dayTime != null">
        day_time,
      </if>
      <if test="flower != null">
        flower,
      </if>
      <if test="totalLiveTime != null">
        total_live_time,
      </if>
      <if test="viewer != null">
        viewer,
      </if>
      <if test="send != null">
        send,
      </if>
      <if test="fans != null">
        fans,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="isLive != null">
        is_live,
      </if>
      <if test="isValidLive != null">
        is_valid_live,
      </if>
      <if test="isDisable != null">
        is_disable,
      </if>
      <if test="isStill != null">
        is_still,
      </if>
      <if test="isNoLive != null">
        is_no_live,
      </if>
      <if test="isZero != null">
        is_zero,
      </if>
      <if test="isNew != null">
        is_new,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hostUuid != null">
        #{hostUuid,jdbcType=CHAR},
      </if>
      <if test="consortiaId != null">
        #{consortiaId,jdbcType=INTEGER},
      </if>
      <if test="hostAmount != null">
        #{hostAmount,jdbcType=INTEGER},
      </if>
      <if test="dayTime != null">
        #{dayTime,jdbcType=INTEGER},
      </if>
      <if test="flower != null">
        #{flower,jdbcType=INTEGER},
      </if>
      <if test="totalLiveTime != null">
        #{totalLiveTime,jdbcType=INTEGER},
      </if>
      <if test="viewer != null">
        #{viewer,jdbcType=INTEGER},
      </if>
      <if test="send != null">
        #{send,jdbcType=INTEGER},
      </if>
      <if test="fans != null">
        #{fans,jdbcType=INTEGER},
      </if>
      <if test="message != null">
        #{message,jdbcType=INTEGER},
      </if>
      <if test="isLive != null">
        #{isLive,jdbcType=INTEGER},
      </if>
      <if test="isValidLive != null">
        #{isValidLive,jdbcType=INTEGER},
      </if>
      <if test="isDisable != null">
        #{isDisable,jdbcType=INTEGER},
      </if>
      <if test="isStill != null">
        #{isStill,jdbcType=INTEGER},
      </if>
      <if test="isNoLive != null">
        #{isNoLive,jdbcType=INTEGER},
      </if>
      <if test="isZero != null">
        #{isZero,jdbcType=INTEGER},
      </if>
      <if test="isNew != null">
        #{isNew,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.live.entity.LiveHostStatisticsMonth">
    update live_host_statistics_month
    <set>
      <if test="hostUuid != null">
        host_uuid = #{hostUuid,jdbcType=CHAR},
      </if>
      <if test="consortiaId != null">
        consortia_id = #{consortiaId,jdbcType=INTEGER},
      </if>
      <if test="hostAmount != null">
        host_amount = #{hostAmount,jdbcType=INTEGER},
      </if>
      <if test="dayTime != null">
        day_time = #{dayTime,jdbcType=INTEGER},
      </if>
      <if test="flower != null">
        flower = #{flower,jdbcType=INTEGER},
      </if>
      <if test="totalLiveTime != null">
        total_live_time = #{totalLiveTime,jdbcType=INTEGER},
      </if>
      <if test="viewer != null">
        viewer = #{viewer,jdbcType=INTEGER},
      </if>
      <if test="send != null">
        send = #{send,jdbcType=INTEGER},
      </if>
      <if test="fans != null">
        fans = #{fans,jdbcType=INTEGER},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=INTEGER},
      </if>
      <if test="isLive != null">
        is_live = #{isLive,jdbcType=INTEGER},
      </if>
      <if test="isValidLive != null">
        is_valid_live = #{isValidLive,jdbcType=INTEGER},
      </if>
      <if test="isDisable != null">
        is_disable = #{isDisable,jdbcType=INTEGER},
      </if>
      <if test="isStill != null">
        is_still = #{isStill,jdbcType=INTEGER},
      </if>
      <if test="isNoLive != null">
        is_no_live = #{isNoLive,jdbcType=INTEGER},
      </if>
      <if test="isZero != null">
        is_zero = #{isZero,jdbcType=INTEGER},
      </if>
      <if test="isNew != null">
        is_new = #{isNew,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.live.entity.LiveHostStatisticsMonth">
    update live_host_statistics_month
    set host_uuid = #{hostUuid,jdbcType=CHAR},
      consortia_id = #{consortiaId,jdbcType=INTEGER},
      host_amount = #{hostAmount,jdbcType=INTEGER},
      day_time = #{dayTime,jdbcType=INTEGER},
      flower = #{flower,jdbcType=INTEGER},
      total_live_time = #{totalLiveTime,jdbcType=INTEGER},
      viewer = #{viewer,jdbcType=INTEGER},
      send = #{send,jdbcType=INTEGER},
      fans = #{fans,jdbcType=INTEGER},
      message = #{message,jdbcType=INTEGER},
      is_live = #{isLive,jdbcType=INTEGER},
      is_valid_live = #{isValidLive,jdbcType=INTEGER},
      is_disable = #{isDisable,jdbcType=INTEGER},
      is_still = #{isStill,jdbcType=INTEGER},
      is_no_live = #{isNoLive,jdbcType=INTEGER},
      is_zero = #{isZero,jdbcType=INTEGER},
      is_new = #{isNew,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getNumByOrgUuid"  parameterType="cn.taqu.gonghui.live.search.LiveCommonSearch" resultType="cn.taqu.gonghui.live.vo.LiveCommonVo">
    select
      count(*) newHostNum,
      SUM(host_amount) orgAmount,
      SUM(total_live_time) totalLiveTime
    from live_host_statistics_month
    <where>
      <if test="uuidList != null and uuidList.size() > 0">
        and host_uuid in
        <foreach item="item" index="index" collection="uuidList"
                 open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="dayTime != null">
        and day_time = #{dayTime}
      </if>
    </where>
  </select>

  <select id="getNumByHostUuid"  parameterType="cn.taqu.gonghui.live.search.LiveCommonSearch" resultType="cn.taqu.gonghui.live.vo.TopHost">
    select
    SUM(host_amount) topHostAmount,
    count(*) topHostNum
    from live_host_statistics_month
    <where>
      <if test="uuidList != null and uuidList.size() > 0">
        and consortia_id in
        <foreach item="item" index="index" collection="uuidList"
                 open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="dayTime != null">
        and day_time = #{dayTime}
      </if>
    </where>
  </select>
</mapper>