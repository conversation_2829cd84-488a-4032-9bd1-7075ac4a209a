<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.common.mapper.ApprovalFlowLogMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.common.entity.ApprovalFlowLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="flow_id" jdbcType="BIGINT" property="flowId" />
    <result column="flow_status" jdbcType="INTEGER" property="flowStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="user_time_info" jdbcType="VARCHAR" property="userTimeInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>

  <sql id="Base_Column_List">
    id, flow_id, flow_status, remark, user_time_info, create_time, create_user
  </sql>

  <insert id="insert">
    insert into approval_flow_log(flow_id, flow_status, remark, user_time_info, create_user)
    values(#{flowId},#{flowStatus},#{remark},#{userTimeInfo},#{createUser})
  </insert>

  <insert id="insertBatch">
    INSERT INTO approval_flow_log
    (flow_id, flow_status, remark, user_time_info, create_user)
    VALUES
    <foreach collection="logList" item="item" separator =",">
      (#{item.flowId}, #{item.flowStatus}, #{item.remark},#{item.userTimeInfo},#{item.createUser})
    </foreach >
  </insert>

    <select id="selectByFlowId" resultType="cn.taqu.gonghui.common.entity.ApprovalFlowLog">
    SELECT <include refid="Base_Column_List"></include> FROM approval_flow_log
    WHERE is_del = 0 AND flow_id = #{flowId}
  </select>

</mapper>