<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.live.mapper.EmployeeMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.Employee">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="employee_name" jdbcType="VARCHAR" property="employeeName" />
    <result column="employee_id_card_cipher" jdbcType="VARCHAR" property="employeeIdCardCipher" />
    <result column="employee_id_card_sm3" jdbcType="VARCHAR" property="employeeIdCardSm3" />
    <result column="employee_mobile_cipher" jdbcType="VARCHAR" property="employeeMobileCipher" />
    <result column="employee_mobile_sm3" jdbcType="VARCHAR" property="employeeMobileSm3" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="employee_uuid" jdbcType="VARCHAR" property="employeeUuid" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
    <result column="valid" jdbcType="TINYINT" property="valid" />

  </resultMap>
  <sql id="Base_Column_List">
    id, employee_name, employee_id_card_cipher, employee_id_card_sm3, employee_mobile_cipher, employee_mobile_sm3, account_uuid,employee_uuid,create_time,update_time,valid
  </sql>

  <select id="selectAll" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from employee
  </select>


  <select id="getEmployeeList" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
      t1.*
    FROM
      employee t1
        LEFT JOIN guild_info t2 ON t1.account_uuid = t2.account_uuid
    WHERE
      t2.uuid = #{orgUuid}
  </select>

    <select id="selectOne" resultType="cn.taqu.gonghui.live.entity.Employee">
        SELECT employee_id,employee_name FROM team_employee
        WHERE employee_id = #{id} LIMIT 1
    </select>

  <select id="selectById" resultType="cn.taqu.gonghui.live.entity.Employee">
    select
    <include refid="Base_Column_List" />
    from employee where id = #{id}
  </select>

  <select id="selectByUuidList" resultType="cn.taqu.gonghui.live.entity.Employee">
    SELECT <include refid="Base_Column_List"></include> FROM employee
    WHERE employee_uuid IN
    <foreach item="item" index="index" collection="uuidList" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

</mapper>
