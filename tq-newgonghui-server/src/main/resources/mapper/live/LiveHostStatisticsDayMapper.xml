<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.live.mapper.LiveHostStatisticsDayMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.LiveHostStatisticsDay" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="host_uuid" property="hostUuid" jdbcType="CHAR" />
    <result column="consortia_id" property="consortiaId" jdbcType="INTEGER" />
    <result column="host_amount" property="hostAmount" jdbcType="INTEGER" />
    <result column="day_time" property="dayTime" jdbcType="INTEGER" />
    <result column="flower" property="flower" jdbcType="INTEGER" />
    <result column="total_live_time" property="totalLiveTime" jdbcType="INTEGER" />
    <result column="viewer" property="viewer" jdbcType="INTEGER" />
    <result column="send" property="send" jdbcType="INTEGER" />
    <result column="fans" property="fans" jdbcType="INTEGER" />
    <result column="message" property="message" jdbcType="INTEGER" />
    <result column="host_status" property="hostStatus" jdbcType="INTEGER" />
    <result column="is_live" property="isLive" jdbcType="INTEGER" />
    <result column="is_valid_live" property="isValidLive" jdbcType="INTEGER" />
    <result column="is_disable" property="isDisable" jdbcType="INTEGER" />
    <result column="is_still" property="isStill" jdbcType="INTEGER" />
    <result column="is_no_live" property="isNoLive" jdbcType="INTEGER" />
    <result column="is_zero" property="isZero" jdbcType="INTEGER" />
    <result column="is_new" property="isNew" jdbcType="INTEGER" />
    <result column="total_fans" property="totalFans" jdbcType="INTEGER" />
    <result column="businessman_uuid" property="businessmanUuid" jdbcType="VARCHAR" />
    <result column="game_amount" property="gameAmount" jdbcType="INTEGER" />


  </resultMap>
  <sql id="Base_Column_List" >
    id, host_uuid, consortia_id, host_amount, day_time, flower, total_live_time, viewer,
    send, fans, message, host_status, is_live, is_valid_live, is_disable, is_still, is_no_live,
    is_zero, is_new, total_fans, businessman_uuid
  </sql>

  <select id="getData" parameterType="cn.taqu.gonghui.live.search.LiveCommonSearch" resultType="cn.taqu.gonghui.live.vo.LiveCommonVo">
    SELECT
    count(*) hostNum,
    SUM(host_amount) amount,
    SUM(flower) flower,
    SUM(total_live_time) totalLiveTime,
    SUM(viewer) viewer,
    SUM(send) send,
    SUM(message) message,
    SUM(fans) fans
    FROM
    live_host_statistics_day
    <where>
      <if test="uuidList != null and uuidList.size() > 0">
        and host_uuid in
        <foreach item="item" index="index" collection="uuidList"
                 open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="dayTime != null">
        and day_time = #{dayTime}
      </if>
    </where>
  </select>

  <select id="getLiveHostStatisticsDayByHostUuidList" parameterType="cn.taqu.gonghui.live.search.LiveHostSearch" resultMap="BaseResultMap">
    SELECT
    *
    FROM
    live_host_statistics_day_${dayTime}
    <where>
      <if test="uuidList != null and uuidList.size() > 0">
        and host_uuid in
        <foreach item="item" index="index" collection="uuidList"
                 open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>

  </select>

</mapper>
