<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.live.mapper.OperationSpecialistMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.OperationSpecialist">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, status, create_time, update_time
  </sql>
  <select id="selectAll" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from operation_specialist
  </select>

</mapper>
