<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.live.mapper.GuildInfoMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.GuildInfo" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="uuid" property="uuid" jdbcType="INTEGER" />
    <result column="guild_name" property="guildName" jdbcType="VARCHAR" />
    <result column="charge_person" property="chargePerson" jdbcType="VARCHAR" />
    <result column="charge_person_id_card" property="chargePersonIdCard" jdbcType="VARCHAR" />
    <result column="charge_person_phone" property="chargePersonPhone" jdbcType="VARCHAR" />
    <result column="charge_person_email" property="chargePersonEmail" jdbcType="VARCHAR" />
    <result column="charge_person_birthday" property="chargePersonBirthday" jdbcType="INTEGER" />
    <result column="receiving_address" property="receivingAddress" jdbcType="VARCHAR" />
    <result column="legal_person" property="legalPerson" jdbcType="VARCHAR" />
    <result column="legal_person_id_card" property="legalPersonIdCard" jdbcType="VARCHAR" />
    <result column="public_receiving_bank_account" property="publicReceivingBankAccount" jdbcType="VARCHAR" />
    <result column="account_name" property="accountName" jdbcType="VARCHAR" />
    <result column="account_bank_name" property="accountBankName" jdbcType="VARCHAR" />
    <result column="province" property="province" jdbcType="VARCHAR" />
    <result column="province_id" property="provinceId" jdbcType="INTEGER" />
    <result column="city" property="city" jdbcType="VARCHAR" />
    <result column="city_id" property="cityId" jdbcType="INTEGER" />
    <result column="sub_branch_name" property="subBranchName" jdbcType="VARCHAR" />
    <result column="guild_status" property="guildStatus" jdbcType="TINYINT" />
    <result column="apply_status" property="applyStatus" jdbcType="TINYINT" />
    <result column="business_person" property="businessPerson" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
    <result column="charge_person_vx" property="chargePersonVx" jdbcType="VARCHAR" />
    <result column="form_status" property="formStatus" jdbcType="TINYINT" />
    <result column="account_uuid" property="accountUuid" jdbcType="VARCHAR" />
    <result column="audit_msg" property="auditMsg" jdbcType="VARCHAR" />
    <result column="charge_person_phone_cipher" property="chargePersonPhoneCipher" jdbcType="VARCHAR" />
    <result column="charge_person_email_cipher" property="chargePersonEmailCipher" jdbcType="VARCHAR" />
    <result column="charge_person_cipher" property="chargePersonCipher" jdbcType="VARCHAR" />
    <result column="charge_person_vx_cipher" property="chargePersonVxCipher" jdbcType="VARCHAR" />
    <result column="charge_person_id_card_cipher" property="chargePersonIdCardCipher" jdbcType="VARCHAR" />
    <result column="receiving_address_cipher" property="receivingAddressCipher" jdbcType="VARCHAR" />
    <result column="legal_person_cipher" property="legalPersonCipher" jdbcType="VARCHAR" />
    <result column="legal_person_id_card_cipher" property="legalPersonIdCardCipher" jdbcType="VARCHAR" />
    <result column="public_receiving_bank_account_cipher" property="publicReceivingBankAccountCipher" jdbcType="VARCHAR" />
    <result column="account_name_cipher" property="accountNameCipher" jdbcType="VARCHAR" />
    <result column="account_bank_name_cipher" property="accountBankNameCipher" jdbcType="VARCHAR" />
    <result column="sub_branch_name_cipher" property="subBranchNameCipher" jdbcType="VARCHAR" />
    <result column="business_person_cipher" property="businessPersonCipher" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, uuid, guild_name, charge_person, charge_person_id_card, charge_person_phone,
    charge_person_email, charge_person_birthday, receiving_address, legal_person, legal_person_id_card,
    public_receiving_bank_account, account_name, account_bank_name, province, province_id,
    city, city_id, sub_branch_name, guild_status, apply_status, business_person, create_time,
    update_time, charge_person_vx, form_status, account_uuid, audit_msg, charge_person_phone_cipher,
    charge_person_email_cipher, charge_person_cipher, charge_person_vx_cipher, charge_person_id_card_cipher,
    receiving_address_cipher, legal_person_cipher, legal_person_id_card_cipher, public_receiving_bank_account_cipher,
    account_name_cipher, account_bank_name_cipher, sub_branch_name_cipher, business_person_cipher
  </sql>

  <select id="selectByUuid" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from guild_info
    where uuid = #{uuid,jdbcType=INTEGER}
  </select>

  <select id="getGuildList" resultMap="BaseResultMap">
    select id,uuid,guild_name,account_uuid from guild_info where guild_status = 1
  </select>

  <update id="updateGuildInfoStatus">
    update guild_info set guild_status = #{status} where uuid = #{uuid}
  </update>
</mapper>
