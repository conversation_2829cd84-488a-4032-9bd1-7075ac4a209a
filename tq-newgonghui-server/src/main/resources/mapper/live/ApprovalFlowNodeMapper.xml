<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.common.mapper.ApprovalFlowNodeMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.common.entity.ApprovalFlowNode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="flow_id" jdbcType="BIGINT" property="flowId" />
    <result column="node_index" jdbcType="INTEGER" property="nodeIndex" />
    <result column="next_index" jdbcType="INTEGER" property="nextIndex" />
    <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
    <result column="node_role" jdbcType="INTEGER" property="nodeRole" />
    <result column="node_user" jdbcType="VARCHAR" property="nodeUser" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="mobile_cipher" jdbcType="VARCHAR" property="mobileCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler"/>
    <result column="mobile_digest" jdbcType="VARCHAR" property="mobileDigest" typeHandler="cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler"/>
    <result column="node_status" jdbcType="INTEGER" property="nodeStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="review_time" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modify_user" jdbcType="VARCHAR" property="modifyUser" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>

  <sql id="Base_Column_List">
    id, flow_id, node_index, next_index, node_name, node_role, node_user, mobile, mobile_cipher, mobile_digest, node_status,
    remark, review_time, create_time, create_user, modify_time, modify_user, is_del
  </sql>

  <insert id="insertBatch">
    INSERT INTO approval_flow_node
    (flow_id, node_index, next_index, node_name, node_role, node_user, mobile,mobile_cipher, mobile_digest, node_status
    , create_time, create_user, modify_time, modify_user)
    VALUES
    <foreach collection="nodeList" item="node" separator =",">
      (#{node.flowId}, #{node.nodeIndex}, #{node.nextIndex},#{node.nodeName},#{node.nodeRole},#{node.nodeUser}
      ,#{node.mobile,typeHandler=cn.taqu.gonghui.system.common.SetNullTypeHandler}
      ,#{node.mobileCipher,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler}
      ,#{node.mobileDigest, typeHandler=cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler}
      ,#{node.nodeStatus},#{node.createTime},#{node.createUser},#{node.modifyTime},#{node.modifyUser})
    </foreach >
  </insert>

  <update id="updateByRecord" parameterType="cn.taqu.gonghui.common.entity.ApprovalFlowNode">
    update approval_flow_node
    <set>
      <if test="nodeStatus != null">
        node_status = #{nodeStatus},
      </if>
      <if test="reviewTime != null">
        review_time = #{reviewTime},
      </if>
      <if test="remark != null">
        remark = #{remark},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime},
      </if>
      <if test="modifyUser != null">
        modify_user = #{modifyUser},
      </if>
    </set>
    WHERE id = #{id}
  </update>

  <update id="updateBatch">
    update approval_flow_node
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="node_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.nodeStatus}
        </foreach>
      </trim>
      <trim prefix="modify_user = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.modifyUser}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>

  <select id="selectCurrentNode" resultMap="BaseResultMap">
<!--          resultType="cn.taqu.gonghui.common.entity.ApprovalFlowNode">-->
    SELECT <include refid="Base_Column_List"></include> FROM approval_flow_node WHERE is_del = 0
    AND flow_id = #{flowId} AND node_status = 0 ORDER BY id ASC LIMIT 1;
  </select>

  <select id="selectByFlowId" resultMap="BaseResultMap" >
<!--          resultType="cn.taqu.gonghui.common.entity.ApprovalFlowNode">-->
    SELECT <include refid="Base_Column_List"></include> FROM approval_flow_node
    WHERE is_del = 0
    AND flow_id = #{flowId}
    ORDER BY id DESC
  </select>

  <select id="selectByCondition" resultMap="BaseResultMap" >
<!--          resultType="cn.taqu.gonghui.common.entity.ApprovalFlowNode">-->
    SELECT <include refid="Base_Column_List"></include> FROM approval_flow_node
    WHERE is_del = 0
    <if test="flowId != null">
      AND flow_id = #{flowId}
    </if>
    <if test="nodeUser != null">
      AND node_user = #{nodeUser}
    </if>
  </select>

  <select id="selectOneByCondition" resultMap="BaseResultMap" >
<!--          resultType="cn.taqu.gonghui.common.entity.ApprovalFlowNode">-->
    SELECT <include refid="Base_Column_List"></include> FROM approval_flow_node
    WHERE is_del = 0
    <if test="flowId != null">
      AND flow_id = #{flowId}
    </if>
    <if test="nodeUser != null">
      AND node_user = #{nodeUser}
    </if>
    <if test="nodeStatus != null">
      AND node_status = #{nodeStatus}
    </if>
    ORDER BY id ASC LIMIT 1;
  </select>

  <select id="selectByCustom" resultMap="BaseResultMap" >
<!--          resultType="cn.taqu.gonghui.common.entity.ApprovalFlowNode">-->
    SELECT fn.id,fn.flow_id,fn.mobile,fn.node_user,f.host_uuid as create_user FROM approval_flow_node fn LEFT JOIN approval_flow f ON fn.flow_id = f.id
    WHERE fn.is_del = 0 AND f.flow_status = 0
    <if test="nodeStatus != null">
      AND fn.node_status = #{nodeStatus}
    </if>
    <if test="flowType != null">
      AND f.flow_type = #{flowType}
    </if>
    <if test="afterCreateTime != null">
      AND fn.create_time > #{afterCreateTime}
    </if>
    <if test="preCreateTime != null">
      AND #{preCreateTime} > fn.create_time
    </if>
  </select>

    <update id="updateClearTxtByRange">
        update approval_flow_node set  mobile = '' where id between #{curStartId,jdbcType=BIGINT} and #{curEndId,jdbcType=BIGINT};
    </update>

</mapper>