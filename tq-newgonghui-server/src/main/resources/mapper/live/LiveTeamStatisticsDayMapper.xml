<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.live.mapper.LiveTeamStatisticsDayMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.LiveTeamStatisticsDay" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="team_id" property="teamId" jdbcType="INTEGER" />
    <result column="day_time" property="dayTime" jdbcType="INTEGER" />
    <result column="live" property="live" jdbcType="INTEGER" />
    <result column="new" property="newNum" jdbcType="INTEGER" />
    <result column="no_live" property="noLive" jdbcType="INTEGER" />
    <result column="disable" property="disable" jdbcType="INTEGER" />
    <result column="still" property="still" jdbcType="INTEGER" />
    <result column="zero" property="zero" jdbcType="INTEGER" />
    <result column="amount" property="amount" jdbcType="INTEGER" />
    <result column="total_live_time" property="totalLiveTime" jdbcType="INTEGER" />
    <result column="flower" property="flower" jdbcType="INTEGER" />
    <result column="viewer" property="viewer" jdbcType="INTEGER" />
    <result column="send" property="send" jdbcType="INTEGER" />
    <result column="fans" property="fans" jdbcType="INTEGER" />
    <result column="message" property="message" jdbcType="INTEGER" />
    <result column="host_num" property="hostNum" jdbcType="INTEGER" />
    <result column="total_fans" property="totalFans" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, team_id, day_time, live, new, no_live, disable, still, zero, amount, total_live_time,
    flower, viewer, send, fans, message, host_num, total_fans, create_time
  </sql>
  <insert id="insert" parameterType="cn.taqu.gonghui.live.entity.LiveTeamStatisticsDay" >
    insert into live_team_statistics_day
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="teamId != null" >
        team_id,
      </if>
      <if test="dayTime != null" >
        day_time,
      </if>
      <if test="live != null" >
        live,
      </if>
      <if test="newNum != null" >
        new,
      </if>
      <if test="noLive != null" >
        no_live,
      </if>
      <if test="disable != null" >
        disable,
      </if>
      <if test="still != null" >
        still,
      </if>
      <if test="zero != null" >
        zero,
      </if>
      <if test="amount != null" >
        amount,
      </if>
      <if test="totalLiveTime != null" >
        total_live_time,
      </if>
      <if test="flower != null" >
        flower,
      </if>
      <if test="viewer != null" >
        viewer,
      </if>
      <if test="send != null" >
        send,
      </if>
      <if test="fans != null" >
        fans,
      </if>
      <if test="message != null" >
        message,
      </if>
      <if test="hostNum != null" >
        host_num,
      </if>
      <if test="totalFans != null" >
        total_fans,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="teamId != null" >
        #{teamId,jdbcType=INTEGER},
      </if>
      <if test="dayTime != null" >
        #{dayTime,jdbcType=INTEGER},
      </if>
      <if test="live != null" >
        #{live,jdbcType=INTEGER},
      </if>
      <if test="newNum != null" >
        #{new,jdbcType=INTEGER},
      </if>
      <if test="noLive != null" >
        #{noLive,jdbcType=INTEGER},
      </if>
      <if test="disable != null" >
        #{disable,jdbcType=INTEGER},
      </if>
      <if test="still != null" >
        #{still,jdbcType=INTEGER},
      </if>
      <if test="zero != null" >
        #{zero,jdbcType=INTEGER},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="totalLiveTime != null" >
        #{totalLiveTime,jdbcType=INTEGER},
      </if>
      <if test="flower != null" >
        #{flower,jdbcType=INTEGER},
      </if>
      <if test="viewer != null" >
        #{viewer,jdbcType=INTEGER},
      </if>
      <if test="send != null" >
        #{send,jdbcType=INTEGER},
      </if>
      <if test="fans != null" >
        #{fans,jdbcType=INTEGER},
      </if>
      <if test="message != null" >
        #{message,jdbcType=INTEGER},
      </if>
      <if test="hostNum != null" >
        #{hostNum,jdbcType=INTEGER},
      </if>
      <if test="totalFans != null" >
        #{totalFans,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="queryByCondition" parameterType="cn.taqu.gonghui.live.search.OrgStatisticsSearch" resultType="cn.taqu.gonghui.system.vo.DailyStatisticsVo">
    SELECT
    day_time dayTime,
    host_num hostNum,
    amount amount,
    flower flower,
    total_live_time totalLiveTime,
    viewer viewer,
    send send,
    fans fans,
    message message
    FROM
    live_team_statistics_day
    <where>
      <if test="consortiaIdList != null and consortiaIdList.size() > 0">
        and consortia_id in
        <foreach item="item" index="index" collection="consortiaIdList"
                 open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="startTime != null">
        and day_time <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        and day_time <![CDATA[ <= ]]> #{endTime}
      </if>
    </where>
  </select>

  <delete id="deleteByDayTime">
    delete from live_team_statistics_day where day_time <![CDATA[ >= ]]> #{startTime} and day_time <![CDATA[ <= ]]> #{endTime}
  </delete>
</mapper>
