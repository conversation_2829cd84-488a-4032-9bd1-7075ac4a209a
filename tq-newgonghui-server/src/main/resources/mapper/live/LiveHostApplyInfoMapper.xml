<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.live.mapper.LiveHostApplyInfoMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.LiveHostApplyInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cover_url" jdbcType="VARCHAR" property="coverUrl" />
    <result column="card_zm_url" jdbcType="VARCHAR" property="cardZmUrl" />
    <result column="card_fm_url" jdbcType="VARCHAR" property="cardFmUrl" />
    <result column="card_sc_url" jdbcType="VARCHAR" property="cardScUrl" />
    <result column="width" jdbcType="SMALLINT" property="width" />
    <result column="height" jdbcType="SMALLINT" property="height" />
    <result column="alipay_account" jdbcType="VARCHAR" property="alipayAccount" />
    <result column="account_mobile" jdbcType="VARCHAR" property="accountMobile" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_uuid" jdbcType="CHAR" property="accountUuid" />
    <result column="video" jdbcType="VARCHAR" property="video" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="theme_id" jdbcType="INTEGER" property="themeId" />
    <result column="tag_id" jdbcType="VARCHAR" property="tagId" />
    <result column="consortia_id" jdbcType="INTEGER" property="consortiaId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="refuse_reason" jdbcType="VARCHAR" property="refuseReason" />
    <result column="admin_name" jdbcType="VARCHAR" property="adminName" />
    <result column="host_intro" jdbcType="VARCHAR" property="hostIntro" />
    <result column="is_download" jdbcType="TINYINT" property="isDownload" />
    <result column="label_id" jdbcType="INTEGER" property="labelId" />
    <result column="apply_from" jdbcType="INTEGER" property="applyFrom" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
    <result column="agreement_video" jdbcType="VARCHAR" property="agreementVideo" />
    <result column="businessman_uuid" jdbcType="VARCHAR" property="businessmanUuid" />
    <result column="refuse_num" jdbcType="INTEGER" property="refuseNum" />
    <result column="alipay_account_cipher" jdbcType="VARCHAR" property="alipayAccountCipher" />
    <result column="account_mobile_cipher" jdbcType="VARCHAR" property="accountMobileCipher" />
    <result column="real_name_cipher" jdbcType="VARCHAR" property="realNameCipher" />
    <result column="id_card_cipher" jdbcType="VARCHAR" property="idCardCipher" />
    <result column="alipay_account_sm3" jdbcType="VARCHAR" property="alipayAccountSm3" />
    <result column="account_mobile_sm3" jdbcType="VARCHAR" property="accountMobileSm3" />
    <result column="real_name_sm3" jdbcType="VARCHAR" property="realNameSm3" />
    <result column="id_card_sm3" jdbcType="VARCHAR" property="idCardSm3" />
  </resultMap>
  <sql id="Base_Column_List">
    id, cover_url, card_zm_url, card_fm_url, card_sc_url, width, height, alipay_account,
    account_mobile, account_name, account_uuid, video, real_name, id_card, theme_id,
    tag_id, consortia_id, `status`, create_time, refuse_reason, admin_name, host_intro,
    is_download, label_id, apply_from, update_time, agreement_video, businessman_uuid,
    refuse_num, alipay_account_cipher, account_mobile_cipher, real_name_cipher, id_card_cipher,
    alipay_account_sm3, account_mobile_sm3, real_name_sm3, id_card_sm3
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from live_host_apply_info
    where id = #{id,jdbcType=BIGINT}
  </select>


  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.taqu.gonghui.live.entity.LiveHostApplyInfo" useGeneratedKeys="true">
    insert into live_host_apply_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="coverUrl != null">
        cover_url,
      </if>
      <if test="cardZmUrl != null">
        card_zm_url,
      </if>
      <if test="cardFmUrl != null">
        card_fm_url,
      </if>
      <if test="cardScUrl != null">
        card_sc_url,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="alipayAccount != null">
        alipay_account,
      </if>
      <if test="accountMobile != null">
        account_mobile,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="accountUuid != null">
        account_uuid,
      </if>
      <if test="video != null">
        video,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="idCard != null">
        id_card,
      </if>
      <if test="themeId != null">
        theme_id,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
      <if test="consortiaId != null">
        consortia_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="refuseReason != null">
        refuse_reason,
      </if>
      <if test="adminName != null">
        admin_name,
      </if>
      <if test="hostIntro != null">
        host_intro,
      </if>
      <if test="isDownload != null">
        is_download,
      </if>
      <if test="labelId != null">
        label_id,
      </if>
      <if test="applyFrom != null">
        apply_from,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="agreementVideo != null">
        agreement_video,
      </if>
      <if test="businessmanUuid != null">
        businessman_uuid,
      </if>
      <if test="refuseNum != null">
        refuse_num,
      </if>
      <if test="alipayAccountCipher != null">
        alipay_account_cipher,
      </if>
      <if test="accountMobileCipher != null">
        account_mobile_cipher,
      </if>
      <if test="realNameCipher != null">
        real_name_cipher,
      </if>
      <if test="idCardCipher != null">
        id_card_cipher,
      </if>
      <if test="alipayAccountSm3 != null">
        alipay_account_sm3,
      </if>
      <if test="accountMobileSm3 != null">
        account_mobile_sm3,
      </if>
      <if test="realNameSm3 != null">
        real_name_sm3,
      </if>
      <if test="idCardSm3 != null">
        id_card_sm3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="coverUrl != null">
        #{coverUrl},
      </if>
      <if test="cardZmUrl != null">
        #{cardZmUrl},
      </if>
      <if test="cardFmUrl != null">
        #{cardFmUrl},
      </if>
      <if test="cardScUrl != null">
        #{cardScUrl},
      </if>
      <if test="width != null">
        #{width,jdbcType=SMALLINT},
      </if>
      <if test="height != null">
        #{height,jdbcType=SMALLINT},
      </if>
      <if test="alipayAccount != null">
        #{alipayAccount},
      </if>
      <if test="accountMobile != null">
        #{accountMobile},
      </if>
      <if test="accountName != null">
        #{accountName},
      </if>
      <if test="accountUuid != null">
        #{accountUuid,jdbcType=CHAR},
      </if>
      <if test="video != null">
        #{video},
      </if>
      <if test="realName != null">
        #{realName},
      </if>
      <if test="idCard != null">
        #{idCard},
      </if>
      <if test="themeId != null">
        #{themeId,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        #{tagId},
      </if>
      <if test="consortiaId != null">
        #{consortiaId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="refuseReason != null">
        #{refuseReason},
      </if>
      <if test="adminName != null">
        #{adminName},
      </if>
      <if test="hostIntro != null">
        #{hostIntro},
      </if>
      <if test="isDownload != null">
        #{isDownload,jdbcType=TINYINT},
      </if>
      <if test="labelId != null">
        #{labelId,jdbcType=INTEGER},
      </if>
      <if test="applyFrom != null">
        #{applyFrom,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="agreementVideo != null">
        #{agreementVideo},
      </if>
      <if test="businessmanUuid != null">
        #{businessmanUuid},
      </if>
      <if test="refuseNum != null">
        #{refuseNum,jdbcType=INTEGER},
      </if>
      <if test="alipayAccountCipher != null">
        #{alipayAccountCipher},
      </if>
      <if test="accountMobileCipher != null">
        #{accountMobileCipher},
      </if>
      <if test="realNameCipher != null">
        #{realNameCipher},
      </if>
      <if test="idCardCipher != null">
        #{idCardCipher},
      </if>
      <if test="alipayAccountSm3 != null">
        #{alipayAccountSm3},
      </if>
      <if test="accountMobileSm3 != null">
        #{accountMobileSm3},
      </if>
      <if test="realNameSm3 != null">
        #{realNameSm3},
      </if>
      <if test="idCardSm3 != null">
        #{idCardSm3},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.live.entity.LiveHostApplyInfo">
    update live_host_apply_info
    <set>
      <if test="coverUrl != null">
        cover_url = #{coverUrl},
      </if>
      <if test="cardZmUrl != null">
        card_zm_url = #{cardZmUrl},
      </if>
      <if test="cardFmUrl != null">
        card_fm_url = #{cardFmUrl},
      </if>
      <if test="cardScUrl != null">
        card_sc_url = #{cardScUrl},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=SMALLINT},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=SMALLINT},
      </if>
      <if test="alipayAccount != null">
        alipay_account = #{alipayAccount},
      </if>
      <if test="accountMobile != null">
        account_mobile = #{accountMobile},
      </if>
      <if test="accountName != null">
        account_name = #{accountName},
      </if>
      <if test="accountUuid != null">
        account_uuid = #{accountUuid,jdbcType=CHAR},
      </if>
      <if test="video != null">
        video = #{video},
      </if>
      <if test="realName != null">
        real_name = #{realName},
      </if>
      <if test="idCard != null">
        id_card = #{idCard},
      </if>
      <if test="themeId != null">
        theme_id = #{themeId,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        tag_id = #{tagId},
      </if>
      <if test="consortiaId != null">
        consortia_id = #{consortiaId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="refuseReason != null">
        refuse_reason = #{refuseReason},
      </if>
      <if test="adminName != null">
        admin_name = #{adminName},
      </if>
      <if test="hostIntro != null">
        host_intro = #{hostIntro},
      </if>
      <if test="isDownload != null">
        is_download = #{isDownload,jdbcType=TINYINT},
      </if>
      <if test="labelId != null">
        label_id = #{labelId,jdbcType=INTEGER},
      </if>
      <if test="applyFrom != null">
        apply_from = #{applyFrom,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="agreementVideo != null">
        agreement_video = #{agreementVideo},
      </if>
      <if test="businessmanUuid != null">
        businessman_uuid = #{businessmanUuid},
      </if>
      <if test="refuseNum != null">
        refuse_num = #{refuseNum,jdbcType=INTEGER},
      </if>
      <if test="alipayAccountCipher != null">
        alipay_account_cipher = #{alipayAccountCipher},
      </if>
      <if test="accountMobileCipher != null">
        account_mobile_cipher = #{accountMobileCipher},
      </if>
      <if test="realNameCipher != null">
        real_name_cipher = #{realNameCipher},
      </if>
      <if test="idCardCipher != null">
        id_card_cipher = #{idCardCipher},
      </if>
      <if test="alipayAccountSm3 != null">
        alipay_account_sm3 = #{alipayAccountSm3},
      </if>
      <if test="accountMobileSm3 != null">
        account_mobile_sm3 = #{accountMobileSm3},
      </if>
      <if test="realNameSm3 != null">
        real_name_sm3 = #{realNameSm3},
      </if>
      <if test="idCardSm3 != null">
        id_card_sm3 = #{idCardSm3},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="countByNotIdAccountUuidAndStatus" resultType="int">
    select count(1) from
    live_host_apply_info
    <where>
      status in (-1, 1, 2, 3)
      and account_uuid = #{accountUuid}
      <if test="id != null" >
        and  id != #{id,jdbcType=BIGINT}
      </if>
    </where>
  </select>

  <select id="selectListByParams" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from live_host_apply_info
    <where>
      <if test="consortiaIdList != null and consortiaIdList.size()>0">
        and consortia_id in
        <foreach collection="consortiaIdList" index="index" item="val" separator="," open="(" close=")">
          #{val,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="businessmanUuid != null and businessmanUuid!=''">
        and businessman_uuid = #{businessmanUuid}
      </if>
      <if test="accountName != null and accountName!=''">
       and account_name = #{accountName}
      </if>
      <if test="accountUuid != null and accountUuid!=''">
        and account_uuid = #{accountUuid}
      </if>
      <if test="status != null and status!=0">
        and status = #{status,jdbcType=INTEGER}
      </if>
    </where>
     order by create_time desc
  </select>

  <select id="selectByUuid" resultType="cn.taqu.gonghui.live.entity.LiveHostApplyInfo">
    select
    <include refid="Base_Column_List"></include>
    from live_host_apply_info
    where account_uuid = #{accountUuid}
  </select>

</mapper>
