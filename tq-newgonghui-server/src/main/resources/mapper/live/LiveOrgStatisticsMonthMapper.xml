<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.live.mapper.LiveOrgStatisticsMonthMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.LiveOrgStatisticsMonth">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="month_time" jdbcType="INTEGER" property="monthTime" />
    <result column="org_num" jdbcType="INTEGER" property="orgNum" />
    <result column="org_flow" jdbcType="INTEGER" property="orgFlow" />
    <result column="org_live_time_sum" jdbcType="VARCHAR" property="orgLiveTimeSum" />
    <result column="new_org_num" jdbcType="INTEGER" property="newOrgNum" />
    <result column="new_org_flow" jdbcType="INTEGER" property="newOrgFlow" />
    <result column="new_host_num" jdbcType="INTEGER" property="newHostNum" />
    <result column="new_host_flow" jdbcType="INTEGER" property="newHostFlow" />
    <result column="total_live_time" jdbcType="INTEGER" property="totalLiveTime" />
    <result column="top_host_num" jdbcType="INTEGER" property="topHostNum" />
    <result column="top_host_flow" jdbcType="INTEGER" property="topHostFlow" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, operator_name, month_time, org_num, org_flow, org_live_time_sum, new_org_num, 
    new_org_flow, new_host_num, new_host_flow, total_live_time, top_host_num, top_host_flow, 
    create_time
  </sql>

  <insert id="insert" parameterType="cn.taqu.gonghui.live.entity.LiveOrgStatisticsMonth">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into live_org_statistics_month (operator_name, month_time, org_num, 
      org_flow, org_live_time_sum, new_org_num, 
      new_org_flow, new_host_num, new_host_flow, 
      total_live_time, top_host_num, top_host_flow, 
      create_time)
    values (#{operatorName}, #{monthTime,jdbcType=INTEGER}, #{orgNum,jdbcType=INTEGER},
      #{orgFlow,jdbcType=INTEGER}, #{orgLiveTimeSum}, #{newOrgNum,jdbcType=INTEGER},
      #{newOrgFlow,jdbcType=INTEGER}, #{newHostNum,jdbcType=INTEGER}, #{newHostFlow,jdbcType=INTEGER}, 
      #{totalLiveTime,jdbcType=INTEGER}, #{topHostNum,jdbcType=INTEGER}, #{topHostFlow,jdbcType=INTEGER}, 
      #{createTime,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.live.entity.LiveOrgStatisticsMonth">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into live_org_statistics_month
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="monthTime != null">
        month_time,
      </if>
      <if test="orgNum != null">
        org_num,
      </if>
      <if test="orgFlow != null">
        org_flow,
      </if>
      <if test="orgLiveTimeSum != null">
        org_live_time_sum,
      </if>
      <if test="newOrgNum != null">
        new_org_num,
      </if>
      <if test="newOrgFlow != null">
        new_org_flow,
      </if>
      <if test="newHostNum != null">
        new_host_num,
      </if>
      <if test="newHostFlow != null">
        new_host_flow,
      </if>
      <if test="totalLiveTime != null">
        total_live_time,
      </if>
      <if test="topHostNum != null">
        top_host_num,
      </if>
      <if test="topHostFlow != null">
        top_host_flow,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="operatorName != null">
        #{operatorName},
      </if>
      <if test="monthTime != null">
        #{monthTime,jdbcType=INTEGER},
      </if>
      <if test="orgNum != null">
        #{orgNum,jdbcType=INTEGER},
      </if>
      <if test="orgFlow != null">
        #{orgFlow,jdbcType=INTEGER},
      </if>
      <if test="orgLiveTimeSum != null">
        #{orgLiveTimeSum},
      </if>
      <if test="newOrgNum != null">
        #{newOrgNum,jdbcType=INTEGER},
      </if>
      <if test="newOrgFlow != null">
        #{newOrgFlow,jdbcType=INTEGER},
      </if>
      <if test="newHostNum != null">
        #{newHostNum,jdbcType=INTEGER},
      </if>
      <if test="newHostFlow != null">
        #{newHostFlow,jdbcType=INTEGER},
      </if>
      <if test="totalLiveTime != null">
        #{totalLiveTime,jdbcType=INTEGER},
      </if>
      <if test="topHostNum != null">
        #{topHostNum,jdbcType=INTEGER},
      </if>
      <if test="topHostFlow != null">
        #{topHostFlow,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="queryByCondition" parameterType="cn.taqu.gonghui.live.search.OrgStatisticsSearch" resultType="cn.taqu.gonghui.live.entity.LiveOrgStatisticsMonth">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    live_org_statistics_month
    <where>
      <if test="businessPerson != null and businessPerson != ''">
        and operator_name = #{businessPerson}
      </if>
      <if test="startTime != null">
        and month_time <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        and month_time <![CDATA[ <= ]]> #{endTime}
      </if>
    </where>
  </select>
</mapper>