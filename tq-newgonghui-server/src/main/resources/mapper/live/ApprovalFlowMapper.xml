<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.common.mapper.ApprovalFlowMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.common.entity.ApprovalFlow">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="flow_title" jdbcType="VARCHAR" property="flowTitle" />
    <result column="flow_type" jdbcType="INTEGER" property="flowType" />
    <result column="flow_status" jdbcType="INTEGER" property="flowStatus" />
    <result column="apply_reason" jdbcType="VARCHAR" property="applyReason" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="team_id" jdbcType="INTEGER" property="teamId" />
    <result column="host_uuid" jdbcType="VARCHAR" property="hostUuid" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="apply_level" jdbcType="VARCHAR" property="applyLevel" />
    <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
    <result column="live_no" jdbcType="VARCHAR" property="liveNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="modify_user" jdbcType="VARCHAR" property="modifyUser" />
    <result column="is_del" jdbcType="INTEGER" property="isDel" />
  </resultMap>

  <sql id="Base_Column_List">
    id, flow_title, flow_type, flow_status, apply_reason, apply_time, org_id, org_name,team_id, host_uuid, nick_name,
    avatar, apply_level,reject_reason,live_no,create_time, create_user, modify_time, modify_user,is_del
  </sql>

  <insert id="insertGetId" useGeneratedKeys="true" keyProperty="id" parameterType="cn.taqu.gonghui.common.entity.ApprovalFlow">
    insert into approval_flow(flow_title, flow_type, flow_status, apply_reason, apply_time, org_id, org_name,team_id, host_uuid, nick_name,
                              avatar, apply_level, live_no, create_time, create_user, modify_time,modify_user)
    values(#{flowTitle},#{flowType},#{flowStatus},#{applyReason},#{applyTime},#{orgId},#{orgName},#{teamId},#{hostUuid},#{nickName}
          ,#{avatar},#{applyLevel},#{liveNo},#{createTime},#{createUser},#{modifyTime},#{modifyUser})
  </insert>

  <update id="updateByRecord">
    update approval_flow
    <set>
      <if test="applyReason != null">
        apply_reason = #{applyReason},
      </if>
      <if test="flowStatus != null">
        flow_status = #{flowStatus},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime},
      </if>
      <if test="rejectReason != null and rejectReason != ''">
        reject_reason = #{rejectReason},
      </if>
      <if test="modifyUser != null">
        modify_user = #{modifyUser},
      </if>
    </set>
    where id = #{id}
  </update>

  <select id="selectOneById" resultType="cn.taqu.gonghui.common.entity.ApprovalFlow">
    SELECT <include refid="Base_Column_List"></include> FROM approval_flow
    WHERE is_del = 0 AND id = #{id}
  </select>

  <select id="selectOneByCondition" resultType="cn.taqu.gonghui.common.entity.ApprovalFlow">
    SELECT <include refid="Base_Column_List"></include> FROM approval_flow
    WHERE is_del = 0
    <if test="teamId != null">
      AND team_id = #{teamId}
    </if>
    <if test="hostUuid != null">
      AND host_uuid = #{hostUuid}
    </if>
    <if test="flowStatus != null">
      AND flow_status = #{flowStatus}
    </if>
    ORDER BY id DESC LIMIT 1
  </select>

  <select id="selectByCondition" resultType="cn.taqu.gonghui.common.entity.ApprovalFlow">
    SELECT <include refid="Base_Column_List"></include> FROM approval_flow
    WHERE is_del = 0
    <if test="teamId != null">
      AND team_id = #{teamId}
    </if>
    <if test="hostUuid != null and hostUuid != ''">
      AND host_uuid = #{hostUuid}
    </if>
    <if test="flowType != null and flowType != ''">
      AND flow_type = #{flowType}
    </if>
    <if test="flowStatus != null and flowStatus != ''">
      AND flow_status = #{flowStatus}
    </if>
    <if test="orgId != null and orgId != ''">
      AND org_id = #{orgId}
    </if>
    <if test="liveNo != null and liveNo != ''">
      AND live_no = #{liveNo}
    </if>
    ORDER BY id DESC
  </select>

  <select id="selectBatchIdList" resultType="cn.taqu.gonghui.common.entity.ApprovalFlow">
    SELECT <include refid="Base_Column_List"></include> FROM approval_flow
    WHERE is_del = 0
    AND id IN
    <foreach collection="idList" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
  </select>

  <select id="selectByCustom" resultType="cn.taqu.gonghui.common.entity.ApprovalFlow">
    SELECT id,host_uuid,org_name FROM approval_flow WHERE is_del = 0
    <if test="flowType != null">
      AND flow_type = #{flowType}
    </if>
    <if test="flowStatus != null">
      AND flow_status = #{flowStatus}
    </if>
    <if test="flowStatus != null">
      AND flow_status = #{flowStatus}
    </if>
    <if test="afterCreateTime != null">
      AND create_time > #{afterCreateTime}
    </if>
    <if test="preCreateTime != null">
      AND #{preCreateTime} > create_time
    </if>
  </select>

  <update id="updateSql">
    ${sql}
  </update>

  <update id="updateBatchStatus">
    update approval_flow set flow_status = 1
    where id in
    <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
  </update>

</mapper>