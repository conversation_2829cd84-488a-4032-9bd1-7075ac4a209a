<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.live.mapper.GuildAgreementInfoMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.GuildAgreementInfo" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="order_level" property="orderLevel" jdbcType="INTEGER" />
    <result column="version" property="version" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="valid" property="valid" jdbcType="TINYINT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="cn.taqu.gonghui.live.entity.GuildAgreementInfo" extends="BaseResultMap" >
    <result column="content" property="content" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, order_level, version, create_time, update_time, operator, title, valid
  </sql>
  <sql id="Blob_Column_List" >
    content
  </sql>




  <select id="selectAll" resultMap="ResultMapWithBLOBs" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from guild_agreement_info
  </select>
</mapper>
