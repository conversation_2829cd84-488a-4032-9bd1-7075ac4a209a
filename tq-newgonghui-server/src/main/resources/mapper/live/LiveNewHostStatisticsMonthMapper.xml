<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.live.mapper.LiveNewHostStatisticsMonthMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.LiveNewHostStatisticsMonth">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="host_uuid" jdbcType="CHAR" property="hostUuid" />
    <result column="consortia_id" jdbcType="INTEGER" property="consortiaId" />
    <result column="host_amount" jdbcType="INTEGER" property="hostAmount" />
    <result column="month_time" jdbcType="INTEGER" property="monthTime" />
    <result column="total_live_time" jdbcType="INTEGER" property="totalLiveTime" />
    <result column="valid_day" jdbcType="INTEGER" property="validDay" />
  </resultMap>
  <sql id="Base_Column_List">
    id, host_uuid, consortia_id, host_amount, month_time, total_live_time, valid_day
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from live_new_host_statistics_month
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from live_new_host_statistics_month
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="cn.taqu.gonghui.live.entity.LiveNewHostStatisticsMonth">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into live_new_host_statistics_month (host_uuid, consortia_id, host_amount, 
      month_time, total_live_time, valid_day
      )
    values (#{hostUuid,jdbcType=CHAR}, #{consortiaId,jdbcType=INTEGER}, #{hostAmount,jdbcType=INTEGER}, 
      #{monthTime,jdbcType=INTEGER}, #{totalLiveTime,jdbcType=INTEGER}, #{validDay,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.taqu.gonghui.live.entity.LiveNewHostStatisticsMonth">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into live_new_host_statistics_month
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hostUuid != null">
        host_uuid,
      </if>
      <if test="consortiaId != null">
        consortia_id,
      </if>
      <if test="hostAmount != null">
        host_amount,
      </if>
      <if test="monthTime != null">
        month_time,
      </if>
      <if test="totalLiveTime != null">
        total_live_time,
      </if>
      <if test="validDay != null">
        valid_day,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hostUuid != null">
        #{hostUuid,jdbcType=CHAR},
      </if>
      <if test="consortiaId != null">
        #{consortiaId,jdbcType=INTEGER},
      </if>
      <if test="hostAmount != null">
        #{hostAmount,jdbcType=INTEGER},
      </if>
      <if test="monthTime != null">
        #{monthTime,jdbcType=INTEGER},
      </if>
      <if test="totalLiveTime != null">
        #{totalLiveTime,jdbcType=INTEGER},
      </if>
      <if test="validDay != null">
        #{validDay,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.taqu.gonghui.live.entity.LiveNewHostStatisticsMonth">
    update live_new_host_statistics_month
    <set>
      <if test="hostUuid != null">
        host_uuid = #{hostUuid,jdbcType=CHAR},
      </if>
      <if test="consortiaId != null">
        consortia_id = #{consortiaId,jdbcType=INTEGER},
      </if>
      <if test="hostAmount != null">
        host_amount = #{hostAmount,jdbcType=INTEGER},
      </if>
      <if test="monthTime != null">
        month_time = #{monthTime,jdbcType=INTEGER},
      </if>
      <if test="totalLiveTime != null">
        total_live_time = #{totalLiveTime,jdbcType=INTEGER},
      </if>
      <if test="validDay != null">
        valid_day = #{validDay,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.taqu.gonghui.live.entity.LiveNewHostStatisticsMonth">
    update live_new_host_statistics_month
    set host_uuid = #{hostUuid,jdbcType=CHAR},
      consortia_id = #{consortiaId,jdbcType=INTEGER},
      host_amount = #{hostAmount,jdbcType=INTEGER},
      month_time = #{monthTime,jdbcType=INTEGER},
      total_live_time = #{totalLiveTime,jdbcType=INTEGER},
      valid_day = #{validDay,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getNewNumByOrgUuid"  parameterType="cn.taqu.gonghui.live.search.LiveCommonSearch" resultType="cn.taqu.gonghui.live.vo.LiveCommonVo">
    select
    count(*) newHostNum,
    SUM(host_amount) newAmount
    from live_host_statistics_month
    <where>
      <if test="uuidList != null and uuidList.size() > 0">
        and host_uuid in
        <foreach item="item" index="index" collection="uuidList"
                 open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="dayTime != null">
        and day_time = #{dayTime}
      </if>
    </where>
  </select>
</mapper>