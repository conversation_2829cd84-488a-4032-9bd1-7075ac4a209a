<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.live.mapper.LiveConsortiaStatisticsDayMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.LiveConsortiaStatisticsDay" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="consortia_id" property="consortiaId" jdbcType="INTEGER" />
    <result column="day_time" property="dayTime" jdbcType="INTEGER" />
    <result column="live" property="live" jdbcType="INTEGER" />
    <result column="new" property="newNum" jdbcType="INTEGER" />
    <result column="no_live" property="noLive" jdbcType="INTEGER" />
    <result column="disable" property="disable" jdbcType="INTEGER" />
    <result column="still" property="still" jdbcType="INTEGER" />
    <result column="zero" property="zero" jdbcType="INTEGER" />
    <result column="amount" property="amount" jdbcType="INTEGER" />
    <result column="total_live_time" property="totalLiveTime" jdbcType="INTEGER" />
    <result column="flower" property="flower" jdbcType="INTEGER" />
    <result column="viewer" property="viewer" jdbcType="INTEGER" />
    <result column="send" property="send" jdbcType="INTEGER" />
    <result column="fans" property="fans" jdbcType="INTEGER" />
    <result column="message" property="message" jdbcType="INTEGER" />
    <result column="host_num" property="hostNum" jdbcType="INTEGER" />
    <result column="total_fans" property="totalFans" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, consortia_id, day_time, live, new, no_live, disable, still, zero, amount, total_live_time,
    flower, viewer, send, fans, message, host_num, total_fans
  </sql>

  <select id="queryByCondition" parameterType="cn.taqu.gonghui.live.search.OrgStatisticsSearch" resultType="cn.taqu.gonghui.system.vo.DailyStatisticsVo">
    SELECT
    consortia_id consortiaId,
    day_time dayTime,
    host_num hostNum,
    amount amount,
    flower flower,
    total_live_time totalLiveTime,
    viewer viewer,
    send send,
    fans fans,
    message message,
    game_amount gameAmount,
    shell_amount shellAmount,
    system_activity_amount systemActivityAmount
    FROM
    live_consortia_statistics_day
    <where>
      <if test="consortiaIdList != null and consortiaIdList.size() > 0">
        and consortia_id in
        <foreach item="item" index="index" collection="consortiaIdList"
                 open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="startTime != null">
        and day_time <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        and day_time <![CDATA[ <= ]]> #{endTime}
      </if>
    </where>
  </select>

  <select id="queryByCondition2" parameterType="cn.taqu.gonghui.live.search.OrgStatisticsSearch" resultType="cn.taqu.gonghui.system.vo.DailyStatisticsVo">
    SELECT
    day_time dayTime,
    SUM(host_num) hostNum,
    SUM(amount) amount,
    SUM(flower) flower,
    SUM(total_live_time) totalLiveTime,
    SUM(viewer) viewer,
    SUM(send) send,
    SUM(fans) fans,
    SUM(message) message,
    SUM(game_amount) gameAmount,
    SUM(system_activity_amount) systemActivityAmount
    FROM
    live_consortia_statistics_day
    <where>
      <if test="consortiaIdList != null and consortiaIdList.size() > 0">
        and consortia_id in
        <foreach item="item" index="index" collection="consortiaIdList"
                 open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="startTime != null">
        and day_time <![CDATA[ >= ]]> #{startTime}
      </if>
      <if test="endTime != null">
        and day_time <![CDATA[ <= ]]> #{endTime}
      </if>
    </where>
    GROUP BY
    day_time;
  </select>
</mapper>
