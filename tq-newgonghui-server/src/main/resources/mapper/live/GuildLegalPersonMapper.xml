<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.live.mapper.GuildLegalPersonMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.LegalPerson" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="guild_uuid" property="guildUuid" jdbcType="VARCHAR" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, guild_uuid, url, create_time, update_time, type
  </sql>

  <select id="selectByGuidUuid" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from legal_person
    where guild_uuid = #{guildUuid}
  </select>
</mapper>
