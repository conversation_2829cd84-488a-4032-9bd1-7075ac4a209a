<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.live.mapper.GuildNoticeMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.Notice" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="title" property="title" jdbcType="LONGVARCHAR" />
    <result column="content" property="content" jdbcType="LONGVARCHAR" />
    <result column="operator" property="operator" jdbcType="VARCHAR" />
    <result column="notice_type" property="noticeType" jdbcType="TINYINT" />
    <result column="guild_type" property="guildType" jdbcType="TINYINT" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
    <result column="file_key" property="fileKey" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, title,content,operator, notice_type, guild_type, create_time, update_time, file_key
  </sql>


  <select id="selectAll" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from notice
  </select>
</mapper>
