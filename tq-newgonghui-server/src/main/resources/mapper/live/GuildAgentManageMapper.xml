<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.live.mapper.GuildAgentManageMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.AgentManage" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="agent_name" property="agentName" jdbcType="VARCHAR" />
    <result column="agent_id_card" property="agentIdCard" jdbcType="VARCHAR" />
    <result column="invitation_code" property="invitationCode" jdbcType="VARCHAR" />
    <result column="phone" property="phone" jdbcType="VARCHAR" />
    <result column="agent_uuid" property="agentUuid" jdbcType="VARCHAR" />
    <result column="account_uuid" property="accountUuid" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
    <result column="update_time" property="updateTime" jdbcType="INTEGER" />
    <result column="valid" property="valid" jdbcType="TINYINT" />
    <result column="agent_name_cipher" property="agentNameCipher" jdbcType="VARCHAR" />
    <result column="agent_id_card_cipher" property="agentIdCardCipher" jdbcType="VARCHAR" />
    <result column="phone_cipher" property="phoneCipher" jdbcType="VARCHAR" />
    <result column="phone_sm3" property="phoneSm3" jdbcType="VARCHAR" />
    <result column="agent_id_card_sm3" property="agentIdCardSm3" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, agent_name, agent_id_card, invitation_code, phone, agent_uuid, account_uuid,
    create_time, update_time, valid, agent_name_cipher, agent_id_card_cipher, phone_cipher,
    phone_sm3, agent_id_card_sm3
  </sql>

<!-- 自定义查询 -->
  <select id="getAgentList" parameterType="java.lang.String" resultMap="BaseResultMap">
    SELECT
      t1.*
    FROM
      agent_manage t1
        LEFT JOIN guild_info t2 ON t1.account_uuid = t2.account_uuid
    WHERE
      t2.uuid = #{orgUuid}
  </select>

  <select id="selectListByAgentUuids" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List"></include>
    FROM
      agent_manage
    WHERE
    agent_uuid in
    <foreach collection="agentUuidList" index="index" item="val" separator="," open="(" close=")">
      #{val}
    </foreach>
  </select>
</mapper>
