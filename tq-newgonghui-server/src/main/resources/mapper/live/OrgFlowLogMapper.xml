<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.common.mapper.OrgFlowLogMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.common.entity.OrgFlowLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="scene" jdbcType="INTEGER" property="scene" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="user_time_info" jdbcType="VARCHAR" property="userTimeInfo" />
    <result column="result" jdbcType="INTEGER" property="result" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, org_id, scene, remark, user_time_info, result, create_time, create_user, modify_time
  </sql>

  <insert id="insert">
    insert into org_flow_log(org_id, scene, remark, user_time_info, result, create_user)
    values(#{orgId},#{scene},#{remark},#{userTimeInfo},#{result},#{createUser})
  </insert>

  <insert id="insertBatch">
    INSERT INTO org_flow_log
    (org_id, scene, remark, user_time_info, result, create_user)
    VALUES
    <foreach collection="logList" item="item" separator =",">
      (#{item.orgId}, #{item.scene},#{item.remark},#{item.userTimeInfo},#{item.result},#{item.createUser})
    </foreach >
  </insert>

  <select id="selectByOrgId" resultType="cn.taqu.gonghui.common.entity.OrgFlowLog">
    SELECT <include refid="Base_Column_List"></include> FROM org_flow_log
    WHERE org_id = #{orgId}
  </select>

</mapper>