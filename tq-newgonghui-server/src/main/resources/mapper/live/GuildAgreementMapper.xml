<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.taqu.gonghui.live.mapper.GuildAgreementMapper" >
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.live.entity.GuildAgreement" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="guild_id" property="guildId" jdbcType="INTEGER" />
    <result column="agreement_id" property="agreementId" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, guild_id, agreement_id, create_time
  </sql>



  <select id="selectAll" resultMap="BaseResultMap"  >
    select
    <include refid="Base_Column_List" />
    from guild_agreement
  </select>
</mapper>
