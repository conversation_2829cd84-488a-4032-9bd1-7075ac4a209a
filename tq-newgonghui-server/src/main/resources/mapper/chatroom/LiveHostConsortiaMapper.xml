<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.chatroom.mapper.LiveHostConsortiaMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.chatroom.entity.LiveHostConsortia">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="invite_code" jdbcType="VARCHAR" property="inviteCode" />
    <result column="president_uuid" jdbcType="VARCHAR" property="presidentUuid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, title, status, create_time, update_time,invite_code,president_uuid
  </sql>
  <select id="selectAllChatRoomConsortia"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from live_host_consortia
    where status = 1 and type = 3
  </select>

</mapper>
