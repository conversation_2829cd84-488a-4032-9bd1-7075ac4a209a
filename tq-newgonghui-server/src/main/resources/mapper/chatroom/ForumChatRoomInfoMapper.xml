<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.chatroom.mapper.ForumChatRoomInfoMapper">
  <resultMap id="BaseResultMap" type="cn.taqu.gonghui.chatroom.entity.ForumChatRoomInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="consortia_id" jdbcType="BIGINT" property="consortiaId" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="chat_uuid" jdbcType="VARCHAR" property="chatUuid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, consortia_id, create_time, update_time,chat_uuid
  </sql>
  <select id="selectChatRoomByConsortiaId" parameterType="long"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from forum_chat_room_info
    where consortia_id = #{consortiaId,jdbcType=BIGINT}
  </select>

</mapper>
