<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.taqu.gonghui.chatroom.mapper.RoomMapper">

    <resultMap id="BaseResultMap" type="cn.taqu.gonghui.chatroom.entity.Room">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="room_no" jdbcType="VARCHAR" property="roomNo" />
        <result column="audit_order_no" jdbcType="VARCHAR" property="auditOrderNo" />
        <result column="org_uuid" jdbcType="VARCHAR" property="orgUuid" />
        <result column="team_id" jdbcType="BIGINT" property="teamId" />
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
        <result column="time_scope_start" jdbcType="VARCHAR" property="timeScope" />
        <result column="type" jdbcType="INTEGER" property="type" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_time" jdbcType="INTEGER" property="createTime" />
        <result column="modify_time" jdbcType="INTEGER" property="modifyTime" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="mobile_cipher" jdbcType="VARCHAR" property="mobileCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
        <result column="owner_name_cipher" jdbcType="VARCHAR" property="ownerNameCipher" typeHandler="cn.taqu.gonghui.system.common.EncryptTypeHandler" />
    </resultMap>


    <select id="listByStatusOrderByIdDesc" resultMap="BaseResultMap">
<!--            resultType="cn.taqu.gonghui.chatroom.entity.Room">-->
        SELECT
        id,
        room_no,
        audit_order_no,
        org_uuid,
        team_id,
        account_uuid,
        mobile,
        owner_name,
        time_scope_start,
        type,
        status,
        create_time,
        modify_time,
        create_by,
        update_by,
        remark,
        mobile_cipher,
        owner_name_cipher
        FROM room
        WHERE status IN
        <foreach collection="statusList" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>

        AND id > #{lastId}
        ORDER BY id
        LIMIT 0, #{limit}
    </select>
    <update id="updateCiperById" >
        update room
            set mobile_cipher = #{room.mobileCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler},
                owner_name_cipher = #{room.ownerNameCipher,jdbcType=VARCHAR,typeHandler=cn.taqu.gonghui.system.common.EncryptTypeHandler}
        where id = #{room.id}
    </update>
</mapper>
