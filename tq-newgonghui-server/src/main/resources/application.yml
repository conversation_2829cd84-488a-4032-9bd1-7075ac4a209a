#系统信息配置
service:
  index: j47
  code: newgonghui
  name: 新公会
  configVersion: V1

project:
  name: mp-gonghui
  biz:
    name: mp

#定时任务线程池数量
spring:
  application:
    name: mp-gonghui
  task:
    scheduling:
      pool:
        size: 100
  profiles:
    active: dev #启用的环境
  datasource:
    initial-size: 10
    min-idle: 10
    max-active: 80
    max-wait: 3000
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 300000
    validation-query: SELECT 1
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
    pool-prepared-statements: true
    max-pool-prepared-statement-per-connection-size: 20
    filters: stat,slf4j
  jpa:
    open-in-view: false
#  cloud:
#    sentinel:
#      transport:
#        dashboard: mp-sentinel-dashboard.test.hbmonitor.com
#      datasource:
#        flow:
#          nacos:
#            server-addr: mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848
#            namespace: sentinel
#            group-id: sentinel_rules
#            rule-type: flow
#            data-id: sentinel.flow_rules.mp.mp-gonghui
#            data-type: json
#        degrade:
#          nacos:
#            server-addr: mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848
#            namespace: sentinel
#            group-id: sentinel_rules


server:
  port: 8087
  servlet:
    context-path: /tq-newgonghui
  tomcat:
    threads:
      max: 2000
      min-spare: 50
    mbeanregistry:
      enabled: true

mybatis:
  map-underscore-to-camel-case: true
  mapper-locations: classpath*:mapper/*/*Mapper.xml

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

#端点信息监控
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  metrics:
    tags:
      application: j47
  info:
    git:
      mode: full
  health:
    defaults:
      enabled: false
    circuitbreakers:
      enabled: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认120分钟）
  expireTime: 1440

shiro:
  # shiro 过滤器配置，配置过滤器要拦截哪些路径，严格按照顺序，配置在前面的会先执行
  filterPath:
    - anon: /commons/**, /static/**, /api/**, /metrics, /test/**, /actuator/**
    - logout: /logout
    - cas: /cas
    - authc: /**


jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: caffeine
      limit: 100000
      keyConvertor: fastjson
      expireAfterWriteInMillis: 30000

#powerjob:
#  worker:
#    app-name: mp-gonghui
#    server-address: powerjob.soa.internal.taqu.cn
