package cn.taqu.gonghui.system.entity;

import cn.taqu.gonghui.common.constant.LiveSettlementeTypeEnum;
import cn.taqu.gonghui.common.domain.BaseEntity;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import javax.persistence.Column;
import javax.persistence.Transient;

@Data
@TableName(value = "organization", autoResultMap = true)
public class Organization extends BaseEntity {
    /**机构id*/
    @TableId(value = "org_id", type = IdType.AUTO)
    private Long orgId;

    /**机构名称*/
    private String orgName;

    private String orgUuid;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String chargePerson;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String chargePersonIdCard;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String chargePersonPhone;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String chargePersonEmail;

    private Long chargePersonBirthday;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String receivingAddress;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String legalPerson;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String legalPersonIdCard;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String publicReceivingBankAccount;

    private String accountName;

    private String accountBankName;

    private String province;

    private Integer provinceId;

    private String city;

    private Integer cityId;

    private String subBranchName;

    private Integer orgStatus;

    private Integer applyStatus;

    private Integer modifyStatus;

    private Integer remitModifyStatus;
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String businessPerson;

    private Long createTime;

    private Long updateTime;

    private String chargePersonVx;

    private Integer formStatus;

    private String accountUuid;

    private String auditMsg;

    private String auditPerson;

    /**
     * 机构登录人手机号-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String chargePersonPhoneCipher;

    /**
     * 机构登录人手机号-摘要
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = Sm3EncryptTypeHandler.class)
    private String chargePersonPhoneDigest;

    /**
     * 负责人邮箱
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String chargePersonEmailCipher;

    /**
     * 负责人姓名
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String chargePersonCipher;

    private String chargePersonVxCipher;

    /**
     * 负责人身份证号
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String chargePersonIdCardCipher;

    /**
     * 收件地址
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String receivingAddressCipher;

    /**
     * 法人姓名
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String legalPersonCipher;

    /**
     * 法人身份证号
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String legalPersonIdCardCipher;

    /**
     * 对公收款账号
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String publicReceivingBankAccountCipher;

    private String accountNameCipher;

    private String accountBankNameCipher;

    private String subBranchNameCipher;

    /**
     * 业务对接人姓名-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String businessPersonCipher;

    /**
     * 业务对接人姓名-摘要
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = Sm3EncryptTypeHandler.class)
    private String businessPersonDigest;

    private Long joinTime;

    private Integer joinSource;

    private Integer livePermissions;

    private Integer quliaoPermissions;


    @TableField(value = "chat_room__permissions")
    private Integer chatRoomPermissions;

    private String content;

    /**
     * 联系人手机号
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String contactPhone;

    /**
     * 联系人手机号-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String contactPhoneCipher;


    @Transient
    private String businessName;

    @Transient
    private String presidentMobile;


    @Transient
    @Column(name = "business_license_url")
    private String businessLicenseUrl; //营业执照url

    @Transient
    @Column(name = "opening_permit_url")
    private String openingPermitUrl; //开户许可证url

    @Transient
    @Column(name = "legal_person_url")
    private String legalPersonUrl; //法人信息身份证, url1,url2,url3   正面,反面,手持

    @Transient
    @Column(name = "charge_person_url")
    private String chargePersonUrl; //负责人信息身份证, url1,url2,url3   正面,反面,手持

//    @Transient
//    @Column(name = "org_cooperation_flow_url")
    private String orgCooperationFlowUrl; //流水截图

    @Transient
    @Column(name = "host_screenshot_url")
    private String hostScreenshotUrl; //主播截图

    private String socialUnifiedCreditCode;   //社会统一信用代码

    private String enterpriseName;  //企业全名

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String legalPersonPhone;   //法人手机号

    /**
     * 法人手机号
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String legalPersonPhoneCipher;

    private String applyLog;    //审核记录

    private String premises;   //经营场所

    private Integer settlementeType;   //结算类型

    private Integer liveSettlementeType; // 直播结算方式 （1-月结，2周结，3-新月结）

    private Integer creditGrade; //信用得分

//    @Transient
    private Boolean settlementeModifyFlag;

    public void organization(){
        this.liveSettlementeType = LiveSettlementeTypeEnum.MONTH.getValue();
    }


    public String getChargePerson() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.chargePersonCipher;
        }
        if(StringUtils.isBlank(this.chargePerson)&&StringUtils.isNotBlank(this.chargePersonCipher)){
            return this.chargePersonCipher;
        }
        return this.chargePerson;
    }

    public String getChargePersonIdCard() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.chargePersonIdCardCipher;
        }
        if(StringUtils.isBlank(this.chargePersonIdCard)&&StringUtils.isNotBlank(this.chargePersonIdCardCipher)){
            return this.chargePersonIdCardCipher;
        }
        return this.chargePersonIdCard;
    }

    public String getChargePersonPhone() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.chargePersonPhoneCipher;
        }
        if(StringUtils.isBlank(this.chargePersonPhone)&&StringUtils.isNotBlank(this.chargePersonPhoneCipher)){
            return this.chargePersonPhoneCipher;
        }
        return this.chargePersonPhone;
    }

    public String getChargePersonPhoneDigest() {
        return getChargePersonPhone();
    }

    public String getBusinessPersonDigest() {
        return getBusinessPerson();
    }

    public String getChargePersonEmail() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.chargePersonEmailCipher;
        }
        if(StringUtils.isBlank(this.chargePersonEmail)&&StringUtils.isNotBlank(this.chargePersonEmailCipher)){
            return this.chargePersonEmailCipher;
        }
        return this.chargePersonEmail;
    }

    public String getReceivingAddress() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.receivingAddressCipher;
        }
        if(StringUtils.isBlank(this.receivingAddress)&&StringUtils.isNotBlank(this.receivingAddressCipher)){
            return this.receivingAddressCipher;
        }
        return receivingAddress;
    }

    public String getLegalPerson() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.legalPersonCipher;
        }
        if(StringUtils.isBlank(this.legalPerson)&&StringUtils.isNotBlank(this.legalPersonCipher)){
            return this.legalPersonCipher;
        }
        return this.legalPerson;
    }

    public String getLegalPersonIdCard() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.legalPersonIdCardCipher;
        }
        if(StringUtils.isBlank(this.legalPersonIdCard)&&StringUtils.isNotBlank(this.legalPersonIdCardCipher)){
            return this.legalPersonIdCardCipher;
        }
        return legalPersonIdCard;
    }

    public String getPublicReceivingBankAccount() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.publicReceivingBankAccountCipher;
        }
        if(StringUtils.isBlank(this.publicReceivingBankAccount)&&StringUtils.isNotBlank(this.publicReceivingBankAccountCipher)){
            return this.publicReceivingBankAccountCipher;
        }
        return publicReceivingBankAccount;
    }

    public String getBusinessPerson() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.businessPersonCipher;
        }
        if(StringUtils.isBlank(this.businessPerson)&&StringUtils.isNotBlank(this.businessPersonCipher)){
            return this.businessPersonCipher;
        }
        return businessPerson;
    }
    public String getLegalPersonPhone(){
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.legalPersonPhoneCipher;
        }
        if(StringUtils.isBlank(this.legalPersonPhone)&&StringUtils.isNotBlank(this.legalPersonPhoneCipher)){
            return this.legalPersonPhoneCipher;
        }
        return this.legalPersonPhone;
    }

    public String getContactPhone(){
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.contactPhoneCipher;
        }
        if(StringUtils.isBlank(this.contactPhone)&&StringUtils.isNotBlank(this.contactPhoneCipher)){
            return this.contactPhoneCipher;
        }
        return this.contactPhone;
    }

    public void setChargePerson(String chargePerson) {
        this.chargePerson = chargePerson;
        this.chargePersonCipher = chargePerson;
    }

    public void setChargePersonIdCard(String chargePersonIdCard) {
        this.chargePersonIdCard = chargePersonIdCard;
        this.chargePersonIdCardCipher = chargePersonIdCard;
    }

    public void setChargePersonPhone(String chargePersonPhone) {
        this.chargePersonPhone = chargePersonPhone;
        this.chargePersonPhoneCipher = chargePersonPhone;
        this.chargePersonPhoneDigest = chargePersonPhone;
    }

    public void setChargePersonEmail(String chargePersonEmail) {
        this.chargePersonEmail = chargePersonEmail;
        this.chargePersonEmailCipher = chargePersonEmail;
    }

    public void setReceivingAddress(String receivingAddress) {
        this.receivingAddress = receivingAddress;
        this.receivingAddressCipher = receivingAddress;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
        this.legalPersonCipher = legalPerson;
    }

    public void setLegalPersonIdCard(String legalPersonIdCard) {
        this.legalPersonIdCard = legalPersonIdCard;
        this.legalPersonIdCardCipher = legalPersonIdCard;
    }

    public void setPublicReceivingBankAccount(String publicReceivingBankAccount) {
        this.publicReceivingBankAccount = publicReceivingBankAccount;
        this.publicReceivingBankAccountCipher = publicReceivingBankAccount;
    }

    public void setBusinessPerson(String businessPerson) {
        this.businessPerson = businessPerson;
        this.businessPersonCipher = businessPerson;
        this.businessPersonDigest = businessPerson;
    }

    public void setLegalPersonPhone(String legalPersonPhone){
        this.legalPersonPhone = legalPersonPhone;
        this.legalPersonPhoneCipher = legalPersonPhone;
    }

    public void setContactPhone(String contactPhone){
        this.contactPhone = contactPhone;
        this.contactPhoneCipher = contactPhone;
    }
}
