package cn.taqu.gonghui.system.vo;

import lombok.Data;

/**
 * @Description 操作机构请求参数
 * <AUTHOR>
 * @Date 2022/5/10 4:46 下午
 **/
@Data
public class TeamOperateRequestVo {
    /**
     * 要操作或者迁移的机构id
     */
    private Long oldOrgId;
    /**
     * 要操作或者迁移的团队id
     */
    private Long oldTeamId;
    /**
     * 要迁入的机构id
     */
    private Long newOrgId;
    /**
     * 可传可不传，不传就新建一个team
     */
    private Long newTeamId;
}
