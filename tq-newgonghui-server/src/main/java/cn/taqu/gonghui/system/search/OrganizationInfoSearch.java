package cn.taqu.gonghui.system.search;

import lombok.Data;

import java.util.List;

@Data
public class OrganizationInfoSearch{
    private Long orgId;
    private String orgName; //机构名称
    private Integer orgStatus; //机构状态
    private Integer applyStatus; //审核状态
    private Long operationSpecialistId; //运营人员id
    private Integer isBindAccountUuid;  //绑定 1是0否
    private String inviteCode; //邀请码
    private String orgUuid;
    private String mobile;   //手机号
    private String applyLog;    //审核记录
    private Long startTime;
    private Long endTime;
    private String enterpriseName;  //企业全名
    private Integer livePermissions;
    private Integer chatRoomPermissions;
    private List<Integer> businessPermissions;
    private String businessPerson;

    private Integer selectByDigest;
}
