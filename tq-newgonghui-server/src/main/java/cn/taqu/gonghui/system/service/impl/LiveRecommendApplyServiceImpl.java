package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.LiveRecommendApplyLocationEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.system.entity.LiveRecommendApply;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.service.HostService;
import cn.taqu.gonghui.system.service.LiveRecommendApplyService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.vo.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class LiveRecommendApplyServiceImpl implements LiveRecommendApplyService {

    private Logger logger = LoggerFactory.getLogger(LiveRecommendApplyServiceImpl.class);

    @Autowired
    private HostService hostService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SysUserService userService;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    private SysUserMapper sysUserMapper;



    /**
     * 获取推荐位申请记录aa
     *
     * @param date
     * @param hostUuid
     * @param nickname
     * @param status
     * @param location
     * @param page
     * @param pageSize
     */
    @Override
    public PageDataVo<LiveRecommendApplyVo> getApplyList(Long teamId,String date, String hostUuid, String nickname, Integer status,
                                                         String location, Integer page, Integer pageSize,Integer type) {
        PageDataVo<LiveRecommendApplyVo> vo = new PageDataVo(0, Lists.newArrayList());
        List<LiveRecommendApplyVo> data = Lists.newArrayList();
        if (null == teamId || 0L == teamId) {
            throw new ServiceException("invalid_query","请选择团队");
        }
;
        if (Objects.nonNull(nickname)) {
            String uuid = hostService.getUuidByAccountName(nickname);
            if (Objects.nonNull(hostUuid) && !Objects.equals(hostUuid, uuid)) {
                return vo;
            } else {
                hostUuid = uuid;
            }
        }
        if (Objects.equals(status, 0)) {
            status = null;
        }
        String locationValue;
        if (StringUtils.isNotBlank(location)) {
            locationValue = location;
        } else {
            locationValue = null;
        }

        PageData pageData = null;
        RoleVo currentRole = userService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        String limit = (page - 1) + "," + pageSize;
        String orgUuid = String.valueOf(teamId);
        List<String> uuidList = new ArrayList<>();
        // 管理员
        if (currentRole.getRoleKey().equals(UserTypeEnum.MANAGER.getCode())) {
            if (StringUtils.isNotBlank(hostUuid)) {
                uuidList.add(hostUuid);
            }
            logger.info("管理员角色获取推荐位申请记录，主播uuidList:{},团队id:{}", JsonUtils.objectToString(uuidList),orgUuid);
            pageData = hostService.getRecommendApplyList(date,uuidList,nickname,locationValue,status,orgUuid,limit,type);
            // 负责人或者经纪人
        } else if (currentRole.getRoleKey().equals(UserTypeEnum.LEADER.getCode()) || currentRole.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())) {
            if (110166 != teamId) {
                uuidList = getHostUuidsByAccountUuid();
                if (CollectionUtils.isNotEmpty(uuidList)) {
                    if (StringUtils.isNotBlank(hostUuid)) {
                        if (uuidList.contains(hostUuid)) {
                            uuidList.clear();
                            uuidList.add(hostUuid);
                        } else {
                            uuidList.clear();
                            uuidList.add("no_host_uuid");
                        }
                    }
                } else {
                    uuidList = new ArrayList<>();
                    uuidList.add("no_host_uuid");
                }
            }
            logger.info("经纪人或者负责人角色获取推荐位申请记录，主播uuidList:{},团队id:{}", JsonUtils.objectToString(uuidList),orgUuid);
            pageData = hostService.getRecommendApplyList(date,uuidList,nickname,locationValue,status,orgUuid,limit,type);
        }  else {
            throw new ServiceException(CodeStatus.ROLE_ERROR.value(), CodeStatus.ROLE_ERROR.getReasonPhrase());
        }
        pageData.getList().forEach(item -> data.add(model2Vo(item)));
        vo.setData(data);
        vo.setTotal(pageData.getTotal() == null ? 0 : pageData.getTotal());
        return vo;
    }

    /**
     * 获取当前用户的机构id
     * @return
     */
    private String getOrgUuidByAccountUuid(){
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        Long orgId = user.getOrgId();
        if (null == orgId) {
            throw new ServiceException(CodeStatus.ORG_NOT_FOUNT_ERROR.value(),"当前用户所属机构不存在");
        }
        return organizationMapper.selectByPrimaryKey(orgId).getOrgUuid();
    }


    /**
     * 获取当前用户下的有效主播uuid
     */
    private List<String> getHostUuidsByAccountUuid(){
        String accountUuid = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getAccountUuid();
        List<String> hostUuids = null;
        RoleVo role = userService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        // 角色是负责人
        if (role.getRoleKey().equals(UserTypeEnum.LEADER.getCode())) {
            Long teamId = sysUserMapper.getTeamIdByAccountUuid(accountUuid,TeamTypeEnum.LIVE_TEAM.getValue());
            if (null == teamId) {
                logger.warn("当前负责人无所属团队");
                return hostUuids;
            }
            hostUuids = teamHostMapper.getHostUuidsByLeader(teamId);
        }
        // 角色是经纪人
        else if (role.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())){
            Long employeeId = sysUserMapper.getEmployeeIdByAccountUuid(accountUuid,TeamTypeEnum.LIVE_TEAM.getValue());
            if (null == employeeId) {
                logger.warn("当前经纪人是个无效成员");
                return  hostUuids;
            }
            hostUuids = teamHostMapper.getHostUuidsByAgenter(employeeId);
        }
        return hostUuids;
    }

    private LiveRecommendApplyVo model2Vo(LiveRecommendApply entity) {
        LiveRecommendApplyVo vo = new LiveRecommendApplyVo();
        vo.setId(entity.getId());
        vo.setDate(entity.getDate());
        vo.setTime(entity.getStart_time() + "~" + entity.getEnd_time());
        vo.setNickname(hostService.getNickNameByUuid(entity.getHost_uuid()));
        vo.setHostUuid(entity.getHost_uuid());
        String locationValue = String.valueOf(entity.getLocation());
        vo.setLocationName(LiveRecommendApplyLocationEnum.getName(locationValue));
        vo.setStatus(entity.getStatus());
        vo.setCreateTime(entity.getCreate_time());
        vo.setUseRecommendCard(entity.getUse_recommend_card());
        return vo;
    }

    /**
     * 根据id撤销推荐位申请
     */
    @Override
    public void cancelApplyById(Long id) {
        hostService.cancelApplyById(id);
    }

    /**
     * 获取推荐位列表
     */
    @Override
    public RecommendApplyInfoVo getRecommendList(String hostUuid) {
        TeamHost host = Optional.ofNullable(teamHostMapper.getOneByHostUuid(hostUuid,TeamTypeEnum.LIVE_TEAM.getValue()))
                .orElseThrow(() -> new ServiceException(CodeStatus.DATA_NOT_FOUND_ERROR.value(), CodeStatus.DATA_NOT_FOUND_ERROR.getReasonPhrase()));
        RecommendApplyInfoVo vo = hostService.getRecommendApplyInfo(hostUuid, String.valueOf(host.getTeamId()));
        return vo;
    }

    /**
     * 创建推荐位申请
     *
     * @param hostUuid
     * @param date
     * @param location
     * @param time
     */
    @Override
    public void saveRecommendApply(String hostUuid, String date, String location, String time) {
        TeamHost host = Optional.ofNullable(teamHostMapper.getOneByHostUuid(hostUuid,TeamTypeEnum.LIVE_TEAM.getValue()))
                .orElseThrow(() -> new ServiceException(CodeStatus.DATA_NOT_FOUND_ERROR.value(), CodeStatus.DATA_NOT_FOUND_ERROR.getReasonPhrase()));

        String[] timeArr = time.split(",");
        String start = timeArr[0].split("~")[0];
        String end = timeArr[0].split("~")[1];
        logger.info("当前公会id={},申请普通时段,请求时段开始-请求时段结束={}-{}", host.getTeamId(), start, end);
        hostService.addRecommendApplyG(String.valueOf(host.getTeamId()), date, location, hostUuid, time, 0);


    }
}
