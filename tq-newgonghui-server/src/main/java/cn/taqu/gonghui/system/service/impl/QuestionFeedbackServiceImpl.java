package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.common.constant.QuestionFeedbackStatusEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.dto.QuestionFeedbackDto;
import cn.taqu.gonghui.system.entity.QuestionFeedback;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.mapper.QuestionFeedbackMapper;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.search.QuestionFeedbackSearch;
import cn.taqu.gonghui.system.service.QuestionFeedbackService;
import cn.taqu.gonghui.system.vo.QuestionFeedbackVo;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户意见反馈
 */
@Service
public class QuestionFeedbackServiceImpl implements QuestionFeedbackService {

    private static Logger logger = LoggerFactory.getLogger(QuestionFeedbackServiceImpl.class);

    @Autowired
    private QuestionFeedbackMapper feedbackMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private TokenService tokenService;

    /**
     * 管理端反馈列表
     * @param search
     * @return
     */
    @Override
    public List<QuestionFeedbackVo> manageList(QuestionFeedbackSearch search) {
        PageHelper.startPage(search.getPage(),search.getPageSize());
        List<QuestionFeedbackVo> vos = feedbackMapper.manageList(search);
        for (QuestionFeedbackVo vo : vos) {
            if (StringUtils.isNotBlank(vo.getImageUrl())) {
                String[] split = vo.getImageUrl().split(",");
                vo.setImageUrlArr(split);
            }
            if (0 == vo.getTeamId()) {
                vo.setTeamId(null);
            }
        }
        return vos;
    }

    /**
     * 用户端反馈列表
     * @param search
     * @return
     */
    @Override
    public List<QuestionFeedbackVo> userList(QuestionFeedbackSearch search) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        if (null == user || StringUtils.isBlank(user.getAccountUuid())) {
            throw new ServiceException("invalid_user","当前登陆用户是无效用户");
        }
        search.setAccount_uuid(user.getAccountUuid());
        PageHelper.startPage(search.getPage(),search.getPageSize());
        List<QuestionFeedbackVo> vos = feedbackMapper.userList(search);
        for (QuestionFeedbackVo vo : vos) {
            if (StringUtils.isNotBlank(vo.getImageUrl())) {
                String[] split = vo.getImageUrl().split(",");
                vo.setImageUrlArr(split);
            }
            if (0 == vo.getTeamId()) {
                vo.setTeamId(null);
            }
        }
        return vos;
    }

    /**
     * 用户端提交反馈意见
     * @param record
     */
    @Override
    public void add(QuestionFeedback record,Integer businessType) {
        // 参数校验
        checkParams(record,businessType);
        // 封装数据
        packageFormData(record,businessType);
        feedbackMapper.insertSelective(record);
    }

    /**
     * 管理端变更 跟进状态
     * @param id
     * @param remark
     */
    @Override
    public void changeStatus(Long id,Integer status,String remark,String followUpPerson) {
        // 校验参数
        if (null == id) {
            throw new ServiceException("invalid_id","ID不能为空");
        }
        if (null == status) {
            throw new ServiceException("invalid_status","请选择处理结果");
        }
        if (StringUtils.isBlank(remark)) {
            throw new ServiceException("invalid_remark","请填写备注信息");
        }
        if (StringUtils.isBlank(followUpPerson)) {
            throw new ServiceException("invalid_operator","请输入操作人");
        }
        QuestionFeedbackDto dto = new QuestionFeedbackDto();
        dto.setId(id);
        dto.setStatus(status);
        dto.setRemark(remark);
        dto.setFollowUpPerson(followUpPerson);
        dto.setUpdateTime(System.currentTimeMillis()/1000);
        feedbackMapper.changeStatus(dto);
    }


    /**
     * 参数校验
     * @param record
     */
    private void checkParams(QuestionFeedback record,Integer businessType){
        if (StringUtils.isBlank(record.getPageUrl())) {
            throw new ServiceException("invalid_page_url","页面地址不能为空");
        }
        if (StringUtils.isBlank(record.getPageName())) {
            throw new ServiceException("invalid_page_name","页面名称不能为空");
        }
        if (StringUtils.isBlank(record.getTitle())) {
            throw new ServiceException("invalid_title","反馈标题不能为空");
        }
        if (StringUtils.isBlank(record.getContent())) {
            throw new ServiceException("invalid_content","反馈内容不能为空");
        }
        if (record.getTitle().length() > 50) {
            throw new ServiceException("title_length_out_range","标题最多输入50个字");
        }
        if (record.getContent().length() > 5000) {
            throw new ServiceException("content_length_out_range","内容最多输入5000个字");
        }
        if(businessType == null || businessType > 3){
            throw new ServiceException("business_type_error","当前没有此业务类型");
        }
    }

    /**
     * 封装表单数据
     * @param record
     */
    private void packageFormData(QuestionFeedback record,Integer businessType){
       // 获取当前登陆人信息
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        if (null == user || StringUtils.isBlank(user.getAccountUuid()) || null == user.getOrgId()) {
            throw new ServiceException("invalid_user","当前登陆用户无效");
        }
        // 设置机构id
        record.setOrgId(user.getOrgId());
        // 判断当前登陆人角色，如果是管理员或者空角色则不存在所属团队，否则获取团队id
        if (UserTypeEnum.DEFAULT.getType().equals(user.getUserType()) || UserTypeEnum.MANAGER.getType().equals(user.getUserType())) {
            record.setTeamId(0L);
        } else {
            Long teamId = sysUserMapper.getTeamIdByAccountUuid(user.getAccountUuid(), businessType);
            if (null == teamId) {
                throw new ServiceException("invalid_team","当前用户所属团队无效");
            }
            // 设置团队id
            record.setTeamId(teamId);
        }
        // 初始状态为待处理
        record.setStatus(QuestionFeedbackStatusEnum.PENDING.getValue());
        // 设置用户uuid
        record.setFeedbackUuid(user.getAccountUuid());
        // 设置其它信息
        record.setFeedbackTime(System.currentTimeMillis()/1000);
        record.setCreateTime(System.currentTimeMillis()/1000);
        record.setUpdateTime(System.currentTimeMillis()/1000);

    }
}
