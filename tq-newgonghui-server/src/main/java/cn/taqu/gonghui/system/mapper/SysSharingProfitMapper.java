package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.SysSharingProfit;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.search.SysSharingProfitSearch;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysSharingProfitMapper extends BaseMapper<SysSharingProfit> {

    SysSharingProfit getOneById(Long id);

    List<SysSharingProfit> listByCondition(SysSharingProfitSearch search);

    void updateStatus(@Param("id") Long id, @Param("status") Integer status);

    Integer percentNumberHasExist(String percentNumber);

    TeamHost rateHasUsed(String rate);
}
