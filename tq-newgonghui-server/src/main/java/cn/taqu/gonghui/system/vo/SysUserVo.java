package cn.taqu.gonghui.system.vo;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/4/27
 */
@Data
public class SysUserVo {

    private Long userId;
    private Long orgId;

    private String  userName;
    private String  accountUuid;
    private String mobile;
    private String  orgName;
    private Integer  userType;
    private String  createTime;
    private String  updateTime;
    private Integer status;
    private Long liveTeamId;
    private Long chatTeamId;
    private String liveTeamName;
    private String liveRoleName;
    private String chatTeamName;
    private String chatRoleName;
    private String mobileCipher;
    private String userNameCipher;

    public String getMobile() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.mobileCipher;
        }
        if(StringUtils.isBlank(this.mobile) && StringUtils.isNotBlank(this.mobileCipher)){
            return this.mobileCipher;
        }
        return this.mobile;
    }

    public String getUserName() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.userNameCipher;
        }
        if(StringUtils.isBlank(this.userName) && StringUtils.isNotBlank(this.userNameCipher)){
            return this.userNameCipher;
        }
        return userName;
    }
}
