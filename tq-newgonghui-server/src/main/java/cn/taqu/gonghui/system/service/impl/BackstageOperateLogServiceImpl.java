package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.entity.BackstageOperateLog;
import cn.taqu.gonghui.system.mapper.BackstageOperateLogMapper;
import cn.taqu.gonghui.system.service.BackstageOperateLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-11 13:55
 */
@Service
@Slf4j
public class BackstageOperateLogServiceImpl extends ServiceImpl<BackstageOperateLogMapper, BackstageOperateLog> implements BackstageOperateLogService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addBackstageOperateLog(String batchId, Integer operateType, String operator, String info) {
        if(operateType == null){
            operateType = 0;
        }
        if(StringUtils.isBlank(operator)){
            operator = SoaBaseParams.fromThread().getToken();
        }
        Long now = DateUtil.currentTimeSeconds();
        BackstageOperateLog backstageOperateLog = new BackstageOperateLog().setBatchId(batchId).setOperateType(operateType).setOperator(operator).setInfo(info)
                .setCreateTime(now).setUpdateTime(now);
        this.save(backstageOperateLog);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public void addBackstageOperateLogTxNew(String batchId, Integer operateType, String operator, String info) {
        if(operateType == null){
            operateType = 0;
        }
        if(StringUtils.isBlank(operator)){
            operator = SoaBaseParams.fromThread().getToken();
        }
        Long now = DateUtil.currentTimeSeconds();
        BackstageOperateLog backstageOperateLog = new BackstageOperateLog().setBatchId(batchId).setOperateType(operateType).setOperator(operator).setInfo(info)
                .setCreateTime(now).setUpdateTime(now);
        this.save(backstageOperateLog);
    }
}
