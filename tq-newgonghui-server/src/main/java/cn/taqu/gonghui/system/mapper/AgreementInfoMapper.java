package cn.taqu.gonghui.system.mapper;


import cn.taqu.gonghui.system.entity.Agreement;
import cn.taqu.gonghui.system.entity.AgreementInfo;
import cn.taqu.gonghui.system.search.AgreementSignLogSearch;
import cn.taqu.gonghui.system.vo.AgreementSignLogVo;
import cn.taqu.gonghui.system.vo.AgreementVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AgreementInfoMapper extends BaseMapper<AgreementInfo> {
    int deleteByPrimaryKey(Long agrId);

    int updateByPrimaryKeySelective(AgreementInfo record);

    /**
     * 根据协议id&是否有效获取协议详情
     * @param agrId
     * @return
     */
    AgreementInfo getById(@Param("agrId") Long agrId, @Param("valid") Integer valid);

    /**
     * 查询有效协议列表（无分页）
     * @return
     */
    List<AgreementInfo> findAllOrderByOrderLevelAsc(@Param("valid") Integer valid,@Param("title") String title,@Param("types") List<Integer> types);

    /**
     *根据协议顺序获取协议
     * @param orderLevel
     * @return
     */
    AgreementInfo getByOrderLevel(Integer orderLevel);


    /**
     * 获取机构签署协议记录列表（分页）
     * @param search
     * @return
     */
    List<AgreementSignLogVo> findAgreementSignLogPageList(AgreementSignLogSearch search);

    /**
     * 获取有效协议下拉tree
     * @return
     */
    List<AgreementInfo> findListForSearch();

    /**
     * 当前协议是否已经签署
     * @return
     */
    Integer agreenmentIsSigned(Long agrId);


    /**
     * 根据机构id和协议顺序获取协议列表
     * @param orgId
     * @param orderLevel
     * @return
     */
    List<AgreementVo> findAgreementByOrgIdAndOrderLevel(@Param("orgId") Long orgId, @Param("orderLevel") Integer orderLevel);



    // -------------------- 以下为用户端接口 ----------------------------

    /**
     * 根据机构id获取该接口下所有签署的协议列表
     * @param orgId
     * @return
     */
    List<Agreement> findAgreementByOrgIdGroupByAgrId(Long orgId);


    /**
     * 根据机构id和协议id获取签署的协议记录
     * @param orgId
     * @param agrId
     * @return
     */
    Agreement findByOrgIdAndAgrId(@Param("orgId") Long orgId, @Param("agrId") Long agrId);

    AgreementInfo getByAgrId(Long agrId);


    int insertBatch(List<AgreementInfo> list);
}
