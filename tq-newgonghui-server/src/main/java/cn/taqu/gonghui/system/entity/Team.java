package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class Team {
    /**
     * 团队id
     */
    @TableId(value = "team_id",type = IdType.AUTO)
    private Long teamId;

    /**
     * 所属机构id
     */
    private Long orgId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 业务类型（1-直播，2-趣聊，3-聊天室...）
     */
    private Integer type;

    /**
     * 团队标识符
     */
    private String signKey;

    /**
     * 是否默认团队（1-是，0-否）
     */
    private Integer isDefault;

    /**
     * 是否启用（1-启用，0-禁用）
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Long createTime;

    private Long updateTime;

    private String inviteCode;
}
