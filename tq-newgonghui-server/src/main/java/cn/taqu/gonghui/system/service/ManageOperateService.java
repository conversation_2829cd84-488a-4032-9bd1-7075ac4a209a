package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.dto.OperationPersonDto;
import cn.taqu.gonghui.system.entity.MyData;
import cn.taqu.gonghui.system.entity.OperationPerson;
import cn.taqu.gonghui.system.entity.Performance;
import cn.taqu.gonghui.system.entity.TargetInfo;

import java.util.List;

public interface ManageOperateService {

    int saveOperationPerson(OperationPerson operationPerson);

    List<OperationPerson> getOperationPersonDtoList(OperationPersonDto operationPersonDto);

    OperationPerson getOperationPersonByName(String name);

    int updateOperationPerson(OperationPerson operationPerson);

    int deleteOperationPerson(OperationPerson operationPerson);

    int saveTargetInfo(TargetInfo targetInfo);

    int updateTargetInfo(TargetInfo targetInfo);

    TargetInfo getTargetInfo();

    List<MyData> myDataList(String name,Integer month);

    List<MyData> beforeYearDateList(Integer beforeYearDatem,Integer beforeYearDateEnd,String name);

    List<Performance> myPerformanceList(String name, Integer month);
//    List<MyData> tabulateDataList(int month);
//    List<Performance> performanceDataList(int month);









}
