package cn.taqu.gonghui.system.common;

import cn.taqu.core.etcd.annotation.EtcdValue;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/8 10 49
 * 加密开关控制
 */
@Component
public class EncryptSwitchConfig {

    /**
     * 双写开关 0关闭双写（即只写加密处理）  1开启双写（明文和密文都写入）
     */
    @EtcdValue(key = "encrypt.switch.doubleWrite",defaultValue = "0")
    public static Integer doubleWrite;


    /**
     * 优先从哪里读取 1- 优先从明文   2-只从密文
     */
    @EtcdValue(key = "encrypt.switch.readFrom",defaultValue = "2")
    public static Integer readFrom;


    /**
     * 是否开启从摘要筛选数据 0-否  1-是
     */
    @EtcdValue(key = "encrypt.switch.selectByDigest",defaultValue = "1")
    public static Integer selectByDigest ;
}
