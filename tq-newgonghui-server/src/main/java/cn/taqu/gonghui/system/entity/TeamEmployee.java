package cn.taqu.gonghui.system.entity;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
@Data
@TableName(value = "team_employee" ,autoResultMap = true)
public class TeamEmployee {

    @TableId(value = "employee_id",type = IdType.AUTO)
    private Long employeeId;
    private Long orgId;
    /**
     * 机构名称
     */
    private String orgName;

    private Long teamId;

    private Long userId;
    /**
     *  成员类型（2-负责人，3-经纪人）
     */
    private Integer type;
    /**
     * 成员姓名
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String employeeName;
    /**
     * 成员姓名-密文
     */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String employeeNameCipher;

    @TableField(typeHandler = Sm3EncryptTypeHandler.class)
    private String employeeNameDigest;
    /**
     * 成员手机号
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String mobile;
    /**
     * 成员手机号-密文
     */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String mobileCipher;
    /**
     * 成员手机号-摘要
     */
    @TableField(typeHandler = Sm3EncryptTypeHandler.class)
    private String mobileDigest;

    private String inviteCode;

    private Integer status;

    private Long inviteTime;

    private Long createTime;

    private Long updateTime;

    public String getEmployeeName() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.employeeNameCipher;
        }
        if (StringUtils.isBlank(this.employeeName) && StringUtils.isNotBlank(this.employeeNameCipher)) {
            return this.employeeNameCipher;
        }
        return employeeName;
    }

    public String getEmployeeNameDigest() {
        return getEmployeeName();
    }

    public String getMobile() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.mobileCipher;
        }
        if (StringUtils.isBlank(this.mobile) && StringUtils.isNotBlank(this.mobileCipher)) {
            return this.mobileCipher;
        }
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
        this.mobileCipher = mobile;
        this.mobileDigest = mobile;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
        this.employeeNameCipher = employeeName;
        this.employeeNameDigest = employeeName;
    }
}
