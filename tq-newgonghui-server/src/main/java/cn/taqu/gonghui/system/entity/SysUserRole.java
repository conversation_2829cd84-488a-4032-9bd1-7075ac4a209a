package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/4/30
 */
@Data
public class SysUserRole {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @TableField
    private Long userId;
    @TableField
    private Long roleId;

    private Long createTime;

    private Long updateTime;
}
