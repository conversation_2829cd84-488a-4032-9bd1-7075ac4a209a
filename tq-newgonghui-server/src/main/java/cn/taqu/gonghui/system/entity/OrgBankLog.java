package cn.taqu.gonghui.system.entity;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;
@Data
@TableName(value = "org_bank_log", autoResultMap = true)
public class OrgBankLog {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 对公银行账号（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldAccount;
    /**
     * 负责人手机号(旧)-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldAccountCipher;

    /**
     * 对公银行账号（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newAccount;
    /**
     * 对公银行账号（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newAccountCipher;

    /**
     * 开户地（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldAddress;
    /**
     * 开户地（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldAddressCipher;

    /**
     *  开户地（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newAddress;
    /**
     *  开户地（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newAddressCipher;

    /**
     * 支行名称（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldBankName;
    /**
     * 支行名称（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldBankNameCipher;
    /**
     * 支行名称（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newBankName;
    /**
     * 支行名称（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newBankNameCipher;
    /**
     * 开户行（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldBank;
    /**
     * 开户行（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldBankCipher;
    /**
     * 开户行（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newBank;
    /**
     * 开户行（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newBankCipher;

    /**
     * 开户名称（旧）
     */
    private String oldAccountName;
    /**
     * 开户名称（新）
     */
    private String newAccountName;

    /**
     * 操作人员
     */
    private String createOperator;

    private Date createTime;

    /**
     * 关联id
     */
    private Long relevanceId;

    public String getOldAccount() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldAccountCipher;
        }
        if(StringUtils.isBlank(this.oldAccount) && StringUtils.isNotBlank(this.oldAccountCipher)){
            return this.oldAccountCipher;
        }
        return this.oldAccount;
    }

    public String getNewAccount() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newAccountCipher;
        }
        if(StringUtils.isBlank(this.newAccount) && StringUtils.isNotBlank(this.newAccountCipher)){
            return this.newAccountCipher;
        }
        return this.newAccount;
    }

    public String getOldAddress() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldAddressCipher;
        }
        if(StringUtils.isBlank(this.oldAddress) && StringUtils.isNotBlank(this.oldAddressCipher)){
            return this.oldAddressCipher;
        }
        return this.oldAddress;
    }

    public String getNewAddress() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newAddressCipher;
        }
        if(StringUtils.isBlank(this.newAddress) && StringUtils.isNotBlank(this.newAddressCipher)){
            return this.newAddressCipher;
        }
        return newAddress;
    }

    public String getOldBankName() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldBankNameCipher;
        }
        if(StringUtils.isBlank(this.oldBankName) && StringUtils.isNotBlank(this.oldBankNameCipher)){
            return this.oldBankNameCipher;
        }
        return this.oldBankName;
    }

    public String getNewBankName() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newBankNameCipher;
        }
        if(StringUtils.isBlank(this.newBankName) && StringUtils.isNotBlank(this.newBankNameCipher)){
            return this.newBankNameCipher;
        }
        return this.newBankName;
    }

    public String getOldBank() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldBankCipher;
        }
        if(StringUtils.isBlank(this.oldBank) && StringUtils.isNotBlank(this.oldBankCipher)){
            return this.oldBankCipher;
        }
        return this.oldBank;
    }

    public String getNewBank() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newBankCipher;
        }
        if(StringUtils.isBlank(this.newBank) && StringUtils.isNotBlank(this.newBankCipher)){
            return this.newBankCipher;
        }
        return newBank;
    }

    public void setOldAccount(String oldAccount) {
        this.oldAccount = oldAccount;
        this.oldAccountCipher = oldAccount;
    }

    public void setNewAccount(String newAccount) {
        this.newAccount = newAccount;
        this.newAccountCipher = newAccount;
    }

    public void setOldAddress(String oldAddress) {
        this.oldAddress = oldAddress;
        this.oldAddressCipher = oldAddress;
    }

    public void setNewAddress(String newAddress) {
        this.newAddress = newAddress;
        this.newAddressCipher = newAddress;
    }

    public void setOldBankName(String oldBankName) {
        this.oldBankName = oldBankName;
        this.oldBankNameCipher = oldBankName;
    }

    public void setNewBankName(String newBankName) {
        this.newBankName = newBankName;
        this.newBankNameCipher = newBankName;
    }

    public void setOldBank(String oldBank) {
        this.oldBank = oldBank;
        this.oldBankCipher = oldBank;
    }

    public void setNewBank(String newBank) {
        this.newBank = newBank;
        this.newBankCipher = newBank;
    }
}