package cn.taqu.gonghui.system.service.impl;

import cn.taqu.gonghui.system.entity.OrgBankLog;
import cn.taqu.gonghui.system.mapper.OrgBankLogMapper;
import cn.taqu.gonghui.system.service.IOrgBankLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/3 16 33
 * discription
 */
@Service
public class OrgBankLogServiceImpl extends ServiceImpl<OrgBankLogMapper, OrgBankLog> implements IOrgBankLogService {
    @Override
    public OrgBankLog getLastByRelevanceId(Long relevanceId) {
        LambdaQueryWrapper<OrgBankLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgBankLog::getRelevanceId, relevanceId).orderByDesc(OrgBankLog::getId).last(" limit 1");
        List<OrgBankLog> orgBankLogs = this.baseMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(orgBankLogs)){
            return null;
        }
        return orgBankLogs.get(0);
    }
}
