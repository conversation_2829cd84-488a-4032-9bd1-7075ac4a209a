package cn.taqu.gonghui.system.vo;

import lombok.Data;

/**
 * 团队名称、主播uuid、修改时间、修改结束时间、原始分成比例、修改分成比例、状态1待确认2拒绝 3同意 4取消
 */

@Data
public class SharingResultVo {

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 主播uuid
     */
    private String hostUuid;

    /**
     * 调整时间
     */
    private Long changeTime;

    /**
     * 调整结束时间
     */
    private Long changeEndTime;

    /**
     * 当前分润比例
     */
    private String currentSharingProfitRate;

    /**
     * 新的分润比例
     */
    private String newSharingProfitRate;

    /**
     *状态（1-待确认，2-已确认，待生效，3-已生效，4-已拒绝）
     */
    private Integer status;

    /**
     * 操作类型（1-用户端，2-管理端）
     */
    private Integer type;

    /**
     * 是否灰度用户首次申请 0否 1是
     */
    private Integer isGrayFirst;

    /**
     * 主播类型 1自营 2自提
     */
    private Integer hostType;

    /**
     * 生效类型 1次月生效 2马上生效 该字段用来做h5通知详情文案回显用
     */
    private Integer effectiveType;

}
