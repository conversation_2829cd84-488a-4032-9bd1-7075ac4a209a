package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.utils.SecurityUtils;
import cn.taqu.gonghui.live.vo.HostTaskStatisticInfoVo;
import cn.taqu.gonghui.soa.HostTaskSoaService;
import cn.taqu.gonghui.soa.dto.HostTaskStatisticInfo;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.service.HostTaskService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamHostService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/2/19 16 47
 * discription
 */
@Service
@Slf4j
public class HostTaskServiceImpl implements HostTaskService {

    @Resource
    private TeamHostService teamHostService;

    @Resource
    private HostTaskSoaService hostTaskSoaService;

    @Resource
    private TeamEmployeeService teamEmployeeService;

    @Override
    public IPage<HostTaskStatisticInfoVo> pageStatDataBySearch(Integer page, Integer pageSize, Long monthDate, String hostUuid) {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        if(user == null){
            throw new ServiceException("un_login","未登录，请先登录~");
        }

        IPage<TeamHost> byPage = new Page<>(page, pageSize);
        if(user.getUserType().equals(UserTypeEnum.AGENTER.getType())){
            byPage = teamHostService.pageByOwnEmployee(page, pageSize, user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
        }
        else if(user.getUserType().equals(UserTypeEnum.MANAGER.getType())){
            byPage = teamHostService.findByPage(user.getOrgId(), null, null, hostUuid, TeamTypeEnum.LIVE_TEAM.getValue(), page, pageSize, null);
        }else if(user.getUserType().equals(UserTypeEnum.LEADER.getType())){
            // 获取对应的团队
            List<TeamEmployee> teamEmployeeList = teamEmployeeService.getOneByUserId(user.getUserId());
            if(CollectionUtils.isEmpty(teamEmployeeList)){
                byPage = teamHostService.findByPage(user.getOrgId(), null, null, hostUuid, TeamTypeEnum.LIVE_TEAM.getValue(), page, pageSize, null);
                log.warn("当前用户为团队负责人，但未找到用户对应的团队。userId={}", user.getUserId());
            }else {
                List<Long> teamIds = teamEmployeeList.stream().map(l -> l.getTeamId()).collect(Collectors.toList());
                byPage = teamHostService.findByPage(user.getOrgId(), teamIds, null, hostUuid, TeamTypeEnum.LIVE_TEAM.getValue(), page, pageSize, null);
            }
        }
        if(CollectionUtils.isEmpty(byPage.getRecords())){
            return new Page<>(page, pageSize, 0L);
        }
        List<String> hostUuidList = byPage.getRecords().stream().map(l -> l.getHostUuid()).collect(Collectors.toList());
//        hostUuidList.add("p224t03oxss");
//        hostUuidList.add("cbhhdszbb1xm");
        List<HostTaskStatisticInfo> list = hostTaskSoaService.listStatData(hostUuidList, monthDate);

        //针对同一月份和同一主播的进行合并
        Page<HostTaskStatisticInfoVo> pageResult = new Page<>();
        Map<String, List<HostTaskStatisticInfo>> groupList = list.stream().collect(Collectors.groupingBy(l -> l.getDate() + "|" + l.getHostUuid()));
        List<HostTaskStatisticInfoVo> voList = new ArrayList<>();
        for (Map.Entry<String, List<HostTaskStatisticInfo>> entry : groupList.entrySet()) {
            HostTaskStatisticInfoVo voItem = new HostTaskStatisticInfoVo();

            voItem.setDate(entry.getValue().get(0).getDate());
            voItem.setMonth(entry.getValue().get(0).getMonth());
            voItem.setHostUuid(entry.getValue().get(0).getHostUuid());
            voItem.setNickname(entry.getValue().get(0).getNickname());
            if(entry.getValue().size() == 1){
                voItem.setTaskId(entry.getValue().get(0).getTaskId());
                voItem.setTaskTitle(entry.getValue().get(0).getTaskTitle());
                voItem.setIsFinish(entry.getValue().get(0).getIsFinish());
                voItem.setEffectiveDay(entry.getValue().get(0).getEffectiveDay());
                voItem.setMonthDesc(entry.getValue().get(0).getMonthDesc());
                voItem.setStarNum(entry.getValue().get(0).getStarNum());
            }else {
                voItem.setChildren(entry.getValue());
                voItem.setTaskId("");
                voItem.setTaskTitle("--");
                voItem.setEffectiveDay("--");
                voItem.setIsFinish("--");
                voItem.setMonthDesc("--");
                voItem.setStarNum("--");
            }
            voList.add(voItem);
        }
        pageResult.setPages(byPage.getPages());
        pageResult.setCurrent(byPage.getCurrent());
        pageResult.setTotal(byPage.getTotal());
        pageResult.setSize(byPage.getSize());
        pageResult.setRecords(voList);
        return pageResult;
    }

    @Override
    public JSONObject listDetailData(String hostUuid, Integer taskUuid, Long month) {
        return hostTaskSoaService.listDetail(hostUuid, taskUuid, month);
    }
}
