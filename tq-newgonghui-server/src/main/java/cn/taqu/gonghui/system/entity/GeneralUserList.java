package cn.taqu.gonghui.system.entity;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.net.ntp.TimeStamp;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "general_user_list", autoResultMap = true)
public class GeneralUserList {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer scene;

    private String loginName;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String trueName;

    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String trueNameCipher;

    private Long createTime;

    private String createUname;

//    private TimeStamp modifyTime;

    private String modifyUname;

    private Integer isDeleted;

    public void setTrueName(String trueName) {
        this.trueName = trueName;
        this.trueNameCipher = trueName;
    }

    public String getTrueName() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.trueNameCipher;
        }
        if(StringUtils.isBlank(this.trueName) && StringUtils.isNotBlank(this.trueNameCipher)){
            return this.trueNameCipher;
        }
        return trueName;
    }
}