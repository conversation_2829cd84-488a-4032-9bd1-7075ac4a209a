package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.entity.QuestionFeedback;
import cn.taqu.gonghui.system.search.QuestionFeedbackSearch;
import cn.taqu.gonghui.system.vo.QuestionFeedbackVo;

import java.util.List;

public interface QuestionFeedbackService {

    List<QuestionFeedbackVo> manageList(QuestionFeedbackSearch search);

    List<QuestionFeedbackVo> userList(QuestionFeedbackSearch search);

    void add(QuestionFeedback record,Integer businessType);

    void changeStatus(Long id,Integer status,String remark,String followUpPerson);
}
