package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.vo.LiveRecommendApplyVo;
import cn.taqu.gonghui.system.vo.PageDataVo;
import cn.taqu.gonghui.system.vo.RecommendApplyInfoVo;

public interface LiveRecommendApplyService {

    PageDataVo<LiveRecommendApplyVo> getApplyList(Long teamId,String date, String hostUuid, String nickname, Integer status,
                                                  String location, Integer page, Integer pageSize,Integer type);

    void cancelApplyById(Long id);

    RecommendApplyInfoVo getRecommendList(String hostUuid);

    void saveRecommendApply(String hostUuid, String date, String location, String time);
}
