package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName(value = "operation_person")
public class Operation<PERSON>erson extends BaseDO {
    @TableField(value = "loginname")
    private String loginname;
    @TableField(value = "name")
    private String name;
    @TableField(value = "permission")
    private Integer permission;
    @TableField(value = "work_status")
    private Integer workStatus;
    @TableField(value = "invite_link")
    private String inviteLink;


}
