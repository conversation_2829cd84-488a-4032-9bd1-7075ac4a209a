package cn.taqu.gonghui.system.service.impl;

import cn.taqu.gonghui.system.entity.Region;
import cn.taqu.gonghui.system.mapper.RegionMapper;
import cn.taqu.gonghui.system.service.RegionService;
import cn.taqu.gonghui.system.vo.RegionLevelOneVo;
import cn.taqu.gonghui.system.vo.RegionLevelTwoVo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RegionServiceImpl implements RegionService {

    @Autowired
    private RegionMapper regionMapper;
    @Autowired
    private StringRedisTemplate masterStringRedisTemplate;

    @Override
    public List<RegionLevelOneVo> findRegionList() {
        String regionJson = masterStringRedisTemplate.opsForValue().get("gonghui_region_list");
        if (StringUtils.isNotBlank(regionJson)) {
            List<RegionLevelOneVo> regionLevelOneVoList = JSON.parseObject(regionJson, new TypeReference<List<RegionLevelOneVo>>() {
            });
            return regionLevelOneVoList;
        }
        List<Region> regionList = regionMapper.findRegionList();
        List<Region> level1List = regionList.stream().filter(region -> region.getLevel().intValue() == 1).collect(Collectors.toList());
        List<Region> level2List = regionList.stream().filter(region -> region.getLevel().intValue() == 2).collect(Collectors.toList());
        List<RegionLevelOneVo> regionVoList = new ArrayList<>();
        level1List.stream().forEach(level1 -> {
            RegionLevelOneVo levelOneVo = new RegionLevelOneVo();
            List<RegionLevelTwoVo> levelTwoVoList = new ArrayList<>();
            levelOneVo.setLabel(level1.getRegionName());
            levelOneVo.setValue(level1.getId());
            level2List.stream().forEach(level2 -> {
                if (level2.getParentId().longValue() == level1.getId().longValue()) {
                    RegionLevelTwoVo levelTwoVo = new RegionLevelTwoVo();
                    levelTwoVo.setLabel(level2.getRegionName());
                    levelTwoVo.setValue(level2.getId());
                    levelTwoVoList.add(levelTwoVo);
                }
            });
            levelOneVo.setChildren(levelTwoVoList);
            regionVoList.add(levelOneVo);
        });
        masterStringRedisTemplate.opsForValue().set("gonghui_region_list", JSON.toJSONString(regionVoList));
        return regionVoList;
    }
}
