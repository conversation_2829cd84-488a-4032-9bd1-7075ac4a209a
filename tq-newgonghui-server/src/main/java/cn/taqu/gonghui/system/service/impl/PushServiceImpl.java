package cn.taqu.gonghui.system.service.impl;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.DingRobotWebhookKey;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.soa.DingRobotService;
import cn.taqu.gonghui.soa.InfoService;
import cn.taqu.gonghui.system.service.PushService;
import cn.taqu.gonghui.system.vo.TeamOrgInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-16 09:45
 */
@Service
@Slf4j
public class PushServiceImpl implements PushService {

    @SoaReference("account")
    private InfoService infoService;
    @EtcdValue(value = "biz.feishu.teamMoveGroup")
    public static String teamMoveGroup;

    @Override
    public void pushTeamHostChangeTeamToDingRobot(String hostUuid, String operator, Integer teamType, TeamOrgInfoVO oldTeam, TeamOrgInfoVO newTeam) {
        try{
            String oldTeamName = oldTeam == null ? "" : StringUtils.isNotBlank(oldTeam.getOrgAndTeamInfo()) ? oldTeam.getOrgAndTeamInfo() : oldTeam.getOrgName() + " - " + oldTeam.getTeamName();
            String newTeamName = newTeam == null ? "" : StringUtils.isNotBlank(newTeam.getOrgAndTeamInfo()) ? newTeam.getOrgAndTeamInfo() : newTeam.getOrgName() + " - " + newTeam.getTeamName();
            Map<String, Object> infos = infoService.getInfoByUuid(new String[]{hostUuid}, new String[]{"account_name"}, "1", false, false).get(hostUuid);
            String accountName = MapUtils.getString(infos, "account_name", "");
            if(StringUtils.isNotBlank(accountName)){
                accountName += " - ";
            }
            String mdText = String.format("{\n" +
                    "    \"msg_type\": \"interactive\",\n" +
                    "    \"card\": {\n" +
                    "        \"config\": {\n" +
                    "            \"wide_screen_mode\": true,\n" +
                    "            \"enable_forward\": true\n" +
                    "        },\n" +
                    "        \"elements\": [\n" +
                    "            {\n" +
                    "                \"fields\": [\n" +
                    "                    {\n" +
                    "                        \"is_short\": true,\n" +
                    "                        \"text\": {\n" +
                    "                            \"content\": \"**\uD83D\uDC64 调整人：**\\n%s\",\n" +
                    "                            \"tag\": \"lark_md\"\n" +
                    "                        }\n" +
                    "                    },\n" +
                    "                    {\n" +
                    "                        \"is_short\": true,\n" +
                    "                        \"text\": {\n" +
                    "                            \"content\": \"**\uD83D\uDC64 被调整人：**\\n%s\",\n" +
                    "                            \"tag\": \"lark_md\"\n" +
                    "                        }\n" +
                    "                    }\n" +
                    "                ],\n" +
                    "                \"tag\": \"div\"\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"fields\": [\n" +
                    "                    {\n" +
                    "                        \"is_short\": true,\n" +
                    "                        \"text\": {\n" +
                    "                            \"content\": \"**\uD83D\uDDC2️ 调整之前机构团队：**\\n%s\",\n" +
                    "                            \"tag\": \"lark_md\"\n" +
                    "                        }\n" +
                    "                    },\n" +
                    "                    {\n" +
                    "                        \"is_short\": true,\n" +
                    "                        \"text\": {\n" +
                    "                            \"content\": \"**\uD83D\uDDC2️ 调整之后机构团队：**\\n%s\",\n" +
                    "                            \"tag\": \"lark_md\"\n" +
                    "                        }\n" +
                    "                    },\n" +
                    "                    {\n" +
                    "                        \"is_short\": false,\n" +
                    "                        \"text\": {\n" +
                    "                            \"content\": \"\",\n" +
                    "                            \"tag\": \"lark_md\"\n" +
                    "                        }\n" +
                    "                    },\n" +
                    "                    {\n" +
                    "                        \"is_short\": false,\n" +
                    "                        \"text\": {\n" +
                    "                            \"content\": \"**\uD83D\uDCC5 调整时间：**\\n%s\",\n" +
                    "                            \"tag\": \"lark_md\"\n" +
                    "                        }\n" +
                    "                    }\n" +
                    "                ],\n" +
                    "                \"tag\": \"div\"\n" +
                    "            }\n" +
                    "        ],\n" +
                    "        \"header\": {\n" +
                    "            \"template\": \"blue\",\n" +
                    "            \"title\": {\n" +
                    "                \"content\": \"后台调整艺人归属公会\",\n" +
                    "                \"tag\": \"plain_text\"\n" +
                    "            }\n" +
                    "        }\n" +
                    "    }\n" +
                    "}", operator, accountName + hostUuid, oldTeamName, newTeamName, DateUtil.dateToString20(new Date()));
            HttpRequest httpRequest = HttpUtil.createPost(teamMoveGroup);
            httpRequest.body(JSONUtil.parseObj(mdText).toString(), "application/json");
            HttpResponse httpResponse = httpRequest.execute();
            log.info("飞书推送返回结果：{}", httpResponse.body());
        }catch (Exception e){
            log.warn("飞书推送异常,用户公会机构团队变更操作,params={}-{}-{}-{},", hostUuid, operator, JsonUtils.objectToString2(oldTeam), JsonUtils.objectToString2(newTeam), e);
        }
    }

    @Override
    public void pushBackstageOperateToDingRobot(String operator, String operateTypeName, String teamTypeName, String info) {
        try {
            String mdText = String.format("{\n" +
                            "    \"msg_type\": \"interactive\",\n" +
                            "    \"card\": {\n" +
                            "        \"config\": {\n" +
                            "            \"wide_screen_mode\": true,\n" +
                            "            \"enable_forward\": true\n" +
                            "        },\n" +
                            "        \"elements\": [\n" +
                            "            {\n" +
                            "                \"fields\": [\n" +
                            "                    {\n" +
                            "                        \"is_short\": true,\n" +
                            "                        \"text\": {\n" +
                            "                            \"content\": \"**操作人：**\\%s\",\n" +
                            "                            \"tag\": \"lark_md\"\n" +
                            "                        }\n" +
                            "                    },\n" +
                            "                    {\n" +
                            "                        \"is_short\": true,\n" +
                            "                        \"text\": {\n" +
                            "                            \"content\": \"**操作类型：**\\n%s\",\n" +
                            "                            \"tag\": \"lark_md\"\n" +
                            "                        }\n" +
                            "                    }\n" +
                            "                ],\n" +
                            "                \"tag\": \"div\"\n" +
                            "            },\n" +
                            "            {\n" +
                            "                \"fields\": [\n" +
                            "                    {\n" +
                            "                        \"is_short\": true,\n" +
                            "                        \"text\": {\n" +
                            "                            \"content\": \"**业务类型：**\\n%s\",\n" +
                            "                            \"tag\": \"lark_md\"\n" +
                            "                        }\n" +
                            "                    },\n" +
                            "                    {\n" +
                            "                        \"is_short\": true,\n" +
                            "                        \"text\": {\n" +
                            "                            \"content\": \"**时间：**\\n%s\",\n" +
                            "                            \"tag\": \"lark_md\"\n" +
                            "                        }\n" +
                            "                    },\n" +
                            "                    {\n" +
                            "                        \"is_short\": false,\n" +
                            "                        \"text\": {\n" +
                            "                            \"content\": \"\",\n" +
                            "                            \"tag\": \"lark_md\"\n" +
                            "                        }\n" +
                            "                    },\n" +
                            "                    {\n" +
                            "                        \"is_short\": false,\n" +
                            "                        \"text\": {\n" +
                            "                            \"content\": \"**操作内容：**\\n%s\",\n" +
                            "                            \"tag\": \"lark_md\"\n" +
                            "                        }\n" +
                            "                    }\n" +
                            "                ],\n" +
                            "                \"tag\": \"div\"\n" +
                            "            }\n" +
                            "        ],\n" +
                            "        \"header\": {\n" +
                            "            \"template\": \"blue\",\n" +
                            "            \"title\": {\n" +
                            "                \"content\": \"后台操作\",\n" +
                            "                \"tag\": \"plain_text\"\n" +
                            "            }\n" +
                            "        }\n" +
                            "    }\n" +
                            "}",
                    operator, operateTypeName, teamTypeName, DateUtil.dateToString20(new Date()), info);
            HttpRequest httpRequest = HttpUtil.createPost(teamMoveGroup);
            httpRequest.body(JSONUtil.parseObj(mdText).toString(), "application/json");
            HttpResponse httpResponse = httpRequest.execute();
            log.info("飞书推送返回结果：{}", httpResponse.body());
        } catch (Exception e) {
            log.warn("飞书操作通知异常：", e);
        }
    }
}
