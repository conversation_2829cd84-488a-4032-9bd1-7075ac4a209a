package cn.taqu.gonghui.system.entity;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;
@Data
@TableName(value = "org_company_log", autoResultMap = true)
public class OrgCompanyLog {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 企业全名（新）
     */
    private String newEnterpriseName;
    /**
     * 企业全名（旧）
     */
    private String oldEnterpriseName;

    /**
     * 法人姓名（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newLegalPerson;

    /**
     * 法人姓名（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newLegalPersonCipher;
    /**
     * 法人姓名（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldLegalPerson;
    /**
     * 法人姓名（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldLegalPersonCipher;
    /**
     * 法人手机号（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newLegalPersonPhone;
    /**
     * 法人手机号（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newLegalPersonPhoneCipher;
    /**
     * 法人手机号（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldLegalPersonPhone;
    /**
     * 法人手机号（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldLegalPersonPhoneCipher;

    /**
     * 法人身份证号（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newLegalPersonidCard;
    /**
     * 法人身份证号（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newLegalPersonidCardCipher;
    /**
     * 法人身份证号（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldLegalPersonidCard;
    /**
     * 法人身份证号（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldLegalPersonidCardCipher;

    /**
     * 法人身份证正面照片(新)
     */
    private String newLegalPersonUrlFront;
    /**
     * 法人身份证正面照片（旧）
     */
    private String oldLegalPersonUrlFront;

    /**
     * 法人身份证反面照片(新)
     */
    private String newLegalPersonUrlReverse;
    /**
     * 法人身份证反面照片(旧)
     */
    private String oldLegalPersonUrlReverse;

    /**
     * 法人身份证手持照片(新)
     */
    private String newLegalPersonUrlHand;
    /**
     * 法人身份证手持照片(旧)
     */
    private String oldLegalPersonUrlHand;
    /**
     * 营业执照(新)
     */
    private String newBusinessLicenseUrl;
    /**
     * 营业执照(旧)
     */
    private String oldBusinessLicenseUrl;

    /**
     * 社会统一信用代码（新）
     */
    private String newSocialUnifiedCreditCode;

    /**
     * 社会统一信用代码（旧）
     */
    private String oldSocialUnifiedCreditCode;

    /**
     * 经营场所（新）
     */
    private String newPremises;

    /**
     * 经营场所(旧)
     */
    private String oldPremises;

    /**
     * 操作人
     */
    private String createOperator;

    private Date createTime;

    /**
     * 关联机构id
     */
    private Long relevanceId;


    public String getNewLegalPerson() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newLegalPersonCipher;
        }
        if(StringUtils.isBlank(this.newLegalPerson)&&StringUtils.isNotBlank(this.newLegalPersonCipher)){
            return this.newLegalPersonCipher;
        }
        return this.newLegalPerson;
    }

    public String getOldLegalPerson() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldLegalPersonCipher;
        }
        if(StringUtils.isBlank(this.oldLegalPerson)&&StringUtils.isNotBlank(this.oldLegalPersonCipher)){
            return this.oldLegalPersonCipher;
        }
        return this.oldLegalPerson;
    }

    public String getNewLegalPersonPhone() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newLegalPersonPhoneCipher;
        }
        if(StringUtils.isBlank(this.newLegalPersonPhone)&&StringUtils.isNotBlank(this.newLegalPersonPhoneCipher)){
            return this.newLegalPersonPhoneCipher;
        }
        return this.newLegalPersonPhone;
    }

    public String getOldLegalPersonPhone() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldLegalPersonPhoneCipher;
        }
        if(StringUtils.isBlank(this.oldLegalPersonPhone)&&StringUtils.isNotBlank(this.oldLegalPersonPhoneCipher)){
            return this.oldLegalPersonPhoneCipher;
        }
        return this.oldLegalPersonPhone;
    }

    public String getNewLegalPersonidCard() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newLegalPersonidCardCipher;
        }
        if(StringUtils.isBlank(this.newLegalPersonidCard)&&StringUtils.isNotBlank(this.newLegalPersonidCardCipher)){
            return this.newLegalPersonidCardCipher;
        }
        return this.newLegalPersonidCard;
    }

    public String getOldLegalPersonidCard() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldLegalPersonidCardCipher;
        }
        if(StringUtils.isBlank(this.oldLegalPersonidCard)&&StringUtils.isNotBlank(this.oldLegalPersonidCardCipher)){
            return this.oldLegalPersonidCardCipher;
        }
        return this.oldLegalPersonidCard;
    }


    public void setNewLegalPerson(String newLegalPerson) {
        this.newLegalPerson = newLegalPerson;
        this.newLegalPersonCipher = newLegalPerson;
    }

    public void setOldLegalPerson(String oldLegalPerson) {
        this.oldLegalPerson = oldLegalPerson;
        this.oldLegalPersonCipher = oldLegalPerson;
    }

    public void setNewLegalPersonPhone(String newLegalPersonPhone) {
        this.newLegalPersonPhone = newLegalPersonPhone;
        this.newLegalPersonPhoneCipher = newLegalPersonPhone;
    }

    public void setOldLegalPersonPhone(String oldLegalPersonPhone) {
        this.oldLegalPersonPhone = oldLegalPersonPhone;
        this.oldLegalPersonPhoneCipher = oldLegalPersonPhone;
    }

    public void setNewLegalPersonidCard(String newLegalPersonidCard) {
        this.newLegalPersonidCard = newLegalPersonidCard;
        this.newLegalPersonidCardCipher = newLegalPersonidCard;
    }

    public void setOldLegalPersonidCard(String oldLegalPersonidCard) {
        this.oldLegalPersonidCard = oldLegalPersonidCard;
        this.oldLegalPersonidCardCipher = oldLegalPersonidCard;
    }
}