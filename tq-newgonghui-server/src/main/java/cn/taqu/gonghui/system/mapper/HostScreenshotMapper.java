package cn.taqu.gonghui.system.mapper;


import cn.taqu.gonghui.system.entity.HostScreenshot;
import cn.taqu.gonghui.system.entity.OrgCooperationFlow;

import java.util.List;

public interface HostScreenshotMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(HostScreenshot record);

    int insertSelective(HostScreenshot record);

    HostScreenshot selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(HostScreenshot record);

    int updateByPrimaryKey(HostScreenshot record);

    int deleteByUuid(String uuid);

    List<HostScreenshot> getAllByOrgUuid(String orgUuid);
}