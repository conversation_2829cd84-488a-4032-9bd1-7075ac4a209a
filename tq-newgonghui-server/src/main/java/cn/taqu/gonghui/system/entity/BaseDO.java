package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;
@Data
public class BaseDO {
    @TableId(value = "id", type = IdType.AUTO)
    public Long id;
    @TableField(value = "create_time")
    public Date createTime;
    @TableField(value = "modify_time")
    public Date modifyTime;
}
