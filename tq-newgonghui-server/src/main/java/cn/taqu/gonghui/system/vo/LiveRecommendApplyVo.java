package cn.taqu.gonghui.system.vo;

import lombok.Data;

/**
 * Created by gjj on 2021/05/31.
 */
@Data
public class LiveRecommendApplyVo {
    private Long id;
    /**
     * 申请推荐日期
     */
    private String date;
    /**
     * 时间
     */
    private String time;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 主播uuid
     */
    private String hostUuid;
    /**
     * 位置
     */
    private String locationName;
    /**
     * 状态 1审批中 2审批通过 -1拒绝申请 -2撤销申请'
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Long createTime;

    private Integer useRecommendCard;
}
