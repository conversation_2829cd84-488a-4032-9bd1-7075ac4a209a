package cn.taqu.gonghui.system.mapper;


import cn.taqu.gonghui.system.entity.BusinessLicense;
import cn.taqu.gonghui.system.entity.LegalPerson;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

public interface BusinessLicenseMapper extends BaseMapper<BusinessLicense> {
    int deleteByPrimaryKey(Integer id);

    int insert(BusinessLicense record);

    int insertSelective(BusinessLicense record);

    BusinessLicense selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(BusinessLicense record);

    int updateByPrimaryKey(BusinessLicense record);

    int deleteByUuid(String uuid);

    List<BusinessLicense> getAllByOrgUuid(String orgUuid);
}