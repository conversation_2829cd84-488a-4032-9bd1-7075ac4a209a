package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 营销管理（推荐位申请卡使用记录）
 */
@Data
public class RecommendApplyCardUseLog {
    /**
     * 流水号
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 卡片编号
     */
    private String cardNo;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 使用者uuid
     */
    private String hostUuid;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 更新时间
     */
    private Integer updateTime;

    /**
     * 主播名称
     */
    private String hostName;

    /**
     * 使用时间段
     */
    private String useTime;

    /**
     * 栏位信息
     */
    private String banner;
}
