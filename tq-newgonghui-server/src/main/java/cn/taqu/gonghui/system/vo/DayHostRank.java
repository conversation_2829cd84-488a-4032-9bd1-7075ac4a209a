package cn.taqu.gonghui.system.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class DayHostRank implements Serializable {

    @JSONField(name = "rank_list")
    private List<RankList> rankList;
    @JSONField(name = "total")
    private String total;

    @NoArgsConstructor
    @Data
    public static class RankList {
        @J<PERSON>NField(name = "host_uuid")
        private String hostUuid;
        @J<PERSON><PERSON><PERSON>(name = "nickname")
        private String nickname;
        @JSONField(name = "amount")
        private String amount;
        @J<PERSON><PERSON>ield(name = "time")
        private String time;
    }
}
