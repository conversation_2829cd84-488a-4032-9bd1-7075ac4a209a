package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName(value = "credit_grade_Log")
public class CreditGradeLog extends BaseDO {
    @TableField(value = "credit_grade")
    private Integer creditGrade;//"信用分"
    @TableField(value = "operation_name")
    private String operationName;  //修改
    @TableField(value = "reason")
    private String reason; // 理由
    @TableField(value = "org_id")
    private String orgId; // 机构id

}
