package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.constant.HostOperateTypeEnum;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.entity.HostModifyRecord;
import cn.taqu.gonghui.system.vo.HostMoveInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-10 11:45
 */
public interface HostModifyRecordService extends IService<HostModifyRecord> {

    /**
     * 增加记录
     *
     * @param hostUuid
     * @param teamType
     * @param batchId
     * @param typeEnum
     * @param info
     * @param reason
     * @param failMsg
     * @param operator
     */
    void addRecord(String hostUuid, Integer teamType, String batchId, HostOperateTypeEnum typeEnum, ModifyRecordInfoDTO info, String reason, Integer status, String failMsg, String operator);

    /**
     * 根据batchid获取艺人uuid
     *
     * @param batchId
     * @param status
     * @return
     */
    List<String> getUuidByBatchId(String batchId, Integer status);

    /**
     * 获取艺人转会记录
     *
     * @param uuid
     * @param teamType
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<HostMoveInfoVo> getHostMoveList(String uuid, Integer teamType, Integer pageNo, Integer pageSize);

    /**
     * 获取艺人转回记录总条数
     *
     * @param uuid
     * @param teamType
     * @return
     */
    Long countHostMoveList(String uuid, Integer teamType);

    /**
     * 获取更改信息
     * @param hostUuid
     * @param typeEnum
     * @return
     */
    ModifyRecordInfoDTO getModifyRecordInfo(String hostUuid, HostOperateTypeEnum typeEnum);

}
