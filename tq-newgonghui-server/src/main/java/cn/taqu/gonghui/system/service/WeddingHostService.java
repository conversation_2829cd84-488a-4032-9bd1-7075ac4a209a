package cn.taqu.gonghui.system.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.taqu.gonghui.system.dto.WeddingHostDto;
import cn.taqu.gonghui.system.dto.WeddingHostInfoDto;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.vo.TeamHostVo;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@Service
public class WeddingHostService {

    @Autowired
    private TeamHostMapper teamHostMapper;

    public List<WeddingHostInfoDto> query(WeddingHostDto weddingHostDto, Integer page) {
        Map<String, Object> param = BeanUtil.beanToMap(weddingHostDto);
        return SoaService.getHostStat(param, page);
    }

    public List<Map<String, Object>> allOnlineHost(Long consortiaId) {
        List<Map<String, Object>> hosts = SoaService.allOnlineHost();

        List<String> uuids = hosts.stream().map(m -> MapUtil.getStr(m, "account_uuid")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<TeamHostVo> teamHostVos = teamHostMapper.orgInfoByHostUuidList(uuids);
        Map<String, Long> hostOrgMap = teamHostVos.stream().collect(Collectors.toMap(TeamHostVo::getHostUuid, TeamHostVo::getTeamId));

        List<Map<String, Object>> res = hosts.stream().filter(map -> {
            String accountUuid = MapUtil.getStr(map, "account_uuid");
            if (StringUtils.isBlank(accountUuid)) return false;
            return hostOrgMap.containsKey(accountUuid) && hostOrgMap.get(accountUuid).equals(consortiaId);
        }).collect(Collectors.toList());

        return res;
    }

    public void changeHost(String weddingUuid, String hostUuid, Long consortiaId) {
        // TODO: 2022/7/20 可能会报错 需要处理一下
        SoaService.changeHost(weddingUuid, hostUuid, consortiaId);
    }
}
