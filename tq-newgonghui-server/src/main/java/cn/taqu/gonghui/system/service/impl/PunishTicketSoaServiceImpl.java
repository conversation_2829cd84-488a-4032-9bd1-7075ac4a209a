package cn.taqu.gonghui.system.service.impl;

import cn.hutool.json.JSONUtil;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.Constants;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.common.vo.res.LinkAccountRes;
import cn.taqu.gonghui.live.entity.Employee;
import cn.taqu.gonghui.live.entity.LiveHostInfo;
import cn.taqu.gonghui.live.mapper.EmployeeMapper;
import cn.taqu.gonghui.live.mapper.LiveHostInfoMapper;
import cn.taqu.gonghui.live.vo.DoPunishVO;
import cn.taqu.gonghui.soa.PunishTicketSoaService;
import cn.taqu.gonghui.soa.SOAUtil;
import cn.taqu.gonghui.system.dto.PunishHourDTO;
import cn.taqu.gonghui.system.dto.PunishLogDTO;
import cn.taqu.gonghui.system.dto.PunishTagConfigDTO;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.mapper.TeamEmployeeMapper;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.mapper.TeamMapper;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.vo.PunishLogVO;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 风控-处罚
 * <AUTHOR>
 * @date 2022/10/08
 */
@Service
@Slf4j
public class PunishTicketSoaServiceImpl implements PunishTicketSoaService {

    /**
     * 风控etcd key
     */
    private static final String PUNISH_ETCD_URL = "/soa/application/live/punish";

    /**
     * 风控查询关联用户
     */
    private static final String ACCOUNT_ETCD_URL = "/soa/application/live/antiSpam";

    /**
     * 服务名称
     */
    private static final String SERVICE = "punishTicket";

    /**
     * 列表场景码
     */
    private static final String LIST_CODE = "live";

    /**
     * 应用编码 1-他趣
     */
    private static final int APP_CODE = 1;

    /**
     * 场景编码
     */
    private static final String SCENE_CODE = "newgonghui";

    /**
     * basictype 1 图片
     */
    private static final String IMG_TYPE = "1";

    /**
     * basictype video类型
     */
    private static final String VIDEO_TYPE = "4";

    /**
     * json格式类型
     */
    private static final String JSON_TYPE = "5";

    /**
     * 文本
     */
    private static final String TXT_TYPE = "2";

    // 定义时间格式化对象和定义格式化样式
    private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Resource
    private EmployeeMapper employeeMapper;

    @Resource
    private TokenService tokenService;

    @Resource
    private TeamMapper teamMapper;

    @Resource
    private TeamHostMapper teamHostMapper;

    @Resource
    private LiveHostInfoMapper liveHostInfoMapper;

    @Resource
    private TeamEmployeeMapper teamEmployeeMapper;

    @Autowired
    private SysUserService sysUserService;

    @Override
    public JsonResult doPunish(PunishLogDTO punishLogDTO) {
        if (Objects.isNull(punishLogDTO.getPunishId()) || punishLogDTO.getPunishId().equals(0)) {
            throw new ServiceException(CodeStatus.PARAM_NOT_EMPTY.value(), "处罚理由id不能为空");
        }
        if (StringUtils.isEmpty(punishLogDTO.getAccountUuid())) {
            throw new ServiceException(CodeStatus.PARAM_NOT_EMPTY.value(), "用户id不能为空");
        }
        if (StringUtils.isEmpty(punishLogDTO.getOperator())) {
            throw new ServiceException(CodeStatus.PARAM_NOT_EMPTY.value(), "操作人不能为空");
        }
        if (StringUtils.isEmpty(punishLogDTO.getBizId())) {
            throw new ServiceException(CodeStatus.PARAM_NOT_EMPTY.value(), "业务id不能为空");
        }

        log.info("soa请求doPunish,req={}", punishLogDTO);
        try {
            SoaResponse soaResponse = SOAUtil.create(PUNISH_ETCD_URL)
                    .call(SERVICE, "doPunish", punishLogDTO);
            log.info("soaResponse={}", JsonUtils.objectToString(soaResponse));
            JsonResult res = new JsonResult();
            res.setCode(soaResponse.getCode());
            res.setMsg(soaResponse.getMsg());
            res.setData(soaResponse.getData());
            return res;
        } catch (Exception e) {
            log.error("doPunish调用风控处罚接口失败，msg:{},req:{}",e.getMessage(), punishLogDTO);
            throw new ServiceException(CodeStatus.OPERATION_FAIL_ERROR.value(), "系统异常（f165）");
        }
    }

    /**
     * 组装dto参数
     * @param doPunishVO
     * @return
     */
    public PunishLogDTO fillPunishLogDTO(DoPunishVO doPunishVO) {
        PunishLogDTO dto = new PunishLogDTO();
        // 获取用户信息
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        // 获取公会id
        TeamHost teamHost = teamHostMapper.getOneByHostUuid(doPunishVO.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
        if (Objects.isNull(teamHost)) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "查无主播团队信息");
        }
        dto.setPunishId(doPunishVO.getPunishId());
        dto.setAccountUuid(doPunishVO.getHostUuid());
        dto.setAccountId(doPunishVO.getAccountId());
        //dto.setOperator(user.getUserName());
        // 此处风控要求固定参数
        dto.setOperator("liyue");
        dto.setBizId(doPunishVO.getHostUuid() + LocalDate.now());
        dto.setGuildId(teamHost.getOrgId().toString());
        dto.setGroupId(teamHost.getTeamId().toString());
        Long employeeId = Objects.isNull(teamHost.getEmployeeId()) ? 0L : teamHost.getEmployeeId();
        String sysAccountUuid = teamEmployeeMapper.accountUuidByEmployeeId(employeeId);
        dto.setAgent(sysAccountUuid);
        dto.setNickName(doPunishVO.getNickName());
        dto.setAppCode(APP_CODE);
        dto.setSceneCode(SCENE_CODE);

        return dto;
    }

    @Override
    public List<Object> getPunishTagConfig(PunishTagConfigDTO punishTagConfigDTO) {
        // 入参校验
        if (StringUtils.isEmpty(punishTagConfigDTO.getLoginName())) {
            throw new ServiceException(CodeStatus.USER_NOT_EXSISTS.value(), "用户名不能为空");
        }

        punishTagConfigDTO.setAppCode(APP_CODE);
        punishTagConfigDTO.setSceneCode(SCENE_CODE);
        log.info("soa请求getPunishTagConfig,req={}", punishTagConfigDTO);
        try {
            SoaResponse soaResponse = SOAUtil.create(PUNISH_ETCD_URL)
                    .call(SERVICE, "getPunishTagConfig", punishTagConfigDTO);
            log.info("soaResponse={}", JsonUtils.objectToString(soaResponse));
            return JsonUtils.stringToObject(soaResponse.getData(), new TypeReference<List<Object>>() {});
        } catch (Exception e) {
            log.error("getPunishTagConfig调用风控处罚理由列表接口失败，msg:{},req:{}",e.getMessage(), punishTagConfigDTO);
            throw new ServiceException(CodeStatus.OPERATION_FAIL_ERROR.value(), "系统异常（f218）");
        }
    }

    /**
     * 获取原因时长
     * @param punishHourDTO
     * @return
     */
    public Object getPunishTypeById(PunishHourDTO punishHourDTO) {
        //punishHourDTO.setAppCode(APP_CODE);
        Object[] obj = new Object[]{APP_CODE,punishHourDTO.getUuid(),punishHourDTO.getPunishId()};
        log.info("soa请求getPunishTypeById,req={}", obj);
        try {
            SoaResponse soaResponse = SOAUtil.create(PUNISH_ETCD_URL)
                    .call(SERVICE, "getPunishTypeById", obj);
            log.info("soaResponse={}", JsonUtils.objectToString(soaResponse));
            return JsonUtils.stringToObject(soaResponse.getData(), new TypeReference<Object>() {});
        } catch (Exception e) {
            log.error("getPunishTypeById调用风控理由时长接口失败，msg:{},req:{}",e.getMessage(), punishHourDTO);
            throw new ServiceException(CodeStatus.OPERATION_FAIL_ERROR.value(), "系统异常（f238）");
        }
    }

    @Override
    public PageInfo<PunishLogVO> riskPunishList(PunishLogDTO punishLogDTO) {
        // 获取用户信息
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        // 判断是否机构管理员
        //if (UserTypeEnum.MANAGER.getType().equals(user.getUserType())) {
        punishLogDTO.setGuildId(user.getOrgId().toString());
        //}
        // 判断是否团队负责人
        if (UserTypeEnum.LEADER.getType().equals(user.getUserType())) {
            // 查询用户团队id
            Long teamId = teamEmployeeMapper.teamIdByAccountUuid(user.getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (Objects.isNull(teamId)) {
                throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "查无所属团队信息");
            }
            punishLogDTO.setGroupId(teamId.toString());
        }
        // 判断经纪人范围
        if (UserTypeEnum.AGENTER.getType().equals(user.getUserType())) {
            punishLogDTO.setAgent(user.getAccountUuid());
        }

        punishLogDTO.setAppCode(APP_CODE);
        punishLogDTO.setSceneCode(LIST_CODE);
        log.info("soa请求[riskPunishList],req={}", punishLogDTO);
        try {
            SoaResponse soaResponse = SOAUtil.create(PUNISH_ETCD_URL)
                    .call(SERVICE, "pageQueryLogWithTotal", punishLogDTO);
            PageInfo<PunishLogVO> pageInfo = JsonUtils.stringToObject(soaResponse.getData(), new TypeReference<PageInfo<PunishLogVO>>() {});
            if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
                fillFieldList(pageInfo.getList());
            }

            return pageInfo;
        } catch (Exception e) {
            log.error("[riskPunishList]处理风控处罚列表接口失败，msg:{},req:{}",e.getMessage(), punishLogDTO);
            throw new ServiceException(CodeStatus.OPERATION_FAIL_ERROR.value(), "系统异常（f280)");
        }
    }

    @Override
    public LinkAccountRes linkAccountFromToken(String hostUuid) {
        String service = "accountProfile";
        String method = "linkAccountFromToken";
        try {
            SoaResponse soaResponse = SOAUtil.create(PUNISH_ETCD_URL)
                    .call(service, method, hostUuid);
            LinkAccountRes linkAccountRes = JsonUtils.stringToObject(soaResponse.getData(), new TypeReference<LinkAccountRes>() {});
            log.info("linkAccountFromToken,req:{},res:{}",hostUuid,linkAccountRes);
            return linkAccountRes;
        } catch (Exception e) {
            log.error("[linkAccountFromToken]接口异常，msg:{},req:{}",e.getMessage(), hostUuid);
            throw new ServiceException(CodeStatus.OPERATION_FAIL_ERROR.value(), "系统异常（f296)");
        }
    }

    /**
     * 填充字段
     * @param list
     */
    private void fillFieldList(List<PunishLogVO> list) {
        List<String> hostUuidList = list.stream().map(PunishLogVO::getAccountUuid).distinct().collect(Collectors.toList());
        List<LiveHostInfo> infoList = liveHostInfoMapper.selectByHostUuidList(hostUuidList);
        Map<String, LiveHostInfo> infoMap = infoList.stream().collect(Collectors.toMap(LiveHostInfo::getHostUuid, a -> a, (k1,k2)->k1));
        List<String> groupList = list.stream().map(PunishLogVO::getGroupId).distinct().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<String> employeeIdStrList = list.stream().map(PunishLogVO::getAgent).distinct().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, String> employeeMap = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(employeeIdStrList)) {
            List<SysUser> sysUserList = sysUserService.getUserListByAccountUuids(employeeIdStrList);
            if (CollectionUtils.isNotEmpty(sysUserList)) {
                employeeMap = sysUserList.stream().collect(Collectors.toMap(SysUser::getAccountUuid, SysUser::getUserName));
            }
        }
        Map<Long, Team> teamMap = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(groupList)) {
            List<Long> groupIdList = groupList.stream().mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            List<Team> teamList = teamMapper.teamListByTeamIds(groupIdList);
            teamMap = teamList.stream().collect(Collectors.toMap(Team::getTeamId, a->a,(k1,k2) -> k1));
        }

        for (PunishLogVO vo : list) {
            if (Objects.nonNull(infoMap.get(vo.getAccountUuid()))) {
                vo.setApplyLevel(infoMap.get(vo.getAccountUuid()).getApplyLevel());
            }
            if (StringUtils.isNotEmpty(vo.getGroupId()) && Objects.nonNull(teamMap.get(Long.parseLong(vo.getGroupId())))) {
                vo.setGroupName(teamMap.get(Long.parseLong(vo.getGroupId())).getTeamName());
            }
            List<String> basisList = new ArrayList<>();
            if (Objects.nonNull(vo.getBasis()) && JSONUtil.isJsonArray(vo.getBasis())) {
                basisList = JsonUtils.stringToObject(vo.getBasis(), new TypeReference<List<String>>() {});
            }
            if (vo.getBasisType().equals(IMG_TYPE)) {
                vo.setSystemScreen(CollectionUtils.isNotEmpty(basisList) ? basisList.get(0) : "");
            }
            // 由于风控把回放地址放在jsontype 所以需要特殊处理 可能存在多个 只取1个
            if (vo.getBasisType().equals(JSON_TYPE)) {
                String playbackUrl = "";
                if (Objects.nonNull(vo.getBasis())) {
                    Map<String, Object> basicMap = JsonUtils.stringToObject(vo.getBasis(), new TypeReference<Map<String, Object>>() {});
                    if (!MapUtils.isEmpty(basicMap)) {
                        List<String> videoArray = (List<String>) basicMap.get("videoArray");
                        if (CollectionUtils.isNotEmpty(videoArray)) {
                            playbackUrl = videoArray.get(0);
                        }
                    }
                }
                vo.setPlaybackUrl(playbackUrl);
            }
            if (vo.getBasisType().equals(VIDEO_TYPE)) {
                vo.setPlaybackUrl(CollectionUtils.isNotEmpty(basisList) ? basisList.get(0) : "");
            }
            if (vo.getBasisType().equals(TXT_TYPE)) {
                vo.setBasisTxt(vo.getBasis());
            }
            if (StringUtils.isNotEmpty(vo.getAgent())) {
                vo.setAgent(Optional.ofNullable(employeeMap.get(vo.getAgent())).orElse(""));
            }
            if (Objects.nonNull(vo.getPunishEndTime())) {
                // 格式化时间对象
                vo.setPunishEndDate(dateFormat.format(vo.getPunishEndTime() * 1000));
            }
        }
    }

}

