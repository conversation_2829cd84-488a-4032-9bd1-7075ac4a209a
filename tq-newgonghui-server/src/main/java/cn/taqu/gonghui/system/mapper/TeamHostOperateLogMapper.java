package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.TeamHostOperateLog;
import cn.taqu.gonghui.system.search.TeamHostOperateLogSearch;
import cn.taqu.gonghui.system.vo.TeamHostOperateLogVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TeamHostOperateLogMapper {
    int insert(TeamHostOperateLog record);

    List<TeamHostOperateLogVo> queryByCondition(TeamHostOperateLogSearch search);

    TeamHostOperateLog selectOneByUuidAndType(@Param("hostUuid") String hostUuid, @Param("type") Integer type, @Param("teamId") Long teamId);

}
