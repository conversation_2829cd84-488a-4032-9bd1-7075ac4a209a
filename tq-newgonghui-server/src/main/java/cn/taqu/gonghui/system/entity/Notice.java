package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class Notice {

    /**
     * 类型 1公告  2规范 3帮助中心
     */
    public final static Integer NOTICE_TYPE_ANNOUNCEMENT=1;
    public final static Integer NOTICE_TYPE_NORM=2;
    public final static Integer NOTICE_TYPE_HELP=3;
    /*
     * 流水号
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 标题
     */
    private String content;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 公告类型（1-公告，2-规范，3-帮助中心）
     */
    private Integer noticeType;

    /**
     * 业务类型（1-直播，2-趣聊，3-聊天室）
     */
    private Integer businessType;

    /**
     * 附件key
     */
    private String fileKey;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}
