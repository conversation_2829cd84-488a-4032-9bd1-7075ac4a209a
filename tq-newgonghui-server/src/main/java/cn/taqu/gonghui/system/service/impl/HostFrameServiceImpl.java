package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.soa.GonghuiService;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import cn.taqu.gonghui.system.search.HostFrameListSearch;
import cn.taqu.gonghui.system.service.HostFrameService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.vo.RoleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HostFrameServiceImpl implements HostFrameService {
    @Autowired
    private TokenService tokenService;
    @SoaReference(application = "liveV1",value = "liveV1")
    private GonghuiService gonghuiService;
    @Autowired
    private TeamEmployeeMapper teamEmployeeMapper;
    @Autowired
    private TeamEmployeeService teamEmployeeService;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserService sysUserService;



    @Override
    public List<Map<String, String>> findOnlineHostList() {
        List<Map<String, String>> onlineHoset = null;
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        RoleVo userRole = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        List<String> agengtUuids = new ArrayList<>();
        if( UserTypeEnum.MANAGER.getCode().equals(userRole.getRoleKey())){
            List<Long> orgUuidNew = teamMapper.selectTeamListIds(loginUser.getUser().getOrgId(),TeamTypeEnum.LIVE_TEAM.getValue());
            onlineHoset = gonghuiService.getAllConsortiaHost(orgUuidNew,null);
        }else if (UserTypeEnum.LEADER.getCode().equals(userRole.getRoleKey())){
            // 根据传来的负责人查找相对于的团队，获取团队下的所有经纪人
            List<Long> orgTeamId = new ArrayList<>();
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(loginUser.getUser().getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if(teamEmployee == null){
                log.warn("当前用户:{}不是团队成员",loginUser.getUser().getUserId());
                throw new ServiceException("query_error","查询异常，请联系管理端");
            }
            orgTeamId.add(teamEmployee.getTeamId());

            List<TeamEmployee> agenterTree = teamEmployeeMapper.getAgenterTree(teamEmployee.getTeamId());
           if(CollectionUtils.isNotEmpty(agenterTree)){
               for (TeamEmployee employee : agenterTree) {
                   SysUser sysUser = sysUserMapper.selectUserByMobile(employee.getMobile(), EncryptSwitchConfig.selectByDigest);
                   if(sysUser != null){
                       agengtUuids.add(sysUser.getAccountUuid());
                   }
               }
           }
            onlineHoset = gonghuiService.getAllConsortiaHost(orgTeamId,agengtUuids);
        }else {
            List<Long> orgTeamId = new ArrayList<>();
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(loginUser.getUser().getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            orgTeamId.add(teamEmployee.getTeamId());
            if(StringUtils.isNotEmpty(loginUser.getUser().getAccountUuid()) && loginUser.getUser() != null){
                agengtUuids.add(loginUser.getUser().getAccountUuid());
            }
            onlineHoset = gonghuiService.getAllConsortiaHost(orgTeamId,agengtUuids);
        }
        return onlineHoset;
    }

    @Override
    public List<Map<String, String>> findOnlineFrameList(HostFrameListSearch search) {
        List<Map<String, String>> resultMapList = null;
        //根据入参设定查询条件
        HostFrameListSearch uuidSearch = getSearch(search);
        resultMapList = gonghuiService.getFrameList(uuidSearch.getTeamIds(),uuidSearch.getAgentUuids(), search.getPage(), search.getRows(), uuidSearch.getHostUuids(), search.getApplyLevel());

        if (CollectionUtils.isNotEmpty(resultMapList)){
            resultMapList.forEach(rs->{
               String hostuuidStr=  rs.get("host_uuid");
               TeamHost teamHost = teamHostMapper.getOneByHostUuid(hostuuidStr,TeamTypeEnum.LIVE_TEAM.getValue());
                TeamEmployee teamEmployee = new TeamEmployee();
               if(teamHost != null){
                   teamEmployee = teamEmployeeMapper.selectById(teamHost.getEmployeeId());
                }
                rs.put("businessman_name","");
               if(teamEmployee != null){
                   rs.put("businessman_name",teamEmployee.getEmployeeName());
               }
            });
        }
        return resultMapList;
    }

    /**
     * 返回参数搜索的uuid
     */
    private HostFrameListSearch getSearch(HostFrameListSearch teamHostSearch){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        RoleVo userRole = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        HostFrameListSearch uuidSearch = new HostFrameListSearch();
        List<Long> teamIds = new ArrayList<>();
        List<String> agentUuids = new ArrayList<>();
        List<String> hostUuid = new ArrayList<>();

        if(UserTypeEnum.AGENTER.getCode().equals(userRole.getRoleKey())){
            List<Team> teams = teamMapper.getTeamTreeByRole(loginUser.getUser().getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), loginUser.getUser().getOrgId());
             teamIds = teams.stream().map(Team::getTeamId).collect(Collectors.toList());
            uuidSearch.setTeamIds(teamIds);
            agentUuids.add(loginUser.getUser().getAccountUuid());
            uuidSearch.setAgentUuids(agentUuids);
        }
        if( UserTypeEnum.LEADER.getCode().equals(userRole.getRoleKey())){
            List<Team> teams = teamMapper.getTeamTreeByRole(loginUser.getUser().getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), loginUser.getUser().getOrgId());
            teamIds = teams.stream().map(Team::getTeamId).collect(Collectors.toList());
            uuidSearch.setTeamIds(teamIds);
        }
        if(UserTypeEnum.MANAGER.getCode().equals(userRole.getRoleKey())){
            if(loginUser.getUser().getOrgId() != null){
                List<Long> teams = teamMapper.selectTeamListIds(loginUser.getUser().getOrgId(),TeamTypeEnum.LIVE_TEAM.getValue());
                if(teams != null){
                    uuidSearch.setTeamIds(teams);
                }
            }
        }

        //如果传入值有根据经纪人传入，则根据经纪人查询显示结果
        if(teamHostSearch.getAgentId() != null){
            agentUuids.clear();
            hostUuid.clear();
            TeamEmployee teamEmployee = teamEmployeeMapper.selectById(teamHostSearch.getAgentId());
            if(teamEmployee != null && teamEmployee.getUserId() != null){
                SysUser sysUser = sysUserService.selectUserByUserId(teamEmployee.getUserId());
                if(sysUser != null && StringUtils.isNotEmpty(sysUser.getAccountUuid())){
                    agentUuids.add(sysUser.getAccountUuid());
                }
            }
            uuidSearch.setAgentUuids(agentUuids);
        }
        //如果传入有主播uuid，则根据主播uuid查询
        if(StringUtils.isNotEmpty(teamHostSearch.getHostUuid())){
            hostUuid.clear();
            TeamHost teamHost = teamHostMapper.getOneByHostUuid(teamHostSearch.getHostUuid(),TeamTypeEnum.LIVE_TEAM.getValue());
            if(teamHost != null){
                if( UserTypeEnum.AGENTER.getCode().equals(userRole.getRoleKey())){
                    Long employeeId = teamEmployeeMapper.seletEmployeeId(loginUser.getUser().getAccountUuid(),TeamTypeEnum.LIVE_TEAM.getValue());
                    if(teamHost.getEmployeeId() != null && teamHost.getEmployeeId().equals(employeeId)){
                        hostUuid.add(teamHostSearch.getHostUuid());
                    }else {
                        hostUuid.add("host is null");
                    }
                }
                if(UserTypeEnum.LEADER.getCode().equals(userRole.getRoleKey())){
                    TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(loginUser.getUser().getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
                    if(teamHost.getTeamId() != null && teamEmployee.getTeamId().equals(teamHost.getTeamId())){
                        hostUuid.add(teamHostSearch.getHostUuid());
                    }else {
                        hostUuid.add("host is null");
                    }
                }
                if( UserTypeEnum.MANAGER.getCode().equals(userRole.getRoleKey())){
                    if(teamHost.getOrgId() != null && loginUser.getUser().getOrgId().equals(teamHost.getOrgId())){
                        hostUuid.add(teamHostSearch.getHostUuid());
                    }else {
                        hostUuid.add("host is null");
                    }
                }
            }else {
                hostUuid.add("host is null");
            }
            uuidSearch.setHostUuids(hostUuid);
        }
        return uuidSearch;
    }




}
