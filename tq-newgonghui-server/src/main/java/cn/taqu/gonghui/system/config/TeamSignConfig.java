package cn.taqu.gonghui.system.config;

import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TeamSignConfig {

    private static Logger LOGGER = LoggerFactory.getLogger(TeamSignConfig.class);

    // 直播团队标识符
    private static List<String> liveTeamConfig = null;
    // 趣聊团队标识符
    private static List<String> funChatTeamConfig = null;
    // 聊天室团队标识符
    private static List<String> chatRoomTeamConfig = null;

    @EtcdValue("biz.team.sign")
    public static void initTeamConfig(String json) {
        liveTeamConfig = new ArrayList<>();
        funChatTeamConfig = new ArrayList<>();
        chatRoomTeamConfig = new ArrayList<>();
        if (StringUtils.isBlank(json)) {
            LOGGER.warn("解析团队标识符配置失败，配置数据为空");
            return;
        }
        if (StringUtils.isNotBlank(json) && json.startsWith("\\")) {
            json = StringUtils.removeStart(json, "\\");
        }
        try {

            JsonNode jsonNode = JsonUtils.reader().readTree(json);
            if (jsonNode == null) {
                LOGGER.warn("解析团队标识符配置失败，json对象为空，失败数据：{}", json);
                return;
            }

            initLiveTeamConfig(jsonNode);
            initFunChatTeamConfig(jsonNode);
            initChatRoomTeamConfig(jsonNode);
        } catch (IOException e) {
            LOGGER.error("解析团队标识符配置失败，失败数据：{}，失败原因：{}", json, e);
        }
    }

    /**
     * 解析直播团队标识符
     *
     * @param jsonNode
     */
    private static void initLiveTeamConfig(JsonNode jsonNode) {
        JsonNode liveTeamConfigNode = jsonNode.get("liveTeamConfig");
        if (liveTeamConfigNode == null) {
            LOGGER.warn("解析直播团队标识符配置失败，liveTeamConfigNode为空");
            return;
        }
        if (liveTeamConfigNode.size() <= 0) {
            LOGGER.warn("解析直播团队标识符配置失败，liveTeamConfigNode为[]");
            return;
        }
        if (!liveTeamConfigNode.isArray()) {
            LOGGER.warn("直播团队标识符配置格式错误");
            return;
        }
        for (JsonNode jsonItem : liveTeamConfigNode) {
            if (!liveTeamConfig.contains(jsonItem.asText())) {
                liveTeamConfig.add(jsonItem.asText());
            }
        }
    }

    /**
     * 解析趣聊团队标识符
     *
     * @param jsonNode
     */
    private static void initFunChatTeamConfig(JsonNode jsonNode) {
        JsonNode funChatTeamConfigNode = jsonNode.get("funChatTeamConfig");
        if (funChatTeamConfigNode == null) {
            LOGGER.warn("解析趣聊团队标识符配置失败，funChatTeamConfigNode为空");
            return;
        }
        if (funChatTeamConfigNode.size() <= 0) {
            LOGGER.warn("解析趣聊团队标识符配置失败，funChatTeamConfigNode为[]");
            return;
        }
        if (!funChatTeamConfigNode.isArray()) {
            LOGGER.warn("趣聊团队标识符配置格式错误");
            return;
        }
        for (JsonNode jsonItem : funChatTeamConfigNode) {
            if (!funChatTeamConfig.contains(jsonItem.asText())) {
                funChatTeamConfig.add(jsonItem.asText());
            }
        }
    }

    /**
     * 解析聊天室团队标识符
     *
     * @param jsonNode
     */
    private static void initChatRoomTeamConfig(JsonNode jsonNode) {
        JsonNode chatRoomTeamConfigNode = jsonNode.get("chatRoomTeamConfig");
        if (chatRoomTeamConfigNode == null) {
            LOGGER.warn("解析聊天室团队标识符配置失败，chatRoomTeamConfigNode为空");
            return;
        }
        if (chatRoomTeamConfigNode.size() <= 0) {
            LOGGER.warn("解析聊天室团队标识符配置失败，chatRoomTeamConfigNode为[]");
            return;
        }
        if (!chatRoomTeamConfigNode.isArray()) {
            LOGGER.warn("聊天室团队标识符配置格式错误");
            return;
        }
        for (JsonNode jsonItem : chatRoomTeamConfigNode) {
            if (!chatRoomTeamConfig.contains(jsonItem.asText())) {
                chatRoomTeamConfig.add(jsonItem.asText());
            }
        }
    }

    /**
     * 获取团队标识符
     *
     * @return
     */
    public List<Map<String, Object>> getTeamSign() {
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> liveMap = new HashMap<>();
        Map<String, Object> funChatMap = new HashMap<>();
        Map<String, Object> chatRoomMap = new HashMap<>();
        // 直播团队标识符
        liveMap.put("label", "直播");
        liveMap.put("value", 1);
        liveMap.put("children", liveTeamConfig);
        // 趣聊团队标识符
        funChatMap.put("label", "趣聊");
        funChatMap.put("value", 2);
        funChatMap.put("children", funChatTeamConfig);
        // 聊天室团队标识符
        chatRoomMap.put("label", "聊天室");
        chatRoomMap.put("value", 3);
        chatRoomMap.put("children", chatRoomTeamConfig);
        list.add(liveMap);
        list.add(funChatMap);
        list.add(chatRoomMap);
        return list;
    }

    public String getTeamSignByType(Integer type) {
        if (type == TeamTypeEnum.LIVE_TEAM.getValue()) {
            if (CollectionUtils.isEmpty(liveTeamConfig)) {
                return "";
            } else {
                return liveTeamConfig.iterator().next();
            }
        } else if (type == TeamTypeEnum.CALL_TEAM.getValue()) {
            if (CollectionUtils.isEmpty(funChatTeamConfig)) {
                return "";
            } else {
                return funChatTeamConfig.iterator().next();
            }
        } else {
            if (CollectionUtils.isEmpty(chatRoomTeamConfig)) {
                return "";
            } else {
                return chatRoomTeamConfig.iterator().next();
            }
        }
    }
}
