package cn.taqu.gonghui.system.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class HostAmountTaskList implements Serializable {

    @JsonProperty("list")
    private List<ListDTO> list;
    @JsonProperty("reach_host_num")
    private String reachHostNum;
    @JsonProperty("new_host_num")
    private String newHostNum;
    @JsonProperty("new_reach_host_num")
    private String newReachHostNum;
    @JsonProperty("total")
    private String total;

    @NoArgsConstructor
    @Data
    public static class ListDTO {
        @JsonProperty("month")
        private String month;
        @JsonProperty("nickname")
        private String nickname;
        @JsonProperty("avatar")
        private String avatar;
        @JsonProperty("host_create_time")
        private String hostCreateTime;
        @JsonProperty("sex_type")
        private String sexType;
        @JsonProperty("live_no")
        private String liveNo;
        @JsonProperty("host_uuid")
        private String hostUuid;
        @JsonProperty("apply_level")
        private String applyLevel;
        @JsonProperty("real_name")
        private String realName;
        @JsonProperty("total_duration")
        private String totalDuration;
        @JsonProperty("valid_live")
        private String validLive;
        @JsonProperty("total_amount")
        private String totalAmount;
        @JsonProperty("panel_amount")
        private String panelAmount;
        @JsonProperty("game_amount")
        private String gameAmount;
        @JsonProperty("system_activity_amount")
        private String systemActivityAmount;
    }
}
