package cn.taqu.gonghui.system.search;

import cn.taqu.core.orm.PageSearch;
import lombok.Data;

import java.util.List;

@Data
public class HostStatisticSearch extends PageSearch {


    //公用
    private String order_type; //排序类型 1趣豆 2有效天数 3加入机构时间
    private String sort_type; //排序方式 1倒序 2正序
    private String consortiaId; //团队uuid
    private List<Long> consortiaIdList; // 团队uuid数组
    private String live_no; // 他趣id(直播号)
    private Integer page;
    private Integer pageSize;

    private String start; // 添加公会开始时间
    private String end; // 添加公会结束时间
    private String type;
    private String host_uuid;
    private String host_status;
    private String apply_level; // 评级
    private String live_status; // 直播状态
    private String agentManageUuid; // 经纪人id
    private String date; //月份 格式 202103
    private Integer export; // 是否下载表单
    private List<String> hostUuidList; // uuid数组
    private Long orgId;
    private List<Long> agentId;
    private String phone;
    private List<String> businessmanUuids; //经纪人uuid数组
    private Integer hostType;// 主播类型
    /**
     * 所属经纪人
     */
    private Long agentIdForAdmin;
    private Long employeeId;
    private String orgUuid;  //机构uuid
    private Long teamId;
    private Integer businessType;
    private List<Long> teamIds;

    private Integer isUpdate; // 是否开启电子分润（1-是，0-否）

    private String createTimeStart; // 注册开始时间
    private String createTimeEnd; // 注册结束时间
}
