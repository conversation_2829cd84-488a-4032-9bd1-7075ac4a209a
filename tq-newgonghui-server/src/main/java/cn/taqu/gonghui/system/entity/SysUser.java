package cn.taqu.gonghui.system.entity;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @Date 2021/4/25
 */
@Data
@TableName(value = "sys_user", autoResultMap = true)
public class SysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @TableId(value = "user_id",type = IdType.AUTO)
    private Long userId;

    /** 登录账号 */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String userName;

    /**
     * 登录账号 密文
     */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String userNameCipher;

    /** 自定义uuid，跟他趣uuid无关 */
    private String accountUuid;

    private Long orgId;

    private String orgName;

    /**
     * 手机号码
     * */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String mobile;

    /**
     * 手机号码 密文
     * */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String mobileCipher;

    /**
     * 手机号码-摘要
     */
    @TableField(typeHandler = Sm3EncryptTypeHandler.class)
    private String mobileDigest;

    /** 账号类型（1 管理员 2 负责人 3 经纪人 ） */
    private Integer userType;


    /** 帐号状态（0正常 1停用） */
    private String status;

    private Long createTime;

    private Long updateTime;

    public String getMobile() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.mobileCipher;
        }
        if(StringUtils.isBlank(this.mobile) && StringUtils.isNotBlank(this.mobileCipher)){
            return this.mobileCipher;
        }
        return this.mobile;
    }

    public String getMobileDigest() {
        return getMobile();
    }

    public String getUserName() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.userNameCipher;
        }
        if(StringUtils.isBlank(this.userName) && StringUtils.isNotBlank(this.userNameCipher)){
            return this.userNameCipher;
        }
        return this.userName;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
        this.mobileCipher = mobile;
        this.mobileDigest = mobile;
    }

    public void setUserName(String userName) {
        this.userName = userName;
        this.userNameCipher = userName;
    }
}
