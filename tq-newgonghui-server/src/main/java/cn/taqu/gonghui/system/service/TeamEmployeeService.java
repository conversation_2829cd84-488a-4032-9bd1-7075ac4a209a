package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.system.search.TeamEmployeeSearch;
import cn.taqu.gonghui.system.vo.TeamEmployeeVo;
import cn.taqu.gonghui.system.vo.TeamEmyloyeeAndRoleVo;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
public interface TeamEmployeeService extends IService<TeamEmployee> {


    void addTeamEmployee(String mobile,String employeeName, Long teamId, Long userId,Integer businessType,Integer type);


    IPage<TeamEmployeeVo> selectTeamEmployeePage(TeamEmployeeSearch search, int pageNo, int pageSize);

    IPage<TeamEmployeeVo> selectTeamEmployeePageForUser(TeamEmployeeSearch search, int pageNo, int pageSize);


    void saveMember(String mobile,String verify,String userName,Long teamId,Long roleId,Integer businessType);

    void updateName(Long id,String name);

    TeamEmployee getOne(Long empoyeeId);

    List<TeamEmployee> getOneByUserId(Long userId);

    TeamEmployee getOneByUserIdAndType(Long userId,Integer businessType);

    void employeeDeparture(String userId);

    List<CommonSelect> getAgenterTree(Long teamId);

    List<CommonSelect> getAgenterTreeByAccountUuid(Integer type);

    List<Map<String,String>> getAgenterForLogin(Integer type);

    void agentDeparture(Long employeeId);

    List<CommonSelect> selectAgentList(Integer type);

    List<CommonSelect> selectAgentListByOrgId(Long orgId,Integer type);


    void deleteByUserId(Long userId);

    TeamEmyloyeeAndRoleVo getMemberById(Long employeeId);


    Map<Long, String> mapEmployeeName(List<Long> employeeIdList);
}
