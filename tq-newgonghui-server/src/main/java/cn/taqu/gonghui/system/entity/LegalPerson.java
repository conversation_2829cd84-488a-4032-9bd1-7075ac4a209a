package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class LegalPerson {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String orgUuid;

    private String url;

    private Long createTime;

    private Long updateTime;

    private Integer type;

}