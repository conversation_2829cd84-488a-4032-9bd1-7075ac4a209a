package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class SysSharingProfit {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 分润比例（0 ~ 99），整数
     */
    private String percentNumber;

    /**
     * 状态（1-启用，0-禁用）
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Long createTime;
}
