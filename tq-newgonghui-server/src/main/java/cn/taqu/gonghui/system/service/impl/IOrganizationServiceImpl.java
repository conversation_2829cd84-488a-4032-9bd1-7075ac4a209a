package cn.taqu.gonghui.system.service.impl;

import cn.taqu.gonghui.system.entity.OrgLegalPersonPhone;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.service.IOrgnizatonService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/3 16 33
 * discription
 */
@Service
public class IOrganizationServiceImpl extends ServiceImpl<OrganizationMapper, Organization> implements IOrgnizatonService {


    @Override
    public Map<Long, String> MapOrgName(List<Long> orgIdList) {

        if(CollectionUtils.isEmpty(orgIdList)){
            return new HashMap<>();
        }
        LambdaQueryWrapper<Organization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Organization::getOrgId, Organization::getOrgName)
                .in(Organization::getOrgId, orgIdList);
        List<Organization> organizationList = this.baseMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(organizationList)){
            return new HashMap<>();
        }
        Map<Long, String> orgNameMap = organizationList.stream().collect(Collectors.toMap(l -> l.getOrgId(), l -> l.getOrgName(), (key1, key2) -> key1));
        return orgNameMap;
    }

    @Override
    public List<OrgLegalPersonPhone> selectLegalPersonPhoneByRange(Long curStartId, Long curEndId) {
        return baseMapper.selectLegalPersonPhoneByRange(curStartId,curEndId);
    }

    @Override
    public void batchUpdateLegalPersonPhoneCipher(List<OrgLegalPersonPhone> list) {
        this.baseMapper.batchUpdateLegalPersonPhoneCipher(list);
    }

    @Override
    public void updateLegalPersonPhoneCipher(Long orgId, String legalPersonPhoneCipher) {
        this.baseMapper.updateLegalPersonPhoneCipher(orgId, legalPersonPhoneCipher);
    }
}
