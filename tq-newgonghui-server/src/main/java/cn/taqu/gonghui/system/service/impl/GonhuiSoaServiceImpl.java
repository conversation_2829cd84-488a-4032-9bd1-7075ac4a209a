package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.gonghui.soa.GonghuiSoaService;
import cn.taqu.gonghui.soa.SOAUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 协议管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class GonhuiSoaServiceImpl implements GonghuiSoaService {
    private static final String CHATROOM_URL = "/soa/application/live/api";
    private static final String service = "Gonghui";

    @Override
    public JSONObject getStatData(List<Long> consortiaIdList, String businessmanUuid) {
        Object[] data = {consortiaIdList, businessmanUuid};
        log.info("soa请求getStatData,data={}",JSON.toJSONString(data));
        SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                .call(service, "getStatData", data);
        log.info("soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();
        JSONObject jsonObject = JSONObject.parseObject(resData);
        return jsonObject;
    }

    @Override
    public JSONObject getLineChartData(String type, List<Long> consortiaIdList, String businessmanUuid) {
        Object[] data = {type, consortiaIdList, businessmanUuid};

        log.info("soa请求getLineChartData,data={}",JSON.toJSONString(data));
        SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                .call(service, "getLineChartData", data);
        log.info("soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();
        JSONObject jsonObject = JSONObject.parseObject(resData);
        return jsonObject;
    }

    @Override
    public JSONObject getDayHostRank(List<Long> consortiaIdList, Integer page, Integer limit,String host_uuid) {
        Object[] data = {consortiaIdList, page, limit,host_uuid};
        log.info("soa请求getDayHostRank,data={}",JSON.toJSONString(data));

        SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                .call(service, "getDayHostRank", data);
        log.info("soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();
        JSONObject jsonObject = JSONObject.parseObject(resData);
        return jsonObject;
    }

    @Override
    public JSONObject getFrameDetail(String host_uuid, String consortia_id) {
        Object[] data = {host_uuid, consortia_id};
        log.info("soa请求getFrameDetail,data={}",JSON.toJSONString(data));

        SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                .call(service, "getFrameDetail", data);
        log.info("soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();
        JSONObject jsonObject = JSONObject.parseObject(resData);
        return jsonObject;
    }

    @Override
    public JSONObject getFrameListExtraInfo(List<Long> consortia_ids, String businessman_uuid, Integer page, Integer pageSize, String host_uuid, String apply_level) {
        Object[] data = {consortia_ids, businessman_uuid, page, pageSize, host_uuid, apply_level};
        log.info("soa请求getFrameListExtraInfo,data={}",JSON.toJSONString(data));

        SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                .call(service, "getFrameListExtraInfo", data);
        log.info("soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();
        JSONObject jsonObject = JSONObject.parseObject(resData);
        return jsonObject;
    }

    @Override
    public JSONObject getHostAmountTaskList(Integer page, Integer stat_time, List<Long> consortia_id, String businessman_uuid,String host_uuid, String is_new_host, Integer is_export) {
        Object[] data = {page, stat_time, consortia_id, businessman_uuid,host_uuid, is_new_host, is_export};
        log.info("soa请求getHostAmountTaskList,data={}",JSON.toJSONString(data));

        SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                .call(service, "getHostAmountTaskList", data);
        log.info("soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();
        JSONObject jsonObject = JSONObject.parseObject(resData);
        return jsonObject;
    }
}
