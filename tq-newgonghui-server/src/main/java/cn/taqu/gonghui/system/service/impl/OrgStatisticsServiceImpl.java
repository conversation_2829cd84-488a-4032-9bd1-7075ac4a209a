package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.domain.CommonPage;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ReportUtils;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LiveHostStatisticVo;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.live.entity.LiveOrgStatisticsMonth;
import cn.taqu.gonghui.live.mapper.LiveConsortiaStatisticsDayMapper;
import cn.taqu.gonghui.live.mapper.LiveOrgStatisticsMonthMapper;
import cn.taqu.gonghui.live.mapper.LiveTeamStatisticsDayMapper;
import cn.taqu.gonghui.live.search.OrgStatisticsSearch;
import cn.taqu.gonghui.soa.GonghuiService;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import cn.taqu.gonghui.system.search.HostStatisticSearch;
import cn.taqu.gonghui.system.search.LivePerDayStatisticSearch;
import cn.taqu.gonghui.system.service.HostService;
import cn.taqu.gonghui.system.service.OrgStatisticsService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.vo.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrgStatisticsServiceImpl implements OrgStatisticsService {

    private Logger logger = LoggerFactory.getLogger(OrgStatisticsServiceImpl.class);

    @Autowired
    private HostService hostService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysUserService userService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @SoaReference(application = "liveV1",value = "liveV1")
    private GonghuiService gonghuiService;
    @Autowired
    private TeamEmployeeMapper teamEmployeeMapper;
    @Autowired
    private TeamEmployeeService teamEmployeeService;
    @Autowired
    private LiveConsortiaStatisticsDayMapper liveConsortiaStatisticsDayMapper;
    @Autowired
    private LiveOrgStatisticsMonthMapper liveOrgStatisticsMonthMapper;

    @Autowired
    private TeamMapper teamMapper;


    /**
     * 获取新增主播数据（用户端调用）
     * @param employeeId
     * @param date
     * @param orderType
     * @param sortType
     * @param export
     * @param teamId
     * @param applyLevel
     * @param liveNo
     * @return
     */
    @Override
    public LiveHostStatisticNewVo getLiveHostStatisticNew(String employeeId, String date, String orderType, String sortType, Integer export,
                                                          String teamId, String applyLevel, String liveNo,Integer page,Integer pageSize,Integer type) {
        HostStatisticSearch search = new HostStatisticSearch();
        // 设置机构id
        search.setOrgId(getOrgIdByAccountUuid());
        search.setApply_level(applyLevel);
        search.setLive_no(liveNo);
        search.setDate(date);
        search.setExport(export);
        search.setOrder_type(orderType);
        search.setSort_type(sortType);
        search.setPage(page);
        search.setPageSize(pageSize);
        search.setBusinessType(type);

        // 判断当前用户角色，根据不同角色查询不同角色所属hostUuid
        RoleVo currentRole = userService.getCurrentRole(type);
        // 机构管理员
        if (currentRole.getRoleKey().equals(UserTypeEnum.MANAGER.getCode())){
            // 设置团队id 数组
            search.setConsortiaIdList(teamIdsByOrgId(type));
            setManagerStatisticSearch(teamId,employeeId,search);
        // 负责人
        } else if (currentRole.getRoleKey().equals(UserTypeEnum.LEADER.getCode())) {
            // 设置团队id数组
            search.setConsortiaIdList(teamIdByAccountUuid(TeamTypeEnum.LIVE_TEAM.getValue()));
            setLeaderStatisticsSearch(employeeId,search);
        // 经纪人
        } else if (currentRole.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())) {
            // 设置经纪人所属团队
            search.setConsortiaIdList(teamIdByAccountUuid(TeamTypeEnum.LIVE_TEAM.getValue()));
            setAgenterStatisticsSearch(search);
        }
        logger.info("新增主播列表参数：{}",JsonUtils.objectToString(search));
        LiveHostStatisticNewVo vo = hostService.getConsortiaNewHostStat(search);
        // 设置主播头像和昵称和经纪人名称
        if (null != vo) {
            if (CollectionUtils.isEmpty(vo.getList())) {
                vo.setList(new ArrayList<>());
            }
            logger.info("新增主播列表查询接口返回源数据：{}",JsonUtils.objectToString(vo.getList()));
            // 如果输入了他趣id参数，那么判断改主播是否在当前账号下
            if (CollectionUtils.isNotEmpty(vo.getList())) {
                if (StringUtils.isNotBlank(search.getLive_no())) {
                    if (CollectionUtils.isNotEmpty(search.getHostUuidList()) && !"no_host_uuid".equals(search.getHostUuidList().get(0))) {
                        if (!search.getHostUuidList().containsAll(vo.getList().stream().map(LiveHostVo::getHost_uuid).collect(Collectors.toList()))) {
                            vo.setList(null);
                        }
                    }
                }
            }

            processHostNickNameAndAvatar(vo.getList());
            processHostOrgName(vo.getList());
            // 设置经纪人名称
            processAgent(vo.getList());
            // 设置团队名称
            processTeamName(vo.getList());

            // 如果是经纪人角色，统计数据给 0
            if (currentRole.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())) {
                List<LiveHostStatisticSumVo> stat_data = vo.getStat_data();
                if (CollectionUtils.isNotEmpty(stat_data)) {
                    for (LiveHostStatisticSumVo item : stat_data) {
                         item.setNum("-1");
                    }
                }
            }
            vo.setRoleKey(currentRole.getRoleKey());
        } else {
            vo = new LiveHostStatisticNewVo();
        }
        return vo;
    }

    /**
     * 获取新增主播数据(管理端调用)
     * @param employeeId
     * @param date
     * @param orderType
     * @param sortType
     * @param export
     * @param teamId
     * @param applyLevel
     * @param liveNo
     * @return
     */
    @Override
    public Map<String,Object> getLiveHostStatisticNewForAdmin(String orgId,String employeeId, String date, String orderType, String sortType, Integer export,
                                                          String teamId, String applyLevel, String liveNo,Integer page,Integer pageSize) {
        HostStatisticSearch search = new HostStatisticSearch();
        // 设置机构uuid
        search.setConsortiaId(getOrgUuidByOrgId(orgId));
        search.setOrgId(Long.valueOf(orgId));
        search.setApply_level(applyLevel);
        search.setLive_no(liveNo);
        search.setDate(date);
        search.setExport(export);
        search.setOrder_type(orderType);
        search.setSort_type(sortType);
        search.setPage(page);
        search.setPageSize(pageSize);

        setManagerStatisticSearch(teamId,employeeId,search);

        LiveHostStatisticNewVo vo = hostService.getConsortiaNewHostStat(search);
        // 设置主播头像和昵称
        processHostNickNameAndAvatar(vo.getList());
        // 设置机构名称
        processHostOrgName(vo.getList());
        Map<String,Object> map = new HashMap<>();
        map.put("list",vo.getList().stream().map(ManageLiveHostVo::new).collect(Collectors.toList()));
        map.put("stat_data",vo.getStat_data());
        return map;
    }

    /**
     * 管理端 - 数据统计 - 机构团队数据
     * @return
     */
    @Override
    public DailyVo getTeamDailyStatistics(OrgStatisticsSearch search) {
        checkTeamDailyParams(search);
        List<DailyStatisticsVo> resultList =  getOriginData(search);

        // 这里对源数据进行处理，不存在的日期给0
        Date startDate = DateUtil.StringToDate(String.valueOf(search.getStartTime()), "yyyyMMdd");
        Date endDate = DateUtil.StringToDate(String.valueOf(search.getEndTime()), "yyyyMMdd");
        List<String> dateList = ReportUtils.getBetweenDates(startDate, endDate);
        List<DailyStatisticsVo> returnList  = new ArrayList<>();
        Map<String, DailyStatisticsVo> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(resultList)) {
            for (DailyStatisticsVo item : resultList) {
                map.put(String.valueOf(item.getDayTime()),item);
            }
        }
        for (String item : dateList) {
            if (null != map.get(item)) {
                returnList.add(map.get(item));
            } else {
                DailyStatisticsVo vo = new DailyStatisticsVo();
                vo.setAmount(0L);
                vo.setDayTime(Long.valueOf(item));
                vo.setFans(0L);
                vo.setFlower(0L);
                vo.setHostNum(0L);
                vo.setTotalLiveTime(0L);
                vo.setTotalTimeStr("0");
                vo.setMessage(0L);
                vo.setSend(0L);
                vo.setViewer(0L);
                vo.setGameAmount(0);
                vo.setGameRatio("0%");
                vo.setSystemActivityAmount(0);
                returnList.add(vo);
            }
        }

        DailyVo dailyVo = new DailyVo();
        if (CollectionUtils.isNotEmpty(returnList)) {
            dailyVo.setTotal(Long.valueOf(returnList.size()));
        } else {
            dailyVo.setTotal(0L);
        }
        dailyVo.setList(returnList);
        return dailyVo;
    }


    private List<DailyStatisticsVo> getOriginData(OrgStatisticsSearch search){
        List<DailyStatisticsVo> resultList = null;
        // 如果团队id不为空，那么查询团队维度统计天表
        if (null != search.getTeamId()) {
            List<Long> consortiaIdList = new ArrayList<>();
            consortiaIdList.add(search.getTeamId());
            search.setConsortiaIdList(consortiaIdList);
            resultList = liveConsortiaStatisticsDayMapper.queryByCondition(search);
        }
        // 如果团队id为空，那么查询该机构下所有团队维度统计天表聚合
        else {
            Long orgId = search.getOrgId();
            List<Team> teams = teamMapper.selectTeamList(orgId, TeamTypeEnum.LIVE_TEAM.getValue());
            if (CollectionUtils.isNotEmpty(teams)) {
                List<Long> teamIdList = teams.stream().map(Team::getTeamId).collect(Collectors.toList());
                search.setConsortiaIdList(teamIdList);
                resultList = liveConsortiaStatisticsDayMapper.queryByCondition2(search);
            }
        }
        if(resultList != null){
            resultList.forEach(rs->{
                rs.setTotalTimeStr(secondToTime(rs.getTotalLiveTime()));
            });
        }
        return resultList;
    }



    @Override
    public  List<DailyStatisticsVo> getOrgDailyStatistics(LivePerDayStatisticSearch search) {
        List<DailyStatisticsVo> dailyStatisticsVos = null;
        List<Long> teamIds = teamMapper.selectTeamListIds(search.getOrgId(),TeamTypeEnum.LIVE_TEAM.getValue());
        OrgStatisticsSearch searchHost = new OrgStatisticsSearch();
        searchHost.setPage(search.getPage());
        searchHost.setPageSize(search.getPageSize());
        searchHost.setStartTime(Long.valueOf(search.getStart()));
        searchHost.setEndTime(Long.valueOf(search.getEnd()));
        if (CollectionUtils.isNotEmpty(teamIds)) {
            searchHost.setConsortiaIdList(teamIds);
            dailyStatisticsVos  = liveConsortiaStatisticsDayMapper.queryByCondition(searchHost);
            if(CollectionUtils.isNotEmpty(dailyStatisticsVos)){
                dailyStatisticsVos.forEach(ds->{
                    ds.setTotalTimeStr(secondToTime(ds.getTotalLiveTime()));
                    if(ds.getConsortiaId() != null){
                        Team team = teamMapper.selectById(ds.getConsortiaId());
                        if(team != null){
                            ds.setTeamName(team.getTeamName());
                        }
                    }
                });
            }
        }
        return dailyStatisticsVos;
    }

    @Override
    public LiveHostStatisticVo getOrgStatistics(LivePerDayStatisticSearch search) {
        LiveHostStatisticVo liveHostStatisticVo = null;
        List<Long> teamIdList = new ArrayList<>();
        List<String> hostUuids = new ArrayList<>();
        if(search.getOrgId() != null && search.getOrgId() != 0){
            List<Long> teamIds = teamMapper.selectTeamListIds(search.getOrgId(),TeamTypeEnum.LIVE_TEAM.getValue());
            if(CollectionUtils.isNotEmpty(teamIds)){
                teamIdList.addAll(teamIds);
            }else{
                return new LiveHostStatisticVo();
            }
        }
        if((search.getTeamId() != null &&  search.getTeamId() != 0)){
            teamIdList.clear();
            teamIdList.add(search.getTeamId());
        }
        if(StringUtils.isNotEmpty(search.getAgentManageUuid())){
            if(CollectionUtils.isNotEmpty(hostUuids)){
                hostUuids.clear();
            }
            List<String> teamHostUuid = teamHostMapper.getHostUuidsByAgenter(Long.parseLong(search.getAgentManageUuid()));
            if(CollectionUtils.isNotEmpty(teamHostUuid)){
                search.setHostUuidList(teamHostUuid);
                hostUuids.addAll(teamHostUuid);
            }
            if(CollectionUtils.isEmpty(teamHostUuid)){
                hostUuids.add("null");
            }
        }
        if(StringUtils.isNotEmpty(search.getHost_uuid())){
            hostUuids.clear();
            hostUuids.add(search.getHost_uuid());
        }
        liveHostStatisticVo = gonghuiService.hostStatistic(teamIdList,search.getStart(), search.getEnd(), null, search.getOrder_type(), search.getSort_type(), hostUuids, search.getApply_level(), search.getPage(), search.getRows(), search.getLive_status());

        if(liveHostStatisticVo != null){
            if(CollectionUtils.isNotEmpty(liveHostStatisticVo.getList())){
                liveHostStatisticVo.getList().forEach(lhsv->{
                    lhsv.setBusinessman_name("");
                    if(StringUtils.isNotBlank(lhsv.getConsortia_id())){
                        Team team = teamMapper.selectByPrimaryKey(Long.valueOf(lhsv.getConsortia_id()));
                        if(team!=null){
                            Organization organization = organizationMapper.selectByPrimaryKey(team.getOrgId());
                            if(organization != null){
                                lhsv.setConsortia_name(organization.getOrgName());
                            }else{
                                lhsv.setConsortia_name("");
                            }
                        }else{
                            lhsv.setConsortia_name("");
                        }
                    }else{
                        lhsv.setConsortia_name("");
                    }

                    TeamHost empId = teamHostMapper.getOneByHostUuid(lhsv.getHost_uuid(),TeamTypeEnum.LIVE_TEAM.getValue());
                    if(empId != null){
                        TeamEmployee name = teamEmployeeMapper.selectById(empId.getEmployeeId());
                        if(name != null){
                            lhsv.setBusinessman_name(name.getEmployeeName());
                        }
                    }
                });
            }
        }
        return liveHostStatisticVo;
    }

    @Override
    public MonthVo getOperatorDailyStatistics(OrgStatisticsSearch search) {
        MonthVo monthVo = new MonthVo();
        checkOperatorParams(search);
        List<LiveOrgStatisticsMonth> liveOrgStatisticsMonths = liveOrgStatisticsMonthMapper.queryByCondition(search);
        if(CollectionUtils.isNotEmpty(liveOrgStatisticsMonths)){
            monthVo.setList(liveOrgStatisticsMonths);
            monthVo.setTotal(liveOrgStatisticsMonths.size());
        }
        return monthVo;
    }


    @Override
    public LiveHostStatisticVo getOrgStatisticsByUser(LivePerDayStatisticSearch search) {
        LiveHostStatisticVo liveHostStatisticVo = null;
        if(cn.taqu.gonghui.common.utils.StringUtils.isNotEmpty(search.getEnd()) && cn.taqu.gonghui.common.utils.StringUtils.isNotEmpty(search.getStart())){
            String newStartTime = search.getStart() + " 00:00:00";
            String newEndTime = search.getEnd() + " 23:59:59";
            search.setEnd(newEndTime);
            search.setStart(newStartTime);
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        RoleVo userRole = userService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        //将入参传入符合查询条件的主播uuid塞入list集合中
        LivePerDayStatisticSearch uuidSearch = getSearch(search,loginUser);
        Object[] from = {uuidSearch.getTeamIdList(),search.getStart(), search.getEnd(), null, search.getOrder_type(), search.getSort_type(),  uuidSearch.getHostUuidList(), search.getApply_level(), search.getPage(), search.getRows(), search.getLive_status()};
        logger.info("用户数据统计参数{}---{}：",JsonUtils.objectToString(from),userRole.getRoleKey());
        liveHostStatisticVo = gonghuiService.hostStatistic(uuidSearch.getTeamIdList(),search.getStart(), search.getEnd(), null, search.getOrder_type(), search.getSort_type(), uuidSearch.getHostUuidList(), search.getApply_level(), search.getPage(), search.getRows(), search.getLive_status());
        if(liveHostStatisticVo!= null){
            if(CollectionUtils.isNotEmpty(liveHostStatisticVo.getList())){
                liveHostStatisticVo.getList().forEach(lhsv->{
                    lhsv.setBusinessman_name("");
                    lhsv.setConsortia_name("");
                    Organization organization = organizationMapper.selectByPrimaryKey(loginUser.getUser().getOrgId());
                    if(organization != null){
                        lhsv.setConsortia_name(organization.getOrgName());
                    }
                    TeamHost empId = teamHostMapper.getOneByHostUuid(lhsv.getHost_uuid(),TeamTypeEnum.LIVE_TEAM.getValue());
                    if(empId != null){
                        //如果主播存在绑定公会，则查出相关联的团队名称
                        if(empId.getTeamId() != null && empId.getTeamId() != 0){
                            Team team = teamMapper.selectById(empId.getTeamId());
                            if(team != null){
                                if(StringUtils.isNotEmpty(team.getTeamName())){
                                    lhsv.setTeamName(team.getTeamName());
                                }
                            }
                        }
                        TeamEmployee name = teamEmployeeMapper.selectById(empId.getEmployeeId());
                        if(name != null){
                            lhsv.setBusinessman_name(name.getEmployeeName());
                        }
                    }
                });
            }
        }
        return liveHostStatisticVo;
    }


    /**
     * 返回参数搜索的uuid
     */
    private LivePerDayStatisticSearch getSearch(LivePerDayStatisticSearch search, LoginUser loginUser){
        RoleVo userRole = userService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        LivePerDayStatisticSearch uuidSearch = new LivePerDayStatisticSearch();
        List<Long> teamIds = new ArrayList<>();
        List<String> agentUuids = new ArrayList<>();
        List<String> hostUuid = new ArrayList<>();

        if(UserTypeEnum.AGENTER.getCode().equals(userRole.getRoleKey())){
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(loginUser.getUser().getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            teamIds.add(teamEmployee.getTeamId());
            uuidSearch.setTeamIdList(teamIds);
            agentUuids.add(loginUser.getUser().getAccountUuid());
            if(teamEmployee.getEmployeeId() != null){
                hostUuid = teamHostMapper.getHostUuidsByAgenter(teamEmployee.getEmployeeId());
                if(CollectionUtils.isNotEmpty(hostUuid)){
                    uuidSearch.setHostUuidList(hostUuid);
                }else {
                    hostUuid.add("host is null");
                    uuidSearch.setHostUuidList(hostUuid);
                }
            }
            // 说明 110166 是他趣自己的公会，关联的主播数量非常多，印象线上的查询，所以要排除掉
            if(110166 == teamEmployee.getTeamId()){
                hostUuid.clear();
                hostUuid.add("host is null");
                uuidSearch.setHostUuidList(hostUuid);
            }

        }
        if(UserTypeEnum.LEADER.getCode().equals(userRole.getRoleKey())){
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(loginUser.getUser().getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            teamIds.add(teamEmployee.getTeamId());
            uuidSearch.setTeamIdList(teamIds);
        }
        if(UserTypeEnum.MANAGER.getCode().equals(userRole.getRoleKey())){
            if(loginUser.getUser().getOrgId() != null){
                List<Long> teams = teamMapper.selectTeamListIds(loginUser.getUser().getOrgId(),TeamTypeEnum.LIVE_TEAM.getValue());
                if(teams != null){
                    uuidSearch.setTeamIdList(teams);
                }
            }
        }
        //如果传入值是团队传入
        if(search.getTeamId() != null && !UserTypeEnum.AGENTER.getCode().equals(userRole.getRoleKey()) && search.getTeamId() !=0){
            teamIds.clear();
            agentUuids.clear();
            hostUuid.clear();
            teamIds.add(search.getTeamId());
            uuidSearch.setTeamIdList(teamIds);
        }
        //如果传入值有根据经纪人传入，则根据经纪人查询显示结果
        if(StringUtils.isNotEmpty(search.getAgentManageUuid())){
            agentUuids.clear();
            hostUuid.clear();
            Long employeeId = teamEmployeeMapper.seletEmployeeId(search.getAgentManageUuid(),TeamTypeEnum.LIVE_TEAM.getValue());
            hostUuid = teamHostMapper.getHostUuidsByAgenter(employeeId);
            if(CollectionUtils.isNotEmpty(hostUuid)){
                uuidSearch.setHostUuidList(hostUuid);
            }else {
                hostUuid.add("host is null");
                uuidSearch.setHostUuidList(hostUuid);
            }
        }
        //如果传入有主播uuid，则根据主播uuid查询
        if(StringUtils.isNotEmpty(search.getHost_uuid())){
            hostUuid.clear();
            TeamHost teamHost = teamHostMapper.getOneByHostUuid(search.getHost_uuid(),TeamTypeEnum.LIVE_TEAM.getValue());
            if(teamHost != null){
                if(UserTypeEnum.AGENTER.getCode().equals(userRole.getRoleKey())){
                    Long employeeId = teamEmployeeMapper.seletEmployeeId(loginUser.getUser().getAccountUuid(),TeamTypeEnum.LIVE_TEAM.getValue());
                    if(teamHost.getEmployeeId() != null && teamHost.getEmployeeId().equals(employeeId)){
                        hostUuid.add(search.getHost_uuid());
                    }else {
                        hostUuid.add("host is null");
                    }
                }
                TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(loginUser.getUser().getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());

                if(UserTypeEnum.LEADER.getCode().equals(userRole.getRoleKey())){
                    if(teamHost.getTeamId() != null && teamEmployee.getTeamId().equals(teamHost.getTeamId())){
                        hostUuid.add(search.getHost_uuid());
                    }else {
                        hostUuid.add("host is null");
                    }
                }
                if( UserTypeEnum.MANAGER.getCode().equals(userRole.getRoleKey())){
                    if(teamHost.getOrgId() != null && loginUser.getUser().getOrgId().equals(teamHost.getOrgId())){
                        hostUuid.add(search.getHost_uuid());
                    }else {
                        hostUuid.add("host is null");
                    }
                }
            }else {
                hostUuid.add("host is null");
            }
            uuidSearch.setHostUuidList(hostUuid);
        }
        return uuidSearch;
    }

    @Override
    public List<DailyStatisticsVo> getOrgDailyStatisticsByUser(LivePerDayStatisticSearch search) {
        List<DailyStatisticsVo> dailyStatisticsVos = null;
        OrgStatisticsSearch searchHost = new OrgStatisticsSearch();
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        RoleVo userRole = userService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        searchHost.setPage(search.getPage());
        searchHost.setPageSize(search.getPageSize());
        searchHost.setStartTime(Long.valueOf(search.getStart()));
        searchHost.setEndTime(Long.valueOf(search.getEnd()));
        List<Long> teamIds = new ArrayList<>();
        if((search.getTeamId() !=null && search.getTeamId() != 0) ||   !UserTypeEnum.MANAGER.getCode().equals(userRole.getRoleKey())){
                teamIds.add(search.getTeamId());
                searchHost.setConsortiaIdList(teamIds);
                if(!UserTypeEnum.MANAGER.getCode().equals(userRole.getRoleKey())){
                    teamIds.clear();
                    TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(loginUser.getUser().getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
                    teamIds.add(teamEmployee.getTeamId());
                    searchHost.setConsortiaIdList(teamIds);
                }
            logger.info("登录用户Leader{}-----{}",JsonUtils.objectToString(searchHost),JsonUtils.objectToString(userRole));
            dailyStatisticsVos = liveConsortiaStatisticsDayMapper.queryByCondition(searchHost);
        } else {
                teamIds = teamMapper.selectTeamListIds(loginUser.getUser().getOrgId(),TeamTypeEnum.LIVE_TEAM.getValue());
                if (CollectionUtils.isNotEmpty(teamIds)) {
                    searchHost.setConsortiaIdList(teamIds);
                }else {
                    logger.warn("未查到该用户所对应的机构团队，机构id{}",loginUser.getUser().getOrgId());
                    throw new ServiceException("error","未查到该用户所对应的机构团队");
                }
                if(search.getTeamId() !=null && search.getTeamId() != 0){
                    teamIds.clear();
                    teamIds.add(search.getTeamId());
                    searchHost.setConsortiaIdList(teamIds);
                }
                logger.info("登录用户Master{}-----{}",JsonUtils.objectToString(searchHost),JsonUtils.objectToString(userRole));
                dailyStatisticsVos = liveConsortiaStatisticsDayMapper.queryByCondition(searchHost);
        }
        if(dailyStatisticsVos.size() != 0){
            dailyStatisticsVos.forEach(ds->{
                ds.setTotalTimeStr(secondToTime(ds.getTotalLiveTime()));
                if(ds.getConsortiaId() != null){
                    Team team = teamMapper.selectById(ds.getConsortiaId());
                    if(team != null){
                        ds.setTeamName(team.getTeamName());
                    }
                }
            });
        }
        return dailyStatisticsVos;
    }

    /**
     * 返回日时分秒
     * @param second
     * @return
     */
    private String secondToTime(long second) {
        long days = second / 86400;//转换天数
        second = second % 86400;//剩余秒数
        long hours = second / 3600;//转换小时数
        second = second % 3600;//剩余秒数
        long minutes = second / 60;//转换分钟
        second = second % 60;//剩余秒数
        String time = "";
        if(hours != 0){
            time = hours+"时"+minutes+"分"+second;
        }else{
            time = minutes+"分"+second;
        }
        return time;
    }



    private void checkTeamDailyParams(OrgStatisticsSearch search){
        if (null == search.getOrgId()) {
            throw new ServiceException("param_error","请选择机构");
        }
        if (null == search.getStartTime()) {
            throw new ServiceException("param_error","统计开始周期不能为空");
        }
        if (null == search.getEndTime()) {
            throw new ServiceException("param_error","统计结束周期不能为空");
        }
        long startTimeMillis = DateUtil.StringToDate(String.valueOf(search.getStartTime()), "yyyyMMdd").getTime();
        long endTimeMillis = DateUtil.StringToDate(String.valueOf(search.getEndTime()), "yyyyMMdd").getTime();
        if ((endTimeMillis - startTimeMillis) > (long) 30 * (long) 24 * (long) 60 * (long) 60 * (long) 1000)  {
            throw new ServiceException("param_error","统计周期间隔不能超过三十天");
        }
    }

    private void checkOperatorParams(OrgStatisticsSearch search){
        if (null == search.getStartTime()) {
            throw new ServiceException("param_error","统计周期不能为空");
        }
        if (null == search.getEndTime()) {
            throw new ServiceException("param_error","统计周期不能为空");
        }
    }

    private void processHostOrgName(List<LiveHostVo> voList){
        if (CollectionUtils.isNotEmpty(voList)) {
            List<String> uuidList = voList.stream().map(LiveHostVo::getHost_uuid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(uuidList)) {
                List<TeamHostVo> teamHostVos = teamHostMapper.orgNameByHostUuidList(uuidList);
                Map<String, String> map  = null;
                if (CollectionUtils.isNotEmpty(teamHostVos)) {
                    map = teamHostVos.stream().collect(Collectors.toMap(TeamHostVo::getHostUuid,TeamHostVo::getOrgName, (k1,k2) -> k1));
                }
                for (LiveHostVo item : voList) {
                    if (MapUtils.isNotEmpty(map)) {
                        item.setConsortia_name(map.get(item.getHost_uuid()));
                    }
                }
            }
        }
    }

    private void processHostNickNameAndAvatar(List<LiveHostVo> voList){
        if (CollectionUtils.isNotEmpty(voList)) {
            List<String> uuidList = voList.stream().map(LiveHostVo::getHost_uuid).collect(Collectors.toList());
            String[] uuids = uuidList.toArray(new String[uuidList.size()]);
            Map<String, Map<String, Object>> uuidMap = hostService.getInfoByUuids(uuids, new String[]{"account_name", "avatar","account_card_id"});
            if (MapUtils.isNotEmpty(uuidMap)) {
                for (LiveHostVo item : voList) {
                    Map<String, Object> map = uuidMap.get(item.getHost_uuid());
                    if (MapUtils.isNotEmpty(map)) {
                        item.setAvatar(String.valueOf(map.get("avatar")));
                        item.setNickName(String.valueOf(map.get("account_name")));
                        item.setLive_no(String.valueOf(map.get("account_card_id")));
                    }
                }
            }
        }
    }

    public void processAgent(List<LiveHostVo> voList){
        if (CollectionUtils.isNotEmpty(voList)) {
            List<String> uuidList = voList.stream().map(LiveHostVo::getBusinessman_uuid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(uuidList)) {
                List<SysUser> userList = sysUserMapper.userNameListByAccountUuidList(uuidList);
                Map<String, String> map  = null;
                if (CollectionUtils.isNotEmpty(userList)) {
                    map = userList.stream().collect(Collectors.toMap(SysUser::getAccountUuid, SysUser::getUserName));
                }
                for (LiveHostVo item : voList) {
                    if (MapUtils.isNotEmpty(map)) {
                        item.setBusinessman_name(map.get(item.getBusinessman_uuid()));
                    }
                }
            }
        }
    }

    public void processTeamName(List<LiveHostVo> voList){
        if (CollectionUtils.isNotEmpty(voList)) {
            List<String> uuidList = voList.stream().map(LiveHostVo::getHost_uuid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(uuidList)) {
                List<TeamHostVo> teamHostVos = teamHostMapper.teamNameByHostUuidList(uuidList);
                Map<String, String> map  = null;
                if (CollectionUtils.isNotEmpty(teamHostVos)) {
                    map = teamHostVos.stream().collect(Collectors.toMap(TeamHostVo::getHostUuid,TeamHostVo::getTeamName,(k1,k2)->k1));
                }
                for (LiveHostVo item : voList) {
                    if (MapUtils.isNotEmpty(map)) {
                        item.setTeam_name(map.get(item.getHost_uuid()));
                    }
                }
            }
        }
    }

    /**
     * 设置管理员角色搜索参数
     */
    private void setManagerStatisticSearch(String teamId,String employeeId,HostStatisticSearch search){
        // 如果选择了经纪人,获取经纪人下hostUuid
        if (StringUtils.isNotBlank(employeeId)) {
            if (1001281 != search.getOrgId()) {
                List<String> hostList = getHostUuidsByEmployeeId(employeeId);
                if (CollectionUtils.isNotEmpty(hostList)) {
                    String hostArgs = org.apache.commons.lang3.StringUtils.join(hostList, ",");
                    search.setHost_uuid(hostArgs);
                    search.setHostUuidList(hostList);
                } else {
                    search.setHost_uuid("no host uuid");
                    search.setHostUuidList(Arrays.asList("no_host_uuid"));
                }
            }
        } else {
            // 如果没有选择经纪人，那么是否选择了团队，如果选择了团队，获取团队下所有hostUuid
            if (StringUtils.isNotBlank(teamId)) {
                if (1001281 != search.getOrgId()) {
                    List<String> hostList = getHostUuidsByTeamId(teamId);
                    if (CollectionUtils.isNotEmpty(hostList)) {
                        String hostArgs = org.apache.commons.lang3.StringUtils.join(hostList, ",");
                        search.setHost_uuid(hostArgs);
                        search.setHostUuidList(hostList);
                    } else {
                        search.setHost_uuid("no host uuid");
                        search.setHostUuidList(Arrays.asList("no_host_uuid"));
                    }
                }
            }
        }
    }

    /**
     * 设置负责人角色搜索参数
     */
    private void setLeaderStatisticsSearch(String employeeId,HostStatisticSearch search){
        // 是否选择了经纪人，如果选择了那么获取该经纪人下所有hostUuid
        if (StringUtils.isNotBlank(employeeId)) {
            if (1001281 != search.getOrgId()) {
                List<String> hostList = getHostUuidsByEmployeeId(employeeId);
                if (CollectionUtils.isNotEmpty(hostList)) {
                    String hostArgs = org.apache.commons.lang3.StringUtils.join(hostList, ",");
                    search.setHost_uuid(hostArgs);
                    search.setHostUuidList(hostList);
                } else {
                    search.setHost_uuid("no host uuid");
                    search.setHostUuidList(Arrays.asList("no_host_uuid"));
                }
            }
        }
        // 如果没有选择经纪人，那么获取该负责人所属团队下的所有hostUuid
        else {
            if (1001281 != search.getOrgId()) {
                List<String> hostList = getHostUuidsByAccountUuid(search.getBusinessType());
                if (CollectionUtils.isNotEmpty(hostList)) {
                    String hostArgs = org.apache.commons.lang3.StringUtils.join(hostList, ",");
                    search.setHost_uuid(hostArgs);
                    search.setHostUuidList(hostList);
                } else {
                    search.setHost_uuid("no host uuid");
                    search.setHostUuidList(Arrays.asList("no_host_uuid"));
                }
            }
        }
    }

    /**
     * 设置经纪人角色搜索参数
     */
    private void setAgenterStatisticsSearch(HostStatisticSearch search){
        if (1001281 != search.getOrgId()) {
            // 获取该经纪人下所有hostUuid
            List<String> hostList = getHostUuidsByAccountUuid(search.getBusinessType());
            if (CollectionUtils.isNotEmpty(hostList)) {
                String hostArgs = org.apache.commons.lang3.StringUtils.join(hostList, ",");
                search.setHost_uuid(hostArgs);
                search.setHostUuidList(hostList);
            } else {
                search.setHost_uuid("no host uuid");
                search.setHostUuidList(Arrays.asList("no_host_uuid"));
            }
        }
    }



    /**
     * 获取负责人所属teamId
     */
    public List<Long> teamIdByAccountUuid(Integer businiessType){
        String accountUuid = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getAccountUuid();
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException("invalid_user","当前用户无效");
        }
        List<Long> teamIdList = new ArrayList<>();
        Long teamId = sysUserMapper.getTeamIdByAccountUuid(accountUuid,businiessType);
        if (null == teamId) {
            teamIdList.add(000000L);
        } else {
            teamIdList.add(teamId);
        }
        return teamIdList;
    }

    /**
     * 获取当前用户机构下所有teamId
     */
    private List<Long> teamIdsByOrgId(Integer type){
        Long orgId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getOrgId();
        if (null == orgId) {
            throw new ServiceException("invalid_organization","您所属机构无效,请联系管理员");
        }
        List<Team> teams = teamMapper.selectTeamList(orgId, type);
        if (CollectionUtils.isNotEmpty(teams)) {
            return teams.stream().map(Team::getTeamId).collect(Collectors.toList());
        } else {
            List<Long> list = new ArrayList<>();
            list.add(000000L);
            return list;
        }
    }

    /**
     * 获取当前用户所属机构id
     * @return
     */
    private Long getOrgIdByAccountUuid(){
        Long orgId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getOrgId();
        if (null ==  orgId) {
            throw new ServiceException(CodeStatus.ORG_NOT_FOUNT_ERROR.value(),"当前用户所属机构无效");
        }
        return orgId;
    }

    /**
     * 获取当前用户所属机构uuid
     * @return
     */
    private String getOrgUuidByOrgId(String orgId){
        return organizationMapper.selectByPrimaryKey(Long.valueOf(orgId)).getOrgUuid();
    }



    /**
     * 获取当前用户下的有效主播uuid
     */
    private List<String> getHostUuidsByAccountUuid(Integer type){
        if(type == null || type > 3){
            throw new ServiceException("business_type_error","当前业务类型有误");
        }
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        List<String> hostUuids = null;
        RoleVo role = userService.getCurrentRole(type);
        // 角色是负责人
        if (role.getRoleKey().equals(UserTypeEnum.LEADER.getCode())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), type);
            if (null == teamEmployee) {
                logger.warn("当前负责人无所属团队");
                return hostUuids;
            }
            hostUuids = teamHostMapper.getHostUuidsByLeader(teamEmployee.getTeamId());
        }
        // 角色是经纪人
        else if (role.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())){
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), type);
            if (null == teamEmployee) {
                logger.warn("当前经纪人是个无效成员");
                return  hostUuids;
            }
            hostUuids = teamHostMapper.getHostUuidsByAgenter(teamEmployee.getEmployeeId());
        }
        return hostUuids;
    }

    /**
     * 获取经纪人下所有有效主播uuid
     * @return
     */
    private List<String> getHostUuidsByEmployeeId(String employeeId){
        return teamHostMapper.getHostUuidsByAgenter(Long.valueOf(employeeId));
    }

    /**
     * 获取负责人所属团队下所有有效主播uuid
     * @return
     */
    private List<String> getHostUuidsByTeamId(String teamId){
        return teamHostMapper.getHostUuidsByLeader(Long.valueOf(teamId));
    }


}
