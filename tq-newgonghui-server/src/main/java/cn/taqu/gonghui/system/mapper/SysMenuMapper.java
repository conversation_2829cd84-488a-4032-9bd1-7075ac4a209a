package cn.taqu.gonghui.system.mapper;


import cn.taqu.gonghui.system.entity.SysMenu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysMenuMapper extends BaseMapper<SysMenu> {

    /**
     * 查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
    List<SysMenu> selectMenuList(SysMenu menu);

    /**
     * 根据用户查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
    List<SysMenu> selectMenuListByUserId(SysMenu menu);

    /**
     * 查询所有用户对应的菜单权限
     *
     * @return 权限列表
     */
    List<String> selectMenuPerms();

    /**
     * 根据用户ID查询用户拥有菜单权限
     *
     * @param accountUuid 用户ID
     * @return 权限列表
     */
    List<String> selectMenuPermsByUserId(String accountUuid);

    /**
     *
     * @param accountUuid
     * @param type  角色类型 1 直播 2趣聊 3聊天室
     * @return
     */
    List<String> selectMenuPermsByUserIdAndType(String accountUuid,Integer type);

    /**
     * 查询所有菜单列表
     *
     * @return 菜单列表
     */
    List<SysMenu> selectMenuTreeAll(@Param("typeList") List<Integer> typeList);

    /**
     * 根据用户ID查询菜单树
     *
     * @param accountUuid 用户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenuTreeByUserId(@Param("accountUuid") String accountUuid,@Param("type") Integer type);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    List<Long> selectMenuListByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    SysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    int hasChildByMenuId(Long menuId);

    /**
     * 新增菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    int insertMenu(SysMenu menu);

    /**
     * 修改菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    int updateMenu(SysMenu menu);

    void updateChildrenVisible(Long menuId);

    List<SysMenu> selectChildrenMenuById(Long menuId);

    void updateMenuChildren(List<SysMenu> children);

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    int deleteMenuById(Long menuId);

    /**
     * 校验菜单名称是否唯一
     *
     * @param menuName 菜单名称
     * @param parentId 父菜单ID
     * @return 结果
     */
    SysMenu checkMenuNameUnique(@Param("menuName") String menuName, @Param("parentId") Long parentId,@Param("type") Integer type);

    /**
     * 根据service 和 method获取菜单路径
     */
    String getmenuNameByServiceAndMethod(String serviceAndMethod);

    /**
     * 根据service 和 method获取菜单信息
     */
    SysMenu getMenuInfoByServiceAndMethod(@Param("serviceAndMethod")String serviceAndMethod,@Param("type")Integer type);
}
