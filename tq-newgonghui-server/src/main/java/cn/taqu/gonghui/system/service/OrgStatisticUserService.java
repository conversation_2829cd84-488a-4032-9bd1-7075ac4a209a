package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.vo.HostVo;
import cn.taqu.gonghui.common.vo.LiveHostSelfStatisticVo;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.search.HostStatisticSearch;
import cn.taqu.gonghui.system.search.LiveHostSelfStatisticSearch;

import java.util.List;


public interface OrgStatisticUserService {

    HostVo getLiveHostListForAdmin(HostStatisticSearch search);

    LiveHostSelfStatisticVo getLiveHostSelfStatistic(LiveHostSelfStatisticSearch search);


}
