package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName(value = "org_name_log")
@Data
public class OrgNameLog {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String oldOrgName;

    private String newOrgName;

    private String createOperator;

    private Date createTime;

    private Long relevanceId;

}