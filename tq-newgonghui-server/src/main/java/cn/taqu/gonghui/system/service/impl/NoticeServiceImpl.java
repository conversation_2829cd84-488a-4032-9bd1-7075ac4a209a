package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.entity.Notice;
import cn.taqu.gonghui.system.mapper.NoticeMapper;
import cn.taqu.gonghui.system.search.NoticeSearch;
import cn.taqu.gonghui.system.service.NoticeService;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class NoticeServiceImpl implements NoticeService {

    @Autowired
    private NoticeMapper noticeMapper;

    @Override
    public List<Notice> getNoticeByNoticeType(Integer noticeType) {
        if (Notice.NOTICE_TYPE_ANNOUNCEMENT.equals(noticeType) || Notice.NOTICE_TYPE_NORM.equals(noticeType) || Notice.NOTICE_TYPE_HELP.equals(noticeType)) {
            Integer limitNum=0;
            if(Notice.NOTICE_TYPE_ANNOUNCEMENT.equals(noticeType)){
                limitNum = 5;
            }else if(Notice.NOTICE_TYPE_NORM.equals(noticeType)||Notice.NOTICE_TYPE_HELP.equals(noticeType)) {
                limitNum = 3;
            }else{
                limitNum = 10;
            }
            return noticeMapper.findNoticeByNoticeType(noticeType,limitNum);
        } else {
            throw new ServiceException("noticeType", "公告类型异常！");
        }
    }

    /**
     * 公告分页列表
     * @param search
     * @return
     */
    @Override
    public List<Notice> pagelist(NoticeSearch search) {
        PageHelper.startPage(search.getPageNum() == null ? 1 : search.getPageNum(),
                search.getPageSize() == null ? 15 : search.getPageSize());
        List<Notice> notices = noticeMapper.selectNoticePageList(search);
        PageHelper.clearPage();
        return notices;
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @Override
    public Notice detail(Long id) {
        if (null == id) {
            throw new ServiceException("param_error","ID不能为空");
        }
        return noticeMapper.selectByPrimaryKey(id);
    }

    /**
     * 添加
     * @param record
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(Notice record) {
        checkParams(record);
        record.setCreateTime(System.currentTimeMillis()/1000);
        noticeMapper.insertNotice(record);
    }

    /**
     * 更新
     * @param record
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(Notice record) {
        if (null == record.getId()) {
            throw new ServiceException("param_error","ID不能为空");
        }
        checkParams(record);
        record.setUpdateTime(System.currentTimeMillis()/1000);
        noticeMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 删除
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long id) {
        if (null == id) {
            throw new ServiceException("param_error","ID不能为空");
        }
        noticeMapper.deleteByPrimaryKey(id);
    }

    private void checkParams(Notice record){
        if (StringUtils.isBlank(record.getTitle())) {
            throw new ServiceException("param_error","标题不能为空");
        }
        if (StringUtils.isBlank(record.getContent())) {
            throw new ServiceException("param_error","内容不能为空");
        }
        if (null == record.getNoticeType()) {
            throw new ServiceException("param_error","请选择公告类型");
        }
        if (null == record.getBusinessType()) {
            throw new ServiceException("param_error","请选择业务类型");
        }
        if (StringUtils.isBlank(record.getOperator())) {
            throw new ServiceException("param_error","操作人不能为空");
        }
    }

    /**
     * 详情
     * @param title
     * @return
     */
    @Override
    public Notice detailBytitle(String title) {
        if (null == title) {
            throw new ServiceException("param_error","title不能为空");
        }
        QueryWrapper<Notice> queryWrapper = new QueryWrapper<Notice>();
        queryWrapper.eq("title",title);
        noticeMapper.selectOne(queryWrapper);
        return noticeMapper.selectOne(queryWrapper);
    }
}
