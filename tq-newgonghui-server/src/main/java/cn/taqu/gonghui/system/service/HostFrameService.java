package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.search.HostFrameListSearch;

import java.util.List;
import java.util.Map;

public interface HostFrameService {
    /**
     * 通过传入 公会id/经纪人id 来获取旗下主播的列表
     * 获取公会下或经纪人下主播列表信息
     * @return
     */
    List<Map<String, String>> findOnlineHostList();

    /**
     * 获取公会主播视频截帧
     * Gonghui/getFrameList
     * 需要字段:
     * 直播间id   `card_id`
     * 开播时长   `live_time`
     * 开播收入   `receive_gift`
     * 送礼人数   `sender_num`
     * 观众人数   `viewer_num`
     * 新增粉丝   `follower_num`
     *
     * @param search
     * @return
     */
    List<Map<String, String>> findOnlineFrameList(HostFrameListSearch search);
}
