package cn.taqu.gonghui.system.vo;

import cn.taqu.gonghui.common.utils.StringUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/4/30
 */
@Data
public class TeamEmployeeVo {

    private Long employeeId;
    private String userName;
    private String employeeName;
    private String orgName;
    private Long teamId;
    private String teamName;
    private String mobile;
    private Long inviteTime;
    private Integer type;
    private Long createTime;
    private Long userId;
    /**
     * 上级
     */
    private String superiors;

    /**
     * 账号状态
     */
    private Integer status;

    private String roleName;

    private String roleKey;

    /**
     * 关联主播数量
     */
    private Long relevanceHostNumber;

    /**
     * 手机号-密文
     */
    private String mobileCipher;
    /**
     * 成员姓名-密文
     */
    private String employeeNameCipher;

    public String getMobile() {
        if(StringUtils.isBlank(this.mobile) && StringUtils.isNotBlank(this.mobileCipher)){
            return this.mobileCipher;
        }
        return this.mobile;
    }

    public String getEmployeeName() {
        if(StringUtils.isBlank(this.employeeName) && StringUtils.isNotBlank(this.employeeNameCipher)){
            return this.employeeNameCipher;
        }
        return this.employeeName;
    }

    public String getUserName() {
        if(StringUtils.isBlank(this.userName)) {
            return getEmployeeName();
        }
        return this.userName;
    }
}
