package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 机构-协议关系
 * <AUTHOR>
 */
@Data
public class Agreement {
    /**
     * 流水号
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 协议id
     */
    private Long agrId;

    /**
     * 创建时间
     */
    private Long createTime;
}
