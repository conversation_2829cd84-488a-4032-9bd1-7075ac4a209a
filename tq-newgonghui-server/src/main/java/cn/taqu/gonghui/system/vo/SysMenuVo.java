package cn.taqu.gonghui.system.vo;

import cn.taqu.gonghui.system.entity.SysMenu;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

public class SysMenuVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 菜单ID */
    private Long menuId;

    /** 菜单名称 */
    private String menuName;

    /** 父菜单id */
    private Long parentId;

    /** 路由地址 */
    private String routerPath;

    /** 菜单类型（M-目录，C-菜单，F-按钮） */
    private String menuType;

    /** 图标 */
    private String icon;

    /** 是否隐藏（1-显示，0隐藏） */
    private Integer visible;

    /** 权限标识 */
    private String perms;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SysMenuVo> children;

    public SysMenuVo()
    {

    }

    public SysMenuVo(SysMenu menu)
    {
        this.menuId = menu.getMenuId();
        this.menuName = menu.getMenuName();
        this.parentId = menu.getParentId();
        this.routerPath = menu.getRouterPath();
        this.menuType = menu.getMenuType();
        this.icon = menu.getIcon();
        this.visible = Integer.valueOf(menu.getVisible());
        this.perms = menu.getPerms();
        this.children = menu.getChildren().stream().map(SysMenuVo::new).collect(Collectors.toList());
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getMenuId() {
        return menuId;
    }

    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getRouterPath() {
        return routerPath;
    }

    public void setRouterPath(String routerPath) {
        this.routerPath = routerPath;
    }

    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getVisible() {
        return visible;
    }

    public void setVisible(Integer visible) {
        this.visible = visible;
    }

    public List<SysMenuVo> getChildren() {
        return children;
    }

    public void setChildren(List<SysMenuVo> children) {
        this.children = children;
    }

    public String getPerms() {
        return perms;
    }

    public void setPerms(String perms) {
        this.perms = perms;
    }
}
