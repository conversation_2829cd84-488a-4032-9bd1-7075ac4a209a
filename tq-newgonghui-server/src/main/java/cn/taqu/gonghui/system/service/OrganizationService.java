package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.common.entity.OrgFlowLog;
import cn.taqu.gonghui.common.vo.OrganizationVo;
import cn.taqu.gonghui.common.vo.RemitSearchVO;
import cn.taqu.gonghui.system.entity.FeishuAuditRelation;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.search.OrganizationInfoSearch;
import cn.taqu.gonghui.system.vo.RemitItemVO;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

public interface OrganizationService {

    PageInfo<List<OrganizationInfoSearch>> findOrgInfoPageList(OrganizationInfoSearch search, int pageNo, int pageSize);

    PageInfo<List<OrganizationInfoSearch>> findPassOrgInfoPageList(OrganizationInfoSearch search, int pageNo, int pageSize);

    Organization getOrgInfo(Long id);

    void changeApplyStatus(Long id, Integer applyStatus, String auditMsg, String operator, String operationSpecialistId,Integer settlementeType,Integer[] businessPermissions);

    void rejectOrg(String auditMsg, Organization organization);

    void passOrg(Organization organization, FeishuAuditRelation relation);

    void updateOrgInfo(String paramJson, String operator);

    void modifyOrgInfo(Organization vo, String operator);


    void bindMobile(String orgId, String mobile, String vCode, String operator);
    void bindMobile2(String orgId, String mobile, String vCode, String operator,String oldMobile);

    void doBindMobile(String orgId, String mobile, String operator);

    void doBindMobile2(String orgId, String mobile, String operator,String oldMobile);

    List<Organization> findPassList();

    List<CommonSelect> findGuildInfoUuidAndNameList();

    Map<String, Object> saveOrgAll(OrganizationVo vo, String operator);

    List<CommonSelect> findOrgUuidAndNameList();

    void liveSettlementeTypeOperate();

    /**
     * 机构注销
     *
     * @param orgId
     * @param sceneId
     * @param code
     */
    void orgClose(Long orgId, String sceneId, String code);

    void changeOrg(Long orgId);

    void sendMsg(Organization organization, String auditMsg);

    void saveBusinessLicense(String orgUuid, String businessLicenseUrl);

    void saveLegalPerson(String orgUuid, String legalPersonUrl);

    void saveChargePerson(String orgUuid, String chargePersonUrl);

    void saveOrgCooperationFlow(String orgUuid, String orgCooperationFlowUrl);

    void saveHostScreenshot(String orgUuid, String hostScreenshotUrl);

    void sendPassMsg(Organization organization, String msg, String tagCode);


    /**
     * 启停机构运营业务
     *
     * @param sceneId
     * @param code
     * @param orgId
     * @param businessTypes
     * @param settlementeType
     */
    void changeBusinessType(String sceneId, String code, Long orgId, Integer businessTypes, Integer permissions, Integer settlementeType);

    /**
     * 将团队变更到他趣公会（关闭业务前）
     * @param orgId
     */
    void changeLivingTeamToTaquBeforClose(Long orgId, Integer backstageOperateType, String batchId, String operator);

    /**
     * 获取打款列表
     * @param remitSearchVO
     * @param pageInfo
     * @return
     */
    PageInfo<RemitItemVO> getRemitPageList(RemitSearchVO remitSearchVO, PageInfo pageInfo);

    /**
     * 获取机构审批日志
     * @param orgId
     * @return
     */
    List<OrgFlowLog> getOrgFlowLogById(Long orgId);

    /**
     * 飞书审核-机构关闭
     * @param orgId
     * @param operator
     * @param reason
     */
    void orgCloseReview(Long orgId, String operator, String reason);

}
