package cn.taqu.gonghui.system.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
@Data
public class SysRoleDto {

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色权限标识符
     */
    private String roleKey;

    /**
     * 业务类型（1-直播，2-趣聊，3-聊天室...）
     */
    private Integer type;

    /**
     * 状态（1-停用，0-正常）
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    private String createBy;

    private String updateBy;


    /**
     * 角色关联菜单列表
     */
    private List<Long> menuIdList;
}
