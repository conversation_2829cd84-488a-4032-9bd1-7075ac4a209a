package cn.taqu.gonghui.system.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/5/10
 */
@Data
public class InvitationRecordVo {


    @JsonAlias("host_uuid")
    private String hostUuid;

//    @JsonAlias("live_no")
//    private String liveNo;
//
//    @JsonAlias("businessman_uuid")
//    private String businessmanUuid;
//
//    @JsonAlias("consortia_id")
//    private Long consortiaId;
//
//    private String remark;
//
//    private Integer status;
//
//    @JsonAlias("create_time")
//    private Long createTime;
//
//    //@JsonAlias("update_time")
//    private Long updateTime;
//
//    @JsonAlias("oper_uuid")
//    private String  operUuid;

}
