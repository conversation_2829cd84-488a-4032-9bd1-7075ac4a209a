package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.vo.req.ResetHostReq;
import cn.taqu.gonghui.system.dto.HostChangeInfoDTO;
import cn.taqu.gonghui.system.dto.HostLastProfitDTO;
import cn.taqu.gonghui.system.dto.HostSharingProfitDto;
import cn.taqu.gonghui.system.dto.SharingProfitRecordDto;
import cn.taqu.gonghui.system.entity.BackstageOperateLog;
import cn.taqu.gonghui.system.entity.HostSharingProfitRecord;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.search.SharingProfitRecordSearch;
import cn.taqu.gonghui.system.vo.HostSharingProfitRecordVo;
import cn.taqu.gonghui.system.vo.SharingResultVo;

import java.util.List;
import java.util.Map;

public interface HostSharingProfitRecordService {

    void changeSharingProfitByHost(HostSharingProfitDto dto);

    Map<String,String> hostConfirmSharingRate(SharingProfitRecordDto dto);

    List<HostSharingProfitRecordVo> manageList(SharingProfitRecordSearch search);

    List<HostSharingProfitRecordVo> clientList(SharingProfitRecordSearch search);

    SharingResultVo detail(Long id,String hostUuid);

    void autoExpire();

    /**
     * 重置分润
     * @param resetHostReq
     */
    List<String> resetProfit(ResetHostReq resetHostReq);

    /**
     * 执行
     * @param teamHost
     */
    void runResetProfit(TeamHost teamHost);

    /**
     * 获取主播重置修改信息
     * @param hostUuid
     * @return
     */
    HostChangeInfoDTO getResetChangeInfo(String hostUuid);

    /**
     * 获取上次分润比例
     * @param hostUuid
     * @return
     */
    HostLastProfitDTO getLastProfit(String hostUuid);

    /**
     * 获取是否灰度用户首次
     * @param hostUuid
     * @return
     */
    Integer getHostGrayFirst(String hostUuid);
}
