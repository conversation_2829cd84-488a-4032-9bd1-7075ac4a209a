package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.FeishuAuditRelation;
import cn.taqu.gonghui.system.entity.Organization;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public interface FeishuAuditRelationMapper extends BaseMapper<FeishuAuditRelation> {

    /**
     * 根据审核定义code和实例code获取绑定信息
     * @param approvalCode
     * @param instanceCode
     * @return
     */
    FeishuAuditRelation getByApproval(String approvalCode, String instanceCode);
}
