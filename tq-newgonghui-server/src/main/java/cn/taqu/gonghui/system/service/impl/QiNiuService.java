package cn.taqu.gonghui.system.service.impl;


import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.LoggerUtils;
import cn.taqu.gonghui.soa.PictureService;
import cn.taqu.gonghui.system.vo.QiNiuVo;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class QiNiuService {
    private static final Integer DEFAULT_APPCODE = 1;
    private static final String PICTURE = "[图片系统]";

    @SoaReference(application = "picture",value = "picture")
    private PictureService pictureService;

    public QiNiuVo getUpdateToken(String bucket) {
        QiNiuVo uploadToken = pictureService.getUploadToken(bucket, 1, 1);
        log.info("获取七牛updateToken成功");
        return uploadToken;
    }

    public QiNiuVo getUpdateVideoToken(String type, String source) {
        QiNiuVo qiNiuVo = pictureService.getUploadTokenByMediaType(type, null, source, null, 1);
        log.info("获取七牛videoUpdateToken成功");
        return qiNiuVo;
    }
    /**
     * 私有空间下载地址
     *
     * @return
     */
    public Object privateDownloadUrl(String bucket, String baseUrl) {

        String downloadUrl = "";
        String service = "picture";
        String fn = "privateDownloadUrl";
        Object[] data = {bucket, baseUrl, DEFAULT_APPCODE};
        try {
            SoaClient soaClient = SoaClientFactory.create(SoaServer.JAVA.PICTURE);
            SoaResponse response = soaClient.post().call(service, fn, data);
            if (response.fail()) {
                log.error(LoggerUtils.soaRequestFail(PICTURE, service, fn, response.getCode(), response.getMsg()));
            } else {
                log.info("privateDownloadUrl~~~={}", JSON.toJSON(response));
                downloadUrl = response.getData();
            }
        } catch (Exception e) {
            log.error("soa qiniu/privateDownloadUrl error.", e);
        }

        return downloadUrl;
    }

}
