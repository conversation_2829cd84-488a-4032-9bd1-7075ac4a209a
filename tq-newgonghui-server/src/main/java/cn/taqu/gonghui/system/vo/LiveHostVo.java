package cn.taqu.gonghui.system.vo;

import lombok.Data;

@Data
public class LiveHostVo {
    /**
     * 经纪人uuid
     */
    private String businessman_uuid;
    /**
     * 经纪人姓名
     */
    private String businessman_name;
    /**
     * 主播uuid
     */
    private String host_uuid;

    /**
     * 他趣id(直播号)
     */
    private String live_no;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 昵称
     */
    private String nickName;
    /**
     * 机构id
     */
    private String consortia_id;
    /**
     * 机构名称
     */
    private String consortia_name;
    /**
     * 鲜花
     */
    private String flower;
    /**
     * 时长
     */
    private String total_live_time;
    /**
     * 观看人数
     */
    private String viewer;
    /**
     * 送礼人数
     */
    private String send;
    /**
     * 粉丝
     */
    private String fans;
    /**
     * 消息数
     */
    private String message;
    /**
     * 直播状态
     */
    private String live_status;
    /**
     * 有效天数
     */
    private String valid_live;
    /**
     * 收益(趣豆)
     */
    private String amount;

    /**
     * 团队id
     */
    private String team_id;

    /**
     * 团队名称
     */
    private String team_name;

    /**
     * 游戏收入
     */
    private Integer game_amount;
    /**
     * 游戏收入占比
     */
    private String game_amount_ratio;

    private String total_score;

    private Integer shell_amount;

    private String shell_ratio;

    //新增主播相关
    private String host_amount; //收益
    private String valid_day; //有效天数
    private String card_id; //身份证
    private String add_consortia_time; //加入公会时间
}
