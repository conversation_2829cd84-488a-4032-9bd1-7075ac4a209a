package cn.taqu.gonghui.system.entity;

import lombok.Data;

@Data
public class HostSharingProfitRecord {

    /**
     * 流水号
     */
    private Long id;

    /**
     * 主播uuid
     */
    private String hostUuid;

    /**
     * 用户端操作人uuid
     * 管理端操作人名称
     */
    private String accountUuid;

    /**
     * 操作类型（1-用户端，2-管理端）
     */
    private Integer type;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 状态（1-待确认，2-已同意调整、待生效，3-已生效，4-已拒绝）
     */
    private Integer status;

    /**
     * 主播当前分润比例
     */
    private String currentSharingProfitRate;

    /**
     * 主播调整后分润比例
     */
    private String newSharingProfitRate;

    /**
     * 调整时间
     */
    private Long changeTime;

    /**
     * 生效时间
     */
    private Long effectiveTime;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 暂存主播类型
     */
    private Integer tmpHostType;
}
