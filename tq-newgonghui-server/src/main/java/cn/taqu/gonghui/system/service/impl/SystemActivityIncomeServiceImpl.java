package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.soa.GonghuiService;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.search.SystemActivityIncomeSearch;
import cn.taqu.gonghui.system.service.*;
import cn.taqu.gonghui.system.vo.RoleVo;
import cn.taqu.gonghui.system.vo.SystemActivityIncomeVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SystemActivityIncomeServiceImpl implements SystemActivityIncomeService {


    @SoaReference(application = "liveV1",value = "liveV1")
    private GonghuiService gonghuiService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private TeamEmployeeService teamEmployeeService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private TeamHostService teamHostService;

    @Override
    public List<Map<String, Object>>  getSystemProfitServiceList(SystemActivityIncomeSearch search, Integer page){
         checkParam(search);
         if(search.getTeamId() != null && search.getOrgId() == null){
             throw new ServiceException("param_error","请先选择机构");
         }
         List<Long> teamIds = null;
         List<String> hostUuids = null;
        if(search.getOrgId() !=null){
             teamIds = teamService.selectTeamList(search.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue()).stream().map(Team::getTeamId).collect(Collectors.toList());
             if(search.getTeamId() != null && !teamIds.contains(search.getTeamId())){
                 throw new ServiceException("param_error","所选团队不再所选的机构中，请重新选择！");
             }
             if(CollectionUtils.isEmpty(teamIds)){
                 return new ArrayList<>();
             }
         }
         if(search.getTeamId() != null){
             teamIds = new ArrayList<>();
             teamIds.add(search.getTeamId());
         }
         if(StringUtils.isNotBlank(search.getAgenterUuid())){
             TeamEmployee oneByUserId = teamEmployeeService.getById(Long.valueOf(search.getAgenterUuid()));
             if(oneByUserId == null){
                 throw new ServiceException("param_error","当前经纪人不存在");
             }
             hostUuids = teamHostMapper.getHostUuidsByAgenter(oneByUserId.getEmployeeId());
            if(StringUtils.isNotBlank(search.getHostUuid()) && !hostUuids.contains(search.getHostUuid()) ){
                return new ArrayList<>();
            }
         }
         if(StringUtils.isNotBlank(search.getHostUuid())){

             TeamHost teamHost = getHostByHostUuidAndType(search.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
             if(teamHost == null){
                 log.info("getSystemProfitServiceList-》主播不存在,uuid:{}",search.getHostUuid());
                 return new ArrayList<>();
             }
             if(search.getOrgId()!=null && !search.getOrgId().equals(teamHost.getOrgId())){
                 return new ArrayList<>();
             }
             if(search.getTeamId()!=null && !search.getTeamId().equals(teamHost.getTeamId())){
                 return new ArrayList<>();
             }

             hostUuids = Arrays.asList(search.getHostUuid());
         }
        List<Map<String, Object>> list = gonghuiService.getSystemActivityIncomeStat(search.getStartTime(), search.getEndTime(), page, teamIds, hostUuids, search.getApplyLevel(), search.getLiveNo(), search.getExport());
        return covertResult(list);
    }


    private void checkParam(SystemActivityIncomeSearch search){
        log.info("getSystemProfitServiceList:{}", JsonUtils.objectToString(search));
        if(search.getStartTime() == null){
            throw new ServiceException("param_error","请选择开始时间");
        }
        if(search.getEndTime() == null){
            throw new ServiceException("param_error","请选择结束时间");
        }
        if(search.getExport() == null){
           search.setExport(0);
        }
        int betweenDays = getBetweenDays(search.getStartTime() * 1000, search.getEndTime() * 1000);
        if(betweenDays > 40){
            throw new ServiceException("param_error","统计周期最长40天以内");
        }
    }

    @Override
    public List<Map<String, Object>> getSystemProfitServiceListForUser(SystemActivityIncomeSearch search, Integer page){
        checkParam(search);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        RoleVo currentRole = getCurrentRole(loginUser.getUser());
        List<Long> teamIds = null;
        List<String> hostUuids = null;
       if(UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())){
           teamIds = teamService.selectTeamList(loginUser.getUser().getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue()).stream().map(Team::getTeamId).collect(Collectors.toList());
       }
        if(UserTypeEnum.LEADER.getCode().equals(currentRole.getRoleKey())){
            SysUser sysUser = sysUserService.selectUserByUserId(loginUser.getUser().getUserId());
            if(sysUser == null){
                throw new ServiceException("param_error","当前用户不存在");
            }
            TeamEmployee oneByUserId = teamEmployeeService.getOneByUserIdAndType(sysUser.getUserId(),TeamTypeEnum.LIVE_TEAM.getValue());
            if(oneByUserId == null){
                throw new ServiceException("param_error","当前用户不是团队成员");
            }
            teamIds = new ArrayList<>();
            teamIds.add(oneByUserId.getTeamId());
        }
        if(UserTypeEnum.AGENTER.getCode().equals(currentRole.getRoleKey())){
            SysUser sysUser = sysUserService.selectUserByUserId(loginUser.getUser().getUserId());
            if(sysUser == null){
                throw new ServiceException("param_error","当前经纪人不存在");
            }
            TeamEmployee oneByUserId = teamEmployeeService.getOneByUserIdAndType(sysUser.getUserId(),TeamTypeEnum.LIVE_TEAM.getValue());
            if(oneByUserId == null){
                throw new ServiceException("param_error","当前经纪人不存在");
            }
            hostUuids = teamHostMapper.getHostUuidsByAgenter(oneByUserId.getEmployeeId());
        }


        if(UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())){
            if(search.getTeamId() != null ){
                teamIds = new ArrayList<>();
                teamIds.add(search.getTeamId());
            }
        }
        if(UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey()) || UserTypeEnum.LEADER.getCode().equals(currentRole.getRoleKey())){
            if(StringUtils.isNotBlank(search.getAgenterUuid())){
                SysUser sysUser = sysUserService.selectUserByAccountUuid(search.getAgenterUuid());
                if(sysUser == null){
                    throw new ServiceException("param_error","当前经纪人不存在");
                }
                TeamEmployee oneByUserId = teamEmployeeService.getOneByUserIdAndType(sysUser.getUserId(),TeamTypeEnum.LIVE_TEAM.getValue());
                if(oneByUserId == null){
                    throw new ServiceException("param_error","当前经纪人不存在");
                }
                hostUuids = teamHostMapper.getHostUuidsByAgenter(oneByUserId.getEmployeeId());
            }
        }
        List<Map<String, Object>> list = gonghuiService.getSystemActivityIncomeStat(search.getStartTime(), search.getEndTime(), page, teamIds, hostUuids, search.getApplyLevel(), search.getLiveNo(), search.getExport());
        return covertResult(list);
    }


    private List<Map<String, Object>>covertResult(List<Map<String, Object>> list){
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        for(Map map: list){
            map.put("teamName",getTeamNameByHostUuid(MapUtils.getString(map,"host_uuid")));
        }
        return list;
    }

    private String getTeamNameByHostUuid(String hostUuid){
        if(StringUtils.isBlank(hostUuid)){
            return "";
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("host_uuid",hostUuid);
        queryWrapper.eq("team_type",TeamTypeEnum.LIVE_TEAM.getValue());
        TeamHost teamHost = teamHostService.getOne(queryWrapper);
         if(teamHost == null){
             return "";
         }
       return teamService.detail(teamHost.getTeamId()).getTeamName();
    }
    private TeamHost getHostByHostUuidAndType(String hostUuid,Integer type){
        if(StringUtils.isBlank(hostUuid)){
            return null;
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("host_uuid",hostUuid);
        queryWrapper.eq("team_type",type);
        return teamHostService.getOne(queryWrapper);
    }


    private RoleVo getCurrentRole(SysUser user){
        RoleVo roleVo = sysUserService.getRoleByAccountUuid(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
        if (null == roleVo) {
            throw new ServiceException(CodeStatus.ROLE_ERROR.value(),"当前用户尚未分配角色");
        }
        return roleVo;
    }
    public static int getBetweenDays(long startTime, long endTime) {
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(new Date(startTime));
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(new Date(endTime));
        int startDay = startCal.get(6);
        int endDay = endCal.get(6);
        int startYear = startCal.get(1);
        int endYear = endCal.get(1);
        if (startYear == endYear) {
            return endDay - startDay;
        } else {
            int timeDistance = 0;

            for(int i = startYear; i < endYear; ++i) {
                if ((i % 4 != 0 || i % 100 == 0) && i % 400 != 0) {
                    timeDistance += 365;
                } else {
                    timeDistance += 366;
                }
            }
            return timeDistance + (endDay - startDay);
        }
    }

}
