package cn.taqu.gonghui.system.dto;

import cn.hutool.core.date.DateUtil;
import lombok.Data;

/**
 * <AUTHOR> Wu.D.J
 */
@Data
public class WeddingHostInfoDto {
    // 婚礼UUID
    private String wedding_uuid;

    // 房间UUID
    private String chat_uuid;

    // 婚礼开始时间
    private Long wedding_start_time;

    // 婚礼结束时间
    private Long wedding_end_time;

    // 婚礼时长
    private Integer wedding_duration;

    // 司仪UUID
    private String host_uuid;

    // 婚礼状态(1-进行中/2-未开始/3-结束/4-取消)
    private Integer wedding_status;

    // 婚礼开启状态(0-婚礼未开始/1-成功开启/2-开启失败)
    private Integer wedding_open;

    // 司仪是否到场(-1-不展示/0-缺勤/1-正常签到/2-迟到)
    private Integer host_on_site;

    // 司仪连麦时长
    private Integer host_meeting_time;

    // 房间送礼流水
    private Integer chat_cost;

    // 司仪收礼流水
    private Integer host_income;

    // 是否可以更换司仪(0-否/1-是)
    private Integer change_host;

    // 婚礼彩礼
    private Integer bride_amount;

    private String wedding_time_str;

    private String wedding_status_str;

    private String host_on_site_str;

    private Integer distribution_status	;//司仪分配状态 0-原司仪 1-新司仪
    private String consortia_name;//	公会名称

    public void showTransform() {
        this.wedding_time_str = DateUtil.date(wedding_start_time).toString("yyyy-MM-dd HH:mm:ss") + " ~ " + DateUtil.date(wedding_end_time).toString("yyyy-MM-dd HH:mm:ss");
        this.wedding_status_str = translateWeddingStatus();
        this.host_on_site_str = translateHostOnSite();
    }

    private String translateHostOnSite() {
        if (host_on_site == 1) return "缺勤";
        if (host_on_site == 2) return "正常签到";
        if (host_on_site == 3) return "迟到";
        return "";
    }

    private String translateWeddingStatus() {
        if (wedding_status == 1) return "进行中";
        if (wedding_status == 2) return "未开始";
        if (wedding_status == 3) return "结束";
        if (wedding_status == 4) return "取消";
        return "";
    }
}

