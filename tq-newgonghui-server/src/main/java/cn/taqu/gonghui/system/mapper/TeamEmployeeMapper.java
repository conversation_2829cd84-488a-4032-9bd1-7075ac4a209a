package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.search.TeamEmployeeSearch;
import cn.taqu.gonghui.system.vo.CommonVo;
import cn.taqu.gonghui.system.vo.TeamEmployeeVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */

public interface TeamEmployeeMapper extends BaseMapper<TeamEmployee> {

    IPage<TeamEmployeeVo> selectTeamEmployeeList(IPage<TeamEmployeeVo> page, @Param("search") TeamEmployeeSearch search);


    void updateEmployeeName(@Param("employeeId") Long employeeId,@Param("employeeName") String employeeName, Integer doubleWrite);


    List<TeamEmployeeVo> selectLeaderList(@Param("orgId") Long orgId,@Param("type") Integer type);

    List<TeamEmployeeVo> selectAgentList(Integer type);

    List<TeamEmployeeVo> selectAgentListByOrgId(@Param("orgId") Long orgId,@Param("type") Integer type);


    TeamEmployee selectInviteCode(@Param("inviteCode") String inviteCode);

    TeamEmployee selectMobile(@Param("mobile") String mobile,@Param("type") Integer type, @Param("selectByDigest") Integer selectByDigest);

    /**
     * 获取团队下的成员id
     */
    List<Long> employeeIdsByTeamId(Long teamId);

    /**
     * 将团队下所有成员归属到默认团队
     */
    void employeeBelongToDefaultTeam(@Param("employeeIds") List<Long> employeeIds,@Param("teamId") Long teamId);

    /**
     * 根据团队id查询经纪人下拉tree
     */
    List<TeamEmployee> getAgenterTree(Long teamId);

    /**
     * 查询用户角色为经纪人的经纪人下拉tree
     */
    List<TeamEmployee> getAgentTreeByAgent(@Param("type") Integer type,@Param("accountUuid") String accountUuid);

    /**
     * 获取用户角色为管理员的经纪人下拉tree
     * @param type
     * @param orgId
     * @return
     */
    List<TeamEmployee> getAgentTreeByManager(@Param("type") Integer type,@Param("orgId") Long orgId);


    List<TeamEmployee> getByOrgId(@Param("orgId") Long orgId);

    /**
     * 获取当前用户所属团队
     * @return
     */
    Long teamIdByAccountUuid(@Param("accountUuid")String accountUuid,@Param("type")Integer type);

    /**
     * 根据uuid 和type查询employee
     * @param accountUuid
     * @param type
     * @return
     */
    TeamEmployee selectByUuid(@Param("accountUuid")String accountUuid, @Param("type")Integer type);

    /**
     * 获取团队下成员数量
     * @return
     */
    List<CommonVo> membersByTeamId(List<Long> teamIds);

    /**
     * 获取团队下主播数量
     * @return
     */
    List<CommonVo> hostsByTeamId(@Param("teamIds")List<Long> teamIds);

    /**
     * 获取经纪人下主播数量
     */
    List<CommonVo> hostsByEmployeeId(@Param("employeeIds") List<Long> employeeIds,@Param("teamType")Integer teamType);

    /**
     * 根据employeeId获取accountUuid
     */
    String accountUuidByEmployeeId(Long employeeId);

    /**
     * 根据uuid获取employeeId
     */
    Long seletEmployeeId(@Param("accountUuid") String accountUuid,@Param("type")Integer type);

    /**
     * 一次性修改相关机构名称
     * @param orgId
     */
    void updateOrgName(@Param("orgName") String orgName,@Param("orgId") Long orgId);

    List<TeamEmployee> seletEmployeeByUserIdAndType(@Param("userId") Long userId,@Param("type") Integer type);

    // -----------------------------  以下是数据迁移需要用到的接口 -----------------------------
    void deleteByOrgId(Long orgId);

    void batchDeleteByIds(Set<Long> idSet);

    /**
     * 根据用户id查询
     * @param userId
     * @return
     */
    TeamEmployee selectByUserId(Long userId);


    void updateClearTxtByRange(@Param("curStartId") Long curStartId, @Param("curEndId") Long curEndId);
}
