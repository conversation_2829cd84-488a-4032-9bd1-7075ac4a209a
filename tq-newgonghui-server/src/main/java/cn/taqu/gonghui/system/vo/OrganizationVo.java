package cn.taqu.gonghui.system.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.persistence.Column;

@Data
public class OrganizationVo {

    @Column(name = "org_uuid")
    private String orgUuid; //机构uuid

    @Column(name = "account_uuid")
    private String accountUuid;

    @Column(name = "org_name")
    private String orgName; //机构名称

    @Column(name = "charge_person")
    private String chargePerson; //负责人名字

    @Column(name = "charge_person_id_card")
    private String chargePersonIdCard; //负责人身份证

    @Column(name = "charge_person_phone")
    private String chargePersonPhone; //负责人联系电话

    @Column(name = "charge_person_email")
    private String chargePersonEmail; //负责人邮箱

    @Column(name = "charge_person_vx")
    private String chargePersonVx; //负责人微信

    @Column(name = "charge_person_birthday")
    private Long chargePersonBirthday; //负责人出生日期

    @Column(name = "receiving_address")
    private String receivingAddress; //收件地址

    @Column(name = "legal_person")
    private String legalPerson; //法人姓名
    @Column(name = "legal_person_id_card")
    private String legalPersonIdCard; //法人身份证

    @Column(name = "business_person")
    private String businessPerson; //业务对接人的姓名

    @Column(name = "public_receiving_bank_account")
    private String publicReceivingBankAccount; //对公收款账号

    @Column(name = "account_name")
    private String accountName; //开户名

    @Column(name = "account_bank_name")
    private String accountBankName; //开户行

    @Column(name = "province")
    private String province;

    @Column(name = "province_id")
    private Integer provinceId;

    @Column(name = "city")
    private String city;

    @Column(name = "city_id")
    private Integer cityId;

    @Column(name = "sub_branch_name")
    private String subBranchName; //支行

    @Column(name = "org_status")
    private Integer orgStatus; //机构状态 1开启 2关闭

    @Column(name = "apply_status")
    private Integer applyStatus; //1待审核 2审核通过 3审核拒绝

    @Column(name = "business_license_url")
    private String businessLicenseUrl; //营业执照url

    @Column(name = "opening_permit_url")
    private String openingPermitUrl; //开户许可证url

    @Column(name = "legal_person_url")
    private String legalPersonUrl; //法人信息身份证, url1,url2,url3   正面,反面,手持

    @Column(name = "charge_person_url")
    private String chargePersonUrl; //负责人信息身份证, url1,url2,url3   正面,反面,手持

    @Column(name = "org_cooperation_flow_url")
    private String orgCooperationFlowUrl; //流水截图

    @Column(name = "host_screenshot_url")
    private String hostScreenshotUrl; //主播截图

    @Column(name = "form_status")
    private Integer formStatus;

    @Column(name = "check")
    private Boolean check;


    //20200520新增的加密字段
    @JsonIgnore
    @Column(name = "charge_person_phone_cipher")
    private String chargePersonPhoneCipher;
    @JsonIgnore
    @Column(name = "charge_person_email_cipher")
    private String chargePersonEmailCipher;

    @JsonIgnore
    @Column(name = "charge_person_cipher")
    private String chargePersonCipher;

    @JsonIgnore
    @Column(name = "charge_person_vx_cipher")
    private String chargePersonVxCipher;

    @JsonIgnore
    @Column(name = "charge_person_id_card_cipher")
    private String chargePersonIdCardCipher;

    @JsonIgnore
    @Column(name = "receiving_address_cipher")
    private String receivingAddressCipher;

    @JsonIgnore
    @Column(name = "legal_person_cipher")
    private String legalPersonCipher;

    @JsonIgnore
    @Column(name = "legal_person_id_card_cipher")
    private String legalPersonIdCardCipher;

    @JsonIgnore
    @Column(name = "public_receiving_bank_account_cipher")
    private String publicReceivingBankAccountCipher;

    @JsonIgnore
    @Column(name = "account_name_cipher")
    private String accountNameCipher;

    @JsonIgnore
    @Column(name = "account_bank_name_cipher")
    private String accountBankNameCipher;

    @JsonIgnore
    @Column(name = "sub_branch_name_cipher")
    private String subBranchNameCipher;

    @JsonIgnore
    @Column(name = "business_person_cipher")
    private String businessPersonCipher;

}
