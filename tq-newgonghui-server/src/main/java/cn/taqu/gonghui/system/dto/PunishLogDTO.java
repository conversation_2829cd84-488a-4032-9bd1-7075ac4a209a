package cn.taqu.gonghui.system.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/08
 */
@Data
public class PunishLogDTO {

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 业务id
     */
    private String bizId;

    /**
     * 他趣uuid
     */
    private String accountUuid;

    /**
     * 应用appCode
     */
    private Integer appCode;

    /**
     * 依据
     */
    private String basis;

    /**
     * 依据类型
     */
    private String basisType;

    /**
     * 操作人名称
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 处罚id
     */
    private Long punishId;

    /**
     * 国家
     */
    private String country;

    /**
     * 集群标识
     */
    private Integer clusterId;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 账户id
     */
    private String accountId;

    /**
     * 内部理由
     */
    private String inReason;

    /**
     * 对外理由
     */
    private String outReason;

    /**
     * 公会id
     */
    private String guildId;

    /**
     * 团队id
     */
    private String groupId;

    /**
     * 经纪人id
     */
    private String agent;

    /**
     * 罚单开始时间
     */
    private Long punishStartTime;

    /**
     * 罚单结束时间
     */
    private Long punishEndTime;

    /**
     * 是否撤销数据
     */
    private Boolean isRevoke;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 每页数
     */
    private Integer pageSize;

    /**
     * 页数
     */
    private Integer pageNo;

    /**
     * 类型 10警告 stop处罚
     */
    private String punishType;

    /**
     * 是否是公会
     */
    private Integer isGh;

    /**
     * 处罚状态
     */
    private String status;

}
