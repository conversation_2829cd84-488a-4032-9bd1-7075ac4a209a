package cn.taqu.gonghui.system.service;

import cn.taqu.core.jdbc.pagehelper.PageRequest;
import cn.taqu.core.jdbc.pagehelper.PageResult;
import cn.taqu.gonghui.chatroom.search.ChatRoomSearch;
import cn.taqu.gonghui.chatroom.vo.ChatVo;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.vo.*;
import cn.taqu.gonghui.common.vo.req.HostInfoReq;
import cn.taqu.gonghui.common.vo.req.InviteHostReq;
import cn.taqu.gonghui.common.vo.req.ResetHostReq;
import cn.taqu.gonghui.common.vo.res.InviteHostDetailRes;
import cn.taqu.gonghui.common.vo.res.InviteHostItemRes;
import cn.taqu.gonghui.system.dto.*;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.search.HostStatisticSearch;
import cn.taqu.gonghui.system.search.PunishSearch;
import cn.taqu.gonghui.system.search.TeamHostOperateLogSearch;
import cn.taqu.gonghui.system.search.WarnSearch;
import cn.taqu.gonghui.system.vo.CommonHostInfo;
import cn.taqu.gonghui.system.vo.TeamHostOperateLogVo;
import cn.taqu.gonghui.system.vo.TeamHostVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/5/11
 */
public interface TeamHostService extends IService<TeamHost> {

    IPage<TeamHost> findByPage(Long orgId, List<Long> teamIds, List<String> uuids, String uuid, Integer businessType, int pageNo, int pageSize, ChatRoomSearch chatRoomSearch);

    IPage<TeamHost> pageByOwnEmployee(Integer pageNO, Integer pageSize, Long employeeId, Integer teamType);
    void invitationHost(HostDto hostDto);

    void reInvitationHost(Long inviteId);

    void getApplyHostinfo(String hostUuid);

    /**
     * 添加到直播团队
     *
     * @param accountUuid
     * @param hostUuid
     * @param profit
     * @param teamId
     * @param forceSameOrgWithOtherTeam
     * @param hostType
     */
    void addTeamHost(String accountUuid, String hostUuid, Integer profit, Long teamId, Integer forceSameOrgWithOtherTeam, Integer hostType);

    List<TeamHost> getHostByHostUuid(String hostUuid, Integer teamType);

    List<TeamHostOperateLogVo> getLogList(TeamHostOperateLogSearch search);

    /**
     * 查询直播变更团队记录
     *
     * @param search
     * @return
     */
    List<TeamHostOperateLogVo> getLiveChangeTeamLogList(TeamHostOperateLogSearch search);

    Map<String, String> getHostSharingProfitInfo(List<String> uuidList);


    void changeTeamOrAgenter(HostTeamOrAgentDto dto);

    void batchChangeTeamOrAgenter(HostTeamOrAgentDto dto);

    void updateSharingProfitRateCron();

    LiveHostSelfStatisticVo getLiveHostSelfByAgent(TeamHost teamHost);

    HostVo getLiveHostListUser(HostStatisticSearch search);

    void updateHostInfo(Long teamId, String hostUuid);

    void changeHostTeam(Long teamId, List<String> hostUuids);

    List<TeamHost> getListByEmployeeId(Long employeeId, Integer teamType);

    void updateBatchEmpIdById(Set<Long> ids);

    void moveHost(HostTeamOrAgentDto dto);

    void punishHost(String hostUuid, Integer publishWay, Integer publishHour, String reason);

    void warnHost(String hostUuid, String reason);

    Map<String, Object> getPunishList(PunishSearch search, Integer page, Integer pageSize);

    Map<String, Object> getWarnList(WarnSearch search, Integer page, Integer pageSize);

    Map<String, Object> getWarnReasonList();

    TeamHost getHostByUuidAndType(String uuid, Integer type);

    List<TeamHost> getBatchHostByUuidsAndType(List<String> uuids, Integer type);

    void saveTeamHost(TeamHost teamHost);

    List<TeamHost> getBatchHostType(Integer type);

    List<ChatVo> findChatListByPage(Integer pageNo, Integer pageSize);

    /**
     * 计算团队下的艺人数量
     * @param teamId
     * @return
     */
    Integer countTeamHostInTeamId(Long teamId);

    Map<String, TeamHostVo> getHostOrgInfo(List<String> uuids);

    /**
     * 退会申请权限校验
     *
     * @param hostUuid
     * @return
     */
    AppValidVO quitGuildAuth(String hostUuid);

    /**
     * 获取退会申请信息
     * @param hostUuid
     * @return
     */
    AppQuitGuildBtnVO getQuitGuildInfo(String hostUuid);

    /**
     * 申请退会
     * @param hostUuid
     * @param type
     * @param reason
     */
    void applyQuitGuild(String hostUuid, Integer type, String reason);

    /**
     * 获取用户信息
     * @param hostUuid
     * @return
     */
    CommonHostInfo singleHostInfo(String hostUuid);

    /**
     * 获取退会申请列表
     * @param approvalFlow
     * @param pageRequest
     * @return
     */
    PageInfo<ApprovalFlowItem> getQuitGuildPageList(ApprovalFlow approvalFlow, PageRequest pageRequest);

    /**
     * 获取退出公会流水
     * @param flowId
     * @return
     */
    List<ApprovalFlowLogItem> getQuitGuildLog(Integer flowId);

    /**
     * 中转公会
     * @param hostUuid
     */
    void transferGuild(String hostUuid);

    /**
     * 聊天室入会邀请
     * @param chatRoomInviteVO
     * @param reFlag
     */
    void chatRoomInvite(ChatRoomInviteVO chatRoomInviteVO, Boolean reFlag);

    /**
     * 聊天室邀请入会详情
     * @param inviteHostReq
     * @return
     */
    InviteHostDetailRes getChatRoomInviteDetail(InviteHostReq inviteHostReq);

    /**
     * 入会审核
     * @param inviteHostReq
     */
    void chatRoomInviteReview(InviteHostReq inviteHostReq);

    /**
     * 艺人邀请记录列表
     * @param approvalFlow
     * @param pageRequest
     * @return
     */
    PageInfo<InviteHostItemRes> getInvitePageList(ApprovalFlow approvalFlow, PageRequest pageRequest);

    /**
     * 重新邀约
     * @param chatRoomInviteVO
     */
    void reInvite(ChatRoomInviteVO chatRoomInviteVO);

    /**
     * 设置主播为团播
     * @param hostUuid
     * @param isGroup
     */
    void setHostIsGroup(String hostUuid, Integer isGroup);

    /**
     * 批量中转公会
     * @param resetHostReq
     * @return
     */
    List<String> batchTransferGuild(ResetHostReq resetHostReq);

    /**
     * 获取不同环境 素人团队id 中转团队id
     * @param type
     * @return
     */
    Long getEnvTeamId(Integer type);

	/*
     * 获取主播信息
     * @param hostUuid
     * @param teamType
     * @return
     */
    HostInfoDto getHostInfo(String hostUuid, Integer teamType);

    /**
     * 获取登录用户所能查看的用户权限 旗下团队 uuid
     * @return
     */
    UserAuthVO getUserAuthUuids();

    /**
     * 检查用户修改权限
     * @param hostUuid
     */
    void checkUserChangeAuth(String hostUuid);

    /**
     * 批量获取主播信息
     * @param req
     * @return
     */
    List<HostOrgTeamDto> multiHostInfo(List<HostInfoReq> req);

    Map<String, Long> mapEmployeeId(List<String> uuidList, int value);
}
