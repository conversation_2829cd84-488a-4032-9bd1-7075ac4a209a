package cn.taqu.gonghui.system.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class FrameDetail implements Serializable {

    @JSONField(name = "host_info")
    private HostInfoDTO hostInfo;
    @JSONField(name = "vs_host_info")
    private VsHostInfoDTO vsHostInfo;

    @NoArgsConstructor
    @Data
    public static class HostInfoDTO {
        @JSONField(name = "top_info")
        private TopInfoDTO topInfo;
        @JSONField(name = "live_info")
        private LiveInfoDTO liveInfo;
        @JSONField(name = "true_info")
        private TrueInfoDTO trueInfo;

        @NoArgsConstructor
        @Data
        public static class TopInfoDTO {
            @JSONField(name = "avatar")
            private String avatar;
            @JSONField(name = "nickname")
            private String nickname;
            @JSONField(name = "card_id")
            private Object cardId;
            @JSONField(name = "businessman_uuid")
            private String businessmanUuid;
        }

        @NoArgsConstructor
        @Data
        public static class LiveInfoDTO {
            @JSONField(name = "live_time")
            private Integer liveTime;
            @JSONField(name = "receive_gift")
            private Integer receiveGift;
            @JSONField(name = "sender_num")
            private Integer senderNum;
            @JSONField(name = "viewer_num")
            private Integer viewerNum;
            @JSONField(name = "follower_add_num")
            private Integer followerAddNum;
            @JSONField(name = "follower_num")
            private String followerNum;
            @JSONField(name = "warn_times_today")
            private Integer warnTimesToday;
            @JSONField(name = "stop_times_today")
            private Integer stopTimesToday;
        }

        @NoArgsConstructor
        @Data
        public static class TrueInfoDTO {
            @JSONField(name = "real_name")
            private String realName;
            @JSONField(name = "age")
            private Integer age;
            @JSONField(name = "apply_level")
            private Object applyLevel;
            @JSONField(name = "identity_no")
            private String identityNo;
            @JSONField(name = "cover_url")
            private String coverUrl;
        }
    }

    @NoArgsConstructor
    @Data
    public static class VsHostInfoDTO {
        @JSONField(name = "top_info")
        private TopInfoDTO topInfo;
        @JSONField(name = "live_info")
        private LiveInfoDTO liveInfo;
        @JSONField(name = "true_info")
        private TrueInfoDTO trueInfo;

        @NoArgsConstructor
        @Data
        public static class TopInfoDTO {
            @JSONField(name = "avatar")
            private String avatar;
            @JSONField(name = "nickname")
            private String nickname;
            @JSONField(name = "card_id")
            private Object cardId;
            @JSONField(name = "businessman_uuid")
            private String businessmanUuid;
        }

        @NoArgsConstructor
        @Data
        public static class LiveInfoDTO {
            @JSONField(name = "live_time")
            private Integer liveTime;
            @JSONField(name = "receive_gift")
            private Integer receiveGift;
            @JSONField(name = "sender_num")
            private Integer senderNum;
            @JSONField(name = "viewer_num")
            private Integer viewerNum;
            @JSONField(name = "follower_add_num")
            private Integer followerAddNum;
            @JSONField(name = "follower_num")
            private String followerNum;
            @JSONField(name = "warn_times_today")
            private Integer warnTimesToday;
            @JSONField(name = "stop_times_today")
            private Integer stopTimesToday;
        }

        @NoArgsConstructor
        @Data
        public static class TrueInfoDTO {
            @JSONField(name = "real_name")
            private String realName;
            @JSONField(name = "age")
            private Integer age;
            @JSONField(name = "apply_level")
            private Object applyLevel;
            @JSONField(name = "identity_no")
            private String identityNo;
            @JSONField(name = "cover_url")
            private String coverUrl;
        }
    }
}
