package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-11 13:49
 */
@Accessors(chain = true)
@Data
@TableName("backstage_operate_log")
@AllArgsConstructor
@NoArgsConstructor
public class BackstageOperateLog {

    /**
     * 主键id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作内容
     */
    private String info;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 操作类型枚举
     */
    public enum OperateTypeEnum{
        /**
         * 默认值
         */
        DEFAULT(0, "默认"),
        /**
         * 业务变更
         */
        CHANGE_BUSINESS(1, "业务变更"),
        /**
         * 业务变更导致关闭团队
         */
        CHANGE_BUSINESS_CLOSE_LIVE_TEAM(2, "业务调整关闭直播团队"),
        /**
         * 业务变更导致关闭团队
         */
        CHANGE_BUSINESS_CLOSE_CHAT_ROOM_TEAM(3, "业务调整关闭聊天室团队"),
        /**
         * 关闭直播团队
         */
        CLOSE_LIVE_TEAM(4, "关闭直播团队"),
        /**
         * 关闭聊天室团队
         */
        CLOSE_CHAT_ROOM_TEAM(5, "关闭聊天室团队"),
        /**
         * 关闭机构
         */
        CLOSE_ORG(6, "关闭机构"),
        ;
        private Integer value;

        private String msg;

        OperateTypeEnum(Integer value, String msg){
            this.value = value;
            this.msg = msg;
        }

        public Integer getValue() {
            return value;
        }

        public String getMsg() {
            return msg;
        }
    }
}
