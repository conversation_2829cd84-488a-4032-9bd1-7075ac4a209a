package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.chatroom.vo.ChatVo;
import cn.taqu.gonghui.common.vo.req.HostInfoReq;
import cn.taqu.gonghui.system.dto.HostOrgTeamDto;
import cn.taqu.gonghui.system.dto.HostTeamOrAgentDto;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.search.SharingCronSearch;
import cn.taqu.gonghui.system.vo.TeamHostVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/11
 */
public interface TeamHostMapper extends BaseMapper<TeamHost> {

    /**
     * 获取团队下所有艺人
     */
    List<Long> idsByTeamId(Long teamId);

    /**
     * 将团队下所有艺人归属到该机构的默认团队
     */
    void hostsBelongToDefaultTeam(@Param("hostIds") List<Long> hostIds, @Param("teamId") Long teamId,@Param("teamType") Integer teamType);


    /**
     * 获取团队下所有艺人
     */
    List<TeamHost> idsByAgentId(Long employeeId);
    /**
     * 获取经纪人下所有主播uuid
     */
    List<String> getHostUuidsByAgenter(Long employeeId);

    /**
     * 获取负责人下所有主播uuid
     */
    List<String> getHostUuidsByLeader(Long teamId);

    /**
     * 获取当前主播所属机构
     */
    String getOrgIdByHostUuid(String hostUuid);

    /**
     * 根据主播uuid获取一条记录
     */
    TeamHost getOneByHostUuid(@Param("hostUuid") String hostUuid,@Param("teamType")Integer teamType);

    /**
     * 调整团队或者经纪人
     */
    void updateTeamOrAgenter(HostTeamOrAgentDto dto);

    List<TeamHost> getTeamHostList(SharingCronSearch search);

    void batchUpdateHost(List<TeamHost> teamHostList);

    void updateProfitAndIsUpdate(TeamHost teamHost);

    void updateProfitAndIsUpdateAndChangeTime(TeamHost teamHost);


    /**
     * 查询某机构下所有有效的主播
     * @param teamHost
     * @return
     */
    List<TeamHost> getHostByOrgId(TeamHost teamHost);

    /**
     * 获取团队名称
     * @param uuidList
     */
    List<TeamHostVo> teamNameByHostUuidList(List<String> uuidList);

    List<TeamHostVo> orgInfoByHostUuidList(List<String> uuidList);

    /**
     * 获取机构名称
     * @param uuidList
     */
    List<TeamHostVo> orgNameByHostUuidList(List<String> uuidList);

    /**
     * 根据主播获取公会信息
     * @param hostUuid
     * @return
     */
    TeamHostVo selectTeamHostByUuid(String hostUuid);

    /**
     * 查询该团队下的所有艺人（主播）
     */
    List<String> findHostsByTeamId(Long teamId);

    /**
     *
     * @param teamHost
     */
    void updateByPrimaryKey(TeamHost teamHost);

    /**
     * 查询某机构下所有的主播
     * @param orgId
     * @return
     */
    List<TeamHost> getHostListByOrgId(Long orgId,Integer teamType);

    /**
     * 获取teamhost
     * @param hostUuid
     * @param teamType
     * @return
     */
    TeamHost selectHostOneByUuid(String hostUuid, Integer teamType);

    /**
     * 获取表中最大id值
     */
    Integer getMaxId();

    /**
     * 获取主播当前分润比例集合
     * @param uuidList
     */
    List<TeamHost> getSharingPorfitRateList(List<String> uuidList);


    // --------------------------  以下是数据迁移用到的接口  -----------------------------------
    void batchInsertHost(List<TeamHost> hostList);

    void deleteByOrgId(Long orgId);

    void deleteByTeamId(Long teamId);

    void updateByHostUuid(TeamHost teamHost);

    void insertHost(TeamHost teamHost);

    List<ChatVo> getChatListByPage();

    /**
     * 批量更新
     * @param teamHostList
     */
    void batchUpdate(List<TeamHost> teamHostList);

    /**
     * 查询待重置分润记录
     * @param num
     * @return
     */
    List<TeamHost> selectResetRow(Integer num);

    /**
     * 批量查询主播信息
     * @param reqList
     * @return
     */
    List<HostOrgTeamDto> selectListByHostAndType(List<HostInfoReq> reqList);

    void updateClearTxtByRange(@Param("curStartId") Long curStartId, @Param("curEndId") Long curEndId);
}
