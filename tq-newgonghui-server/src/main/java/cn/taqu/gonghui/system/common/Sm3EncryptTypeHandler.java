package cn.taqu.gonghui.system.common;

import cn.taqu.gonghui.common.client.EncryptDecryptClient;
import cn.taqu.gonghui.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024/7/2 09 43
 * discription
 */
@Slf4j
@Service
@MappedJdbcTypes(JdbcType.VARCHAR)
public class Sm3EncryptTypeHandler extends BaseTypeHandler<String> {

    @Resource
    private EncryptDecryptClient encryptDecryptClient;
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {

        if(StringUtils.isBlank(parameter)){
            ps.setString(i, "");
            return;
        }
        String encodedContent = encryptDecryptClient.encode(parameter);
        ps.setString(i, encodedContent);
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {

        String encodedContent = rs.getString(columnName);
        return encodedContent;
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String  encodedContent = rs.getString(columnIndex);
        return encodedContent;
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String encodedContent = cs.getNString(columnIndex);
        return encodedContent;
    }
}
