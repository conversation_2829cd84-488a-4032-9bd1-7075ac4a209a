package cn.taqu.gonghui.system.vo;

import cn.taqu.gonghui.system.entity.Team;
import lombok.Data;

@Data
public class TeamVo extends Team {
    /**
     * 所属机构名称
     */
    private String orgName;

    /**
     * 负责人名称
     */
    private String chargeName;

    /**
     * 负责人电话号码
     */
    private String chargeMobile;

    /**
     * 团队成员（工作人员+艺人）
     */
    private String teamMember;

    /**
     * 团队人数（工作人员 + 艺人）
     */
    private Integer teamNumber;

    /**
     * 团队艺人数量
     */
    private Integer number;
    /**
     * 团队艺人数量
     */
    private String inviteCode;
}
