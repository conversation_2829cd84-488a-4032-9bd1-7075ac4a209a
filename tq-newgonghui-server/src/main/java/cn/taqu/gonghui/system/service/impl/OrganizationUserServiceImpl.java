package cn.taqu.gonghui.system.service.impl;


import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.service.AccountLoginService;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ParamsCheckUtil;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.common.vo.OrganizationVo;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import cn.taqu.gonghui.system.service.AgreementInfoService;
import cn.taqu.gonghui.system.service.OrganizationService;
import cn.taqu.gonghui.system.service.OrganizationUserService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class OrganizationUserServiceImpl implements OrganizationUserService {
    private Logger log = LoggerFactory.getLogger(OrganizationUserServiceImpl.class);

    @Value("${contact.qq}")
    private String qq;

    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private HostScreenshotMapper hostScreenshotMapper;
    @Autowired
    private OrgCooperationFlowMapper orgCooperationFlowMapper;
    @Autowired
    private LegalPersonMapper legalPersonMapper;
    @Autowired
    private BusinessLicenseMapper businessLicenseMapper;
    @Autowired
    private ChargePersonMapper chargePersonMapper;
    @Autowired
    private OpeningPermitMapper openingPermitMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private AgreementInfoService agreementInfoService;
    @Autowired
    private AccountLoginService accountLoginService;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private OrganizationService organizationService;


    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> saveOrgApplyStepOne(OrganizationVo vo) {
        Map<String, Object> result = new HashMap<>();
        Organization organization = setStepOneProperties(vo);
        organization.setCreateTime(DateUtil.currentTimeSeconds());
        mergeOrgInfo(organization);
        result.put("uuid", organization.getOrgUuid());
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> saveOrgApplyStepTwo(OrganizationVo vo) {
        Map<String, Object> result = new HashMap<>();
        Organization organization = setStepTwoProperties(vo);
        mergeOrgInfo(organization);
        result.put("uuid", organization.getOrgUuid());
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> saveOrgApplyStepThree(OrganizationVo vo) {
        Map<String, Object> result = new HashMap<>();
        Organization organization = setStepThreeProperties(vo);
        mergeOrgInfo(organization);
        result.put("uuid", organization.getOrgUuid());
        return result;
    }

    /**
     * 机构申请-保存第四步-银行信息
     *
     * @param vo
     */


    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> saveOrgApplyStepFour(OrganizationVo vo) {
        Map<String, Object> result = new HashMap<>();
        Organization organization = setStepFourProperties(vo);
        mergeOrgInfo(organization);
        result.put("uuid", organization.getOrgUuid());
        return result;
    }


    /**
     * 机构申请-保存第五步-其他信息
     *
     * @param vo
     */


    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> saveOrgApplyStepFive(OrganizationVo vo) {
        Map<String, Object> result = new HashMap<>();
        Organization organization = setStepFiveProperties(vo);
        mergeOrgInfo(organization);
        result.put("uuid", organization.getOrgUuid());
        return result;
    }

    @Override
    public Organization getOrgApplyInfo() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String orgUuid = "";
        //todo
        //如果是机构职员的话,则进行取机构职员的会长id,用来获取机构信息
        Organization organization = organizationMapper.selectByPrimaryKey(loginUser.getUser().getOrgId());
        //如果从未申请过机构,则为第一步表单.
        Organization vo = new Organization();
        if (null == organization) {
            vo.setFormStatus(FormStatusEnum.ONE.getValue());
        } else {
            orgUuid = organization.getOrgUuid();
            //获取机构的信息
            Organization orgInfoMsg = organizationMapper.getByAccountUuidGroupConcatUrl(orgUuid);
            if (orgInfoMsg == null) {
                log.warn("机构状态为关闭，机构uuid:{}", orgUuid);
                throw new ServiceException("org_close", "当前用户尚未分配角色");
            }
            vo = orgInfoMsg;
        }
        // 加入时间为空就取创建时间
        if (vo.getJoinTime() == null) {
            vo.setJoinTime(vo.getCreateTime());
        }
        // 聊天室公会按周结计算
        if (organization != null && organization.getChatRoomPermissions() == 1) {
            organization.setLiveSettlementeType(LiveSettlementeTypeEnum.WEEK.getValue());
        }
        // 1.周结 2.月结
        if (organization == null || organization.getLiveSettlementeType() == null) {
            vo.setSettlementeModifyFlag(true);
        } else if (LiveSettlementeTypeEnum.WEEK.getValue() == organization.getLiveSettlementeType()) {
            int dayOfWeek = LocalDate.now().getDayOfWeek();
            vo.setSettlementeModifyFlag(dayOfWeek == DateTimeConstants.FRIDAY);
        } else if (LiveSettlementeTypeEnum.MONTH.getValue() == organization.getLiveSettlementeType()) {
            int day = LocalDate.now().getDayOfMonth();
            vo.setSettlementeModifyFlag(day >= 20);
//            vo.setSettlementeModifyFlag(day >= 10);
        } else {
            log.warn("结算类型有误,organization={}", organization.getOrgId());
        }
        return vo;
    }

    @Override
    public Map<String, Object> getOrgInfoStatus() {
        Map<String, Object> result = new HashMap<>();
        // todo
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser.getUser().getOrgId().equals(0L) || loginUser.getUser().getOrgId() == null) {
            result.put("apply_status", ApplyStatusEnum.DEFAULT.getValue());
            result.put("form_status", ApplyStatusEnum.DEFAULT.getValue());
            return result;
        } else {
            SysUser user = sysUserMapper.selectById(loginUser.getUser().getUserId().intValue());
            Organization organization = organizationMapper.selectByPrimaryKey(user.getOrgId());
            if (organization == null) {
                result.put("apply_status", ApplyStatusEnum.DEFAULT.getValue());
                result.put("form_status", ApplyStatusEnum.DEFAULT.getValue());
                return result;
            }
            boolean isManager = false;
            if (loginUser.getUser().getUserType() == UserTypeEnum.MANAGER.getType()) {
                isManager = true;
            }
            List<Map<String, Object>> signMap = agreementInfoService.getSignMap(loginUser.getUser().getOrgId());
            List<Integer> teamTypeList = getOrgInfoType(organization.getOrgId());
            result.put("orgName", organization.getOrgName());
            result.put("qq", qq);
            result.put("teamType", TeamTypeEnum.getByValues(teamTypeList));
            result.put("applyStatus", null == organization ? ApplyStatusEnum.DEFAULT.getValue() : organization.getApplyStatus());
            result.put("auditMsg", organization.getAuditMsg());
            result.put("signMap", signMap);
            result.put("manager", isManager);
            result.put("liveSettlementeType", organization.getLiveSettlementeType());
            result.put("orgId", organization.getOrgId());
            return result;
        }
    }

    @Override
    public List<Integer> getOrgInfoType(Long orgId) {
        Organization organization = organizationMapper.selectByPrimaryKey(orgId);
        List<Integer> list = new ArrayList<>();
        if (organization != null) {
            if (organization.getLivePermissions() != null && 1 == organization.getLivePermissions()) {
                list.add(TeamTypeEnum.LIVE_TEAM.getValue());
            }
            if (organization.getQuliaoPermissions() != null && 1 == organization.getQuliaoPermissions()) {
                list.add(TeamTypeEnum.CALL_TEAM.getValue());
            }
            if (organization.getQuliaoPermissions() != null && 1 == organization.getChatRoomPermissions()) {
                list.add(TeamTypeEnum.TALK_TEAM.getValue());
            }
        }
        return list;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeForumStatus() {
        // todo
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //todo
        //如果是机构职员的话,则进行取机构职员的会长id,用来获取机构信息
        Organization organization = organizationMapper.selectByPrimaryKey(loginUser.getUser().getOrgId());
        if (organization.getApplyStatus().intValue() == ApplyStatusEnum.FAILED.getValue()) {
            organization.setFormStatus(FormStatusEnum.ONE.getValue());
            organization.setApplyStatus(ApplyStatusEnum.RE_APPLY.getValue());
            organizationMapper.updateByPrimaryKeySelective(organization);
        } else {
            throw new ServiceException(CodeStatus.ORG_APPLY_STATUS_ERROR.value(), CodeStatus.ORG_APPLY_STATUS_ERROR.getReasonPhrase());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeOrg() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Organization organization = organizationMapper.getByAccountUuid(loginUser.getUser().getAccountUuid());
        if (organization.getApplyStatus() != null && organization.getApplyStatus().equals(ApplyStatusEnum.SUCCESS.getValue())) {
            throw new ServiceException("error", "通过机构不允许撤销。");
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("org_id", organization.getOrgId());
        queryWrapper.eq("account_uuid", organization.getAccountUuid());
        SysUser sysUser = sysUserMapper.selectOne(queryWrapper);
        if (sysUser != null) {
            sysUser.setOrgId((long) OrgIdEnum.ZERO.getValue());
            sysUser.setUserType(UserTypeEnum.DEFAULT.getType());
            sysUser.setOrgName("");
            sysUser.setUserName("");
            sysUserMapper.updateByPrimaryKey(sysUser);
        } else {
            throw new ServiceException("error", "该机构负责人手机号是未注册用户。");
        }
        if (StringUtils.isNotEmpty(organization.getOrgUuid())) {
            chargePersonMapper.deleteByUuid(organization.getOrgUuid());
            businessLicenseMapper.deleteByUuid(organization.getOrgUuid());
            legalPersonMapper.deleteByUuid(organization.getOrgUuid());
            hostScreenshotMapper.deleteByUuid(organization.getOrgUuid());
            orgCooperationFlowMapper.deleteByUuid(organization.getOrgUuid());
        }
        organizationMapper.deleteByPrimaryKey(organization.getOrgId());
    }

    @Override
    public Organization getOrgInfoByCurrentThrow() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String accountUuid = loginUser.getUser().getAccountUuid();//当前登陆uuid
        Organization organization = Optional.ofNullable(organizationMapper.getByAccountUuid(accountUuid))
                .orElseThrow(() -> new ServiceException(CodeStatus.ORG_NOT_FOUNT_ERROR.value(), CodeStatus.ORG_NOT_FOUNT_ERROR.getReasonPhrase()));

        return organization;
    }

    /**
     * 修改机构简介
     *
     * @param orgId
     * @param content
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeContent(Long orgId, String content) {
        if (null == orgId) {
            throw new ServiceException("invalid_operate", "机构失效，该操作无效");
        }
        if (StringUtils.isNotBlank(content) && content.length() > 500) {
            throw new ServiceException("invalid_params", "机构简介不能超过500字");
        }
        organizationMapper.updateContent(orgId, content);
    }

    public Integer getUserType() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        QueryWrapper roleIdQuery = new QueryWrapper();
        roleIdQuery.eq("user_id", loginUser.getUser().getUserId());
        SysUserRole userRole = sysUserRoleMapper.selectOne(roleIdQuery);
        if (userRole == null) {
            throw new ServiceException("no_role", "没有查询权限");
        }
        SysRole sysRole = sysRoleMapper.getRoleName(userRole.getRoleId());
        Integer roleType = 0;
        if (sysRole.getRoleKey().equals(UserTypeEnum.MANAGER.getCode())) {
            roleType = UserTypeEnum.MANAGER.getType();
        }
        if (sysRole.getRoleKey().equals(UserTypeEnum.LEADER.getCode())) {
            roleType = UserTypeEnum.LEADER.getType();
        }
        if (sysRole.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())) {
            roleType = UserTypeEnum.AGENTER.getType();
        }
        return roleType;
    }

    /**
     * 设置第一步的参数
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Organization setStepOneProperties(OrganizationVo vo) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String accountUuid = loginUser.getUser().getAccountUuid();
        // 在这里设置chargePersonPhone
        vo.setChargePersonPhone(loginUser.getUser().getMobile());
        checkStepOneProperties(vo, accountUuid);
        Organization organization = organizationMapper.getByAccountUuid(accountUuid);
        Long orgUuid;
        if (null == organization) {
            Long maxOrgUuid = organizationMapper.getMaxOrgUuid();
            orgUuid = maxOrgUuid == null ? 210000L : maxOrgUuid++;
            Organization gi = organizationMapper.getByUuid(orgUuid.toString());
            //一开始设置的字段是uuid,设置为varchar类型,但是旧机构后台是以主键作为机构id,所以当时取max(uuid)的时候会出问题.要改动uuid为long类型改动较多,先简单处理一下
            while (null != gi) {
                log.info("uuid相同,重新生成,机构信息：{}", gi.getOrgUuid());
                orgUuid += 1;
                gi = organizationMapper.getByUuid(orgUuid.toString());
            }
            organization = new Organization();
            organization.setOrgUuid(orgUuid.toString());
        }
        organization.setOrgName(vo.getOrgName());
        organization.setContent(vo.getContent());
        organization.setChargePersonPhone(loginUser.getUser().getMobile());
        organization.setChargePersonEmail(vo.getChargePersonEmail());
        organization.setAccountUuid(loginUser.getUser().getAccountUuid());
        organization.setFormStatus(FormStatusEnum.TWO.getValue()); //跳转到第二步 .
        Integer applyStatus = organization.getApplyStatus();
        organization.setApplyStatus((null == applyStatus || 0 == applyStatus) ? ApplyStatusEnum.DEFAULT.getValue() : applyStatus);
        organization.setOrgStatus(OrgStatusEnum.DEFAULT.getValue());
        organization.setQuliaoPermissions(TeamStatusEnum.NO_VALID.getValue());
        if (vo.getBusinessPermissions().contains(TeamTypeEnum.LIVE_TEAM.getValue())) {
            organization.setLivePermissions(TeamStatusEnum.VALID.getValue());
        } else {
            organization.setLivePermissions(TeamStatusEnum.NO_VALID.getValue());
        }
        if (vo.getBusinessPermissions().contains(TeamTypeEnum.TALK_TEAM.getValue())) {
            organization.setChatRoomPermissions(TeamStatusEnum.VALID.getValue());
        } else {
            organization.setChatRoomPermissions(TeamStatusEnum.NO_VALID.getValue());
        }
        return organization;
    }

    /**
     * 检查第一步参数有效性
     *
     * @param vo
     */
    public void checkStepOneProperties(OrganizationVo vo, String accountUuid) {
        Organization organization = organizationMapper.getByAccountUuid(accountUuid);
        if (null != organization && null != organization.getApplyStatus() && (organization.getApplyStatus() == ApplyStatusEnum.SUCCESS.getValue() || organization.getOrgStatus() == OrgStatusEnum.OPEN.getValue())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "已有机构,无法重复申请");
        }
        boolean flag = false;
        Organization search = new Organization();
        search.setOrgName(vo.getOrgName());
        List<Organization> getByList = organizationMapper.getByList(search);
        if (CollectionUtils.isNotEmpty(getByList) && organization == null) {
            flag = true;
        }
        if (organization != null && CollectionUtils.isNotEmpty(getByList)) {
            long count = getByList.stream().filter(item -> !item.getOrgId().equals(organization.getOrgId())).count();
            if (count > 0) {
                flag = true;
            }
        }
        search.setAccountUuid(accountUuid);
        if (flag) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "机构名称重复,无法申请");
        }
        //1.机构信息
        if (StringUtils.isBlank(vo.getOrgName())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "机构名称不能为空");
        }
        if (StringUtils.isBlank(vo.getChargePersonPhone())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人联系电话不能为空");
        }
        if (StringUtils.isBlank(vo.getChargePersonEmail())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人邮箱不能为空");
        }
        if (!ParamsCheckUtil.isMobile(vo.getChargePersonPhone())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人联系电话格式错误");
        }
        if (!ParamsCheckUtil.isEmail(vo.getChargePersonEmail())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人邮箱格式错误");
        }
        if (StringUtils.isNotBlank(vo.getContent()) && vo.getContent().length() > 500) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "请输入500字以内的简介");
        }
        if (CollectionUtils.isEmpty(vo.getBusinessPermissions())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "请勾选要申请开通的业务类型");
        }
    }

    /**
     * 设置第二步的参数
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Organization setStepTwoProperties(OrganizationVo vo) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String accountUuid = loginUser.getUser().getAccountUuid();
        // todo
        Organization organization = organizationMapper.getByAccountUuid(accountUuid);
        checkStepTwoProperties(vo, organization);

        organization.setChargePerson(vo.getChargePerson());
        organization.setChargePersonVx(vo.getChargePersonVx());
        organization.setChargePersonBirthday(vo.getChargePersonBirthday());
        organization.setChargePersonIdCard(vo.getChargePersonIdCard());
        organization.setReceivingAddress(vo.getReceivingAddress());
        organization.setContactPhone(vo.getContactPhone());
        //保存负责人的身份证信息
        saveChargePerson(vo, organization);
        organization.setFormStatus(FormStatusEnum.THREE.getValue()); //跳转到第二步 .
        return organization;
    }

    /**
     * 检查第二步参数有效性
     *
     * @param vo
     */
    public void checkStepTwoProperties(OrganizationVo vo, Organization organization) {
        if (null == organization) {
            throw new ServiceException(CodeStatus.ORG_NOT_FOUNT_ERROR.value(), "机构创建异常");
        }
        //2.账号信息
        if (StringUtils.isBlank(vo.getChargePerson())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人名字不能为空");
        }
        if (StringUtils.isBlank(vo.getChargePersonVx())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人微信不能为空");
        }
        if (null == vo.getChargePersonBirthday()) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人出生日期不能为空");
        }
        if (StringUtils.isBlank(vo.getChargePersonIdCard())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人身份证号码不能为空");
        }
        if (StringUtils.isBlank(vo.getChargePersonUrl())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人身份证上传url不能为空");
        }
        if (vo.getChargePersonUrl().split(",").length != 3) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人身份证图片格式错误");
        }
        if (StringUtils.isBlank(vo.getReceivingAddress())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "收件地址不能为空");
        }
        if (!ParamsCheckUtil.isLegalPattern(vo.getChargePersonIdCard())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "身份证号码格式错误");
        }
    }


    /**
     * 设置第三步参数
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Organization setStepThreeProperties(OrganizationVo vo) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String accountUuid = loginUser.getUser().getAccountUuid();
        Organization organization = organizationMapper.getByAccountUuid(accountUuid);
        checkStepThreeProperties(vo, organization);
        organization.setLegalPerson(vo.getLegalPerson());
        organization.setLegalPersonIdCard(vo.getLegalPersonIdCard());
        organization.setEnterpriseName(vo.getEnterpriseName());
        organization.setLegalPersonPhone(vo.getLegalPersonPhone());
        organization.setSocialUnifiedCreditCode(vo.getSocialUnifiedCreditCode());
        organization.setPremises(vo.getPremises());
        organizationService.saveLegalPerson(organization.getOrgUuid(), vo.getLegalPersonUrl());
        saveOpeningPermit(vo, organization);
        organizationService.saveBusinessLicense(organization.getOrgUuid(), vo.getBusinessLicenseUrl());
        organization.setFormStatus(FormStatusEnum.FOUR.getValue()); //跳转到第四步 .
        // 对公打款信息 审核中 标识
        organization.setRemitModifyStatus(vo.getRemitModifyStatus());
        return organization;
    }

    /**
     * 检查第三步参数有效性
     *
     * @param vo
     */
    public void checkStepThreeProperties(OrganizationVo vo, Organization organization) {
        if (null == organization) {
            throw new ServiceException(CodeStatus.ORG_NOT_FOUNT_ERROR.value(), "机构创建异常");
        }
        //3.公司信息
        if (StringUtils.isBlank(vo.getLegalPerson())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "法人姓名不能为空");
        }
        if (StringUtils.isBlank(vo.getLegalPersonIdCard())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "法人身份证不能为空"); //todo 进行身份证校验
        }
        if (StringUtils.isBlank(vo.getOpeningPermitUrl())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "开户许可证不能为空");
        }
        if (StringUtils.isBlank(vo.getLegalPersonUrl()) && vo.getLegalPersonUrl().split(",").length != 3) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "法人身份证信息缺失错误");
        }
        if (!ParamsCheckUtil.isLegalPattern(vo.getLegalPersonIdCard())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "法人身份证号码格式错误");
        }
        if (StringUtils.isBlank(vo.getSocialUnifiedCreditCode())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "社会统一信用代码不能为空");
        }
        if (StringUtils.isBlank(vo.getEnterpriseName())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "企业全名不能为空");
        }
        if (StringUtils.isBlank(vo.getLegalPersonPhone())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "法人手机号不能为空");
        }
        if (StringUtils.isBlank(vo.getPremises())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "经营场所不能为空");
        }
    }


    /**
     * 设置第四步参数
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Organization setStepFourProperties(OrganizationVo vo) {
        // todo
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String accountUuid = loginUser.getUser().getAccountUuid();
        Organization organization = organizationMapper.getByAccountUuid((accountUuid));
        checkStepFourProperties(vo, organization);
        organization.setPublicReceivingBankAccount(vo.getPublicReceivingBankAccount());
        organization.setAccountName(vo.getAccountName());
        organization.setAccountBankName(vo.getAccountBankName());
        organization.setProvince(vo.getProvince());
        organization.setProvinceId(vo.getProvinceId());
        organization.setCity(vo.getCity());
        organization.setCityId(vo.getCityId());
        organization.setSubBranchName(vo.getSubBranchName());
        organization.setFormStatus(FormStatusEnum.FIVE.getValue()); //跳转到第五步 .
        organization.setRemitModifyStatus(vo.getRemitModifyStatus());
        return organization;
    }

    /**
     * 保存开户许可证
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOpeningPermit(OrganizationVo vo, Organization organization) {
        openingPermitMapper.deleteByUuid(organization.getOrgUuid());
        String[] urls = vo.getOpeningPermitUrl().split(",");
        Arrays.stream(urls).forEach(url -> {
            OpeningPermit openingPermit = new OpeningPermit();
            openingPermit.setOrgUuid(organization.getOrgUuid());
            openingPermit.setUrl(url);
            openingPermit.setCreateTime(DateUtil.currentTimeSeconds());
            openingPermit.setUpdateTime(DateUtil.currentTimeSeconds());
            openingPermitMapper.insert(openingPermit);
        });
    }

    /**
     * 检查第四步参数有效性
     *
     * @param vo
     */
    public void checkStepFourProperties(OrganizationVo vo, Organization organization) {
        if (null == organization) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "机构创建异常");
        }
        //4.银行信息
        if (StringUtils.isBlank(vo.getPublicReceivingBankAccount())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "对公收款账户不能为空");
        }
        if (StringUtils.isBlank(vo.getAccountName())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "开户名不能为空");
        }
        if (StringUtils.isBlank(vo.getAccountBankName())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "开户行不能为空");
        }
        if (StringUtils.isBlank(vo.getProvince())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "省份不能为空");
        }
        if (null == vo.getProvinceId()) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "省份id不能为空");
        }
        if (StringUtils.isBlank(vo.getCity())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "市不能为空");
        }
        if (null == vo.getProvinceId()) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "市id不能为空");
        }
        if (StringUtils.isBlank(vo.getSubBranchName())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "支行名称不能为空");
        }
    }

    /**
     * 设置第第五步参数
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Organization setStepFiveProperties(OrganizationVo vo) {
        // todo
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String accountUuid = loginUser.getUser().getAccountUuid();
        Organization organization = organizationMapper.getByAccountUuid(accountUuid);
        checkStepFiveProperties(vo, organization);
        organization.setBusinessPerson(vo.getBusinessPerson());
        saveGuildCooperationFlow(vo, organization);
        saveHostScreenshot(vo, organization);
        organization.setFormStatus(FormStatusEnum.SIX.getValue()); //等待审核 .
        organization.setApplyStatus(ApplyStatusEnum.WAIT.getValue());
        return organization;
    }

    /**
     * 检查第五步参数有效性
     *
     * @param vo
     */
    public void checkStepFiveProperties(OrganizationVo vo, Organization organization) {
        if (null == organization) {
            throw new ServiceException(CodeStatus.ORG_NOT_FOUNT_ERROR.value(), "机构创建异常");
        }
        //5.其他信息
//        if (StringUtils.isBlank(vo.getBusinessPerson())) {
////            throw new ServiceException(CodeStatus.APPLY_GUILD_PARAM_ERROR.value(), "业务对接人不能为空");
////        }
        if (StringUtils.isBlank(vo.getOrgCooperationFlowUrl())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "机构流水截图不能为空");
        }
        if (StringUtils.isBlank(vo.getHostScreenshotUrl())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "主播截图不能为空");
        }
    }

    /**
     * 保存机构外站流水截图
     *
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveGuildCooperationFlow(OrganizationVo vo, Organization organization) {
        orgCooperationFlowMapper.deleteByUuid(organization.getOrgUuid());
        String[] urls = vo.getOrgCooperationFlowUrl().split(",");
        Arrays.stream(urls).forEach(url -> {
            OrgCooperationFlow orgCooperationFlow = new OrgCooperationFlow();
            orgCooperationFlow.setOrgUuid(organization.getOrgUuid());
            orgCooperationFlow.setUrl(url);
            orgCooperationFlow.setCreateTime(DateUtil.currentTimeSeconds());
            orgCooperationFlow.setUpdateTime(DateUtil.currentTimeSeconds());
            orgCooperationFlowMapper.insert(orgCooperationFlow);
        });

    }

    /**
     * 保存主播截图
     *
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveHostScreenshot(OrganizationVo vo, Organization organization) {
        hostScreenshotMapper.deleteByUuid(organization.getOrgUuid());
        String[] urls = vo.getHostScreenshotUrl().split(",");
        Arrays.stream(urls).forEach(url -> {
            HostScreenshot hostScreenshot = new HostScreenshot();
            hostScreenshot.setOrgUuid(organization.getOrgUuid());
            hostScreenshot.setUrl(url);
            hostScreenshot.setCreateTime(organization.getCreateTime());
            hostScreenshot.setUpdateTime(organization.getUpdateTime());
            hostScreenshotMapper.insert(hostScreenshot);
        });
    }


    /**
     * 保存负责人信息身份证
     *
     * @param vo
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveChargePerson(OrganizationVo vo, Organization organization) {
        //删除负责人的身份证信息图片
        chargePersonMapper.deleteByUuid(organization.getOrgUuid());
        String[] urls = vo.getChargePersonUrl().split(",");
        int i = 1;
        for (String url : urls) {
            ChargePerson chargePerson = new ChargePerson();
            chargePerson.setType(i++);
            chargePerson.setOrgUuid(organization.getOrgUuid());
            chargePerson.setUrl(url);
            chargePerson.setCreateTime(DateUtil.currentTimeSeconds());
            chargePerson.setUpdateTime(DateUtil.currentTimeSeconds());
            chargePersonMapper.insert(chargePerson);
            i++;
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void mergeOrgInfo(Organization organization) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (organization.getOrgId() == null) {
            SysUser user = sysUserMapper.selectById(loginUser.getUser().getUserId());
            organization.setUpdateTime(DateUtil.currentTimeSeconds());
            organization.setAccountUuid(user.getAccountUuid());
            log.info("开始插入机构信息，organization为:{}", JsonUtils.objectToString(organization));
            organizationMapper.insert(organization);
            if (user.getOrgId() == null || (user.getOrgId().equals(0L) && user.getUserType() == UserTypeEnum.DEFAULT.getType())) {
                Organization organizationVo = organizationMapper.getByUuid(organization.getOrgUuid());
                user.setOrgId(organizationVo.getOrgId());
                user.setOrgName(organizationVo.getOrgName());
                user.setUserType(UserTypeEnum.MANAGER.getType());
                sysUserMapper.updateById(user);
            } else if (user.getOrgId() != 0) {
                throw new ServiceException("error", "该登录用户已经绑定过机构");
            }
        } else {
            SysUser user = sysUserMapper.selectById(loginUser.getUser().getUserId());
            user.setOrgName(organization.getOrgName());
            sysUserMapper.updateById(user);
            log.info("开始更新机构信息，organization为:{}", JsonUtils.objectToString(organization));
            organizationMapper.updateByPrimaryKeySelective(organization);
        }
        accountLoginService.refreshAccount(organization.getAccountUuid());
    }

}
