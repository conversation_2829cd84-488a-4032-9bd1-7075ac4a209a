package cn.taqu.gonghui.system.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class FrameListExtraInfo implements Serializable {

    @JSONField(name = "punish_info")
    private List<PunishInfoDTO> punishInfo;
    @JSONField(name = "count")
    private String count;

    @NoArgsConstructor
    @Data
    public static class PunishInfoDTO {
        @JSONField(name = "type")
        private String type;
        @J<PERSON>NField(name = "reason")
        private String reason;
        @J<PERSON>NField(name = "host_uuid")
        private String hostUuid;
        @JSONField(name = "punish_time")
        private String punishTime;
    }
}
