package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.vo.TeamOperateRequestVo;
import cn.taqu.gonghui.system.vo.TeamOperateResponseVo;

import java.util.List;

public interface TeamOperateService {

    /**
     * 操作团队方法
     *
     * @param vo
     * <AUTHOR>
     */
    void operateTeam(TeamOperateRequestVo vo);

    /**
     * 获取迁移团队历史记录
     *
     * @param pageNo
     * @param pageSize
     * @param type
     * @return
     */
    public List<TeamOperateResponseVo> getOperateList(int pageNo, int pageSize, Integer type);
}
