package cn.taqu.gonghui.system.service.impl;

import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.common.mapper.ApprovalFlowNodeMapper;
import cn.taqu.gonghui.system.service.iservice.IApprovalFlowNodeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/8 19 24
 * discription
 */
@Service
public class ApprovalFlowNodeServiceImpl extends ServiceImpl<ApprovalFlowNodeMapper, ApprovalFlowNode> implements IApprovalFlowNodeService {
    @Override
    public List<ApprovalFlowNode> getByRange(Long startId, Long endId) {

        LambdaQueryWrapper<ApprovalFlowNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(ApprovalFlowNode::getId, startId, endId);
        return list(queryWrapper);
    }

    @Override
    public void updateClearTxtByRange(Long curStartId, Long curEndId) {
        this.baseMapper.updateClearTxtByRange(curStartId, curEndId);
    }
}
