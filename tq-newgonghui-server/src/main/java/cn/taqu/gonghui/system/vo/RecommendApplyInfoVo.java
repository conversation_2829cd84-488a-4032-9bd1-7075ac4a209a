package cn.taqu.gonghui.system.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class RecommendApplyInfoVo {

    private String date;
    private Map<String, Integer> gonghuiApplyLimit;
    private String hostName;
    private String hostUuid;
    private Integer location1;
    private Integer location2;
    private List<Map<String, Object>> locationVos;
    private Long cardUseNum;
}
