package cn.taqu.gonghui.system.entity;

import lombok.Data;


@Data
public class LiveRecommendApply {

    private Long id;

    /**
     * 主播uuid
     */
    private String host_uuid;
    /**
     * 公会id
     */
    private String consortia_id;
    /**
     * 开始时间
     */
    private String start_time;
    /**
     * 结束时间
     */
    private String end_time;

    private Long create_time;

    /**
     * 申请推荐日期
     */
    private String date;
    /**
     * 位置 100热二;201 ~ 208 横条1 ~ 8;300大厅banner;400社区banner
     */
    private Integer location;
    /**
     * 类型 1公会申请  2超管设置'
     */
    private Integer type;
    /**
     * 状态 1审批中 2审批通过 -1拒绝申请 -2撤销申请'
     */
    private Integer status;

    private Integer use_recommend_card;
}
