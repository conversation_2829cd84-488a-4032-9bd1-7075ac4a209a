package cn.taqu.gonghui.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.jdbc.pagehelper.PageRequest;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.LocalConfUtil;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.gonghui.chatroom.search.ChatRoomSearch;
import cn.taqu.gonghui.chatroom.service.ChatRoomService;
import cn.taqu.gonghui.chatroom.vo.ChatVo;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.entity.ApprovalFlowLog;
import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.common.mapper.ApprovalFlowLogMapper;
import cn.taqu.gonghui.common.mapper.ApprovalFlowMapper;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.mapper.ApprovalFlowNodeMapper;
import cn.taqu.gonghui.common.service.GeneralService;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.service.flow.ChatRoomInviteFlow;
import cn.taqu.gonghui.common.service.flow.QuitGuildFlow;
import cn.taqu.gonghui.common.utils.*;
import cn.taqu.gonghui.common.utils.UUID;
import cn.taqu.gonghui.common.vo.*;
import cn.taqu.gonghui.common.vo.req.HostInfoReq;
import cn.taqu.gonghui.common.vo.req.InviteHostReq;
import cn.taqu.gonghui.common.vo.req.ResetHostReq;
import cn.taqu.gonghui.common.vo.res.InviteHostDetailRes;
import cn.taqu.gonghui.common.vo.res.InviteHostItemRes;
import cn.taqu.gonghui.common.vo.res.LinkAccountRes;
import cn.taqu.gonghui.common.vo.res.LiveGrayUuidRes;
import cn.taqu.gonghui.live.entity.LiveHostInfo;
import cn.taqu.gonghui.live.mapper.LiveHostApplyInfoMapper;
import cn.taqu.gonghui.live.mapper.LiveHostInfoMapper;
import cn.taqu.gonghui.live.service.LiveSoaService;
import cn.taqu.gonghui.live.util.LiveDateUtils;
import cn.taqu.gonghui.soa.*;
import cn.taqu.gonghui.system.dto.*;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.HostSharingProfitRecordMapper;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.TeamEmployeeMapper;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.mapper.TeamHostOperateLogMapper;
import cn.taqu.gonghui.system.mapper.TeamMapper;
import cn.taqu.gonghui.system.search.HostStatisticSearch;
import cn.taqu.gonghui.system.search.PunishSearch;
import cn.taqu.gonghui.system.search.SharingCronSearch;
import cn.taqu.gonghui.system.search.SharingProfitRecordSearch;
import cn.taqu.gonghui.system.search.TeamHostOperateLogSearch;
import cn.taqu.gonghui.system.search.WarnSearch;
import cn.taqu.gonghui.system.service.HostModifyRecordService;
import cn.taqu.gonghui.system.service.HostService;
import cn.taqu.gonghui.system.service.PushService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamHostService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.vo.*;
import cn.taqu.gonghui.util.EnvUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/5/11
 */
@Service
@Slf4j
public class TeamHostServiceImpl extends ServiceImpl<TeamHostMapper, TeamHost> implements TeamHostService {

    private static Logger logger = LoggerFactory.getLogger(TeamHostServiceImpl.class);

    @Autowired
    private GeneralService generalService;

    @Autowired
    private HostService hostService;
    @Autowired
    private TeamEmployeeService teamEmployeeService;

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @SoaReference(application = "liveV1", value = "liveV1")
    private HostApplyService hostApplyService;
    @SoaReference("liveV1")
    private GonghuiService gonghuiService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private TeamHostOperateLogMapper operateLogMapper;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private TeamEmployeeMapper teamEmployeeMapper;
    @Autowired
    private HostSharingProfitRecordMapper sharingProfitRecordMapper;
    @Autowired
    private LiveHostInfoMapper liveHostInfoMapper;

    @Autowired
    private LiveHostApplyInfoMapper liveHostApplyInfoMapper;

    @SoaReference(application = "liveV1", value = "liveV1")
    private HostInfoService hostInfoService;
    @Autowired
    private HostModifyRecordService hostModifyRecordService;
    @Autowired
    private PushService pushService;
    @Lazy
    @Autowired
    private ChatRoomService chatRoomService;

    @SoaReference("account")
    private CertificationService certificationService;

    @Autowired
    private BackstageOperateLogServiceImpl backstageOperateLogService;

    @SoaReference("account")
    private InfoService infoService;

    @Autowired
    private QuitGuildFlow quitGuildFlow;

    @Autowired
    private ChatRoomInviteFlow chatRoomInviteFlow;

    @Autowired
    private ApprovalFlowMapper approvalFlowMapper;

    @Autowired
    private ApprovalFlowNodeMapper approvalFlowNodeMapper;

    @Autowired
    private ApprovalFlowLogMapper approvalFlowLogMapper;

    @Autowired
    private PunishTicketSoaService punishTicketSoaService;

    @Autowired
    private LiveSoaService liveSoaService;

    private static final long CREATE_MAX_DAY = 7;

    private static final long THREE_MONTH_DAY = 90;

    private static final long ONE_DAY = 24 * 60 * 60 * 1000;
    //private static final long ONE_DAY = 60 * 1000; 灰度时开这个

    @EtcdValue("soa.application.account.day_90")
    private static long DAY_90;

    @EtcdValue("soa.application.account.day_180")
    private static long DAY_180;

    @EtcdValue("soa.application.account.day_365")
    private static long DAY_365;

    @EtcdValue("soa.application.account.money_10_w")
    private static long MONEY_10_W;

    @EtcdValue("soa.application.account.money_50_w")
    private static long MONEY_50_W;

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 素人团队id
     */
    private static final Map<String, Long> normalTeamId = new HashMap<String, Long>() {{
        put("test", 110016L);
        put("online", 110166L);
    }};

    /**
     * 中转团队id
     */
    private static final Map<String, Long> midTeamId = new HashMap<String, Long>() {{
        put("test", 40L);
        put("online", 110648L);
    }};

    @Override
    public IPage<TeamHost> findByPage(Long orgId, List<Long> teamIds, List<String> uuids, String uuid, Integer businessType, int pageNo, int pageSize, ChatRoomSearch chatRoomSearch) {
        IPage<TeamHost> page = new Page<>(pageNo, pageSize);
        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        if (orgId != null) {
            queryWrapper.eq("org_id", orgId);
        }
        if (CollectionUtils.isNotEmpty(teamIds)) {
            queryWrapper.in("team_id", teamIds);
        }
        if (StringUtils.isNotBlank(uuid)) {
            queryWrapper.eq("host_uuid", uuid);
        }
        if (CollectionUtils.isNotEmpty(uuids)) {
            queryWrapper.in("host_uuid", uuids);
        }
        if (businessType != null) {
            queryWrapper.eq("team_type", businessType);
        }
        if (Objects.nonNull(chatRoomSearch) && Objects.nonNull(chatRoomSearch.getStartTime()) && Objects.nonNull(chatRoomSearch.getEndTime())) {
            int startTime = Integer.parseInt(chatRoomSearch.getStartTime());
            int endTime = Integer.parseInt(chatRoomSearch.getEndTime());
            queryWrapper.between("invite_time", startTime, endTime);
        }
        queryWrapper.eq("status", 1);
        queryWrapper.isNotNull("team_id");
        IPage<TeamHost> teamHostPage = baseMapper.selectPage(page, queryWrapper);
        return teamHostPage;
    }

    @Override
    public IPage<TeamHost> pageByOwnEmployee(Integer pageNo, Integer pageSize, Long employeeId, Integer teamType) {
        IPage<TeamHost> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<TeamHost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeamHost::getEmployeeId, employeeId)
                .eq(TeamHost::getTeamType, teamType)
                .eq(TeamHost::getStatus, 1);
        IPage<TeamHost> teamHostPage = baseMapper.selectPage(page, queryWrapper);
        return teamHostPage;
    }

    @Override
    public List<ChatVo> findChatListByPage(Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        List<ChatVo> chatListByPage = baseMapper.getChatListByPage();
        PageHelper.clearPage();
        return chatListByPage;
    }

    @Override
    public Integer countTeamHostInTeamId(Long teamId) {
        QueryWrapper<TeamHost> wrapper = new QueryWrapper<>();
        wrapper.eq("team_id", teamId);
        return this.count(wrapper);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invitationHost(HostDto hostDto) {
        validParams(hostDto);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Team team = teamMapper.selectByPrimaryKey(hostDto.getTeamId());
        if (team == null) {
            throw new ServiceException("team_empty", "当前团队数据异常，请联系管理员");
        }
        // 如果用户已经在聊天室团队，不允许非当前机构的直播团队发起邀约
        String uuid = chatRoomService.getUuidByNickNameOrCarId(hostDto.getLiveNo());
        TeamHost host = teamHostMapper.getOneByHostUuid(uuid, TeamTypeEnum.TALK_TEAM.getValue());
        if (host != null && !Objects.equals(host.getOrgId(), team.getOrgId())) {
            throw new ServiceException("org_not_match", "该用户已存在聊天室公会");
        }
        String accountUuid = "";
        if (hostDto.getTeamEmployeeId() != null && hostDto.getTeamEmployeeId() != 0) {
            TeamEmployee teamEmployee = teamEmployeeService.getOne(hostDto.getTeamEmployeeId());
            SysUser sysUser = sysUserService.selectUserByUserId(teamEmployee.getUserId());
            if (sysUser != null) {
                accountUuid = sysUser.getAccountUuid();
            }
        }
        gonghuiService.inviteHost(hostDto.getLiveNo(), hostDto.getHostMobile(), hostDto.getRealName(), accountUuid, String.valueOf(team.getTeamId()), hostDto.getRemark(), loginUser.getUser().getAccountUuid(), hostDto.getProfitRate(), hostDto.getHostType());
    }

    @Override
    public void reInvitationHost(Long inviteId) {
        if (null == inviteId) {
            throw new ServiceException("inviteId_empty", "邀请id不存在");
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        gonghuiService.reInvite(inviteId, loginUser.getUser().getAccountUuid());
    }


    @Override
    public void getApplyHostinfo(String hostUuid) {
        QueryWrapper queryRoleId = new QueryWrapper();
        queryRoleId.eq("host_uuid", hostUuid);
//        TeamHost teamHost =teamHostMapper.selectOne(queryRoleId);
//        String hostUuid = teamHost.getHostUuid();
        HostApply hostApply = hostApplyService.getListWithCondition(null, null, null, new String[]{hostUuid});
    }

    private void validParams(HostDto hostDto) {
        if (StringUtils.isEmpty(hostDto.getLiveNo())) {
            throw new ServiceException("uuid_empty", "请输入主播他趣id");
        } else {
            hostDto.setLiveNo(hostDto.getLiveNo().trim());
        }
        if (StringUtils.isEmpty(hostDto.getHostMobile())) {
            throw new ServiceException("host_mobile_empty", "请输入主播手机号");
        } else {
            hostDto.setHostMobile(hostDto.getHostMobile().trim());
        }
        if (StringUtils.isEmpty(hostDto.getRealName())) {
            throw new ServiceException("real_name_empty", "请输入主播真实姓名");
        } else {
            hostDto.setRealName(hostDto.getRealName().trim());
        }
        if (null == hostDto.getTeamId()) {
            throw new ServiceException("team_empty", "请选一个团队");
        }
        if (null == hostDto.getProfitRate()) {
            throw new ServiceException("profit_empty", "请选择分润比例");
        }
        if (hostDto.getHostMobile().length() != 11) {
            throw new ServiceException("host_mobile_error", "请输入主播11位手机号");
        }
        if (hostDto.getRealName().length() > 32) {
            throw new ServiceException("real_name_error", "主播真实姓名超出长度");
        }
        if (StringUtils.isNotEmpty(hostDto.getRemark()) && hostDto.getRemark().trim().length() > 255) {
            throw new ServiceException("remark_out_of_length", "备注超出长度");
        } else {
            hostDto.setRemark(hostDto.getRemark().trim());
        }

    }


    /**
     * 直播邀请主播
     *
     * @param accountUuid
     * @param hostUuid
     * @param profilt
     * @param teamId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTeamHost(String accountUuid, String hostUuid, Integer profilt, Long teamId, Integer forceSameOrgWithOtherTeam, Integer hostType) {
        log.info("邀请主播同意-经纪人：{},主播：{},分润：{},团队id：{},主播类型:{}", accountUuid, hostUuid, profilt, teamId, hostType);
        if (null == profilt) {
            profilt = 0;
        }
        if (StringUtils.isEmpty(hostUuid)) {
            log.warn("主播uuid:{}为空", hostUuid);
            throw new ServiceException("host_uuid_is_empty", "主播uuid为空");
        }
        TeamEmployee employee = null;
        if (StringUtils.isNotEmpty(accountUuid)) {
            SysUser sysUser = sysUserService.selectUserByAccountUuid(accountUuid);
            if (null != sysUser) {
                employee = teamEmployeeService.getOneByUserIdAndType(sysUser.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            }
            if (employee == null) {
                log.info("邀请主播 的经纪人uuid:{},不存在：{}", accountUuid);
            }
        }

        Team team = teamMapper.selectByPrimaryKey(teamId);
        if (team == null || Objects.equals(team.getStatus(), Constants.NO_0)) {
            log.warn("邀请主播 团队id：{},不存在", teamId);
            throw new ServiceException("team_is_not_exsist", "团队不存在");
        }
        if (Objects.equals(forceSameOrgWithOtherTeam, Constants.YES_1)) {
            // 判断是否同一个机构
            Boolean inTaquOrPersonalTeam = SpringUtils.getBean(HostService.class).isInTaquOrPersonalTeam(teamId);
            if (!inTaquOrPersonalTeam) {
                checkLiveOrgSameWithChat(team.getOrgId(), hostUuid);
            }
        }
        List<TeamHost> hostByHostUuid = getHostByHostUuid(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
        Long oldTeamId = 0L;
        Long oldOrgId = 0L;
        TeamHost teamHost;
        String historyProfit = "0";
        if (CollectionUtils.isNotEmpty(hostByHostUuid)) {
            teamHost = hostByHostUuid.get(0);
            historyProfit = teamHost.getCurrentSharingProfitRate();
            log.info("主播：{}，调整团队从团队-》：{}，到团队-》{} ", hostUuid, teamHost.getTeamId(), teamId);
            if (teamHost.getTeamId() == null) {
                log.info("当前邀请的主播：{}， 不在一个团队中", teamHost.getHostUuid());
            }
            if (teamId.equals(teamHost.getTeamId())) {
                log.info("当前邀请已经在团队id:{} 中 ", teamId);
            }
            if (teamHost.getTeamId() != null && teamHost.getOrgId() != null) {
                oldTeamId = teamHost.getTeamId();
                oldOrgId = teamHost.getOrgId();
            }
            if (employee != null) {
                if (!employee.getTeamId().equals(teamId)) {
                    log.info("当前邀请的主播的经纪人所在团队：{}，与邀请的团队:{}， 不是同一个团队,经纪人设置空", employee.getTeamId(), teamId);
                    teamHost.setEmployeeId(null);
                } else {
                    teamHost.setEmployeeId(employee.getEmployeeId());
                }
            } else {
                teamHost.setEmployeeId(null);
            }
            if (Objects.nonNull(hostType)) {
                teamHost.setHostType(hostType);
            }
            teamHost.setTeamId(team.getTeamId());
            teamHost.setOrgId(team.getOrgId());
            teamHost.setUpdateTime(DateUtil.currentTimeSeconds());
            teamHost.setCurrentSharingProfitRate(String.valueOf(profilt));
            teamHost.setStatus(1);
            teamHost.setIsGroup(Constants.NO_0);
            teamHostMapper.updateByPrimaryKey(teamHost);
        } else {
            teamHost = new TeamHost();
            teamHost.setHostUuid(hostUuid);
            teamHost.setOrgId(team.getOrgId());
            teamHost.setTeamId(team.getTeamId());
            if (employee != null) {
                teamHost.setEmployeeId(Optional.ofNullable(employee.getEmployeeId()).orElse(null));
            }
            if (Objects.nonNull(hostType)) {
                teamHost.setHostType(hostType);
            }
            teamHost.setStatus(1);
            teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
            teamHost.setCurrentSharingProfitRate(String.valueOf(profilt));
            teamHost.setInviteTime(DateUtil.currentTimeSeconds());
            teamHost.setCreateTime(DateUtil.currentTimeSeconds());
            teamHost.setUpdateTime(DateUtil.currentTimeSeconds());
            this.save(teamHost);
        }
        // 增加一条日志
        ModifyRecordInfoDTO modifyRecordInfo = new ModifyRecordInfoDTO();
        modifyRecordInfo.setOldOrgId(oldOrgId);
        modifyRecordInfo.setOldTeamId(oldTeamId);
        modifyRecordInfo.setNewOrgId(teamHost.getOrgId());
        modifyRecordInfo.setNewTeamId(teamHost.getTeamId());
        hostModifyRecordService.addRecord(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue(), UUID.genUuid(), HostOperateTypeEnum.ADD_TO_LIVE_TEAM, modifyRecordInfo, "", Constants.YES_1, "", "");

        Long nowTime = System.currentTimeMillis() / 1000;
        // 插入分润记录为0 自动生效
        HostSharingProfitRecord profitRecord = new HostSharingProfitRecord();
        profitRecord.setHostUuid(teamHost.getHostUuid());
        profitRecord.setAccountUuid("admin");
        profitRecord.setType(2);
        profitRecord.setOrgId(teamHost.getOrgId());
        profitRecord.setTeamId(teamHost.getTeamId());
        profitRecord.setStatus(HostSharingProfitStatusEnum.EFFECTIVE.getValue());
        profitRecord.setCurrentSharingProfitRate(historyProfit);
        profitRecord.setNewSharingProfitRate(teamHost.getCurrentSharingProfitRate());
        profitRecord.setChangeTime(nowTime);
        profitRecord.setEffectiveTime(0L);
        profitRecord.setCreateTime(nowTime);
        profitRecord.setUpdateTime(nowTime);
        sharingProfitRecordMapper.insertSelective(profitRecord);
    }

    @Override
    public List<TeamHost> getHostByHostUuid(String hostUuid, Integer teamType) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("host_uuid", hostUuid);
        queryWrapper.eq("team_type", teamType);
        return this.list(queryWrapper);
    }


    /**
     * 获取主播操作日志列表
     *
     * @param search
     * @return
     */
    @Override
    public List<TeamHostOperateLogVo> getLogList(TeamHostOperateLogSearch search) {
        PageHelper.startPage(search.getPage() == null ? 1 : search.getPage(),
                search.getPageSize() == null ? 20 : search.getPageSize());
        List<TeamHostOperateLogVo> vos = operateLogMapper.queryByCondition(search);
        if (CollectionUtils.isNotEmpty(vos)) {
            for (TeamHostOperateLogVo item : vos) {
                Map<String, String> map = JsonUtils.stringToObject(item.getContent(), Map.class);
                String remark = "";
                if (item.getType().equals(TeamHostOperateLogEnum.CHANGE_SHARING_PROFIT_RATE.getValue())) {
                    if (StringUtils.isBlank(map.get("oldValue"))) {
                        remark = "分润比例由0" + "调整为" + map.get("newValue");
                    } else {
                        remark = "分润比例由" + map.get("oldValue") + "调整为" + map.get("newValue");
                    }
                    item.setRemark(remark);
                } else if (item.getType().equals(TeamHostOperateLogEnum.CHANGE_TEAM.getValue())) {
                    if (StringUtils.isBlank(map.get("oldValue"))) {
                        remark = "团队由无" + "调整为" + map.get("newValue");
                    } else {
                        remark = "团队由" + map.get("oldValue") + "调整为" + map.get("newValue");
                    }
                    item.setRemark(remark);
                } else if (item.getType().equals(TeamHostOperateLogEnum.CHANGE_AGENTER.getValue())) {
                    if (StringUtils.isBlank(map.get("oldValue")) && StringUtils.isNotBlank(map.get("newValue"))) {
                        remark = "经纪人由无" + "调整为" + map.get("newValue");
                    } else if (StringUtils.isNotBlank(map.get("oldValue")) && StringUtils.isNotBlank(map.get("newValue"))) {
                        remark = "经纪人由" + map.get("oldValue") + "调整为" + map.get("newValue");
                    } else if (StringUtils.isNotBlank(map.get("oldValue")) && StringUtils.isBlank(map.get("newValue"))) {
                        remark = "经纪人由" + map.get("oldValue") + "调整为" + "无";
                    }
                    item.setRemark(remark);
                }
            }
        }
        return vos;
    }

    @Override
    public List<TeamHostOperateLogVo> getLiveChangeTeamLogList(TeamHostOperateLogSearch search) {
        search.setType(TeamHostOperateLogEnum.CHANGE_TEAM.getValue());
        PageHelper.startPage(search.getPage() == null ? 1 : search.getPage(),
                search.getPageSize() == null ? 20 : search.getPageSize());
        List<TeamHostOperateLogVo> vos = operateLogMapper.queryByCondition(search);
        vos.forEach(item -> {
            String oldTeamValue = "";
            String newTeamValue = "";
            try {
                JSONObject contentJson = JSONObject.parseObject(item.getContent());
                String oldValue = contentJson.getString("oldValue");
                oldTeamValue = oldValue;
                String newValue = contentJson.getString("newValue");
                newTeamValue = newValue;

                Team oldTeam = teamMapper.teamNameHasExist(oldValue, TeamTypeEnum.LIVE_TEAM.getValue());

                if (oldTeam != null) {
                    Long orgId = oldTeam.getOrgId();
                    if (orgId != null && orgId > 0L) {
                        Organization org = organizationMapper.selectByPrimaryKey(orgId);
                        if (org != null) {
                            oldTeamValue = org.getOrgName() + "-" + oldValue;
                        }
                    }
                }
                Team newTeam = teamMapper.teamNameHasExist(newValue, TeamTypeEnum.LIVE_TEAM.getValue());
                if (newTeam != null) {
                    Long orgId = newTeam.getOrgId();
                    if (orgId != null && orgId > 0L) {
                        Organization org = organizationMapper.selectByPrimaryKey(orgId);
                        if (org != null) {
                            newTeamValue = org.getOrgName() + "-" + newValue;
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("数据解析出错,data={}", JsonUtils.objectToString2(item), e);
            }
            oldTeamValue = StringUtils.isBlank(oldTeamValue) ? "无公会" : oldTeamValue;
            newTeamValue = StringUtils.isBlank(newTeamValue) ? "无公会" : newTeamValue;
            item.setRemark(oldTeamValue + " --> " + newTeamValue);
        });
        return vos;
    }

//    /**
//     * 查询主播当前分润比例信息
//     * 有缓存
//     */
//    @Override
//    public Map<String, String> getHostSharingProfitInfo(List<String> uuidList) {
//        Map<String,String> profitRateMap = null;
//        if (CollectionUtils.isNotEmpty(uuidList)) {
//            profitRateMap = new HashMap<>();
//            // key：sharing:profit:rate:uuid:hostUuid,
//            // value：currentSharingProfitRate
//            for (String uuid : uuidList) {
//                // 先从缓存中获取
//                String hostUuidKey = RedisKeyConstant.SHARING_PROFIT_RATE_UUID.setArg(uuid);
//                String sharingProfitRate  = masterStringRedisTemplate.opsForValue().get(hostUuidKey);
//                if (StringUtils.isBlank(sharingProfitRate)) {
//                    // 如果缓存中获取不到就从库中获取再缓存到缓存中去
//                    TeamHost one = this.baseMapper.getOneByHostUuid(uuid);
//                    if (null == one) {
//                        throw new ServiceException("invalid_host","当前主播数据暂未迁移");
//                    }
//                    profitRateMap.put(uuid,one.getCurrentSharingProfitRate());
//                    masterStringRedisTemplate.opsForValue().set(hostUuidKey,one.getCurrentSharingProfitRate());
//                    masterStringRedisTemplate.expire(hostUuidKey,5, TimeUnit.HOURS);
//                } else {
//                    // 缓存中存在就直接返回
//                    profitRateMap.put(uuid,sharingProfitRate);
//                }
//            }
//        }
//        return profitRateMap;
//    }

    /**
     * 查询主播当前分润比例信息
     * 无缓存
     */
    @Override
    public Map<String, String> getHostSharingProfitInfo(List<String> uuidList) {
        Map<String, String> profitRateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(uuidList)) {
            for (String uuid : uuidList) {
                profitRateMap.put(uuid, "");
            }
            // 直接从库种获取
            List<TeamHost> teamHostList = teamHostMapper.getSharingPorfitRateList(uuidList);
            if (CollectionUtils.isNotEmpty(teamHostList)) {
                for (TeamHost host : teamHostList) {
                    //if (StringUtils.isNotBlank(profitRateMap.get(host.getHostUuid()))) {
                    profitRateMap.put(host.getHostUuid(), host.getCurrentSharingProfitRate());
                    //}
                }
            }
        }
        return profitRateMap;
    }


    /**
     * 修改主播团队或者经纪人
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeTeamOrAgenter(HostTeamOrAgentDto dto) {
        checkParams(dto);
        TeamHost teamHost = this.baseMapper.getOneByHostUuid(dto.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue());

        // 如果团队做了更改，那么一定要更改经纪人
        if (null != teamHost.getEmployeeId() && null != dto.getEmployeeId()) {
            if (teamHost.getTeamId().longValue() != dto.getTeamId().longValue() && teamHost.getEmployeeId().longValue() == dto.getEmployeeId().longValue()) {
                throw new ServiceException("param_error", "请选择该团队下的经纪人");
            }
        }
        // 之前有经纪人
        if (null != teamHost.getEmployeeId() && 0 != teamHost.getEmployeeId()) {
            // 之前有经纪人现在有经纪人
            if (null != dto.getEmployeeId()) {
                // 经纪人做了更改
                if (teamHost.getEmployeeId().longValue() != dto.getEmployeeId().longValue()) {
                    // 获取经纪人名称
                    TeamEmployee teamEmployee = teamEmployeeService.getOne(teamHost.getEmployeeId());
                    Map<String, String> agentMap = new HashMap<>();
                    agentMap.put("oldValue", teamEmployee.getEmployeeName());
                    agentMap.put("newValue", dto.getEmployeeName());
                    // 记录日志
                    TeamHostOperateLog log = new TeamHostOperateLog();
                    log.setHostUuid(dto.getHostUuid());
                    log.setType(TeamHostOperateLogEnum.CHANGE_AGENTER.getValue());
                    log.setCreateTime(DateUtil.currentTimeSeconds());
                    log.setContent(JsonUtils.objectToString(agentMap));
                    log.setOperator(getOperator());
                    saveOperateLog(log);

                    try {
                        // 这里需要同步主播更改后的经纪人到直播系统
                        hostService.changeHostBusinessman(dto.getHostUuid(), getAgentUuid(dto.getEmployeeId()));
                        logger.info("调整经纪人同步到直播系统，主播uuid:{}，经纪人id:{}", dto.getHostUuid(), dto.getEmployeeId());
                    } catch (Exception e) {
                        logger.error("调整经纪人同步到直播系统失败，主播uuid:{}，失败原因：{}", dto.getHostUuid(), JsonUtils.objectToString(e));
                        throw new ServiceException("sync_host_businessman_fail", "同步主播更改后的经纪人到直播系统失败");
                    }
                }
            }
            // 之前有经纪人，现在无经纪人
            else {
                // 获取经纪人名称
                TeamEmployee teamEmployee = teamEmployeeService.getOne(teamHost.getEmployeeId());
                Map<String, String> agentMap = new HashMap<>();
                agentMap.put("oldValue", teamEmployee.getEmployeeName());
                agentMap.put("newValue", "");
                // 记录日志
                TeamHostOperateLog log = new TeamHostOperateLog();
                log.setHostUuid(dto.getHostUuid());
                log.setType(TeamHostOperateLogEnum.CHANGE_AGENTER.getValue());
                log.setCreateTime(DateUtil.currentTimeSeconds());
                log.setContent(JsonUtils.objectToString(agentMap));
                log.setOperator(getOperator());
                saveOperateLog(log);

                try {
                    // 这里需要同步主播更改后的经纪人到直播系统
                    hostService.changeHostBusinessman(dto.getHostUuid(), "");
                    logger.info("调整经纪人同步到直播系统，主播uuid:{}", dto.getHostUuid());
                } catch (Exception e) {
                    logger.error("调整经纪人同步到直播系统失败，主播uuid:{}，失败原因：{}", dto.getHostUuid(), JsonUtils.objectToString(e));
                    throw new ServiceException("sync_host_businessman_fail", "同步主播更改后的经纪人到直播系统失败");
                }
            }
        }
        // 之前无经纪人
        else {
            // 之前无经纪人现在有经纪人
            if (null != dto.getEmployeeId()) {
                Map<String, String> agentMap = new HashMap<>();
                agentMap.put("oldValue", "");
                agentMap.put("newValue", dto.getEmployeeName());
                // 记录日志
                TeamHostOperateLog log = new TeamHostOperateLog();
                log.setHostUuid(dto.getHostUuid());
                log.setType(TeamHostOperateLogEnum.CHANGE_AGENTER.getValue());
                log.setCreateTime(DateUtil.currentTimeSeconds());
                log.setContent(JsonUtils.objectToString(agentMap));
                log.setOperator(getOperator());
                saveOperateLog(log);

                try {
                    // 这里需要同步主播更改后的经纪人到直播系统
                    hostService.changeHostBusinessman(dto.getHostUuid(), getAgentUuid(dto.getEmployeeId()));
                    logger.info("调整经纪人同步到直播系统，主播uuid:{},经纪人id:{}", dto.getHostUuid(), dto.getEmployeeId());
                } catch (Exception e) {
                    logger.error("调整经纪人同步到直播系统失败，主播uuid:{}，失败原因：{}", dto.getHostUuid(), JsonUtils.objectToString(e));
                    throw new ServiceException("sync_host_businessman_fail", "同步主播更改后的经纪人到直播系统失败");
                }
            }
        }
        // 团队做了更改
        if (teamHost.getTeamId().longValue() != dto.getTeamId().longValue()) {
            // 获取团队名称
            Team team = teamMapper.selectByPrimaryKey(teamHost.getTeamId());
            Map<String, String> teamMap = new HashMap<>();
            teamMap.put("oldValue", team.getTeamName());
            teamMap.put("newValue", dto.getTeamName());
            dto.setTeamType(team.getType());
            // 记录日志
            TeamHostOperateLog log = new TeamHostOperateLog();
            log.setHostUuid(dto.getHostUuid());
            log.setType(TeamHostOperateLogEnum.CHANGE_TEAM.getValue());
            log.setCreateTime(DateUtil.currentTimeSeconds());
            log.setContent(JsonUtils.objectToString(teamMap));
            log.setOperator(getOperator());
            saveOperateLog(log);
            Long oldOrgId = team.getOrgId();
            Long oldTeamId = team.getTeamId();

            // 大数据需求 直播转机构额外记录type = 5
            try {
                TeamHostOperateLog log2 = new TeamHostOperateLog();
                log2.setHostUuid(dto.getHostUuid());
                log2.setType(TeamHostOperateLogEnum.LIVE_CHANGE_TEAM.getValue());
                log2.setCreateTime(DateUtil.currentTimeSeconds());
                Map<String, Long> map2 = new HashMap<>();
                if (null != team && team.getTeamId() != null) {
                    map2.put("oldValue", team.getTeamId());
                } else {
                    map2.put("oldValue", 0L);
                }
                map2.put("newValue", dto.getTeamId() == null ? 0L : dto.getTeamId());
                log2.setContent(JsonUtils.objectToString(map2));
                log2.setOperator(getOperator());
                saveOperateLog(log2);
            } catch (Exception e) {
                logger.warn("记录直播变更团队日志数据异常.type=5.teamId={}.hostIds={}", team == null ? "null" : team.getTeamId(), dto.getHostUuid(), e);
            }
            // 插入日志 用户端变更 - 只变更团队id
            ModifyRecordInfoDTO modifyRecordInfo = new ModifyRecordInfoDTO();
            modifyRecordInfo.setOldOrgId(oldOrgId);
            modifyRecordInfo.setOldTeamId(oldTeamId);
            modifyRecordInfo.setNewOrgId(oldOrgId);
            modifyRecordInfo.setNewTeamId(dto.getTeamId());
            hostModifyRecordService.addRecord(dto.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), UUID.genUuid(), HostOperateTypeEnum.USER_CHANGE_TEAM, modifyRecordInfo, "", Constants.YES_1, "", getOperator());

            // 同步主播团队到直播系统
            String consortiaId = String.valueOf(dto.getTeamId());
            List<String> hostUuidList = new ArrayList<>();
            hostUuidList.add(dto.getHostUuid());

            try {
                // 同步主播团队到直播系统
                hostService.changeHostConsortia(consortiaId, hostUuidList);
                logger.info("调整团队同步到直播系统，主播uuid:{}，团队id：{}", dto.getHostUuid(), consortiaId);
            } catch (Exception e) {
                logger.error("调整团队同步到直播系统失败，主播uuid:{}，失败原因：{}", dto.getHostUuid(), JsonUtils.objectToString(e));
                throw new ServiceException("sync_host_team_fail", "同步主播更改后的团队到直播系统失败");
            }
        }
        this.baseMapper.updateTeamOrAgenter(dto);
        logger.info("调整团队或者经纪人成功，调整信息：{}", JsonUtils.objectToString(dto));
    }

    /**
     * 获取经纪人uuid
     */
    private String getAgentUuid(Long employeeId) {
        String accountUuid = teamEmployeeMapper.accountUuidByEmployeeId(employeeId);
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException("invalid_user", "无效用户");
        }
        return accountUuid;
    }

    /**
     * 批量更改主播团队或者经纪人
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchChangeTeamOrAgenter(HostTeamOrAgentDto dto) {
        logger.info("批量调整主播经纪人或者团队：{}", JsonUtils.objectToString(dto));
        if (CollectionUtils.isEmpty(dto.getUuidList())) {
            throw new ServiceException("no_host", "请选择主播");
        }
        List<String> uuidList = dto.getUuidList();
        for (String uuid : uuidList) {
            dto.setHostUuid(uuid);
            changeTeamOrAgenter(dto);
        }
    }

    /**
     * 每个月零晨00:00:01秒执行一次
     * 邀约主播入驻成功后当前分润比例存入currentSharingProfitRate字段，newSharingProfitRate字段为空，isUpdate字段默认为0
     * 定时任务轮询 isUpdate为1的有效主播，将主播调整后的分润比例（newSharingProfitRate字段）的值更新到主播当前分润比例（currentSharingProfitRate字段）中去，并将isUpdate置为0
     * 当用户端执行调整主播分润比例操作 将调整后的分润比例存入（newSharingProfitRate）字段并将isUpdate字段置为1
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSharingProfitRateCron() {
        logger.info("当前月调整主播分润比例任务开始执行，执行时间：{}", System.currentTimeMillis() / 1000);

        Integer idStart = 0;
        Integer step = 1000;
        Integer maxId = this.baseMapper.getMaxId();
        SharingCronSearch cronSearch = new SharingCronSearch();
        cronSearch.setStartTime(LiveDateUtils.getFirstTimeOfBeforeMonth());
        cronSearch.setEndTime(LiveDateUtils.getFirstTimeOfMonth());
        while (idStart <= maxId) {
            Integer idEnd = idStart + step;
            // 分批获取isUpdate为1的主播列表
            cronSearch.setIdStart(idStart);
            cronSearch.setIdEnd(idEnd);
            List<TeamHost> teamHostList = this.baseMapper.getTeamHostList(cronSearch);
            logger.info("当前批次起始id:{},获取到批次列表：{}", idStart, JsonUtils.objectToString(teamHostList));
            if (CollectionUtils.isNotEmpty(teamHostList)) {
                for (TeamHost host : teamHostList) {
                    host.setCurrentSharingProfitRate(host.getNewSharingProfitRate());
                    host.setIsUpdate(SharingProfitRateIsUpdateEnum.NO.getValue());
                }
                // 批量更新
                this.baseMapper.batchUpdateHost(teamHostList);
                try {
                    for (TeamHost host : teamHostList) {
                        SharingProfitRecordSearch search = new SharingProfitRecordSearch();
                        search.setHostUuid(host.getHostUuid());
                        search.setStatus(HostSharingProfitStatusEnum.TO_BE_EFFECTIVE.getValue());
                        search.setOrgId(host.getOrgId());
                        search.setStartTime(LiveDateUtils.getFirstTimeOfBeforeMonth());
                        search.setEndTime(LiveDateUtils.getFirstTimeOfMonth());
                        HostSharingProfitRecord one = sharingProfitRecordMapper.getOneRecordBySearch(search);
                        if (null == one) {
                            continue;
                        }
                        one.setStatus(HostSharingProfitStatusEnum.EFFECTIVE.getValue());
                        one.setUpdateTime(System.currentTimeMillis() / 1000);
                        one.setEffectiveTime(System.currentTimeMillis() / 1000);
                        sharingProfitRecordMapper.updateByPrimaryKey(one);
                    }
                } catch (Exception e) {
                    logger.error("当前月调整主播分润比例任务执行到修改分润记录状态为已生效时失败，失败原因：{}", e);
                }
            }

            idStart = idStart + step;
            try {
                TimeUnit.SECONDS.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        logger.info("当前月调整主播分润比例任务执行结束，结束时间：{}", System.currentTimeMillis() / 1000);
    }

    @Override
    public LiveHostSelfStatisticVo getLiveHostSelfByAgent(TeamHost teamHost) {
        //搜索参数
        if (StringUtils.isNotEmpty(teamHost.getEndTime()) && StringUtils.isNotEmpty(teamHost.getStartTime())) {
            String newStartTime = teamHost.getStartTime() + " 00:00:00";
            String newEndTime = teamHost.getEndTime() + " 23:59:59";
            teamHost.setEndTime(newEndTime);
            teamHost.setStartTime(newStartTime);
        }
        TeamHost host = teamHostMapper.getOneByHostUuid(teamHost.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
        if (host == null) {
            throw new ServiceException("invalid_user", "未找到该主播");
        }
        LiveHostSelfStatisticVo liveHostSelfStatisticVo = gonghuiService.getHostLiveTimeStat(teamHost.getHostUuid(), teamHost.getStartTime(), teamHost.getEndTime());
        if (host != null) {
            Team team = teamMapper.selectById(host.getTeamId());
            Organization organization = organizationMapper.selectByPrimaryKey(host.getOrgId());
            liveHostSelfStatisticVo.setOrgName(organization.getOrgName());
            liveHostSelfStatisticVo.setTeamName(team.getTeamName());
            liveHostSelfStatisticVo.setTeamType(host.getTeamType());
            liveHostSelfStatisticVo.setCurrentSharingProfitRate(host.getCurrentSharingProfitRate());
            liveHostSelfStatisticVo.setIsUpdate(host.getIsUpdate());
            liveHostSelfStatisticVo.setAgentName("");
            liveHostSelfStatisticVo.setHostType(host.getHostType());
            liveHostSelfStatisticVo.setHostTypeName(HostTypeEnum.getName(host.getHostType()));
            if (host.getEmployeeId() != null) {
                TeamEmployee teamEmployee = teamEmployeeMapper.selectById(host.getEmployeeId());
                if (teamEmployee != null) {
                    liveHostSelfStatisticVo.setAgentName(teamEmployee.getEmployeeName());
                }
            }

            // 设置主播最近一次分润比例调整记录的调整状态
            SharingProfitRecordSearch search = new SharingProfitRecordSearch();
            search.setHostUuid(host.getHostUuid());
            search.setOrgId(host.getOrgId());
            HostSharingProfitRecord one = sharingProfitRecordMapper.getLastedOneRecord(search);
            if (null != one) {
                liveHostSelfStatisticVo.setChangeStatus(one.getStatus());
                liveHostSelfStatisticVo.setNewSharingProfitRate(one.getNewSharingProfitRate());
                liveHostSelfStatisticVo.setOldSharingProfitRate(one.getCurrentSharingProfitRate());
            }

        } else {
            liveHostSelfStatisticVo.setAgentName("");
            liveHostSelfStatisticVo.setOrgName("");
            liveHostSelfStatisticVo.setTeamType(1);
            liveHostSelfStatisticVo.setNewSharingProfitRate("0");
        }
        liveHostSelfStatisticVo.getList().forEach(e -> {
            e.setDuration_Str(secondToTime(e.getDuration().longValue()));
            e.setTotal_live_time_Str(secondToTime(e.getTotal_live_time().longValue()));
        });
        return liveHostSelfStatisticVo;
    }

    /**
     * 返回日时分秒
     *
     * @param second
     * @return
     */
    private String secondToTime(long second) {
        long days = second / 86400;//转换天数
        second = second % 86400;//剩余秒数
        long hours = second / 3600;//转换小时数
        second = second % 3600;//剩余秒数
        long minutes = second / 60;//转换分钟
        second = second % 60;//剩余秒数
        String time = "";
        if (hours != 0) {
            time = hours + "时" + minutes + "分" + second;
        } else {
            time = minutes + "分" + second;
        }
        return time;
    }

    /**
     * 调整分润比例/调整团队/调整经纪人记录日志
     */
    private void saveOperateLog(TeamHostOperateLog log) {
        operateLogMapper.insert(log);
    }

    /**
     * 获取当前操作人
     *
     * @return
     */
    private String getOperator() {
        return tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getUserName();
    }

    private void checkParams(HostTeamOrAgentDto dto) {
        if (null == dto.getTeamId()) {
            throw new ServiceException("param_error", "请选择团队");
        }
        if (StringUtils.isBlank(dto.getTeamName())) {
            throw new ServiceException("param_error", "请选择团队");
        }
        if (StringUtils.isBlank(dto.getHostUuid())) {
            throw new ServiceException("param_error", "请输入主播信息");
        }
        // 如果经纪人不为空，那么判断当前经纪人是否属于当前团队
        if (null != dto.getEmployeeId() && 0 != dto.getEmployeeId()) {
            TeamEmployee one = teamEmployeeService.getOne(dto.getEmployeeId());
            if (null == one) {
                throw new ServiceException("invalid_agent", "当前选择经纪人无效");
            }
            if (!dto.getTeamId().equals(one.getTeamId())) {
                throw new ServiceException("invalid_operate", "当前经纪人不属于当前团队");
            }
        }
    }


    /**
     * 返回参数搜索的uuid
     */
    private HostStatisticSearch getSearch(HostStatisticSearch teamHostSearch) {
        log.info("主播列表-HostStatisticSearch:{}", JsonUtils.objectToString(teamHostSearch));
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser.getUser().getOrgId() == null) {
            throw new ServiceException("param_error", "该用户没有绑定机构");
        }
        RoleVo userRole = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        HostStatisticSearch uuidSearch = new HostStatisticSearch();
        List<Long> teamIds = new ArrayList<>();
        List<String> agentUuids = new ArrayList<>();
        List<String> hostUuid = new ArrayList<>();
        if (UserTypeEnum.AGENTER.getCode().equals(userRole.getRoleKey())) {
            List<Team> teamList = teamMapper.getTeamTreeByRole(loginUser.getUser().getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), loginUser.getUser().getOrgId());
            teamIds = teamList.stream().map(Team::getTeamId).collect(Collectors.toList());
            uuidSearch.setTeamIds(teamIds);
            agentUuids.add(loginUser.getUser().getAccountUuid());
            uuidSearch.setBusinessmanUuids(agentUuids);
        }
        if (UserTypeEnum.LEADER.getCode().equals(userRole.getRoleKey())) {
            List<Team> teamList = teamMapper.getTeamTreeByRole(loginUser.getUser().getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), loginUser.getUser().getOrgId());
            teamIds = teamList.stream().map(Team::getTeamId).collect(Collectors.toList());
            uuidSearch.setTeamIds(teamIds);
        }
        if (UserTypeEnum.MANAGER.getCode().equals(userRole.getRoleKey())) {
            if (loginUser.getUser().getOrgId() != null) {
                List<Long> teams = teamMapper.selectTeamListIds(loginUser.getUser().getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
                if (teams != null) {
                    uuidSearch.setTeamIds(teams);
                }
            }
            if (CollectionUtils.isNotEmpty(teamHostSearch.getTeamIds())) {
                uuidSearch.setTeamIds(teamHostSearch.getTeamIds());
            }
        }
        //如果传入值是团队传入
        if (CollectionUtils.isNotEmpty(teamHostSearch.getTeamIds()) && UserTypeEnum.AGENTER.getCode().equals(userRole.getRoleKey())) {
            teamIds.clear();
            agentUuids.clear();
            hostUuid.clear();
            uuidSearch.setTeamIds(teamHostSearch.getTeamIds());
        }
        //如果传入值有根据经纪人传入，则根据经纪人查询显示结果
        if (CollectionUtils.isNotEmpty(teamHostSearch.getAgentId())) {
            agentUuids.clear();
            hostUuid.clear();
            for (int i = 0; i < teamHostSearch.getAgentId().size(); i++) {
                TeamEmployee teamEmployee = teamEmployeeMapper.selectById(teamHostSearch.getAgentId().get(i));
                if (teamEmployee != null && teamEmployee.getUserId() != null) {
                    SysUser sysUser = sysUserService.selectUserByUserId(teamEmployee.getUserId());
                    if (sysUser != null && StringUtils.isNotEmpty(sysUser.getAccountUuid())) {
                        agentUuids.add(sysUser.getAccountUuid());
                    }
                }
            }
            uuidSearch.setBusinessmanUuids(agentUuids);
        }
        //如果传入有主播uuid，则根据主播uuid查询
        if (StringUtils.isNotEmpty(teamHostSearch.getHost_uuid())) {
            hostUuid.clear();
            TeamHost teamHost = teamHostMapper.getOneByHostUuid(teamHostSearch.getHost_uuid(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (teamHost != null) {
                if (UserTypeEnum.AGENTER.getCode().equals(userRole.getRoleKey())) {
                    Long employeeId = teamEmployeeMapper.seletEmployeeId(loginUser.getUser().getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
                    if (teamHost.getEmployeeId() != null && teamHost.getEmployeeId().equals(employeeId)) {
                        hostUuid.add(teamHostSearch.getHost_uuid());
                    } else {
                        hostUuid.add("host is null");
                    }
                }
                if (UserTypeEnum.LEADER.getCode().equals(userRole.getRoleKey())) {
                    List<Team> teamList = teamMapper.getTeamTreeByRole(loginUser.getUser().getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), loginUser.getUser().getOrgId());
                    teamIds = teamList.stream().map(Team::getTeamId).collect(Collectors.toList());
                    if (teamHost.getTeamId() != null && teamIds.contains(teamHost.getTeamId())) {
                        hostUuid.add(teamHostSearch.getHost_uuid());
                    } else {
                        hostUuid.add("host is null");
                    }
                }
                if (UserTypeEnum.MANAGER.getCode().equals(userRole.getRoleKey())) {
                    if (teamHost.getOrgId() != null && loginUser.getUser().getOrgId().equals(teamHost.getOrgId())) {
                        hostUuid.add(teamHostSearch.getHost_uuid());
                    } else {
                        hostUuid.add("host is null");
                    }
                }
            } else {
                hostUuid.add("host is null");
            }
            uuidSearch.setHostUuidList(hostUuid);
        }
        return uuidSearch;
    }

    @Override
    public HostVo getLiveHostListUser(HostStatisticSearch teamHostSearch) {
        Organization organization = new Organization();
        //将入参传入符合查询条件的主播uuid塞入list集合中
        HostStatisticSearch uuidSearch = getSearch(teamHostSearch);
        HostVo hostVos = gonghuiService.getHostList(uuidSearch.getTeamIds(), uuidSearch.getBusinessmanUuids(), uuidSearch.getHostUuidList(), teamHostSearch.getPage(), teamHostSearch.getPageSize(), teamHostSearch.getLive_status(), null, teamHostSearch.getLive_no(), teamHostSearch.getHost_status(), null, null, teamHostSearch.getApply_level(), teamHostSearch.getOrder_type(), teamHostSearch.getStart(), teamHostSearch.getEnd(), null, null, teamHostSearch.getIsUpdate());
        if (CollectionUtils.isNotEmpty(hostVos.getList())) {
            Organization finalOrganization = organization;
            hostVos.getList().forEach(hostEach -> {
                hostEach.setAgentName("");
                TeamHost teamHost = teamHostMapper.getOneByHostUuid(hostEach.getHost_uuid(), TeamTypeEnum.LIVE_TEAM.getValue());
                if (finalOrganization != null) {
                    hostEach.setOrgName(finalOrganization.getOrgName());
                }
                hostEach.setOrgName("");
                if (teamHost != null) {
                    Team team = teamMapper.selectByPrimaryKey(teamHost.getTeamId());
                    if (team != null) {
                        hostEach.setTeamName(team.getTeamName());
                    }
                    TeamEmployee teamEmployee = teamEmployeeMapper.selectById(teamHost.getEmployeeId());
                    if (teamEmployee != null) {
                        hostEach.setAgentName(teamEmployee.getEmployeeName());
                    }

                    // 查询是否有分润记录
                    SharingProfitRecordSearch search = new SharingProfitRecordSearch();
                    search.setHostUuid(hostEach.getHost_uuid());
                    search.setOrgId(teamHost.getOrgId());
                    HostSharingProfitRecord oneRecordBySearch = sharingProfitRecordMapper.getLastedOneRecord(search);
                    if (null != oneRecordBySearch) {
                        hostEach.setOldSharingRate(oneRecordBySearch.getCurrentSharingProfitRate());
                        hostEach.setNewSharingRate(oneRecordBySearch.getNewSharingProfitRate());
                        hostEach.setSharingStatus(oneRecordBySearch.getStatus());
                    }
                    hostEach.setCurrentSharingRate(teamHost.getCurrentSharingProfitRate());
                }
            });
        }
        return hostVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateHostInfo(Long teamId, String hostUuid) {
        if (null == teamId) {
            throw new ServiceException("synchronize_host_info_fail", "团队id不能为空");
        }
        if (StringUtils.isBlank(hostUuid)) {
            throw new ServiceException("synchronize_host_info_fail", "主播uuid不能为空");
        }
        // 验证团队是否有效
        Team team = teamMapper.selectByPrimaryKey(teamId);
        if (null == team || TeamStatusEnum.NO_VALID.equals(team.getStatus()) || null == team.getOrgId()) {
            logger.warn("待同步团队无效，主播uuid:{},团队id:{}", hostUuid, teamId);
            throw new ServiceException("invalid_team", "当前团队无效");
        }
        if (TeamTypeEnum.TALK_TEAM.getValue() == team.getType()) {
            throw new ServiceException("invalid_team", "聊天室团队不能调整");
        }
        Boolean inTaquOrPersonalTeam = SpringUtils.getBean(HostService.class).isInTaquOrPersonalTeam(teamId);
        if (!inTaquOrPersonalTeam) {
            checkLiveOrgSameWithChat(team.getOrgId(), hostUuid);
        }

        TeamHost host = teamHostMapper.getOneByHostUuid(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
        if (null == host) {
            Long currentTime = System.currentTimeMillis() / 1000;
            logger.warn("待同步主播不存在，进行迁移，主播uuid:{},团队id:{}", hostUuid, teamId);
            TeamHost hostRecord = new TeamHost();
            hostRecord.setHostUuid(hostUuid);
            hostRecord.setCurrentSharingProfitRate("0");
            hostRecord.setTeamId(teamId);
            hostRecord.setEmployeeId(null);
            hostRecord.setTeamType(team.getType());
            hostRecord.setOrgId(team.getOrgId());
            hostRecord.setCreateTime(currentTime);
            hostRecord.setUpdateTime(currentTime);
            hostRecord.setChangeTime(currentTime);
            hostRecord.setInviteTime(currentTime);
            hostRecord.setNewSharingProfitRate("");
            hostRecord.setIsUpdate(0);
            hostRecord.setStatus(1);
            teamHostMapper.insert(hostRecord);
            return;
        }
        Long oldOrgId = host.getOrgId();
        // 获取主播以前所属团队名称
        Team oldTeam = null;
        if (null != host.getTeamId()) {
            oldTeam = teamMapper.selectByPrimaryKey(host.getTeamId());
        }

        // 跨机构的主播
        List<String> outTeamList = new ArrayList<>();
        // 团队调整是跨机构
        if (!team.getOrgId().equals(host.getOrgId())) {
            host.setCurrentSharingProfitRate("");
            host.setTeamId(teamId);
            host.setEmployeeId(null);
            host.setTeamType(team.getType());
            host.setOrgId(team.getOrgId());
            host.setUpdateTime(System.currentTimeMillis() / 1000);
            host.setNewSharingProfitRate("");
            host.setIsUpdate(0);
            outTeamList.add(hostUuid);
        } else {
            host.setTeamId(teamId);
            host.setOrgId(team.getOrgId());
            host.setEmployeeId(null);
            host.setTeamType(team.getType());
            host.setUpdateTime(System.currentTimeMillis() / 1000);
        }
        try {
            teamHostMapper.updateByPrimaryKey(host);
            if (null != oldTeam) {
                logger.info("[updateHostInfo]php修改主播所属公会同步成功,主播uuid:{},调整前团队id:{},调整后团队id：{},调整前机构id:{},调整后机构id:{}", hostUuid, oldTeam.getTeamId(), team.getTeamId(), oldTeam.getOrgId(), team.getOrgId());
            } else {
                logger.info("[updateHostInfo]php修改主播所属公会同步成功,主播uuid:{},调整前团队id:{},调整后团队id：{},调整前机构id:{},调整后机构id:{}", hostUuid, "无", team.getTeamId(), oldTeam.getOrgId(), team.getOrgId());
            }
        } catch (Exception e) {
            logger.error("同步修改主播公会失败,主播信息：{},团队信息：{}", JsonUtils.objectToString(host), JsonUtils.objectToString(team));
            throw new ServiceException("同步修改主播公会失败，失败原因：{}", e);
        }
        try {
            // 添加主播操作日志
            Map<String, String> map = new HashMap<>();
            if (null != oldTeam) {
                map.put("oldValue", oldTeam.getTeamName());
            } else {
                map.put("oldValue", "");
            }
            map.put("newValue", team.getTeamName());
            TeamHostOperateLog log = new TeamHostOperateLog();
            log.setHostUuid(hostUuid);
            log.setType(TeamHostOperateLogEnum.CHANGE_TEAM.getValue());
            log.setCreateTime(DateUtil.currentTimeSeconds());
            log.setContent(JsonUtils.objectToString(map));
            log.setOperator(RequestParams.getBaseParam().getToken());
            saveOperateLog(log);

            // 大数据需求 直播转机构额外记录type = 5
            try {
                TeamHostOperateLog log2 = new TeamHostOperateLog();
                log2.setHostUuid(hostUuid);
                log2.setType(TeamHostOperateLogEnum.LIVE_CHANGE_TEAM.getValue());
                log2.setCreateTime(DateUtil.currentTimeSeconds());
                Map<String, Long> map2 = new HashMap<>();
                if (null != oldTeam && oldTeam.getTeamId() != null) {
                    map2.put("oldValue", oldTeam.getTeamId());
                } else {
                    map2.put("oldValue", 0L);
                }
                map2.put("newValue", team.getTeamId() == null ? 0L : team.getTeamId());
                log2.setContent(JsonUtils.objectToString(map2));
                log2.setOperator(RequestParams.getBaseParam().getToken());
                saveOperateLog(log2);
            } catch (Exception e) {
                logger.warn("记录直播变更团队日志数据异常.type=5.teamId={}.hostIds={}", teamId, hostUuid, e);
            }

            // 如果该主播在转会前有待确认得调整记录，则修改为拒绝
            SharingProfitRecordSearch search = new SharingProfitRecordSearch();
            search.setOrgId(oldOrgId);
            search.setHostUuid(hostUuid);
            search.setStatus(HostSharingProfitStatusEnum.PENDING.getValue());
            HostSharingProfitRecord one = sharingProfitRecordMapper.getOneRecordBySearch(search);
            if (one != null) {
                one.setStatus(HostSharingProfitStatusEnum.REJECT.getValue());
                one.setUpdateTime(System.currentTimeMillis() / 1000);
                one.setEffectiveTime(0L);
                sharingProfitRecordMapper.updateInfo(one);
                logger.info("单个主播转移公会 - 将之前待确认的分润比例调整修改为拒绝状态,主播uuid:{},调整记录id:{}", one.getHostUuid(), one.getId());
            }

            // 增加一条日志
            ModifyRecordInfoDTO modifyRecordInfo = new ModifyRecordInfoDTO();
            modifyRecordInfo.setOldOrgId(0L);
            modifyRecordInfo.setOldTeamId(0L);
            if (oldTeam != null) {
                modifyRecordInfo.setOldOrgId(oldTeam.getOrgId());
                modifyRecordInfo.setOldTeamId(oldTeam.getTeamId());
            }
            modifyRecordInfo.setNewOrgId(host.getOrgId());
            modifyRecordInfo.setNewTeamId(host.getTeamId());
            hostModifyRecordService.addRecord(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue(), UUID.genUuid(), HostOperateTypeEnum.UPDATE_LIVE_TEAM, modifyRecordInfo, "", Constants.YES_1, "", "");

            // 推送钉钉通知
            TeamOrgInfoVO oldTeamOrgInfoVO = null;
            TeamOrgInfoVO newTeamOrgInfoVO = null;
            if (oldTeam == null) {
                oldTeamOrgInfoVO = new TeamOrgInfoVO();
                oldTeamOrgInfoVO.setOrgAndTeamInfo("无公会");
            } else {
                oldTeamOrgInfoVO = SpringUtils.getBean(TeamService.class).getTeamOrgInfoVO(oldTeam.getTeamId());
            }
            newTeamOrgInfoVO = SpringUtils.getBean(TeamService.class).getTeamOrgInfoVO(host.getTeamId());
            pushService.pushTeamHostChangeTeamToDingRobot(hostUuid, SoaBaseParams.fromThread().getToken(), TeamTypeEnum.LIVE_TEAM.getValue(), oldTeamOrgInfoVO, newTeamOrgInfoVO);

        } catch (Exception e) {
            logger.error("[updateHostInfo]php修改主播所属公会同步添加主播操作日志失败，失败原因：{}", e);
        }

        // 重置主播 电子分润状态为 待开启
        if (CollectionUtils.isNotEmpty(outTeamList)) {
            // 直播研发楷木说可以不用调用
            //try {
            //    hostInfoService.changeHostSplitStatus(outTeamList, "0");
            //} catch (Exception e) {
            //    logger.error("批量转移公会重置主播电子分润状态为待开启失败，主播uuid:{},失败原因：{}", hostUuid, JsonUtils.objectToString(e));
            //    throw new ServiceException("change_host_is_set_split_fail", "批量转移主播重置主播电子分润状态为待开启失败");
            //}
        }
    }

    /**
     * 批量调整主播所属公会
     *
     * @param teamId
     * @param hostUuids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeHostTeam(Long teamId, List<String> hostUuids) {
        if (null == teamId) {
            throw new ServiceException("synchronize_host_info_fail", "团队id不能为空");
        }
        if (CollectionUtils.isEmpty(hostUuids)) {
            throw new ServiceException("synchronize_host_info_fail", "主播uuid不能为空");
        }
        // 获取当前系统环境
        String env = getEnv();
        if (StringUtils.isBlank(env)) {
            throw new ServiceException("invalid_env", "获取当前系统运行环境失败");
        }
        // 验证团队是否有效
        Team team = teamMapper.selectByPrimaryKey(teamId);
        if (null == team || TeamStatusEnum.NO_VALID.equals(team.getStatus()) || null == team.getOrgId()) {
            logger.warn("待同步团队无效，主播uuid:{},团队id:{}", JsonUtils.objectToString(hostUuids), teamId);
            throw new ServiceException("invalid_team", "当前团队无效");
        }
        if (TeamTypeEnum.TALK_TEAM.getValue() == team.getType()) {
            throw new ServiceException("invalid_team", "聊天室团队不能调整");
        }
        // 跨团队调整的主播
        List<String> outTeamList = new ArrayList<>();

        for (String hostUuid : hostUuids) {

            TeamHost host = teamHostMapper.getOneByHostUuid(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
            if (null == host) {
                logger.warn("待同步主播不存在，主播uuid:{},团队id:{}", hostUuid, teamId);
                throw new ServiceException("invalid_host", "当前主播不存在");
            }
            if ("online".equals(env) || "gray".equals(env)) {
                if (1001281 == host.getOrgId() || 1001284 == host.getOrgId()) {
                    throw new ServiceException("invalid_operate", "您的选择包含素人和中转公会，请重新检查");
                }
            } else {
                if (1000703 == host.getOrgId() || 1000748 == host.getOrgId()) {
                    throw new ServiceException("invalid_operate", "您的选择包含素人和中转公会，请重新检查");
                }
            }

            // 获取主播以前机构id
            Long oldOrgId = 0L;
            Long oldTeamId = 0L;
            // 获取主播以前所属团队名称
            Team oldTeam = null;
            if (null != host.getTeamId()) {
                oldTeam = teamMapper.selectByPrimaryKey(host.getTeamId());
                oldTeamId = host.getTeamId();
            }
            if (host != null && host.getOrgId() != null) {
                oldOrgId = host.getOrgId();
            }

            // 如果修改得团队跨机构
            if (!team.getOrgId().equals(host.getOrgId())) {
                host.setCurrentSharingProfitRate("");
                host.setTeamId(teamId);
                host.setEmployeeId(null);
                host.setTeamType(team.getType());
                host.setOrgId(team.getOrgId());
                host.setUpdateTime(System.currentTimeMillis() / 1000);
                host.setNewSharingProfitRate("");
                host.setIsUpdate(0);
                outTeamList.add(hostUuid);
            } else {
                host.setTeamId(teamId);
                host.setOrgId(team.getOrgId());
                host.setEmployeeId(null);
                host.setTeamType(team.getType());
                host.setUpdateTime(System.currentTimeMillis() / 1000);
            }

            try {
                teamHostMapper.updateByPrimaryKey(host);
                if (null != oldTeam) {
                    logger.info("修改主播所属公会成功,主播uuid:{},调整前团队id:{},调整后团队id：{},调整前机构id:{},调整后机构id:{}", hostUuid, oldTeam.getTeamId(), team.getTeamId(), host.getOrgId(), team.getOrgId());
                } else {
                    logger.info("修改主播所属公会成功,主播uuid:{},调整前团队id:{},调整后团队id：{},调整前机构id:{},调整后机构id:{}", hostUuid, "无", team.getTeamId(), host.getOrgId(), team.getOrgId());
                }
            } catch (Exception e) {
                logger.error("修改主播公会失败,主播信息：{},团队信息：{}", JsonUtils.objectToString(host), JsonUtils.objectToString(team));
                throw new ServiceException("修改主播公会失败，失败原因：{}", e);
            }
            try {
                // 添加主播操作日志
                Map<String, String> map = new HashMap<>();
                if (null != oldTeam) {
                    map.put("oldValue", oldTeam.getTeamName());
                } else {
                    map.put("oldValue", "");
                }
                map.put("newValue", team.getTeamName());
                TeamHostOperateLog log = new TeamHostOperateLog();
                log.setHostUuid(hostUuid);
                log.setType(TeamHostOperateLogEnum.CHANGE_TEAM.getValue());
                log.setCreateTime(DateUtil.currentTimeSeconds());
                log.setContent(JsonUtils.objectToString(map));
                log.setOperator(RequestParams.getBaseParam().getToken());
                saveOperateLog(log);

                // 大数据需求 直播转机构额外记录type = 5
                try {
                    TeamHostOperateLog log2 = new TeamHostOperateLog();
                    log2.setHostUuid(hostUuid);
                    log2.setType(TeamHostOperateLogEnum.LIVE_CHANGE_TEAM.getValue());
                    log2.setCreateTime(DateUtil.currentTimeSeconds());
                    Map<String, Long> map2 = new HashMap<>();
                    if (null != oldTeam && oldTeam.getTeamId() != null) {
                        map2.put("oldValue", oldTeam.getTeamId());
                    } else {
                        map2.put("oldValue", 0L);
                    }
                    map2.put("newValue", team.getTeamId() == null ? 0L : team.getTeamId());
                    log2.setContent(JsonUtils.objectToString(map2));
                    log2.setOperator(RequestParams.getBaseParam().getToken());
                    saveOperateLog(log2);
                } catch (Exception e) {
                    logger.warn("记录直播变更团队日志数据异常.type=5.teamId={}.hostIds={}", teamId, JsonUtils.objectToString2(hostUuids), e);
                }

                // 如果该主播在转会前有待确认得调整记录，则修改为拒绝
                SharingProfitRecordSearch search = new SharingProfitRecordSearch();
                search.setOrgId(oldOrgId);
                search.setHostUuid(hostUuid);
                search.setStatus(HostSharingProfitStatusEnum.PENDING.getValue());
                HostSharingProfitRecord one = sharingProfitRecordMapper.getOneRecordBySearch(search);
                if (one != null) {
                    one.setStatus(HostSharingProfitStatusEnum.REJECT.getValue());
                    one.setUpdateTime(System.currentTimeMillis() / 1000);
                    one.setEffectiveTime(0L);
                    sharingProfitRecordMapper.updateInfo(one);
                    logger.info("批量主播转移公会 - 将之前待确认的分润比例调整修改为拒绝状态，主播uuid:{},调整记录id:{}", one.getHostUuid(), one.getId());
                }

                // 增加一条日志
                ModifyRecordInfoDTO modifyRecordInfo = new ModifyRecordInfoDTO();
                modifyRecordInfo.setOldOrgId(oldOrgId);
                modifyRecordInfo.setOldTeamId(oldTeamId);
                modifyRecordInfo.setNewOrgId(host.getOrgId());
                modifyRecordInfo.setNewTeamId(host.getTeamId());
                hostModifyRecordService.addRecord(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue(), UUID.genUuid(), HostOperateTypeEnum.BATCH_UPDATE_LIVE_TEAM, modifyRecordInfo, "", Constants.YES_1, "", "");

                // 推送钉钉通知
                TeamOrgInfoVO oldTeamOrgInfoVO = null;
                TeamOrgInfoVO newTeamOrgInfoVO = null;
                if (oldTeam == null) {
                    oldTeamOrgInfoVO = new TeamOrgInfoVO();
                    oldTeamOrgInfoVO.setOrgAndTeamInfo("无公会");
                } else {
                    oldTeamOrgInfoVO = SpringUtils.getBean(TeamService.class).getTeamOrgInfoVO(oldTeam.getTeamId());
                }
                newTeamOrgInfoVO = SpringUtils.getBean(TeamService.class).getTeamOrgInfoVO(host.getTeamId());
                pushService.pushTeamHostChangeTeamToDingRobot(hostUuid, SoaBaseParams.fromThread().getToken(), TeamTypeEnum.LIVE_TEAM.getValue(), oldTeamOrgInfoVO, newTeamOrgInfoVO);

            } catch (Exception e) {
                logger.error("[updateHostInfo]php修改主播所属公会同步添加主播操作日志失败，失败原因：{}", e);
            }

        }

        // 同步信息到直播系统
        try {
            hostService.changeHostConsortia(String.valueOf(teamId), hostUuids);
        } catch (Exception e) {
            logger.error("批量转移公会同步到直播失败，主播uuid:{},团队id:{},失败原因：{}", JsonUtils.objectToString(hostUuids), teamId, JsonUtils.objectToString(e));
            throw new ServiceException("sync_host_team_to_live_fail", "批量转移主播公会同步到直播失败");
        }

        // 重置主播 电子分润状态为 待开启
        if (CollectionUtils.isNotEmpty(outTeamList)) {
            // 直播研发楷木说可以不用调用
            //try {
            //    hostInfoService.changeHostSplitStatus(outTeamList, "0");
            //} catch (Exception e) {
            //    logger.error("批量转移公会重置主播电子分润状态为待开启失败，主播uuid:{},失败原因：{}", JsonUtils.objectToString(hostUuids), JsonUtils.objectToString(e));
            //    throw new ServiceException("change_host_is_set_split_fail", "批量转移主播重置主播电子分润状态为待开启失败");
            //}
        }
    }

    @Override
    public void updateBatchEmpIdById(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        UpdateWrapper<TeamHost> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("employee_id", null);
        updateWrapper.in("id", ids);
        this.update(updateWrapper);
    }

    @Override
    public List<TeamHost> getListByEmployeeId(Long employeeId, Integer teamType) {
        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("employee_id", employeeId);
        queryWrapper.eq("team_type", teamType);
        return this.list(queryWrapper);
    }

    /**
     * 迁移还未迁移的主播
     *
     * @param dto
     */
    @Override
    public void moveHost(HostTeamOrAgentDto dto) {
        if (CollectionUtils.isEmpty(dto.getUuidList())) {
            throw new ServiceException("invalid_host", "请选择需要修改的主播");
        }
        if (null == dto.getTeamId() || 0 == dto.getTeamId()) {
            throw new ServiceException("invalid_team", "请选择团队");
        }
        Team team = teamMapper.selectByPrimaryKey(dto.getTeamId());
        if (null == team) {
            logger.warn("invalid_team", "当前团队无效，团队id:{}", dto.getTeamId());
            throw new ServiceException("invalid_team", "当前团队无效");
        }
        // 判断team_host是否存当前主播，如果不存在进行迁移
        List<TeamHost> hostList = new ArrayList<>();
        for (String uuid : dto.getUuidList()) {
            TeamHost teamHost = this.baseMapper.getOneByHostUuid(uuid, TeamTypeEnum.LIVE_TEAM.getValue());
            if (null == teamHost) {
                logger.warn("当前主播暂未迁移，现在进行迁移，主播uuid：{}", uuid);
                LiveHostInfo liveHostInfo = liveHostInfoMapper.selectByHostUuid(uuid);
                if (null == liveHostInfo) {
                    logger.warn("当前主播暂未入驻公会，主播uuid：{}", uuid);
                }
                TeamHost hostRecord = new TeamHost();
                hostRecord.setHostUuid(uuid);
                hostRecord.setOrgId(team.getOrgId());
                hostRecord.setTeamId(dto.getTeamId());
                hostRecord.setTeamType(team.getType());
                hostRecord.setEmployeeId(dto.getEmployeeId());
                if (null != liveHostInfo.getAddConsortiaTime()) {
                    hostRecord.setInviteTime(Long.valueOf(liveHostInfo.getAddConsortiaTime()));
                } else {
                    hostRecord.setInviteTime(System.currentTimeMillis() / 1000);
                }
                hostRecord.setStatus(1);
                if (null != liveHostInfo.getCreateTime()) {
                    hostRecord.setCreateTime(Long.valueOf(liveHostInfo.getCreateTime()));
                } else {
                    hostRecord.setCreateTime(hostRecord.getInviteTime());
                }
                if (null != liveHostInfo.getUpdateTime()) {
                    hostRecord.setUpdateTime(Long.valueOf(liveHostInfo.getUpdateTime()));
                } else {
                    hostRecord.setUpdateTime(System.currentTimeMillis() / 1000);
                }
                hostRecord.setCurrentSharingProfitRate("0");
                hostRecord.setNewSharingProfitRate("");
                hostRecord.setIsUpdate(0);
                List<String> uuidList = new ArrayList<>();
                uuidList.add(uuid);
                hostList.add(hostRecord);

                try {
                    if (null != dto.getEmployeeId() && 0 != dto.getEmployeeId()) {
                        // 这里需要同步主播更改后的经纪人到直播系统
                        hostService.changeHostBusinessman(uuid, getAgentUuid(dto.getEmployeeId()));
                        logger.info("调整经纪人同步到直播系统失败，主播uuid:{},经纪人id:{}", uuid, dto.getEmployeeId());
                    }
                } catch (Exception e) {
                    logger.warn("调整经纪人同步到直播系统失败，主播uuid:{}，失败原因：{}", uuid, JsonUtils.objectToString(e));
                    throw new ServiceException("sync_host_businessman_fail", "同步主播更改后的经纪人到直播系统失败");
                }
                try {
                    if (CollectionUtils.isNotEmpty(uuidList)) {
                        // 同步主播团队到直播系统
                        hostService.changeHostConsortia(String.valueOf(dto.getTeamId()), uuidList);
                        logger.info("调整团队同步到直播系统失败，主播uuid:{}，团队id：{}", uuid, dto.getTeamId());
                    }
                } catch (Exception e) {
                    logger.warn("调整团队同步到直播系统失败，主播uuid:{}，失败原因：{}", uuid, JsonUtils.objectToString(e));
                    throw new ServiceException("sync_host_team_fail", "同步主播更改后的团队到直播系统失败");
                }
            }
        }

        // 批量插入
        if (CollectionUtils.isNotEmpty(hostList)) {
            teamHostMapper.batchInsertHost(hostList);
            logger.info("批量调整团队经纪人新公会不存在的主播进行迁移，主播uuid:{}", JsonUtils.objectToString(hostList));
        }

    }

    private String getEnv() {
        return LocalConfUtil.getLocalEnv();
    }

    public void punishHost(String hostUuid, Integer publishWay, Integer publishHour, String reason) {
        if (StringUtils.isBlank(hostUuid)) {
            logger.warn("惩罚主播接口出现异常，参数主播uuid为空");
            throw new ServiceException("host_uuid_empty", "对不起，请求出现异常！请刷新下页面");
        }
        if (publishHour < 1 || publishHour > 72) {
            throw new ServiceException("publish_hour_out_of_limit", "禁播时间范围是【0-72】小时之间");
        }
        if (StringUtils.isBlank(reason)) {
            throw new ServiceException("reason_empty", "请输入处罚理由！");
        }
        if (StringUtils.isNotBlank(reason) && StringUtils.trim(reason).length() > 200) {
            throw new ServiceException("reason_out_of_lengh", "处罚理由超出长度，请输入200字符以内的理由！");
        }
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        if (user != null) {
            gonghuiService.ghPunish(hostUuid, user.getUserName(), user.getAccountUuid(), StringUtils.trim(reason), publishHour);

        } else {
            logger.warn("处罚主播当前登录用户无法获取,用户信息：{}", JsonUtils.objectToString(user));
            throw new ServiceException("reason_out_of_lengh", "操作失败，请重新登录后再试");
        }
    }

    public void warnHost(String hostUuid, String reason) {
        if (StringUtils.isBlank(hostUuid)) {
            logger.warn("警告主播接口出现异常，参数主播uuid为空");
            throw new ServiceException("host_uuid_empty", "对不起，请求出现异常！请刷新下页面");
        }
        if (StringUtils.isBlank(reason)) {
            throw new ServiceException("reason_empty", "请选择一条警告理由！");
        }
        if (StringUtils.isNotBlank(reason) && StringUtils.trim(reason).length() > 200) {
            throw new ServiceException("reason_out_of_lengh", "警告理由超出长度，请输入200字符以内的理由！");
        }
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        gonghuiService.ghWarnHost(hostUuid, user.getUserName(), user.getAccountUuid(), StringUtils.trim(reason));
    }

    @Override
    public Map<String, Object> getPunishList(PunishSearch search, Integer page, Integer pageSize) {
        initPunishSearch(search);
        Map<String, Object> souceMap = gonghuiService.getPunishLog(page, pageSize, search.getStartTime(), search.getEndTime(), search.getLiveNo(), search.getTeamIdList(), search.getAgenterUuid(), search.getSource(), search.getPunishStatus());
        List<Map<String, String>> punishLogList = (List<Map<String, String>>) souceMap.get("list");
        if (MapUtils.isNotEmpty(souceMap) && CollectionUtils.isNotEmpty(punishLogList)) {
            for (Map<String, String> map : punishLogList) {
                Long teamId = MapUtils.getLong(map, "consortia_id");
                Team team = teamMapper.selectByPrimaryKey(teamId);
                if (null != team) {
                    map.put("team_name", team.getTeamName());
                } else {
                    logger.warn("获取处罚列表中，团队id：{} 在公会中不存在", teamId);
                    map.put("team_name", "");
                }
                String businessmanUuid = MapUtils.getString(map, "businessman_uuid");
                if (StringUtils.isNotBlank(businessmanUuid)) {
                    SysUser sysUser = sysUserService.selectUserByAccountUuid(businessmanUuid);
                    if (null != sysUser) {
                        map.put("businessman_name", sysUser.getUserName());
                    } else {
                        map.put("businessman_name", "");
                    }
                } else {
                    String hostUuid = MapUtils.getString(map, "host_uuid");
                    TeamHost teamHost = teamHostMapper.getOneByHostUuid(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
                    if (null != teamHost) {
                        if (teamHost.getEmployeeId() != null) {
                            TeamEmployee teamEmployee = teamEmployeeService.getById(teamHost.getEmployeeId());
                            if (null == teamEmployee) {
                                map.put("businessman_name", "");
                            } else {
                                map.put("businessman_name", teamEmployee.getEmployeeName());
                            }
                        } else {
                            map.put("businessman_name", "");
                            logger.info("获取处罚列表中，主播uuid：{},EmployeeId为空，没有关联经纪人 ", hostUuid);
                        }
                    } else {
                        logger.warn("获取处罚列表中，主播uuid：{} 在公会中不存在", hostUuid);
                        map.put("businessman_name", "");
                    }
                }
            }
        }
        return souceMap;
    }

    @Override
    public Map<String, Object> getWarnList(WarnSearch search, Integer page, Integer pageSize) {
        initWarnSearch(search);
        Map<String, Object> map = gonghuiService.getWarnLog(page, pageSize, search.getStartTime(), search.getEndTime(), search.getLiveNo(), search.getHostName(), search.getTeamIdList(), search.getAgenterUuid());
        return map;
    }

    private PunishSearch initPunishSearch(PunishSearch search) {
        logger.info("initPunishSearch-search-{}", JsonUtils.objectToString(search));
        if (search == null) {
            throw new ServiceException("search_null", "重新刷新页面");
        }
        if (StringUtils.isNotBlank(search.getLiveNo())) {
            search.setLiveNo(search.getLiveNo());
        }
        if (StringUtils.isNotBlank(search.getAgenterUuid())) {
            search.setAgenterUuid(search.getAgenterUuid());
        }
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        RoleVo role = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        if (UserTypeEnum.AGENTER.getCode().equals(role.getRoleKey())) {
            List<Team> teamList = teamMapper.getTeamTreeByRole(user.getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), user.getOrgId());
            search.setAgenterUuid(user.getAccountUuid());
            search.setTeamIdList(teamList.stream().map(Team::getTeamId).collect(Collectors.toList()));
        }
        if (UserTypeEnum.LEADER.getCode().equals(role.getRoleKey())) {
            List<Team> teamList = teamMapper.getTeamTreeByRole(user.getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), user.getOrgId());
            search.setTeamIdList(teamList.stream().map(Team::getTeamId).collect(Collectors.toList()));
        }
        if (UserTypeEnum.MANAGER.getCode().equals(role.getRoleKey())) {
            if (user.getOrgId() != null) {
                List<Long> teams = teamMapper.selectTeamListIds(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
                if (CollectionUtils.isEmpty(teams)) {
                    logger.warn("处罚列表搜索中 当前用户管理员的团队不存在，详细信息{}", JsonUtils.objectToString(role));
                    throw new ServiceException("search_error", "查询异常，请稍后再试~");
                }
                search.setTeamIdList(teams);
                if (null != search.getTeamId() && teams.contains(search.getTeamId())) {
                    search.setTeamIdList(Arrays.asList(search.getTeamId()));
                    return search;
                }
            } else {
                logger.warn("处罚列表搜索中 当前用户管理员的机构id不存在！详细信息：{}", JsonUtils.objectToString(role));
                throw new ServiceException("search_error", "查询异常，请稍后再试~");
            }

        }
        return search;
    }

    private WarnSearch initWarnSearch(WarnSearch search) {
        if (search == null) {
            throw new ServiceException("search_null", "重新刷新页面");
        }
        if (StringUtils.isNotBlank(search.getLiveNo())) {
            search.setLiveNo(search.getLiveNo());
        }
        if (StringUtils.isNotBlank(search.getHostName())) {
            search.setHostName(search.getHostName());
        }
        if (StringUtils.isNotBlank(search.getAgenterUuid())) {
            search.setAgenterUuid(search.getAgenterUuid());
        }
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        RoleVo role = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        if (UserTypeEnum.AGENTER.getCode().equals(role.getRoleKey())) {
            List<Team> teamList = teamMapper.getTeamTreeByRole(user.getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), user.getOrgId());
            search.setAgenterUuid(user.getAccountUuid());
            search.setTeamIdList(teamList.stream().map(Team::getTeamId).collect(Collectors.toList()));
        }
        if (UserTypeEnum.LEADER.getCode().equals(role.getRoleKey())) {
            List<Team> teamList = teamMapper.getTeamTreeByRole(user.getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), user.getOrgId());
            search.setTeamIdList(teamList.stream().map(Team::getTeamId).collect(Collectors.toList()));
        }
        if (UserTypeEnum.MANAGER.getCode().equals(role.getRoleKey())) {
            if (user.getOrgId() != null) {
                List<Long> teams = teamMapper.selectTeamListIds(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
                if (CollectionUtils.isEmpty(teams)) {
                    logger.warn("警告列表搜索中 当前用户管理员的团队不存在，详细信息{}", JsonUtils.objectToString(role));
                    throw new ServiceException("search_error", "查询异常，请稍后再试~");
                }
                search.setTeamIdList(teams);
                if (null != search.getTeamId() && teams.contains(search.getTeamId())) {
                    search.setTeamIdList(Arrays.asList(search.getTeamId()));
                    return search;
                }
            } else {
                logger.warn("警告列表搜索中 当前用户管理员的机构id不存在！详细信息：{}", JsonUtils.objectToString(role));
                throw new ServiceException("search_error", "查询异常，请稍后再试~");
            }
        }
        return search;
    }


    @Override
    public Map<String, Object> getWarnReasonList() {
        return gonghuiService.getPunishReasonList();
    }

    @Override
    public TeamHost getHostByUuidAndType(String uuid, Integer type) {
        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("host_uuid", uuid);
        queryWrapper.eq("team_type", type);
        return this.getOne(queryWrapper);
    }

    ;

    public List<TeamHost> getBatchHostByUuidsAndType(List<String> uuids, Integer type) {
        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("team_type", type);
        queryWrapper.in("host_uuid", uuids);
        return this.list(queryWrapper);
    }

    public void saveTeamHost(TeamHost teamHost) {
        teamHostMapper.insertHost(teamHost);
    }

    public List<TeamHost> getBatchHostType(Integer type) {

        //id,host_uuid,org_id,team_id,team_type,employee_id,invite_time,real_name,real_name_cipher,
        // status,create_time,update_time,change_time,current_sharing_profit_rate,new_sharing_profit_rate,
        // is_update,is_group,host_type
        // 不查real_name字段，防止set到ps的时候调用解密耗时
        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "host_uuid", "org_id", "team_id", "team_type", "employee_id", "invite_time",
                "status", "create_time", "update_time", "change_time", "current_sharing_profit_rate", "new_sharing_profit_rate",
                "is_update", "is_group", "host_type");
        queryWrapper.eq("team_type", type);
        return this.list(queryWrapper);
    }

    /**
     * 校验直播机构是否与聊天室机构同一个（忽略直播机构和聊天室机构）
     *
     * @param liveOrgId 直播机构id
     * @param hostUuid  艺人uuid
     */
    private void checkLiveOrgSameWithChat(Long liveOrgId, String hostUuid) {
        // 判断用户是否有直播业务，如果有则需要判断是否同一个机构
        TeamHost chatHost = this.getHostByUuidAndType(hostUuid, TeamTypeEnum.TALK_TEAM.getValue());
        if (chatHost != null) {
            Long chatOrgId = chatHost.getOrgId();
            if (!Objects.equals(liveOrgId, chatOrgId)) {
                Team liveTeam = SpringUtils.getBean(TeamService.class).detail(chatHost.getTeamId());
                String str = liveTeam != null ? "「" + liveTeam.getTeamName() + "」中" : "聊天室业务中";
                throw new ServiceException("exist_chat_room_team", "无法加入该公会，因为用户已经存在 " + str);
            }
        }
    }

    @Override
    public Map<String, TeamHostVo> getHostOrgInfo(List<String> uuids) {
        if (CollectionUtils.isEmpty(uuids)) {
            return new HashMap<>();
        }
        List<TeamHostVo> hostList = teamHostMapper.teamNameByHostUuidList(uuids);
        return hostList.stream().collect(Collectors.toMap(TeamHostVo::getHostUuid, v -> v, (m1, m2) -> m2));
    }

    @Override
    public AppValidVO quitGuildAuth(String hostUuid) {
        // 判断是否是中转公会或素人 是则隐藏按钮
        TeamHost liveHost = getHostByUuidAndType(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
        if (Objects.nonNull(liveHost)) {
            Boolean inTaquOrPersonalTeam = SpringUtils.getBean(HostService.class).isInTaquOrPersonalTeam(liveHost.getTeamId());
            if (inTaquOrPersonalTeam) {
                log.info("[quitGuildAuth],inTaquOrPersonalTeam{},hostUuid{}", inTaquOrPersonalTeam, hostUuid);
                return buildAppValidVO(1, "当前属于中转公会或素人，不可操作", null);
            }
        } else {
            return buildAppValidVO(1, "当前主播无公会，不可操作", null);
        }

        ApprovalFlow record = new ApprovalFlow();
        record.setHostUuid(hostUuid);
        ApprovalFlow approvalFlow = approvalFlowMapper.selectOneByCondition(record);
        if (Objects.nonNull(approvalFlow)) {
            // 判断是否审核中状态
            if (approvalFlow.getFlowStatus().equals(ApprovalStatusEnum.WAIT_APPROVAL.getCode())) {
                return buildAppValidVO(1, "退会申请审批中，请耐心等待", null);
            }

            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.DAY_OF_MONTH, -7);
            Date before7days = calendar.getTime();
            // 拒绝状态 & 是否7天内
            if (approvalFlow.getFlowStatus().equals(ApprovalStatusEnum.REJECT.getCode())
                    && approvalFlow.getModifyTime().getTime() > before7days.getTime()) {
                return buildAppValidVO(1, "未满足申请要求，退会申请被拒后七个工作日内无法再提交申请", null);
            }
        }

        // 获取主播信息
        HostVo.DataVo hostData = getHostDataByUuid(hostUuid);
        if (Objects.isNull(hostData)) {
            return buildAppValidVO(1, "查无主播信息", null);
        }

        // 条件1 直播权限被关闭
        int forbidStatus = 3;
        if (Integer.valueOf(hostData.getLive_status()).equals(forbidStatus)) {
            return buildAppValidVO(1, "直播权限关闭时，无法发起退会申请", null);
        }

        long lastTimestamp = Long.parseLong(hostData.getLast_online_time()) * 1000;
        long currentTimestamp = System.currentTimeMillis();
        // 停播时长
        long stopDay = (currentTimestamp - lastTimestamp) / ONE_DAY;
        // 主播流水
        long income = Long.parseLong(hostData.getReceive_gift_value()) / 100;
        log.info("[quitGuildAuth]day,{},income,{}", stopDay, income);

        boolean flagA = income < MONEY_10_W && stopDay > DAY_90;
        boolean flagB = MONEY_10_W <= income && income < MONEY_50_W && stopDay > DAY_180;
        boolean flagC = income >= MONEY_50_W && stopDay > DAY_365;
        // 条件2
        if (flagA || flagB || flagC) {
            return buildAppValidVO(2
                    , "退会成功后您本月所产生的直播收益分成比例与个人主播相同，分成比例请查看奖励说明，确定要退会吗？"
                    , "m=web&a=url&ul=https://web.whtaqu.cn/html/h5_page/1/1/202109281152196022.html");
        } else {
            // 条件3
            return buildAppValidVO(3
                    , "退会申请被拒后七个工作日内无法再提交申请，退会成功后您本月所产生的直播收益分成比例与个人主播相同，分成比例请查看奖励说明。"
                    , "m=web&a=url&ul=https://web.whtaqu.cn/html/h5_page/1/1/202109281152196022.html");
        }
    }

    /**
     * 构建vo
     *
     * @param type
     * @param content
     * @param contentRelation
     * @return
     */
    private AppValidVO buildAppValidVO(Integer type, String content, String contentRelation) {
        AppValidVO appValidVO = new AppValidVO();
        appValidVO.setType(type);
        appValidVO.setContent(content);
        appValidVO.setContentRelation(contentRelation);

        return appValidVO;
    }

    /**
     * 根据主播uuid获取主播信息
     *
     * @param hostUuid
     * @return
     */
    private HostVo.DataVo getHostDataByUuid(String hostUuid) {
        int page = 1;
        int pageSize = 1;
        HostVo hostVos = gonghuiService.getHostList(null, null, Collections.singletonList(hostUuid), page, pageSize
                , null, null, null, null, null, null, null
                , null, null, null, null, null, null);

        return CollectionUtils.isNotEmpty(hostVos.getList()) ? hostVos.getList().get(0) : null;
    }

    @Override
    public AppQuitGuildBtnVO getQuitGuildInfo(String hostUuid) {
        AppQuitGuildBtnVO btnVO = new AppQuitGuildBtnVO();
        // 默认显示按钮
        btnVO.setShowQuitGuildButton(Constants.YES_1);
        btnVO.setQuitGuildStatus(0);

        ApprovalFlow condition = new ApprovalFlow();
        condition.setHostUuid(hostUuid);
        condition.setFlowStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        condition.setFlowType(FlowTypeEnum.QUIT_GUILD.getCode());
        ApprovalFlow approvalFlow = approvalFlowMapper.selectOneByCondition(condition);
        if (Objects.nonNull(approvalFlow)) {
            int reviewStatusCode = 100;
            btnVO.setQuitGuildStatus(reviewStatusCode);
            btnVO.setQuitGuildStatusName(ApprovalStatusEnum.WAIT_APPROVAL.getName());
            btnVO.setShowQuitGuildButton(Constants.YES_1);
        }

        // 判断是否是中转公会或素人 是则隐藏按钮 copyby HostServiceImpl.isInTaquOrPersonalTeam
        TeamHost liveHost = getHostByUuidAndType(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
        if (Objects.nonNull(liveHost)) {
            Boolean inTaquOrPersonalTeam = SpringUtils.getBean(HostService.class).isInTaquOrPersonalTeam(liveHost.getTeamId());
            if (inTaquOrPersonalTeam) {
                btnVO.setShowQuitGuildButton(0);
            }
        } else {
            btnVO.setShowQuitGuildButton(0);
        }

        return btnVO;
    }

    @Override
    public void applyQuitGuild(String hostUuid, Integer type, String reason) {
        log.info("[applyQuitGuild],hostUuid:{},type:{},reason:{}", hostUuid, type, reason);

        // 判断类型 如果是条件2 则直接中转转会
        int condition2 = 2;
        if (type.equals(condition2)) {
            transferGuild(hostUuid);
        } else {
            CommonHostInfo commonHostInfo = singleHostInfo(hostUuid);
            QuitGuildFlowVO quitGuildFlowVO = new QuitGuildFlowVO();
            quitGuildFlowVO.setCommonHostInfo(commonHostInfo);
            quitGuildFlowVO.setApplyReason(reason);
            quitGuildFlow.create(quitGuildFlowVO);
        }
    }

    @Override
    public CommonHostInfo singleHostInfo(String hostUuid) {
        CommonHostInfo commonHostInfo = new CommonHostInfo();
        commonHostInfo.setHostUuid(hostUuid);
        String[] accountUuids = {hostUuid};
        String[] fields = {"account_name", "avatar", "account_level_id", "vip_level_avatar", "account_card_level", "account_level", "vip_level"};

        // 查询主播信息
        LiveHostInfo liveHostInfo = liveHostInfoMapper.selectByHostUuid(hostUuid);
        if (Objects.isNull(liveHostInfo)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无主播信息");
        }
        Map<String, Map<String, Object>> res = infoService.getInfoByUuid(accountUuids, fields, null, true, true);
        if (Objects.isNull(res)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无用户信息");
        }
        Map<String, Object> account = res.get(hostUuid);
        if (Objects.isNull(account)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无用户account信息");
        }
        commonHostInfo.setNickName(Optional.ofNullable(account.get("account_name")).orElse(StringUtils.EMPTY).toString());
        commonHostInfo.setAvatar(Optional.ofNullable(account.get("avatar")).orElse(StringUtils.EMPTY).toString());
        commonHostInfo.setApplyLevel(liveHostInfo.getApplyLevel());
        commonHostInfo.setLiveNo(liveHostInfo.getLiveNo().toString());

        // 查询host机构信息
        Map<String, TeamHostVo> map = getHostOrgInfo(Collections.singletonList(hostUuid));
        if (Objects.isNull(map) || map.isEmpty()) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无用户机构信息");
        }
        TeamHostVo teamHostVo = map.get(hostUuid);
        if (Objects.isNull(teamHostVo)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无用户团队信息");
        }
        if (Objects.isNull(teamHostVo.getOrgId())) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无用户机构id");
        }
        commonHostInfo.setTeamId(Optional.ofNullable(teamHostVo.getTeamId()).orElse(0L));
        commonHostInfo.setOrgId(Optional.ofNullable(teamHostVo.getOrgId()).orElse(0L));

        // 查询团队负责人
        if (!commonHostInfo.getTeamId().equals(0L)) {
            TeamOrgInfoVO teamOrgInfoVO = teamMapper.selectOneTeamLeader(commonHostInfo.getTeamId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (Objects.nonNull(teamOrgInfoVO) && Objects.nonNull(teamOrgInfoVO.getAccountUuid())) {
                commonHostInfo.setTeamAccountUuid(teamOrgInfoVO.getAccountUuid());
                commonHostInfo.setTeamMobile(teamOrgInfoVO.getMobile());
            }
        }

        // 查询机构负责人
        Organization organization = organizationMapper.selectByPrimaryKey(teamHostVo.getOrgId());
        if (Objects.isNull(organization)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无用户机构信息");
        }
        // 由于判断机构负责人不能用uuid 所以注释
        //if (Objects.isNull(organization.getAccountUuid())) {
        //throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无用户机构负责人uuid");
        //}
        commonHostInfo.setOrgAccountUuid(Optional.ofNullable(organization.getAccountUuid()).orElse("system"));
        commonHostInfo.setOrgName(organization.getOrgName());
        commonHostInfo.setOrgMobile(organization.getChargePersonPhone());
        log.info("[singleHostInfo],commonHostInfo:{}", Base64.getEncoder().encodeToString(commonHostInfo.toString().getBytes(StandardCharsets.UTF_8)));

        return commonHostInfo;
    }

    @Override
    public PageInfo<ApprovalFlowItem> getQuitGuildPageList(ApprovalFlow approvalFlow, PageRequest pageRequest) {
        // 分页
        PageHelper.startPage(pageRequest.getPage(), pageRequest.getPageSize());
        List<ApprovalFlow> itemList = approvalFlowMapper.selectByCondition(approvalFlow);
        List<ApprovalFlowItem> flowList = new ArrayList<>();
        for (ApprovalFlow item : itemList) {
            ApprovalFlowItem approvalFlowItem = new ApprovalFlowItem();
            approvalFlowItem.setFlowId(item.getId());
            approvalFlowItem.setFlowStatus(item.getFlowStatus());
            approvalFlowItem.setFlowStatusColor(item.getFlowStatus());
            approvalFlowItem.setFlowStatusName(ApprovalStatusEnum.getName(item.getFlowStatus())
                    + (item.getFlowStatus().equals(ApprovalStatusEnum.REJECT.getCode()) ? "(" + item.getRejectReason() + ")" : ""));
            approvalFlowItem.setHostUuid(item.getHostUuid());
            approvalFlowItem.setOrgId(item.getOrgId());
            approvalFlowItem.setOrgName(item.getOrgName());
            approvalFlowItem.setApplyReason(item.getApplyReason());
            approvalFlowItem.setApplyLevel(item.getApplyLevel());
            approvalFlowItem.setAvatar(item.getAvatar());
            approvalFlowItem.setNickName(item.getNickName());
            approvalFlowItem.setApplyTime(DateFormatUtils.format(item.getApplyTime(), DATE_FORMAT));
            approvalFlowItem.setLiveNo(item.getLiveNo());

            flowList.add(approvalFlowItem);
        }

        return new PageInfo<>(flowList);
    }

    @Override
    public List<ApprovalFlowLogItem> getQuitGuildLog(Integer flowId) {
        List<ApprovalFlowLog> itemList = approvalFlowLogMapper.selectByFlowId(flowId);
        List<ApprovalFlowLogItem> logList = new ArrayList<>();
        for (ApprovalFlowLog item : itemList) {
            ApprovalFlowLogItem log = new ApprovalFlowLogItem();
            log.setResult(item.getFlowStatus().equals(ApprovalStatusEnum.REJECT.getCode()) ? 2 : 1);
            log.setRemark(item.getRemark());
            log.setUserTimeInfo(item.getUserTimeInfo());

            logList.add(log);
        }

        return logList;
    }

    /**
     * 迁移自changeBusinessType 中转公会 changeLivingTeamToTaquBeforClose
     *
     * @param hostUuid
     */

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void transferGuild(String hostUuid) {
        // 中转公会的团队id
        Integer teamIdTemp = hostService.getTaquConsortiaId();
        //根据对应团队查找对应的机构
        Team team = teamMapper.selectById(teamIdTemp);
        if (Objects.isNull(team)) {
            throw new ServiceException("param_error", "查询不到中转机构信息。");
        }
        Long nowTime = System.currentTimeMillis() / 1000;
        TeamHost teamHost = teamHostMapper.selectHostOneByUuid(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
        if (Objects.nonNull(teamHost)) {
            int backstageOperateType = BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS.getValue();
            log.info("中转机构名称：{}，主播：{}", teamIdTemp, hostUuid);
            operateLog(teamHost.getHostUuid(), teamHost.getTeamId(), Long.valueOf(teamIdTemp));
            String itemInfo = String.format("直播主播变更公会,将原机构公会下主播移动到中转公会,旧机构id=%s,旧团队id=%s,新团队id=%s", teamHost.getTeamId(), teamHost.getTeamId(), team.getOrgId(), teamIdTemp);
            backstageOperateLogService.addBackstageOperateLog(hostUuid, backstageOperateType, hostUuid, itemInfo);

            // 插入分润记录为0 自动生效
            HostSharingProfitRecord profitRecord = new HostSharingProfitRecord();
            profitRecord.setHostUuid(teamHost.getHostUuid());
            profitRecord.setAccountUuid("admin");
            profitRecord.setType(2);
            profitRecord.setOrgId(teamHost.getOrgId());
            profitRecord.setTeamId(teamHost.getTeamId());
            profitRecord.setStatus(HostSharingProfitStatusEnum.EFFECTIVE.getValue());
            profitRecord.setCurrentSharingProfitRate(teamHost.getCurrentSharingProfitRate());
            profitRecord.setNewSharingProfitRate(Constants.MID_PROFIT);
            profitRecord.setChangeTime(nowTime);
            profitRecord.setEffectiveTime(0L);
            profitRecord.setCreateTime(nowTime);
            profitRecord.setUpdateTime(nowTime);
            sharingProfitRecordMapper.insertSelective(profitRecord);

            // 插入日志
            ModifyRecordInfoDTO modifyRecordInfo = new ModifyRecordInfoDTO();
            modifyRecordInfo.setOldOrgId(teamHost.getOrgId());
            modifyRecordInfo.setOldTeamId(teamHost.getTeamId());
            Organization organization = organizationMapper.selectByPrimaryKey(teamHost.getOrgId());
            modifyRecordInfo.setOldOrgName(organization.getOrgName());
            modifyRecordInfo.setNewOrgId(team.getOrgId());
            modifyRecordInfo.setNewTeamId(Long.valueOf(teamIdTemp));
            hostModifyRecordService.addRecord(teamHost.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), UUID.genUuid(), HostOperateTypeEnum.QUIT_GUILD_MOVE, modifyRecordInfo, "", Constants.YES_1, "", "");

            teamHost.setOrgId(team.getOrgId());
            teamHost.setEmployeeId(null);
            teamHost.setTeamId(Long.valueOf(teamIdTemp));
            teamHost.setStatus(EmployeeStatusEnum.DEPAETURE.getValue());
            teamHost.setCurrentSharingProfitRate(Constants.MID_PROFIT);
            teamHostMapper.updateByPrimaryKey(teamHost);
            hostService.changeHostConsortia(String.valueOf(teamIdTemp), Collections.singletonList(hostUuid));
            String info = String.format("直播主播变更公会,将原机构公会下主播移动到中转公会,旧机构id=%s,完成", teamHost.getTeamId());
            backstageOperateLogService.addBackstageOperateLog(hostUuid, backstageOperateType, hostUuid, info);
        }
    }

    @Override
    public void chatRoomInvite(ChatRoomInviteVO chatRoomInviteVO, Boolean reFlag) {
        Map<String, String> cardRes = infoService.getInfoByNormalCard(chatRoomInviteVO.getLiveNo());
        String hostUuid = cardRes.get("account_uuid");
        chatRoomInviteVO.setHostUuid(hostUuid);

        log.info("chatRoomInviteVO,{}", chatRoomInviteVO);
        if (!reFlag) {
            Map<String, Object> certMap = certificationService.getInfoByUuid(hostUuid);
            if (MapUtils.isEmpty(certMap)) {
                throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "请主播先登录APP实名认证");
            }
            // 判断是否有实名
            String realName = certMap.get("real_name").toString();
            if (realName.equals(StringUtils.EMPTY)) {
                throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "聊天室实名认证还未通过");
            }
            if (!realName.equals(chatRoomInviteVO.getTrueName())) {
                throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "实名认证真实姓名不一致");
            }
        }

        // 查询是否存在待审核订单
        checkInviteStatus(hostUuid);

        String[] accountUuids = {hostUuid};
        String[] fields = {"account_name", "avatar", "mobile", "create_time", "active_time"};
        Map<String, Map<String, Object>> res = infoService.getInfoByUuidsNoSecret(accountUuids, fields, null, true, true);
        log.info("infores,invite,res{}", res);
        if (Objects.isNull(res)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无用户信息");
        }
        Map<String, Object> account = res.get(hostUuid);
        if (Objects.isNull(account)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无用户account信息");
        }
        // 判断入参手机号是否一致
        if (!account.get("mobile").equals(chatRoomInviteVO.getMobile()) && !reFlag) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "输入手机号与系统不一致 ");
        }
        if (StringUtils.isEmpty(account.get("active_time").toString())) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无用户最近活跃时间");
        }

        // 判断注册时间是否大于 7天 & 最近活跃时间超过3个月
        long now = System.currentTimeMillis();
        long createTime = Long.parseLong(account.get("create_time").toString()) * 1000;
        long activeTime = Long.parseLong(account.get("active_time").toString()) * 1000;
        int diffDays = (int) ((now - createTime) / (60 * 60 * 24 * 1000));
        int activeDiffDays = (int) ((now - activeTime) / (60 * 60 * 24 * 1000));
        if (diffDays >= CREATE_MAX_DAY && activeDiffDays <= THREE_MONTH_DAY) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "最近一次登录app时间n≤3个月 且 艺人注册时间t≥7天,不可邀约入会");
        }

        // 接入风控判断逻辑
        linkAccountRes(chatRoomInviteVO.getHostUuid());

        // 操作人
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        chatRoomInviteVO.setOrgId(loginUser.getUser().getOrgId());
        chatRoomInviteVO.setOrgName(loginUser.getUser().getOrgName());
        chatRoomInviteVO.setNickName(Optional.ofNullable(account.get("account_name")).orElse(StringUtils.EMPTY).toString());
        chatRoomInviteVO.setLoginUser(loginUser);
        chatRoomInviteVO.setAvatar(Optional.ofNullable(account.get("avatar")).orElse(StringUtils.EMPTY).toString());
        chatRoomInviteVO.setTeamId(chatRoomInviteVO.getTeamId());
        chatRoomInviteVO.setApplyLevel("");

        chatRoomInviteFlow.create(chatRoomInviteVO);
    }


    @Override
    public void reInvite(ChatRoomInviteVO chatRoomInviteVO) {
        ApprovalFlow approvalFlow = approvalFlowMapper.selectOneById(chatRoomInviteVO.getId());
        if (Objects.isNull(approvalFlow)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无入会邀请单");
        }
        chatRoomInviteVO.setHostUuid(approvalFlow.getHostUuid());
        chatRoomInviteVO.setLiveNo(approvalFlow.getLiveNo());

        // 判断是不是已过期 或 已拒绝
        if (approvalFlow.getFlowStatus().equals(InviteStatusEnum.WAIT_CONFIRM.getCode())
                || approvalFlow.getFlowStatus().equals(InviteStatusEnum.AGREE.getCode())) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), InviteStatusEnum.getName(approvalFlow.getFlowStatus()) + "状态,无法重新邀约");
        }

        List<ApprovalFlowNode> nodeList = approvalFlowNodeMapper.selectByFlowId(approvalFlow.getId());
        chatRoomInviteVO.setMobile(nodeList.get(0).getMobile());
        chatRoomInviteVO.setRemark("重新邀约");
        chatRoomInviteVO.setTeamId(approvalFlow.getTeamId());

        // 发起邀约
        chatRoomInvite(chatRoomInviteVO, true);
    }

    @Override
    public void setHostIsGroup(String hostUuid, Integer isGroup) {
        // 判断是否是直播主播
        TeamHost teamHost = teamHostMapper.selectHostOneByUuid(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
        if (Objects.isNull(teamHost)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无主播数据");
        }
        teamHost.setIsGroup(isGroup);
        teamHostMapper.updateByPrimaryKey(teamHost);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchTransferGuild(ResetHostReq resetHostReq) {
        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("team_type", TeamTypeEnum.LIVE_TEAM.getValue());
        //queryWrapper.ne("team_id", getEnvTeamId(Constants.ONE));
        if (resetHostReq.getType().equals(ResetTypeEnum.LIVE_SOURCE.getCode())) {
            LiveGrayUuidRes grayUuidRes = liveSoaService.getSettltmentTestUuidInfo();
            log.info("batchTransferGuild,grayUuidRes:{}", grayUuidRes);
            if (Objects.nonNull(grayUuidRes) && CollectionUtils.isEmpty(grayUuidRes.getPersonal_consortia_test_uuids())) {
                throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "数据为空，无法操作中转");
            }
            if (CollectionUtils.isNotEmpty(grayUuidRes.getPersonal_consortia_test_uuids())) {
                queryWrapper.in("host_uuid", grayUuidRes.getPersonal_consortia_test_uuids());
            }
        }
        if (resetHostReq.getType().equals(ResetTypeEnum.TEAM.getCode())) {
            queryWrapper.eq("team_id", resetHostReq.getTeamId());
        }
        if (resetHostReq.getType().equals(ResetTypeEnum.ORG.getCode())) {
            queryWrapper.eq("org_id", resetHostReq.getOrgId());
        }
        List<TeamHost> teamHostList = teamHostMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(teamHostList)) {
            log.info("查不到主播信息:{}", teamHostList);
        }
        for (TeamHost teamHost : teamHostList) {
            String hostUuid = teamHost.getHostUuid();
            try {
                transferGuild(hostUuid);
            } catch (Exception e) {
                log.error("中转失败,error", e.getMessage());
                continue;
            }
        }
        return teamHostList.stream().map(TeamHost::getHostUuid).collect(Collectors.toList());
    }

    /**
     * 1素人团队id 2中转团队id
     *
     * @param type
     * @return
     */
    @Override
    public Long getEnvTeamId(Integer type) {
        String localEnv = LocalConfUtil.getLocalEnv();
        String online = "online";
        String gray = "gray";
        String test = "test";
        if (localEnv.startsWith(online) || localEnv.startsWith(gray)) {
            return type.equals(Constants.ONE) ? normalTeamId.get(online) : midTeamId.get(online);
        }

        return type.equals(Constants.TWO) ? normalTeamId.get(test) : midTeamId.get(test);
    }

    /**
     * 判断风控关联用户逻辑
     *
     * @param hostUuid
     * @return
     */
    private void linkAccountRes(String hostUuid) {
        LinkAccountRes res = punishTicketSoaService.linkAccountFromToken(hostUuid);
        if (Objects.isNull(res)) {
            return;
        }
        int limit = 10;
//        int limit = 9999;
        if (res.getLintCount() > limit) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "关联账号超过10个，该账号存在风险，不可被邀约");
        }
        if (Objects.nonNull(res.getStatusList())) {
            AtomicInteger num = new AtomicInteger();
            res.getStatusList().forEach((k, v) -> {
                if (v.equals(1)) {
                    num.getAndIncrement();
                }
            });
            int maxNum = 4;
//            int maxNum = 9999;
            if (num.get() > maxNum) {
                throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "封禁账号达到n>4个，该账号存在风险，不可被邀约");
            }
        }
    }

    @Override
    public InviteHostDetailRes getChatRoomInviteDetail(InviteHostReq inviteHostReq) {
        ApprovalFlow approvalFlow = approvalFlowMapper.selectOneById(inviteHostReq.getInviteId());
        if (Objects.isNull(approvalFlow)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无入会邀请单");
        }
        // 权限校验
        if (!approvalFlow.getHostUuid().equals(inviteHostReq.getHostUuid())) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "邀请主播与当前用户不一致，无法查看");
        }

        InviteHostDetailRes inviteHostDetailRes = new InviteHostDetailRes();
        inviteHostDetailRes.setStatus(approvalFlow.getFlowStatus());
        inviteHostDetailRes.setGuildName(approvalFlow.getOrgName());
        inviteHostDetailRes.setStatusName(InviteStatusEnum.getName(approvalFlow.getFlowStatus()));
        inviteHostDetailRes.setSendDate(DateFormatUtils.format(approvalFlow.getCreateTime(), DATE_FORMAT));

        Calendar ca = Calendar.getInstance();
        ca.setTime(approvalFlow.getCreateTime());
        ca.add(Calendar.DATE, 3);
        String expireDate = DateFormatUtils.format(ca.getTime(), DATE_FORMAT);
        inviteHostDetailRes.setExpireDate(expireDate);

        return inviteHostDetailRes;
    }

    @Override
    public void chatRoomInviteReview(InviteHostReq inviteHostReq) {
        ApprovalFlow approvalFlow = approvalFlowMapper.selectOneById(inviteHostReq.getInviteId());
        if (Objects.isNull(approvalFlow)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "查无入会邀请单");
        }
        // 权限校验
        if (!approvalFlow.getHostUuid().equals(inviteHostReq.getHostUuid())) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "邀请主播与当前用户不一致，无法操作");
        }
        // 状态校验
        if (!approvalFlow.getFlowStatus().equals(InviteStatusEnum.WAIT_CONFIRM.getCode())) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "单据没有处于待确认状态，不能操作");
        }

        PassRejectVO passRejectVO = new PassRejectVO();
        passRejectVO.setFlowId(inviteHostReq.getInviteId());
        passRejectVO.setReviewUser(inviteHostReq.getHostUuid());
        log.info("chatRoomInviteReview,{}", passRejectVO);
        chatRoomInviteFlow.review(inviteHostReq.getStatus(), passRejectVO);
    }

    @Override
    public PageInfo<InviteHostItemRes> getInvitePageList(ApprovalFlow approvalFlow, PageRequest pageRequest) {
        // 分页
        PageHelper.startPage(pageRequest.getPage(), pageRequest.getPageSize());
        List<ApprovalFlow> itemList = approvalFlowMapper.selectByCondition(approvalFlow);
        PageInfo resPageInfo = new PageInfo(itemList);
        List<InviteHostItemRes> flowList = new ArrayList<>();
        for (ApprovalFlow item : itemList) {
            InviteHostItemRes inviteHostItemRes = new InviteHostItemRes();
            inviteHostItemRes.setId(item.getId());
            inviteHostItemRes.setHostUuid(item.getHostUuid());
            inviteHostItemRes.setInviteDate(DateFormatUtils.format(item.getCreateTime(), DATE_FORMAT));
            inviteHostItemRes.setAccountId(item.getLiveNo());
            inviteHostItemRes.setApplyLevel(item.getApplyLevel());
            inviteHostItemRes.setInviteUser(item.getCreateUser());
            inviteHostItemRes.setAvatar(item.getAvatar());
            inviteHostItemRes.setNickName(item.getNickName());
            inviteHostItemRes.setOrgName(item.getOrgName());
            inviteHostItemRes.setStatus(item.getFlowStatus());
            inviteHostItemRes.setStatusName(InviteStatusEnum.getName(item.getFlowStatus()));
            inviteHostItemRes.setRemark(item.getApplyReason());

            flowList.add(inviteHostItemRes);
        }
        resPageInfo.setList(flowList);

        return resPageInfo;
    }

    /**
     * 校验是否聊天室邀约 状态
     *
     * @param hostUuid
     */
    private void checkInviteStatus(String hostUuid) {
        // 查询是否存在待审核订单
        ApprovalFlow condition = new ApprovalFlow();
        condition.setFlowType(FlowTypeEnum.CHATROOM_INVITE.getCode());
        condition.setHostUuid(hostUuid);
        List<ApprovalFlow> existsList = approvalFlowMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(existsList)) {
            for (ApprovalFlow flow : existsList) {
                if (flow.getFlowStatus().equals(InviteStatusEnum.WAIT_CONFIRM.getCode())
                        || flow.getFlowStatus().equals(InviteStatusEnum.AGREE.getCode())) {
                    throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), InviteStatusEnum.getName(flow.getFlowStatus()) + "状态,无法发起邀约");
                }
            }
        }
    }

    /**
     * 迁移自changeBusinessType
     *
     * @param uuid
     * @param oldValue
     * @param newValue
     */
    private void operateLog(String uuid, Long oldValue, Long newValue) {
        Map<String, Long> agentMap = new HashMap<>();
        agentMap.put("oldValue", oldValue);
        agentMap.put("newValue", newValue);
        // 记录日志
        TeamHostOperateLog log = new TeamHostOperateLog();
        log.setHostUuid(uuid);
        log.setType(TeamHostOperateLogEnum.LIVE_CHANGE_TEAM.getValue());
        log.setCreateTime(DateUtil.currentTimeSeconds());
        log.setContent(JsonUtils.objectToString(agentMap));
        log.setOperator("");
        operateLogMapper.insert(log);
    }

    @Override
    public HostInfoDto getHostInfo(String hostUuid, Integer teamType) {
        HostInfoDto hostInfoDto = new HostInfoDto();
        TeamHost teamHost = teamHostMapper.selectHostOneByUuid(hostUuid, teamType);
        if (Objects.isNull(teamHost)) {
            return null;
        }

        hostInfoDto.setHostUuid(hostUuid);
        hostInfoDto.setTeamId(teamHost.getTeamId());
        hostInfoDto.setOrgId(teamHost.getOrgId());
        Long employeeId = teamHost.getEmployeeId();
        hostInfoDto.setAgentId(employeeId);
        if (Objects.nonNull(employeeId) && !employeeId.equals(0L)) {
            TeamEmployee teamEmployee = teamEmployeeMapper.selectById(employeeId);
            if (Objects.nonNull(teamEmployee)) {
                hostInfoDto.setAgent(teamEmployee.getEmployeeName());
            }
        }

        return hostInfoDto;
    }

    @Override
    public UserAuthVO getUserAuthUuids() {
        UserAuthVO authVO = new UserAuthVO();
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser.getUser().getOrgId() == null) {
            throw new ServiceException("param_error", "该用户没有绑定机构");
        }
        SysUser user = loginUser.getUser();
        RoleVo userRole = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());

        // 设置角色类型
        authVO.setUserType(user.getUserType());

        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("team_type", TeamTypeEnum.LIVE_TEAM.getValue());
        queryWrapper.eq("org_id", user.getOrgId());
        if (UserTypeEnum.AGENTER.getCode().equals(userRole.getRoleKey())) {
            TeamEmployee teamEmployee = teamEmployeeMapper.selectByUserId(user.getUserId());
            if (Objects.isNull(teamEmployee)) {
                throw new ServiceException("param_error", "查无团队成员信息");
            }
            queryWrapper.eq("employee_id", teamEmployee.getEmployeeId());
            authVO.setTeamIdList(Collections.singletonList(teamEmployee.getTeamId()));
        }
        if (UserTypeEnum.LEADER.getCode().equals(userRole.getRoleKey())) {
            List<Team> teamList = teamMapper.getTeamTreeByRole(user.getAccountUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), user.getOrgId());
            if (CollectionUtils.isEmpty(teamList)) {
                throw new ServiceException("param_error", "该用户查无团队id");
            }
            List<Long> teamIdList = teamList.stream().map(Team::getTeamId).collect(Collectors.toList());
            queryWrapper.in("team_id", teamIdList);
            authVO.setTeamIdList(teamIdList);
        }
        if (UserTypeEnum.MANAGER.getCode().equals(userRole.getRoleKey())) {
            queryWrapper.eq("org_id", user.getOrgId());
            List<Long> teamIdList = teamMapper.selectTeamListIds(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            authVO.setTeamIdList(teamIdList);
        }
        List<TeamHost> teamHostList = teamHostMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(teamHostList)) {
            throw new ServiceException("param_error", "该用户查无团队主播信息");
        }

        List<String> uuidList = teamHostList.stream().map(TeamHost::getHostUuid).collect(Collectors.toList());
        authVO.setUuidList(uuidList);
        authVO.setOrgId(user.getOrgId());

        return authVO;
    }

    /**
     * @param hostUuid
     * @remark 如果此处有异常 可能是由于没有同步主播公会-团队信息到直播 导致列表能看的老机构的数据 被操作了
     * Gonghui这个服务的数据其实是直播的
     */
    @Override
    public void checkUserChangeAuth(String hostUuid) {
        // 查询直播host信息
        TeamHost teamHost = teamHostMapper.selectHostOneByUuid(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
        // 操作用户
        SysUser sysUser = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();

        if (!sysUser.getOrgId().equals(teamHost.getOrgId())) {
            throw new ServiceException("param_error", "主播机构id" + teamHost.getOrgId() + ",操作用户机构id" + sysUser.getOrgId() +
                    ",所属机构id不一致，无权操作");
        }
    }

    @Override
    public List<HostOrgTeamDto> multiHostInfo(List<HostInfoReq> req) {
        List<HostOrgTeamDto> res = teamHostMapper.selectListByHostAndType(req);
        Long surenTeamId = EnvUtil.isOnlineOrGray() ? 110166L : 110016L;
        for (HostOrgTeamDto item : res) {
            Integer isPersonal = Objects.isNull(item.getTeamId()) ? 1 : (surenTeamId.equals(item.getTeamId()) ? 1 : 0);
            item.setIsPersonal(isPersonal);
        }
        return res;
    }

    @Override
    public Map<String, Long> mapEmployeeId(List<String> uuidList, int type) {
        if (CollectionUtils.isEmpty(uuidList)) {
            return new HashMap<>();
        }

        LambdaQueryWrapper<TeamHost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TeamHost::getEmployeeId, TeamHost::getHostUuid)
                .eq(TeamHost::getTeamType, type)
                .in(TeamHost::getHostUuid, uuidList);

        List<TeamHost> teamHostList = this.baseMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(teamHostList)) {
            return new HashMap<>();
        }
        Map<String, Long> map = teamHostList.stream().filter(l -> l.getEmployeeId() != null).collect(Collectors.toMap(l -> l.getHostUuid(), l -> l.getEmployeeId(), (key1, key2) -> key1));
        return map;
    }


//    public static void main(String[] args) {
//        List<TeamHost> teamHostList = new ArrayList<>();
////        TeamHost teamHost1 = new TeamHost();
////        teamHost1.setHostUuid("1111");
////        teamHost1.setEmployeeId(11111L);
////
////        teamHostList.add(teamHost1);
//        TeamHost teamHost2 = new TeamHost();
//        teamHost2.setHostUuid("2222");
//        teamHostList.add(teamHost2);
//        Map<String, Long> map = teamHostList.stream().collect(Collectors.toMap(l -> l.getHostUuid(), l -> l.getEmployeeId(), (key1, key2) -> key1));
//        System.out.println("集合为："+ JsonUtils.objectToString(map));
//    }

}
