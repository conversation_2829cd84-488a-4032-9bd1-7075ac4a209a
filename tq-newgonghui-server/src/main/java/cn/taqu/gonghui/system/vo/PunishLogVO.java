package cn.taqu.gonghui.system.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 罚单记录VO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@Data
public class PunishLogVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 应用：1他趣、2听鸭、3配配、888海外他趣
     */
    private Integer appCode;


    /**
     * 用户uuid
     */
    private String accountUuid;


    /**
     * 罚单id
     */
    private Long punishId;

    /**
     * 处罚办法/类型
     */
    private String punishType;

    private List<String> punishTypes;

    private String punishTypesStr;

    /**
     * 处罚小时
     */
    private Integer punishHours;


    /**
     * 处罚天数
     */
    private Integer punishDays;


    /**
     * 用户身份
     */
    private String userType;

    /**
     * 后台处罚理由
     */
    private String inReason;

    private String inReasonContent;

    /**
     * 对外处罚理由
     */
    private String outReason;

    private String outReasonContent;


    /**
     * 处罚小时
     */
    private Integer punishMinutes;


    /**
     * 处罚结束时间
     */
    private Long punishEndTime;

    /**
     * 处罚结束日期格式化
     */
    private String punishEndDate;


    /**
     * 备注
     */
    private String remark;


    /**
     * 依据：如图片地址、文字、私信对话ID
     */
    private String basis;

    /**
     * 依据类型：如图片、文字、私信
     */
    private String basisType;


    /**
     * 操作人
     */
    private String operator;


    /**
     * 状态：1处罚中、2处罚结束、3取消
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;


    private String punishDate;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 用户id
     */
    private String accountId;

    /**
     * 手机号
     */
    private String phoneNumber;


    /**
     * 撤销操作人
     */
    private String revokeOperator;


    /**
     * 场景编码
     **/
    private String sceneCode;

    /**
     * 场景值
     **/
    private String sceneCodeStr;

    /**
     * 用户昵称
     */
    private String accountName;

    /**
     * 用户头像
     */
    private String imageUrl;


    /**
     * 性别[0:未知,1:男,2:女]
     */
    private Integer sexType;

    /**
     * 国家
     */
    private String country;

    /**
     * 公会id
     */
    private String guildId;

    /**
     * 经纪人
     */
    private String agent;

    /**
     * 团队id
     */
    private String groupId;

    /**
     * 主播评级
     */
    private String applyLevel;

    /**
     * 系统截图
     */
    private String systemScreen;

    /**
     * 回放地址
     */
    private String playbackUrl;

    /**
     * 团队名称
     */
    private String groupName;

    /**
     * 文本依据
     */
    private String basisTxt;

}
