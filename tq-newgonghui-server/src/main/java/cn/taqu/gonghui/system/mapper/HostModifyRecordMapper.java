package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.HostModifyRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/10 11:27 上午
 **/
@Mapper
public interface HostModifyRecordMapper extends BaseMapper<HostModifyRecord> {

    List<HostModifyRecord> findAllByHostAndCreateTime(String hostUuid, Long createTime, Integer status, Integer teamType);

    Long countByHostUuidAndCreateTime(String hostUuid, Integer teamType, Integer status);

    List<String> findUuidByBatchId(String batchId, Integer status);

    /**
     * 查询单条
     * @param hostModifyRecord
     * @return
     */
    HostModifyRecord selectByOne(HostModifyRecord hostModifyRecord);

    /**
     * 批量写入
     * @param recordList
     */
    void batchInsert(List<HostModifyRecord> recordList);

}
