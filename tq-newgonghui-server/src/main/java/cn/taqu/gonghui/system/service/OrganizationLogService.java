package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.entity.FeishuAuditRelation;
import cn.taqu.gonghui.system.entity.Organization;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @date: 2022/9/19
 * @Description:
 */
public interface OrganizationLogService {
    public void addLog(Organization newOrganization, Organization oldOrganization,String operator);

    void getModifyInfo(Organization organization, FeishuAuditRelation relation);

    /**
     * 审核失败清理日志
     * @param organization
     * @param relation
     */
    void clearModifyLog(Organization organization, FeishuAuditRelation relation);
}
