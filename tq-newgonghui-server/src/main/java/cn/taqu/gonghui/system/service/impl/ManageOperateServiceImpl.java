package cn.taqu.gonghui.system.service.impl;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.dto.OperationPersonDto;
import cn.taqu.gonghui.system.entity.MyData;
import cn.taqu.gonghui.system.entity.OperationPerson;
import cn.taqu.gonghui.system.entity.Performance;
import cn.taqu.gonghui.system.entity.TargetInfo;
import cn.taqu.gonghui.system.mapper.MyDataMapper;
import cn.taqu.gonghui.system.mapper.OperationPersonMapper;
import cn.taqu.gonghui.system.mapper.PerformanceMapper;
import cn.taqu.gonghui.system.mapper.TargetInfoPersonMapper;
import cn.taqu.gonghui.system.service.ManageOperateService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ManageOperateServiceImpl implements ManageOperateService {

    @Autowired
    private OperationPersonMapper operationPersonMapper;
    @Autowired
    private TargetInfoPersonMapper targetInfoPersonMapper;
    @Autowired
    private PerformanceMapper performanceMapper;
    @Autowired
    private MyDataMapper myDataMapper;


    @Override
    public int saveOperationPerson(OperationPerson operationPerson) {
        return operationPersonMapper.insert(operationPerson);
    }


    @Override
    public List<OperationPerson> getOperationPersonDtoList(OperationPersonDto operationPersonDto) {
        PageHelper.startPage(operationPersonDto.getPageNo() == null ? 1 : operationPersonDto.getPageNo(),
                operationPersonDto.getPageSize() == null ? 15 : operationPersonDto.getPageSize());
        QueryWrapper<OperationPerson> queryWrapper = new QueryWrapper<OperationPerson>();
        if (!StringUtils.isEmpty(operationPersonDto.getLoginname())) {
            queryWrapper.eq("loginname", operationPersonDto.getLoginname());

        }
        if (!StringUtils.isEmpty(operationPersonDto.getName())) {
            queryWrapper.eq("name", operationPersonDto.getName());
        }
        queryWrapper.orderByDesc("create_time");

        List<OperationPerson> operationPeopleList = operationPersonMapper.selectList(queryWrapper);
        PageHelper.clearPage();
        return operationPeopleList;
    }

    @Override
    public OperationPerson getOperationPersonByName(String name) {
        QueryWrapper<OperationPerson> queryWrapper = new QueryWrapper<OperationPerson>();
        queryWrapper.eq("name", name);
        OperationPerson operationPerson = operationPersonMapper.selectOne(queryWrapper);
        return operationPerson;

    }

    @Override
    public int updateOperationPerson(OperationPerson operationPerson) {
        return operationPersonMapper.updateById(operationPerson);
    }

    @Override
    public int deleteOperationPerson(OperationPerson operationPerson) {
        return operationPersonMapper.deleteById(operationPerson.getId());
    }

    @Override
    public int saveTargetInfo(TargetInfo targetInfo) {
        return targetInfoPersonMapper.insert(targetInfo);
    }

    @Override
    public int updateTargetInfo(TargetInfo targetInfo) {
        return targetInfoPersonMapper.updateById(targetInfo);
    }

    @Override
    public TargetInfo getTargetInfo() {
        QueryWrapper<TargetInfo> queryWrapper = new QueryWrapper<TargetInfo>();
        queryWrapper.orderByDesc("create_time");
        queryWrapper.last("limit 1");//查询一条数据
        TargetInfo targetInfoResult = targetInfoPersonMapper.selectOne(queryWrapper);
        return targetInfoResult;
    }

    @Override
    public List<MyData> myDataList(String name, Integer month) {
        QueryWrapper<MyData> queryWrapper = new QueryWrapper<MyData>();
        queryWrapper.orderByDesc("month");
        if (!StringUtils.isEmpty(name)) {
            queryWrapper.eq("name", name);
        }
        if (month != null) {
            queryWrapper.eq("month", month);

        }
        List<MyData> myData = myDataMapper.selectList(queryWrapper);
        return myData;

    }

    @Override
    public List<MyData> beforeYearDateList(Integer beforeYearDate, Integer beforeYearDateEnd,String name) {
        QueryWrapper<MyData> queryWrapper = new QueryWrapper<MyData>();
        queryWrapper.orderByDesc("create_time");


        queryWrapper.ge("month", beforeYearDate);
        queryWrapper.le("month", beforeYearDateEnd);
        queryWrapper.eq("name",name);


        List<MyData> myData = myDataMapper.selectList(queryWrapper);
        return myData;
    }


    @Override
    public List<Performance> myPerformanceList(String name, Integer month) {
        QueryWrapper<Performance> queryWrapper = new QueryWrapper<Performance>();
        queryWrapper.orderByDesc("month");
        if (!StringUtils.isEmpty(name)) {
            queryWrapper.eq("name", name);
        }
        if (month != null) {
            queryWrapper.eq("month", month);

        }
        List<Performance> performanceList = performanceMapper.selectList(queryWrapper);
        return performanceList;

    }

//
//    @Override
//    public List<Performance> performanceDataList(int month) {
//        QueryWrapper<Performance> queryWrapper = new QueryWrapper<Performance>();
//        queryWrapper.orderByDesc("create_time");
//        queryWrapper.eq("month", month);
//        List<Performance> performanceList = performanceMapper.selectList(queryWrapper);
//        return performanceList;
//    }
//
//    @Override
//    public List<MyData> tabulateDataList(int month) {
//        QueryWrapper<MyData> queryWrapper = new QueryWrapper<MyData>();
//        queryWrapper.orderByDesc("create_time");
//        queryWrapper.eq("month", month);
//        List<MyData> myData = myDataMapper.selectList(queryWrapper);
//        return myData;
//    }


}
