package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserConstants;
import cn.taqu.gonghui.common.domain.TreeSelect;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysMenu;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.SysMenuMapper;
import cn.taqu.gonghui.system.mapper.SysRoleMenuMapper;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.service.SysMenuService;
import cn.taqu.gonghui.system.vo.SysMenuVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/4/26
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    private static Logger logger = LoggerFactory.getLogger(SysMenuServiceImpl.class);

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;
    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private TokenService tokenService;
    @Autowired
    private OrganizationMapper organizationMapper;

    /**
     * 用户端登陆注册时候调用获取当前用户拥有的菜单权限
     */
    @Override
    public List<SysMenuVo> getMenuListByLoginAccount(Integer businessType) {
        if (businessType == null) {
            throw new ServiceException("business_type_null", "请选择业务类型");
        }
        List<SysMenu> menus = selectMenuTreeByUserId(businessType);
        Collections.sort(menus, new Comparator<SysMenu>() {
            @Override
            public int compare(SysMenu u1, SysMenu u2) {
                Integer integer1 = u1.getOrderNum();
                Integer integer2 = u2.getOrderNum();
                if (integer1 == null) {
                    integer1 = 0;
                }
                if (integer2 == null) {
                    integer2 = 0;
                }
                int diff = integer1 - integer2;
                if (diff > 0) {
                    return 1;
                } else if (diff < 0) {
                    return -1;
                }
                return 0; //相等为0
            }
        });


        List<SysMenu> sysMenus = buildMenuTree(menus);
        List<SysMenuVo> sysMenuVoList = sysMenus.stream().map(SysMenuVo::new).collect(Collectors.toList());
        return sysMenuVoList;
    }

    /**
     * 查询系统菜单列表
     *
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuList(SysMenu menu) {
        return this.baseMapper.selectMenuList(menu);
    }

    /**
     * 返回当前用户是否拥有入参菜单权限
     */
    @Override
    public Map<String, Boolean> selectMenuPermsMap(List<String> pathList) {
        logger.info("[selectMenuPermsMap]获取到pathList:{}", JsonUtils.objectToString(pathList));
        Map<String, Boolean> map = new HashMap<>();

        Map<String, String> permsMap = new HashMap<>();
        Set<String> perms = tokenService.getLoginUser(ServletUtils.getRequest()).getPermissions();
        if (CollectionUtils.isNotEmpty(perms)) {
            for (String perm : perms) {
                permsMap.put(perm, perm);
            }
        }

        if (CollectionUtils.isNotEmpty(pathList)) {
            for (String path : pathList) {
                if (StringUtils.isNotBlank(path)) {
                    String serviceAndMethod = path.substring(path.lastIndexOf("/", path.lastIndexOf("/") - 1) + 1);
                    logger.info("解析serviceAndMethod：{}", serviceAndMethod);
                    String[] split = serviceAndMethod.split("/");
                    String splitStr = split[0] + ":" + split[1];
                    if (StringUtils.isNotBlank(permsMap.get(splitStr))) {
                        map.put(path, true);
                    } else {
                        map.put(path, false);
                    }
                }
            }
        }
        return map;
    }

    /**
     * 根据用户account_uuid查询权限
     *
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByUserId(String accountUuid) {
        List<String> perms = this.baseMapper.selectMenuPermsByUserId(accountUuid);
        return getSetPerms(perms);
    }

    /**
     * 根据用户account_uuid 和业务类型type查询权限
     *
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByUserId(String accountUuid, Integer type) {
        List<String> perms = this.baseMapper.selectMenuPermsByUserIdAndType(accountUuid, type);
        return getSetPerms(perms);
    }

    private Set<String> getSetPerms(List<String> perms) {
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                String serviceAndMethod = perm.substring(perm.lastIndexOf("/", perm.lastIndexOf("/") - 1) + 1);
                String[] split = serviceAndMethod.split("/");
                String splitStr = split[0] + ":" + split[1];
                permsSet.add(splitStr);
            }
        }
        return permsSet;
    }

    /**
     * 根据用户ID查询菜单
     *
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuTreeByUserId(Integer businessType) {
        logger.info("获取当前登陆账号用户信息：{}", JsonUtils.objectToString2(tokenService.getLoginUser(ServletUtils.getRequest()).getUser()));
        // 获取用户信息
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        List<SysMenu> menus = null;
        // 判断是否机构管理员，如果是可以查看所有菜单
        Organization organization = organizationMapper.selectByPrimaryKey(loginUser.getUser().getOrgId());
        if (organization == null) {
            logger.warn("机构id:{},不存在", loginUser.getUser().getOrgId());
            throw new ServiceException("org_not_found", "对不起，当前机构不存在，请联系客服人员");
        }
        Integer livePermissions = organization.getLivePermissions();
        boolean perm = false;
        if (livePermissions == 1 && TeamTypeEnum.LIVE_TEAM.getValue() == businessType) {
            perm = true;
        }
        Integer quliaoPermissions = organization.getQuliaoPermissions();
        if (quliaoPermissions == 1 && TeamTypeEnum.CALL_TEAM.getValue() == businessType) {
            perm = true;
        }
        Integer chatRoomPermissions = organization.getChatRoomPermissions();
        if (chatRoomPermissions == 1 && TeamTypeEnum.TALK_TEAM.getValue() == businessType) {
            perm = true;
        }
        if (!perm) {
            throw new ServiceException("permisson_not_allow", "您没有开通此业务权限，请联系客服人员");
        }
        if (loginUser.getUser().getUserType().equals(1)) {
            List<Integer> typeList = new ArrayList<>();
            typeList.add(businessType);
            menus = this.baseMapper.selectMenuTreeAll(typeList);
        } else {
            menus = this.baseMapper.selectMenuTreeByUserId(loginUser.getUser().getAccountUuid(), businessType);
        }
        logger.info("当前用户：{},拥有菜单为：{}", loginUser.getUser().getAccountUuid(), JsonUtils.objectToString2(menus));
        return menus;
    }

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    @Override
    public List<Long> selectMenuListByRoleId(Long roleId) {
        return this.baseMapper.selectMenuListByRoleId(roleId);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    @Override
    public List<SysMenu> buildMenuTree(List<SysMenu> menus) {
        List<SysMenu> returnList = new ArrayList<SysMenu>();
        List<Long> tempList = new ArrayList<Long>();
        for (SysMenu dept : menus) {
            tempList.add(dept.getMenuId());
        }
        for (Iterator<SysMenu> iterator = menus.iterator(); iterator.hasNext(); ) {
            SysMenu menu = (SysMenu) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId())) {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus) {
        List<SysMenu> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    @Override
    public SysMenu selectMenuById(Long menuId) {
        return this.baseMapper.selectMenuById(menuId);
    }

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean hasChildByMenuId(Long menuId) {
        int result = this.baseMapper.hasChildByMenuId(menuId);
        return result > 0 ? true : false;
    }

    /**
     * 查询菜单使用数量
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean checkMenuExistRole(Long menuId) {
        int result = roleMenuMapper.checkMenuExistRole(menuId);
        return result > 0 ? true : false;
    }

    /**
     * 新增保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertMenu(SysMenu menu) {
        //  目录不允许上级是菜单或者按钮
        if ("M".equals(menu.getMenuType())) {
            if (null != menu.getParentId()) {
                String menuType = this.baseMapper.selectMenuById(menu.getParentId()).getMenuType();
                if (menuType.equals("F") || menuType.equals("C")) {
                    throw new ServiceException("param_error", "目录上级不能是菜单或者按钮");
                }
            }
        }
        //  菜单只能添加在目录或者菜单下
        if ("C".equals(menu.getMenuType())) {
            if (null == menu.getParentId()) {
                throw new ServiceException("param_error", "添加菜单必须选择上级目录");
            }
            if (this.baseMapper.selectMenuById(menu.getParentId()).getMenuType().equals("F")) {
                throw new ServiceException("param_error", "菜单上级只能是目录");
            }
        }
        // 按钮只能添加在菜单下
        if ("F".equals(menu.getMenuType())) {
            if (null == menu.getParentId()) {
                throw new ServiceException("param_error", "添加菜单必须选择上级目录");
            }
            if (!this.baseMapper.selectMenuById(menu.getParentId()).getMenuType().equals("C")) {
                throw new ServiceException("param_error", "按钮上级只能是菜单");
            }
        }
        checkParams(menu);
        SysMenu sysMenu = this.baseMapper.selectMenuById(menu.getParentId());
        if (null != sysMenu) {
            // 如果父节点不为正常状态,则不允许新增子节点
            if (!sysMenu.getStatus().equals("1")) {
                throw new ServiceException("param_error", "父菜单停用，不能新增子菜单");
            }
            menu.setAncestors(sysMenu.getAncestors() + "," + menu.getParentId());
        } else {
            menu.setAncestors("0");
        }
        menu.setCreateTime(System.currentTimeMillis() / 1000);
        this.save(menu);
    }

    /**
     * 修改保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMenu(SysMenu menu) {
        if (null == menu.getMenuId()) {
            throw new ServiceException("param_error", "menuId不能为空");
        }
        checkParams(menu);
        // 所属业务类型不能更改
        if (!menu.getType().equals(this.baseMapper.selectMenuById(menu.getMenuId()).getType())) {
//            if (hasChildByMenuId(menu.getMenuId())) {
//                throw new ServiceException("param_error","当前菜单下存在子菜单，所属业务类型不允许更新");
//            }
            throw new ServiceException("param_error", "当所属业务类型不允许修改");
        }
        SysMenu newParentMenu = this.baseMapper.selectMenuById(menu.getParentId());
        SysMenu oldMenu = this.baseMapper.selectMenuById(menu.getMenuId());
        if (cn.taqu.gonghui.common.utils.StringUtils.isNotNull(newParentMenu) && cn.taqu.gonghui.common.utils.StringUtils.isNotNull(oldMenu)) {
            String newAncestors = newParentMenu.getAncestors() + "," + newParentMenu.getMenuId();
            String oldAncestors = oldMenu.getAncestors();
            menu.setAncestors(newAncestors);
            updateMenuChildren(menu.getMenuId(), newAncestors, oldAncestors);
        }
        menu.setUpdateTime(System.currentTimeMillis() / 1000);
        int result = this.baseMapper.updateMenu(menu);
        // 如果讲当前菜单隐藏，那么隐藏其下所有子菜单
        if (menu.getVisible().equals("0")) {
            updateChildrenVisible(menu);
        }
        return result;
    }

    /**
     * 将当前菜单下的所有子菜单隐藏
     */
    private void updateChildrenVisible(SysMenu menu) {
        this.baseMapper.updateChildrenVisible(menu.getMenuId());
    }

    /**
     * 修改子元素关系
     *
     * @param menuId       被修改的菜单ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateMenuChildren(Long menuId, String newAncestors, String oldAncestors) {
        List<SysMenu> children = this.baseMapper.selectChildrenMenuById(menuId);
        for (SysMenu child : children) {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }
        if (children.size() > 0) {
            this.baseMapper.updateMenuChildren(children);
        }
    }

    /**
     * 校验表单参数
     */
    private void checkParams(SysMenu menu) {
        if (StringUtils.isBlank(menu.getMenuName())) {
            throw new ServiceException("param_error", "菜单名称不能为空");
        } else {
            menu.setMenuName(menu.getMenuName().trim());
        }
        if (menu.getMenuType().equals("C") || menu.getMenuType().equals("F")) {
            if (StringUtils.isBlank(menu.getRouterPath())) {
                throw new ServiceException("param_error", "菜单或者按钮路径不能为空");
            } else {
                menu.setRouterPath(menu.getRouterPath().trim());
            }
        }
        if (menu.getMenuName().length() > 30) {
            throw new ServiceException("param_error", "菜单名称长度超出范围，请输入30字符以内的长度");
        }
        if (null == menu.getOrderNum()) {
            throw new ServiceException("param_error", "菜单排序不能为空");
        }
        if (StringUtils.isBlank(menu.getMenuType())) {
            throw new ServiceException("param_error", "菜单类型不能为空");
        }
        if (null == menu.getType()) {
            throw new ServiceException("param_error", "所属业务类型不能为空");
        }
        if (StringUtils.isBlank(menu.getVisible())) {
            throw new ServiceException("param_error", "菜单是否可见未设置");
        }
        if (StringUtils.isBlank(menu.getStatus())) {
            throw new ServiceException("param_error", "菜单状态未设置");
        }
        if (menu.getMenuType().equals("F")) {
            if (UserConstants.NOT_UNIQUE.equals(checkMenuRouterPathUnique(menu))) {
                String str = "";
                if (menu.getType() == TeamTypeEnum.LIVE_TEAM.getValue()) {
                    str = TeamTypeEnum.LIVE_TEAM.getMsg();
                }
                if (menu.getType() == TeamTypeEnum.CALL_TEAM.getValue()) {
                    str = TeamTypeEnum.CALL_TEAM.getMsg();
                }
                if (menu.getType() == TeamTypeEnum.TALK_TEAM.getValue()) {
                    str = TeamTypeEnum.TALK_TEAM.getMsg();
                }
                throw new ServiceException("param_error", str + "业务下该路径已经存在");
            }
        }
    }

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteMenuById(Long menuId) {
        return this.baseMapper.deleteMenuById(menuId);
    }

    /**
     * 校验菜单名称是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public String checkMenuNameUnique(SysMenu menu) {
        Long menuId = cn.taqu.gonghui.common.utils.StringUtils.isNull(menu.getMenuId()) ? -1L : menu.getMenuId();
        SysMenu info = this.baseMapper.checkMenuNameUnique(menu.getMenuName(), menu.getParentId(), menu.getType());
        if (cn.taqu.gonghui.common.utils.StringUtils.isNotNull(info) && info.getMenuId().longValue() != menuId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验按钮级别路径在不同业务是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public String checkMenuRouterPathUnique(SysMenu menu) {
        Long menuId = cn.taqu.gonghui.common.utils.StringUtils.isNull(menu.getMenuId()) ? -1L : menu.getMenuId();
        String path = menu.getRouterPath();
        String serviceAndMethod = path.substring(path.lastIndexOf("/", path.lastIndexOf("/") - 1) + 1);
        SysMenu info = this.baseMapper.getMenuInfoByServiceAndMethod(serviceAndMethod, menu.getType());
        if (cn.taqu.gonghui.common.utils.StringUtils.isNotNull(info) && info.getMenuId().longValue() != menuId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 根据登陆账号查询用户信息
     *
     * @param accountUuid
     * @return
     */
    @Override
    public SysUser getUserByAccountUuid(String accountUuid) {
        Map<String, Object> columnMap = new HashMap<>();
        columnMap.put("account_uuid", accountUuid);
        List<SysUser> sysUsers = userMapper.selectByMap(columnMap);
        if (CollectionUtils.isEmpty(sysUsers)) {
            throw new ServiceException(CodeStatus.USER_NOT_EXSISTS.value(), "用户不存在");
        }
        return sysUsers.get(0);
    }

    /**
     * 根据服务名称和方法名称获取菜单路径
     *
     * @param service
     * @param method
     * @return
     */
    @Override
    public String getMenuNameByServiceAndMethod(String service, String method) {
        String serviceAndMethod = service + "/" + method;
        return this.baseMapper.getmenuNameByServiceAndMethod(serviceAndMethod);
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<SysMenu> getChildPerms(List<SysMenu> list, int parentId) {
        List<SysMenu> returnList = new ArrayList<SysMenu>();
        for (Iterator<SysMenu> iterator = list.iterator(); iterator.hasNext(); ) {
            SysMenu t = (SysMenu) iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId() == parentId) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list
     * @param t
     */
    private void recursionFn(List<SysMenu> list, SysMenu t) {
        // 得到子节点列表
        List<SysMenu> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysMenu tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysMenu> getChildList(List<SysMenu> list, SysMenu t) {
        List<SysMenu> tlist = new ArrayList<SysMenu>();
        Iterator<SysMenu> it = list.iterator();
        while (it.hasNext()) {
            SysMenu n = (SysMenu) it.next();
            if (n.getParentId().longValue() == t.getMenuId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysMenu> list, SysMenu t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
