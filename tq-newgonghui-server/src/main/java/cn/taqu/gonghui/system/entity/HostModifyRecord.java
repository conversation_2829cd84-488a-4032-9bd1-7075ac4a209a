package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * @Description 艺人操作明细表
 * <AUTHOR>
 * @Date 2022/5/10 11:06 上午
 **/
@Data
public class HostModifyRecord {

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 艺人uuid
     */
    private String hostUuid;

    /**
     * 业务类型(1-直播 2-趣聊 3-聊天室)
     */
    private Integer teamType;

    /**
     * 批量操作id，没有就默认13位时间戳，表示单独操作
     */
    private String batchId;

    /**
     * 操作类型，1-机构合并，2-艺人管理-设定工会
     */
    private Integer operateType;

    /**
     * 操作内容，业务自定义内容格式
     */
    private String info;

    /**
     * 操作理由，文字或者图片链接
     */
    private String reason;

    /**
     * 操作状态，1-成功，2-失败
     */
    private Integer status;

    /**
     * 失败理由
     */
    private String failMsg;

    /**
     * 操作人
     */
    private String operator;

    private Long createTime;
    private Long updateTime;

    /**
     * 操作类型枚举
     */
    public enum OperateTypeEnum{
        /**
         * 机构合并团队
         */
        MERGE_ORG(1),
        /**
         * 变更聊天室
         */
        CHANGE_CHAT_TEAM(2),
        ;
        private Integer value;

        OperateTypeEnum(Integer value){
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }
}
