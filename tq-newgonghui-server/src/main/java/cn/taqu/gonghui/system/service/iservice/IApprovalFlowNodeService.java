package cn.taqu.gonghui.system.service.iservice;

import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.system.entity.OperatorLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/8 19 23
 * discription
 */
public interface IApprovalFlowNodeService extends IService<ApprovalFlowNode> {

    List<ApprovalFlowNode> getByRange(Long startId, Long endId);

    void updateClearTxtByRange(Long curStartId, Long curEndId);
}
