package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.vo.LiveHostStatisticVo;
import cn.taqu.gonghui.live.search.OrgStatisticsSearch;
import cn.taqu.gonghui.system.search.LivePerDayStatisticSearch;
import cn.taqu.gonghui.system.vo.DailyVo;
import cn.taqu.gonghui.system.vo.LiveHostStatisticNewVo;
import cn.taqu.gonghui.system.vo.DailyStatisticsVo;
import cn.taqu.gonghui.system.vo.MonthVo;

import java.util.List;
import java.util.Map;

public interface OrgStatisticsService {

    LiveHostStatisticNewVo getLiveHostStatisticNew(String employeeId,String date,String orderType,String sortType,
                                                   Integer export,String teamId,String applyLevel,String liveNo,Integer page,Integer pageSize,Integer type);

    Map<String,Object> getLiveHostStatisticNewForAdmin(String orgId, String employeeId, String date, String orderType, String sortType,
                                        Integer export, String teamId, String applyLevel, String liveNo, Integer page, Integer pageSize);

    DailyVo getTeamDailyStatistics(OrgStatisticsSearch search);


    /**
     * 每日直播数据
     * @param search
     * @return
     */
    List<DailyStatisticsVo> getOrgDailyStatistics(LivePerDayStatisticSearch search);


    /**
     * 数据统计
     * @param search
     * @return
     */
    LiveHostStatisticVo getOrgStatistics(LivePerDayStatisticSearch search);

    MonthVo getOperatorDailyStatistics(OrgStatisticsSearch search);


    /**
     * 数据统计（用户端）
     * @param search
     * @return
     */
    LiveHostStatisticVo getOrgStatisticsByUser(LivePerDayStatisticSearch search);
    /**
     * 每日直播数据
     * @param search
     * @return
     */
    List<DailyStatisticsVo> getOrgDailyStatisticsByUser(LivePerDayStatisticSearch search);
}
