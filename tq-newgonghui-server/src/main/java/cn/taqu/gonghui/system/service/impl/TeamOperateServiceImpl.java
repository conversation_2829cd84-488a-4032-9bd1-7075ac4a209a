package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.gonghui.chatroom.service.ChatRoomService;
import cn.taqu.gonghui.common.constant.HostOperateTypeEnum;
import cn.taqu.gonghui.common.constant.TeamOperateTypeEnum;
import cn.taqu.gonghui.common.constant.TeamStatusEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.utils.UUID;
import cn.taqu.gonghui.system.config.TeamSignConfig;
import cn.taqu.gonghui.system.entity.BackstageOperateLog;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.entity.TeamOperateInfo;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.mapper.TeamMapper;
import cn.taqu.gonghui.system.mapper.TeamOperateInfoMapper;
import cn.taqu.gonghui.system.service.OrganizationUserService;
import cn.taqu.gonghui.system.service.TeamOperateService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.vo.TeamOperateRequestVo;
import cn.taqu.gonghui.system.vo.TeamOperateResponseVo;
import cn.taqu.gonghui.system.vo.TeamOrgInfoVO;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/10 4:52 下午
 **/
@Slf4j
@Service
public class TeamOperateServiceImpl implements TeamOperateService {
    private final static Random RANDOM = new Random();

    @Autowired
    private TeamService teamService;
    @Autowired
    private OrganizationUserService organizationUserService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private TeamSignConfig teamSignConfig;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    private ChatRoomService chatRoomService;
    @Autowired
    private TeamOperateInfoMapper teamOperateInfoMapper;

    @Override
    public void operateTeam(TeamOperateRequestVo vo) {
        Team oldTeamDetail = teamService.detail(vo.getOldTeamId());
        if (oldTeamDetail == null) {
            // 判断是否存在
            throw new ServiceException("team_empty", "团队获取失败");
        }
        if (!Objects.equals(oldTeamDetail.getOrgId(), vo.getOldOrgId())) {
            throw new ServiceException("org_error", "机构无此团队");
        }
        List<TeamHost> teamHosts = teamHostMapper.idsByAgentId(vo.getOldTeamId());
        if (CollectionUtils.isEmpty(teamHosts)) {
            // 没有艺人就不进行迁移了
            log.info("团队id：{}，旗下没有艺人，不进行迁移", vo.getOldTeamId());
            return;
        }
        // 获取新团队id
        this.getNewTeam(vo, oldTeamDetail);
        // 迁移信息info
        Map<String, Object> infoMap = new HashMap<>();
        infoMap.put("oldOrgId", vo.getOldOrgId());
        infoMap.put("oldTeamId", vo.getOldTeamId());
        infoMap.put("newOrgId", vo.getNewOrgId());
        infoMap.put("newTeamId", vo.getNewTeamId());
        String info = JsonUtils.objectToString(infoMap);
        // 迁移艺人到新团队
        String uuid = UUID.genUuid();
        int sucNum = 0, failNum = 0;
        for (TeamHost host : teamHosts) {
            try {
                chatRoomService.changeTeamForChat(host.getHostUuid(), vo.getNewTeamId(), info, uuid, HostOperateTypeEnum.TEAM_MOVE, TeamTypeEnum.getByValue(oldTeamDetail.getType()));
                sucNum++;
            } catch (Exception e) {
                // 有异常继续，不影响其他艺人迁移
                log.warn("迁移团队旗下艺人：" + host.getHostUuid() + "，报错", e);
                failNum++;
            }
        }
        // 记录日志
        TeamOperateInfo teamOperateInfo = new TeamOperateInfo();
        teamOperateInfo.setBatchId(uuid);
        teamOperateInfo.setType(TeamOperateTypeEnum.TEAM_MOVE.ordinal());
        teamOperateInfo.setOldOrgId(vo.getOldOrgId());
        teamOperateInfo.setOldTeamId(vo.getOldTeamId());
        teamOperateInfo.setNewOrgId(vo.getNewOrgId() == null ? 0 : vo.getNewOrgId());
        teamOperateInfo.setNewTeamId(vo.getNewTeamId() == null ? 0 : vo.getNewTeamId());
        teamOperateInfo.setTeamType(oldTeamDetail.getType());
        teamOperateInfo.setStatus(1);
        teamOperateInfo.setHostNum(teamHosts.size());
        teamOperateInfo.setSuccessNum(sucNum);
        teamOperateInfo.setFailNum(failNum);
        teamOperateInfo.setOperator(SoaBaseParams.fromThread().getToken());
        teamOperateInfo.setCreateTime(System.currentTimeMillis() / 1000);
        teamOperateInfo.setUpdateTime(System.currentTimeMillis() / 1000);
        teamOperateInfoMapper.insert(teamOperateInfo);
    }

    private void getNewTeam(TeamOperateRequestVo vo, Team oldTeamDetail) {
        if (vo.getNewTeamId() != null) {
            // 外部有传入，直接用外部的
            return;
        }
        // 没有团队id，就先新建一个团队
        // 先判断业务有没有开通，没有先开通
        List<Integer> types = organizationUserService.getOrgInfoType(vo.getNewOrgId());
        if (CollectionUtils.isEmpty(types) || !types.contains(oldTeamDetail.getType())) {
            // 如果新机构没有开通聊天室业务，则激活功能，生成默认团队，把艺人都迁移到默认团队
            String token = SoaBaseParams.fromThread().getToken();
            // 开启权限
            Organization organization = new Organization();
            organization.setOrgId(vo.getNewOrgId());
            organization.setChatRoomPermissions(1);
            organizationMapper.updateByPrimaryKeySelective(organization);
            // 创建默认团队
            teamService.insertDefaultTeamByType(vo.getNewOrgId(), "聊天室默认团队 - " + vo.getNewOrgId(), oldTeamDetail.getType(),
                    1, token, teamSignConfig.getTeamSignByType(oldTeamDetail.getType()));
            // 获取要迁移的团队id
            Team defaultTeam = teamMapper.getDefaultTeam(vo.getNewOrgId(), oldTeamDetail.getType());
            vo.setNewTeamId(defaultTeam.getTeamId());
        } else if (vo.getNewTeamId() == null) {
            // 新机构开通业务，并且有默认团队，则新建一个团队，把艺人迁移到新团队
            Team record = new Team();
            record.setOrgId(vo.getNewOrgId());
            record.setTeamName(oldTeamDetail.getTeamName() + RANDOM.nextInt(10));
            record.setType(oldTeamDetail.getType());
            record.setStatus(1);
            record.setSignKey(oldTeamDetail.getSignKey());
            record.setInviteCode(teamService.getUniqueInvivationCode());
            // 判断5次团队名称是否重复
            for (int i = 0; i < 5; i++) {
                if (teamService.teamNameHasExist(record)) {
                    record.setTeamName(oldTeamDetail.getTeamName() + RANDOM.nextInt(20));
                    continue;
                }
                break;
            }
            teamService.insertTeam(record);
            vo.setNewTeamId(record.getTeamId());
        }
    }

    /**
     * 获取迁移团队历史记录
     *
     * @param pageNo
     * @param pageSize
     * @param type
     * @return
     */
    public List<TeamOperateResponseVo> getOperateList(int pageNo, int pageSize, Integer type) {
        PageHelper.startPage(pageNo, pageSize);
        List<TeamOperateInfo> list = teamOperateInfoMapper.findAll(type);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        Set<Long> teamIds = new HashSet<>();
        for (TeamOperateInfo info : list) {
            teamIds.add(info.getOldTeamId());
            teamIds.add(info.getNewTeamId());
        }
        List<TeamOrgInfoVO> teamInfos = teamMapper.selectTeamOrgInfoByTeamIds(new ArrayList<>(teamIds));
        Map<Long, TeamOrgInfoVO> teamMap = teamInfos.stream().collect(Collectors.toMap(TeamOrgInfoVO::getTeamId, v -> v, (m1, m2) -> m2));
        return list.stream()
                .map(info -> {
                    TeamOrgInfoVO oldTeam = teamMap.get(info.getOldTeamId());
                    TeamOrgInfoVO newTeam = teamMap.get(info.getNewTeamId());
                    TeamOperateResponseVo vo = new TeamOperateResponseVo();
                    vo.setBatchId(info.getBatchId());
                    vo.setCreateTime(info.getCreateTime());
                    if (oldTeam != null) {
                        vo.setOldOrgName(oldTeam.getOrgName());
                        vo.setOldTeamName(oldTeam.getTeamName());
                    }
                    if (newTeam != null) {
                        vo.setNewOrgName(newTeam.getOrgName());
                        vo.setNewTeamName(newTeam.getTeamName());
                    }
                    vo.setOperator(info.getOperator());
                    vo.setSuccessNum(info.getSuccessNum());
                    vo.setFailNum(info.getFailNum());
                    return vo;
                })
                .collect(Collectors.toList());
    }
}
