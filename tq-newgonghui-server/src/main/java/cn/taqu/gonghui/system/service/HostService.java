package cn.taqu.gonghui.system.service;


import cn.taqu.gonghui.system.search.HostStatisticSearch;
import cn.taqu.gonghui.system.vo.*;

import java.util.List;
import java.util.Map;

public interface HostService {
    Map<String, Object> getNicknameByMobile(String mobile);

    String getNickNameByUuid(String accountUuid);

    Map<String,String> getNickNameByUuids(List<String> accountUuids);

    Map<String, Map<String,Object>> getInfoByUuids(String[] accountUuids, String[] fields);

    RecommendApplyInfoVo getRecommendApplyInfo(String hostUuid, String consortiaId);

    void addRecommendApplyG(String consortiaId, String date, String location, String hostUuid, String time, Integer isGold);

    String getUuidByAccountName(String accountName);


    PageData getRecommendApplyList(String date, List<String> uuidList, String host_name,
                                   String location, Integer status, String consortia_id, String limit, Integer type);

    void cancelApplyById(Long id);

    LiveHostStatisticNewVo getConsortiaNewHostStat(HostStatisticSearch search);

    void changeHostBusinessman(String hostUuid,String businessUuid);

    void changeHostConsortia(String consortiaId, List<String> hostUuidList);

    String getUuidByMobile(String mobile);

    Integer zhimaCertification(String accountUuid);

    Integer getTaquConsortiaId();

    /**
     * 获取素人公会的团队id
     * @return
     */
    Integer getPersonalConsortiaId();

    /**
     * 是否属于他趣公会
     *
     * @param teamId
     * @return
     */
    Boolean isInTaquOrPersonalTeam(Long teamId);

    void sendSpiltRatioNotice(Long changeId, String hostUuid, String teamName, Long changeTime);


}
