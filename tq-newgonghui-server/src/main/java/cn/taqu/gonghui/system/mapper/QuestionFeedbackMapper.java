package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.dto.QuestionFeedbackDto;
import cn.taqu.gonghui.system.entity.QuestionFeedback;
import cn.taqu.gonghui.system.search.QuestionFeedbackSearch;
import cn.taqu.gonghui.system.vo.QuestionFeedbackVo;

import java.util.List;

public interface QuestionFeedbackMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(QuestionFeedback record);

    QuestionFeedback selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(QuestionFeedback record);

    List<QuestionFeedbackVo> manageList(QuestionFeedbackSearch search);

    List<QuestionFeedbackVo> userList(QuestionFeedbackSearch search);

    void changeStatus(QuestionFeedbackDto dto);
}