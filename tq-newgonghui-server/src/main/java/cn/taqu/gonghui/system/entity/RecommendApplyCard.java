package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 营销管理（推荐位管理）
 */
@Data
public class RecommendApplyCard {
    /**
     * 流水号
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 卡片编号
     */
    private String cardNo;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 发卡时间
     */
    private Long cardCreateTime;

    /**
     * 卡片过期时间
     */
    private Long cardExpireTime;

    /**
     * 卡片有效天数
     */
    private Integer effectiveDays;

    /**
     * 可使用卡片数量
     */
    private Integer useNum;

    /**
     * 总卡片数量
     */
    private Integer totalNum;

    /**
     * 卡片创建人
     */
    private String createUser;

    /**
     * 卡片状态（1-有效，0-作废）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}
