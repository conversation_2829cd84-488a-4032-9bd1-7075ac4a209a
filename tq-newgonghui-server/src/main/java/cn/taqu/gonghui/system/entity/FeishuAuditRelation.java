package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @Description 飞书审批流关联表
 * <AUTHOR>
 * @Date 2022/9/16 11:32
 **/
@Data
@TableName(value = "feishu_audit_relation")
public class FeishuAuditRelation {
    @TableId(value = "id", type = IdType.AUTO)
    public Long id;
    @TableField(value = "approval_code")
    public String approvalCode;
    @TableField(value = "instance_code")
    public String instanceCode;
    @TableField(value = "business_id")
    public Long businessId;
    @TableField(value = "business_type")
    public Integer businessType;
    /**
     * 状态，
     * 用户创建审批后，推送【PENDING】状态
     * 任一审批人拒绝后，推送【REJECTED】状态
     * 流程中所有人同意后，推送【APPROVED】状态
     * 发起人撤回审批后，推送【CANCELED】状态
     * 审批定义被管理员删除后，推送【DELETED】状态
     * 发起人撤销已通过的审批，推送【REVERTED】状态
     */
    @TableField(value = "status")
    public String status;
    @TableField(value = "create_time")
    public Date createTime;
    @TableField(value = "modify_time")
    public Date modifyTime;
}
