package cn.taqu.gonghui.system.mapper;


import cn.taqu.gonghui.system.entity.OpeningPermit;

public interface OpeningPermitMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(OpeningPermit record);

    int insertSelective(OpeningPermit record);

    OpeningPermit selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(OpeningPermit record);

    int updateByPrimaryKey(OpeningPermit record);

    int deleteByUuid(String uuid);
}