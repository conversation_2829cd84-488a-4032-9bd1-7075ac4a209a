package cn.taqu.gonghui.system.vo;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;


public class DailyStatisticsVo {

    /**
     * 统计时间
     */
    private Long dayTime;

    /**
     * 主播人数
     */
    private Long hostNum;

    /**
     * 收益
     */
    private Long amount;

    /**
     * 鲜花数量
     */
    private Long flower;

    /**
     * 直播时长
     */
    private Long totalLiveTime;

    /**
     * 观看人数
     */
    private Long viewer;

    /**
     * 送礼人数
     */
    private Long send;

    /**
     * 粉丝人数
     */
    private Long fans;

    /**
     * 消息数量
     */
    private Long message;

    private String totalTimeStr;

    /**
     * 团队id
     */
    private Long consortiaId;
    /**
     * 团队iname
     */
    private String teamName;

    /**
     * 游戏收入占比
     */
    private String gameRatio;

    /**
     * 游戏收入
     */
    private Integer gameAmount;

    /**
     * 贝壳收入
     */
    private Long shellAmount;

    /**
     * 系统活动收益
     */
    private Integer systemActivityAmount;



    public Long getDayTime() {
        return dayTime;
    }

    public void setDayTime(Long dayTime) {
        this.dayTime = dayTime;
    }

    public Long getHostNum() {
        return hostNum;
    }

    public void setHostNum(Long hostNum) {
        this.hostNum = hostNum;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getFlower() {
        return flower;
    }

    public void setFlower(Long flower) {
        this.flower = flower;
    }

    public Long getTotalLiveTime() {
        return totalLiveTime;
    }

    public void setTotalLiveTime(Long totalLiveTime) {
        this.totalLiveTime = totalLiveTime;
    }

    public Long getViewer() {
        return viewer;
    }

    public void setViewer(Long viewer) {
        this.viewer = viewer;
    }

    public Long getSend() {
        return send;
    }

    public void setSend(Long send) {
        this.send = send;
    }

    public Long getFans() {
        return fans;
    }

    public void setFans(Long fans) {
        this.fans = fans;
    }

    public Long getMessage() {
        return message;
    }

    public void setMessage(Long message) {
        this.message = message;
    }

    public String getTotalTimeStr() {
        return totalTimeStr;
    }

    public void setTotalTimeStr(String totalTimeStr) {
        this.totalTimeStr = totalTimeStr;
    }

    public Long getConsortiaId() {
        return consortiaId;
    }

    public void setConsortiaId(Long consortiaId) {
        this.consortiaId = consortiaId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getGameRatio() {
        return gameRatio;
    }

    public void setGameRatio(String gameRatio) {
        this.gameRatio = gameRatio;
    }

    public Integer getGameAmount() {
        return gameAmount;
    }

    public void setGameAmount(Integer gameAmount) {
        this.gameAmount = gameAmount;

        if(this.gameAmount == null){
            this.gameRatio =  "0%";
            this.gameAmount =  0;
            return;
        }
        if(this.gameAmount == 0){
            this.gameRatio =  "0%";
            return;
        }
        if(null != this.gameAmount && this.gameAmount !=0){
            BigDecimal ratio = new BigDecimal(this.gameAmount).divide(new BigDecimal(this.amount), 4, RoundingMode.HALF_UP);

//            float ratio = (float)this.gameAmount/this.amount;
            BigDecimal multiply = ratio.multiply(new BigDecimal(100));
            BigDecimal f1 = multiply.setScale(2, BigDecimal.ROUND_HALF_UP);

            this.gameRatio = f1+ "%";
        }
    }

    public Long getTotalScore() {
        return Optional.ofNullable(amount).orElse(0L) + Optional.ofNullable(shellAmount).orElse(0L);
    }

    public String getShellRatio() {
        if (this.shellAmount == null || this.shellAmount == 0) {
            return "0%";
        }
        Long totalScore = getTotalScore();
        if (totalScore == 0L) {
            return "0%";
        }

        BigDecimal ratio = new BigDecimal(this.shellAmount).divide(new BigDecimal(getTotalScore()), 4, RoundingMode.HALF_UP);
        BigDecimal multiply = ratio.multiply(new BigDecimal(100));
        BigDecimal r = multiply.setScale(2, RoundingMode.HALF_UP);
        return r + "%";
    }

    public Long getShellAmount() {
        return shellAmount;
    }

    public void setShellAmount(Long shellAmount) {
        this.shellAmount = shellAmount;
    }

    public Integer getSystemActivityAmount() {
        return systemActivityAmount;
    }

    public void setSystemActivityAmount(Integer systemActivityAmount) {
        this.systemActivityAmount = systemActivityAmount;
    }

}
