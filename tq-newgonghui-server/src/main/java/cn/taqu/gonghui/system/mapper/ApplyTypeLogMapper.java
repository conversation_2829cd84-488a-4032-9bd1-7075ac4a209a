package cn.taqu.gonghui.system.mapper;


import cn.taqu.gonghui.system.entity.ApplyTypeLog;

import java.util.List;

public interface ApplyTypeLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(ApplyTypeLog record);

    int insertSelective(ApplyTypeLog record);

    ApplyTypeLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ApplyTypeLog record);

    int updateByPrimaryKey(ApplyTypeLog record);

    List<ApplyTypeLog> selectByOrgId(Long orgId);
}