package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.chatroom.search.ChatRoomSearch;
import cn.taqu.gonghui.live.vo.HostTaskStatisticInfoVo;
import cn.taqu.gonghui.soa.dto.HostTaskStatisticInfo;
import cn.taqu.gonghui.system.entity.TeamHost;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/11
 */
public interface HostTaskService {



    IPage<HostTaskStatisticInfoVo> pageStatDataBySearch(Integer page, Integer pageSize, Long monthDate, String hostUuid);


    JSONObject listDetailData(String hostUuid, Integer taskUuid, Long month);
}
