package cn.taqu.gonghui.system.service.impl;


import cn.hutool.json.JSONUtil;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.log.Log;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.gonghui.chatroom.service.ChatRoomService;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.gonghui.common.constant.ApplyStatusEnum;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.Constants;
import cn.taqu.gonghui.common.constant.EmployeeStatusEnum;
import cn.taqu.gonghui.common.constant.ErrorCodeStatus;
import cn.taqu.gonghui.common.constant.FormStatusEnum;
import cn.taqu.gonghui.common.constant.HostOperateTypeEnum;
import cn.taqu.gonghui.common.constant.LiveSettlementeTypeEnum;
import cn.taqu.gonghui.common.constant.OperatorTypeEnum;
import cn.taqu.gonghui.common.constant.OrgIdEnum;
import cn.taqu.gonghui.common.constant.OrgStatusEnum;
import cn.taqu.gonghui.common.constant.TeamHostOperateLogEnum;
import cn.taqu.gonghui.common.constant.TeamStatusEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserStatus;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.constant.VerifyCodeEnum;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.constant.callback.feishu.FeiShuRelationType;
import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.entity.OrgFlowLog;
import cn.taqu.gonghui.common.mapper.OrgFlowLogMapper;
import cn.taqu.gonghui.common.service.AccountLoginService;
import cn.taqu.gonghui.common.service.FeiShuService;
import cn.taqu.gonghui.common.service.VerifyService;
import cn.taqu.gonghui.common.utils.ParamsCheckUtil;
import cn.taqu.gonghui.common.utils.SpringUtils;
import cn.taqu.gonghui.common.utils.UUID;
import cn.taqu.gonghui.common.vo.ApprovalFlowItem;
import cn.taqu.gonghui.common.vo.OrganizationVo;
import cn.taqu.gonghui.common.vo.RemitSearchVO;
import cn.taqu.gonghui.common.vo.feishu.ApprovalInstanceRequest;
import cn.taqu.gonghui.soa.SsomsService;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.config.FeishuConfig;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.dto.TeamDto;
import cn.taqu.gonghui.system.entity.ApplyTypeLog;
import cn.taqu.gonghui.system.entity.BackstageOperateLog;
import cn.taqu.gonghui.system.entity.BusinessLicense;
import cn.taqu.gonghui.system.entity.ChargePerson;
import cn.taqu.gonghui.system.entity.HostScreenshot;
import cn.taqu.gonghui.system.entity.LegalPerson;
import cn.taqu.gonghui.system.entity.OpeningPermit;
import cn.taqu.gonghui.system.entity.OperatorLog;
import cn.taqu.gonghui.system.entity.OrgCooperationFlow;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysRole;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.SysUserRole;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.entity.TeamHostOperateLog;
import cn.taqu.gonghui.system.mapper.ApplyTypeLogMapper;
import cn.taqu.gonghui.system.mapper.BusinessLicenseMapper;
import cn.taqu.gonghui.system.mapper.ChargePersonMapper;
import cn.taqu.gonghui.system.mapper.HostScreenshotMapper;
import cn.taqu.gonghui.system.mapper.LegalPersonMapper;
import cn.taqu.gonghui.system.mapper.OpeningPermitMapper;
import cn.taqu.gonghui.system.mapper.OperatorLogMapper;
import cn.taqu.gonghui.system.mapper.OrgCooperationFlowMapper;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.SysRoleMapper;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.mapper.SysUserRoleMapper;
import cn.taqu.gonghui.system.mapper.TeamEmployeeMapper;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.mapper.TeamHostOperateLogMapper;
import cn.taqu.gonghui.system.mapper.TeamMapper;
import cn.taqu.gonghui.system.entity.ApplyTypeLog;
import cn.taqu.gonghui.system.entity.BusinessLicense;
import cn.taqu.gonghui.system.entity.ChargePerson;
import cn.taqu.gonghui.system.entity.FeishuAuditRelation;
import cn.taqu.gonghui.system.entity.HostScreenshot;
import cn.taqu.gonghui.system.entity.LegalPerson;
import cn.taqu.gonghui.system.entity.OpeningPermit;
import cn.taqu.gonghui.system.entity.OperatorLog;
import cn.taqu.gonghui.system.entity.OrgCooperationFlow;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysRole;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.SysUserRole;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.entity.TeamHostOperateLog;
import cn.taqu.gonghui.system.mapper.ApplyTypeLogMapper;
import cn.taqu.gonghui.system.mapper.BusinessLicenseMapper;
import cn.taqu.gonghui.system.mapper.ChargePersonMapper;
import cn.taqu.gonghui.system.mapper.HostScreenshotMapper;
import cn.taqu.gonghui.system.mapper.LegalPersonMapper;
import cn.taqu.gonghui.system.mapper.OpeningPermitMapper;
import cn.taqu.gonghui.system.mapper.OperatorLogMapper;
import cn.taqu.gonghui.system.mapper.OrgCooperationFlowMapper;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.SysRoleMapper;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.mapper.SysUserRoleMapper;
import cn.taqu.gonghui.system.mapper.TeamEmployeeMapper;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.mapper.TeamHostOperateLogMapper;
import cn.taqu.gonghui.system.mapper.TeamMapper;
import cn.taqu.gonghui.system.search.OrganizationInfoSearch;
import cn.taqu.gonghui.system.search.TeamSearch;
import cn.taqu.gonghui.system.service.*;
import cn.taqu.gonghui.system.vo.RemitItemVO;
import cn.taqu.gonghui.system.vo.SsoUserCombobox;
import cn.taqu.gonghui.system.service.BackstageOperateLogService;
import cn.taqu.gonghui.system.service.HostModifyRecordService;
import cn.taqu.gonghui.system.service.HostService;
import cn.taqu.gonghui.system.service.OrganizationService;
import cn.taqu.gonghui.system.service.PushService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.aspectj.weaver.ast.Or;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrganizationServiceImpl implements OrganizationService {
    private Logger log = LoggerFactory.getLogger(OrganizationServiceImpl.class);

    @EtcdValue("biz.whiteList.org")
    public static String orgWhiteList;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private OperatorLogMapper operatorLogMapper;
    @Autowired
    private HostScreenshotMapper hostScreenshotMapper;
    @Autowired
    private OrgCooperationFlowMapper orgCooperationFlowMapper;
    @Autowired
    private LegalPersonMapper legalPersonMapper;
    @Autowired
    private BusinessLicenseMapper businessLicenseMapper;
    @Autowired
    private ChargePersonMapper chargePersonMapper;
    @Autowired
    private OpeningPermitMapper openingPermitMapper;
    @Autowired
    private TeamEmployeeMapper teamEmployeeMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private ApplyTypeLogMapper applyTypeLogMapper;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private AccountLoginService accountLoginService;
    @Value("${sms.url}")
    private String smsUrl;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    private HostService hostService;
    @Autowired
    private TeamHostOperateLogMapper teamHostOperateLogMapper;
    @Autowired
    private OrganizationLogService organizationLogService;
    @Autowired
    private FeiShuService feiShuService;
    @SoaReference(application = "ssoms", value = "ssoms")
    private SsomsService ssomsService;

    @Autowired
    private OrgFlowLogMapper orgFlowLogMapper;

    @Autowired
    private BackstageOperateLogService backstageOperateLogService;
    @Autowired
    private HostModifyRecordService hostModifyRecordService;
    @Autowired
    private PushService pushService;

    @Autowired
    private OrganizationService organizationService;

    @Override
    public PageInfo<List<OrganizationInfoSearch>> findOrgInfoPageList(OrganizationInfoSearch search, int pageNo, int pageSize) {
        List<Organization> organizationInfoSearchList = null;
        try {
            PageHelper.startPage(pageNo == 0 ? 1 : pageNo, pageSize == 0 ? 1 : pageSize, "org_id desc");
            search.setSelectByDigest(EncryptSwitchConfig.selectByDigest);
            organizationInfoSearchList = organizationMapper.findOrgInfoPageList(search);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            PageHelper.clearPage();
        }
        PageInfo<List<OrganizationInfoSearch>> pageInfo = new PageInfo(organizationInfoSearchList);
        return pageInfo;
    }

    @Override
    public PageInfo<List<OrganizationInfoSearch>> findPassOrgInfoPageList(OrganizationInfoSearch search, int pageNo, int pageSize) {
        //通过邀请码搜索
        search.setApplyStatus(ApplyStatusEnum.SUCCESS.getValue());
        List<Organization> organizationInfoSearchList = null;
        if (CollectionUtils.isNotEmpty(search.getBusinessPermissions())) {
            if (search.getBusinessPermissions().contains(TeamTypeEnum.LIVE_TEAM.getValue())) {
                search.setLivePermissions(TeamStatusEnum.VALID.getValue());
            }
            if (search.getBusinessPermissions().contains(TeamTypeEnum.TALK_TEAM.getValue())) {
                search.setChatRoomPermissions(TeamStatusEnum.VALID.getValue());
            }
        }
        // 如果不是白名单用户，只能查运营人员是自己的记录
        String operator = SoaBaseParams.fromThread().getToken();
        if (!orgWhiteList.contains(operator)) {
            SsoUserCombobox ssoUser = ssomsService.getSsoUser(operator);
            search.setBusinessPerson(ssoUser.getName());
        }
        try {
            PageHelper.startPage(pageNo == 0 ? 1 : pageNo, pageSize == 0 ? 1 : pageSize, "org_id desc");
            search.setSelectByDigest(EncryptSwitchConfig.selectByDigest);
            organizationInfoSearchList = organizationMapper.findPassOrgInfoPageList(search);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            PageHelper.clearPage();
        }
        PageInfo<List<OrganizationInfoSearch>> pageInfo = new PageInfo(organizationInfoSearchList);
        return pageInfo;
    }

    public Organization getOrgInfo(Long id) {
        Organization organizationSearch = new Organization();
        organizationSearch.setOrgId(id);
        organizationSearch.setAccountUuid(null);
        organizationSearch.setOrgUuid(null);
        Organization organization = new Organization();
        try {
            organization = organizationMapper.getOrgInfo(organizationSearch);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return organization;
    }


    /**
     * 审核机构 通过2  拒绝3
     *
     * @param id
     * @param applyStatus
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeApplyStatus(Long id, Integer applyStatus, String auditMsg, String operator, String operationSpecialistId, Integer settlementeType, Integer[] businessPermissions) {
        Organization organization = checkOrgApply(id, applyStatus);
        if (organization.getApplyStatus().intValue() != ApplyStatusEnum.WAIT.getValue()) {
            throw new ServiceException("org_already_operate", "机构已经操作过，请勿重复操作！");
        }
        if (StringUtils.isEmpty(operationSpecialistId) && applyStatus.intValue() == ApplyStatusEnum.SUCCESS.getValue()) {
            throw new ServiceException("error", "未分配运营人员");
        }
        organization.setLiveSettlementeType(settlementeType);
        organization.setUpdateTime(DateUtil.currentTimeSeconds());
        organization.setAuditPerson(operator);
        if (applyStatus == ApplyStatusEnum.FAILED.getValue()) {//拒绝
            organization.setApplyStatus(applyStatus);
            this.rejectOrg(auditMsg, organization);
        } else {
            if (businessPermissions == null || businessPermissions.length == 0) {
                throw new ServiceException("business_permissions_null", "请勾选要开通的业务类型");
            }
            List<Integer> permissionsList = Arrays.asList(businessPermissions);
            if (permissionsList.contains(TeamTypeEnum.LIVE_TEAM.getValue())) {
                organization.setLivePermissions(TeamStatusEnum.VALID.getValue());
            } else {
                organization.setLivePermissions(TeamStatusEnum.NO_VALID.getValue());
            }
            if (permissionsList.contains(TeamTypeEnum.CALL_TEAM.getValue())) {
                organization.setQuliaoPermissions(TeamStatusEnum.NO_VALID.getValue());
            } else {
                organization.setQuliaoPermissions(TeamStatusEnum.NO_VALID.getValue());
            }
            if (permissionsList.contains(TeamTypeEnum.TALK_TEAM.getValue())) {
                organization.setChatRoomPermissions(TeamStatusEnum.VALID.getValue());
            } else {
                organization.setChatRoomPermissions(TeamStatusEnum.NO_VALID.getValue());
            }
            if (organization.getLivePermissions() == TeamStatusEnum.VALID.getValue() && settlementeType == null) {
                throw new ServiceException("settlement_type_null", "请选择结算方式");
            }
            int livePer = TeamStatusEnum.NO_VALID.getValue();
            int callPer = TeamStatusEnum.NO_VALID.getValue();
            int talkPer = TeamStatusEnum.NO_VALID.getValue();
            if (permissionsList.contains(TeamTypeEnum.LIVE_TEAM.getValue())) {
                livePer = TeamStatusEnum.VALID.getValue();
            }
            if (permissionsList.contains(TeamTypeEnum.CALL_TEAM.getValue())) {
                callPer = TeamStatusEnum.VALID.getValue();
            }
            if (permissionsList.contains(TeamTypeEnum.TALK_TEAM.getValue())) {
                talkPer = TeamStatusEnum.VALID.getValue();
            }
            organization.setLivePermissions(livePer);
            organization.setQuliaoPermissions(callPer);
            organization.setChatRoomPermissions(talkPer);
            organization.setBusinessPerson(operationSpecialistId);
            organization.setApplyStatus(ApplyStatusEnum.AUDITING.ordinal());
            organization.setModifyStatus(ModifyStatusEnum.SUBMIT.ordinal());
        }
        organizationMapper.updateByPrimaryKeySelective(organization);

        if (applyStatus == ApplyStatusEnum.SUCCESS.getValue()) {
            // 审核通过发起飞书流程审核
            feiShuService.approvalAdd(organization, operationSpecialistId);
        }
    }

    @Override
    public void rejectOrg(String auditMsg, Organization organization) {
        checkChangeApplyStatusParams(auditMsg);
        afterRejected(organization, auditMsg, organization.getAuditPerson());
        ApplyTypeLog applyTypeLog = new ApplyTypeLog();
        applyTypeLog.setCreateTime(DateUtil.currentTimeSeconds());
        applyTypeLog.setOrgId(organization.getOrgId().intValue());
        applyTypeLog.setOperatorType(ApplyStatusEnum.FAILED.getValue());
        applyTypeLog.setOperator(organization.getAuditPerson());
        applyTypeLog.setMsg(auditMsg);
        applyTypeLogMapper.insertSelective(applyTypeLog);
        accountLoginService.refreshAccount(organization.getAccountUuid());
        organization.setApplyStatus(ApplyStatusEnum.FAILED.ordinal());
        organizationMapper.updateByPrimaryKeySelective(organization);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void passOrg(Organization organization, FeishuAuditRelation relation) {
        this.afterReviewing(organization, organization.getAuditPerson());
        organization.setApplyStatus(ApplyStatusEnum.SUCCESS.ordinal());
        organization.setOrgStatus(OrgStatusEnum.OPEN.getValue());
        organization.setAuditMsg("");
        organization.setJoinTime(DateUtil.currentTimeSeconds());

        try {
            TeamDto teamDto = new TeamDto();
            teamDto.setOrgId(organization.getOrgId());
            teamDto.setCreateBy(organization.getAuditPerson());
            Map<Integer, Integer> mapType = new HashMap<>();
            mapType.put(TeamTypeEnum.LIVE_TEAM.getValue(), organization.getLivePermissions());
            mapType.put(TeamTypeEnum.CALL_TEAM.getValue(), organization.getQuliaoPermissions());
            mapType.put(TeamTypeEnum.TALK_TEAM.getValue(), organization.getChatRoomPermissions());
            teamDto.setTypeMap(mapType);
            teamService.insertDefaultTeam(teamDto);
        } catch (ServiceException e) {
            log.warn("机构审核通过，创建默认团队失败，机构id：" + organization.getOrgId(), e);
        }

        organization.setJoinTime(DateUtil.currentTimeSeconds());
        ApplyTypeLog applyTypeLog = new ApplyTypeLog();
        applyTypeLog.setCreateTime(DateUtil.currentTimeSeconds());
        applyTypeLog.setOrgId(organization.getOrgId().intValue());
        if (StringUtils.isEmpty(organization.getContactPhone())) {
            organization.setContactPhone(organization.getChargePersonPhone());
        }
        applyTypeLog.setOperatorType(ApplyStatusEnum.SUCCESS.getValue());
        applyTypeLog.setOperator(organization.getAuditPerson());
        applyTypeLog.setMsg("");
        organizationMapper.updateByPrimaryKeySelective(organization);
        applyTypeLogMapper.insertSelective(applyTypeLog);
        accountLoginService.refreshAccount(organization.getAccountUuid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrgInfo(String paramJson, String operator) {
        OrganizationVo vo = JSON.parseObject(paramJson, new TypeReference<OrganizationVo>() {
        });
        Organization organization = organizationMapper.selectByPrimaryKey(vo.getOrgId());
        Organization oldorganization = new Organization();
        Organization orgNameCheck = new Organization();
        orgNameCheck.setOrgName(vo.getOrgName());
        List<Organization> orgNameCheckList = organizationMapper.getByList(orgNameCheck);
        if (CollectionUtils.isNotEmpty(orgNameCheckList)) {
            long count = orgNameCheckList.stream().filter(item -> !item.getOrgId().equals(organization.getOrgId())).count();
            if (count > 0) {
                throw new ServiceException("error", "机构名称已存在！");
            }
        }
        checkupdateProperties(vo);
        BeanUtils.copyProperties(organization, oldorganization);
        organization.setOrgName(vo.getOrgName());
        teamEmployeeMapper.updateOrgName(vo.getOrgName(), vo.getOrgId());
        sysUserMapper.updateOrgName(vo.getOrgName(), vo.getOrgId());
        organization.setChargePersonPhone(vo.getChargePersonPhone());
        organization.setChargePersonEmail(vo.getChargePersonEmail());
        organization.setChargePerson(vo.getChargePerson());
        organization.setChargePersonVx(vo.getChargePersonVx());
        organization.setChargePersonBirthday(vo.getChargePersonBirthday());
        organization.setChargePersonIdCard(vo.getChargePersonIdCard());
        organization.setReceivingAddress(vo.getReceivingAddress());
        organization.setLegalPerson(vo.getLegalPerson());
        organization.setLegalPersonIdCard(vo.getLegalPersonIdCard());
        organization.setPublicReceivingBankAccount(vo.getPublicReceivingBankAccount());
        organization.setAccountName(vo.getAccountName());
        organization.setAccountBankName(vo.getAccountBankName());
        organization.setProvince(vo.getProvince());
        organization.setProvinceId(vo.getProvinceId());
        organization.setCity(vo.getCity());
        organization.setCityId(vo.getCityId());
        organization.setSubBranchName(vo.getSubBranchName());
        organization.setBusinessPerson(vo.getBusinessPerson());
        organization.setUpdateTime(DateUtil.currentTimeSeconds());
        organization.setSocialUnifiedCreditCode(vo.getSocialUnifiedCreditCode());
        organization.setEnterpriseName(vo.getEnterpriseName());
        organization.setLegalPersonPhone(vo.getLegalPersonPhone());
        organization.setPremises(vo.getPremises());
        organization.setContactPhone(vo.getContactPhone());
        organization.setLiveSettlementeType(vo.getLiveSettlementeType());
        organizationMapper.updateByPrimaryKeySelective(organization);
        saveBusinessLicense(organization.getOrgUuid(), organization.getBusinessLicenseUrl());
        saveOpeningPermit(vo, organization.getOrgUuid());
        saveLegalPerson(organization.getOrgUuid(), organization.getLegalPersonUrl());
        saveChargePerson(organization.getOrgUuid(), organization.getChargePersonUrl());
        saveOrgCooperationFlow(organization.getOrgUuid(), organization.getOrgCooperationFlowUrl());
        saveHostScreenshot(organization.getOrgUuid(), organization.getHostScreenshotUrl());
        List<SysUser> sysUsers = sysUserService.selectUserListByMobile(organization.getChargePersonPhone());
        if (CollectionUtils.isNotEmpty(sysUsers) && sysUsers.size() > 1) {
            log.warn("异常手机号：{}", organization.getChargePersonPhone());
            throw new ServiceException("error", "该机构负责人手机号信息异常。");
        } else if (CollectionUtils.isNotEmpty(sysUsers) && sysUsers.size() == 1) {
            SysUser sysUser = sysUsers.get(0);
            sysUser.setOrgName(vo.getOrgName());
            sysUserMapper.updateById(sysUser);
        } else {
            throw new ServiceException("error", "该机构负责人手机号是未注册用户。");
        }
        //设置修改日志信息
        saveOperatorLog(OperatorTypeEnum.UPDATE_GUILD, null, JSON.toJSONString(oldorganization), JSON.toJSONString(organization), operator, null);
    }

    @Override
    public void modifyOrgInfo(Organization vo, String operator) {
        Organization organization = organizationMapper.selectByPrimaryKey(vo.getOrgId());
        if (StringUtils.isNotEmpty(vo.getOrgName())) {
            // 修改机构名称时，校验机构名称是否重复
            Organization orgNameCheck = new Organization();
            orgNameCheck.setOrgName(vo.getOrgName());
            List<Organization> orgNameCheckList = organizationMapper.getByList(orgNameCheck);
            if (CollectionUtils.isNotEmpty(orgNameCheckList)) {
                long count = orgNameCheckList.stream().filter(item -> !item.getOrgId().equals(organization.getOrgId())).count();
                if (count > 0) {
                    throw new ServiceException("error", "机构名称已存在！");
                }
            }
        }
        Organization oldorganization = new Organization();
        BeanUtils.copyProperties(organization, oldorganization);

        if(vo.getOrgName()!=null){
            organization.setOrgName(vo.getOrgName());
            teamEmployeeMapper.updateOrgName(vo.getOrgName(), vo.getOrgId());
            sysUserMapper.updateOrgName(vo.getOrgName(), vo.getOrgId());
        }

        organization.setChargePersonPhone(vo.getChargePersonPhone());
        organization.setChargePersonEmail(vo.getChargePersonEmail());
        organization.setChargePerson(vo.getChargePerson());
        organization.setChargePersonVx(vo.getChargePersonVx());
        organization.setChargePersonBirthday(vo.getChargePersonBirthday());
        organization.setChargePersonIdCard(vo.getChargePersonIdCard());
        organization.setReceivingAddress(vo.getReceivingAddress());
        organization.setLegalPerson(vo.getLegalPerson());
        organization.setLegalPersonIdCard(vo.getLegalPersonIdCard());
        organization.setPublicReceivingBankAccount(vo.getPublicReceivingBankAccount());
        organization.setAccountName(vo.getAccountName());
        organization.setAccountBankName(vo.getAccountBankName());
        organization.setProvince(vo.getProvince());
        organization.setProvinceId(vo.getProvinceId());
        organization.setCity(vo.getCity());
        organization.setCityId(vo.getCityId());
        organization.setSubBranchName(vo.getSubBranchName());
        organization.setBusinessPerson(vo.getBusinessPerson());
        organization.setUpdateTime(DateUtil.currentTimeSeconds());
        organization.setSocialUnifiedCreditCode(vo.getSocialUnifiedCreditCode());
        organization.setEnterpriseName(vo.getEnterpriseName());
        organization.setLegalPersonPhone(vo.getLegalPersonPhone());
        organization.setPremises(vo.getPremises());
        organization.setContactPhone(vo.getContactPhone());
        organization.setLiveSettlementeType(vo.getLiveSettlementeType());
        organizationMapper.updateByPrimaryKeySelective(organization);
        //设置修改日志信息
        saveOperatorLog(OperatorTypeEnum.UPDATE_GUILD, null, JSON.toJSONString(oldorganization), JSON.toJSONString(organization), operator, null);
        //记录日志
        organizationLogService.addLog(vo,oldorganization,operator);

    }

    /**
     * 检查参数有效性
     *
     * @param vo
     */
    public void checkupdateProperties(OrganizationVo vo) {
        //1.机构信息
        if (org.apache.commons.lang3.StringUtils.isBlank(vo.getOrgName())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "机构名称不能为空");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(vo.getChargePersonPhone())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "绑定登录手机号不能为空");
        }
        if (!ParamsCheckUtil.isMobile(vo.getChargePersonPhone())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人联系电话格式错误");
        }
        if (!ParamsCheckUtil.isEmail(vo.getChargePersonEmail()) && !org.apache.commons.lang3.StringUtils.isBlank(vo.getChargePersonEmail())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "负责人邮箱格式错误");
        }
        if (!ParamsCheckUtil.isLegalPattern(vo.getChargePersonIdCard()) && !org.apache.commons.lang3.StringUtils.isBlank(vo.getChargePersonIdCard())) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "身份证号码格式错误");
        }
    }


    public void bindMobile(String orgId, String mobile, String vCode, String operator) {
        if (StringUtils.isEmpty(mobile)) {
            throw new ServiceException("mobile_empty", "换绑的手机号为空");
        }
        if (StringUtils.isEmpty(vCode)) {
            throw new ServiceException("vCode_empty", "验证码为空");
        }
        if (StringUtils.isEmpty(orgId)) {
            throw new ServiceException("orgId_empty", "传入机构id为空");
        }
        verifyService.verifyCommon(VerifyCodeEnum.REBIND_ORG.getCode(), mobile, vCode);
        doBindMobile(orgId, mobile, operator);
    }

    @Override
    public void bindMobile2(String orgId, String mobile, String vCode, String operator,String oldMobile) {
        if (StringUtils.isEmpty(mobile)) {
            throw new ServiceException("mobile_empty", "换绑的手机号为空");
        }
        if (StringUtils.isEmpty(vCode)) {
            throw new ServiceException("vCode_empty", "验证码为空");
        }
        if (StringUtils.isEmpty(orgId)) {
            throw new ServiceException("orgId_empty", "传入机构id为空");
        }
        verifyService.verifyCommon(VerifyCodeEnum.REBIND_ORG.getCode(), mobile, vCode);
        doBindMobile2(orgId, mobile, operator,oldMobile);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doBindMobile(String orgId, String mobile, String operator) {
        String oldMpbile = "";
        Organization oldOrganization = organizationMapper.selectByPrimaryKey(Long.parseLong(orgId));
        List<SysUser> oldUserPhone = new ArrayList<>();
        List<SysUser> oldUserPhoneStatus = new ArrayList<>();
        if (StringUtils.isNotEmpty(oldOrganization.getChargePersonPhone())) {
            oldUserPhone = sysUserService.selectUserListByMobile(oldOrganization.getChargePersonPhone());
            if (CollectionUtils.isNotEmpty(oldUserPhone)) {
                oldUserPhone.forEach(oldUserPhone1 -> {
                    if (oldUserPhone1.getStatus().equals(UserStatus.OK.getCode())) {
                        oldUserPhoneStatus.add(oldUserPhone1);
                    }
                });
            }
        }
        if (StringUtils.isNotEmpty(oldOrganization.getChargePersonPhone()) && oldUserPhone.size() == 1) {
            oldMpbile = oldOrganization.getChargePersonPhone();
        } else if ((CollectionUtils.isNotEmpty(oldUserPhone) && oldUserPhone.size() == 1) || (CollectionUtils.isNotEmpty(oldUserPhoneStatus) && oldUserPhoneStatus.size() == 1)) {
            oldMpbile = oldUserPhone.get(0).getMobile();
        } else if (CollectionUtils.isNotEmpty(oldUserPhone) && oldUserPhone.size() > 1 && CollectionUtils.isNotEmpty(oldUserPhoneStatus) && oldUserPhoneStatus.size() > 1) {
            log.warn("错误手机号信息：{}------oldUserPhoneStatus{}，大小{}", JsonUtils.objectToString(oldUserPhone), JsonUtils.objectToString(oldUserPhoneStatus), oldUserPhone.size());
            throw new ServiceException("error", "该机构绑定手机号信息异常。");
        } else if (StringUtils.isEmpty(oldOrganization.getChargePersonPhone())) {
            oldMpbile = "";
        }
        //判断旧公会是否有绑定登录手机号,如果就手机号不为空
        if (StringUtils.isNotBlank(oldMpbile)) {
            //机构已经绑定手机号uuid一致,无需修改
            if (oldMpbile.equals(mobile)) {
                return;
            }
            SysUser sysUser = new SysUser();
            if (CollectionUtils.isNotEmpty(oldUserPhone) && oldUserPhone.size() == 1) {
                sysUser = oldUserPhone.get(0);
            }
            if (CollectionUtils.isNotEmpty(oldUserPhoneStatus) && oldUserPhoneStatus.size() == 1) {
                sysUser = oldUserPhoneStatus.get(0);
            }
            //检测新手机是否有存在多账号问题
            List<SysUser> newSysUser = sysUserService.selectUserListByMobile(mobile);
            List<SysUser> newSysUserStatus = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(newSysUser)) {
                newSysUser.forEach(sysUser1 -> {
                    if (sysUser1.getStatus().equals(UserStatus.OK.getCode())) {
                        newSysUserStatus.add(sysUser1);
                    }
                });
                if (newSysUser.size() == 1) {
                    if (newSysUser != null && newSysUser.get(0).getOrgId() != null && newSysUser.get(0).getOrgId().equals(oldOrganization.getOrgId())) {
                        if (!newSysUser.get(0).getOrgId().equals(0L)) {
                            throw new ServiceException("org_has_bind", "该手机号已经是该机构的成员，不能换绑");
                        }
                    }
                    if (newSysUser != null && newSysUser.get(0).getOrgId() != null) {
                        if (!newSysUser.get(0).getOrgId().equals(0L)) {
                            throw new ServiceException("org_has_bind", "该手机号已经绑定其他机构，不能换绑");
                        }
                    }
                    if (newSysUser != null) {
                        if (newSysUser.get(0).getOrgId() == null || newSysUser.get(0).getOrgId().equals(0L)) {
                            sysUserMapper.deleteById(newSysUser.get(0).getUserId());
                        }
                    }
                }
                if (newSysUser.size() > 1 && CollectionUtils.isNotEmpty(newSysUserStatus)) {
                    if (newSysUserStatus.size() > 1) {
                        log.warn("换绑异常手机号：{}", mobile);
                        throw new ServiceException("error", "该换绑手机号信息异常。");
                    }
                }
            }
            if (sysUser == null) {
                SysUser userMobileSys = sysUserService.registerAccountByChangeUser(mobile, Long.parseLong(orgId), oldOrganization.getOrgName());
                SysUserRole sysUserRole = new SysUserRole();
                QueryWrapper queryRoleId = new QueryWrapper();
                queryRoleId.eq("role_key", UserTypeEnum.MANAGER.getCode());
                SysRole sysRole = sysRoleMapper.selectOne(queryRoleId);
                sysUserRole.setRoleId(sysRole.getRoleId());
                sysUserRole.setUserId(userMobileSys.getUserId());
                sysUserRoleMapper.insert(sysUserRole);
            } else {
                sysUser.setMobile(mobile);
                sysUserMapper.updateById(sysUser);
            }
        } else {
            //判断旧公会是否有绑定登录手机号,如果就手机号为空
            //检测新手机是否有存在多账号问题
            SysUser newSysUser = new SysUser();
            List<SysUser> newSysUsers = sysUserService.selectUserListByMobile(mobile);
            if (CollectionUtils.isEmpty(newSysUsers)) {
                SysUser userMobileSys = sysUserService.registerAccountByChangeUser(mobile, Long.parseLong(orgId), oldOrganization.getOrgName());
                QueryWrapper queryRoleId = new QueryWrapper();
                queryRoleId.eq("role_key", UserTypeEnum.MANAGER.getCode());
                SysRole sysRole = sysRoleMapper.selectOne(queryRoleId);
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setRoleId(sysRole.getRoleId());
                sysUserRole.setUserId(userMobileSys.getUserId());
                sysUserRoleMapper.insert(sysUserRole);
            } else {
                if (newSysUsers.size() > 1) {
                    log.warn("换绑异常手机号：{}", mobile);
                    throw new ServiceException("error", "该换绑手机号信息异常。");
                }
                if (newSysUsers.size() == 1) {
                    newSysUser = newSysUsers.get(0);
                    if (newSysUser != null && newSysUser.getOrgId() != null && newSysUser.getOrgId().equals(oldOrganization.getOrgId())) {
                        if (!newSysUser.getOrgId().equals(0L)) {
                            throw new ServiceException("org_has_bind", "该手机号已经是该机构的成员，不能换绑");
                        }
                    }
                    if (newSysUser != null && newSysUser.getOrgId() != null) {
                        if (!newSysUser.getOrgId().equals(0L)) {
                            throw new ServiceException("org_has_bind", "该手机号已经绑定其他机构，不能换绑");
                        }
                    }
                }
                newSysUser.setOrgId(Long.parseLong(orgId));
                newSysUser.setOrgName(oldOrganization.getOrgName());
                newSysUser.setUserType(UserTypeEnum.MANAGER.getType());
                sysUserMapper.updateById(newSysUser);
                QueryWrapper queryRoleId = new QueryWrapper();
                queryRoleId.eq("role_key", UserTypeEnum.MANAGER.getCode());
                SysRole sysRole = sysRoleMapper.selectOne(queryRoleId);
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setRoleId(sysRole.getRoleId());
                sysUserRole.setUserId(newSysUser.getUserId());
                sysUserRoleMapper.insert(sysUserRole);
            }
        }
        oldOrganization.setChargePersonPhone(mobile);
        organizationMapper.updateByPrimaryKey(oldOrganization);
        saveOperatorLog(OperatorTypeEnum.REBIND_MOBILE, oldOrganization, oldMpbile, mobile, operator, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doBindMobile2(String orgId, String mobile, String operator,String oldMobile) {
        String oldMpbile = "";
        Organization oldOrganization = organizationMapper.selectByPrimaryKey(Long.parseLong(orgId));
        if(oldOrganization == null){
            log.warn("orgId={},由于不存在机构信息,所以换绑定失败。oldOrganization={}",orgId,JSON.toJSONString(oldOrganization));
            throw new ServiceException("error", "由于不存在机构信息,所以换绑定失败。");
        }
        //检测新手机是否有存在多账号问题
        List<SysUser> newSysUser = sysUserService.selectUserListByMobile2(mobile);
        if(!newSysUser.isEmpty()){
            log.warn("mobile={},由于已经存在用户信息,所以换绑定失败。newSysUser={}",mobile,JSON.toJSONString(newSysUser));
            throw new ServiceException("error", "由于已经存在用户信息,所以换绑定失败。");
        }

        List<SysUser> sysUserOldList = sysUserService.selectUserListByMobile2(oldMobile);
        if(sysUserOldList.isEmpty()){
            log.warn("oldMobile={},由于不存在用户信息,所以换绑定失败。sysUserOldList={}",oldMobile,JSON.toJSONString(sysUserOldList));
            throw new ServiceException("error", "由于不存在用户信息,所以换绑定失败。");
        }
        if(sysUserOldList.size()>1){
            log.warn("oldMobile={},由于已经存在多条用户信息,所以换绑定失败。sysUserOldList={}",oldMobile,JSON.toJSONString(sysUserOldList));
            throw new ServiceException("error", "由于已经存在多条用户信息,所以换绑定失败。");
        }


        SysUser userOld = sysUserOldList.get(0);
        log.info("userOld={}", JSON.toJSONString(userOld));
        SysUser sysUser = new SysUser();
        sysUser.setAccountUuid(sysUserService.genUniqueUuid());
        sysUser.setOrgId(Long.valueOf(orgId));
        sysUser.setUserName(userOld.getUserName());
        sysUser.setUserType(userOld.getUserType());
        sysUser.setStatus("1");
        sysUser.setMobile(mobile);
        sysUser.setCreateTime(DateUtil.currentTimeSeconds());
        sysUser.setUpdateTime(DateUtil.currentTimeSeconds());
        sysUser.setOrgName(oldOrganization.getOrgName());
        sysUserService.save(sysUser);

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("user_id", userOld.getUserId());
        sysUserMapper.delete(queryWrapper);


        List<SysUserRole> sysUserRoleListOld = sysUserRoleMapper.getListByUserId(userOld.getUserId());
        log.info("sysUserRoleListOld={}",JSON.toJSONString(sysUserRoleListOld));
        for(SysUserRole sysUserRole : sysUserRoleListOld){
            sysUserRole.setRoleId(sysUserRole.getRoleId());
            sysUserRole.setUserId(sysUser.getUserId());
            sysUserRoleMapper.insert(sysUserRole);
        }
        queryWrapper.clear();
        queryWrapper.eq("user_id",userOld.getUserId());
        sysUserRoleMapper.delete(queryWrapper);


        oldOrganization.setChargePersonPhone(mobile);
        organizationMapper.updateByPrimaryKey(oldOrganization);

        saveOperatorLog(OperatorTypeEnum.REBIND_MOBILE, oldOrganization, oldMpbile, mobile, operator, null);
    }
    /**
     * 审核通过的工会信息
     *
     * @return
     */
    public List<Organization> findPassList() {
        OrganizationInfoSearch organizationInfoSearch = new OrganizationInfoSearch();
        organizationInfoSearch.setApplyStatus(ApplyStatusEnum.SUCCESS.getValue());
        organizationInfoSearch.setSelectByDigest(EncryptSwitchConfig.selectByDigest);
        List<Organization> list = organizationMapper.findOrgInfoPageList(organizationInfoSearch);
        return list;
    }

    public List<CommonSelect> findGuildInfoUuidAndNameList() {
        List<Organization> organizations = organizationMapper.findGuildInfoUuidAndNameList();
        List<CommonSelect> selectVoList = new ArrayList<>();
        organizations.stream().forEach(organization -> {
            CommonSelect vo = new CommonSelect();
            vo.setLabel(organization.getOrgName());
            vo.setValue(organization.getOrgId());
            selectVoList.add(vo);
        });
        return selectVoList;
    }

    @Override
    public List<CommonSelect> findOrgUuidAndNameList() {
        List<Organization> organizations = organizationMapper.findOrgUuidAndNameTree();
        List<CommonSelect> selectVoList = new ArrayList<>();
        organizations.stream().forEach(organization -> {
            CommonSelect vo = new CommonSelect();
            vo.setLabel(organization.getOrgName());
            vo.setValue(Long.valueOf(organization.getOrgUuid()));
            selectVoList.add(vo);
        });
        return selectVoList;
    }

    /**
     * 保存机构外站流水截图
     *
     * @param orgCooperationFlowUrl
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrgCooperationFlow(String orgUuid, String orgCooperationFlowUrl) {
        orgCooperationFlowMapper.deleteByUuid(orgUuid);
        if (org.springframework.util.StringUtils.hasText(orgCooperationFlowUrl)) {
            String[] urls = orgCooperationFlowUrl.split(",");
            Arrays.stream(urls).forEach(url -> {
                OrgCooperationFlow orgCooperationFlow = new OrgCooperationFlow();
                orgCooperationFlow.setOrgUuid(orgUuid);
                orgCooperationFlow.setUrl(url);
                orgCooperationFlow.setCreateTime(DateUtil.currentTimeSeconds());
                orgCooperationFlow.setUpdateTime(DateUtil.currentTimeSeconds());
                orgCooperationFlowMapper.insert(orgCooperationFlow);
            });
        }

    }

    /**
     * 保存主播截图
     *
     * @param hostScreenshotUrl
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveHostScreenshot(String orgUuid, String hostScreenshotUrl) {
        hostScreenshotMapper.deleteByUuid(orgUuid);
        if (org.springframework.util.StringUtils.hasText(hostScreenshotUrl)) {
            String[] urls = hostScreenshotUrl.split(",");
            Arrays.stream(urls).forEach(url -> {
                HostScreenshot hostScreenshot = new HostScreenshot();
                hostScreenshot.setOrgUuid(orgUuid);
                hostScreenshot.setUrl(url);
                hostScreenshot.setCreateTime(DateUtil.currentTimeSeconds());
                hostScreenshot.setUpdateTime(DateUtil.currentTimeSeconds());
                hostScreenshotMapper.insert(hostScreenshot);
            });
        }
    }

    /**
     * 保存营业执照
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBusinessLicense(String orgUuid, String businessLicenseUrl) {
        businessLicenseMapper.deleteByUuid(orgUuid);
        if (org.springframework.util.StringUtils.hasText(businessLicenseUrl)) {
            String[] urls = businessLicenseUrl.split(",");
            Arrays.stream(urls).forEach(url -> {
                BusinessLicense businessLicense = new BusinessLicense();
                businessLicense.setOrgUuid(orgUuid);
                businessLicense.setUrl(url);
                businessLicense.setCreateTime(DateUtil.currentTimeSeconds());
                businessLicense.setUpdateTime(DateUtil.currentTimeSeconds());
                businessLicenseMapper.insert(businessLicense);
            });
        }


    }


    /**
     * 保存开户许可证
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOpeningPermit(OrganizationVo vo, String orgUuid) {
        openingPermitMapper.deleteByUuid(orgUuid);
        if (org.springframework.util.StringUtils.hasText(vo.getOpeningPermitUrl())) {
            String[] urls = vo.getOpeningPermitUrl().split(",");
            Arrays.stream(urls).forEach(url -> {
                OpeningPermit openingPermit = new OpeningPermit();
                openingPermit.setOrgUuid(orgUuid);
                openingPermit.setUrl(url);
                openingPermit.setCreateTime(DateUtil.currentTimeSeconds());
                openingPermit.setUpdateTime(DateUtil.currentTimeSeconds());
                openingPermitMapper.insert(openingPermit);
            });
        }
    }

    /**
     * 保存法人信息身份证
     *
     * @param legalPersonUrl
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLegalPerson(String orgUuid, String legalPersonUrl) {
        legalPersonMapper.deleteByUuid(orgUuid);
        if (org.springframework.util.StringUtils.hasText(legalPersonUrl)) {
            String[] urls = legalPersonUrl.split(",");
            int i = 1;
            for (String url : urls) {
                LegalPerson legalPerson = new LegalPerson();
                legalPerson.setType(i++);
                legalPerson.setOrgUuid(orgUuid);
                legalPerson.setUrl(url);
                legalPerson.setCreateTime(DateUtil.currentTimeSeconds());
                legalPerson.setUpdateTime(DateUtil.currentTimeSeconds());
                legalPersonMapper.insert(legalPerson);
            }
        }
    }

    /**
     * 保存负责人信息身份证
     *
     * @param chargePersonUrl
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveChargePerson(String orgUuid, String chargePersonUrl) {
        //删除负责人的身份证信息图片
        chargePersonMapper.deleteByUuid(orgUuid);
        if (org.springframework.util.StringUtils.hasText(orgUuid)) {
            String[] urls = chargePersonUrl.split(",");
            int i = 1;
            for (String url : urls) {
                ChargePerson chargePerson = new ChargePerson();
                chargePerson.setType(i++);
                chargePerson.setOrgUuid(orgUuid);
                chargePerson.setUrl(url);
                chargePerson.setCreateTime(DateUtil.currentTimeSeconds());
                chargePerson.setUpdateTime(DateUtil.currentTimeSeconds());
                chargePersonMapper.insert(chargePerson);
            }
        }
    }


    /**
     * 通过后的操作
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized void afterReviewing(Organization organization, String operator) {
        sendPassMsg(organization, null, "gonghui_relational_notice");
        QueryWrapper queryWrapperUser = new QueryWrapper();
        queryWrapperUser.eq("account_uuid", organization.getAccountUuid());
        SysUser sysUser = sysUserMapper.selectOne(queryWrapperUser);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("role_key", UserTypeEnum.MANAGER.getCode());
        //tpye類型：1直播，2趣聊 3聊天室
        queryWrapper.eq("type", 1);
        SysRole sysRole = sysRoleMapper.selectOne(queryWrapper);
        // 先删掉再新增，免得新增太多
        sysUserRoleMapper.deleteUserIdAndRoleId(sysUser.getUserId(), sysRole.getRoleId());
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setRoleId(sysRole.getRoleId());
        sysUserRole.setUserId(sysUser.getUserId());
        sysUserRoleMapper.insert(sysUserRole);
        saveOperatorLog(OperatorTypeEnum.PASS_GUILD, organization, null, null, operator, null);
    }

    @Autowired
    private IOperatorLogService iOperatorLogService;
    public void saveOperatorLog(OperatorTypeEnum operatorTypeEnum, Organization info, String oldContent, String newContent, String operator, String auditMsg) {
        OperatorLog operatorLog = new OperatorLog();

        //更新机构
        if (operatorTypeEnum.getValue() == OperatorTypeEnum.UPDATE_GUILD.getValue().intValue()) {
            operatorLog.setOldContent(oldContent);
            operatorLog.setNewContent(newContent);
            operatorLog.setType(OperatorTypeEnum.UPDATE_GUILD.getValue().intValue());
            operatorLog.setCreateTime(DateUtil.currentTimeSeconds());
            //开启机构
        } else if (operatorTypeEnum.getValue().intValue() == OperatorTypeEnum.OPEN_GUILD.getValue().intValue()) {
            operatorLog.setOldContent(JSON.toJSONString(info));
            operatorLog.setNewContent(newContent);
            operatorLog.setType(OperatorTypeEnum.OPEN_GUILD.getValue().intValue());
            operatorLog.setCreateTime(DateUtil.currentTimeSeconds());
            operatorLog.setOrgName(info.getOrgName());
            //关闭机构
        } else if (operatorTypeEnum.getValue().intValue() == OperatorTypeEnum.CLOSE_GUILD.getValue().intValue()) {
            operatorLog.setOldContent(JSON.toJSONString(info));
            operatorLog.setNewContent(newContent);
            operatorLog.setType(OperatorTypeEnum.CLOSE_GUILD.getValue().intValue());
            operatorLog.setCreateTime(DateUtil.currentTimeSeconds());
            operatorLog.setOrgName(info.getOrgName());
            //通过机构
        } else if (operatorTypeEnum.getValue().intValue() == OperatorTypeEnum.PASS_GUILD.getValue().intValue()) {
            operatorLog.setOldContent(JSON.toJSONString(info));
            operatorLog.setType(OperatorTypeEnum.PASS_GUILD.getValue().intValue());
            operatorLog.setOldContent(JSON.toJSONString(info));
            operatorLog.setOrgName(info.getOrgName());
            operatorLog.setChargePersonPhone(info.getChargePersonPhone());
            operatorLog.setCreateTime(DateUtil.currentTimeSeconds());
            //拒绝机构
        } else if (operatorTypeEnum.getValue().intValue() == OperatorTypeEnum.REJECT_GUILD.getValue().intValue()) {
            operatorLog.setType(OperatorTypeEnum.REJECT_GUILD.getValue().intValue());
            operatorLog.setAuditMsg(auditMsg);
            operatorLog.setOldContent(JSON.toJSONString(info));
            operatorLog.setOrgName(info.getOrgName());
            operatorLog.setChargePersonPhone(info.getChargePersonPhone());
            operatorLog.setCreateTime(DateUtil.currentTimeSeconds());
            //重新绑定手机号
        } else if (operatorTypeEnum.getValue().intValue() == OperatorTypeEnum.REBIND_MOBILE.getValue().intValue()) {
            operatorLog.setType(OperatorTypeEnum.REBIND_MOBILE.getValue().intValue());
            operatorLog.setAuditMsg(auditMsg);
            operatorLog.setOldContent(oldContent);
            operatorLog.setNewContent(newContent);
            operatorLog.setOrgName(info.getOrgName());
            operatorLog.setChargePersonPhone("");
            operatorLog.setCreateTime(DateUtil.currentTimeSeconds());
        } else {
            // do sth
        }
        operatorLog.setOperator(operator);
        iOperatorLogService.save(operatorLog);
    }

    /**
     * 拒绝后的操作
     */
    @Transactional(rollbackFor = Exception.class)
    public void afterRejected(Organization organization, String auditMsg, String operator) {
        //step1: 发送短信
        sendMsg(organization, auditMsg);
        organization.setAuditMsg(auditMsg);
        saveOperatorLog(OperatorTypeEnum.REJECT_GUILD, organization, null, null, operator, auditMsg);
    }


    /**
     * 拒绝的时候发送短信通知
     *
     * @param organization
     * @param auditMsg
     */
    @Override
    public void sendMsg(Organization organization, String auditMsg) {
        Map<String, Object> map = new HashMap<>();
        map.put("service", "businessSms");
        map.put("method", "processBusinessSms");
        Map<String, Object> asyncforms = new HashMap<>();
        SysUser sysUser = sysUserMapper.selectOneManager(organization.getOrgId());
        if (sysUser == null) {
            throw new ServiceException("error", "此负责人手机号不是该公会超级管理员或手机号已占用。");
        }
        asyncforms.put("phone", sysUser.getMobile());
        asyncforms.put("content", "您修改的机构信息审核被拒，原因：" + auditMsg);
        asyncforms.put("tagCode", "gonghui_modify_notice");
        asyncforms.put("appcode", 1);
        asyncforms.put("cloned", 1);
        map.put("asyncforms", asyncforms);
        MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
        MqResponse mqResponse = mqClient.push("mp_sms_async_invoke_queue", map, null);
    }


    /**
     * 通过的时候发送短信通知
     *
     * @param organization
     */
    @Override
    public void sendPassMsg(Organization organization, String msg, String tagCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("service", "businessSms");
        map.put("method", "processBusinessSms");
        Map<String, Object> asyncforms = new HashMap<>();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("org_id", organization.getOrgId());
        ;
        queryWrapper.eq("user_type", UserTypeEnum.MANAGER.getType());
        List list = sysUserMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("error", "此负责人手机号不是该公会超级管理员或手机号已占用。");
        }
        SysUser sysUser = (SysUser) list.get(list.size() - 1);
        asyncforms.put("phone", sysUser.getMobile());
        String content = "尊敬的「" + organization.getOrgName() + "」，欢迎您加入他趣公会，登陆地址为:" + smsUrl;
        if (StringUtils.isNotEmpty(msg)) {
            content = msg;
        }
        asyncforms.put("content", content);
        asyncforms.put("tagCode", tagCode);
        asyncforms.put("appcode", 1);
        asyncforms.put("cloned", 1);
        map.put("asyncforms", asyncforms);
        MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
        mqClient.push("mp_sms_async_invoke_queue", map, null);

    }

    public void checkChangeApplyStatusParams(String auditMsg) {
        if (StringUtils.isEmpty(auditMsg)) {
            throw new ServiceException("auditMsg_is_null", "拒绝理由不能为空！");
        }
        if (auditMsg.length() > 100) {
            throw new ServiceException("auditMsg_length_error", "拒绝理由长度最高为100个字！");
        }
    }

    public Organization checkOrgApply(Long id, Integer applyStatus) {
        Organization organizationSearch = new Organization();
        organizationSearch.setOrgId(id);
        organizationSearch.setAccountUuid(null);
        organizationSearch.setOrgUuid(null);
        Organization organization = organizationMapper.selectByPrimaryKey(id);
        if (null == organization) {
            throw new ServiceException(ErrorCodeStatus.GUILD_INFO_NOT_EXIST.value(), ErrorCodeStatus.GUILD_INFO_NOT_EXIST.getReasonPhrase());
        }
        //传入审核状态值不为2或者3 则为无效值
        if ((applyStatus.intValue() != ApplyStatusEnum.SUCCESS.getValue() && ApplyStatusEnum.FAILED.getValue() != 3)) {
            throw new ServiceException(ErrorCodeStatus.GUILD_AUDIT_PARAMS_ERROR.value(), ErrorCodeStatus.GUILD_AUDIT_PARAMS_ERROR.getReasonPhrase());
        }
        //机构的审核状态要为1待审核或者3审核失败 才能重新审核
        if (organization.getApplyStatus().intValue() != ApplyStatusEnum.WAIT.getValue() && ApplyStatusEnum.FAILED.getValue() != 3) {
            throw new ServiceException(ErrorCodeStatus.GUILD_APPLY_STATUS_ERROR.value(), ErrorCodeStatus.GUILD_APPLY_STATUS_ERROR.getReasonPhrase());
        }
        return organization;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> saveOrgAll(OrganizationVo vo, String operator) {
        verifyService.verifyCommon(VerifyCodeEnum.BIND_ORG.getCode(), vo.getChargePersonPhone(), vo.getVcode());
        Map<String, Object> result = new HashMap<>();
        Organization organization = setStepAllProperties(vo);
        organization.setCreateTime(DateUtil.currentTimeSeconds());
        organization.setQuliaoPermissions(TeamStatusEnum.NO_VALID.getValue());
        if (vo.getBusinessPermissions().contains(TeamTypeEnum.LIVE_TEAM.getValue())) {
            organization.setLivePermissions(TeamStatusEnum.VALID.getValue());
        } else {
            organization.setLivePermissions(TeamStatusEnum.NO_VALID.getValue());
        }
        if (vo.getBusinessPermissions().contains(TeamTypeEnum.TALK_TEAM.getValue())) {
            organization.setChatRoomPermissions(TeamStatusEnum.VALID.getValue());
        } else {
            organization.setChatRoomPermissions(TeamStatusEnum.NO_VALID.getValue());
        }
        organizationMapper.insert(organization);
        Organization organizations = organizationMapper.getByUuid(organization.getOrgUuid());
        TeamDto teamDto = new TeamDto();
        teamDto.setOrgId(organizations.getOrgId());
        teamDto.setCreateBy(operator);
        Integer livePer = TeamStatusEnum.NO_VALID.getValue();
        Integer callPer = TeamStatusEnum.NO_VALID.getValue();
        Integer talkPer = TeamStatusEnum.NO_VALID.getValue();
        if (vo.getBusinessPermissions().contains(TeamTypeEnum.LIVE_TEAM.getValue())) {
            livePer = TeamStatusEnum.VALID.getValue();
        }
        if (vo.getBusinessPermissions().contains(TeamTypeEnum.CALL_TEAM.getValue())) {
            callPer = TeamStatusEnum.VALID.getValue();
        }
        if (vo.getBusinessPermissions().contains(TeamTypeEnum.TALK_TEAM.getValue())) {
            talkPer = TeamStatusEnum.VALID.getValue();
        }
        Map<Integer, Integer> mapType = new HashMap<>();
        mapType.put(TeamTypeEnum.LIVE_TEAM.getValue(), livePer);
        mapType.put(TeamTypeEnum.CALL_TEAM.getValue(), callPer);
        mapType.put(TeamTypeEnum.TALK_TEAM.getValue(), talkPer);
        teamDto.setTypeMap(mapType);
        teamService.insertDefaultTeam(teamDto);
        result.put("uuid", organization.getOrgUuid());
        SysUser sysUser = sysUserMapper.selectUserByMobile(vo.getChargePersonPhone(),EncryptSwitchConfig.selectByDigest);
        if (sysUser != null) {
            sysUser.setUserType(UserTypeEnum.MANAGER.getType());
            sysUser.setOrgId(organizations.getOrgId());
            sysUser.setOrgName(organizations.getOrgName());
            sysUser.setUpdateTime(DateUtil.currentTimeSeconds());
            sysUserMapper.updateById(sysUser);
        } else {
            throw new ServiceException("error", "该机构负责人手机号是未注册用户。");
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Organization setStepAllProperties(OrganizationVo vo) {
        Organization organization = new Organization();
        Long maxOrgUuid = organizationMapper.getMaxOrgUuid();
        Long orgUuid = maxOrgUuid == null ? 210000L : maxOrgUuid++;
        Organization gi = organizationMapper.getByUuid(orgUuid.toString());
        //一开始设置的字段是uuid,设置为varchar类型,但是旧机构后台是以主键作为机构id,所以当时取max(uuid)的时候会出问题.要改动uuid为long类型改动较多,先简单处理一下
        while (null != gi) {
            orgUuid += 1;
            gi = organizationMapper.getByUuid(orgUuid.toString());
        }
        Organization organizationName = new Organization();
        organizationName.setOrgName(vo.getOrgName());
        Organization orgNameSelect = organizationMapper.getBy(organizationName);
        if (StringUtils.isEmpty(vo.getOrgName())) {
            throw new ServiceException("param_error", "请输入机构名称");
        }
        if (StringUtils.isEmpty(vo.getChargePerson())) {
            throw new ServiceException("param_error", "请输入机构负责人名称");
        }
        if (StringUtils.isEmpty(vo.getChargePersonPhone())) {
            throw new ServiceException("param_error", "请输入机构负责人手机");
        }
        if (orgNameSelect != null) {
            throw new ServiceException("param_error", "请输入机构名称重复，请重新命名");
        }
        SysUser sysUser = sysUserMapper.selectUserByMobile(vo.getChargePersonPhone(), EncryptSwitchConfig.selectByDigest);
        if (sysUser == null) {
            SysUser sysUserNew = new SysUser();
            String uuid = UUID.genUuid();
            sysUserNew.setAccountUuid(uuid);
            sysUserNew.setStatus(UserStatus.OK.getCode());
            sysUserNew.setCreateTime(DateUtil.currentTimeSeconds());
            sysUserNew.setUpdateTime(DateUtil.currentTimeSeconds());
            sysUserNew.setUserType(UserTypeEnum.DEFAULT.getType());
            sysUserNew.setMobile(vo.getChargePersonPhone());
            sysUserMapper.insert(sysUserNew);
            sysUser = sysUserNew;
        } else {
            if (sysUser.getOrgId() != null) {
                if (!sysUser.getOrgId().equals(0L)) {
                    throw new ServiceException("param_error", "该手机号已经绑定过机构。");
                }
            }
            if (sysUser.getUserType() != null && !sysUser.getUserType().equals(UserTypeEnum.DEFAULT.getType())) {
                throw new ServiceException("param_error", "该手机号已经绑定过机构。");
            }
        }
        SysUserRole sysUserRole = new SysUserRole();
        QueryWrapper queryRoleId = new QueryWrapper();
        queryRoleId.eq("role_key", UserTypeEnum.MANAGER.getCode());
        SysRole sysRole = sysRoleMapper.selectOne(queryRoleId);
        sysUserRole.setRoleId(sysRole.getRoleId());
        sysUserRole.setUserId(sysUser.getUserId());
        sysUserRoleMapper.insert(sysUserRole);
        organization.setOrgUuid(orgUuid.toString());
        organization.setOrgName(vo.getOrgName());
        organization.setChargePersonPhone(vo.getChargePersonPhone());
        organization.setChargePerson(vo.getChargePerson());
        organization.setFormStatus(FormStatusEnum.SEVEN.getValue());
        organization.setApplyStatus(ApplyStatusEnum.SUCCESS.getValue());
        organization.setOrgStatus(OrgStatusEnum.OPEN.getValue());
        organization.setBusinessPerson(vo.getBusinessPerson());
        organization.setLiveSettlementeType(vo.getLiveSettlementeType());
        organization.setAccountUuid(sysUser.getAccountUuid());
        organization.setJoinTime(DateUtil.currentTimeSeconds());
        return organization;
    }

    /**
     * 机构结算类型操作
     */
    @Override
    public void liveSettlementeTypeOperate() {
        // 遍历所有机构
        OrganizationInfoSearch search = new OrganizationInfoSearch();
        search.setSelectByDigest(EncryptSwitchConfig.selectByDigest);
        List<Organization> list = organizationMapper.findPassOrgInfoPageList(search);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
            for (Organization organization : list) {
                // 判断该机构下是否存在周结的团队，如果存在至少一个，那么设置该机构结算类型为周结
                TeamSearch teamSearch = new TeamSearch();
                teamSearch.setOrgId(Integer.valueOf(organization.getOrgId().toString()));
                teamSearch.setTeamName("周结");
                List<Team> teams = teamMapper.isWeekType(teamSearch);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(teams)) {
                    organizationMapper.updateLiveSettlementeType(organization.getOrgId(), LiveSettlementeTypeEnum.WEEK.getValue());
                } else {
                    organizationMapper.updateLiveSettlementeType(organization.getOrgId(), LiveSettlementeTypeEnum.MONTH.getValue());
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void orgClose(Long orgId, String sceneId, String code) {
        if (StringUtils.isNotBlank(sceneId)) {
            Boolean verifyResult = verifyService.verifyCaptcha(sceneId, code);
            if (!verifyResult) {
                throw new ServiceException("verify_captcha_error", "验证码错误");
            }
        }
        String batchId = UUID.genUuid();
        if (orgId == null) {
            throw new ServiceException("org_not_exists", "机构不存在");
        }
        Organization org = organizationMapper.selectByPrimaryKey(orgId);
        if (org == null) {
            throw new ServiceException("org_not_exists", "机构不存在");
        }
        if (org.getLivePermissions() == 1) {
            throw new ServiceException("org_not_exists", "当前机构存在直播业务，不能关闭");
        }
        if (org.getChatRoomPermissions() == 1) {
            throw new ServiceException("org_not_exists", "当前机构存在聊天室业务，不能关闭");
        }

        List<SysUser> sysUsers = sysUserMapper.userListByOrgId(orgId);


        //从php那边拿到中转公会的团队id
        Integer teamIdTemp = hostService.getTaquConsortiaId();
        //根据对应团队查找对应的机构
        Team team = teamMapper.selectById(teamIdTemp);
        if (team == null) {
            log.warn("关闭的机构：{}-----从php获取到的中转机构团队id：{}", orgId, teamIdTemp);
            throw new ServiceException("param_error", "查询不到中转机构信息。");
        }
        List<TeamHost> liveTeamHosts = teamHostMapper.getHostListByOrgId(orgId, TeamTypeEnum.LIVE_TEAM.getValue());
        List<TeamHost> chatTeamHosts = teamHostMapper.getHostListByOrgId(orgId, TeamTypeEnum.TALK_TEAM.getValue());
        if (CollectionUtils.isNotEmpty(liveTeamHosts)) {
            throw new ServiceException("has_live_host", "当前直播业务下仍有主播，请操作转出");
        }
        if (CollectionUtils.isNotEmpty(chatTeamHosts)) {
            throw new ServiceException("has_chat_room_host", "当前聊天室业务下仍有艺人，请操作转出");
        }
        if (CollectionUtils.isNotEmpty(sysUsers)) {
            sysUsers.forEach(sysUser -> {
                sysUser.setUserType(UserTypeEnum.DEFAULT.getType());
                sysUser.setUserName("");
                sysUser.setOrgId(null);
                sysUser.setOrgName("");
                //根据userid删除userrole相关信息
                List<SysUserRole> list = sysUserRoleMapper.getListByUserId(sysUser.getUserId());
                if (CollectionUtils.isNotEmpty(list)) {
                    Set<Long> idSet = list.stream().map(SysUserRole::getId).collect(Collectors.toSet());
                    sysUserRoleMapper.deleteBatchIds(idSet);
                }
                sysUserMapper.updateByPrimaryKey(sysUser);
            });
        }
        if (CollectionUtils.isNotEmpty(liveTeamHosts)) {
            List<String> hostUuids = liveTeamHosts.stream().map(TeamHost::getHostUuid).collect(Collectors.toList());
            hostService.changeHostConsortia(String.valueOf(teamIdTemp), hostUuids);
            liveTeamHosts.forEach(teamHost -> {
                teamHost.setOrgId(team.getOrgId());
                teamHost.setEmployeeId(null);
                teamHost.setTeamId(Long.valueOf(teamIdTemp));
                teamHost.setStatus(EmployeeStatusEnum.DEPAETURE.getValue());
                teamHostMapper.updateByPrimaryKey(teamHost);
            });
        }
        List<TeamEmployee> teamEmployees = teamEmployeeMapper.getByOrgId(orgId);
        if (CollectionUtils.isNotEmpty(teamEmployees)) {
            teamEmployees.forEach(teamEmployee -> {
                teamEmployeeMapper.deleteById(teamEmployee.getEmployeeId());
            });
        }
        Organization organization = organizationMapper.selectByPrimaryKey(orgId);
        organization.setOrgStatus(OrgStatusEnum.CLOSED.getValue());
        organization.setChargePersonPhone("");
        organization.setAccountUuid("");
        organizationMapper.updateByPrimaryKey(organization);
        // 关闭后置处理
        afterCloseOrg(organization, BackstageOperateLog.OperateTypeEnum.CLOSE_ORG, SoaBaseParams.fromThread().getToken(), batchId);
    }

    private void operateLog(String uuid, Long oldValue, Long newValue) {
        Map<String, Long> agentMap = new HashMap<>();
        agentMap.put("oldValue", oldValue);
        agentMap.put("newValue", newValue);
        // 记录日志
        TeamHostOperateLog log = new TeamHostOperateLog();
        log.setHostUuid(uuid);
        log.setType(TeamHostOperateLogEnum.LIVE_CHANGE_TEAM.getValue());
        log.setCreateTime(DateUtil.currentTimeSeconds());
        log.setContent(JsonUtils.objectToString(agentMap));
        log.setOperator("");
        teamHostOperateLogMapper.insert(log);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeOrg(Long orgId) {
        Organization organization = organizationMapper.selectByPrimaryKey(orgId);
        if (organization.getApplyStatus().equals(ApplyStatusEnum.SUCCESS.getValue())) {
            throw new ServiceException("error", "通过机构不允许撤销。");
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("org_id", organization.getOrgId());
        queryWrapper.eq("account_uuid", organization.getAccountUuid());
        SysUser sysUser = sysUserMapper.selectOne(queryWrapper);
        if (sysUser != null) {
            sysUser.setOrgId((long) OrgIdEnum.ZERO.getValue());
            sysUser.setUserType(UserTypeEnum.DEFAULT.getType());
            sysUser.setOrgName("");
            sysUser.setUserName("");
            sysUserMapper.updateByPrimaryKey(sysUser);
        } else {
            throw new ServiceException("error", "该机构负责人手机号是未注册用户。");
        }
        if (StringUtils.isNotEmpty(organization.getOrgUuid())) {
            chargePersonMapper.deleteByUuid(organization.getOrgUuid());
            businessLicenseMapper.deleteByUuid(organization.getOrgUuid());
            legalPersonMapper.deleteByUuid(organization.getOrgUuid());
            hostScreenshotMapper.deleteByUuid(organization.getOrgUuid());
            orgCooperationFlowMapper.deleteByUuid(organization.getOrgUuid());
        }
        organizationMapper.deleteByPrimaryKey(orgId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeBusinessType(String sceneId, String code, Long orgId, Integer businessType, Integer permissions, Integer liveSettlementeType) {
        String batchId = java.util.UUID.randomUUID().toString().replaceAll("-", "").toLowerCase();
        String operator = RequestParams.getBaseParam().getToken();
        // 校验验证码
        //Boolean verifyCaptcha = verifyService.verifyCaptcha(sceneId, code);
        //if (!verifyCaptcha) {
        //    throw new ServiceException("verify_captcha_error", "验证码错误");
        //}

        // 验证参数
        if (Objects.isNull(orgId)) {
            throw new ServiceException("params_error", "机构id错误");
        }
        // 直播参数
        if (Objects.equals(TeamTypeEnum.LIVE_TEAM.getValue(), businessType) && Objects.equals(permissions, Constants.YES_1) && (liveSettlementeType == null || !LiveSettlementeTypeEnum.isSettlementTypeInValues(liveSettlementeType))) {
            throw new ServiceException("live_settlemente_type_error", "直播业务结算方式错误");
        }

        Organization organization = organizationMapper.selectByPrimaryKey(orgId);
        if (Objects.isNull(organization) || Objects.isNull(organization.getOrgId())) {
            throw new ServiceException("params_error", "机构id错误");
        }

        // 直播
        if (Objects.equals(businessType, TeamTypeEnum.LIVE_TEAM.getValue())) {
            if (Objects.equals(permissions, Constants.YES_1)) {
                // 开启机构业务
                openOrgBusiessType(organization, businessType, liveSettlementeType, operator, batchId);
            } else if (Objects.equals(permissions, Constants.NO_0)) {
                if (Objects.equals(Constants.NO_0, organization.getLivePermissions())) {
                    throw new ServiceException("already_change", "直播业务已关闭");
                }
                // 变更为关闭
                Organization o = new Organization();
                o.setOrgId(orgId);
                o.setLivePermissions(Constants.NO_0);
                o.setLiveSettlementeType(liveSettlementeType);
                organizationMapper.updateByPrimaryKeySelective(o);

                // 转到中转公会 子事务提交
                SpringUtils.getBean(OrganizationService.class).changeLivingTeamToTaquBeforClose(orgId, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS.getValue(), batchId, operator);
                // 团队变更为不可用
                List<Team> teams = teamService.selectTeamList(orgId, TeamTypeEnum.LIVE_TEAM.getValue());
                if (CollectionUtils.isNotEmpty(teams)) {
                    teamService.checkTeamCanClose(teams, batchId, operator);
                    for (Team t : teams) {
                        teamMapper.updateStatusByTeamId(Constants.NO_0, t.getTeamId());
                        teamService.afterCloseTeam(organization, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS_CLOSE_LIVE_TEAM, t, operator, batchId);
                    }
                }
                String info = String.format("关闭直播业务,机构id=%s,机构名称=%s", orgId, organization.getOrgName());
                backstageOperateLogService.addBackstageOperateLog(batchId, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS.getValue(), operator, info);
            } else {
                throw new ServiceException("data_error", "直播业务参数异常");
            }
        } else if (Objects.equals(businessType, TeamTypeEnum.TALK_TEAM.getValue())) {
            // 聊天室业务
            if (Objects.equals(permissions, Constants.YES_1)) {
                // 开启机构业务
                openOrgBusiessType(organization, businessType, liveSettlementeType, operator, batchId);
            } else if (Objects.equals(permissions, Constants.NO_0)) {
                if (Objects.equals(Constants.NO_0, organization.getChatRoomPermissions())) {
                    throw new ServiceException("already_change", "聊天室业务已关闭");
                }
                // 变更为关闭
                Organization o = new Organization();
                o.setOrgId(orgId);
                o.setChatRoomPermissions(Constants.NO_0);
                organizationMapper.updateByPrimaryKeySelective(o);

                List<TeamHost> chatTeamHosts = teamHostMapper.getHostListByOrgId(orgId, TeamTypeEnum.TALK_TEAM.getValue());
                // 处理聊天室艺人 批量转 无公会
                if (CollectionUtils.isNotEmpty(chatTeamHosts)) {
                    for (TeamHost teamHost : chatTeamHosts) {
                        // 子事务成功就提交
                        try {
                            ModifyRecordInfoDTO infoDTO = new ModifyRecordInfoDTO();
                            infoDTO.setOldOrgId(teamHost.getOrgId());
                            infoDTO.setOldTeamId(teamHost.getTeamId());
                            infoDTO.setNewOrgId(0L);
                            infoDTO.setNewTeamId(0L);
                            SpringUtils.getBean(ChatRoomService.class).changeTeamForChat(teamHost.getHostUuid(), null, JsonUtils.objectToString2(infoDTO), UUID.genUuid(), HostOperateTypeEnum.ORG_BUSINESS_CHANGE, TeamTypeEnum.getByValue(teamHost.getTeamType()));
                        } catch (Exception e) {
                            throw new ServiceException("close_org_fail", "聊天室艺人【" + teamHost.getHostUuid() + "】转到无公会失败");
                        }
                    }
                }

                // 团队变更为不可用
                List<Team> teams = teamService.selectTeamList(orgId, TeamTypeEnum.TALK_TEAM.getValue());
                if (CollectionUtils.isNotEmpty(teams)) {
                    teamService.checkTeamCanClose(teams, batchId, operator);
                    for (Team t : teams) {
                        teamMapper.updateStatusByTeamId(Constants.NO_0, t.getTeamId());
                        teamService.afterCloseTeam(organization, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS_CLOSE_CHAT_ROOM_TEAM, t, operator, batchId);
                    }
                }
                String info = String.format("关闭聊天室业务,机构id=%s,机构名称=%s", orgId, organization.getOrgName());
                backstageOperateLogService.addBackstageOperateLog(batchId, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS.getValue(), operator, info);
            }
        } else {
            throw new ServiceException("data_error", "业务参数异常");
        }
        // 钉钉通知
        String dingMsg = String.format("机构业务变更,机构id=%s,机构名称=%s,业务类型=%s,业务状态=%s", organization.getOrgId(), organization.getOrgName(), TeamTypeEnum.getByValue(businessType).getMsg(), Objects.equals(permissions, Constants.YES_1) ? "开启" : "关闭");
//        pushService.pushBackstageOperateToDingRobot(operator, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS.getMsg(), TeamTypeEnum.getByValue(businessType).getMsg(), dingMsg);
    }

    /**
     * 将直播团队下的主播都转到中转公会（参考之前【解散直播机构】逻辑）
     *
     * @param orgId
     * @param backstageOperateType
     * @param batchId
     * @param operator
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public void changeLivingTeamToTaquBeforClose(Long orgId, Integer backstageOperateType, String batchId, String operator) {
        //从php那边拿到中转公会的团队id
        Integer teamIdTemp = hostService.getTaquConsortiaId();
        //根据对应团队查找对应的机构
        Team team = teamMapper.selectById(teamIdTemp);
        if (team == null) {
            log.warn("关闭的机构：{}-----从php获取到的中转机构团队id：{}", orgId, teamIdTemp);
            throw new ServiceException("param_error", "查询不到中转机构信息。");
        }
        List<TeamHost> liveTeamHosts = teamHostMapper.getHostListByOrgId(orgId, TeamTypeEnum.LIVE_TEAM.getValue());
        if (CollectionUtils.isNotEmpty(liveTeamHosts)) {
            String info = String.format("直播主播变更公会,将原机构公会下主播移动到中转公会,旧机构id=%s,开始", orgId);
            backstageOperateLogService.addBackstageOperateLog(batchId, backstageOperateType, operator, info);
            List<String> hostUuids = liveTeamHosts.stream().map(TeamHost::getHostUuid).collect(Collectors.toList());
            log.info("中转机构名称：{}，主播：{}", teamIdTemp, JsonUtils.objectToString(hostUuids));
            liveTeamHosts.forEach(teamHost -> {
                operateLog(teamHost.getHostUuid(), teamHost.getTeamId(), Long.valueOf(teamIdTemp));
                String itemInfo = String.format("直播主播变更公会,将原机构公会下主播移动到中转公会,旧机构id=%s,旧团队id=%s,新团队id=%s", orgId, teamHost.getTeamId(), team.getOrgId(), teamIdTemp);
                backstageOperateLogService.addBackstageOperateLog(batchId, backstageOperateType, operator, itemInfo);

                // 插入日志
                ModifyRecordInfoDTO modifyRecordInfo = new ModifyRecordInfoDTO();
                modifyRecordInfo.setOldOrgId(teamHost.getOrgId());
                modifyRecordInfo.setOldTeamId(teamHost.getTeamId());
                modifyRecordInfo.setNewOrgId(team.getOrgId());
                modifyRecordInfo.setNewTeamId(Long.valueOf(teamIdTemp));
                hostModifyRecordService.addRecord(teamHost.getHostUuid(), TeamTypeEnum.TALK_TEAM.getValue(), UUID.genUuid(), HostOperateTypeEnum.CLOSE_LIVE_BUSINESS, modifyRecordInfo, "", Constants.YES_1, "", "");

                teamHost.setOrgId(team.getOrgId());
                teamHost.setEmployeeId(null);
                teamHost.setTeamId(Long.valueOf(teamIdTemp));
                teamHost.setStatus(EmployeeStatusEnum.DEPAETURE.getValue());
                teamHostMapper.updateByPrimaryKey(teamHost);
            });
            hostService.changeHostConsortia(String.valueOf(teamIdTemp), hostUuids);
            info = String.format("直播主播变更公会,将原机构公会下主播移动到中转公会,旧机构id=%s,完成", orgId);
            backstageOperateLogService.addBackstageOperateLog(batchId, backstageOperateType, operator, info);
        }
    }

    /**
     * 开启机构业务
     *
     * @param organization
     * @param busiessType
     * @param liveSettlementeType
     * @param operator
     * @param batchId
     */
    @Transactional(rollbackFor = Exception.class)
    public void openOrgBusiessType(Organization organization, Integer busiessType, Integer liveSettlementeType, String operator, String batchId){
        if(organization == null){
            throw new ServiceException("org_not_exist", "机构不存在");
        }
        Long orgId = organization.getOrgId();
        if (Objects.equals(busiessType, TeamTypeEnum.LIVE_TEAM.getValue())) {
            if (Objects.equals(Constants.YES_1, organization.getLivePermissions())) {
                throw new ServiceException("already_change", "直播业务已开启");
            }
            // 开启直播机构权限,保存直播机构结算
            Organization o = new Organization();
            o.setOrgId(orgId);
            o.setLivePermissions(Constants.YES_1);
            o.setLiveSettlementeType(liveSettlementeType);
            organizationMapper.updateByPrimaryKeySelective(o);

            // 默认团队设为启用
            Team team = teamMapper.getDefaultTeam(o.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (team == null) {
                throw new ServiceException("no_default_team", "无默认直播团队");
            }
            teamMapper.updateStatusByTeamId(Constants.YES_1, team.getTeamId());
            String info = String.format("开启直播业务,机构id=%s,机构名称=%s,默认团队id=%s", orgId, organization.getOrgName(), team.getTeamId());
            backstageOperateLogService.addBackstageOperateLog(batchId, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS.getValue(), operator, info);
            // 发送钉钉通知
            pushService.pushBackstageOperateToDingRobot(operator, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS.getMsg(), TeamTypeEnum.LIVE_TEAM.getMsg(), info);
        } else if(Objects.equals(busiessType, TeamTypeEnum.TALK_TEAM.getValue())) {
            if (Objects.equals(Constants.YES_1, organization.getChatRoomPermissions())) {
                throw new ServiceException("already_change", "聊天室业务已开启");
            }
            // 开启直播机构权限,保存直播机构结算
            Organization o = new Organization();
            o.setOrgId(orgId);
            o.setChatRoomPermissions(Constants.YES_1);
            organizationMapper.updateByPrimaryKeySelective(o);

            // 默认团队设为启用
            Team team = teamMapper.getDefaultTeam(o.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue());
            if (team == null) {
                throw new ServiceException("no_default_team", "无默认聊天室团队");
            }
            teamMapper.updateStatusByTeamId(Constants.YES_1, team.getTeamId());
            String info = String.format("开启聊天室业务,机构id=%s,机构名称=%s,默认团队id=%s", orgId, organization.getOrgName(), team.getTeamId());
            backstageOperateLogService.addBackstageOperateLog(batchId, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS.getValue(), operator, info);
            // 钉钉通知
            pushService.pushBackstageOperateToDingRobot(operator, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS.getMsg(), TeamTypeEnum.TALK_TEAM.getMsg(), info);
        } else {
            throw new ServiceException("business_type_error", "业务类型错误");
        }
    }

    /**
     * 关闭机构后置处理
     *
     * @param organization
     * @param operateTypeEnum
     * @param operator
     */
    @Transactional(rollbackFor = Exception.class)
    public void afterCloseOrg(Organization organization, BackstageOperateLog.OperateTypeEnum operateTypeEnum, String operator, String batchId){
        if (organization == null) {
            organization = new Organization();
        }
        if (operateTypeEnum == null) {
            operateTypeEnum = BackstageOperateLog.OperateTypeEnum.DEFAULT;
        }

        String info = String.format("关闭机构,机构id=%s,机构名称=%s", organization.getOrgId(), organization.getOrgName());
        backstageOperateLogService.addBackstageOperateLog(batchId, operateTypeEnum.getValue(), operator, info);

        // 发送钉钉通知
        String teamTypeName = "无";
        pushService.pushBackstageOperateToDingRobot(operator, operateTypeEnum.getMsg(), teamTypeName, info);
    }

    /**
     * 获取打款分页数据
     * @param remitSearchVO
     * @param pageInfo
     * @return
     */
    @Override
    public PageInfo<RemitItemVO> getRemitPageList(RemitSearchVO remitSearchVO, PageInfo pageInfo) {
        // 分页
        PageHelper.startPage(pageInfo.getPageNum(), pageInfo.getPageSize());
        Organization organization = new Organization();
        organization.setRemitModifyStatus(remitSearchVO.getStatus());
        if (StringUtils.isNotBlank(remitSearchVO.getOrgName())) {
            organization.setOrgName(remitSearchVO.getOrgName());
        }
//        List<Organization> organizationList = organizationMapper.getByList(organization);
        List<Organization> organizationList = organizationMapper.listRemitInfo(organization);
        PageInfo<Organization> organizationPageInfo = new PageInfo<>(organizationList);
        List<RemitItemVO> remitItemVOList = new ArrayList<>();
        for (Organization item : organizationList) {
            RemitItemVO remitItemVO = new RemitItemVO();
            remitItemVO.setOrgName(item.getOrgName());
            remitItemVO.setAccountName(item.getAccountName());
            remitItemVO.setPublicReceivingBankAccount(item.getPublicReceivingBankAccount());
            remitItemVO.setSubBranchName(item.getSubBranchName());
            remitItemVO.setAccountProvinceCity(StringUtils.isBlank(item.getProvince()) ? "" : item.getProvince()
                    + (StringUtils.isBlank(item.getCity()) ? "" : item.getCity()));
            remitItemVO.setStatusName(item.getRemitModifyStatus().equals(Constants.ONE) ? "审核中" : "正常");
            remitItemVO.setStatusColor(item.getRemitModifyStatus());
            remitItemVO.setEnterpriseName(item.getEnterpriseName());
            remitItemVO.setAccountBankName(item.getAccountBankName());

            remitItemVOList.add(remitItemVO);
        }
        PageInfo<RemitItemVO> remitPageList = new PageInfo<>(remitItemVOList);
        remitPageList.setTotal(organizationPageInfo.getTotal());

        return remitPageList;
    }

    @Override
    public List<OrgFlowLog> getOrgFlowLogById(Long orgId) {
        List<OrgFlowLog> orgFlowLogList = orgFlowLogMapper.selectByOrgId(orgId);

        return orgFlowLogList;
    }

    @Override
    public void orgCloseReview(Long orgId, String operator, String reason) {
        //SsoUserCombobox combobox = feiShuService.getSsoUserCombobox(operator);
        //if (combobox == null) {
        //    return;
        //}
        // 组装飞书内容
        Organization orgInfo = organizationService.getOrgInfo(orgId);
        ApprovalInstanceRequest instance = new ApprovalInstanceRequest();
        instance.setBusinessType(FeiShuRelationType.ORG_CLOSE_REVIEW.ordinal());
        instance.setApproval_code(FeishuConfig.ORG_CLOSE_REVIEW);
        instance.setUser_id("84eb25g3");
        instance.setOpen_id("ou_4e13f95e03eaff8a809477f5ed64b9a4");
        List<ApprovalInstanceRequest.Form> formList = new ArrayList<>();
        formList.add(new ApprovalInstanceRequest.Form("orgName", "input", orgInfo.getOrgName()));
        formList.add(new ApprovalInstanceRequest.Form("businessPerson", "input", orgInfo.getBusinessPerson()));
        formList.add(new ApprovalInstanceRequest.Form("chargePersonPhone", "input", StringUtils.isBlank(orgInfo.getChargePersonPhone()) ? "empty" : orgInfo.getChargePersonPhone()));
        formList.add(new ApprovalInstanceRequest.Form("orgType", "input", "未知"));
        formList.add(new ApprovalInstanceRequest.Form("enterpriseName", "input", orgInfo.getEnterpriseName()));
        formList.add(new ApprovalInstanceRequest.Form("orgId", "input", orgInfo.getOrgId().toString()));
        formList.add(new ApprovalInstanceRequest.Form("joinTime", "input", Objects.isNull(orgInfo.getJoinTime()) ? "empty" : orgInfo.getJoinTime().toString()));
        formList.add(new ApprovalInstanceRequest.Form("liveSettlementeType", "input", orgInfo.getLiveSettlementeType().toString()));
        formList.add(new ApprovalInstanceRequest.Form("closeReason", "input", reason));
        instance.setForm(JSONUtil.toJsonStr(formList));

        Organization organization = new Organization();
        organization.setOrgId(orgId);

        // 发起飞书通知
        feiShuService.getFeishuApprovalResponse(instance, organization);

        // 飞书发送成功 写入日志
        Date now = new Date();
        OrgFlowLog orgFlowLog = new OrgFlowLog();
        orgFlowLog.setOrgId(orgId);
        orgFlowLog.setCreateTime(now);
        orgFlowLog.setCreateUser(operator);
        orgFlowLog.setRemark("");
        orgFlowLog.setResult(Constants.ZERO);
        orgFlowLog.setUserTimeInfo("");
        orgFlowLog.setScene(Constants.ONE);
        orgFlowLogMapper.insert(orgFlowLog);
    }



}
