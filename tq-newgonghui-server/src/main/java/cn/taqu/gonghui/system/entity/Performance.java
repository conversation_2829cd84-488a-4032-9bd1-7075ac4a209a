package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName(value = "performance")
public class Performance extends BaseDO {

    @TableField(value = "month")
    private Long month;
    @TableField(value = "earnings")
    private Integer earnings;
    @TableField(value = "team_earnings")
    private Integer teamEarnings;
    @TableField(value = "performance_deduct")
    private Integer performanceDeduct;
    @TableField(value = "name")
    private String name;
}
