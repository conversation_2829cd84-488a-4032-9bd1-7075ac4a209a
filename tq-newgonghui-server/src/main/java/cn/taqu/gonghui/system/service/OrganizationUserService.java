package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.vo.OrganizationVo;
import cn.taqu.gonghui.system.entity.Organization;

import java.util.List;
import java.util.Map;

public interface OrganizationUserService {

    Map<String, Object> saveOrgApplyStepOne(OrganizationVo vo);

    Map<String, Object> saveOrgApplyStepTwo(OrganizationVo vo);

    Map<String, Object> saveOrgApplyStepThree(OrganizationVo vo);

    Map<String, Object> saveOrgApplyStepFour(OrganizationVo vo);

    Map<String, Object> saveOrgApplyStepFive(OrganizationVo vo);

    Organization getOrgApplyInfo();

    Map<String, Object> getOrgInfoStatus();

    List<Integer> getOrgInfoType(Long orgId);

    void changeForumStatus();

    void changeOrg();

    Organization getOrgInfoByCurrentThrow();

    void changeContent(Long orgId, String content);
}
