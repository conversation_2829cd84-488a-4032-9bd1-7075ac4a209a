package cn.taqu.gonghui.system.service.multilive;

import cn.taqu.gonghui.chatroom.search.MasterAndApprenticeSearch;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.common.utils.PageResult;
import cn.taqu.gonghui.live.param.*;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 20
 * 多人娱乐主播统计数据服务
 */
public interface MultiLiveStatsService {

    /***
     * 管理端--搜索主播数据
     * @param search
     * @return
     */
    PageDataResult searchAdminHostData(MultiLiveHostSearch search);

    /**
     * 管理端--搜索公会数据
     * @param search
     * @return
     */
    PageDataResult searchAdminConsortiaData(MultiLiveConsortiaSearch search);

    /**
     * 管理端--获取主播数据的下载地址
     * @param search
     * @return
     */
    String getAdminHostDataDownloadUrl(MultiLiveHostSearch search);

    /**
     * 管理端--获取公会数据的下载地址
     * @param search
     * @return
     */
    String getAdminConsortiaDataDownloadUrl(MultiLiveConsortiaSearch search);

    /**
     * 用户端--搜索主播数据
     * @param search
     * @return
     */
    PageDataResult searchHostData(MultiLiveHostSearchOfUM search);

    /**
     * 用户端--获取主播数据的下载地址
     * @param search
     * @return
     */
    String getHostDataDownloadUrl(MultiLiveHostSearchOfUM search);

    /**
     * 管理端--搜索房间数据
     * @param search
     * @return
     */
    PageDataResult<?> searchAdminRoomData(MultiLiveRoomSearch search);

    /**
     * 用户端--搜索公会数据
     * @param search
     * @return
     */
    PageDataResult searchConsortiaData(MultiLiveConsortiaSearchOfUM search);

    /**
     * 用户端--获取公会数据下载地址
     * @param search
     * @return
     */
    String getConsortiaDataDownloadUrl(MultiLiveConsortiaSearchOfUM search);

    /**
     * 用户端--搜索房间数据
     * @param search
     * @return
     */
    PageDataResult searchRoomData(MultiLiveRoomSearchOfUM search);

    /**
     * 用户端--获取房间数据下载地址
     * @param search
     * @return
     */
    String getRoomDataDownloadUrl(MultiLiveRoomSearchOfUM search);

    /**
     * 管理端--获取房间数据下载地址
     * @param search
     * @return
     */
    String getAdminRoomDataDownloadUrl(MultiLiveRoomSearch search);

    /**
     * 管理端--搜索直播业务数据
     * @param search
     * @return
     */
    PageDataResult<?> searchAdminLiveBizData(MultiLiveLiveBizSearch search);

    /**
     * 管理端--获取直播业务数据下载地址
     * @param search
     * @return
     */
    String getAdminLiveBizDataDownloadUrl(MultiLiveLiveBizSearch search);
}
