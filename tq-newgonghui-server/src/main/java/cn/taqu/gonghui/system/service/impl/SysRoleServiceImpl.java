package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.dto.SysRoleDto;
import cn.taqu.gonghui.system.entity.SysRole;
import cn.taqu.gonghui.system.entity.SysRoleMenu;
import cn.taqu.gonghui.system.mapper.SysRoleMapper;
import cn.taqu.gonghui.system.mapper.SysRoleMenuMapper;
import cn.taqu.gonghui.system.mapper.SysUserRoleMapper;
import cn.taqu.gonghui.system.service.SysRoleService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {


    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Override
    public List<SysRole> selectRoleList(SysRole sysRole) {
        return this.list();
    }

    @Override
    public IPage<SysRole> selectRoleList(int pageNo, int pageSize) {
        IPage<SysRole> page = new Page<>(pageNo, pageSize);
       return this.page(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRole(SysRoleDto sysRoleDto) {
        validateParams(sysRoleDto);
        // 校验当前业务类型下角色名称是否已经存在
        if (roleNameIsExisted(sysRoleDto)) {
            throw new ServiceException("param_error","当前业务类型下角色名称已存在");
        }
        // 校验当前业务类型下权限标识符是否已经存在
        if (roleKeyIsExisted(sysRoleDto)){
            throw new ServiceException("param_error","当前业务类型下权限标识符已存在");
        }
        if(UserTypeEnum.MANAGER.getCode().equals(sysRoleDto.getRoleKey())){
            throw new ServiceException("param_error","不能新增机构管理员角色");

        }
        SysRole sysRole = new SysRole();
        sysRole.setRoleName(sysRoleDto.getRoleName());
        sysRole.setRoleKey(sysRoleDto.getRoleKey());
        sysRole.setCreateBy(sysRoleDto.getCreateBy());
        sysRole.setType(sysRoleDto.getType());
        sysRole.setRemark(sysRoleDto.getRemark());
        sysRole.setStatus(sysRoleDto.getStatus());
        sysRole.setCreateTime(DateUtil.currentTimeSeconds());
        sysRole.setUpdateTime(DateUtil.currentTimeSeconds());
        sysRole.setCreateBy(sysRoleDto.getCreateBy());
        sysRole.setUpdateBy(sysRoleDto.getCreateBy());
        this.baseMapper.insert(sysRole);
        if(CollectionUtils.isNotEmpty(sysRoleDto.getMenuIdList())){
            sysRoleDto.getMenuIdList().stream().forEach(item ->{
                SysRoleMenu sysRoleMenu = new SysRoleMenu();
                sysRoleMenu.setMenuId(item);
                sysRoleMenu.setRoleId(sysRole.getRoleId());
                sysRoleMenu.setCreateTime(DateUtil.currentTimeSeconds());
                sysRoleMenuMapper.insert(sysRoleMenu);
            });

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editRole(SysRoleDto sysRoleDto) {
        validateParams(sysRoleDto);
        if(null == sysRoleDto.getRoleId()){
            throw new ServiceException("param_error","参数有误");
        }
        SysRole existRole = this.baseMapper.selectById(sysRoleDto.getRoleId());
        // 不能修改业务类型
        if (!existRole.getType().equals(sysRoleDto.getType())) {
            throw new ServiceException("param_error","业务类型不能修改");
        }
        // 校验当前业务类型下角色名称是否已经存在
        if (roleNameIsExisted(sysRoleDto)) {
            throw new ServiceException("param_error","当前业务类型下角色名称已存在");
        }
        // 校验当前业务类型下权限标识符是否已经存在
        if (roleKeyIsExisted(sysRoleDto)){
            throw new ServiceException("param_error","当前业务类型下权限标识符已存在");
        }
        if(UserTypeEnum.MANAGER.getCode().equals(sysRoleDto.getRoleKey())){
            throw new ServiceException("param_error","机构管理员角色不能操作");

        }
        SysRole sysRole = new SysRole();
        sysRole.setRoleId(sysRoleDto.getRoleId());
        sysRole.setRoleName(sysRoleDto.getRoleName());
        sysRole.setUpdateBy(sysRoleDto.getUpdateBy());
        sysRole.setRoleKey(sysRoleDto.getRoleKey());
        sysRole.setType(sysRoleDto.getType());
        sysRole.setRemark(sysRoleDto.getRemark());
        sysRole.setStatus(sysRoleDto.getStatus());
        sysRole.setUpdateTime(DateUtil.currentTimeSeconds());
        sysRole.setUpdateBy(sysRoleDto.getUpdateBy());
        this.updateById(sysRole);
    }

    /**
     * 编辑角色菜单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editRoleMenu(Long roleId,Long[] menuIdArr){
        if(roleId == null){
            throw new ServiceException("param_error","参数有误");
        }
        sysRoleMenuMapper.deleteRoleMenuByRoleId(roleId);
        if(menuIdArr!=null && menuIdArr.length>0){
            List<Long> menuIdList = Arrays.asList(menuIdArr);
            List<SysRoleMenu> list = Lists.newArrayList();
            menuIdList.stream().forEach(item ->{
                SysRoleMenu sysRoleMenu = new SysRoleMenu();
                sysRoleMenu.setMenuId(item);
                sysRoleMenu.setRoleId(roleId);
                sysRoleMenu.setCreateTime(DateUtil.currentTimeSeconds());
                list.add(sysRoleMenu);
            });
            sysRoleMenuMapper.batchRoleMenu(list);
        }
    }


    /**
     * 根据角色id查询角色详情
     */
    @Override
    public SysRole selectRoleByRoleId(Long roleId) {
        if (null == roleId) {
            throw new ServiceException("param_error","角色ID不能为空");
        }
        QueryWrapper queryWrapper =new QueryWrapper();
        queryWrapper.eq("role_id",roleId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    /**
     * 判断当前业务类型下角色名称是否已经存在
     */
    @Override
    public Boolean roleNameIsExisted(SysRoleDto roleDto) {
        Long roleId = roleDto.getRoleId() == null ? -1L : roleDto.getRoleId();
        SysRole sysRole = this.baseMapper.roleNameIsExisted(roleDto.getRoleName(), roleDto.getType());
        if (sysRole != null && sysRole.getRoleId().longValue() != roleId.longValue()) {
            return true;
        }
        return false;
    }

    /**
     * 判断当前业务下角色权限标识符是否已经存在
     */
    @Override
    public Boolean roleKeyIsExisted(SysRoleDto roleDto) {
        Long roleId = roleDto.getRoleId() == null ? -1L : roleDto.getRoleId();
        SysRole sysRole = this.baseMapper.roleKeyIsExisted(roleDto.getRoleKey(), roleDto.getType());
        if (sysRole != null && sysRole.getRoleId().longValue() != roleId.longValue()) {
            return true;
        }
        return false;
    }



    private void validateParams(SysRoleDto sysRoleDto){
        if(StringUtils.isEmpty(sysRoleDto.getRoleName())){
            throw new ServiceException("param_error","请输入角色名称");
        }

        if(sysRoleDto.getRoleName().length() > 30){
            throw new ServiceException("param_error","角色名称超出范围，请输入30字符以内的长度");
        }
        if(StringUtils.isEmpty(sysRoleDto.getRoleKey())){
            throw new ServiceException("param_error","请输入权限标识符");
        }
        if(sysRoleDto.getRoleKey().length() > 100){
            throw new ServiceException("param_error","权限标识符超出范围，请输入100字符以内的长度");
        }

        if(StringUtils.isNotEmpty(sysRoleDto.getRemark()) && sysRoleDto.getRemark().length() > 500){
            throw new ServiceException("param_error","备注超出范围，请输入500字符以内的长度");
        }
        sysRoleDto.setRoleName(StringUtils.trim(sysRoleDto.getRoleName()));
        sysRoleDto.setRoleKey(StringUtils.trim(sysRoleDto.getRoleKey()));
        sysRoleDto.setRemark(StringUtils.trim(sysRoleDto.getRemark()));
    };

    @Override
    public List<CommonSelect> getLeaderAndAgent(Integer type) {
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper();
        queryWrapper.eq("type",type);
        queryWrapper.eq("status",1);
        if(TeamTypeEnum.TALK_TEAM.getValue() == type){
            queryWrapper.and(wrapper -> wrapper.eq("role_key", UserTypeEnum.LEADER.getCode()));
        }else{
            queryWrapper.and(wrapper -> wrapper.eq("role_key", UserTypeEnum.LEADER.getCode()).or().eq("role_key", UserTypeEnum.AGENTER.getCode()));
        }
        List<SysRole> list = this.list(queryWrapper);
        return list.stream().map(CommonSelect::new).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(Long roleId) {
        this.removeById(roleId);
        sysRoleMenuMapper.deleteRoleMenuByRoleId(roleId);
        sysUserRoleMapper.deleteUserRoleByRoleId(roleId);
    }



    @Override
    public List<CommonSelect> getRoleSelect() {
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper();
        queryWrapper.eq("status",1);
        List<SysRole> list = this.list(queryWrapper);
        return list.stream().map(CommonSelect::new).collect(Collectors.toList());
    }

}
