package cn.taqu.gonghui.system.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class ChartData implements Serializable {

    @JSONField(name = "amount_list")
    private List<AmountListDTO> amountList;
    @JSONField(name = "live_num_list")
    private List<LiveNumListDTO> liveNumList;

    @NoArgsConstructor
    @Data
    public static class AmountListDTO {
        @J<PERSON>NField(name = "time")
        private String time;
        @JSONField(name = "value")
        private String value;
    }

    @NoArgsConstructor
    @Data
    public static class LiveNumListDTO {
        @JSONField(name = "time")
        private String time;
        @J<PERSON>NField(name = "value")
        private String value;
    }
}
