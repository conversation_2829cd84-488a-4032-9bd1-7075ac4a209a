package cn.taqu.gonghui.system.entity;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;

@Data
@TableName(value = "org_account_log", autoResultMap = true)
public class OrgAccountLog {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 负责人手机号(旧)
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldPhone;

    /**
     * 负责人手机号(旧)-摘要
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = Sm3EncryptTypeHandler.class)
    private String oldPhoneDigest;

    /**
     * 负责人手机号(旧)-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldPhoneCipher;

    /**
     * 负责人手机号(新)
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newPhone;

    /**
     * 负责人手机号(新)-摘要
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = Sm3EncryptTypeHandler.class)
    private String newPhoneDigest;
    /**
     * 负责人手机号(新)-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newPhoneCipher;

    /**
     * 负责人姓名（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldPrincipal;
    /**
     * 负责人姓名（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldPrincipalCipher;
    /**
     * 负责人姓名（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newPrincipal;
    /**
     * 负责人姓名（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newPrincipalCipher;

    /**
     * 负责人联系手机号（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldMobilePhone;
    /**
     * 负责人联系手机号（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldMobilePhoneCipher;

    /**
     * 负责人联系手机号(新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newMobilePhone;
    /**
     * 负责人联系手机号（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newMobilePhoneCipher;


    private String oldWeixin;

    private String newWeixin;

    private Long oldBirth;

    private Long newBirth;

    /**
     * 负责人身份证（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldIdentity;
    /**
     * 负责人身份证（旧）--密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldIdentityCipher;

    /**
     * 负责人身份证（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newIdentity;
    /**
     * 负责人身份证（新）--密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newIdentityCipher;

    /**
     * 收件地址（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldSite;
    /**
     * 收件地址（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldSiteCipher;

    /**
     * 收件地址（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newSite;
    /**
     * 收件地址（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newSiteCipher;

    /**
     * 联系人邮箱（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldMailbox;
    /**
     * 联系人邮箱（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldMailboxCipher;
    /**
     * 联系人邮箱（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newMailbox;
    /**
     * 联系人邮箱（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newMailboxCipher;

    /**
     * 证件正面（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldIdentityFront;
    /**
     * 证件正面（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldIdentityFrontCipher;
    /**
     * 证件正面（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newIdentityFront;
    /**
     * 证件正面（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newIdentityFrontCipher;

    /**
     * 证件反面（旧）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldIdentityReverse;
    /**
     * 证件反面（旧）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldIdentityReverseCipher;
    /**
     * 证件反面（新）
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newIdentityReverse;

    /**
     * 证件反面（新）-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newIdentityReverseCipher;

    /**
     * 手持证件(旧)
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldIdentityHand;
    /**
     * 手持证件(旧)--密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldIdentityHandCipher;
    /**
     * 手持证件(新)
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newIdentityHand;
    /**
     * 手持证件(新)-密文
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newIdentityHandCipher;

    private String createOperator;

    private Date createTime;

    private Long relevanceId;

    public String getOldPhone() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldPhoneCipher;
        }
        if(StringUtils.isBlank(this.oldPhone) && StringUtils.isNotBlank(this.oldPhoneCipher)){
            return this.oldPhoneCipher;
        }
        return this.oldPhone;
    }

    public String getNewPhone() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newPhoneCipher;
        }
        if(StringUtils.isBlank(this.newPhone) && StringUtils.isNotBlank(this.newPhoneCipher)){
            return this.newPhoneCipher;
        }
        return this.newPhone;
    }

    public String getOldPrincipal() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldPrincipalCipher;
        }
        if(StringUtils.isBlank(this.oldPrincipal) && StringUtils.isNotBlank(this.oldPrincipalCipher)){
            return this.oldPrincipalCipher;
        }
        return this.oldPrincipal;
    }

    public String getNewPrincipal() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newPrincipalCipher;
        }
        if(StringUtils.isBlank(this.newPrincipal) && StringUtils.isNotBlank(this.newPrincipalCipher)){
            return this.newPrincipalCipher;
        }
        return newPrincipal;
    }

    public String getOldMobilePhone() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldMobilePhoneCipher;
        }
        if(StringUtils.isBlank(this.oldMobilePhone) && StringUtils.isNotBlank(this.oldMobilePhoneCipher)){
            return this.oldMobilePhoneCipher;
        }
        return this.oldMobilePhone;
    }

    public String getNewMobilePhone() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newMobilePhoneCipher;
        }
        if(StringUtils.isBlank(this.newMobilePhone) && StringUtils.isNotBlank(this.newMobilePhoneCipher)){
            return this.newMobilePhoneCipher;
        }
        return this.newMobilePhone;
    }

    public String getOldPhoneDigest() {
        return getOldPhone();
    }

    public String getNewPhoneDigest() {
        return getNewPhone();
    }

    public String getOldIdentity() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldIdentityCipher;
        }
        if(StringUtils.isBlank(this.oldIdentity) && StringUtils.isNotBlank(this.oldIdentityCipher)){
            return this.oldIdentityCipher;
        }
        return this.oldIdentity;
    }

    public String getNewIdentity() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newIdentityCipher;
        }
        if(StringUtils.isBlank(this.newIdentity) && StringUtils.isNotBlank(this.newIdentityCipher)){
            return this.newIdentityCipher;
        }
        return this.newIdentity;
    }

    public String getOldSite() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldSiteCipher;
        }
        if(StringUtils.isBlank(this.oldSite) && StringUtils.isNotBlank(this.oldSiteCipher)){
            return this.oldSiteCipher;
        }
        return this.oldSite;
    }

    public String getNewSite() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newSiteCipher;
        }
        if(StringUtils.isBlank(this.newSite) && StringUtils.isNotBlank(this.newSiteCipher)){
            return this.newSiteCipher;
        }
        return this.newSite;
    }

    public String getOldIdentityFront() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldIdentityFrontCipher;
        }
        if(StringUtils.isBlank(this.oldIdentityFront) && StringUtils.isNotBlank(this.oldIdentityFrontCipher)){
            return this.oldIdentityFrontCipher;
        }
        return this.oldIdentityFront;
    }

    public String getNewIdentityFront() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newIdentityFrontCipher;
        }
        if(StringUtils.isBlank(this.newIdentityFront) && StringUtils.isNotBlank(this.newIdentityFrontCipher)){
            return this.newIdentityFrontCipher;
        }
        return this.newIdentityFront;
    }

    public String getOldIdentityReverse() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldIdentityReverseCipher;
        }
        if(StringUtils.isBlank(this.oldIdentityReverse) && StringUtils.isNotBlank(this.oldIdentityReverseCipher)){
            return this.oldIdentityReverseCipher;
        }
        return this.oldIdentityReverse;
    }

    public String getNewIdentityReverse() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newIdentityReverseCipher;
        }
        if(StringUtils.isBlank(this.newIdentityReverse) && StringUtils.isNotBlank(this.newIdentityReverseCipher)){
            return this.newIdentityReverseCipher;
        }
        return this.newIdentityReverse;
    }

    public String getOldIdentityHand() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldIdentityHandCipher;
        }
        if(StringUtils.isBlank(this.oldIdentityHand) && StringUtils.isNotBlank(this.oldIdentityHandCipher)){
            return this.oldIdentityHandCipher;
        }
        return this.oldIdentityHand;
    }

    public String getNewIdentityHand() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldIdentityHandCipher;
        }
        if(StringUtils.isBlank(this.newIdentityHand) && StringUtils.isNotBlank(this.newIdentityHandCipher)){
            return this.oldIdentityHandCipher;
        }
        return newIdentityHandCipher;
    }

    public void setOldPhone(String oldPhone) {
        this.oldPhone = oldPhone;
        this.oldPhoneCipher = oldPhone;
        this.oldPhoneDigest = oldPhone;
    }

    public void setNewPhone(String newPhone) {
        this.newPhone = newPhone;
        this.newPhoneCipher = newPhone;
        this.newPhoneDigest = newPhone;
    }

    public void setOldPrincipal(String oldPrincipal) {
        this.oldPrincipal = oldPrincipal;
        this.oldPrincipalCipher = oldPrincipal;
    }

    public void setNewPrincipal(String newPrincipal) {
        this.newPrincipal = newPrincipal;
        this.newPrincipalCipher = newPrincipal;
    }

    public void setOldMobilePhone(String oldMobilePhone) {
        this.oldMobilePhone = oldMobilePhone;
        this.oldMobilePhoneCipher = oldMobilePhone;
    }

    public void setNewMobilePhone(String newMobilePhone) {
        this.newMobilePhone = newMobilePhone;
        this.newMobilePhoneCipher = newMobilePhone;
    }

    public void setOldIdentity(String oldIdentity) {
        this.oldIdentity = oldIdentity;
        this.oldIdentityCipher = oldIdentity;
    }

    public void setNewIdentity(String newIdentity) {
        this.newIdentity = newIdentity;
        this.newIdentityCipher = newIdentity;
    }

    public void setOldSite(String oldSite) {
        this.oldSite = oldSite;
        this.oldSiteCipher = oldSite;
    }

    public void setNewSite(String newSite) {
        this.newSite = newSite;
        this.newSiteCipher = newSite;
    }

    public void setOldMailbox(String oldMailbox) {
        this.oldMailbox = oldMailbox;
        this.oldMailboxCipher = oldMailbox;
    }

    public void setNewMailbox(String newMailbox) {
        this.newMailbox = newMailbox;
        this.newMailboxCipher = newMailbox;
    }

    public void setOldIdentityFront(String oldIdentityFront) {
        this.oldIdentityFront = oldIdentityFront;
        this.oldIdentityFrontCipher = oldIdentityFront;
    }

    public void setNewIdentityFront(String newIdentityFront) {
        this.newIdentityFront = newIdentityFront;
        this.newIdentityFrontCipher = newIdentityFront;
    }

    public void setOldIdentityReverse(String oldIdentityReverse) {
        this.oldIdentityReverse = oldIdentityReverse;
        this.oldIdentityReverseCipher = oldIdentityReverse;
    }

    public void setNewIdentityReverse(String newIdentityReverse) {
        this.newIdentityReverse = newIdentityReverse;
        this.newIdentityReverseCipher = newIdentityReverse;
    }

    public void setOldIdentityHand(String oldIdentityHand) {
        this.oldIdentityHand = oldIdentityHand;
        this.oldIdentityHandCipher = oldIdentityHand;
    }

    public void setNewIdentityHand(String newIdentityHand) {
        this.newIdentityHand = newIdentityHand;
        this.newIdentityHandCipher = newIdentityHand;
    }
}