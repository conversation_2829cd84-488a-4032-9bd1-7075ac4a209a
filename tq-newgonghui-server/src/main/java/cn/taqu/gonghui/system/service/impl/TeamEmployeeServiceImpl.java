package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.gonghui.common.constant.EmployeeStatusEnum;
import cn.taqu.gonghui.common.constant.OrgIdEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.constant.VerifyCodeEnum;
import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.service.VerifyService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.live.entity.Employee;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.mapper.SysUserRoleMapper;
import cn.taqu.gonghui.system.mapper.TeamEmployeeMapper;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.search.TeamEmployeeSearch;
import cn.taqu.gonghui.system.service.SysRoleService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.vo.CommonVo;
import cn.taqu.gonghui.system.vo.RoleVo;
import cn.taqu.gonghui.system.vo.TeamEmployeeVo;
import cn.taqu.gonghui.system.vo.TeamEmyloyeeAndRoleVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
@Service
@Slf4j
public class TeamEmployeeServiceImpl extends ServiceImpl<TeamEmployeeMapper, TeamEmployee> implements TeamEmployeeService {

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    private TeamEmployeeMapper employeeMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private TeamService teamService;

    @Override
    public void addTeamEmployee(String mobile, String employeeName, Long teamId, Long userId, Integer businessType, Integer type) {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        TeamEmployee employee = getOneByUserIdAndType(userId, businessType);

        if (employee != null && employee.getTeamId().equals(teamId)) {
            throw new ServiceException("teamemployee_is_exsits", "当前邀请用户已在当前团队，不能重复邀请");
        }
        if (employee != null && !employee.getOrgId().equals(loginUser.getUser().getOrgId())) {
            throw new ServiceException("teamemployee_is_exsits", "当前邀请用户已就职于其他机构，不能邀请");
        }
        if (employee != null && employee.getOrgId().equals(loginUser.getUser().getOrgId()) && !employee.getTeamId().equals(teamId)) {
            throw new ServiceException("teamemployee_is_exsits", "当前邀请用户已在机构其他团队中，不能邀请");
        }
        if (employee != null) {
            throw new ServiceException("teamemployee_is_exsits", "当前邀请用户已经是团队成员，不能重复邀请");
        }
        TeamEmployee teamEmployee = new TeamEmployee();
        teamEmployee.setOrgId(loginUser.getUser().getOrgId());
        teamEmployee.setOrgName(loginUser.getUser().getOrgName());
        teamEmployee.setEmployeeName(employeeName);
        teamEmployee.setMobile(mobile);
        teamEmployee.setTeamId(teamId);
        teamEmployee.setUserId(userId);
        teamEmployee.setType(type);
        teamEmployee.setInviteTime(DateUtil.currentTimeSeconds());
        teamEmployee.setCreateTime(DateUtil.currentTimeSeconds());
        teamEmployee.setUpdateTime(DateUtil.currentTimeSeconds());
        this.save(teamEmployee);
    }

    @Override
    public IPage<TeamEmployeeVo> selectTeamEmployeePage(TeamEmployeeSearch search, int pageNo, int pageSize) {
        IPage<TeamEmployeeVo> page = new Page<>(pageNo, pageSize);
        search.setSelectByDigest(EncryptSwitchConfig.selectByDigest);
        return baseMapper.selectTeamEmployeeList(page, search);
    }

    /**
     * 用户端成员列表
     *
     * @param search
     * @param pageNo
     * @param pageSize
     * @return
     */

    @Override
    public IPage<TeamEmployeeVo> selectTeamEmployeePageForUser(TeamEmployeeSearch search, int pageNo, int pageSize) {
        if (search.getTeamType() == null) {
            log.warn("selectTeamEmployeePageForUser:当前业务类型错误：{}", search.getTeamType());
            throw new ServiceException("team_type_error", "当前访问出问题啦！请联系客服人员");
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Long orgId = loginUser.getUser().getOrgId();
        RoleVo userRole = sysUserService.getCurrentRole(search.getTeamType());
        if (null == search) {
            search = new TeamEmployeeSearch();
        }
        search.setOrgId(orgId);
        if (UserTypeEnum.LEADER.getCode().equals(userRole.getRoleKey())) {
            TeamEmployee employee = getOneByUserIdAndType(loginUser.getUser().getUserId(), search.getTeamType());
            search.setTeamId(employee.getTeamId());
        } else if (UserTypeEnum.AGENTER.getCode().equals(userRole.getRoleKey())) {
            TeamEmployee employee = getOneByUserIdAndType(loginUser.getUser().getUserId(), search.getTeamType());
            search.setEmployeeId(employee.getEmployeeId());
        }
        if (search.getRoleId() != null) {
            // 通过roleId 来查询userId
            List<Long> list = sysUserService.selectUserIdsByRoleId(orgId, search.getRoleId());
            search.setUserIdList(list);
        }
        IPage<TeamEmployeeVo> page = new Page<>(pageNo, pageSize);
        search.setSelectByDigest(EncryptSwitchConfig.selectByDigest);
        IPage<TeamEmployeeVo> pageList = baseMapper.selectTeamEmployeeList(page, search);

        //获取用户角色
        converRoleName(pageList.getRecords(), search.getTeamType());
        if (TeamTypeEnum.TALK_TEAM.getValue() == search.getTeamType()) {
            return pageList;
        }
        //查询机构管理员姓名
        String managerName = sysUserService.selectOrgManager(orgId);

        // 查找团队负责人
        List<TeamEmployeeVo> teamEmployeeVos = baseMapper.selectLeaderList(orgId, search.getTeamType());
        List<TeamEmployeeVo> records = pageList.getRecords();

        // 负责人团队列表
        List<Long> leaderTeamIdList = new ArrayList<>();
        // 经纪人列表
        List<Long> agentEmployeeIdList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(records)) {
            records.stream().forEach(item -> {
                // 判断当前用户是否是经纪人
                if (StringUtils.isNotBlank(item.getRoleKey()) && UserTypeEnum.AGENTER.getCode().equals(item.getRoleKey())) {
                    //查找团队负责人
                    String leadersStr = teamEmployeeVos.stream().filter(obj -> item.getTeamId().equals(obj.getTeamId()))
                            .map(TeamEmployeeVo::getEmployeeName).collect(Collectors.joining(","));
                    if (StringUtils.isNotEmpty(leadersStr)) {
                        item.setSuperiors(leadersStr);
                    } else {
                        item.setSuperiors(managerName);
                    }

                    // 获取经纪人列表
                    agentEmployeeIdList.add(item.getEmployeeId());

                } else if (StringUtils.isNotBlank(item.getRoleKey()) && UserTypeEnum.LEADER.getCode().equals(item.getRoleKey())) {
                    item.setSuperiors(managerName);

                    // 获取负责人列表
                    leaderTeamIdList.add(item.getTeamId());
                }

            });

            // 团队下主播数量map(key = teamId,value = count)
            Map<Long, Long> hostsByTeamIdMap = hostsByTeamId(leaderTeamIdList);
            // 经纪人下主播数量map(key = employeeId,value = count)
            Map<Long, Long> hostsByEmployeeIdMap = hostsByEmployeeId(agentEmployeeIdList);
            records.stream().forEach(item -> {
                // 负责人
                Long hosts = 0L;
                if (StringUtils.isNotBlank(item.getRoleKey()) && UserTypeEnum.LEADER.getCode().equals(item.getRoleKey())) {

                    if (MapUtils.isNotEmpty(hostsByTeamIdMap)) {
                        hosts = hostsByTeamIdMap.get(item.getTeamId());
                        if (hosts == null) {
                            hosts = 0L;
                        }
                    }
                    item.setRelevanceHostNumber(hosts);
                }
                // 经纪人
                else if (StringUtils.isNotBlank(item.getRoleKey()) && UserTypeEnum.AGENTER.getCode().equals(item.getRoleKey())) {
                    if (MapUtils.isNotEmpty(hostsByEmployeeIdMap)) {
                        hosts = hostsByEmployeeIdMap.get(item.getEmployeeId());
                        if (hosts == null) {
                            hosts = 0L;
                        }
                    } else {
                        hosts = 0L;
                    }
                    item.setRelevanceHostNumber(hosts);
                }
            });
        }

        return pageList;
    }

    /**
     * 获取成员列表角色名称
     */
    private void converRoleName(List<TeamEmployeeVo> list, Integer businessType) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (TeamEmployeeVo vo : list) {
            RoleVo roleByUserId = sysUserService.getRoleByUserId(vo.getUserId(), businessType);
            vo.setRoleKey(roleByUserId.getRoleKey());
            vo.setRoleName(roleByUserId.getRoleName());
        }


    }

    /**
     * 获取团队下成员数量
     */
    public Map<Long, Long> membersByTeamId(List<Long> teamIds) {
        Map<Long, Long> membersByTeamIdMap = null;
        List<CommonVo> commonVos = null;
        if (CollectionUtils.isNotEmpty(teamIds)) {
            commonVos = this.baseMapper.membersByTeamId(teamIds);
        }
        if (CollectionUtils.isNotEmpty(commonVos)) {
            membersByTeamIdMap = commonVos.stream().collect(Collectors.toMap(CommonVo::getKeyResult, CommonVo::getValueResult, (key1, key2) -> key2));
        }
        return membersByTeamIdMap;
    }

    /**
     * 获取团队下主播数量
     */
    public Map<Long, Long> hostsByTeamId(List<Long> teamIds) {
        Map<Long, Long> hostsByTeamIdMap = null;
        List<CommonVo> commonVos = null;
        if (CollectionUtils.isNotEmpty(teamIds)) {
            commonVos = this.baseMapper.hostsByTeamId(teamIds);
        }
        if (CollectionUtils.isNotEmpty(commonVos)) {
            hostsByTeamIdMap = commonVos.stream().collect(Collectors.toMap(CommonVo::getKeyResult, CommonVo::getValueResult, (key1, key2) -> key2));
        }
        return hostsByTeamIdMap;
    }

    /**
     * 获取经纪人下主播数量
     */
    public Map<Long, Long> hostsByEmployeeId(List<Long> employeeIds) {
        Map<Long, Long> hostsByEmployeeIdMap = null;
        List<CommonVo> commonVos = null;
        if (CollectionUtils.isNotEmpty(employeeIds)) {
            commonVos = this.baseMapper.hostsByEmployeeId(employeeIds, TeamTypeEnum.LIVE_TEAM.getValue());
        }
        if (CollectionUtils.isNotEmpty(commonVos)) {
            hostsByEmployeeIdMap = commonVos.stream().collect(Collectors.toMap(CommonVo::getKeyResult, CommonVo::getValueResult, (key1, key2) -> key2));
        }
        return hostsByEmployeeIdMap;
    }

    /**
     * 查询成员列表
     *
     * @param userId
     * @return
     */


    @Override
    @Deprecated
    public List<TeamEmployee> getOneByUserId(Long userId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("user_id", userId);
        return this.list(queryWrapper);
    }

    public TeamEmployee getOneByUserIdAndType(Long userId, Integer businessType) {
        List<TeamEmployee> teamEmployees = this.baseMapper.seletEmployeeByUserIdAndType(userId, businessType);
        if (CollectionUtils.isNotEmpty(teamEmployees) && teamEmployees.size() > 1) {
            log.warn("当前用户异常,用户userId:{},业务类型：{}", userId, businessType);
            throw new ServiceException("teamemployee_is_exsits", "当前用户异常，请联系客服人员");
        }
        if (CollectionUtils.isEmpty(teamEmployees)) {
            return null;
        }
        return teamEmployees.get(0);
    }

    /**
     * 成员离职
     *
     * @param userId
     */
    @Override
    public void employeeDeparture(String userId) {

        //检验该用户是否为负责人，只有负责人能够操作离职，经纪人不能操作离职操作
    }


    /**
     * 添加成员
     *
     * @param mobile
     * @param verify
     * @param userName
     * @param teamId
     * @param roleId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void saveMember(String mobile, String verify, String userName, Long teamId, Long roleId, Integer businessType) {
        if (businessType == null) {
            log.warn("saveMember业务类型为空");
            throw new ServiceException("role_is_not_exisits", "当前业务类型不存在，请联系客服人员");
        }
        verifyService.verifyCommon(VerifyCodeEnum.ADD_MEMBER.getCode(), mobile, verify);
        SysRole role = sysRoleService.selectRoleByRoleId(roleId);
        if (role == null) {
            throw new ServiceException("role_is_not_exisits", "角色不存在");
        }
        SysUser sysUser = sysUserService.selectUserByMobile(mobile);
        if (null == sysUser) {
            sysUser = sysUserService.registerAccountForMember(mobile, UserTypeEnum.getByCode(role.getRoleKey()), userName);
        } else {
            LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
            if (sysUser != null && UserTypeEnum.MANAGER.getType().equals(sysUser.getUserType()) && sysUser.getOrgId().equals(loginUser.getUser().getOrgId())) {
                throw new ServiceException("teamemployee_is_exsits", "当前邀请用户是机构负责人，不能邀请");
            }
            if (sysUser != null && UserTypeEnum.MANAGER.getType().equals(sysUser.getUserType()) && !sysUser.getOrgId().equals(loginUser.getUser().getOrgId())) {
                throw new ServiceException("teamemployee_is_exsits", "当前邀请用户已就职于其他机构，不能邀请");
            }
            // 成员管理中添加已注册无机构的用户时 更新机构以及更新用户角色
            if (sysUser != null && UserTypeEnum.DEFAULT.getType().equals(sysUser.getUserType())) {
                sysUser.setOrgId(loginUser.getUser().getOrgId());
                sysUser.setOrgName(loginUser.getUser().getOrgName());
                sysUser.setUserType(UserTypeEnum.getByCode(role.getRoleKey()).getType());
                sysUser.setUserName(userName);
                sysUserService.updateById(sysUser);
            }
        }
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(sysUser.getUserId());
        sysUserRole.setRoleId(roleId);
        sysUserRole.setCreateTime(DateUtil.currentTimeSeconds());
        sysUserRoleMapper.insert(sysUserRole);
        addTeamEmployee(mobile, userName, teamId, sysUser.getUserId(), businessType, UserTypeEnum.getByCode(role.getRoleKey()).getType());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateName(Long id, String name) {
        if (StringUtils.isEmpty(name)) {
            throw new ServiceException("param_error", "请输入成员姓名");
        }
        TeamEmployee teamEmployee = this.getOne(id);
        sysUserService.updateNameByUserId(teamEmployee.getUserId(), StringUtils.trim(name));
        this.baseMapper.updateEmployeeName(id, StringUtils.trim(name), EncryptSwitchConfig.doubleWrite);
    }

    @Override
    public TeamEmployee getOne(Long empoyeeId) {
        TeamEmployee teamEmployee = this.getById(empoyeeId);
        if (teamEmployee == null) {
            throw new ServiceException("team_employee_error", "团队成员不存在");
        }
        return teamEmployee;
    }

    /**
     * 获取当前团队下经纪人下拉tree
     *
     * @param teamId
     * @return
     */
    @Override
    public List<CommonSelect> getAgenterTree(Long teamId) {
        if (teamId == null) {
            throw new ServiceException("params_error", "团队id不能为空");
        }
        List<CommonSelect> tree = new ArrayList<>();
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        if (null == user.getOrgId()) {
            throw new ServiceException("invalid_organization", "当前机构信息无效");
        }
        Team team = teamService.detail(teamId);
        if (team == null) {
            throw new ServiceException("params_error", "当前团队不存在");
        }
        RoleVo roleVo = sysUserService.getCurrentRole(team.getType());

        // 机构管理员获取该机构下该类型所有经纪人
        if (roleVo.getRoleKey().equals(UserTypeEnum.MANAGER.getCode()) || roleVo.getRoleKey().equals(UserTypeEnum.LEADER.getCode())) {
            List<TeamEmployee> employees = this.baseMapper.getAgenterTree(teamId);
            return employees.stream().map(CommonSelect::new).collect(Collectors.toList());
        }
        // 经纪人获取当前经纪人
        else if (roleVo.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())) {
            TeamEmployee currentEmp = this.getOneByUserIdAndType(user.getUserId(), team.getType());
            CommonSelect commonSelect = new CommonSelect(currentEmp);
            tree.add(commonSelect);
        }
        return tree;
    }

    /**
     * 获取当前用户下经纪人下拉tree
     *
     * @return
     */
    @Override
    public List<CommonSelect> getAgenterTreeByAccountUuid(Integer type) {
        List<CommonSelect> tree = null;
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        if (null == user.getOrgId()) {
            throw new ServiceException("invalid_organization", "当前机构信息无效");
        }
        RoleVo roleVo = sysUserService.getCurrentRole(type);
        // 机构管理员获取该机构下该类型所有经纪人
        if (user.getUserType().equals(UserTypeEnum.MANAGER.getType())) {
            List<TeamEmployee> employees = this.baseMapper.getAgentTreeByManager(type, user.getOrgId());
            tree = employees.stream().map(CommonSelect::new).collect(Collectors.toList());
        }
        // 负责人获取经纪人所属团队下所有经纪人
        else if (roleVo.getRoleKey().equals(UserTypeEnum.LEADER.getCode())) {
            Long teamId = this.baseMapper.teamIdByAccountUuid(user.getAccountUuid(), type);
            if (null == teamId) {
                throw new ServiceException("invalid_user", "当前用户无所属团队");
            }
            tree = getAgenterTree(teamId);
        }
        // 经纪人获取当前经纪人
        else if (roleVo.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())) {
            List<TeamEmployee> employees = this.baseMapper.getAgentTreeByAgent(type, user.getAccountUuid());
            tree = employees.stream().map(CommonSelect::new).collect(Collectors.toList());
        }
        return tree;
    }


    /**
     * 获取当前用户下经纪人下拉tree.姓名和uuid
     *
     * @return
     */
    @Override
    public List<Map<String, String>> getAgenterForLogin(Integer type) {
        if (type == null) {
            type = 1;
        }
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        if (null == user.getOrgId()) {
            throw new ServiceException("invalid_organization", "当前机构信息无效");
        }
        RoleVo roleVo = sysUserService.getCurrentRole(type);
        // 机构管理员获取该机构下该类型所有经纪人
        List<Map<String, String>> usersMap = Lists.newArrayList();
        if (user.getUserType().equals(UserTypeEnum.MANAGER.getType())) {
            List<TeamEmployee> employees = this.baseMapper.getAgentTreeByManager(type, user.getOrgId());
            List<Long> userIds = employees.stream().map(TeamEmployee::getUserId).collect(Collectors.toList());
            List<SysUser> userListByIds = sysUserService.getUserListByIds(userIds);
            if (CollectionUtils.isEmpty(userListByIds)) {
                return Lists.newArrayList();
            }
            userListByIds.stream().forEach(item -> {
                Map map = new HashMap<String, String>();
                map.put("label", item.getUserName());
                map.put("value", item.getAccountUuid());
                usersMap.add(map);
            });
            return usersMap;
        }
        // 负责人获取经纪人所属团队下所有经纪人
        else if (roleVo.getRoleKey().equals(UserTypeEnum.LEADER.getCode())) {
            Long teamId = this.baseMapper.teamIdByAccountUuid(user.getAccountUuid(), type);
            if (null == teamId) {
                throw new ServiceException("invalid_user", "当前用户无所属团队");
            }
            List<TeamEmployee> employees = this.baseMapper.getAgenterTree(teamId);
            List<Long> userIds = employees.stream().map(TeamEmployee::getUserId).collect(Collectors.toList());
            List<SysUser> userListByIds = sysUserService.getUserListByIds(userIds);
            if (CollectionUtils.isEmpty(userListByIds)) {
                return Lists.newArrayList();
            }
            userListByIds.stream().forEach(item -> {
                Map map = new HashMap<String, String>();
                map.put("label", item.getUserName());
                map.put("value", item.getAccountUuid());
                usersMap.add(map);
            });
            return usersMap;

        }
        // 经纪人获取当前经纪人
        else if (roleVo.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())) {
            Map map = new HashMap<String, String>();
            map.put("label", user.getUserName());
            map.put("value", user.getAccountUuid());
            usersMap.add(map);
            return usersMap;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agentDeparture(Long employeeId) {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        TeamEmployee employeeFree = employeeMapper.selectById(employeeId);

        if (employeeFree.getUserId().equals(loginUser.getUser().getUserId())) {
            throw new ServiceException("userType_error", "您不能自行离职");
        }
        Team detail = teamService.detail(employeeFree.getTeamId());
        if (detail == null) {
            throw new ServiceException("userType_error", "当前人员");
        }
        RoleVo currentRole = sysUserService.getCurrentRole(detail.getType());

        //判断当前人员是否为负责人，经纪人没有权限操作
        if (loginUser.getUser().getUserType().equals(UserTypeEnum.DEFAULT.getType()) || currentRole.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())) {
            throw new ServiceException("userType_error", "您没有权限让该成员离职");
        } else {
            List<String> teamHosts = teamHostMapper.getHostUuidsByAgenter(employeeId);
            if (CollectionUtils.isNotEmpty(teamHosts)) {
                throw new ServiceException("error", "该经纪人手下还有主播，没有主播的经纪人才允许离职，请先调整主播分配");
            }
            TeamEmployee employeeLogin = new TeamEmployee();
            if (!loginUser.getUser().getUserType().equals(UserTypeEnum.MANAGER.getType())) {
                Long teamId = employeeFree.getTeamId();
                Team team = teamService.detail(teamId);
                if (team == null) {
                    throw new ServiceException("userType_error", "当前成员不在团队中");
                }
                employeeLogin = employeeMapper.selectMobile(loginUser.getUser().getMobile(), team.getType(),EncryptSwitchConfig.selectByDigest);
            }
            if (loginUser.getUser().getUserType().equals(UserTypeEnum.LEADER.getType()) && !employeeFree.getTeamId().equals(employeeLogin.getTeamId())) {
                throw new ServiceException("error", "该经纪人不在您的团队，请您联系相关团队负责人进行离职操作");
            } else {
                TeamEmployee employeeTeam = new TeamEmployee();
                employeeTeam.setEmployeeId(employeeId);
                employeeTeam.setTeamId(0L);
                employeeTeam.setStatus(EmployeeStatusEnum.DEPAETURE.getValue());
                employeeMapper.deleteById(employeeTeam.getEmployeeId());

                QueryWrapper<SysUserRole> queryAgentUser = new QueryWrapper();
                queryAgentUser.eq("user_id", employeeFree.getUserId());
                List<SysUserRole> list = sysUserRoleMapper.selectList(queryAgentUser);
                if (CollectionUtils.isNotEmpty(list) && list.size() == 1) {
                    SysUser sysUserUpdate = sysUserMapper.selectById(employeeFree.getUserId());
                    sysUserUpdate.setOrgId((long) OrgIdEnum.ZERO.getValue());
                    sysUserUpdate.setOrgName("");
                    sysUserUpdate.setUserType(UserTypeEnum.DEFAULT.getType());
                    sysUserMapper.updateById(sysUserUpdate);
                    List<SysUserRole> roleList = sysUserRoleMapper.getListByUserId(employeeFree.getUserId());
                    if (CollectionUtils.isNotEmpty(list)) {
                        Set<Long> idSet = roleList.stream().map(SysUserRole::getId).collect(Collectors.toSet());
                        sysUserRoleMapper.deleteBatchIds(idSet);
                    }
                }
                if (CollectionUtils.isNotEmpty(list) && list.size() > 1) {
                    RoleVo roleByUserId = sysUserService.getRoleByUserId(employeeFree.getUserId(), detail.getType());
                    sysUserRoleMapper.deleteUserIdAndRoleId(employeeFree.getUserId(), roleByUserId.getRoleId());
                }

            }
        }
    }

    /**
     * 管理端获取经纪人下拉
     *
     * @param type
     * @return
     */
    @Override
    public List<CommonSelect> selectAgentList(Integer type) {
        List<TeamEmployeeVo> teamEmployeeVos = this.baseMapper.selectAgentList(type);
        return teamEmployeeVos.stream().map(CommonSelect::new).collect(Collectors.toList());
    }

    /**
     * 管理端获取机构下经纪人下拉
     *
     * @param type
     * @return
     */
    @Override
    public List<CommonSelect> selectAgentListByOrgId(Long orgId, Integer type) {
        List<TeamEmployeeVo> teamEmployeeVos = this.baseMapper.selectAgentListByOrgId(orgId, type);
        return teamEmployeeVos.stream().map(CommonSelect::new).collect(Collectors.toList());
    }


    @Override
    public void deleteByUserId(Long userId) {
        this.remove(Wrappers.<TeamEmployee>query().lambda().eq(TeamEmployee::getUserId, userId));
    }

    @Override
    public TeamEmyloyeeAndRoleVo getMemberById(Long employeeId) {
        TeamEmployee one = this.getOne(employeeId);
        TeamEmyloyeeAndRoleVo vo = new TeamEmyloyeeAndRoleVo();
        vo.setEmployeeId(one.getEmployeeId());
        vo.setEmployeeName(one.getEmployeeName());
        vo.setTeamId(one.getTeamId());
        vo.setOrgId(vo.getOrgId());
        vo.setMobile(one.getMobile());
        vo.setUserId(one.getUserId());
        Team detail = teamService.detail(one.getTeamId());
        RoleVo roleByUserId = sysUserService.getRoleByUserId(one.getUserId(), detail.getType());
        vo.setRoleId(roleByUserId.getRoleId());
        return vo;
    }

    @Override
    public Map<Long, String> mapEmployeeName(List<Long> employeeIdList) {

        if(CollectionUtils.isEmpty(employeeIdList)){
            return new HashMap<>();
        }

        LambdaQueryWrapper<TeamEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TeamEmployee::getEmployeeId, TeamEmployee::getEmployeeId, TeamEmployee::getEmployeeNameCipher)
                .in(TeamEmployee::getEmployeeId, employeeIdList);

        List<TeamEmployee> teamEmployeeList = this.baseMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(teamEmployeeList)){
            return new HashMap<>();
        }
        Map<Long, String> map = teamEmployeeList.stream().collect(Collectors.toMap(l -> l.getEmployeeId(), l -> l.getEmployeeName(), (key1, key2) -> key1));
        return map;
    }
}
