package cn.taqu.gonghui.system.search;

import lombok.Data;

import java.util.List;

@Data
public class SharingProfitRecordSearch {

    private Long id;

    /**
     * 主播uuid
     */
    private String hostUuid;

    private List<String> uuidList;

    private String accountUuid;

    /**
     * 调整日期开始时间
     */
    private Long startTime;

    /**
     * 调整日期结束时间
     */
    private Long endTime;

    /**
     * 状态（1-待确认，2-待生效，3已生效，4-已拒绝）
     */
    private Integer status;

    /**
     * 调整人名称
     */
    private String accountName;

    /**
     * 所属机构id
     */
    private Long orgId;

    /**
     * 所属团队
     */
    private Long teamId;

    /**
     * 调整类型（1-用户端，2-管理端）
     */
    private Integer type;

    private Integer page;
    private Integer pageSize;
}
