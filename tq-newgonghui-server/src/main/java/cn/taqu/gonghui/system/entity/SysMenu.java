package cn.taqu.gonghui.system.entity;

import cn.taqu.gonghui.common.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Data
public class SysMenu extends BaseEntity {

    /** 菜单ID */
    @TableId(value = "menu_id",type = IdType.AUTO)
    private Long menuId;

    /** 菜单名称 */
    @NotBlank(message = "菜单名称不能为空")
    @Size(min = 0, max = 50, message = "菜单名称长度不能超过50个字符")
    private String menuName;

    /** 父菜单ID */
    private Long parentId;

    /**
     * 当前菜单的所有父级菜单id
     */
    private String ancestors;

    /** 显示顺序 */
    @NotBlank(message = "显示顺序不能为空")
    private Integer orderNum;

    /** 路由地址 */
    @Size(min = 0, max = 200, message = "路由地址不能超过200个字符")
    private String routerPath;

    /** 类型（M目录 C菜单 F按钮） */
    @NotBlank(message = "菜单类型不能为空")
    private String menuType;

    /** 业务类型（1-直播，2-趣聊，3聊天室...） */
    private Integer type;

    /** 菜单图标 */
    private String icon;

    /** 显示状态（1显示 0隐藏） */
    private String visible;

    /** 菜单状态（1启用 0停用） */
    private String status;

    /** 权限字符串 */
    @Size(min = 0, max = 100, message = "权限标识长度不能超过100个字符")
    private String perms;

    /** 子菜单 */
    @TableField(exist = false)
    private List<SysMenu> children = new ArrayList<SysMenu>();

}
