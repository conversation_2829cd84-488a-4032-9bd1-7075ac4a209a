package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.common.vo.req.TransferTeamReq;
import cn.taqu.gonghui.system.dto.TeamDto;
import cn.taqu.gonghui.system.entity.BackstageOperateLog;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.search.TeamSearch;
import cn.taqu.gonghui.system.vo.TeamOrgInfoVO;
import cn.taqu.gonghui.system.vo.TeamTreeVo;
import cn.taqu.gonghui.system.vo.TeamVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
public interface TeamService {

    /**
     * 条件筛选团队列表
     *
     * @param search
     * @return
     */
    List<TeamVo> selectTeamList(TeamSearch search);

    /**
     * 获取有效团队列表
     */
    List<Team> selectTeamList(Long orgId, Integer type);

    List<TeamTreeVo> getTeamListByBusinessType(Integer type);


    List<CommonSelect> sharingRecordTeamList();

    /**
     * 获取团队下拉tree
     */
    List<CommonSelect> selectTree(Long orgId, Integer type);

    /**
     * 获取团队信息，目前是给迁移团队接口提供团队信息
     *
     * @param teamId
     * @return
     */
    TeamVo info(Long teamId);

    Team detail(Long teamId);

    /**
     * 新增团队
     *
     * @param record
     * @return
     */
    void insertTeam(Team record);

    /**
     * 更改团队信息
     *
     * @param record
     * @return
     */
    void updateTeam(Team record);

    /**
     * 删除团队
     *
     * @return
     */
    void remove(Long teamId);

    /**
     * 启用禁用团队
     *
     * @return
     */
    void startOrForbidden(Long teamId, Integer status);

    /**
     * 团队名称是否已经存在
     *
     * @return
     */
    Boolean teamNameHasExist(Team team);


    /**
     * 获取该团队下的主播的数量
     */
    Integer getHostNumbers(Long teamId);

    /**
     * 获取直播团队标识符
     */
    List<Map<String, Object>> getTeamSignList();

    /**
     * 根据机构id获取该机构下开启的业务类型
     */
    List<Integer> getTypesByOrgId(Long orgId);

    /**
     * 机构注册的时候默认为不同类型的业务都创建一个团队（团队是否开启由审批人勾选）
     *
     * @return
     */
    void insertDefaultTeam(TeamDto teamDto);

    /**
     * 机构注册的时候默认为不同类型的业务都创建一个团队（团队是否开启由审批人勾选）
     *
     * @return
     */
    void insertDefaultTeamByMove(TeamDto teamDto);

    void insertDefaultTeamByType(Long orgId, String teamName, Integer type, Integer status, String createBy, String teamSign);

    void insertDefaultTeamByTypeByMove(Long orgId, Long teamId, String teamName, Integer type, Integer status, String createBy, String teamSign);

    Team getTeamByInviteCodeAndtype(String inviteCode, Integer type);

    void deleteTeamById(Long teamId);


    List<Team> getTeamListByTeamIds(List<Long> teamIds);

    Map<Long,Team> getMapByTeamIds(List<Long> teamIds);

    Map<Long, String> mapTeamNameByTeamIds(List<Long> teamIds);

    List<Team> getChatRoomList();

    /**
     * 获取团队及所属机构基本信息（id，名称）
     * @param teamId
     * @return
     */
    TeamOrgInfoVO getTeamOrgInfoVO(Long teamId);

    /**
     * 关闭团队后置处理（发送通知）
     *
     * @param organization
     * @param operateTypeEnum
     * @param team
     * @param operator
     * @param batchId
     */
    void afterCloseTeam(Organization organization, BackstageOperateLog.OperateTypeEnum operateTypeEnum, Team team, String operator, String batchId);

    /**
     * 判断是团队否可关闭
     *
     * @param teams
     * @param batchId
     * @param operator
     */
    void checkTeamCanClose(List<Team> teams, String batchId, String operator);
    /**
     * 批量获取团队状态
     *
     * @param teamIds
     * @return
     */
    Map<Long, Integer> getTeamStatus(List<Long> teamIds);

    String getUniqueInvivationCode();

    /**
     * 转移A机构/D团队 到 B机构C团队
     * @param req
     * @return
     */
    List<TeamHost> transferTeam(TransferTeamReq req);

    List<Team> listTeamByOrgIdList(List<Long> orgIdList, int value);

    Map<Long, String> mapOrgNameByTeamId(Set<Long> teamIdSet);

    Map<Long, Long> mapOrgIdByTeamId(Set<Long> teamIdSet);

    Map<Long, String> MapName(List<Long> teamIds);

    Map<Long, Long> MapOrgId(List<Long> teamIdList);
}
