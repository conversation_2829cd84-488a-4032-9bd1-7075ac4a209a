package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.dto.RecommendApplyCardDto;
import cn.taqu.gonghui.system.entity.RecommendApplyCard;
import cn.taqu.gonghui.system.search.RecommendApplyCardSearch;
import cn.taqu.gonghui.system.vo.RecommendApplyCardVo;

import java.util.List;
import java.util.Map;

/**
 * 营销管理（推荐位置管理）
 */
public interface RecommendApplyCardService {

    /**
     * 分页列表查询
     * @param search
     * @return
     */
    List<RecommendApplyCardVo> pageList(RecommendApplyCardSearch search);

    /**
     * 作废操作
     * @param status
     * @param id
     */
    void updateStatus(Integer status,Long id);

    /**
     * 发放申请卡
     * @param applyCardDto
     */
    void sendApplyCard(RecommendApplyCardDto applyCardDto);

    // ---------------- 以下方法为用户端使用 ---------------------
    void setCardValid();


    Map<String, Object> getHomeRecommendApplyCardInfo();
}
