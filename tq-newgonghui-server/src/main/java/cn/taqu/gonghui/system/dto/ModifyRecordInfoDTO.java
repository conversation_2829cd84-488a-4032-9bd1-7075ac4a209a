package cn.taqu.gonghui.system.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-13 11:31
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ModifyRecordInfoDTO {
    /**
     * 老机构id
     */
    private Long oldOrgId;

    /**
     * 老团队id
     */
    private Long oldTeamId;
    /**
     * 新机构id
     */
    private Long newOrgId;
    /**
     * 新团队id
     */
    private Long newTeamId;
    /**
     * 理由
     */
    private String reason;
    /**
     * 迁移文件
     */
    private String files;

    /**
     * 老机构名称
     */
    private String oldOrgName;
}
