package cn.taqu.gonghui.system.search;

import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/4/30
 */
@Data
public class TeamEmployeeSearch {


    private Long orgId;

    private Long teamId;

    /**
     * 团队成员类型 2 负责人 3 经纪人
     */
    private Integer type;

    private String mobile;

    /**
     * 业务类型（1-直播，2-趣聊，3-聊天室....）
     */
    private Integer teamType;

    private String employeeName;

    private Long roleId;

    private Long employeeId;

    private List<Long> userIdList;

    /**
     * 是否开启查询按摘要条件（针对加密字段）
     */
    private Integer selectByDigest;
    public Integer getSelectByDigest() {
        return EncryptSwitchConfig.selectByDigest;
    }
}
