package cn.taqu.gonghui.system.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CommonHostInfo {

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 主播id (问过同事 说是和accountUuid 先保留)
     */
    private String hostUuid;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 公会id
     */
    private Long teamId;

    /**
     * 团队负责人id sys_user.account_uuid
     */
    private String teamAccountUuid;

    /**
     * 机构负责人 用的是 sys_user.account_uuid
     */
    private String orgAccountUuid;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 等级
     */
    private String applyLevel;

    /**
     * 直播号 他趣id
     */
    private String liveNo;

    /**
     * 团队负责人手机号
     */
    private String teamMobile;

    /**
     * 机构手机号
     */
    private String orgMobile;

}
