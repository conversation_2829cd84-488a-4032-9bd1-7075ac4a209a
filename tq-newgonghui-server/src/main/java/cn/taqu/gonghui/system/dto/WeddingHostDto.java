package cn.taqu.gonghui.system.dto;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> Wu.D.J
 */
@ToString
@Data
public class WeddingHostDto {
    // 婚礼日期开始时间
    private Long start_time;

    // 婚礼日期结束时间
    private Long end_time;

    // 婚礼UUID
    private String wedding_uuid;

    // 司仪UUID
    private String host_uuid;

    // 婚礼状态 0-全状态 1-进行中 2-未开始 3-结束 4-取消
    private Integer wedding_status;

    // 0-全部 1-游戏礼物流水 2-非游戏礼物流水
    private Integer income_type;

    // 司仪公会ID
    private Long consortia_id;

}
