package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.gonghui.common.constant.HostTypeEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.vo.*;
import cn.taqu.gonghui.soa.GonghuiService;
import cn.taqu.gonghui.soa.InfoService;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import cn.taqu.gonghui.system.search.HostStatisticSearch;
import cn.taqu.gonghui.system.search.LiveHostSelfStatisticSearch;
import cn.taqu.gonghui.system.search.SharingProfitRecordSearch;
import cn.taqu.gonghui.system.service.OrgStatisticUserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class OrgStatisticUserServiceImpl implements OrgStatisticUserService {
    private static Logger logger = LoggerFactory.getLogger(OrgStatisticUserServiceImpl.class);

    @SoaReference(application = "liveV1",value = "liveV1")
    private GonghuiService gonghuiService;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @SoaReference("account")
    private InfoService infoService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private TeamEmployeeMapper teamEmployeeMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private HostSharingProfitRecordMapper sharingProfitRecordMapper;

    @Override
    public HostVo getLiveHostListForAdmin(HostStatisticSearch search) {
        TeamHost teamHostSearch =new TeamHost();
        List<String> agentUuids =  new ArrayList<>();
        List<String> uuidList = new ArrayList<>();
        if (StringUtils.isNotBlank(search.getHost_uuid())) {
            teamHostSearch.setHostUuid(search.getHost_uuid());
        }
        if(StringUtils.isNotEmpty(search.getPhone())){
            Map<String, String> map = infoService.getUuidAndNameByMobile(search.getPhone());
            String uuid = String.valueOf(map.getOrDefault("uuid", ""));
            teamHostSearch.setHostUuid(uuid);
        }
        if(StringUtils.isNotEmpty(search.getConsortiaId())){
            List<Long> teams = teamMapper.selectTeamListIds(Long.parseLong(search.getConsortiaId()), TeamTypeEnum.LIVE_TEAM.getValue());
            if(CollectionUtils.isNotEmpty(teams)){
                search.setTeamIds(teams);
            }else{
                return new HostVo();
            }
        }else {
            List<Long> teams = teamMapper.selectTeamListIds(null,TeamTypeEnum.LIVE_TEAM.getValue());
            if(CollectionUtils.isNotEmpty(teams)){
                search.setTeamIds(teams);
            }else{
                return  new HostVo();
            }
        }
        if(StringUtils.isNotEmpty(search.getApply_level())){
            teamHostSearch.setApplyLevel(search.getApply_level());

            }
            if (search.getAgentIdForAdmin() != null && search.getAgentIdForAdmin() != 0) {
                uuidList.clear();
                TeamEmployee employee = teamEmployeeMapper.selectById(search.getAgentIdForAdmin());
                SysUser sysUser = sysUserMapper.selectUserByMobile(employee.getMobile(), EncryptSwitchConfig.selectByDigest);
                if (sysUser != null && StringUtils.isNotEmpty(sysUser.getAccountUuid())) {
                    agentUuids.add(sysUser.getAccountUuid());
                } else {
                    agentUuids.add("no agentuuids");
                }
                search.setBusinessmanUuids(agentUuids);
            }
            if (StringUtils.isNotBlank(teamHostSearch.getHostUuid())) {
                uuidList.clear();
                uuidList.add(teamHostSearch.getHostUuid());
            }
            if (StringUtils.isNotEmpty(search.getLive_status())) {
                teamHostSearch.setLiveStatus(search.getLive_status());
            }
            HostVo hostVos = gonghuiService.getHostList(search.getTeamIds(), search.getBusinessmanUuids(), uuidList, search.getPage(), search.getRows(), teamHostSearch.getLiveStatus(), null, null, teamHostSearch.getHostStatus(), null, null, teamHostSearch.getApplyLevel(), null, search.getStart(), search.getEnd(), search.getCreateTimeStart(), search.getCreateTimeEnd(), null);
            if (hostVos.getList().size() != 0) {
                Iterator<HostVo.DataVo> it = hostVos.getList().iterator();
                while(it.hasNext()) {
                    HostVo.DataVo hostEach = it.next();
                //hostVos.getList().forEach(hostEach -> {
                    TeamHost teamHost = teamHostMapper.getOneByHostUuid(hostEach.getHost_uuid(),TeamTypeEnum.LIVE_TEAM.getValue());
                    // 此处过滤查询条件
                    if (Objects.nonNull(search.getHostType()) && !teamHost.getHostType().equals(search.getHostType())) {
                        it.remove();
                        continue;
                    }
                    if (teamHost != null) {
                        Team team = teamMapper.selectByPrimaryKey(teamHost.getTeamId());
                        if (team != null) {
                            hostEach.setTeamName(team.getTeamName());
                        }
                        Organization organization = organizationMapper.selectByPrimaryKey(teamHost.getOrgId());
                        if (organization != null) {
                            hostEach.setOrgName(organization.getOrgName());
                        }
                        hostEach.setAgentName("");
                        TeamEmployee teamEmployee = teamEmployeeMapper.selectById(teamHost.getEmployeeId());
                        if (teamEmployee != null) {
                            if (StringUtils.isNotEmpty(teamEmployee.getEmployeeName())) {
                                hostEach.setAgentName(teamEmployee.getEmployeeName());
                            }
                        }
                        hostEach.setHostType(teamHost.getHostType());
                        hostEach.setHostTypeName(HostTypeEnum.getName(teamHost.getHostType()));
                        hostEach.setIsGroup(teamHost.getIsGroup());

                   // 查询是否有分润记录
                   SharingProfitRecordSearch sharingSearch = new SharingProfitRecordSearch();
                   sharingSearch.setHostUuid(hostEach.getHost_uuid());
                   sharingSearch.setOrgId(teamHost.getOrgId());
                   HostSharingProfitRecord oneRecordBySearch = sharingProfitRecordMapper.getLastedOneRecord(sharingSearch);
                   if (null != oneRecordBySearch) {
                       hostEach.setOldSharingRate(oneRecordBySearch.getCurrentSharingProfitRate());
                       hostEach.setNewSharingRate(oneRecordBySearch.getNewSharingProfitRate());
                       hostEach.setSharingStatus(oneRecordBySearch.getStatus());
                   }
                   hostEach.setCurrentSharingRate(teamHost.getCurrentSharingProfitRate());
               }
                }
           //});
       }
        hostVos.setTotal(hostVos.getList().size());
        return hostVos;
    }


        @Override
        public LiveHostSelfStatisticVo getLiveHostSelfStatistic (LiveHostSelfStatisticSearch search){
            if (cn.taqu.gonghui.common.utils.StringUtils.isNotEmpty(search.getEndTime()) && cn.taqu.gonghui.common.utils.StringUtils.isNotEmpty(search.getStartTime())) {
                String newStartTime = search.getStartTime() + " 00:00:00";
                String newEndTime = search.getEndTime() + " 23:59:59";
                search.setEndTime(newEndTime);
                search.setStartTime(newStartTime);
            }
            LiveHostSelfStatisticVo liveHostSelfStatisticVo = gonghuiService.getHostLiveTimeStat(search.getHostUuid(), search.getStartTime(), search.getEndTime());
            TeamHost host = teamHostMapper.getOneByHostUuid(search.getHostUuid(),TeamTypeEnum.LIVE_TEAM.getValue());
            if (host != null) {
                Team team = teamMapper.selectById(host.getTeamId());
                Organization organization = organizationMapper.selectByPrimaryKey(host.getOrgId());
                TeamEmployee teamEmployee = teamEmployeeMapper.selectById(host.getEmployeeId());
                liveHostSelfStatisticVo.setOrgName(organization.getOrgName());
                liveHostSelfStatisticVo.setTeamName(team.getTeamName());
                liveHostSelfStatisticVo.setAgentName(teamEmployee == null ? "" : teamEmployee.getEmployeeName());
                liveHostSelfStatisticVo.setTeamType(host.getTeamType());
                liveHostSelfStatisticVo.setCurrentSharingProfitRate(host.getCurrentSharingProfitRate());
                liveHostSelfStatisticVo.setNewSharingProfitRate(host.getNewSharingProfitRate());
            }
            return liveHostSelfStatisticVo;
        }

}
