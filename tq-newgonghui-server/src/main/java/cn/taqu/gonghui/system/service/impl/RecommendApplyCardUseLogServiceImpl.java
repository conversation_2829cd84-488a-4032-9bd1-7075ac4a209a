package cn.taqu.gonghui.system.service.impl;

import cn.taqu.gonghui.system.entity.RecommendApplyCardUseLog;
import cn.taqu.gonghui.system.mapper.RecommendApplyCardUseLogMapper;
import cn.taqu.gonghui.system.search.RecommendApplyCardUseLogSearch;
import cn.taqu.gonghui.system.service.RecommendApplyCardUseLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 申请卡使用记录管理
 */
@Service
public class RecommendApplyCardUseLogServiceImpl extends ServiceImpl<RecommendApplyCardUseLogMapper,RecommendApplyCardUseLog> implements RecommendApplyCardUseLogService {

    private static Logger logger = LoggerFactory.getLogger(RecommendApplyCardUseLogServiceImpl.class);

    /**
     * 根据申请卡编号查询使用记录
     * @return
     */
    @Override
    public List<RecommendApplyCardUseLog> pageList(RecommendApplyCardUseLogSearch search){
        PageHelper.startPage(search.getPageNum() == null ? 1 : search.getPageNum(),
                search.getPageSize() == null ? 20 : search.getPageSize());
        List<RecommendApplyCardUseLog> dataList = this.baseMapper.queryByCondition(search);
        dataList.stream().forEach(data -> {
            if (Objects.equals("100", data.getBanner())) {
                data.setBanner("热2 ");
            }
        });
        return dataList;
    }
}
