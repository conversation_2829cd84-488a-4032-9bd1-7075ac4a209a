package cn.taqu.gonghui.system.service.impl;

import cn.taqu.gonghui.common.constant.callback.feishu.FeiShuRelationType;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import cn.taqu.gonghui.system.service.IOrgBankLogService;
import cn.taqu.gonghui.system.service.OrganizationLogService;
import cn.taqu.gonghui.system.service.OrganizationService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @date: 2022/9/19
 * @Description:
 */
@Service
@Slf4j
public class OrganizationLogServiceImpl implements OrganizationLogService {
    @Autowired
    OrgNameLogMapper orgNameLogMapper;
    @Autowired
    OrgOperatorLogMapper orgOperatorLogMapper;
    @Autowired
    OrgBankLogMapper orgBankLogMapper;
    @Autowired
    OrgAccountLogMapper orgAccountLogMapper;
    @Autowired
    LegalPersonMapper legalPersonMapper;
    @Autowired
    private ChargePersonMapper chargePersonMapper;
    @Autowired
    private OrgCompanyLogMapper orgCompanyLogMapper;
    @Autowired
    private BusinessLicenseMapper businessLicenseMapper;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationMapper organizationMapper;

    @Resource
    private IOrgBankLogService iOrgBankLogService;

    @Override
    public void addLog(Organization newOrganization, Organization oldOrganization, String operator) {
        OrgNameLog orgNameLog = new OrgNameLog();
        OrgOperatorLog orgOperatorLog = new OrgOperatorLog();
        OrgBankLog orgBankLog = new OrgBankLog();
        OrgAccountLog orgAccountLog = new OrgAccountLog();
        OrgCompanyLog orgCompanyLog = new OrgCompanyLog();

        orgNameLog.setCreateOperator(operator);
        orgOperatorLog.setCreateOperator(operator);
        orgBankLog.setCreateOperator(operator);
        orgAccountLog.setCreateOperator(operator);
        orgCompanyLog.setCreateOperator(operator);

        orgNameLog.setRelevanceId(oldOrganization.getOrgId());
        orgOperatorLog.setRelevanceId(oldOrganization.getOrgId());
        orgBankLog.setRelevanceId(oldOrganization.getOrgId());
        orgAccountLog.setRelevanceId(oldOrganization.getOrgId());
        orgCompanyLog.setRelevanceId(oldOrganization.getOrgId());

        boolean orgNameLogFlag = false;
        boolean orgOperatorLogFlag = false;
        boolean orgBankLogFlag = false;
        boolean orgAccountLogFlag = false;
        boolean orgCompanyLogFlag = false;

        //机构名字修改记录
        if (newOrganization.getOrgName() != null && !newOrganization.getOrgName().equals(oldOrganization.getOrgName())) {
            orgNameLog.setNewOrgName(newOrganization.getOrgName());
            orgNameLog.setOldOrgName(oldOrganization.getOrgName());
            orgNameLogFlag = true;
        }
        //运营人员修改记录
        if (newOrganization.getBusinessPerson() != null && !newOrganization.getBusinessPerson().equals(oldOrganization.getBusinessPerson())) {
            orgOperatorLog.setNewOperation(newOrganization.getBusinessPerson());
            orgOperatorLog.setOldOperation(StringUtils.isEmpty(oldOrganization.getBusinessPerson()) ? newOrganization.getBusinessPerson() : oldOrganization.getBusinessPerson());
            orgOperatorLogFlag = true;
        }
        //银行信息修改记录
        if (newOrganization.getPublicReceivingBankAccount() != null && !newOrganization.getPublicReceivingBankAccount().equals(oldOrganization.getPublicReceivingBankAccount())) {
            orgBankLog.setNewAccount(newOrganization.getPublicReceivingBankAccount());
            orgBankLog.setOldAccount(oldOrganization.getPublicReceivingBankAccount());
            orgBankLogFlag = true;
        }

        if ((newOrganization.getProvince() != null && !newOrganization.getProvince().equals(oldOrganization.getProvince())) || newOrganization.getCity() != null && !newOrganization.getCity().equals(oldOrganization.getCity())) {
            orgBankLog.setNewAddress(newOrganization.getProvince() + ":" + newOrganization.getProvinceId() + ";" + newOrganization.getCity() + ":" + newOrganization.getCityId());
            orgBankLog.setOldAddress(oldOrganization.getProvince() + ":" + oldOrganization.getProvinceId() + ";" + oldOrganization.getCity() + ":" + oldOrganization.getCityId());
            orgBankLogFlag = true;
        }

        if (newOrganization.getSubBranchName() != null && !newOrganization.getSubBranchName().equals(oldOrganization.getSubBranchName())) {
            orgBankLog.setNewBankName(newOrganization.getSubBranchName());
            orgBankLog.setOldBankName(oldOrganization.getSubBranchName());
            orgBankLogFlag = true;

        }

        if (newOrganization.getAccountBankName() != null && !newOrganization.getAccountBankName().equals(oldOrganization.getAccountBankName())) {
            orgBankLog.setNewBank(newOrganization.getAccountBankName());
            orgBankLog.setOldBank(oldOrganization.getAccountBankName());
            orgBankLogFlag = true;

        }

        if (newOrganization.getAccountName() != null && !newOrganization.getAccountName().equals(oldOrganization.getAccountName())) {
            orgBankLog.setNewAccountName(newOrganization.getAccountName());
            orgBankLog.setOldAccountName(oldOrganization.getAccountName());
            orgBankLogFlag = true;
        }
        // orgAccountLog 机构用户信息修改
        if (newOrganization.getChargePersonPhone() != null && !newOrganization.getChargePersonPhone().equals(oldOrganization.getChargePersonPhone())) {
            orgAccountLog.setNewPhone(newOrganization.getChargePersonPhone());
            orgAccountLog.setOldPhone(oldOrganization.getChargePersonPhone());
            orgAccountLogFlag = true;
        }

        if (newOrganization.getChargePerson() != null && !newOrganization.getChargePerson().equals(oldOrganization.getChargePerson())) {
            orgAccountLog.setNewPrincipal(newOrganization.getChargePerson());
            orgAccountLog.setOldPrincipal(oldOrganization.getChargePerson());
            orgAccountLogFlag = true;
        }
        if (newOrganization.getContactPhone() != null && !newOrganization.getContactPhone().equals(oldOrganization.getContactPhone())) {
            orgAccountLog.setNewMobilePhone(newOrganization.getContactPhone());
            orgAccountLog.setOldMobilePhone(oldOrganization.getContactPhone());
            orgAccountLogFlag = true;
        }
        if (newOrganization.getChargePersonVx() != null && !newOrganization.getChargePersonVx().equals(oldOrganization.getChargePersonVx())) {
            orgAccountLog.setNewWeixin(newOrganization.getChargePersonVx());
            orgAccountLog.setOldWeixin(oldOrganization.getChargePersonVx());
            orgAccountLogFlag = true;
        }
        if (newOrganization.getChargePersonBirthday() != null && !newOrganization.getChargePersonBirthday().equals(oldOrganization.getChargePersonBirthday())) {
            orgAccountLog.setNewBirth(newOrganization.getChargePersonBirthday());
            orgAccountLog.setOldBirth(oldOrganization.getChargePersonBirthday());
            orgAccountLogFlag = true;
        }
        if (newOrganization.getLegalPersonIdCard() != null && !newOrganization.getLegalPersonIdCard().equals(oldOrganization.getLegalPersonIdCard())) {
            orgAccountLog.setNewIdentity(newOrganization.getLegalPersonIdCard());
            orgAccountLog.setOldIdentity(oldOrganization.getLegalPersonIdCard());
        }
        if (newOrganization.getChargePersonIdCard() != null && !newOrganization.getChargePersonIdCard().equals(oldOrganization.getChargePersonIdCard())) {
            orgAccountLog.setNewIdentity(newOrganization.getChargePersonIdCard());
            orgAccountLog.setOldIdentity(oldOrganization.getChargePersonIdCard());
            log.debug("orgAccountLog={}", orgAccountLog);
            orgAccountLogFlag = true;
        }
        if (newOrganization.getReceivingAddress() != null && !newOrganization.getReceivingAddress().equals(oldOrganization.getReceivingAddress())) {
            orgAccountLog.setNewSite(newOrganization.getReceivingAddress());
            orgAccountLog.setOldSite(oldOrganization.getReceivingAddress());
            orgAccountLogFlag = true;
        }
        if (newOrganization.getReceivingAddress() != null && !newOrganization.getReceivingAddress().equals(oldOrganization.getReceivingAddress())) {
            orgAccountLog.setNewSite(newOrganization.getReceivingAddress());
            orgAccountLog.setOldSite(oldOrganization.getReceivingAddress());
            orgAccountLogFlag = true;
        }
        if (newOrganization.getChargePersonEmail() != null && !newOrganization.getChargePersonEmail().equals(oldOrganization.getChargePersonEmail())) {
            orgAccountLog.setNewMailbox(newOrganization.getChargePersonEmail());
            orgAccountLog.setOldMailbox(oldOrganization.getChargePersonEmail());
            orgAccountLogFlag = true;
        }

        if (newOrganization.getChargePersonUrl() != null) {
            String[] urls = newOrganization.getChargePersonUrl().split(",");
            if (StringUtils.isNotEmpty(oldOrganization.getChargePersonUrl()) && oldOrganization.getChargePersonUrl().split(",").length == 3) {
                String[] urlsOld = oldOrganization.getChargePersonUrl().split(",");
                if (!urlsOld[0].equals(urls[0])) {
                    orgAccountLog.setNewIdentityFront(urls[0]);
                    orgAccountLog.setOldIdentityFront(urlsOld[0]);
                }
                if (!urlsOld[1].equals(urls[1])) {
                    orgAccountLog.setNewIdentityReverse(urls[1]);
                    orgAccountLog.setOldIdentityReverse(urlsOld[1]);
                }

                if (!urlsOld[2].equals(urls[2])) {
                    orgAccountLog.setNewIdentityHand(urls[2]);
                    orgAccountLog.setOldIdentityHand(urlsOld[2]);
                }

            } else {
                orgAccountLog.setNewIdentityFront(urls[0]);
                orgAccountLog.setNewIdentityReverse(urls[1]);
                orgAccountLog.setNewIdentityHand(urls[2]);
            }
            orgAccountLogFlag = true;
        }

        if (newOrganization.getEnterpriseName() != null && !newOrganization.getEnterpriseName().equals(oldOrganization.getEnterpriseName())) {
            orgCompanyLog.setNewEnterpriseName(newOrganization.getEnterpriseName());
            orgCompanyLog.setOldEnterpriseName(oldOrganization.getEnterpriseName());
            orgCompanyLogFlag = true;
        }
        if (newOrganization.getLegalPerson() != null && !newOrganization.getLegalPerson().equals(oldOrganization.getLegalPerson())) {
            orgCompanyLog.setNewLegalPerson(newOrganization.getLegalPerson());
            orgCompanyLog.setOldLegalPerson(oldOrganization.getLegalPerson());
            orgCompanyLogFlag = true;
        }
        if (newOrganization.getLegalPersonPhone() != null && !newOrganization.getLegalPersonPhone().equals(oldOrganization.getLegalPersonPhone())) {
            orgCompanyLog.setNewLegalPersonPhone(newOrganization.getLegalPersonPhone());
            orgCompanyLog.setOldLegalPersonPhone(oldOrganization.getLegalPersonPhone());
            orgCompanyLogFlag = true;
        }
        if (newOrganization.getLegalPersonPhone() != null && !newOrganization.getLegalPersonPhone().equals(oldOrganization.getLegalPersonPhone())) {
            orgCompanyLog.setNewLegalPersonPhone(newOrganization.getLegalPersonPhone());
            orgCompanyLog.setOldLegalPersonPhone(oldOrganization.getLegalPersonPhone());
            orgCompanyLogFlag = true;
        }
        if (newOrganization.getLegalPersonIdCard() != null && !newOrganization.getLegalPersonIdCard().equals(oldOrganization.getLegalPersonIdCard())) {
            orgCompanyLog.setNewLegalPersonidCard(newOrganization.getLegalPersonIdCard());
            orgCompanyLog.setOldLegalPersonidCard(oldOrganization.getLegalPersonIdCard());
            orgAccountLogFlag = true;
        }
        if (newOrganization.getLegalPersonUrl() != null) {
            String[] urls = newOrganization.getLegalPersonUrl().split(",");
            if (!StringUtils.isEmpty(oldOrganization.getLegalPersonUrl()) && oldOrganization.getLegalPersonUrl().split(",").length == 3) {
                String[] urlsOld = oldOrganization.getLegalPersonUrl().split(",");
                if (!urlsOld[0].equals(urls[0])) {
                    orgCompanyLog.setNewLegalPersonUrlFront(urls[0]);
                    orgCompanyLog.setOldLegalPersonUrlFront(urlsOld[0]);
                }
                if (!urlsOld[1].equals(urls[1])) {
                    orgCompanyLog.setNewLegalPersonUrlReverse(urls[1]);
                    orgCompanyLog.setOldLegalPersonUrlReverse(urlsOld[1]);
                }

                if (!urlsOld[2].equals(urls[2])) {
                    orgCompanyLog.setNewLegalPersonUrlHand(urls[2]);
                    orgCompanyLog.setOldLegalPersonUrlHand(urlsOld[2]);
                }
            } else {
                orgCompanyLog.setNewLegalPersonUrlFront(urls[0]);
                orgCompanyLog.setNewLegalPersonUrlReverse(urls[1]);
                orgCompanyLog.setNewLegalPersonUrlHand(urls[2]);
            }
            orgCompanyLogFlag = true;
        }

        if (newOrganization.getBusinessLicenseUrl() != null) {
            if (oldOrganization.getBusinessLicenseUrl() != null) {
                if (!newOrganization.getBusinessLicenseUrl().equals(oldOrganization.getBusinessLicenseUrl())) {
                    orgCompanyLog.setNewBusinessLicenseUrl(newOrganization.getBusinessLicenseUrl());
                    orgCompanyLog.setOldBusinessLicenseUrl(oldOrganization.getBusinessLicenseUrl());
                }
            } else {
                orgCompanyLog.setNewBusinessLicenseUrl(newOrganization.getBusinessLicenseUrl());
            }
            orgCompanyLogFlag = true;
        }

        if (newOrganization.getSocialUnifiedCreditCode() != null && !newOrganization.getSocialUnifiedCreditCode().equals(oldOrganization.getSocialUnifiedCreditCode())) {
            orgCompanyLog.setNewSocialUnifiedCreditCode(newOrganization.getSocialUnifiedCreditCode());
            orgCompanyLog.setOldSocialUnifiedCreditCode(oldOrganization.getSocialUnifiedCreditCode());
            orgCompanyLogFlag = true;
        }
        if (newOrganization.getPremises() != null && !newOrganization.getPremises().equals(oldOrganization.getPremises())) {
            orgCompanyLog.setNewPremises(newOrganization.getPremises());
            orgCompanyLog.setOldPremises(oldOrganization.getPremises());
            orgCompanyLogFlag = true;
        }

        if (orgNameLogFlag) {
            log.debug("orgNameLogFlag={}", orgNameLogFlag);
            orgNameLogMapper.insert(orgNameLog);
        }
        if (orgOperatorLogFlag) {
            log.debug("orgOperatorLogFlag={}", orgOperatorLogFlag);
            orgOperatorLogMapper.insert(orgOperatorLog);
        }
        if (orgBankLogFlag) {
            log.debug("orgBankLogFlag={}", orgBankLogFlag);
            orgBankLogMapper.insert(orgBankLog);
        }
        if (orgAccountLogFlag) {
            log.debug("orgAccountLogFlag={}", orgAccountLogFlag);
            orgAccountLogMapper.insert(orgAccountLog);
        }
        if (orgCompanyLogFlag) {
            log.debug("orgCompanyLogFlag={}", orgCompanyLogFlag);
            orgCompanyLogMapper.insert(orgCompanyLog);
        }
    }

        @Override
        public void getModifyInfo (Organization organization, FeishuAuditRelation relation){
            if (relation.getBusinessType() == FeiShuRelationType.MODIFY_ORG.ordinal()) {
                // 修改机构信息
                OrgAccountLog accountLog = orgAccountLogMapper.getLastByRelevanceId(organization.getOrgId());
                if (accountLog == null) {
                    return;
                }
                this.setOrgValue(accountLog.getNewPhone(), organization::setChargePersonPhone);
                this.setOrgValue(accountLog.getNewPrincipal(), organization::setChargePerson);
                this.setOrgValue(accountLog.getNewMobilePhone(), organization::setContactPhone);
                this.setOrgValue(accountLog.getNewWeixin(), organization::setChargePersonVx);
                this.setOrgValue(accountLog.getNewBirth(), organization::setChargePersonBirthday);
                this.setOrgValue(accountLog.getNewIdentity(), organization::setChargePersonIdCard);
                this.setOrgValue(accountLog.getNewSite(), organization::setReceivingAddress);
                this.setOrgValue(accountLog.getNewMailbox(), organization::setChargePersonEmail);
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(accountLog.getNewIdentityFront())
                        || org.apache.commons.lang3.StringUtils.isNotEmpty(accountLog.getNewIdentityReverse())
                        || org.apache.commons.lang3.StringUtils.isNotEmpty(accountLog.getNewIdentityHand())) {
                    List<ChargePerson> personList = chargePersonMapper.selectByOrgUuid(organization.getOrgUuid());
                    if (personList.size() != 3) {
                        organization.setChargePersonUrl(String.join(",", accountLog.getNewIdentityFront(), accountLog.getNewIdentityReverse(), accountLog.getNewIdentityHand()));
                    } else {
                        List<String> urlList = personList.stream().map(ChargePerson::getUrl).collect(Collectors.toList());
                        this.setOrgValue(accountLog.getNewIdentityFront(), s -> urlList.set(0, s));
                        this.setOrgValue(accountLog.getNewIdentityReverse(), s -> urlList.set(1, s));
                        this.setOrgValue(accountLog.getNewIdentityHand(), s -> urlList.set(2, s));
                        organization.setChargePersonUrl(String.join(",", urlList));
                    }
                    organizationService.saveChargePerson(organization.getOrgUuid(), organization.getChargePersonUrl());
                }
            }
            if (relation.getBusinessType() == FeiShuRelationType.MODIFY_CERT.ordinal()) {
                OrgCompanyLog companyLog = orgCompanyLogMapper.getLastByRelevanceId(organization.getOrgId());
                if (companyLog == null) {
                    return;
                }
                this.setOrgValue(companyLog.getNewEnterpriseName(), organization::setEnterpriseName);
                this.setOrgValue(companyLog.getNewLegalPerson(), organization::setLegalPerson);
                this.setOrgValue(companyLog.getNewLegalPersonPhone(), organization::setLegalPersonPhone);
                this.setOrgValue(companyLog.getNewLegalPersonidCard(), organization::setLegalPersonIdCard);
                this.setOrgValue(companyLog.getNewBusinessLicenseUrl(), organization::setBusinessLicenseUrl);
                this.setOrgValue(companyLog.getNewSocialUnifiedCreditCode(), organization::setSocialUnifiedCreditCode);
                this.setOrgValue(companyLog.getNewPremises(), organization::setPremises);
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(companyLog.getNewLegalPersonUrlFront())
                        || org.apache.commons.lang3.StringUtils.isNotEmpty(companyLog.getNewLegalPersonUrlReverse())
                        || org.apache.commons.lang3.StringUtils.isNotEmpty(companyLog.getNewLegalPersonUrlHand())) {
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(organization.getLegalPersonUrl())
                    && organization.getLegalPersonUrl().split(",").length == 3) {
                        String[] oldUrls = organization.getLegalPersonUrl().split(",");
                        this.setOrgValue(companyLog.getNewLegalPersonUrlFront(), s -> oldUrls[0] = s);
                        this.setOrgValue(companyLog.getNewLegalPersonUrlReverse(), s -> oldUrls[1] = s);
                        this.setOrgValue(companyLog.getNewLegalPersonUrlHand(), s -> oldUrls[2] = s);
                        organization.setLegalPersonUrl(String.join(",", oldUrls));
                    } else {
                        organization.setLegalPersonUrl(String.join(",",
                                companyLog.getNewLegalPersonUrlFront(),
                                companyLog.getNewLegalPersonUrlReverse(),
                                companyLog.getNewLegalPersonUrlHand()));
                    }
                    organizationService.saveLegalPerson(organization.getOrgUuid(), organization.getLegalPersonUrl());
                    organizationService.saveBusinessLicense(organization.getOrgUuid(), organization.getBusinessLicenseUrl());
                }
            }
            if (relation.getBusinessType() == FeiShuRelationType.MODIFY_FINANCIAL.ordinal()) {
                OrgBankLog bankLog =iOrgBankLogService.getLastByRelevanceId(organization.getOrgId());
//                        orgBankLogMapper.getLastByRelevanceId(organization.getOrgId());
                if (bankLog == null) {
                    return;
                }
                this.setOrgValue(bankLog.getNewAccount(), organization::setPublicReceivingBankAccount);
                this.setOrgValue(bankLog.getNewBankName(), organization::setSubBranchName);
                this.setOrgValue(bankLog.getNewBank(), organization::setAccountBankName);
                this.setOrgValue(bankLog.getNewAccountName(), organization::setAccountName);
                // 特殊处理下地址
                if (StringUtils.isNotEmpty(bankLog.getNewAddress())) {
                    String[] split = bankLog.getNewAddress().split(";");
                    String[] province = split[0].split(":");
                    String[] city = split[1].split(":");
                    organization.setProvince(province[0]);
                    organization.setProvinceId(Integer.parseInt(province[1]));
                    organization.setCity(city[0]);
                    organization.setCityId(Integer.parseInt(city[1]));
                }
            }
            organizationMapper.updateByPrimaryKeySelective(organization);
        }

        private <T > void setOrgValue (T value, Consumer < T > consumer){
            if (value instanceof String) {
                if (!"".equals(value.toString())) {
                    consumer.accept(value);
                }
            } else if (value != null) {
                consumer.accept(value);
            }
        }

        @Override
        public void clearModifyLog (Organization organization, FeishuAuditRelation relation){
            if (relation.getBusinessType() == FeiShuRelationType.MODIFY_ORG.ordinal()) {
                // 修改机构信息
                OrgAccountLog accountLog = orgAccountLogMapper.getLastByRelevanceId(organization.getOrgId());
                if (accountLog == null) {
                    return;
                }
                orgAccountLogMapper.deleteById(accountLog.getId());
            }
            if (relation.getBusinessType() == FeiShuRelationType.MODIFY_CERT.ordinal()) {
                OrgCompanyLog companyLog = orgCompanyLogMapper.getLastByRelevanceId(organization.getOrgId());
                if (companyLog == null) {
                    return;
                }
                orgCompanyLogMapper.deleteById(companyLog.getId());
            }
            if (relation.getBusinessType() == FeiShuRelationType.MODIFY_FINANCIAL.ordinal()) {
                OrgBankLog bankLog = iOrgBankLogService.getLastByRelevanceId(organization.getOrgId());
//                        orgBankLogMapper.getLastByRelevanceId(organization.getOrgId());
                if (bankLog == null) {
                    return;
                }
                orgBankLogMapper.deleteById(bankLog.getId());
            }
        }
    }

