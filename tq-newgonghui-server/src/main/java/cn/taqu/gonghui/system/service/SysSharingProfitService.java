package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.system.entity.SysSharingProfit;
import cn.taqu.gonghui.system.search.SysSharingProfitSearch;

import java.util.List;

public interface SysSharingProfitService {

    List<CommonSelect> tree();

    List<String> getValues();

    List<String> getManageValues();

    List<SysSharingProfit> list(SysSharingProfitSearch search);

    void add(SysSharingProfit profit);

    void changeStatus(Long id, Integer status);

    Boolean percentNumberHasExist(String percentNumber);
}
