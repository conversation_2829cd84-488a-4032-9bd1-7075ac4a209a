package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.vo.TeamOrgInfoVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-13 17:55
 */
public interface PushService {

    /**
     * 推送团队艺人变更
     * @param hostUuid      艺人uuid
     * @param operator      操作人
     * @param oldTeam       旧 机构名-团队
     * @param newTeam       新 机构名-团队
     */
    void pushTeamHostChangeTeamToDingRobot(String hostUuid, String operator, Integer teamType, TeamOrgInfoVO oldTeam, TeamOrgInfoVO newTeam);

    /**
     * 推送后台操作
     *
     * @param operator
     * @param operateTypeName
     * @param teamTypeName
     * @param info
     */
    void pushBackstageOperateToDingRobot(String operator, String operateTypeName, String teamTypeName, String info);
}
