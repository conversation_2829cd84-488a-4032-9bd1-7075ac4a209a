package cn.taqu.gonghui.system.common;

import cn.taqu.gonghui.common.client.EncryptDecryptClient;
import cn.taqu.gonghui.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024/7/2 09 43
 * discription
 */
@Slf4j
@Service
@MappedJdbcTypes(JdbcType.VARCHAR)
public class SetNullTypeHandler extends BaseTypeHandler<String> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {

        if(EncryptSwitchConfig.doubleWrite.equals(0)){ //关闭双写时，设置为空
            ps.setString(i, "");
        }else {
            ps.setString(i, parameter);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {

        String content = rs.getString(columnName);
        if(StringUtils.isBlank(content)){
            return "";
        }
        return content;
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String  content = rs.getString(columnIndex);

        if(StringUtils.isBlank(content)){
            return "";
        }
       return content;
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String content = cs.getNString(columnIndex);
        if(StringUtils.isBlank(content)){
            return "";
        }

        return content;
    }
}
