package cn.taqu.gonghui.system.service;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.vo.req.LinkUserReq;
import cn.taqu.gonghui.common.vo.res.GeneralUserListRes;
import cn.taqu.gonghui.system.entity.GeneralUserList;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.search.UserSearch;
import cn.taqu.gonghui.system.vo.RoleVo;
import cn.taqu.gonghui.system.vo.SsoUserCombobox;
import cn.taqu.gonghui.system.vo.SysUserVo;
import cn.taqu.gonghui.system.vo.UserResultVo;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.rpc.Help;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/4/25
 */
public interface SysUserService extends IService<SysUser> {



    SysUser selectUserByMobile(String mobile);

    List<SysUser> selectUserListByMobile(String mobile);

    List<SysUser> selectUserListByMobile2(String mobile);


    SysUser selectUserByUserId(Long userId);

    IPage<SysUserVo> selectUserByPage(UserSearch search, int pageNo, int pageSize);

    void updateStatus(String uuid,Integer status);

    /**
     * 注册登录用户
     * @param
     */
    SysUser registerAccount(String mobile,UserTypeEnum userTypeEnum);

    SysUser registerAccountForMember(String mobile,UserTypeEnum userTypeEnum,String userName);


    String selectOrgManager(Long orgId);

    void updateLastLoginTime(Long userId);

    /**
     * 换绑超管时未注册就注册用户
     * @param
     */
    SysUser registerAccountByChangeUser(String mobile,Long orgId,String orgName);

    String genUniqueUuid();

    SysUser selectUserByAccountUuid(String accountUuid);

    List<SysUser> getAllAgentManage();



    /**
     * 查询有效的经纪人
     * @return
     */
    Map<String, String> getAgentSelect();
    /**
     * 获取当前用户角色
     */
    RoleVo getRoleByAccountUuid(Long userId, Integer type);

    List<RoleVo>  getRoleByUserId(Long userId);

    RoleVo getRoleByUserId(Long userId,Integer type);

    List<SysUser> getUserListByIds(List<Long> ids);

    List<SysUser> getUserListByAccountUuids(List<String> accountUuids);


    void updateNameByUserId(Long userId,String name);

    void cancelUser(String uuid);

    void editUser(String uuid,String code,String mobile,String vcode,String userName);

    UserResultVo getUserInfo(String accountUuid);

    RoleVo getCurrentRole(Integer type);

    List<Long> selectUserIdsByRoleId(Long orgId,Long roleId);

    /**
     * 查询单点所有有效用户
     * @return
     */
    List<SsoUserCombobox> pointAllUser();

    /**
     * 新增对接运营
     * @param req
     */
    void addLinkUser(LinkUserReq req);

    /**
     * 删除对接运营
     * @param req
     */
    void delLinkUser(LinkUserReq req);

    /**
     * 对接用户列表
     * @param req
     * @return
     */
    List<GeneralUserListRes> linkUserList(LinkUserReq req);

    boolean isManager(Long userId,Integer type);
}
