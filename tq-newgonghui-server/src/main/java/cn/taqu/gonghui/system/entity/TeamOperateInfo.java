package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 团队操作表
 * @Date 2022-05-10
 */
@Data
public class TeamOperateInfo {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作批次号，一个批次可以有一个机构的多个团队
     */
    private String batchId;

    /**
     * 操作类型，1-合并机构
     */
    private Integer type;

    /**
     * 旧机构id
     */
    private Long oldOrgId;

    /**
     * 旧团队id
     */
    private Long oldTeamId;

    /**
     * 新机构id，为0表示无变动
     */
    private Long newOrgId;

    /**
     * 新团队id，为0表示无变动
     */
    private Long newTeamId;

    /**
     * 业务类型(1-直播 2-趣聊 3-聊天室)
     */
    private Integer teamType;

    /**
     * 操作状态，0-开始，1-成功，2-失败
     */
    private Integer status;

    /**
     * 操作艺人数量
     */
    private Integer hostNum;
    /**
     * 操作成功数量
     */
    private Integer successNum;
    /**
     * 操作失败数量
     */
    private Integer failNum;
    /**
     * 操作用户
     */
    private String operator;

    private Long createTime;
    private Long updateTime;
}
