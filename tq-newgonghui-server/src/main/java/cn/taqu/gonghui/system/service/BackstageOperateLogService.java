package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.entity.BackstageOperateLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-11 13:54
 */
public interface BackstageOperateLogService extends IService<BackstageOperateLog> {

    /**
     * 增加操作日志
     *
     * @param batchId
     * @param operateType
     * @param operator
     * @param info
     */
    void addBackstageOperateLog(String batchId, Integer operateType, String operator, String info);

    /**
     * 增加操作日志(独立事务)
     *
     * @param batchId
     * @param operateType
     * @param operator
     * @param info
     */
    void addBackstageOperateLogTxNew(String batchId, Integer operateType, String operator, String info);
}
