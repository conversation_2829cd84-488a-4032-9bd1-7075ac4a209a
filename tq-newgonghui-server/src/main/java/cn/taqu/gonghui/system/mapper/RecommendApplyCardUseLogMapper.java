package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.RecommendApplyCardUseLog;
import cn.taqu.gonghui.system.search.RecommendApplyCardUseLogSearch;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

public interface RecommendApplyCardUseLogMapper extends BaseMapper<RecommendApplyCardUseLog> {

    List<RecommendApplyCardUseLog> queryByCondition(RecommendApplyCardUseLogSearch search);
}
