package cn.taqu.gonghui.system.mapper;


import cn.taqu.gonghui.system.entity.LegalPerson;
import cn.taqu.gonghui.system.entity.OrgCooperationFlow;

import java.util.List;

public interface OrgCooperationFlowMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(OrgCooperationFlow record);

    int insertSelective(OrgCooperationFlow record);

    OrgCooperationFlow selectByPrimaryKey(Integer id);

    int deleteByUuid(String uuid);

    int updateByPrimaryKeySelective(OrgCooperationFlow record);

    int updateByPrimaryKey(OrgCooperationFlow record);

    List<OrgCooperationFlow> getAllByOrgUuid(String orgUuid);
}