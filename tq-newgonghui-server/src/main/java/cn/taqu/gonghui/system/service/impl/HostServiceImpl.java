package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.gonghui.soa.InfoService;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.service.HostService;
import cn.taqu.gonghui.system.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.shiro.util.CollectionUtils;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.soa.GonghuiService;
import cn.taqu.gonghui.soa.HostApplyService;
import cn.taqu.gonghui.system.search.HostStatisticSearch;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class HostServiceImpl implements HostService {

    private static Logger logger = LoggerFactory.getLogger(HostServiceImpl.class);



    @SoaReference("account")
    private InfoService infoService;
    @SoaReference(application = "liveV1",value = "liveV1")
    private GonghuiService gonghuiService;
    @SoaReference(application = "liveV1",value = "liveV1")
    private HostApplyService hostApplyService;

    @Autowired
    private TeamHostMapper teamHostMapper;

    @Override
    public Map<String, Object> getNicknameByMobile(String mobile) {
        try {
            Map<String, String> userMap = getNickNameByMobile(mobile);
            Integer zhimaCertification = zhimaCertification(userMap.get("uuid"));
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("nickName", userMap.get("nickName"));
            resultMap.put("zhimaCertification", zhimaCertification);
            return resultMap;
        } catch (ServiceException e) {
            String code = e.getCodeStatus().value();
            throw new ServiceException(code, "未查找到该用户");
        }

    }

    /**
     * 根据手机号查询昵称
     *
     * @return
     */
    public Map<String, String> getNickNameByMobile(String mobile) {
        String uuid = getUuidByMobile(mobile);
        String nickName = getNickNameByUuid(uuid);
        Map<String, String> map = new HashMap<>();
        map.put("uuid", uuid);
        map.put("nickName", nickName);
        return map;
    }

    /**
     * 根据手机号查询uuid
     *
     * @param mobile
     * @return
     */
    @Override
    public String getUuidByMobile(String mobile) {
        Map<String, String> uuidMao = infoService.getUuidAndNameByMobile(mobile);
        if (MapUtils.isEmpty(uuidMao)) {
            throw new ServiceException("ERROR","根据手机账号查询失败");
        }
        return uuidMao.get("account_uuid");
    }


    /**
     * 根据uuid查询昵称
     *
     * @param accountUuid
     * @return
     */
    @Override
    public String getNickNameByUuid(String accountUuid) {
        String field = "account_name";
        Map<String, Map<String,Object>> infoByUuidMap = infoService.getInfoByUuid(new String[]{accountUuid},new String[]{field},null,true,true);
        if(MapUtils.isEmpty(infoByUuidMap)){
            throw new ServiceException("error","data is null");
        }
        return String.valueOf(null == infoByUuidMap.get(accountUuid).get(field)? "": infoByUuidMap.get(accountUuid).get(field));
    }

    @Override
    public Map<String,String> getNickNameByUuids(List<String> accountUuids) {
        String field = "account_name";
        Map<String, Map<String,Object>> infoByUuidMap = infoService.getInfoByUuid(accountUuids.toArray(new String[0]),
                new String[]{field},null,true,true);
        if(MapUtils.isEmpty(infoByUuidMap)){
            throw new ServiceException("error","data is null");
        }
        Map<String,String> ret = new HashMap<>();
        infoByUuidMap.forEach((uuid,info)->{
            ret.put(uuid,String.valueOf(info.getOrDefault(uuid,"")));
        });
        return ret;
    }

    /**
     * 根据uuids数组查信息
     */
    @Override
    public Map<String, Map<String,Object>> getInfoByUuids(String[] accountUuids, String[] fields) {
        Map<String, Map<String,Object>> infoByUuidMap = infoService.getInfoByUuid(accountUuids,fields,null,true,true);
        return infoByUuidMap;
    }

    /**
     * 根据uuid查询是否实名认证
     *
     * @return 0:未认证; 1:已认证;
     */
    @Override
    public Integer zhimaCertification(String accountUuid) {
        String field = "zhima_certification";
        Map<String, Map<String,Object>> infoByUuidMap = infoService.getInfoByUuid(new String[]{accountUuid},new String[]{field},null,true,true);
        if(MapUtils.isEmpty(infoByUuidMap)){
            throw new ServiceException("error","data is null");
        }
        return Integer.parseInt(String.valueOf(null == infoByUuidMap.get(accountUuid).get(field)?0:infoByUuidMap.get(accountUuid).get(field)));
    }

    @Override
    public Integer getTaquConsortiaId() {
        Map<String,Integer> taquConsortiaId = gonghuiService.getTaquConsortiaId();
       if (StringUtils.isEmpty(taquConsortiaId)) {
           logger.info("返回的数值：{}",JsonUtils.objectToString(taquConsortiaId));
            throw new ServiceException("ERROR","获取中转团队失败");
        }
        return taquConsortiaId.get("consortia_id");
    }

    @Override
    public Integer getPersonalConsortiaId() {
        Map<String,Integer> personalConsortiaId = gonghuiService.getPersonalConsortiaId();
        if (StringUtils.isEmpty(personalConsortiaId)) {
            logger.info("返回的数值：{}",JsonUtils.objectToString(personalConsortiaId));
            throw new ServiceException("ERROR","获取素人团队失败");
        }
        return personalConsortiaId.get("consortia_id");
    }

    @Override
    public Boolean isInTaquOrPersonalTeam(Long teamId) {
        if(teamId == null){
            return false;
        }
        Integer taquConsortiaId = getTaquConsortiaId();
        Integer personalConsortiaId = getPersonalConsortiaId();
        log.info("[isInTaquOrPersonalTeam],{},{},{}", teamId,taquConsortiaId,personalConsortiaId);
        if(Objects.equals(Integer.parseInt(teamId + ""), taquConsortiaId) || Objects.equals(Integer.parseInt(teamId + ""), personalConsortiaId)){
            return true;
        }
        return false;
    }

    /**
     * 获取推荐位列表
     */
    @Override
    public RecommendApplyInfoVo getRecommendApplyInfo(String hostUuid, String consortiaId) {
        Map<String, Object> resultMap = null;
        try {
            resultMap = gonghuiService.getRecommendApplyInfo(hostUuid, consortiaId);
        } catch (Exception e){
            logger.error("soa Gonghui/getRecommendApplyInfo error.", e);
            throw new ServiceException(CodeStatus.SOA_FAIL.value(), CodeStatus.SOA_FAIL.getReasonPhrase());
        }
        RecommendApplyInfoVo vo = new RecommendApplyInfoVo();
        logger.info("推荐位data:" + JsonUtils.objectToString(resultMap));
        vo.setDate(String.valueOf(resultMap.get("date")));
        vo.setGonghuiApplyLimit((Map<String, Integer>) resultMap.get("gonghui_apply_limit"));
        vo.setHostName(String.valueOf(resultMap.get("host_name")));
        vo.setHostUuid(String.valueOf(resultMap.get("host_uuid")));
        vo.setLocation1(Integer.parseInt(String.valueOf(resultMap.get("location_1"))));
        vo.setLocation2(Integer.parseInt(String.valueOf(resultMap.get("location_2"))));
        Map<String, String> locationMaps = (Map<String, String>) resultMap.get("recommend_apply_location");
        String keyPre = "time_list_";
        List<Map<String, Object>> locationVos = Lists.newArrayList();

        for (Map.Entry<String, String> entry : locationMaps.entrySet()) {
            String k = entry.getKey();
            Object v = entry.getValue();
            String realKey = keyPre + k;
            List<Map<String, Object>> innerList = (List<Map<String, Object>>) resultMap.get(realKey);
            if (!CollectionUtils.isEmpty(innerList)) {
                List<RecommendApplyLocationVo> vos = Lists.newArrayList();
                innerList.forEach(item -> {
                    RecommendApplyLocationVo innerVo = new RecommendApplyLocationVo();
                    String startTime = String.valueOf(item.get("start"));
                    String endTime = String.valueOf(item.get("end"));
                    innerVo.setTime(startTime + "~" + endTime);
                    innerVo.setUse(Integer.parseInt(String.valueOf(item.getOrDefault("use", "0"))));
                    innerVo.setIsGoldTime(0); //0不是黄金时间
                    vos.add(innerVo);
                });
                locationVos.add(
                        ImmutableMap.of(
                                "value", k,
                                "name", v,
                                "data", vos));
            }
        }
        vo.setLocationVos(locationVos);

        return vo;
    }

    /**
     * 创建推荐位申请
     *
     * @param consortiaId
     * @param date
     * @param location
     * @param hostUuid
     * @param time
     * @param isGold      1是0否
     */
    @Override
    public void addRecommendApplyG(String consortiaId, String date, String location, String hostUuid, String time, Integer isGold) {
            gonghuiService.addRecommendApplyG(consortiaId,date, location, hostUuid, time, isGold);

    }

    /**
     * 根据昵称查询uuid
     *
     * @param accountName
     * @return
     */
    @Override
    public String getUuidByAccountName(String accountName) {
        String uuid = "";
        try {
            Map<String, String> map = infoService.getUuidAndNameByMobile(accountName);
            uuid = String.valueOf(map.getOrDefault("uuid", ""));
        } catch (Exception e){
            logger.error("soa info/getUuidByAccountName error.", e);
            throw new ServiceException(CodeStatus.SOA_FAIL.value(), CodeStatus.SOA_FAIL.getReasonPhrase());
        }
        return uuid;
    }




    /**
     * 分页获取推荐位列表
     * @param date
     * @param uuidList
     * @param host_name
     * @param location
     * @param status
     * @param consortia_id
     * @param limit
     * @param type
     * @return
     */
    @Override
    public PageData getRecommendApplyList(String date, List<String> uuidList, String host_name, String location, Integer status, String consortia_id, String limit, Integer type) {
        PageData pageData = null;
        try {
            pageData = hostApplyService
                    .getRecommendApplyList(date, uuidList, host_name, location, status,consortia_id,limit,type);
        } catch (Exception e) {
            logger.error("soa HostApply/getRecommendApplyList error.", e);
            throw new ServiceException(CodeStatus.SOA_FAIL.value(), e);
        }
        return pageData;
    }

    /**
     * 根据id撤销推荐位申请
     * @param id
     */
    @Override
    public void cancelApplyById(Long id){
        try {
            hostApplyService.cancel_apply(id);
        } catch (Exception e) {
            logger.error("soa Gonghui/cancel_apply error.", e);
            throw new ServiceException(CodeStatus.SOA_FAIL.value(), CodeStatus.SOA_FAIL.getReasonPhrase());
        }
    }

    /**
     * 新增主播数据
     *
     * @param search
     * @return
     */
    @Override
    public LiveHostStatisticNewVo getConsortiaNewHostStat(HostStatisticSearch search) {
        LiveHostStatisticNewVo liveHostStatisticNewVo = null;
        try {
            liveHostStatisticNewVo =  gonghuiService.getConsortiaNewHostStat(search.getConsortiaIdList(), search.getHostUuidList(), search.getDate(), search.getOrder_type(),
                    search.getSort_type(), search.getPage(), search.getPageSize(), search.getExport(),search.getApply_level(),search.getLive_no());
        } catch (Exception e){
            logger.error("soa Gonghui/getConsortiaNewHostStat error.", e);
            throw new ServiceException(CodeStatus.SOA_FAIL.value(), e);
        }
        return liveHostStatisticNewVo;
    }

    /**
     * 同步经纪人
     * @param hostUuid
     * @param businessUuid
     */
    @Override
    public void changeHostBusinessman(String hostUuid, String businessUuid) {
        try {
            gonghuiService.changeHostBusinessman(hostUuid,businessUuid);
        }catch (Exception e){
            logger.error("soa Gonghui/changeHostBusinessman error.", e);
            throw new ServiceException(CodeStatus.SOA_FAIL.value(), CodeStatus.SOA_FAIL.getReasonPhrase());
        }
    }

    /**
     * 同步团队
     */
    @Override
    public void changeHostConsortia(String consortiaId, List<String> hostUuidList) {
        try {
            gonghuiService.changeHostConsortia(consortiaId,hostUuidList);
        }catch (Exception e){
            logger.error("soa Gonghui/changeHostConsortia error.", e);
            throw new ServiceException(CodeStatus.SOA_FAIL.value(), CodeStatus.SOA_FAIL.getReasonPhrase());
        }
    }

    /**
     * 调整分润比例
     * @param changeId
     * @param hostUuid
     * @param orgName
     * @param changeTime
     */
    @Override
    public void sendSpiltRatioNotice(Long changeId, String hostUuid, String orgName, Long changeTime){
        gonghuiService.sendSpiltRatioNotice(changeId,hostUuid,orgName,changeTime);
    }


}
