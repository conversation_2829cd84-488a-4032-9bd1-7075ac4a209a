package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.SysRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 判断当前业务类型下角色名称是否已经存在
     */
    SysRole roleNameIsExisted(@Param("roleName") String roleName, @Param("type") Integer type);

    /**
     * 判断当前业务下角色权限标识符是否已经存在
     */
    SysRole roleKeyIsExisted(@Param("roleKey") String roleKey, @Param("type") Integer type);


    /**
     * 判断当前业务下角色权限标识符是否已经存在
     */
    SysRole getRoleName(@Param("roleId") Long roleId);

    // --------------------  以下为数据迁移需要用到的接口  -------------------------
    /**
     * 获取直播业务类型的经纪人角色roleId
     */
    Long getAgentRoleId();

    /**
     * 获取直播业务类型的负责人角色roleId
     */
    Long getLeaderRoleId();

    /**
     * 获取直播业务类型的管理员角色roleId
     */
    Long getManagerRoleId();
}
