package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysSharingProfit;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.SysSharingProfitMapper;
import cn.taqu.gonghui.system.search.SysSharingProfitSearch;
import cn.taqu.gonghui.system.service.SysSharingProfitService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SysSharingProfitServiceImpl extends ServiceImpl<SysSharingProfitMapper, SysSharingProfit> implements SysSharingProfitService {

    private static Logger logger = LoggerFactory.getLogger(AgreementInfoServiceImpl.class);

    @Autowired
    private TokenService tokenService;
    @Autowired
    private OrganizationMapper organizationMapper;

    /**
     * 结算方式不同下的分润比例限制
     * key:结算方式（1-月结，2-周结，3-新月结）
     * value:限制起始大小（xx-xx）
     */
    private static Map<String, String> startAndEndRate = new HashMap<>();

    @EtcdValue("biz.sharing.startAndEndRate")
    public static void setStartAndEndRate(String startAndEndRateJson) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(startAndEndRateJson) && startAndEndRateJson.startsWith("\\")) {
            startAndEndRateJson = org.apache.commons.lang3.StringUtils.removeStart(startAndEndRateJson,"\\");
        }
        try {
            startAndEndRate = JSON.parseObject(startAndEndRateJson, new TypeReference<Map<String, String>>() {
            });
        } catch (Exception e) {
            logger.error("解析分润比例限制配置失败,失败原因{}",e);
        }
    }

    /**
     * 用户端返回有效分润比例tree
     * @return
     */
    @Override
    public List<CommonSelect> tree() {
        String startAndEndStr = startAndEndRate.get(getLiveSettlementeType());
        if (StringUtils.isBlank(startAndEndStr)) {
            logger.warn("获取分润比例配置失败，配置为：{}", JsonUtils.objectToString(startAndEndRate));
            throw new ServiceException("invalid_sharing_rate_config","解析分润比例配置失败");
        }
        String[] startAndEndStrSplit = startAndEndStr.split("-");
        if (startAndEndStrSplit.length < 2) {
            logger.warn("获取分润比例配置失败，配置为：{}", JsonUtils.objectToString(startAndEndRate));
            throw new ServiceException("invalid_sharing_rate_config","解析分润比例配置失败");
        }
        String startRate = startAndEndStrSplit[0];
        String endRate = startAndEndStrSplit[1];

        SysSharingProfitSearch search = new SysSharingProfitSearch();
        search.setStatus(1);
        List<SysSharingProfit> list = this.baseMapper.listByCondition(search);
        List<SysSharingProfit> resultList = list.stream()
                .filter(rate -> Integer.valueOf(rate.getPercentNumber()) >= Integer.valueOf(startRate)
                        && Integer.valueOf(rate.getPercentNumber()) <= Integer.valueOf(endRate))
                .collect(Collectors.toList());
        List<CommonSelect> collect = resultList.stream().map(CommonSelect::new).collect(Collectors.toList());
        return collect;
    }

    /**
     * 用户端返回分润比例tree
     * @return
     */
    @Override
    public List<String> getValues() {

        String startAndEndStr = startAndEndRate.get(getLiveSettlementeType());
        if (StringUtils.isBlank(startAndEndStr)) {
            logger.warn("获取分润比例配置失败，配置为：{}", JsonUtils.objectToString(startAndEndRate));
            throw new ServiceException("invalid_sharing_rate_config","解析分润比例配置失败");
        }
        String[] startAndEndStrSplit = startAndEndStr.split("-");
        if (startAndEndStrSplit.length < 2) {
            logger.warn("获取分润比例配置失败，配置为：{}", JsonUtils.objectToString(startAndEndRate));
            throw new ServiceException("invalid_sharing_rate_config","解析分润比例配置失败");
        }
        String startRate = startAndEndStrSplit[0];
        String endRate = startAndEndStrSplit[1];

        SysSharingProfitSearch search = new SysSharingProfitSearch();
        search.setStatus(1);
        List<SysSharingProfit> list = this.baseMapper.listByCondition(search);
        List<SysSharingProfit> resultList = list.stream()
                .filter(rate -> Integer.valueOf(rate.getPercentNumber()) >= Integer.valueOf(startRate)
                        && Integer.valueOf(rate.getPercentNumber()) <= Integer.valueOf(endRate))
                .collect(Collectors.toList());
        List<String> collect = resultList.stream().map(SysSharingProfit::getPercentNumber).collect(Collectors.toList());
        return collect;
    }

    /**
     * 管理端返回分润比例tree
     * @return
     */
    @Override
    public List<String> getManageValues() {
        SysSharingProfitSearch search = new SysSharingProfitSearch();
        search.setStatus(1);
        List<SysSharingProfit> list = this.baseMapper.listByCondition(search);
        List<String> collect = list.stream().map(SysSharingProfit::getPercentNumber).collect(Collectors.toList());
        return collect;
    }

    /**
     * 列表
     * @param search
     * @return
     */
    @Override
    public List<SysSharingProfit> list(SysSharingProfitSearch search) {
        return this.baseMapper.listByCondition(search);
    }

    /**
     * 添加
     * @param profit
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(SysSharingProfit profit) {
        checkParams(profit);
        profit.setCreateTime(System.currentTimeMillis()/1000);
        this.save(profit);
    }

    /**
     * 启用/禁用
     * @param id
     * @param status
     */
    @Override
    public void changeStatus(Long id, Integer status) {
        if (null == id) {
            throw new ServiceException("param_error","ID不能为空");
        }
        if (null == status) {
            throw new ServiceException("param_error","状态不能为空");
        }
        // 正在使用中的分润比例不允许被禁用
        SysSharingProfit one = this.baseMapper.getOneById(id);
        if (status == 0 && one.getStatus() == 1) {
            TeamHost teamHost = this.baseMapper.rateHasUsed(one.getPercentNumber());
            if (null != teamHost) {
                throw new ServiceException("invalid_operate","此分润比例正在使用中，不允许禁用");
            }
        }
        this.baseMapper.updateStatus(id,status);
    }

    /**
     * 分润比例是否已经存在
     * @param percentNumber
     * @return
     */
    @Override
    public Boolean percentNumberHasExist(String percentNumber) {
        Integer count = this.baseMapper.percentNumberHasExist(percentNumber);
        Integer number = count == null ? 0 : count;
        return number != 0 ? true : false;
    }

    /**
     * 参数校验
     * @param profit
     */
    private void checkParams(SysSharingProfit  profit){
        if (StringUtils.isBlank(profit.getPercentNumber())) {
            throw new ServiceException("param_error","分润比例不能为空");
        }
        if (!checkPercentNumber(profit.getPercentNumber())) {
            throw new ServiceException("param_error","分润比例必须是整数");
        }
        if (Integer.valueOf(profit.getPercentNumber()) < 0 || Integer.valueOf(profit.getPercentNumber()) > 100) {
            throw new ServiceException("param_error","分润比例应该在 0 ~ 100 之间");
        }
        if (null ==  profit.getStatus()) {
            throw new ServiceException("param_error","状态未设置");
        }
        if (percentNumberHasExist(profit.getPercentNumber())) {
            throw new ServiceException("param_error","分润比例已经存在，请重新设置");
        }
    }

    /**
     * 校验分润比例是否为整数
     */
    private Boolean checkPercentNumber(String percent){
        try {
            Integer.valueOf(percent);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取当前登陆用户所属机构的结算类型
     * @return
     */
    private String getLiveSettlementeType(){
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        if (null == user || null ==  user.getOrgId()) {
            throw new ServiceException("invalid_organization","当前登陆用户所属机构无效");
        }
        Organization organization = organizationMapper.selectByPrimaryKey(user.getOrgId());
        if (null == organization) {
            throw new ServiceException("invalid_organization","当前登陆用户所属机构无效");
        }
        return organization.getLiveSettlementeType().toString();
    }
}
