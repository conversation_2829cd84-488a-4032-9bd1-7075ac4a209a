package cn.taqu.gonghui.system.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.soa.GonghuiSoaService;
import cn.taqu.gonghui.soa.HostTaskSoaService;
import cn.taqu.gonghui.soa.SOAUtil;
import cn.taqu.gonghui.soa.dto.HostTaskStaticsInfoList;
import cn.taqu.gonghui.soa.dto.HostTaskStatisticInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 协议管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HostTaskSoaServiceImpl implements HostTaskSoaService {
    private static final String CHATROOM_URL = "/soa/application/live/api";
    private static final String service = "AdminHostGrowth";


    @Override
    public List<HostTaskStatisticInfo> listStatData(List<String> hostUuidList, Long monthDate) {
        Object[] data = {hostUuidList, monthDate};
        log.info("soa请求awardListForConsortia, request data={}", JSON.toJSONString(data));
        SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                .call(service, "awardListForConsortia", data);
        log.info("soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();

        HostTaskStaticsInfoList hostTaskStaticsInfoList =
                JsonUtils.stringToObject(resData, new TypeReference<HostTaskStaticsInfoList>() {
                });
        return hostTaskStaticsInfoList.getList();
    }

    @Override
    public JSONObject listDetail(String hostUuid, Integer taskId, Long month) {
        Map<String, Object> params = new HashMap<>();
        params.put("host_uuid", hostUuid);
        params.put("task_id", taskId);
        params.put("month", month);
        Object[] data = {params};
        log.info("soa请求detail, request data={}", JSON.toJSONString(data));
        SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                .call(service, "detail", data);
        log.info("soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();
        return JSONObject.parseObject(resData);
    }
}
