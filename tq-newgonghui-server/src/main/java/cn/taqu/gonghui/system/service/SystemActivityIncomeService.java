package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.search.SystemActivityIncomeSearch;
import cn.taqu.gonghui.system.vo.SystemActivityIncomeVo;

import java.util.List;
import java.util.Map;


public interface SystemActivityIncomeService {

    List<Map<String, Object>> getSystemProfitServiceList(SystemActivityIncomeSearch search, Integer page);

    List<Map<String, Object>>   getSystemProfitServiceListForUser(SystemActivityIncomeSearch search, Integer page);
}
