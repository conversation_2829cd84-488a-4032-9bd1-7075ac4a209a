package cn.taqu.gonghui.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.gonghui.chatroom.service.ChatRoomService;
import cn.taqu.gonghui.chatroom.service.soa.AdminForumChatService;
import cn.taqu.gonghui.chatroom.service.soa.ForumChatRoomService;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.LiveSettlementeTypeEnum;
import cn.taqu.gonghui.common.constant.TeamDefaultEnum;
import cn.taqu.gonghui.common.constant.TeamHostOperateLogEnum;
import cn.taqu.gonghui.common.constant.TeamStatusEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.RandomStringUtil;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.SpringUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.utils.UUID;
import cn.taqu.gonghui.common.vo.req.TransferTeamReq;
import cn.taqu.gonghui.system.config.TeamSignConfig;
import cn.taqu.gonghui.system.dto.HostOrgTeamDto;
import cn.taqu.gonghui.system.dto.HostTeamOrAgentDto;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.dto.TeamDto;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import cn.taqu.gonghui.system.search.SharingProfitRecordSearch;
import cn.taqu.gonghui.system.search.TeamSearch;
import cn.taqu.gonghui.system.service.BackstageOperateLogService;
import cn.taqu.gonghui.system.service.HostModifyRecordService;
import cn.taqu.gonghui.system.service.HostService;
import cn.taqu.gonghui.system.service.OrganizationService;
import cn.taqu.gonghui.system.service.OrganizationUserService;
import cn.taqu.gonghui.system.service.PushService;
import cn.taqu.gonghui.system.service.TeamHostService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.vo.TeamOrgInfoVO;
import cn.taqu.gonghui.system.vo.TeamTreeVo;
import cn.taqu.gonghui.system.vo.TeamVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
@Service
public class TeamServiceImpl extends ServiceImpl<TeamMapper, Team> implements TeamService {

    private static Logger logger = LoggerFactory.getLogger(TeamServiceImpl.class);

    @Autowired
    private TeamEmployeeMapper teamEmployeeMapper;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    private TeamSignConfig teamSignConfig;
    @Autowired
    private TokenService tokenService;
    @Autowired
    OrganizationUserService organizationUserService;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private HostService hostService;
    @SoaReference(application = "liveV1", value = "liveV1")
    private ForumChatRoomService forumChatRoomService;
    @SoaReference(application = "liveV1", value = "liveV1")
    private AdminForumChatService adminForumChatService;
    @Autowired
    private TeamHostOperateLogMapper teamHostOperateLogMapper;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private BackstageOperateLogService backstageOperateLogService;
    @Autowired
    private PushService pushService;
    @Autowired
    private HostModifyRecordService hostModifyRecordService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private HostSharingProfitRecordMapper sharingProfitRecordMapper;

    /**
     * 条件筛选团队列表
     *
     * @param search
     * @return
     */
    @Override
    public List<TeamVo> selectTeamList(TeamSearch search) {
        PageHelper.startPage(search.getPageNum() == null ? 1 : search.getPageNum(), search.getPageSize() == null ? 20 : search.getPageSize());
        List<TeamVo> teamVos = this.baseMapper.selectTeamListByCondition(search);
        // 获取团队负责人

        for (TeamVo item : teamVos) {
            // 设置团队负责人(姓名+手机号)
            item.setChargeName(chargeNameByTeamId(item.getTeamId(), item.getType()));
            // 设置该团队下工作人员数量
            Integer employeeNumbers = getEmployeeNumbers(item.getTeamId());
            // 设置该团队下所有艺人数量
            Integer hostNumbers = getHostNumbers(item.getTeamId());
            item.setNumber(hostNumbers);
            item.setTeamNumber(employeeNumbers + hostNumbers);
        }
        return teamVos;
    }

    private String chargeNameByTeamId(Long teamId, Integer teamType) {
        List<String> teamLeaders = this.baseMapper.findTeamLeaders(teamId, teamType);
        if (CollectionUtils.isEmpty(teamLeaders)) {
            return "";
        } else {
            return StringUtils.join(teamLeaders, ",");
        }
    }

    /**
     * 获取有效团队列表
     *
     * @return
     */
    @Override
    public List<Team> selectTeamList(Long orgId, Integer type) {
        List<Team> teams = this.baseMapper.selectTeamList(orgId, type);
        return teams;
    }

    /**
     * 获取直播有效团队列表
     *
     * @return
     */
    @Override
    public List<TeamTreeVo> getTeamListByBusinessType(Integer type) {
        List<TeamTreeVo> teams = this.baseMapper.selectTeamListByType(type);
        if (CollectionUtils.isNotEmpty(teams)) {
            for (TeamTreeVo vo : teams) {
                vo.setTeamName(vo.getLabel());
                vo.setLabel(vo.getOrgName() + " - " + vo.getLabel());
            }
        }
        return teams;
    }


    /**
     * 获取有效团队列表
     *
     * @return
     */
    @Override
    public List<CommonSelect> sharingRecordTeamList() {
        List<TeamTreeVo> teams = this.baseMapper.selectTeamListByType(TeamTypeEnum.LIVE_TEAM.getValue());
        if (CollectionUtils.isNotEmpty(teams)) {
            for (TeamTreeVo vo : teams) {
                vo.setTeamName(vo.getLabel());
                if (vo.getType() == null) {
                    vo.setLabel("");
                } else if (LiveSettlementeTypeEnum.MONTH.getValue() == vo.getType()) {
                    vo.setLabel(vo.getOrgName() + " - " + vo.getLabel() + " - " + "月结");
                } else if (LiveSettlementeTypeEnum.WEEK.getValue() == vo.getType()) {
                    vo.setLabel(vo.getOrgName() + " - " + vo.getLabel() + " - " + "周结");
                } else if (LiveSettlementeTypeEnum.NEW_MONTH.getValue() == vo.getType()) {
                    vo.setLabel(vo.getOrgName() + " - " + vo.getLabel() + " - " + "新月结");
                }
            }
        }
        return teams.stream().map(CommonSelect::new).collect(Collectors.toList());
    }

    /**
     * 获取团队下拉树
     *
     * @return
     */
    @Override
    public List<CommonSelect> selectTree(Long orgId, Integer type) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        if (null == orgId) {
            throw new ServiceException(CodeStatus.ORG_NOT_FOUNT_ERROR.value(), "获取机构信息失败");
        }
        List<Team> teams = null;
        // 如果是机构管理员就查询该机构下所有团队下拉树
        if (user.getUserType().equals(UserTypeEnum.MANAGER.getType())) {
            teams = selectTeamList(orgId, type);
        }
        // 如果是负责人或者经纪人只查询该负责人或者经纪人所属团队下拉
        else {
            teams = this.baseMapper.getTeamTreeByRole(user.getAccountUuid(), type, orgId);
        }
        return teams.stream().map(CommonSelect::new).collect(Collectors.toList());
    }

    @Override
    public TeamVo info(Long teamId) {
        TeamVo vo = new TeamVo();
        Team team = this.baseMapper.selectByPrimaryKey(teamId);
        if (team == null) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "团队无效");
        }
        Organization orgInfo = organizationService.getOrgInfo(team.getOrgId());
        if (orgInfo == null) {
            throw new ServiceException(CodeStatus.ORG_NOT_FOUNT_ERROR.value(), "机构不存在");
        }
        vo.setTeamId(team.getTeamId());
        vo.setOrgId(team.getOrgId());
        vo.setTeamName(team.getTeamName());
        vo.setType(team.getType());
        vo.setOrgName(orgInfo.getOrgName());
        vo.setChargeName(orgInfo.getChargePerson());
        vo.setStatus(team.getStatus());
        Integer hostNumber = this.baseMapper.findHostNumberByTeamId(teamId);
        vo.setNumber(hostNumber);
        return vo;
    }

    /**
     * 根据teamId获取团队信息
     *
     * @param teamId
     * @return
     */
    @Override
    public Team detail(Long teamId) {
        if (null == teamId) {
            throw new ServiceException("param_error", "团队id不能为空");
        }
        Team team = this.baseMapper.selectByPrimaryKey(teamId);
        return team;
    }

    /**
     * 新增团队
     *
     * @param record
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertTeam(Team record) {
        // 校验参数
        checkParams(record);
        // 判断当前机构下是否有对应的业务在运营
        List<Integer> types = organizationUserService.getOrgInfoType(record.getOrgId());
        if (CollectionUtils.isEmpty(types)) {
            throw new ServiceException("param_error", "该机构下暂未开启该业务");
        }
        if (!types.contains(record.getType())) {
            throw new ServiceException("param_error", "该机构下暂未开启该业务");
        }
        // 正常创建就是给非默认团队
        record.setIsDefault(TeamDefaultEnum.UN_DEFAULT.getValue());
        // 创建团队
        record.setStatus(TeamStatusEnum.VALID.getValue());
        record.setCreateTime(System.currentTimeMillis() / 1000);
        this.save(record);
    }

    /**
     * 更改团队信息
     *
     * @param record
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTeam(Team record) {
        // 参数校验
        checkParams(record);
        // 不能修改业务类型
        Team returnTeam = this.baseMapper.selectByPrimaryKey(record.getTeamId());
        if (!returnTeam.getType().equals(record.getType())) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "业务类型不能更改");
        }
        // 不能修改机构
        if (!returnTeam.getOrgId().equals(record.getOrgId())) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "所属机构不能更改");
        }
        // 目前只有聊天室的团队才有邀请码
        if (TeamTypeEnum.TALK_TEAM.getValue() == returnTeam.getType() && StringUtils.isBlank(returnTeam.getInviteCode())) {
            record.setInviteCode(getUniqueInvivationCode());
        }
        record.setUpdateTime(DateUtil.currentTimeSeconds());
        this.baseMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 解散团队
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(Long teamId) {
        // 默认团队不允许解散
        Team team = this.baseMapper.selectByPrimaryKey(teamId);
        if (null == team) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "团队无效");
        }
//        if (TeamTypeEnum.TALK_TEAM.getValue() == team.getType()) {
//            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "聊天室团队不能解散");
//        }
        if (TeamDefaultEnum.DEFAULT.getValue() == team.getIsDefault()) {
            throw new ServiceException(CodeStatus.OPERATE_TEAM_REFUSE.value(), "默认团队不允许解散");
        }
        // 获取该机构下默认团队
        Team defaultTeam = this.baseMapper.getDefaultTeam(team.getOrgId(), team.getType());
        if (null == defaultTeam) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "该机构下不存在默认团队");
        }
        if (TeamStatusEnum.NO_VALID.getValue() == defaultTeam.getStatus()) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "请先开启该机构下的默认团队");
        }
        // 获取该团队下所有成员
        List<Long> employeeIds = teamEmployeeMapper.employeeIdsByTeamId(team.getTeamId());
        if (CollectionUtils.isNotEmpty(employeeIds)) {
            // 将团队下所有成员归属到默认团队
            teamEmployeeMapper.employeeBelongToDefaultTeam(employeeIds, defaultTeam.getTeamId());
        }
        // 获取该团队下所有艺人
        List<Long> hostIds = teamHostMapper.idsByTeamId(team.getTeamId());
        List<String> hostUuidList = teamHostMapper.findHostsByTeamId(teamId);
        String batchId = UUID.genUuid();
        if (CollectionUtils.isNotEmpty(hostIds)) {
            // 将团队下所有艺人归属到该机构的默认团队
            teamHostMapper.hostsBelongToDefaultTeam(hostIds, defaultTeam.getTeamId(), defaultTeam.getType());
            for (Long hostId : hostIds) {
                TeamHost teamHost = teamHostMapper.selectById(hostId);
                if (teamHost != null) {
                    // 插入日志
                    ModifyRecordInfoDTO modifyRecordInfo = new ModifyRecordInfoDTO();
                    modifyRecordInfo.setOldOrgId(team.getOrgId());
                    modifyRecordInfo.setOldTeamId(team.getOrgId());
                    modifyRecordInfo.setNewOrgId(team.getOrgId());
                    modifyRecordInfo.setNewTeamId(defaultTeam.getTeamId());
                    hostModifyRecordService.addRecord(teamHost.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), batchId, HostOperateTypeEnum.TEAM_CLOSE, modifyRecordInfo, "", Constants.YES_1, "", "");
                }
            }
        }
        // 逻辑删除
        this.baseMapper.updateStatusByTeamId(TeamStatusEnum.NO_VALID.getValue(), teamId);
        BackstageOperateLog.OperateTypeEnum operateTypeEnum = null;
        if (CollectionUtils.isNotEmpty(hostUuidList) && TeamTypeEnum.LIVE_TEAM.getValue() == team.getType()) {
            // 同步主播团队到直播系统
            logger.info("解散团队获取到团队下主播：{},团队id:{}", JsonUtils.objectToString2(hostUuidList), teamId);
            String consortiaId = String.valueOf(defaultTeam.getTeamId());
            hostService.changeHostConsortia(consortiaId, hostUuidList);
            operateTypeEnum = BackstageOperateLog.OperateTypeEnum.CLOSE_LIVE_TEAM;
        } else if (CollectionUtils.isNotEmpty(hostUuidList) && TeamTypeEnum.TALK_TEAM.getValue() == team.getType()) {
            //聊天室通知php
            // 同步主播团队到直播系统
            logger.info("解散团队获取到团队下主播：{},团队id:{}", JsonUtils.objectToString2(hostUuidList), teamId);
            for (String chatUuid : hostUuidList) {
                ModifyRecordInfoDTO infoDTO = new ModifyRecordInfoDTO();
                infoDTO.setOldOrgId(team.getOrgId());
                infoDTO.setNewOrgId(defaultTeam.getOrgId());
                infoDTO.setOldTeamId(teamId);
                infoDTO.setNewTeamId(defaultTeam.getTeamId());
                SpringUtils.getBean(ChatRoomService.class).changeTeamForChat(chatUuid, defaultTeam.getTeamId(), JsonUtils.objectToString2(infoDTO), batchId, HostOperateTypeEnum.TEAM_CLOSE, null);
//                // 检查是不是房主，如果是去通知php，换绑公会
//                Map<String, Object> chatRoomAuthorInfo = adminForumChatService.getChatRoomAuthorInfo(chatUuid);
//                if(MapUtils.isNotEmpty(chatRoomAuthorInfo)){
//                    forumChatRoomService.changeChatRoomConsortia(chatUuid, defaultTeam.getTeamId(),teamId);
//                }
//                operateLog(chatUuid,teamId,defaultTeam.getTeamId());
            }
            operateTypeEnum = BackstageOperateLog.OperateTypeEnum.CLOSE_CHAT_ROOM_TEAM;
        }
        if (TeamTypeEnum.CALL_TEAM.getValue() == team.getType()){
            throw new ServiceException("team_type_error", "团队类型错误");
        }

        Organization org = null;
        if (team.getOrgId() != null) {
            org = SpringUtils.getBean(OrganizationMapper.class).selectByPrimaryKey(team.getOrgId());
        }
        afterCloseTeam(org, operateTypeEnum, team, SoaBaseParams.fromThread().getToken(), batchId);
    }

    /**
     * 启用禁用团队
     *
     * @param teamId
     * @param status
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startOrForbidden(Long teamId, Integer status) {
//        Team team = this.baseMapper.selectByPrimaryKey(teamId);
//        if (status == TeamStatusEnum.NO_VALID.getValue() && team.getStatus() == TeamStatusEnum.VALID.getValue()) {
//            // 如果当前团队下还存在有效用户（工作人员+主播），那么不允许删除
//            if (memberOrHostHasExist(teamId)){
//                throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(),"当前团队下还存在有效工作人员或者主播，不允许删除");
//            }
//        }
        this.baseMapper.updateStatusByTeamId(status, teamId);
    }

    /**
     * 团队名称是否已经存在
     *
     * @return
     */
    @Override
    public Boolean teamNameHasExist(Team team) {
        Long teamId = team.getTeamId() == null ? -1 : team.getTeamId();
        Team exitTeam = this.baseMapper.teamNameHasExist(team.getTeamName().trim(), team.getType());
        if (null != exitTeam && exitTeam.getTeamId().intValue() != teamId.intValue()) {
            return true;
        }
        return false;
    }


    /**
     * 获取该团队下的有效主播的数量
     */
    @Override
    public Integer getHostNumbers(Long teamId) {
        Integer count = this.baseMapper.findHostNumberByTeamId(teamId);
        Integer number = count == null ? 0 : count;
        return number;
    }


    /**
     * 获取该团队下的有效工作人员的数量
     */
    public Integer getEmployeeNumbers(Long teamId) {
        Integer count = this.baseMapper.findEmployeeNumberByTeamId(teamId);
        Integer number = count == null ? 0 : count;
        return number;
    }

    /**
     * 获取直播团队标识符
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getTeamSignList() {
        return teamSignConfig.getTeamSign();

    }

    /**
     * 根据机构id获取该机构下开启的业务类型
     *
     * @param orgId
     * @return
     */
    @Override
    public List<Integer> getTypesByOrgId(Long orgId) {
        return this.baseMapper.getTypesByOrgId(orgId);
    }

    /**
     * 创建默认团队
     * 机构注册的时候默认为不同类型的业务都创建一个团队（团队是否开启由审批人勾选）
     *
     * @return
     */
    @Override
    public void insertDefaultTeam(TeamDto teamDto) {

        // 创建该机构下直播默认团队
        insertDefaultTeamByType(teamDto.getOrgId(), "直播默认团队 - " + teamDto.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue(),
                teamDto.getTypeMap().get(TeamTypeEnum.LIVE_TEAM.getValue()), teamDto.getCreateBy(), teamSignConfig.getTeamSignByType(TeamTypeEnum.LIVE_TEAM.getValue()));
        // 创建该机构下趣聊默认团队
        insertDefaultTeamByType(teamDto.getOrgId(), "趣聊默认团队 - " + teamDto.getOrgId(), TeamTypeEnum.CALL_TEAM.getValue(),
                teamDto.getTypeMap().get(TeamTypeEnum.CALL_TEAM.getValue()), teamDto.getCreateBy(), teamSignConfig.getTeamSignByType(TeamTypeEnum.CALL_TEAM.getValue()));
        // 创建该机构下聊天室默认团队
        insertDefaultTeamByType(teamDto.getOrgId(), "聊天室默认团队 - " + teamDto.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue(),
                teamDto.getTypeMap().get(TeamTypeEnum.TALK_TEAM.getValue()), teamDto.getCreateBy(), teamSignConfig.getTeamSignByType(TeamTypeEnum.TALK_TEAM.getValue()));
    }

    /**
     * 创建默认团队
     * 机构注册的时候默认为不同类型的业务都创建一个团队（团队是否开启由审批人勾选）
     *
     * @return
     */
    @Override
    public void insertDefaultTeamByMove(TeamDto teamDto) {
        // 创建该机构下公会代表团队
        insertDefaultTeamByTypeByMove(teamDto.getOrgId(), teamDto.getTeamId(), teamDto.getTeamName(), TeamTypeEnum.LIVE_TEAM.getValue(),
                teamDto.getTypeMap().get(TeamTypeEnum.LIVE_TEAM.getValue()), teamDto.getCreateBy(), teamSignConfig.getTeamSignByType(TeamTypeEnum.LIVE_TEAM.getValue()));
        // 创建该机构下直播默认团队
        insertDefaultTeamByType(teamDto.getOrgId(), "直播默认团队 - " + teamDto.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue(),
                teamDto.getTypeMap().get(TeamTypeEnum.LIVE_TEAM.getValue()), teamDto.getCreateBy(), teamSignConfig.getTeamSignByType(TeamTypeEnum.LIVE_TEAM.getValue()));
        // 创建该机构下趣聊默认团队
        insertDefaultTeamByType(teamDto.getOrgId(), "趣聊默认团队 - " + teamDto.getOrgId(), TeamTypeEnum.CALL_TEAM.getValue(),
                teamDto.getTypeMap().get(TeamTypeEnum.CALL_TEAM.getValue()), teamDto.getCreateBy(), teamSignConfig.getTeamSignByType(TeamTypeEnum.CALL_TEAM.getValue()));
        // 创建该机构下聊天室默认团队
        insertDefaultTeamByType(teamDto.getOrgId(), "聊天室默认团队 - " + teamDto.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue(),
                teamDto.getTypeMap().get(TeamTypeEnum.TALK_TEAM.getValue()), teamDto.getCreateBy(), teamSignConfig.getTeamSignByType(TeamTypeEnum.TALK_TEAM.getValue()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertDefaultTeamByType(Long orgId, String teamName, Integer type, Integer status, String createBy, String teamSign) {
        Team record = new Team();
        record.setOrgId(orgId);
        record.setTeamName(teamName);
        record.setSignKey(teamSign);
        record.setType(type);
        record.setIsDefault(TeamDefaultEnum.DEFAULT.getValue());
        record.setStatus(status);
        record.setCreateBy(createBy);
        record.setCreateTime(System.currentTimeMillis() / 1000);
        checkParams(record);
        if (type == TeamTypeEnum.TALK_TEAM.getValue()) {
            record.setInviteCode(getUniqueInvivationCode());
        }
        this.save(record);
    }

    @Override
    public String getUniqueInvivationCode() {
        String inviteCode = "";
        while (true) {
            inviteCode = RandomStringUtil.genInviteCode();
            Team teamByInviteCode = getTeamByInviteCodeAndtype(inviteCode, TeamTypeEnum.TALK_TEAM.getValue());
            if (teamByInviteCode == null) {
                break;
            }
        }
        return inviteCode;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TeamHost> transferTeam(TransferTeamReq req) {
        if (Objects.isNull(req.getOriginOrgId()) && Objects.isNull(req.getOriginTeamId()) && CollectionUtils.isEmpty(req.getOriginUuidList())) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "缺少必填参数1");
        }
        if (Objects.isNull(req.getTargetOrgId()) || Objects.isNull(req.getTargetTeamId())) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "缺少必填参数2");
        }
        // 查询目标机构是否存在
        Organization organization = organizationService.getOrgInfo(req.getTargetOrgId());
        if (Objects.isNull(organization)) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "目标机构不存在");
        }
        // 查询目标团队是否存在
        Team team = info(req.getTargetTeamId());
        if (Objects.isNull(team)) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "目标团队不存在");
        }
        if (Objects.isNull(req.getEmployeeId())) {
            req.setEmployeeId(0L);
        }

        List<TeamHost> originList = new ArrayList<>();
        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("team_type", TeamTypeEnum.LIVE_TEAM.getValue());
        queryWrapper.eq("status", 1);
        if (Objects.nonNull(req.getOriginOrgId())) {
            queryWrapper.eq("org_id", req.getOriginOrgId());
        }
        // 判断是否有团队id
        if (Objects.nonNull(req.getOriginTeamId())) {
            queryWrapper.eq("team_id", req.getOriginTeamId());
        }
        // 判断是否存在hostuuid
        if (CollectionUtils.isNotEmpty(req.getOriginUuidList())) {
            queryWrapper.in("host_uuid", req.getOriginUuidList());
        }
        originList = teamHostMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(originList)) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "查无数据");
        }

        for (TeamHost host : originList) {
            // 记录操作日志
            TeamHostOperateLog log = new TeamHostOperateLog();
            log.setHostUuid(host.getHostUuid());
            log.setType(TeamHostOperateLogEnum.LIVE_CHANGE_TEAM.getValue());
            log.setCreateTime(DateUtil.currentTimeSeconds());
            log.setContent(JsonUtils.objectToString(req));
            log.setOperator("system");
            teamHostOperateLogMapper.insert(log);

            // 主播修改日志
            ModifyRecordInfoDTO modifyRecordInfo = new ModifyRecordInfoDTO();
            modifyRecordInfo.setOldOrgId(host.getOrgId());
            modifyRecordInfo.setOldTeamId(host.getTeamId());
            modifyRecordInfo.setNewOrgId(req.getTargetOrgId());
            modifyRecordInfo.setNewTeamId(req.getTargetTeamId());
            hostModifyRecordService.addRecord(host.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue(), UUID.genUuid(), HostOperateTypeEnum.BATCH_UPDATE_LIVE_TEAM, modifyRecordInfo, "", Constants.YES_1, "", "system");

            // 修改分润比例所属机构团队
            SharingProfitRecordSearch search = new SharingProfitRecordSearch();
            search.setHostUuid(host.getHostUuid());
            search.setStatus(HostSharingProfitStatusEnum.EFFECTIVE.getValue());
            HostSharingProfitRecord record = sharingProfitRecordMapper.getOneRecordBySearch(search);
            if (Objects.nonNull(record)) {
                record.setOrgId(req.getTargetOrgId());
                record.setTeamId(req.getTargetTeamId());
                sharingProfitRecordMapper.updateInfo(record);
            }

            // 修改公会团队数据
            host.setEmployeeId(req.getEmployeeId());
            host.setOrgId(req.getTargetOrgId());
            host.setTeamId(req.getTargetTeamId());
            teamHostMapper.updateById(host);

            // 通知直播修改经纪人
            try {
                // 这里需要同步主播更改后的经纪人到直播系统
                String accountUuid = teamEmployeeMapper.accountUuidByEmployeeId(req.getEmployeeId());
                hostService.changeHostBusinessman(host.getHostUuid(), Optional.ofNullable(accountUuid).orElse(""));
                logger.info("调整经纪人同步到直播系统，主播uuid:{}，经纪人id:{}", host.getHostUuid(), req.getEmployeeId());
            } catch (Exception e) {
                logger.error("调整经纪人同步到直播系统失败，主播uuid:{}，失败原因：{}", host.getHostUuid(), JsonUtils.objectToString(e));
                throw new ServiceException("sync_host_businessman_fail", "同步主播更改后的经纪人到直播系统失败");
            }

            // 同步主播团队到直播系统
            hostService.changeHostConsortia(String.valueOf(req.getTargetTeamId()), Collections.singletonList(host.getHostUuid()));
        }

        return originList;
    }

    @Override
    public Map<Long, String> MapName(List<Long> teamIds) {

        if(CollectionUtils.isEmpty(teamIds)){
            return new HashMap<>();
        }
        LambdaQueryWrapper<Team> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Team::getTeamId, Team::getTeamName)
                .in(Team::getTeamId, teamIds);
        List<Team> teams = this.baseMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(teams)){
            return new HashMap<>();
        }
        Map<Long, String> nameMap = teams.stream().collect(Collectors.toMap(l -> l.getTeamId(), l -> l.getTeamName(), (key1, key2) -> key1));
        return  nameMap;
    }

    @Override
    public Map<Long, Long> MapOrgId(List<Long> teamIdList) {

        if(CollectionUtils.isEmpty(teamIdList)){
            return new HashMap<>();
        }
        LambdaQueryWrapper<Team> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Team::getTeamId, Team::getTeamName, Team::getOrgId)
                .in(Team::getTeamId, teamIdList);
        List<Team> teams = this.baseMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(teams)){
            return new HashMap<>();
        }
        Map<Long, Long> team2OrgIdMap = teams.stream().collect(Collectors.toMap(l -> l.getTeamId(), l -> l.getOrgId(), (key1, key2) -> key1));
        return  team2OrgIdMap;
    }

    @Override
    public List<Team> listTeamByOrgIdList(List<Long> orgIdList, int teamType) {
        LambdaQueryWrapper<Team> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Team::getStatus, 1)
                        .eq(Team::getType, teamType)
                                .in(Team::getOrgId, orgIdList);
       return teamMapper.selectList(queryWrapper);

    }

    /**
     * 获取团队id对应的机构名称
     * @param teamIdSet
     * @return
     */
    @Override
    public Map<Long, String> mapOrgNameByTeamId(Set<Long> teamIdSet) {
        if(CollectionUtils.isEmpty(teamIdSet)){
            log.warn("multiLiveHostDataVos list not contains consortia id");
            return new HashMap<>();
        }
        Map<Long, Team> teamMap = getMapByTeamIds(new ArrayList<>(teamIdSet));
        if(CollectionUtil.isEmpty(teamMap)){
            log.warn("未查找到teamId列表为:"+JsonUtils.objectToString(teamIdSet)+"的团队数据.");
            return new HashMap<>();
        }

        Map<Long,String> teamIdOrgNameMap = new HashMap<>();
        Map<Long, Long> teamIdOrgIdMap = new HashMap<>();
        Set<Long> orgIdSet = new HashSet<>();
        for(Map.Entry<Long, Team> entry : teamMap.entrySet()){
            Long orgId = entry.getValue().getOrgId();
            teamIdOrgIdMap.put(entry.getKey(), orgId);
            if(!orgIdSet.contains(orgId)){
                orgIdSet.add(orgId);
            }
        }
        LambdaQueryWrapper<Organization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Organization::getOrgId, Organization::getOrgName)
        .in(Organization::getOrgId, orgIdSet);
        List<Organization> organizationList = organizationMapper.selectList(queryWrapper);
        Map<Long, String> orgIdNameMap = organizationList.stream().collect(Collectors.toMap(l -> l.getOrgId(), l -> l.getOrgName(), (key1, key2) -> key1));

        for(Map.Entry<Long,Long> entry : teamIdOrgIdMap.entrySet()){
            String orgName = orgIdNameMap.getOrDefault(entry.getValue(), "");
            teamIdOrgNameMap.put(entry.getKey(), orgName);
        }
        return teamIdOrgNameMap;
    }

    @Override
    public Map<Long, Long> mapOrgIdByTeamId(Set<Long> teamIdSet) {
        if(CollectionUtils.isEmpty(teamIdSet)){
            log.warn("teamIdSet is empty");
            return new HashMap<>();
        }
        Map<Long, Team> teamMap = getMapByTeamIds(new ArrayList<>(teamIdSet));
        if(CollectionUtil.isEmpty(teamMap)){
            log.warn("未查找到teamId列表为:"+JsonUtils.objectToString(teamIdSet)+"的团队数据.");
            return new HashMap<>();
        }


        Map<Long, Long> teamIdOrgIdMap = new HashMap<>();
        for(Map.Entry<Long, Team> entry : teamMap.entrySet()){
            Long orgId = entry.getValue().getOrgId();
            teamIdOrgIdMap.put(entry.getKey(), orgId);

        }
        return teamIdOrgIdMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertDefaultTeamByTypeByMove(Long orgId, Long teamId, String teamName, Integer type, Integer status, String createBy, String teamSign) {
        Team record = new Team();
        record.setTeamId(teamId);
        record.setOrgId(orgId);
        record.setTeamName(teamName);
        record.setSignKey(teamSign);
        record.setType(type);
        record.setIsDefault(TeamDefaultEnum.UN_DEFAULT.getValue());
        record.setStatus(status);
        record.setCreateBy(createBy);
        record.setCreateTime(System.currentTimeMillis() / 1000);
        checkParams(record);
        teamMapper.insertTeam(record);

    }

    /**
     * 参数校验
     *
     * @param record
     */
    private void checkParams(Team record) {
        if (null == record.getOrgId()) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "所属机构不能为空");
        }
        if (StringUtils.isBlank(record.getTeamName())) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "团队名称不能为空");
        }
        if (record.getTeamName().length() > 30) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "团队名称长度超出范围，请输入30字符以内的长度");
        }
        if (null == record.getType()) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "业务类型不能为空");
        }
        if (null == record.getStatus()) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "状态不能为空");
        }
        // 团队名称是否存在
        if (teamNameHasExist(record)) {
            throw new ServiceException(CodeStatus.CHECK_TEAM_PARAMS_FAILED.value(), "团队名称已经存在");
        }
    }

    public Team getTeamByInviteCodeAndtype(String inviteCode, Integer type) {
        QueryWrapper<Team> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("invite_code", inviteCode);
        queryWrapper.eq("type", type);
        return this.getOne(queryWrapper);
    }

    @Override
    public void deleteTeamById(Long teamId) {
        this.removeById(teamId);
    }

    @Override
    public List<Team> getTeamListByTeamIds(List<Long> teamIds) {
        QueryWrapper<Team> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("team_id", teamIds);
        return this.list(queryWrapper);
    }

    @Override
    public Map<Long, Team> getMapByTeamIds(List<Long> teamIds) {
        QueryWrapper<Team> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("team_id", teamIds);
        return baseMapper.selectList(queryWrapper)
                .stream()
                .collect(Collectors.toMap(Team::getTeamId, Function.identity(), (o, n) -> n));
    }


    @Override
    public Map<Long, String> mapTeamNameByTeamIds(List<Long> teamIds) {
        LambdaQueryWrapper<Team> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Team::getTeamId, Team::getOrgId, Team::getTeamName)
                    .in(Team::getTeamId, teamIds);
        return baseMapper.selectList(queryWrapper)
                .stream()
                .collect(Collectors.toMap(Team::getTeamId, Team::getTeamName, (o, n) -> n));
    }



    @Override
    public List<Team> getChatRoomList() {
        return this.baseMapper.selectChatTeamList();
    }

    @Override
    public TeamOrgInfoVO getTeamOrgInfoVO(Long teamId) {
        TeamOrgInfoVO teamOrgInfoVO = null;
        if (teamId == null) {
            return teamOrgInfoVO;
        }
        String orgAndTeamInfo = "";
        teamOrgInfoVO = teamMapper.selectTeamOrgInfoByTeamId(teamId);
        if (teamOrgInfoVO == null) {
            return teamOrgInfoVO;
        }
        if (teamOrgInfoVO != null && teamOrgInfoVO.getOrgId() != null) {
            orgAndTeamInfo = teamOrgInfoVO.getOrgName() + "-";
        }
        if (teamOrgInfoVO != null && teamOrgInfoVO.getTeamId() != null) {
            orgAndTeamInfo += teamOrgInfoVO.getTeamName();
        }
        teamOrgInfoVO.setOrgAndTeamInfo(orgAndTeamInfo);
        return teamOrgInfoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterCloseTeam(Organization organization, BackstageOperateLog.OperateTypeEnum operateTypeEnum, Team team, String operator, String batchId) {
        if (organization == null) {
            organization = new Organization();
        }
        if (operateTypeEnum == null) {
            operateTypeEnum = BackstageOperateLog.OperateTypeEnum.DEFAULT;
        }
        String info = "";
        if (Objects.equals(BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS_CLOSE_LIVE_TEAM.getValue(), operateTypeEnum.getValue())) {
            info = String.format("关闭直播业务,关闭团队,机构id=%s,机构名称=%s,团队id=%s,团队名称=%s,完成", organization.getOrgId(), organization.getOrgName(), team.getTeamId(), team.getTeamName());
        }
        if (Objects.equals(BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS_CLOSE_CHAT_ROOM_TEAM.getValue(), operateTypeEnum.getValue())) {
            info = String.format("关闭聊天室业务,关闭团队,机构id=%s,机构名称=%s,团队id=%s,团队名称=%s,完成", organization.getOrgId(), organization.getOrgName(), team.getTeamId(), team.getTeamName());
        } else {
            info = String.format("关闭团队,机构id=%s,机构名称=%s,团队id=%s,团队名称=%s,完成", organization.getOrgId(), organization.getOrgName(), team.getTeamId(), team.getTeamName());
        }
        backstageOperateLogService.addBackstageOperateLog(batchId, operateTypeEnum.getValue(), operator, info);
        // 发送钉钉通知
        TeamTypeEnum typeEnum = TeamTypeEnum.getByValue(team.getType());
        String teamTypeName = typeEnum == null ? "" : typeEnum.getMsg();
        pushService.pushBackstageOperateToDingRobot(operator, operateTypeEnum.getMsg(), teamTypeName, info);
    }

    @Override
    public void checkTeamCanClose(List<Team> teams, String batchId, String operator) {
        for (Team t : teams) {
            if (Objects.equals(t.getStatus(), Constants.YES_1)) {
                // 团队下还有艺人
                Integer count = SpringUtils.getBean(TeamHostService.class).countTeamHostInTeamId(t.getTeamId());
                if (count > 0) {
                    String info = String.format("关闭团队,机构id=%s,团队id=%s,失败,%s", t.getOrgId(), t.getTeamId(), "聊天室团队[" + t.getTeamName() + "]下还有" + count + "个艺人");
                    if (Objects.equals(t.getType(), TeamTypeEnum.LIVE_TEAM.getValue())) {
                        info = String.format("关闭直播业务,关闭团队,机构id=%s,团队id=%s,失败,%s", t.getOrgId(), t.getTeamId(), "直播团队[" + t.getTeamName() + "]下还有" + count + "个主播");
                        backstageOperateLogService.addBackstageOperateLogTxNew(batchId, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS_CLOSE_CHAT_ROOM_TEAM.getValue(), operator, info);
                    } else if (Objects.equals(t.getType(), TeamTypeEnum.TALK_TEAM.getValue())) {
                        info = String.format("关闭聊天室业务,关闭团队,机构id=%s,团队id=%s,失败,%s", t.getOrgId(), t.getTeamId(), "聊天室团队[" + t.getTeamName() + "]下还有" + count + "个艺人");
                        backstageOperateLogService.addBackstageOperateLogTxNew(batchId, BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS_CLOSE_CHAT_ROOM_TEAM.getValue(), operator, info);
                    }
                    throw new ServiceException("team_has_host", "聊天室团队[" + t.getTeamName() + "]下还有" + count + "个艺人");
                }
            }
        }
    }

    @Override
    public Map<Long, Integer> getTeamStatus(List<Long> teamIds) {
        if (CollectionUtils.isEmpty(teamIds)) {
            return new HashMap<>();
        }
        Map<Long, Integer> resultMap = new HashMap<>();
        List<List<Long>> partition = Lists.partition(teamIds, 20);
        for (List<Long> list : partition) {
            List<Team> teams = this.baseMapper.teamListByTeamIds(list);
            if (CollectionUtils.isEmpty(teams)) {
                continue;
            }
            for (Team team : teams) {
                resultMap.put(team.getTeamId(), team.getStatus());
            }
        }
        return resultMap;
    }
}
