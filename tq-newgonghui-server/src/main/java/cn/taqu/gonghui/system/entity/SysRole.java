package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;



/**
 * <AUTHOR>
 * @Date 2021/4/25
 */
@Data
public class SysRole  {

    private static final long serialVersionUID = 1L;

    /** 角色ID */
    @TableId(value = "role_id",type = IdType.AUTO)
    private Long roleId;

    /** 角色名称 */
    private String roleName;

    /** 角色权限 */
    private String roleKey;

    /** 业务类型（1-直播，2-趣聊，3-聊天室...） */
    private Integer type;

    /** 角色状态（1正常 0停用） */
    private Integer status;

    private Long createTime;

    private Long updateTime;

    private String remark;

    private String createBy;

    private String updateBy;


}
