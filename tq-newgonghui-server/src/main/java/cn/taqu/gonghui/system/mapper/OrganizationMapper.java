package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.OrgLegalPersonPhone;
import cn.taqu.gonghui.live.search.OrgStatisticsSearch;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.search.OrganizationInfoSearch;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

//@Mapper
public interface OrganizationMapper extends BaseMapper<Organization> {
    int deleteByPrimaryKey(Long orgId);

//    int insert(Organization record);

    int insertSelective(Organization record);

    Organization selectByPrimaryKey(Long orgId);

    int updateByPrimaryKeySelective(Organization record);

    int updateByPrimaryKey(Organization record);

    /**
     *    where org_status != 3 and apply_status != 5 这个没什么用，注意到时要去掉
     * @param accountUuid
     * @return
     */
    Organization getByAccountUuid(String accountUuid);

    List<Organization> findOrgInfoPageList(OrganizationInfoSearch organizationInfoSearch);

    List<Organization> findPassOrgInfoPageList(OrganizationInfoSearch organizationInfoSearch);

    Organization getOrgInfo(Organization record);

    List<Organization> findGuildInfoUuidAndNameList();

    List<Organization> findOrgUuidAndNameTree();

    Organization getByUuid(String uuid);

    Organization getBy(Organization record);

    Organization getByAccountUuidGroupConcatUrl(String accountUuid);  //已经解密

    List<Organization> getByList(Organization record);

    List<Organization> getListByOrgIds(Set<String> orgIdSet);

    List<Organization> orgNameListByOrgIds(Set<Long> orgIdSet);

    List<String> getByOrg(String businessPerson);

    List<String> getNewOrgList(OrgStatisticsSearch orgStatisticsSearch);

    void updateLiveSettlementeType(@Param("orgId") Long orgId,@Param("liveSettlementeType") Integer liveSettlementeType);

    void updateContent(@Param("orgId") Long orgId,@Param("content") String content);


    // -------------------------------------- 以下为数据迁移需要接口 ----------------------------------------
    /**
     * 根据机构uuid获取机构id
     */
    Organization getOrgIdByOrgUuid(String orgUuid);

    Long getMaxOrgUuid();

    List<Organization> getALLByList();

    List<Organization> getOrganizationByJointime(@Param("joinTimeStart") Long joinTimeStart,@Param("joinTimeEnd") Long joinTimeEnd);

    void updateModifyStatusByOrgId(@Param("orgId") Long orgId, @Param("modifyStatus") Integer modifyStatus);

    /**
     * 获取 打款信息相关信息
     * @param organization
     * @return
     */
    List<Organization> listRemitInfo(Organization organization);

    List<OrgLegalPersonPhone> selectLegalPersonPhoneByRange(@Param("curStartId") Long curStartId, @Param("curEndId") Long curEndId);

    void batchUpdateLegalPersonPhoneCipher(@Param("list") List<OrgLegalPersonPhone> list);


    void updateLegalPersonPhoneCipher(@Param("orgId") Long orgId, @Param("legalPersonPhoneCipher") String legalPersonPhoneCipher);

}
