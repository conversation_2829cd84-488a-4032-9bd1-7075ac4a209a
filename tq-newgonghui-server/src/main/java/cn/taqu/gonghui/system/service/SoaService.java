package cn.taqu.gonghui.system.service;

import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.soa.SOAUtil;
import cn.taqu.gonghui.system.dto.WeddingHostInfoDto;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
public class SoaService {

    private static final String CHATROOM_URL = "/soa/application/chatroom/api";

    public static List<WeddingHostInfoDto> getHostStat(Map<String, Object> param, Integer page) {
        String service = "AdminWeddingHost";
        String method = "getHostStat";

        try {
            SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                    .call(service, method, param, page);
            if (soaResponse.fail()) {
                log.error("调用soa司仪统计信息失败，失败原因{}", soaResponse.getMsg());
                return new ArrayList<>();
            }
            String soaResult = soaResponse.getData();
            if (StringUtils.isBlank(soaResult) || "[]".equals(soaResult) || "{}".equals(soaResult)) {
                log.warn("调用soa司仪统计信息为空");
                return new ArrayList<>();
            }
            log.info("getHostStat: {}", soaResult);
            return JSON.parseObject(soaResult, new com.alibaba.fastjson.TypeReference<List<WeddingHostInfoDto>>() {
            });
        } catch (Exception e) {
            log.error("调用soa司仪统计信息出错: ", e);
            return new ArrayList<>();
        }
    }

    public static List<Map<String, Object>> allOnlineHost() {
        String service = "AdminWeddingHost";
        String method = "allOnlineHost";

        try {
            SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                    .call(service, method);
            if (soaResponse.fail()) {
                log.error("调用soa获取所有在线司仪失败，失败原因{}", soaResponse.getMsg());
                return new ArrayList<>();
            }
            String soaResult = soaResponse.getData();
            if (StringUtils.isBlank(soaResult) || "[]".equals(soaResult) || "{}".equals(soaResult)) {
                log.warn("调用soa获取所有在线司仪为空");
                return new ArrayList<>();
            }
            log.info("allOnlineHost: {}", soaResult);
            return JSON.parseObject(soaResult, new com.alibaba.fastjson.TypeReference<List<Map<String, Object>>>() {
            });
        } catch (Exception e) {
            log.error("调用soa获取所有在线司仪: ", e);
            return new ArrayList<>();
        }
    }

    public static void changeHost(String weddingUuid, String hostUuid, Long consortiaId) {
        String service = "AdminWeddingHost";
        String method = "changeHost";

        try {
            SoaResponse soaResponse = SOAUtil.create(CHATROOM_URL)
                    .call(service, method, weddingUuid, hostUuid, consortiaId);

            if (soaResponse.success()) {
                log.info("更换司仪成功: {}", soaResponse.getData());
            } else {
                log.error("调用soa更换司仪失败，失败原因{}", soaResponse.getMsg());
                throw new ServiceException(soaResponse.getMsg());
            }
        } catch (Exception e) {
            log.error("调用soa更换司仪失败，失败原因:", e);
            throw new ServiceException("更换司仪失败");
        }
    }
}
