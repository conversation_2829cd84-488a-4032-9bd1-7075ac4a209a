package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.domain.TreeSelect;
import cn.taqu.gonghui.system.entity.SysMenu;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.vo.SysMenuVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/4/26
 */
public interface SysMenuService {

    /**
     * 用户端登陆注册时候调用获取当前用户拥有的菜单权限
     */
    List<SysMenuVo> getMenuListByLoginAccount(Integer businessType);

    /**
     * 查询系统菜单列表
     *
     * @return 菜单列表
     */
    List<SysMenu> selectMenuList(SysMenu menu);
    Map<String,Boolean> selectMenuPermsMap(List<String> pathList);

    /**
     * 根据用户ID查询权限
     *
     * @return 权限列表
     */
    Set<String> selectMenuPermsByUserId(String accountUuid);

    /**
     * 根据用户uuid和type查询权限
     *
     * @return 权限列表
     */
    Set<String> selectMenuPermsByUserId(String accountUuid,Integer type);

    /**
     * 根据用户ID查询菜单树信息
     *
     * @return 菜单列表
     */
    List<SysMenu> selectMenuTreeByUserId(Integer businessType);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    List<Long> selectMenuListByRoleId(Long roleId);

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    List<SysMenu> buildMenuTree(List<SysMenu> menus);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus);

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    SysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean hasChildByMenuId(Long menuId);

    /**
     * 查询菜单是否存在角色
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean checkMenuExistRole(Long menuId);

    /**
     * 新增保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    void insertMenu(SysMenu menu);

    /**
     * 修改保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    int updateMenu(SysMenu menu);

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    int deleteMenuById(Long menuId);

    /**
     * 校验菜单名称是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    String checkMenuNameUnique(SysMenu menu);

    /**
     * 校验按钮级别路径是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    String checkMenuRouterPathUnique(SysMenu menu);

    /**
     * 根据登陆账号查询用户角色
     */
    SysUser getUserByAccountUuid(String accountUuid);

    /**
     * 根据service 和 method 获取菜单路径
     */
    String getMenuNameByServiceAndMethod(String service,String method);
}
