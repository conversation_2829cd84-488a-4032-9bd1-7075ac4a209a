package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.AgreementValidEnum;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.system.dto.AgreementDto;
import cn.taqu.gonghui.system.entity.Agreement;
import cn.taqu.gonghui.system.entity.AgreementInfo;
import cn.taqu.gonghui.system.mapper.AgreementInfoMapper;
import cn.taqu.gonghui.system.mapper.AgreementMapper;
import cn.taqu.gonghui.system.search.AgreementSignLogSearch;
import cn.taqu.gonghui.system.service.AgreementInfoService;
import cn.taqu.gonghui.system.service.OrganizationUserService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.vo.AgreementSignLogVo;
import cn.taqu.gonghui.system.vo.AgreementVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 协议管理
 * <AUTHOR>
 */
@Service
public class AgreementInfoServiceImpl extends ServiceImpl<AgreementInfoMapper,AgreementInfo> implements AgreementInfoService {

    private Logger log = LoggerFactory.getLogger(AgreementInfoServiceImpl.class);
    @Autowired
    private AgreementMapper agreementMapper;

    @Autowired
    private TokenService tokenService;
    @Autowired
    private OrganizationUserService organizationUserService;

    /**
     * 根据协议id获取协议详情
     * @param agrId
     * @return
     */
    @Override
    public AgreementInfo getById(Long agrId) {
        AgreementInfo byId = this.baseMapper.getById(agrId, AgreementValidEnum.OPEN.getValue());
        return byId;
    }

    /**
     * 根据协议id和状态获取协议详情
     * @param agrId
     * @return
     */
    @Override
    public AgreementInfo getAgreementLogById(Long agrId) {
        AgreementInfo byId = this.baseMapper.getById(agrId, null);
        return byId;
    }

    /**
     * 获取有效协议列表（无分页）
     * @return
     */
    @Override
    public List<AgreementInfo> findList(String title) {
        List<AgreementInfo> allOrderByOrderLevelAsc = this.baseMapper.findAllOrderByOrderLevelAsc(AgreementValidEnum.OPEN.getValue(),title,null);
        return allOrderByOrderLevelAsc;
    }

    /**
     * 获取有效协议下拉tree
     * @return
     */
    @Override
    public List<Map<String, Object>> findMapForSearch() {
        List<AgreementInfo> searchList = this.baseMapper.findListForSearch();
        List<Map<String, Object>> mapList = new ArrayList<>();
        searchList.stream().forEach(searchData -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", searchData.getAgrId());
            map.put("title", searchData.getTitle());
            mapList.add(map);
        });
        return mapList;
    }

    /**
     * 添加协议
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgreementDto dto) {
        AgreementInfo agreementInfo = this.baseMapper.getByOrderLevel(dto.getOrderLevel());
        if (Objects.nonNull(agreementInfo)) {
            throw new ServiceException("agreementInfo_is_already_exist", "该顺序的协议已经存在，请确认");
        }
        checkAddParams(dto);

        AgreementInfo entity = new AgreementInfo();
        entity.setContent(dto.getContent());
        entity.setOperator(dto.getOperator());
        entity.setOrderLevel(dto.getOrderLevel());
        entity.setVersion(1);
        entity.setType(dto.getType());
        entity.setTitle(dto.getTitle());
        entity.setValid(AgreementValidEnum.OPEN.getValue());
        entity.setCreateTime(DateUtil.currentTimeSeconds());
        entity.setUpdateTime(DateUtil.currentTimeSeconds());
        this.save(entity);
    }

    /**
     * 更改协议
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgreementDto dto) {
        AgreementInfo oldEntity = this.baseMapper.getById(dto.getAgrId(),AgreementValidEnum.OPEN.getValue());
        AgreementInfo agreementInfo = this.baseMapper.getByOrderLevel(dto.getOrderLevel());

        if (Objects.nonNull(agreementInfo) && oldEntity.getAgrId().longValue() != agreementInfo.getAgrId().longValue()) {
            throw new ServiceException("orderlevel_is_already_exist", "该顺序协议已经存在");
        }
        if (Objects.isNull(oldEntity)) {
            throw new ServiceException("agreementInfo_is_null", "请勿重复更新协议，请刷新页面后查看");
        }
        // 已经被机构签订的协议不允许修改协议业务类型
        if (!dto.getType().equals(oldEntity.getType())) {
            Integer count = this.baseMapper.agreenmentIsSigned(oldEntity.getAgrId());
            if (null != count && 0 != count) {
                throw new ServiceException("invalid_operate","当前协议已经签署，不能修改业务类型");
            }
        }


        checkUpdateParams(dto);
        //如果只改了排序，只需要更新排序就行
        if (!isUpdate(dto, oldEntity)) {
            oldEntity.setOrderLevel(dto.getOrderLevel());
            this.baseMapper.updateByPrimaryKeySelective(oldEntity);
            return;
        }
        //如果更改了其他内容，得重新签署
        oldEntity.setValid(0);
        oldEntity.setUpdateTime(DateUtil.currentTimeSeconds());
        AgreementInfo newEntity = new AgreementInfo();
        newEntity.setVersion(oldEntity.getVersion() + 1);
        newEntity.setCreateTime(DateUtil.currentTimeSeconds());
        newEntity.setUpdateTime(DateUtil.currentTimeSeconds());
        newEntity.setValid(AgreementValidEnum.OPEN.getValue());

        newEntity.setOrderLevel(dto.getOrderLevel());
        newEntity.setType(dto.getType());
        newEntity.setContent(dto.getContent());
        newEntity.setOperator(dto.getOperator());
        newEntity.setTitle(dto.getTitle());

        this.save(newEntity);
        this.baseMapper.updateByPrimaryKeySelective(oldEntity);
    }

    /**
     * 关闭当前协议
     * @param agrId
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void close(Long agrId) {
        AgreementInfo closeEntity = this.baseMapper.getById(agrId,AgreementValidEnum.OPEN.getValue());
        if (Objects.isNull(closeEntity)) {
            throw new ServiceException("agreementInfo_is_null", "更新协议不存在，请刷新页面后查看");
        }
        closeEntity.setValid(AgreementValidEnum.CLOSE.getValue());
        this.baseMapper.updateByPrimaryKeySelective(closeEntity);
    }

    /**
     * 获取机构有效的签署协议记录
     * @param search
     * @return
     */
    @Override
    public List<AgreementSignLogVo> findAgreementSignLogPageList(AgreementSignLogSearch search) {
        PageHelper.startPage(search.getPageNum() == null ? 1 : search.getPageNum(),
                search.getPageSize() == null ? 10 : search.getPageSize());
        search.setValid(1);
        log.info("获取到签署记录参数：{}",JsonUtils.objectToString2(search));
        List<AgreementSignLogVo> logPageList = this.baseMapper.findAgreementSignLogPageList(search);
        PageHelper.clearPage();
        log.info("获取到签署记录返回值：{}", JsonUtils.objectToString2(logPageList));
        return logPageList;
    }

    /**
     * 根据机构id和协议顺序获取机构前属协议记录
     * @param orgId
     * @param orderLevel
     * @return
     */
    @Override
    public List<AgreementVo> findAgreementByOrgIdAndOrderLevel(Long orgId, Integer orderLevel) {
        return this.baseMapper.findAgreementByOrgIdAndOrderLevel(orgId,orderLevel);
    }

    public void checkAddParams(AgreementDto dto) {
        if (Objects.isNull(dto.getOrderLevel())) {
            throw new ServiceException("params_is_error", "协议顺序为空");
        }
        if (StringUtils.isBlank(dto.getTitle())) {
            throw new ServiceException("params_is_error", "协议标题为空");
        }
        if (StringUtils.isBlank(dto.getContent())) {
            throw new ServiceException("params_is_error", "协议内容为空");
        }
        if (Objects.isNull(dto.getType())) {
            throw new ServiceException("params_is_error", "业务类型为空");
        }
        if (StringUtils.isBlank(dto.getOperator())) {
            throw new ServiceException("params_is_error", "操作人为空");
        }
    }

    public void checkUpdateParams(AgreementDto dto) {
        if (StringUtils.isBlank(dto.getTitle())) {
            throw new ServiceException("params_is_error", "协议标题为空");
        }
        if (Objects.isNull(dto.getType())) {
            throw new ServiceException("params_is_error", "业务类型为空");
        }
        if (StringUtils.isBlank(dto.getContent())) {
            throw new ServiceException("params_is_error", "协议内容为空");
        }
        if (StringUtils.isBlank(dto.getOperator())) {
            throw new ServiceException("params_is_error", "操作人为空");
        }
    }

    /**
     * 只修改排序的时候不算修改
     *
     * @param dto
     */
    public Boolean isUpdate(AgreementDto dto, AgreementInfo oldEntity) {

        Boolean update = false;
        if (!dto.getTitle().equals(oldEntity.getTitle())) {
            update = true;
        }
        if (!dto.getContent().equals(oldEntity.getContent())) {
            update = true;
        }
        if (!dto.getType().equals(oldEntity.getType())) {
            update = true;
        }
        return update;
    }

//    -------------------------- 以下为用户端服务 ---------------------------------

    /**
     * 返回的签署map
     *
     * @param orgId
     * @return
     */
    @Override
    public List<Map<String, Object>> getSignMap(Long orgId) {
        List<Map<String, Object>> listMap = new ArrayList<>();

        // 获取当前机构下开通的业务类型
        List<Integer> types = organizationUserService.getOrgInfoType(orgId);
        //所有的签署协议
        List<AgreementInfo> agreementInfoList = this.baseMapper.findAllOrderByOrderLevelAsc(AgreementValidEnum.OPEN.getValue(),"",types);
        //找到该机构签署的协议
        List<Agreement> signList = this.baseMapper.findAgreementByOrgIdGroupByAgrId(orgId);
        if (CollectionUtils.isNotEmpty(agreementInfoList)) {
            agreementInfoList.stream().forEach(agreementInfo -> {
                Map<String, Object> map = new HashMap<>();
                Long id = agreementInfo.getAgrId();
                map.put("id", id);
                map.put("sign", false);
                signList.stream().forEach(sign -> {
                    if (id.longValue() == sign.getAgrId().longValue()) {
                        map.put("sign", true);
                    }
                });
                listMap.add(map);
            });
        }
        return listMap;
    }

    /**
     * 查看是否已经全部签署
     *
     * @param signMapList
     * @return
     */
    @Override
    public Boolean checkSign(List<Map<String, Object>> signMapList) {
        AtomicReference<Boolean> isSign = new AtomicReference<>(true);
        signMapList.stream().forEach(signMap -> {
            if (!(Boolean) signMap.get("sign")) {
                isSign.set(false);
            }
        });
        return isSign.get();
    }


    /**
     * 根据协议id获取协议
     * @param agrId
     * @return
     */
    @Override
    public AgreementInfo getByAgrId(Long agrId) {
        AgreementInfo agreementInfo = this.baseMapper.getByAgrId(agrId);
        return agreementInfo;
    }

    /**
     * 签署协议
     */
    @Override
    @Transactional
    public void sign(Long agreementId) {
        Long orgId = getOrgId();
        Agreement agreement = this.baseMapper.findByOrgIdAndAgrId(orgId, agreementId);
        if (Objects.nonNull(agreement)) {
            throw new ServiceException("agreement_is_already_sign", "协议已经签署");
        }
        AgreementInfo agreementInfo = this.baseMapper.getByAgrId(agreementId);
        if (AgreementValidEnum.CLOSE.getValue() == agreementInfo.getValid().intValue()) {
            throw new ServiceException("agreement_is_already_close", "协议已经关闭");
        }

        Agreement entity = new Agreement();
        entity.setOrgId(orgId);
        entity.setAgrId(agreementId);
        entity.setCreateTime(DateUtil.currentTimeSeconds());
        agreementMapper.insertSelective(entity);
    }

    /**
     * 先判断当前登陆用户角色是否是会长
     * 如果是会长获取会长所属机构id返回
     * 如果不是会长返回错误标识
     * @return
     */
    private Long getOrgId(){
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (null == loginUser.getUser().getOrgId()) {
            throw new ServiceException(CodeStatus.ORG_NOT_FOUNT_ERROR.value(),"机构信息无效");
        }
        return loginUser.getUser().getOrgId();
    }
}
