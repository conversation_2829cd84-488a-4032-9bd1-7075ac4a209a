package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.soa.FinanceSoaService;
import cn.taqu.gonghui.soa.SOAUtil;
import cn.taqu.gonghui.soa.dto.BusinessRequest;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import jdk.nashorn.internal.ir.annotations.Immutable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 业财
 * <AUTHOR>
 * @date 2024/06/21
 */
@Service
@Slf4j
public class FinanceSoaServiceImpl implements FinanceSoaService {

    /**
     * 业财etcd key
     */
    private static final String FINANCE_ETCD_URL = "/soa/java/finance";

    @Override
    public SoaResponse chatUserToGuildWithdrawal(BusinessRequest businessRequest) {
        try {
            SoaResponse soaResponse = SOAUtil.create(FINANCE_ETCD_URL)
                    .call("withdrawalManage", "chatUserToGuildWithdrawal", businessRequest);
            log.info("soaResponse={}", JsonUtils.objectToString(soaResponse));
            return soaResponse;
        } catch (Exception e) {
            log.error("placeOrder[FAIL]，msg:{},req:{}",e.getMessage(), businessRequest);
        }
        return null;
    }

    public BigDecimal getForumAccountChatReward(String uuid) {
        Map<String, String> form = ImmutableMap.of("account_uuid", uuid);
        try {
            SoaResponse response = SOAUtil.create(FINANCE_ETCD_URL).call("reward", "getForumAccountChatReward", form);
            String rs = response.getData();
            Map<String, BigDecimal> resp = JsonUtils.stringToObject(rs, new TypeReference<Map<String, BigDecimal>>() {});
            log.info("finance getForumAccountChatReward result {} {}", uuid, rs);
            return resp.get("balance");
        } catch (Exception e) {
            log.error("finance getForumAccountChatReward error " + uuid, e);
            return BigDecimal.ZERO;
        }
    }

}

