package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.gonghui.common.constant.RecommendApplyCardStatusEnum;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.system.dto.RecommendApplyCardDto;
import cn.taqu.gonghui.system.entity.RecommendApplyCard;
import cn.taqu.gonghui.system.mapper.RecommendApplyCardMapper;
import cn.taqu.gonghui.system.search.RecommendApplyCardSearch;
import cn.taqu.gonghui.system.service.RecommendApplyCardService;
import cn.taqu.gonghui.system.vo.RecommendApplyCardVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class RecommendApplyCardServiceImpl extends ServiceImpl<RecommendApplyCardMapper, RecommendApplyCard> implements RecommendApplyCardService {

    private static Logger logger = LoggerFactory.getLogger(RecommendApplyCardServiceImpl.class);

    @Autowired
    private TokenService tokenService;
    /**
     * 分页列表
     * @param search
     * @return
     */
    @Override
    public List<RecommendApplyCardVo> pageList(RecommendApplyCardSearch search) {
        PageHelper.startPage(search.getPageNum() == null ? 1 : search.getPageNum(),search.getPageSize() == null ? 20 : search.getPageSize());
        List<RecommendApplyCardVo> recommendApplyCardVos = this.baseMapper.queryByCondition(search);
        return recommendApplyCardVos;
    }

    /**
     * 作废
     * @param status
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatus(Integer status, Long id) {
        if (null == id) {
            throw new ServiceException("param_error","ID不能为空");
        }
        this.baseMapper.updateStatus(status,id);
    }

    /**
     * 发放申请卡
     * @param applyCardDto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sendApplyCard(RecommendApplyCardDto applyCardDto) {
        checkParams(applyCardDto);
        String[] orgIdArr = applyCardDto.getOrgIds().split(",");
        List<RecommendApplyCard> recommendApplyCardList = new ArrayList<>();
        Long nowSecond = DateUtil.currentTimeSeconds();
        Long endSecond = DateUtil.toSecond(DateUtil.changeDay(new Date(), applyCardDto.getEffectiveDays().intValue()));
        for (String orgId : orgIdArr) {
            RecommendApplyCard recommendApplyCard = new RecommendApplyCard();
            recommendApplyCard.setCardNo(UUID.randomUUID().toString().replace("-", ""));
            recommendApplyCard.setOrgId(Long.parseLong(orgId));
            recommendApplyCard.setCardCreateTime(nowSecond);
            recommendApplyCard.setCardExpireTime(endSecond);
            recommendApplyCard.setEffectiveDays(applyCardDto.getEffectiveDays());
            recommendApplyCard.setUseNum(applyCardDto.getTotalNum());
            recommendApplyCard.setTotalNum(applyCardDto.getTotalNum());
            recommendApplyCard.setCreateUser(applyCardDto.getOperator());
            recommendApplyCard.setStatus(1);
            recommendApplyCard.setCreateTime(DateUtil.currentTimeSeconds());
            recommendApplyCard.setUpdateTime(DateUtil.currentTimeSeconds());
            recommendApplyCardList.add(recommendApplyCard);
        }
        this.saveBatch(recommendApplyCardList);
    }

    /**
     * 参数校验
     */
    private void checkParams(RecommendApplyCardDto applyCardDto){
        if (null == applyCardDto.getTotalNum() || 0 == applyCardDto.getTotalNum()) {
            throw new ServiceException("param_error","请输入正确的发送数量");
        }
        if (null == applyCardDto.getEffectiveDays() || 0L == applyCardDto.getEffectiveDays()) {
            throw new ServiceException("param_error","请输入正确有效天数");
        }
        if (StringUtils.isBlank(applyCardDto.getOrgIds())) {
            throw new ServiceException("param_error","请至少选择一个机构");
        }
        if (StringUtils.isBlank(applyCardDto.getOperator())) {
            throw new ServiceException("param_error","创建人不能为空");
        }
    }

    // ----------------------------------- 以下服务提供给用户端调用 ----------------------------------------

    /**
     * 获取首页的友好的 推荐位申请卡 提醒数据  ...
     * 您当前有黄金时段申请卡「数量」张，其中有张申请卡 [次数: XX]  会在「日期时间」过期
     *
     * @return
     */
    @Override
    public Map<String, Object> getHomeRecommendApplyCardInfo() {
        Map<String, Object> resultMap = new HashMap<>();
        Long orgId = getOrgId();
        int count = this.baseMapper.countByOrgId(orgId, RecommendApplyCardStatusEnum.VALID.getValue());
        List<RecommendApplyCard> recommendApplyCardList = this.baseMapper.findListByOrgId(orgId, RecommendApplyCardStatusEnum.VALID.getValue());
        long useNum;
        String expireTime;
        if (CollectionUtils.isEmpty(recommendApplyCardList)) {
            expireTime = "暂无卡片过期";
            useNum = 0;
        } else {
            expireTime = DateUtil.dateToString("yyyy-MM-dd hh:mm:ss", DateUtil.fromSecond(recommendApplyCardList.get(0).getCardExpireTime()));
            useNum = recommendApplyCardList.get(0).getUseNum();
        }
        resultMap.put("count", count);
        resultMap.put("useNum", useNum);
        resultMap.put("expireTime", expireTime);
        return resultMap;
    }




    /**
     * 设置卡无效的定时任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setCardValid() {
        List<RecommendApplyCard> recommendApplyCardList = this.baseMapper.findUnavailableCard(RecommendApplyCardStatusEnum.VALID.getValue(),System.currentTimeMillis()/1000);
        if (!CollectionUtils.isEmpty(recommendApplyCardList)) {
            recommendApplyCardList.stream().forEach(card -> {
                card.setStatus(RecommendApplyCardStatusEnum.NO_VALID.getValue());
            });
            this.saveOrUpdateBatch(recommendApplyCardList);
        }

    }

    /**
     * 根据当前登陆用户获取用户所属机构id
     */
    private Long getOrgId(){
        // 获取用户信息
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        return  loginUser.getUser().getOrgId();
    }
}
