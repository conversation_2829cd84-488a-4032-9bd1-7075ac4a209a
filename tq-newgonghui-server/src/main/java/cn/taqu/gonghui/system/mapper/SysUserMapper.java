package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.search.UserSearch;
import cn.taqu.gonghui.system.vo.RoleVo;
import cn.taqu.gonghui.system.vo.SysUserVo;
import cn.taqu.gonghui.system.vo.UserTeamAndRoleVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @Date 2021/4/25
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

    void updateByPrimaryKey (SysUser sysUser);

    SysUser selectUserByMobile(@Param("mobile") String mobile, @Param("selectByDigest") Integer selectByDigest);

    List<SysUser> selectUserByMobileAndOrgId(String mobile);

    IPage<SysUserVo> selectUserBySearch(IPage<SysUserVo> page, @Param("search") UserSearch search);

    List<UserTeamAndRoleVo> getTeamNameAndRoleNameByUserId(@Param("userId")Long userId);

    /**
     * 查询当前机构负责人登录姓名
     * @param orgId
     * @return
     */
    String selectOrgManager(@Param("orgId") Long orgId);

    /**
     * 查询一个机构负责人
     * @param orgId
     * @return
     */
    SysUser selectOneManager(@Param("orgId") Long orgId);

    List<Long> selectUserIdsByRoleId(@Param("orgId") Long orgId,@Param("roleId") Long roleId);


    List<SysUser> selectUserAgent(SysUser sysUser);
    /**
     * 获取当前账号角色
     * @param userId
     * @return
     */
    List<RoleVo> getRoleByAccountUuid(@Param("userId") Long userId);

    /**
     * 获取经纪人employeeId
     */
    Long getEmployeeIdByAccountUuid(@Param("accountUuid")String accountUuid,@Param("type") Integer type);


    RoleVo getRoleByUserId(@Param("userId") Long userId,@Param("type") Integer type);

    /**
     * 获取负责人所属teamId
     */
    Long getTeamIdByAccountUuid(@Param("accountUuid")String accountUuid ,@Param("type") Integer type);


    List<String> selectAccountUuidByEmployeeIds(@Param("set") Set<Long> employeeIdSet);



    /**
     * 查询经纪人名称
     * @param uuidList
     */
    List<SysUser> userNameListByAccountUuidList(List<String> uuidList);


    void updateOrgName(@Param("orgName") String orgName,@Param("orgId") Long orgId);

    /**
     * 查询当前机构下所有的登录账号
     * @param orgId
     */
    List<SysUser> userListByOrgId(Long orgId);

    // ----------------------  以下是数据迁移需要用到的接口  --------------------------------
    void deleteByOrgId(Long orgId);

    /**
     * 根据账号获取账号信息
     */
    List<SysUser> userListByAccountUuid(String accountUuid);


    int updateNameByUserId(@Param("userId") Long userId,@Param("userName") String userName);

    void batchDeleteByIds(Set<Long> idSet);

    List<SysUser> userIsHasExisted(String accountUuid);

    void updateClearTxtByRange(@Param("curStartId") Long curStartId, @Param("curEndId") Long curEndId);
}
