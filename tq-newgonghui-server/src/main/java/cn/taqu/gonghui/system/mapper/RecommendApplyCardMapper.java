package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.RecommendApplyCard;
import cn.taqu.gonghui.system.search.RecommendApplyCardSearch;
import cn.taqu.gonghui.system.vo.RecommendApplyCardVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RecommendApplyCardMapper extends BaseMapper<RecommendApplyCard> {

    /**
     * 作废操作
     */
    void updateStatus(@Param("status") Integer status, @Param("id") Long id);

    /**
     * 条件筛选
     */
    List<RecommendApplyCardVo> queryByCondition(RecommendApplyCardSearch search);

    // ---------------------------- 以下接口为用户端使用 -----------------------------
    Integer countByOrgId(@Param("orgId") Long orgId,@Param("status") Integer status);

    List<RecommendApplyCard> findListByOrgId(@Param("orgId") Long orgId,@Param("status") Integer status);

    List<RecommendApplyCard> getReleaseCard(@Param("orgId") Long orgId,@Param("status") Integer status,@Param("currentTime") Long currentTime);

    List<RecommendApplyCard> findUnavailableCard(@Param("status") Integer status,@Param("currentTime") Long currentTime);
}
