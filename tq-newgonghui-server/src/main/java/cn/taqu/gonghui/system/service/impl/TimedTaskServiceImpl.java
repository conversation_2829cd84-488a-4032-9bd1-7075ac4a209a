package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.utils.UUID;
import cn.taqu.gonghui.live.entity.LiveHostStatisticsDay;
import cn.taqu.gonghui.live.mapper.LiveHostStatisticsDayMapper;
import cn.taqu.gonghui.live.search.LiveHostSearch;
import cn.taqu.gonghui.live.util.LiveDateUtils;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import cn.taqu.gonghui.system.service.ManageOperateService;
import cn.taqu.gonghui.system.service.TimedTaskService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TimedTaskServiceImpl implements TimedTaskService {


    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private OperationPersonMapper operationPersonMapper;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    private LiveHostStatisticsDayMapper liveHostStatisticsDayMapper;
    @Autowired
    private MyDataMapper myDataMapper;
    @Autowired
    private ManageOperateService manageOperateService;
    @Autowired
    private PerformanceMapper performanceMapper;

    @EtcdValue(value = "biz.live.date", defaultValue = "22")
    public static String liveDate;

    public void myDataTimedTask(String month) throws ParseException {
        log.info("currentMonth入参={}", month);
        //当前要计算的月份
        Date presentDate = null;
        if (!StringUtils.isEmpty(month)) {
            presentDate = LiveDateUtils.dateToStamp(month);
        } else {
            presentDate = new Date();
        }
        log.info("presentDate入参={}", presentDate.getTime());


        // 查询出所有的运营人员
        QueryWrapper<OperationPerson> operationPersonWrapper = new QueryWrapper<OperationPerson>();
        operationPersonWrapper.orderByDesc("create_time");
        List<OperationPerson> operationPersonList = operationPersonMapper.selectList(operationPersonWrapper);
        log.info("运营人员operationPersonList大小={}", operationPersonList.size());

        //查询出月份内引入的机构
        List<Organization> organizationList = organizationMapper.getOrganizationByJointime(LiveDateUtils.getMonthBegin(presentDate) / 1000, LiveDateUtils.getMonthEnd(presentDate) / 1000);
        log.info("机构数量organizationList.size={}", organizationList.size());

        //机构信息和 运营人员进行关联
        for (OperationPerson operationPerson : operationPersonList) {
            String logPre = UUID.genUuid() + ",";
            log.info(logPre + "operationPerson运营人员开始={}", operationPerson.getName());
            QueryWrapper<TeamHost> teamHostWrapper = new QueryWrapper<TeamHost>();

            //数据统计
            QueryWrapper<MyData> myDataWrapper = new QueryWrapper<MyData>();
            myDataWrapper.clear();
            myDataWrapper.eq("month", LiveDateUtils.getCurrentMonth(presentDate));
            myDataWrapper.eq("name", operationPerson.getName());

            MyData myData = getMyData(logPre, presentDate, operationPerson, myDataWrapper);

            //1.本月新引入机构总数
            List<Organization> organizationListTemp = organizationList.stream().filter(organization -> operationPerson.getName().equals(organization.getBusinessPerson())).collect(Collectors.toList());

            currentMonth(presentDate, operationPerson, logPre, teamHostWrapper, myData, organizationListTemp);

            log.info(logPre + "过去12个月 统计 开始");
            MyData myData12 = myDataMapper.selectOne(myDataWrapper);
            SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
            Calendar c = Calendar.getInstance();
            c.setTime(presentDate);
            c.add(Calendar.YEAR, -1);
            Date beforeDateStr_12Start = c.getTime();
            String beforeYearDateStartStr = format.format(beforeDateStr_12Start);
            log.info(logPre + "过去一年开始：" + beforeYearDateStartStr);
            String beforeYearDateEndStr = beforeDateStr(presentDate, -1);
            log.info(logPre + "过去一年结束：{}", beforeYearDateEndStr);
            Date beforeDate_12 = beforeDate(presentDate, -1);

            List<Organization> organizationList12Temp = organizationMapper.getOrganizationByJointime(LiveDateUtils.getMonthBegin(beforeDateStr_12Start) / 1000, LiveDateUtils.getMonthEnd(beforeDate_12) / 1000);
            List<Organization> organizationList12 = organizationList12Temp.stream().filter(organization -> operationPerson.getName().equals(organization.getBusinessPerson())).collect(Collectors.toList());

            List<Long> orgidList12 = organizationList12.stream().map(organization -> organization.getOrgId()).collect(Collectors.toList());
            log.info(logPre + "过去一年的机构数量,orgidList12.size：{}", orgidList12.size());
            log.info(logPre + "过去一年的机构数量,orgidList12：{}", JSON.toJSONString(orgidList12));
            if (orgidList12.isEmpty()) {
                log.warn(logPre + "过去一年的机构数量为空");
                myData.setTotalOrganization12(0);//"近12个月机构数",
                myData.setNewSignAnchor12(0);//"近12个月机构数引入的签约数量",
                myData.setReachTheStandardAnchor12(0);//"近12个月机构数的达标主播数",
                myData.setMonthSurviveRate12(new BigDecimal(0));//"近12个月机构上个月引入的主播存活率",
                myData.setGameGiftEarnings12(0);//"近12个月机构数引入游戏礼物收益",
                myData.setPanelGiftEarnings12(0);//"近12个月机构数面板礼物收益",
                myData.setGotalEarnings12(0);//"近12个月机构数总收益",
                myData.setThirdSignAnchor12(0);//"近12个月引入机构近3个月新引入的所有签约主播",
                myData.setThirdSignAnchor12CheckSurviveNum(0);//"近12个月引入机构近3个月新引入的所有签约主播在考核月留存数",
                myData.setThirdSignAnchor12SurviveRate(new BigDecimal(0));//"引入机构近3个月引入主播本月留存率"
                myData.setModifyTime(new Date());

                myDataMapper.updateById(myData);
            } else {

                teamHostWrapper.clear();
                teamHostWrapper.in("org_id", orgidList12);
                teamHostWrapper.ge("invite_time", LiveDateUtils.getMonthBegin(presentDate) / 1000);
                teamHostWrapper.le("invite_time", LiveDateUtils.getMonthEnd(presentDate) / 1000);

                log.info(logPre + "teamHostWrapper,invite_time 开始={}", LiveDateUtils.getMonthBegin(presentDate) / 1000);
                log.info(logPre + "teamHostWrapper,invite_time 结束={}", LiveDateUtils.getMonthEnd(presentDate) / 1000);
                List<TeamHost> teamHostList7 = teamHostMapper.selectList(teamHostWrapper);

                int signAnchor12 = teamHostList7.size();
                log.info(logPre + "过去一年 signAnchor12={}", signAnchor12);

                teamHostWrapper.clear();
                teamHostWrapper.in("org_id", orgidList12);

                log.info(logPre + "teamHostWrapper,invite_time 开始={}", LiveDateUtils.getMonthBegin(presentDate) / 1000);
                log.info(logPre + "teamHostWrapper,invite_time 结束={}", LiveDateUtils.getMonthEnd(presentDate) / 1000);

                List<TeamHost> teamHostList4 = teamHostMapper.selectList(teamHostWrapper);
                List<LiveHostStatisticsDay> presentDateLiveHostStatisticsDayList4 = getLiveHostStatisticsDays(presentDate, teamHostList4);
                int presentReachTheStandardNum4 = getReachTheStandardNum(presentDateLiveHostStatisticsDayList4);

                log.info(logPre + "过去一年 reachTheStandardAnchor12={}", presentReachTheStandardNum4);

                //4.本月新引入的机构的本月总游戏礼物收益
                int gameAmount12 = presentDateLiveHostStatisticsDayList4.stream().mapToInt(LiveHostStatisticsDay::getGameAmount).sum();
                log.info(logPre + "过去12个月引入机构，本月新引入的机构的本月总游戏礼物收益 gameAmount12={}", gameAmount12);


                //5.本月新引入的机构的本月总面板礼物收益  6.本月新引入的机构的本月总收益
                int hostAmount12 = presentDateLiveHostStatisticsDayList4.stream().mapToInt(LiveHostStatisticsDay::getHostAmount).sum();
                log.info(logPre + "过去12个月引入机构，本月新引入的机构的本月总收益 hostAmount12={}", hostAmount12);

                myData12.setTotalOrganization12(organizationList12.size());
                myData12.setNewSignAnchor12(signAnchor12);
                myData12.setReachTheStandardAnchor12(presentReachTheStandardNum4);
                myData12.setGameGiftEarnings12(gameAmount12);
                myData12.setPanelGiftEarnings12(hostAmount12 - gameAmount12);

                log.info("打9折,gameAmount12={},Math.round(gameAmount12 * 0.9) ={}", gameAmount12, (int) Math.round(gameAmount12 * 0.9));
                myData12.setGotalEarnings12(myData12.getPanelGiftEarnings12() + (int) Math.round(gameAmount12 * 0.9));
                myData12.setModifyTime(new Date());

                //计算留存
                //==============================近12个月机构上个月引入的主播存活率
                log.info(logPre + "近12个月机构上个月引入的主播存 统计开始");
                //引入机构上月引入主播本月留存率
                String beforeDateStr_1 = beforeDateStr(presentDate, -1);

                teamHostWrapper.clear();
                teamHostWrapper.in("org_id", orgidList12);
                teamHostWrapper.ge("invite_time", LiveDateUtils.getMonthBeginByStr(beforeDateStr_1) / 1000);
                teamHostWrapper.le("invite_time", LiveDateUtils.getMonthEndByStr(beforeDateStr_1) / 1000);

                log.info(logPre + "teamHostWrapper,invite_time 开始={}", LiveDateUtils.getMonthBeginByStr(beforeDateStr_1) / 1000);
                log.info(logPre + "teamHostWrapper,invite_time 结束={}", LiveDateUtils.getMonthEndByStr(beforeDateStr_1) / 1000);

                List<TeamHost> teamHostList2 = teamHostMapper.selectList(teamHostWrapper);
                if (teamHostList2.isEmpty()) {
                    log.warn(logPre + "operationPerson={},没有找到对应的teamHostList2", operationPerson.getName());
                    myDataMapper.updateById(myData12);
                } else {
                    log.info(logPre + "引入机构上月引入主播,teamHostList2大小={}", teamHostList2.size());
                    log.info(logPre + "引入机构上月引入主播,teamHostList2={}", JSON.toJSONString(teamHostList2));

                    // 查询本月主播数量直播记录
                    List<LiveHostStatisticsDay> presentDateLiveHostStatisticsDayList = getLiveHostStatisticsDays(presentDate, teamHostList2);
                    log.info(logPre + "查询本月主播数量直播记录 presentDateLiveHostStatisticsDayList.size={}", presentDateLiveHostStatisticsDayList.size());
                    log.info(logPre + "查询本月主播数量直播记录 presentDateLiveHostStatisticsDayList={}", JSON.toJSONString(presentDateLiveHostStatisticsDayList));

                    //计算本月达标数量
                    int presentReachTheStandardNum = getReachTheStandardNum(presentDateLiveHostStatisticsDayList);
                    log.info(logPre + "查询本月达标数据 presentReachTheStandardNum={}", presentReachTheStandardNum);

                    BigDecimal presentReachTheStandardNumbg = new BigDecimal(presentReachTheStandardNum);
                    BigDecimal teamHostListSizebg = new BigDecimal(teamHostList2.size());
                    BigDecimal monthSurviveRate12 = presentReachTheStandardNumbg.divide(teamHostListSizebg, 2, BigDecimal.ROUND_HALF_UP);
                    myData12.setMonthSurviveRate12(monthSurviveRate12);
                    log.info(logPre + "近12个月机构上个月引入的主播存活率 monthSurviveRate12={}", monthSurviveRate12);

                }

                //##########################近12个月机构上个月引入的主播存活率

                //=======================近12个月引入机构近3个月新引入的所有签约主播 开始
                String beforeDate3 = beforeDateStr(presentDate, -3);
                log.info(logPre + "过去3个月：{}", beforeDate3);
                teamHostWrapper.clear();
                teamHostWrapper.in("org_id", orgidList12);
                teamHostWrapper.ge("invite_time", LiveDateUtils.getMonthBeginByStr(beforeDate3) / 1000);
                teamHostWrapper.le("invite_time", LiveDateUtils.getMonthEndByStr(beforeDateStr_1) / 1000);
                log.info(logPre + "teamHostWrapper invite_time 开始={}", LiveDateUtils.getMonthBeginByStr(beforeDate3) / 1000);
                log.info(logPre + "teamHostWrapper invite_time 结束={}", LiveDateUtils.getMonthEndByStr(beforeDateStr_1) / 1000);

                List<TeamHost> teamHostList3 = teamHostMapper.selectList(teamHostWrapper);
                if (teamHostList3.isEmpty()) {
                    log.warn(logPre + "operationPerson={},没有找到对应的teamHostList3", operationPerson.getName());
                    myData12.setThirdSignAnchor12(teamHostList3.size());
                    myDataMapper.updateById(myData12);
                } else {
                    log.info(logPre + "近12个月引入机构近3个月新引入的所有签约主播,teamHostList3大小={}", teamHostList3.size());
                    log.info(logPre + "近12个月引入机构近3个月新引入的所有签约主播,teamHostList3={}", JSON.toJSONString(teamHostList3));

                    myData12.setThirdSignAnchor12(teamHostList3.size());
                    //###################近12个月引入机构近3个月新引入的所有签约主播
                    // 查询本月达标数据
                    List<LiveHostStatisticsDay> presentDateLiveHostStatisticsDayList1 = getLiveHostStatisticsDays(presentDate, teamHostList3);
                    int presentReachTheStandardNum1 = getReachTheStandardNum(presentDateLiveHostStatisticsDayList1);
                    myData12.setThirdSignAnchor12CheckSurviveNum(presentReachTheStandardNum1);
                    BigDecimal presentReachTheStandardNum1bg = new BigDecimal(presentReachTheStandardNum1);
                    BigDecimal teamHostList3bg = new BigDecimal(teamHostList3.size());
                    BigDecimal thirdSignAnchor12SurviveRate = presentReachTheStandardNum1bg.divide(teamHostList3bg, 2, BigDecimal.ROUND_HALF_UP);
                    myData12.setThirdSignAnchor12SurviveRate(thirdSignAnchor12SurviveRate);
                    myData12.setThirdSignAnchor12CheckSurviveNum(presentReachTheStandardNum1);
                    log.info(logPre + "引入机构近3个月引入主播本月留存率 thirdSignAnchor12SurviveRate={}", thirdSignAnchor12SurviveRate);
                    myDataMapper.updateById(myData12);
                }
            }
        }
    }

    private void currentMonth(Date presentDate, OperationPerson operationPerson, String logPre, QueryWrapper<TeamHost> teamHostWrapper, MyData myData, List<Organization> organizationListTemp) {
        if (organizationListTemp.isEmpty()) {
            log.warn(logPre + "operationPerson={},没有找到对应的organizationListTemp", operationPerson.getName());
            MyData myDataTemp = new MyData();
            myDataTemp.setId(myData.getId());
            myDataMapper.updateById(myDataTemp);
        } else {
            List<Long> orgidList = organizationListTemp.stream().map(organization -> organization.getOrgId()).collect(Collectors.toList());
            log.info(logPre + "operationPerson={},引入的机构有orgidList={}", operationPerson.getName(), JSON.toJSONString(orgidList));

            //2.本月引入机构新引入签约主播总数
            teamHostWrapper.clear();
            teamHostWrapper.in("org_id", orgidList);
            teamHostWrapper.ge("invite_time", LiveDateUtils.getMonthBegin(presentDate) / 1000);
            teamHostWrapper.le("invite_time", LiveDateUtils.getMonthEnd(presentDate) / 1000);

            log.info(logPre + "teamHostWrapper,invite_time 开始={}", LiveDateUtils.getMonthBegin(presentDate) / 1000);
            log.info(logPre + "teamHostWrapper,invite_time 结束={}", LiveDateUtils.getMonthEnd(presentDate) / 1000);

            List<TeamHost> teamHostList = teamHostMapper.selectList(teamHostWrapper);
            if (teamHostList.isEmpty()) {
                log.warn(logPre + "operationPerson={},没有找到对应的teamHostList1", operationPerson.getName());
                log.warn(logPre + "organizationListTemp的大小={},myData={},", organizationListTemp.size(), JSON.toJSONString(myData));
                myData.setNewOrganization(organizationListTemp.size());
                myData.setNewSignAnchor(teamHostList.size());
                myData.setNewReachTheStandardAnchor(0);
                myData.setNewGameGiftEarnings(0);
                myData.setNewPanelGiftEarnings(0);
                myData.setNewTotalEarnings(0);
                myData.setModifyTime(new Date());
                myDataMapper.updateById(myData);
            } else {
                log.info(logPre + "operationPerson={},引入的主播teamHostList.size={}", operationPerson.getName(), teamHostList.size());
                log.info(logPre + "operationPerson={},引入的主播teamHostList={}", operationPerson.getName(), JSON.toJSONString(teamHostList));

                // 查询 本月主播数据
                List<LiveHostStatisticsDay> liveHostStatisticsDayList = getLiveHostStatisticsDays(presentDate, teamHostList);
                log.info(logPre + "operationPerson={},引入的主播liveHostStatisticsDayList.size={}", operationPerson.getName(), liveHostStatisticsDayList.size());
                log.info(logPre + "operationPerson={},引入的主播liveHostStatisticsDayList={}", operationPerson.getName(), JSON.toJSONString(liveHostStatisticsDayList));

                //计算达标数量
                int reachTheStandardNum = getReachTheStandardNum(liveHostStatisticsDayList);
                log.info(logPre + "本月引入主播达标数量reachTheStandardNum={}", reachTheStandardNum);
                //4.本月新引入的机构的本月总游戏礼物收益
                int gameAmount = liveHostStatisticsDayList.stream().mapToInt(LiveHostStatisticsDay::getGameAmount).sum();
                log.info(logPre + "本月新引入的机构的本月总游戏礼物收益 gameAmount={}", gameAmount);

                //todo
                //5.本月新引入的机构的本月总面板礼物收益  6.本月新引入的机构的本月总收益
                int hostAmount = liveHostStatisticsDayList.stream().mapToInt(LiveHostStatisticsDay::getHostAmount).sum();
                log.info(logPre + "本月新引入的机构的本月总收益 hostAmount={}", hostAmount);

                myData.setNewOrganization(organizationListTemp.size());
                myData.setNewSignAnchor(teamHostList.size());
                myData.setNewReachTheStandardAnchor(reachTheStandardNum);
                myData.setNewGameGiftEarnings(gameAmount);
                myData.setNewPanelGiftEarnings(hostAmount - gameAmount);
                myData.setNewTotalEarnings(myData.getNewPanelGiftEarnings() + (int) Math.round(gameAmount * 0.9));

                myData.setModifyTime(new Date());
                myDataMapper.updateById(myData);
            }

        }
    }

    private MyData getMyData(String logPre, Date presentDate, OperationPerson operationPerson, QueryWrapper<MyData> myDataWrapper) {

        MyData myData = myDataMapper.selectOne(myDataWrapper);
        log.info(logPre + "myDataWrapper={}", JSON.toJSONString(myDataWrapper));
        log.info(logPre + "执行月份myData={}", JSON.toJSONString(myData));
        if (myData == null) {
            myData = new MyData();
            myData.setMonth(LiveDateUtils.getCurrentMonth(presentDate));
            myData.setName(operationPerson.getName());
            myData.setMonthSurviveRate12(BigDecimal.valueOf(0));
            myData.setThirdSignAnchor12SurviveRate(BigDecimal.valueOf(0));
        }

        if (myData.getId() == null) {
            log.info(logPre + "新增myData={}", JSON.toJSONString(myData));
            myDataMapper.insert(myData);
        } else {
            log.info(logPre + "修改myData={}", JSON.toJSONString(myData));
            myData.setModifyTime(new Date());
            myDataMapper.updateById(myData);
        }
        myDataWrapper.clear();
        myDataWrapper.eq("month", LiveDateUtils.getCurrentMonth(presentDate));
        myDataWrapper.eq("name", operationPerson.getName());

        myData = myDataMapper.selectOne(myDataWrapper);
        log.info(logPre + "myDataWrapper={}", JSON.toJSONString(myDataWrapper));
        log.info(logPre + "再次查询myData={}", JSON.toJSONString(myData));

        return myData;
    }

    @Override
    public void myPerformance(String month) throws ParseException {
        log.info("currentMonth入参={}", month);
        //当前要计算的月份
        Date presentDate = null;
        if (!StringUtils.isEmpty(month)) {
            presentDate = LiveDateUtils.dateToStamp(month);
        } else {
            presentDate = new Date();
        }
        // 查询出所有的运营人员
        QueryWrapper<OperationPerson> operationPersonWrapper = new QueryWrapper<OperationPerson>();
        operationPersonWrapper.orderByDesc("create_time");
        List<OperationPerson> operationPersonList = operationPersonMapper.selectList(operationPersonWrapper);
        log.info("运营人员operationPersonList={}", operationPersonList.size());
        log.info("运营人员operationPersonList={}", JSON.toJSONString(operationPersonList));

        //机构信息和 运营人员进行关联
        for (OperationPerson operationPerson : operationPersonList) {

            Performance performance = getPerformance(presentDate, operationPerson);

            //数据统计
            QueryWrapper<MyData> myDataWrapper = new QueryWrapper<MyData>();
            myDataWrapper.eq("month", LiveDateUtils.getCurrentMonth(presentDate));
            List<MyData> myDataList = myDataMapper.selectList(myDataWrapper);
            if (myDataList.isEmpty()) {
                continue;
            }

            List<MyData> myDataTempList = myDataList.stream().filter(myData -> operationPerson.getName().equals(myData.getName())).collect(Collectors.toList());
            if (myDataTempList.isEmpty()) {
                continue;
            }
            MyData myDataTemp = myDataTempList.get(0);

            int newPanelGiftEarningsSum = myDataList.stream().mapToInt(MyData::getNewPanelGiftEarnings).sum();
            int newGameGiftEarningsSum = myDataList.stream().mapToInt(MyData::getNewGameGiftEarnings).sum();

            performance.setMonth(LiveDateUtils.getCurrentMonth(presentDate));
            performance.setEarnings(myDataTemp.getNewPanelGiftEarnings() + myDataTemp.getNewGameGiftEarnings());
            performance.setTeamEarnings(newPanelGiftEarningsSum + newGameGiftEarningsSum);

            TargetInfo targetInfo = manageOperateService.getTargetInfo();
            BigDecimal moneyGrade = new BigDecimal(40);

            if (targetInfo.getMoney() > performance.getEarnings()) {
                BigDecimal moneyRate = new BigDecimal(performance.getEarnings()).divide(new BigDecimal(targetInfo.getMoney()), 2, RoundingMode.HALF_UP);
                moneyGrade = moneyRate.multiply(moneyGrade);
            }
            Long targeNumber = targetInfo.getNumber();
            //计算总的新签约数
            BigDecimal signAnchorGrade = new BigDecimal(20);

            int newSignAnchorSum = myDataList.stream().mapToInt(MyData::getNewSignAnchor).sum();
            if (targeNumber > newSignAnchorSum) {
                BigDecimal signAnchorRate = new BigDecimal(newSignAnchorSum).divide(new BigDecimal(targeNumber), 2, RoundingMode.HALF_UP);
                signAnchorGrade = signAnchorRate.multiply(signAnchorGrade);
            }

            //todo
            BigDecimal survivalRateGrade = new BigDecimal(40);

            int thirdSignAnchor12Sum = myDataList.stream().mapToInt(MyData::getThirdSignAnchor12).sum();
            int thirdSignAnchor12CheckSurviveNumSum = myDataList.stream().mapToInt(MyData::getThirdSignAnchor12CheckSurviveNum).sum();
            BigDecimal thirdSignAnchor12SurviveRate = null;
            if (thirdSignAnchor12CheckSurviveNumSum == 0) {
                thirdSignAnchor12SurviveRate = new BigDecimal(0);
            } else {
                thirdSignAnchor12SurviveRate = BigDecimal.valueOf(thirdSignAnchor12Sum).divide(BigDecimal.valueOf(thirdSignAnchor12CheckSurviveNumSum), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            }
            if (targetInfo.getSurvivalRate() > thirdSignAnchor12SurviveRate.intValue()) {
                BigDecimal signAnchorRate = thirdSignAnchor12SurviveRate.divide(new BigDecimal(targetInfo.getSurvivalRate()), 2, RoundingMode.HALF_UP);

                survivalRateGrade = signAnchorRate.multiply(survivalRateGrade);
            }


            BigDecimal sumGrade = moneyGrade.add(signAnchorGrade).add(survivalRateGrade);
            log.info("得分sumGrade={}", sumGrade.intValue());
            //当月真实营收
            int newTotalEarningsSum = myDataList.stream().mapToInt(MyData::getNewTotalEarnings).sum();
            log.info("团队当月真实营收newTotalEarningsSum={}", newTotalEarningsSum);
            //团队真实收益
            int realTotalEarningsSum = Math.round(new BigDecimal(newTotalEarningsSum - targetInfo.getMoney()).multiply(sumGrade).divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP).intValue());
            log.info("团队真实收益realTotalEarningsSum={}", realTotalEarningsSum);

            if (performance.getTeamEarnings() == 0) {
                performance.setPerformanceDeduct(0);
            } else {
                int performanceDeduct = Math.round(new BigDecimal(performance.getEarnings()).divide(new BigDecimal(performance.getTeamEarnings()), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(realTotalEarningsSum)).intValue());

                performance.setPerformanceDeduct(performanceDeduct);
            }
            performanceMapper.updateById(performance);

        }


    }

    private Performance getPerformance(Date presentDate, OperationPerson operationPerson) {
        QueryWrapper<Performance> performanceQueryWrapper = new QueryWrapper<Performance>();
        performanceQueryWrapper.clear();
        performanceQueryWrapper.eq("name", operationPerson.getName());
        performanceQueryWrapper.eq("month", LiveDateUtils.getCurrentMonth(presentDate));

        Performance performance = performanceMapper.selectOne(performanceQueryWrapper);
        if (performance == null) {
            performance = new Performance();
            performance.setMonth(LiveDateUtils.getCurrentMonth(presentDate));
            performance.setName(operationPerson.getName());
        }

        if (performance.getId() == null) {
            performanceMapper.insert(performance);
        }
        performanceQueryWrapper.clear();
        performanceQueryWrapper.eq("name", operationPerson.getName());
        performanceQueryWrapper.eq("month", LiveDateUtils.getCurrentMonth(presentDate));
        performance = performanceMapper.selectOne(performanceQueryWrapper);
        return performance;
    }

    private String beforeDateStr(Date presentDate, int num) {
        SimpleDateFormat format3 = new SimpleDateFormat("yyyyMM");
        Calendar c3 = Calendar.getInstance();
        c3.setTime(presentDate);
        c3.add(Calendar.MONTH, num);
        Date beforeDateEnd = c3.getTime();
        String beforeYearDateEnd3 = format3.format(beforeDateEnd);
        return beforeYearDateEnd3;
    }

    private Date beforeDate(Date presentDate, int num) {
        SimpleDateFormat format3 = new SimpleDateFormat("yyyyMM");
        Calendar c3 = Calendar.getInstance();
        c3.setTime(presentDate);
        c3.add(Calendar.MONTH, num);
        Date beforeDateEnd = c3.getTime();
        return beforeDateEnd;
    }

    private List<LiveHostStatisticsDay> getLiveHostStatisticsDays(Date presentDate, List<TeamHost> teamHostList) {
        List<String> hostUuidList = teamHostList.stream().map(teamHost -> teamHost.getHostUuid()).collect(Collectors.toList());
        log.info("hostUuidLis={}", hostUuidList);
        LiveHostSearch liveHostSearch = new LiveHostSearch();
        liveHostSearch.setUuidList(hostUuidList);
        liveHostSearch.setDayTime(LiveDateUtils.getCurrentMonth(presentDate));
        List<LiveHostStatisticsDay> liveHostStatisticsDayList = liveHostStatisticsDayMapper.getLiveHostStatisticsDayByHostUuidList(liveHostSearch);
        return liveHostStatisticsDayList;
    }

    private int getReachTheStandardNum(List<LiveHostStatisticsDay> liveHostStatisticsDayList) {
        Map<String, List<LiveHostStatisticsDay>> uuidMap = liveHostStatisticsDayList.stream().collect(Collectors.groupingBy(LiveHostStatisticsDay::getHostUuid));
        log.info("uuidMap 大小={}", uuidMap.size());
        //3.本月新引入达标主播总数
        int reachTheStandardNum = 0;
        for (List<LiveHostStatisticsDay> liveHostStatisticsDays : uuidMap.values()) {

            int hostAmount = liveHostStatisticsDays.stream().mapToInt(LiveHostStatisticsDay::getHostAmount).sum();
//            log.info("主播uuid={},金额hostAmount={}", liveHostStatisticsDays.get(0).getHostUuid(), hostAmount);

            //直播时间大于2小时 并且 直播天生大于22天 自然流水 大于5千
            List<LiveHostStatisticsDay> liveHostStatisticsDays1 = liveHostStatisticsDays.stream().filter(liveHostStatisticsDay -> 1 == liveHostStatisticsDay.getIsLive() && liveHostStatisticsDay.getTotalLiveTime() > 2 * 60).collect(Collectors.toList());
//            log.info("主播uuid={},天数liveHostStatisticsDays1={}", liveHostStatisticsDays.get(0).getHostUuid(), liveHostStatisticsDays1.size());

            if (liveHostStatisticsDays1.size() >= Integer.valueOf(liveDate)) {
                if (hostAmount > 5000 * 100) {
                    reachTheStandardNum++;
                }

            }
        }
        return reachTheStandardNum;
    }

}
