package cn.taqu.gonghui.system.mapper;


import cn.taqu.gonghui.system.entity.LegalPerson;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

public interface LegalPersonMapper extends BaseMapper<LegalPerson> {
    int deleteByPrimaryKey(Integer id);

    int insert(LegalPerson record);

    int insertSelective(LegalPerson record);

    LegalPerson selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(LegalPerson record);

    int updateByPrimaryKey(LegalPerson record);

    int deleteByUuid(String uuid);

    List<LegalPerson> getAllByOrgUuid(String orgUuid);
}