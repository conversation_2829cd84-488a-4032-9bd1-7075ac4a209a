package cn.taqu.gonghui.system.service.multilive.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.domain.DataPageRequest;
import cn.taqu.gonghui.common.domain.DataPageResult;
import cn.taqu.gonghui.common.domain.DataSortRequest;
import cn.taqu.gonghui.common.service.DataManager;
import cn.taqu.gonghui.common.utils.OssHandler;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.common.utils.SecurityUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.constant.MetaContentTypeEnum;
import cn.taqu.gonghui.live.param.*;
import cn.taqu.gonghui.live.vo.multilive.*;
import cn.taqu.gonghui.live.vo.multilive.dto.*;
import cn.taqu.gonghui.soa.MultiLiveSoaService;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.search.OrganizationInfoSearch;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamHostService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.service.multilive.MultiLiveStatsService;
import cn.taqu.gonghui.system.vo.RoleVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/23 19 16
 * discription
 */
@Slf4j
@Service
public class MultiLiveStatsServiceImpl implements MultiLiveStatsService {

    @Resource
    private MultiLiveSoaService multiLiveSoaService;

    @Resource
    private OssHandler ossHandler;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private TeamEmployeeService teamEmployeeService;

    @Resource
    private TeamService teamService;

    @Resource
    private TeamHostService teamHostService;

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private DataManager dataManager;


    /**
     * 管理端--搜索主播数据
     *
     * @param search
     * @return
     */
    @Override
    public PageDataResult searchAdminHostData(MultiLiveHostSearch search) {
        if (search.getStartTime() == null || search.getEndTime() == null) {
            log.warn("起始时间为空或结束时间为空，startTime:{}, endTime:{}", search.getStartTime(), search.getEndTime());
            return new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        }
        if (search.getOrgId() != null && search.getOrgId() > 0) {
            List<Team> teamList = teamService.selectTeamList(search.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (CollectionUtils.isEmpty(teamList)) {
                log.warn("机构：{}不存在任何团队。", search.getOrgId());
                return new PageDataResult(search.getPage(), search.getPageSize(), 0L, new ArrayList<>());
            }
            List<Long> teamIdList = teamList.stream()
                    .filter(l -> ObjectUtil.isNotNull(l.getTeamId()))
                    .map(Team::getTeamId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(teamList)) {
                log.warn("机构：{}不存在任何有团队Id的团队。", search.getOrgId());
                return new PageDataResult(search.getPage(), search.getPageSize(), 0L, new ArrayList<>());
            }
            search.setTeamIdList(teamIdList);
        } else {
            search.setTeamIdList(new ArrayList<>());
        }
        // 通过soa开始查询
        PageDataResult<MultiLiveHostDataDto> multiLiveHostDataVos = multiLiveSoaService.adminHostStatsData(search);
        fillOrgNameToHostDataList(multiLiveHostDataVos.getList());
        List<MultiLiveHostDataVo> voList = BeanUtil.copyToList(multiLiveHostDataVos.getList(), MultiLiveHostDataVo.class);
        return new PageDataResult<>(search.getPage(), search.getPageSize(), multiLiveHostDataVos.getTotal(), voList);
    }

    private void fillOrgNameToHostDataList(List<MultiLiveHostDataDto> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Set<Long> teamIdSet = list.stream().map(MultiLiveHostDataDto::getConsortiaId).collect(Collectors.toSet());
        Map<Long, String> teamIdOrgNameMap = teamService.mapOrgNameByTeamId(teamIdSet);
        if (CollectionUtil.isEmpty(teamIdOrgNameMap)) {
            return;
        }
        list.forEach(l -> {
            String orgNameOrDefault = teamIdOrgNameMap.getOrDefault(l.getConsortiaId(), "");
            l.setOrganName(orgNameOrDefault);
        });
    }


    /**
     * 管理端--搜索公会数据
     *
     * @param search
     * @return
     */
    @Override
    public PageDataResult searchAdminConsortiaData(MultiLiveConsortiaSearch search) {
        if (search.getStartTime() == null || search.getEndTime() == null) {
            log.warn("起始时间为空或结束时间为空，startTime:{}, endTime:{}", search.getStartTime(), search.getEndTime());
            return new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        }
        //List<Long> consortiaIdList = listTeamIdByOrgName(search.getOrgName());

        List<Long> teamIds = Lists.newArrayList();
        if (search.getOrgId() != null) {
            List<Team> teamList = teamService.selectTeamList(search.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (CollectionUtils.isEmpty(teamList)) {
                log.warn("机构：{}不存在任何团队。", search.getOrgId());
                return new PageDataResult(search.getPage(), search.getPageSize(), 0L, new ArrayList<>());
            }
            List<Long> teamIdList = teamList.stream()
                    .filter(l -> ObjectUtil.isNotNull(l.getTeamId()))
                    .map(Team::getTeamId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(teamList)) {
                log.warn("机构：{}不存在任何有团队Id的团队。", search.getOrgId());
                return new PageDataResult(search.getPage(), search.getPageSize(), 0L, new ArrayList<>());
            }
            teamIds.addAll(teamIdList);
        }

        search.setTeamIdList(teamIds);
        PageDataResult<MultiLiveConsortiaDataDto> pageDataResult = multiLiveSoaService.adminConsortiaStatsData(search);
        if (CollectionUtils.isEmpty(pageDataResult.getList())) {
            return pageDataResult;
        }
        fillOrgNameToConsortiaDataList(pageDataResult.getList());
        fillOrgIdToTeamDataList(pageDataResult.getList());
        List<MultiLiveConsortiaDataVo> voList = BeanUtil.copyToList(pageDataResult.getList(), MultiLiveConsortiaDataVo.class);
        return new PageDataResult<>(search.getPage(), search.getPageSize(), pageDataResult.getTotal(), voList);
    }

    private void fillOrgNameToConsortiaDataList(List<MultiLiveConsortiaDataDto> list) {

        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Set<Long> teamIdSet = list.stream().map(MultiLiveConsortiaDataDto::getConsortiaId).collect(Collectors.toSet());
        Map<Long, String> teamIdOrgNameMap = teamService.mapOrgNameByTeamId(teamIdSet);
        if (CollectionUtil.isEmpty(teamIdOrgNameMap)) {
            return;
        }
        list.forEach(l -> {
            String orgNameOrDefault = teamIdOrgNameMap.getOrDefault(l.getConsortiaId(), "");
            l.setOrganName(orgNameOrDefault);
        });
    }


    private void fillOrgIdToTeamDataList(List<MultiLiveConsortiaDataDto> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Set<Long> teamIdSet = list.stream().map(MultiLiveConsortiaDataDto::getConsortiaId).collect(Collectors.toSet());
        Map<Long, Long> teamIdOrgIdMap = teamService.mapOrgIdByTeamId(teamIdSet);
        if (CollectionUtil.isEmpty(teamIdOrgIdMap)) {
            return;
        }
        list.forEach(l -> {
            Long orgNameOrDefault = teamIdOrgIdMap.getOrDefault(l.getConsortiaId(), 0L);
            l.setConsortiaId(orgNameOrDefault);
        });
    }

    private List<Long> listTeamIdByOrgName(String orgName) {
        if (StringUtils.isBlank(orgName)) {
            return new ArrayList<>();
        }
        OrganizationInfoSearch search = new OrganizationInfoSearch();
        search.setOrgName(orgName);

        LambdaQueryWrapper<Organization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Organization::getOrgId, Organization::getOrgName)
                .like(Organization::getOrgName, orgName)
                .orderByDesc(Organization::getOrgId);
        List<Organization> list = organizationMapper.selectList(queryWrapper); //organizationMapper.findPassOrgInfoPageList(search);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> orgIdList = list.stream().map(Organization::getOrgId).collect(Collectors.toList());
        List<Team> teamList = teamService.listTeamByOrgIdList(orgIdList, TeamTypeEnum.LIVE_TEAM.getValue());
        if (CollectionUtils.isEmpty(teamList)) {
            log.warn("不存在团队归属于:{}", JsonUtils.objectToString(orgIdList));
            return new ArrayList<>();
        }
        List<Long> teamIdList = teamList.stream().map(Team::getTeamId).collect(Collectors.toList());
        return teamIdList;
    }

    /**
     * 管理端--获取主播数据下载地址
     *
     * @param search
     * @return
     */
    @Override
    public String getAdminHostDataDownloadUrl(MultiLiveHostSearch search) {
        Integer page = 1;
        Integer pageSize = 1000;
        search.setPage(page);
        search.setPageSize(pageSize);
        search.setExport(1);
        PageDataResult dataResult = searchAdminHostData(search);
        List<MultiLiveHostDataVo> allDataList = new ArrayList<>();
        allDataList.addAll(dataResult.getList());

        // 计算总页数
        long totalPage = PageUtil.totalPage(dataResult.getTotal().intValue(), pageSize);

        while (page < totalPage) {
            page++;
            search.setPage(page);
            PageDataResult pageResult = searchAdminHostData(search);
            if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                allDataList.addAll(pageResult.getList());
            }
        }
        String fileName = "多人娱乐主播数据" + search.getStartTimeFormat() + "-" + search.getEndTimeFormat() + "-";
        return buildFileToUpload(allDataList, MultiLiveHostDataVo.class, fileName, "主播数据");
//        return buildFileToUploadForHostData(search, allDataList);
    }

    /**
     * 管理端--获取公会数据下载地址
     *
     * @param search
     * @return
     */
    @Override
    public String getAdminConsortiaDataDownloadUrl(MultiLiveConsortiaSearch search) {
        Integer page = 1;
        Integer pageSize = 1000;
        search.setPage(page);
        search.setPageSize(pageSize);
        search.setExport(1);
        PageDataResult dataResult = searchAdminConsortiaData(search);
        List<MultiLiveConsortiaDataVo> allDataList = new ArrayList<>();
        allDataList.addAll(dataResult.getList());

        // 计算总页数
        long totalPage = PageUtil.totalPage(dataResult.getTotal().intValue(), pageSize);

        while (page < totalPage) {
            page++;
            search.setPage(page);
            PageDataResult pageResult = searchAdminConsortiaData(search);
            if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                allDataList.addAll(pageResult.getList());
            }
        }
        String fileName = "多人娱乐公会数据" + search.getStartTimeFormat() + "-" + search.getEndTimeFormat() + "-";
        return buildFileToUpload(allDataList, MultiLiveConsortiaDataVo.class, fileName, "公会数据");
    }

    /**
     * 用户端--搜索主播数据
     *
     * @param search
     * @return
     */
    @Override
    public PageDataResult searchHostData(MultiLiveHostSearchOfUM search) {
        if (search.getStartTime() == null || search.getEndTime() == null) {
            log.warn("起始时间为空或结束时间为空，startTime:{}, endTime:{}", search.getStartTime(), search.getEndTime());
            return new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        }
        buildSearchParamForCurUser(search);
        if (CollectionUtils.isEmpty(search.getHostUuids()) && CollectionUtils.isEmpty(search.getConsortiaIds())) {
            log.warn("搜索主播数据，但是当前用户没有归属公会和管理主播，搜索参数为：{}", JsonUtils.objectToString(search));
            return new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        }
        PageDataResult<MultiLiveHostDataOfUMDto> pageDataResult = multiLiveSoaService.hostStatsData(search);
        List<MultiLiveHostDataOfUMDto> list = pageDataResult.getList();
        if (CollectionUtil.isNotEmpty(list)) {
            fillOrgNameToHostDataListOfUM(list);
        }
        // DTO 转 VO
        List<MultiLiveHostDataOfUMVo> voList = BeanUtil.copyToList(list, MultiLiveHostDataOfUMVo.class);
        return new PageDataResult<>(search.getPage(), search.getPageSize(), pageDataResult.getTotal(), voList);
    }

    private void fillTeamNameToTeamDataListOfUM(List<MultiLiveConsortiaDataOfUMDto> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Set<Long> teamIdSet = list.stream().map(MultiLiveConsortiaDataOfUMDto::getConsortiaId).collect(Collectors.toSet());
        Map<Long, String> teamIdTeamNameMap = teamService.mapTeamNameByTeamIds(new ArrayList<>(teamIdSet));
        if (CollectionUtil.isEmpty(teamIdTeamNameMap)) {
            return;
        }
        list.forEach(l -> {
            String teamNameOrDefault = teamIdTeamNameMap.getOrDefault(l.getConsortiaId(), "");
            l.setTeamName(teamNameOrDefault);
        });
    }

    private void fillOrgNameToHostDataListOfUM(List<MultiLiveHostDataOfUMDto> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Set<Long> teamIdSet = list.stream().map(MultiLiveHostDataOfUMDto::getConsortiaId).collect(Collectors.toSet());
        Map<Long, String> teamIdOrgNameMap = teamService.mapOrgNameByTeamId(teamIdSet);
        if (CollectionUtil.isEmpty(teamIdOrgNameMap)) {
            return;
        }
        list.forEach(l -> {
            String orgNameOrDefault = teamIdOrgNameMap.getOrDefault(l.getConsortiaId(), "");
            l.setOrganName(orgNameOrDefault);
        });
    }

    @Override
    public String getHostDataDownloadUrl(MultiLiveHostSearchOfUM search) {
        Integer page = 1;
        Integer pageSize = 1000;
        search.setPage(page);
        search.setPageSize(pageSize);
        search.setExport(1);
        PageDataResult dataResult = searchHostData(search);
        List<MultiLiveHostDataOfUMVo> allDataList = new ArrayList<>();
        allDataList.addAll(dataResult.getList());

        // 计算总页数
        long totalPage = PageUtil.totalPage(dataResult.getTotal().intValue(), pageSize);

        while (page < totalPage) {
            page++;
            search.setPage(page);
            PageDataResult pageResult = searchHostData(search);
            if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                allDataList.addAll(pageResult.getList());
            }
        }
        String fileName = "多人娱乐主播数据" + search.getStartTimeFormat() + "-" + search.getEndTimeFormat() + "-";
        return buildFileToUpload(allDataList, MultiLiveHostDataOfUMVo.class, fileName, "主播数据");
//        return buildFileToUploadForHostDataOfUM(search, allDataList);
    }


    private cn.taqu.gonghui.common.domain.DataPageResult<Map<String, Object>> buildParamsToRequestRoomData(MultiLiveRoomSearch search,StringBuilder sb){
        ImmutableMap.Builder<String, Object> paramMapBuilder = ImmutableMap.builder();
        String uniqueValue = "/touch/live/MultiLiveRoomDetail";
        String uniqueKey = "multiLiveRoomData";
        switch (search.getTimeType()){
            case 1:
                paramMapBuilder.put("dt", ImmutableList.of(search.getStartTimeFormat(), search.getEndTimeFormat()));
                break;
            case 2:
                paramMapBuilder.put("dt", search.getEndTimeFormat());
                uniqueKey = "multiLiveRoomDataWeek";
                uniqueValue = "/touch/live/MultiLiveRoomDetailWeek";
                break;
            case 3:
                paramMapBuilder.put("dt", search.getEndTimeFormat());
                uniqueKey = "multiLiveRoomDataMonth";
                uniqueValue = "/touch/live/MultiLiveRoomDetailMonth";
                break;
            default:
                throw new ServiceException("un_valid_request", "时间类型不合法");
        }
//        paramMapBuilder.put("dt", ImmutableList.of(search.getStartTimeFormat(), search.getEndTimeFormat()));
        if (StringUtils.isNotBlank(search.getRoomUuid())) {
            paramMapBuilder.put("multi_live_uuid", search.getRoomUuid());
        }
        if (search.getOrgId() != null && search.getOrgId() > 0) {
            paramMapBuilder.put("consortia_id", Lists.newArrayList(search.getOrgId()));
        }
        ImmutableMap<String, Object> params = paramMapBuilder.build();
        sb.append("请求数据侧接口【"+uniqueValue+"】的请求参数为:"+ JsonUtils.objectToString(params));
        cn.taqu.gonghui.common.domain.DataPageResult<Map<String, Object>> mapDataPageResult = dataManager.dateApiSoaWithExcuteV2(uniqueKey, params,
                new DataPageRequest(search.getPage(), search.getPageSize()),
                new DataSortRequest("dt", "DESC"));
        return mapDataPageResult;
    }

    @Override
    public PageDataResult<?> searchAdminRoomData(MultiLiveRoomSearch search) {
        if (search.getStartTime() == null || search.getEndTime() == null) {
            log.warn("起始时间为空或结束时间为空，startTime:{}, endTime:{}", search.getStartTime(), search.getEndTime());
            return new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        }

        StringBuilder sb = new StringBuilder();

        try {
            DataPageResult<Map<String, Object>> mapDataPageResult = buildParamsToRequestRoomData(search, sb);
            sb.append(",响应信息为:" + JsonUtils.objectToString(mapDataPageResult));
            List<MultiLiveRoomDataDto> dataList = new ArrayList<>();
            List<Map<String, Object>> list = mapDataPageResult.getList();

            for (Map<String, Object> mapItem : list) {
                MultiLiveRoomDataDto item = JsonUtils.stringToObject(JsonUtils.objectToString(mapItem), MultiLiveRoomDataDto.class);
                dataList.add(item);
            }

            // DTO 转 VO
            List<MultiLiveRoomDataVo> voList = BeanUtil.copyToList(dataList, MultiLiveRoomDataVo.class);
            return new PageDataResult<>(search.getPage(), search.getPageSize(), mapDataPageResult.getTotal(), voList);
        } finally {
            log.info(sb.toString());
        }

    }

    @Override
    public PageDataResult searchConsortiaData(MultiLiveConsortiaSearchOfUM search) {
        if (search.getStartTime() == null || search.getEndTime() == null) {
            log.warn("起始时间为空或结束时间为空，startTime:{}, endTime:{}", search.getStartTime(), search.getEndTime());
            return new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        }
        buildSearchConsortiaParamForCurUser(search);
        PageDataResult<MultiLiveConsortiaDataOfUMDto> pageDataResult = multiLiveSoaService.consortiaStatsData(search);
        List<MultiLiveConsortiaDataOfUMDto> list = pageDataResult.getList();
        if (CollectionUtil.isNotEmpty(list)) {
            fillTeamNameToTeamDataListOfUM(list);
            fillOrgNameToConsortiaDataListOfUM(list);
        }

        // DTO 转 VO
        List<MultiLiveConsortiaDataOfUMVo> voList = BeanUtil.copyToList(list, MultiLiveConsortiaDataOfUMVo.class);
        return new PageDataResult(search.getPage(), search.getPageSize(), pageDataResult.getTotal(), voList);
    }

    private void fillOrgNameToConsortiaDataListOfUM(List<MultiLiveConsortiaDataOfUMDto> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Set<Long> teamIdSet = list.stream().map(MultiLiveConsortiaDataOfUMDto::getConsortiaId).collect(Collectors.toSet());
        Map<Long, String> teamIdOrgNameMap = teamService.mapOrgNameByTeamId(teamIdSet);
        if (CollectionUtil.isEmpty(teamIdOrgNameMap)) {
            return;
        }
        list.forEach(l -> {
            String orgNameOrDefault = teamIdOrgNameMap.getOrDefault(l.getConsortiaId(), "");
            l.setOrganName(orgNameOrDefault);
        });
    }

    @Override
    public String getConsortiaDataDownloadUrl(MultiLiveConsortiaSearchOfUM search) {
        Integer page = 1;
        Integer pageSize = 1000;
        search.setPage(page);
        search.setPageSize(pageSize);
        search.setExport(1);
        buildSearchConsortiaParamForCurUser(search);
        PageDataResult dataResult = searchConsortiaData(search);
        List<MultiLiveConsortiaDataOfUMVo> allDataList = new ArrayList<>();
        allDataList.addAll(dataResult.getList());

        // 计算总页数
        long totalPage = PageUtil.totalPage(dataResult.getTotal().intValue(), pageSize);

        while (page < totalPage) {
            page++;
            search.setPage(page);
            PageDataResult pageResult = searchConsortiaData(search);
            if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                allDataList.addAll(pageResult.getList());
            }
        }
        String fileName = "多人娱乐公会数据" + search.getStartTimeFormat() + "-" + search.getEndTimeFormat() + "-";
        return buildFileToUpload(allDataList, MultiLiveConsortiaDataOfUMDto.class, fileName, "公会数据");
//        return buildFileToUploadForConsortiaDataOfUM(search, allDataList);
    }

    @Override
    public PageDataResult searchRoomData(MultiLiveRoomSearchOfUM search) {
        if (search.getStartTime() == null || search.getEndTime() == null) {
            log.warn("起始时间为空或结束时间为空，startTime:{}, endTime:{}", search.getStartTime(), search.getEndTime());
            return new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        }
        buildSearchRoomParamForCurUser(search);
        checkRoomUuidOwnTeam(search);
        PageDataResult<MultiLiveRoomDataOfUMDto> pageDataResult = multiLiveSoaService.roomStatsData(search);
        List<MultiLiveRoomDataOfUMDto> list = pageDataResult.getList();
        if (CollectionUtil.isNotEmpty(list)) {
            fillOrgNameToRoomDataListOfUM(list);
        }
        // DTO 转 VO
        List<MultiLiveRoomDataOfUMVo> voList = BeanUtil.copyToList(list, MultiLiveRoomDataOfUMVo.class);
        return new PageDataResult(search.getPage(), search.getPageSize(), pageDataResult.getTotal(), voList);
    }

    private void fillOrgNameToRoomDataListOfUM(List<MultiLiveRoomDataOfUMDto> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Set<Long> teamIdSet = list.stream().map(MultiLiveRoomDataOfUMDto::getConsortiaId).collect(Collectors.toSet());
        Map<Long, String> teamIdOrgNameMap = teamService.mapOrgNameByTeamId(teamIdSet);
        if (CollectionUtil.isEmpty(teamIdOrgNameMap)) {
            return;
        }
        list.forEach(l -> {
            String orgNameOrDefault = teamIdOrgNameMap.getOrDefault(l.getConsortiaId(), "");
            l.setOrganName(orgNameOrDefault);
        });
    }

    @Override
    public String getRoomDataDownloadUrl(MultiLiveRoomSearchOfUM search) {
        Integer page = 1;
        Integer pageSize = 1000;
        search.setPage(page);
        search.setPageSize(pageSize);
        search.setExport(1);
        buildSearchRoomParamForCurUser(search);
        checkRoomUuidOwnTeam(search);
        PageDataResult dataResult = searchRoomData(search);
        List<MultiLiveRoomDataOfUMVo> allDataList = new ArrayList<>();
        allDataList.addAll(dataResult.getList());

        // 计算总页数
        long totalPage = PageUtil.totalPage(dataResult.getTotal().intValue(), pageSize);

        while (page < totalPage) {
            page++;
            search.setPage(page);
            PageDataResult pageResult = searchRoomData(search);
            if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                allDataList.addAll(pageResult.getList());
            }
        }
//        return buildFileToUploadForRoomDataOfUM(search, allDataList);
        String fileName = "多人娱乐房间数据" + search.getStartTimeFormat() + "-" + search.getEndTimeFormat() + "-";
        return buildFileToUpload(allDataList, MultiLiveRoomDataOfUMVo.class, fileName, "房间数据");
    }

    @Override
    public String getAdminRoomDataDownloadUrl(MultiLiveRoomSearch search) {

        Integer page = 1;
        Integer pageSize = 1000;
        search.setPage(page);
        search.setPageSize(pageSize);
        PageDataResult dataResult = searchAdminRoomData(search);
        List<MultiLiveRoomDataVo> allDataList = new ArrayList<>();
        allDataList.addAll(dataResult.getList());
        long totalPage = PageUtil.totalPage(dataResult.getTotal().intValue(), pageSize);
        while (page < totalPage) {
            page++;
            search.setPage(page);
            PageDataResult pageResult = searchAdminRoomData(search);
            if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                allDataList.addAll(pageResult.getList());
            }
        }
        String fileName = "多人娱乐房间数据" + search.getStartTimeFormat() + "-" + search.getEndTimeFormat() + "-";
        return buildFileToUpload(allDataList, MultiLiveRoomDataVo.class, fileName, "房间数据");
    }

    private cn.taqu.gonghui.common.domain.DataPageResult<Map<String, Object>> buildParamToRequestLiveBizData(MultiLiveLiveBizSearch search, StringBuilder sb){
        ImmutableMap.Builder<String, Object> paramMapBuilder = ImmutableMap.builder();
        String uniqueValue = "/touch/live/MultiLiveDetail";
        String uniqueKey = "multiLiveLiveBizData";
        switch (search.getTimeType()){
            case 1:
                paramMapBuilder.put("dt", ImmutableList.of(search.getStartTimeFormat(), search.getEndTimeFormat()));
                break;
            case 2:
                paramMapBuilder.put("dt", search.getEndTimeFormat());
                uniqueKey = "multiLiveLiveBizDataWeek";
                uniqueValue = "/touch/live/MultiLiveDetailWeek";
                break;
            case 3:
                paramMapBuilder.put("dt", search.getEndTimeFormat());
                uniqueKey = "multiLiveLiveBizDataMonth";
                uniqueValue = "/touch/live/MultiLiveDetailMonth";
                break;
            default:
                throw new ServiceException("un_valid_request", "时间类型不合法");
        }
        ImmutableMap<String, Object> params = paramMapBuilder.build();
        sb.append("请求数据侧数据信息【"+uniqueValue+"】的请求参数为:" + JsonUtils.objectToString(params));
        cn.taqu.gonghui.common.domain.DataPageResult<Map<String, Object>> mapDataPageResult =
                dataManager.dateApiSoaWithExcuteV2(uniqueKey, params,
                        new DataPageRequest(search.getPage(), search.getPageSize()),
                        new DataSortRequest("dt", "DESC"));

        sb.append(",响应结果为:" + JsonUtils.objectToString(mapDataPageResult));
        return mapDataPageResult;
    }
    @Override
    public PageDataResult<?> searchAdminLiveBizData(MultiLiveLiveBizSearch search) {
        if (search.getStartTime() == null || search.getEndTime() == null) {
            log.warn("起始时间为空或结束时间为空，startTime:{}, endTime:{}", search.getStartTime(), search.getEndTime());
            return new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        }
        StringBuilder sb = new StringBuilder();
        try {
            DataPageResult<Map<String, Object>> mapDataPageResult = buildParamToRequestLiveBizData(search, sb);
            List<MultiLiveLiveDataDto> dataList = new ArrayList<>();
            List<Map<String, Object>> list = mapDataPageResult.getList();
            for (Map<String, Object> mapItem : list) {
                MultiLiveLiveDataDto item = JsonUtils.stringToObject(JsonUtils.objectToString(mapItem), MultiLiveLiveDataDto.class);
                dataList.add(item);
            }
            // DTO 转 VO
            List<MultiLiveLiveDataVo> voList = BeanUtil.copyToList(dataList, MultiLiveLiveDataVo.class);
            return new PageDataResult<>(search.getPage(), search.getPageSize(), mapDataPageResult.getTotal(), voList);
        } finally {
            log.info(sb.toString());
        }
    }

    @Override
    public String getAdminLiveBizDataDownloadUrl(MultiLiveLiveBizSearch search) {
        Integer page = 1;
        Integer pageSize = 1000;
        search.setPage(page);
        search.setPageSize(pageSize);
        PageDataResult dataResult = searchAdminLiveBizData(search);
        List<MultiLiveLiveDataVo> allDataList = new ArrayList<>();
        allDataList.addAll(dataResult.getList());
        long totalPage = PageUtil.totalPage(dataResult.getTotal().intValue(), pageSize);
        while (page < totalPage) {
            page++;
            search.setPage(page);
            PageDataResult pageResult = searchAdminLiveBizData(search);
            if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                allDataList.addAll(pageResult.getList());
            }
        }
        String fileName = "多人娱乐直播业务数据" + search.getStartTimeFormat() + "-" + search.getEndTimeFormat() + "-";
        return buildFileToUpload(allDataList, MultiLiveLiveDataVo.class, fileName, "直播业务数据");
    }

    private String buildFileToUpload(List<?> allDataList, Class clazz, String fileName, String sheetName) {
        // 转化列表数据为Excel文件
        File xlsxFile = FileUtil.convertListToExcelFile(allDataList, clazz, fileName, sheetName);
        // 获取到下载地址
        String downloadUrl = ossHandler.getDownloadUrlWithFileAndContentType(xlsxFile, MetaContentTypeEnum.XLSX.getCode());
        return downloadUrl;
    }


    private void checkRoomUuidOwnTeam(MultiLiveRoomSearchOfUM search) {
        if (StringUtils.isBlank(search.getRoomUuid())) {
            return;
        }
        TeamHost teamHost = teamHostService.getHostByUuidAndType(search.getRoomUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
        if (null == teamHost) {
            throw new ServiceException("unfound_team_host", "未找到指定的房主uuid的主播信息~");
        }
        Long teamId = teamHost.getTeamId();
        if (!search.getTeamIds().contains(teamId)) {
            throw new ServiceException("permission_denied", "你无权查看指定的房主:" + search.getRoomUuid() + "的房间数据信息");
        }
    }

    private void buildSearchRoomParamForCurUser(MultiLiveRoomSearchOfUM search) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new ServiceException("un_login", "未登录，请先登录~");
        }
        // 在直播公会的角色
        RoleVo currentRole = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        // 机构负责人，查看负责的机构下的团队数据
        if (UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())) {
            List<Team> teams = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (CollectionUtils.isNotEmpty(teams)) {
                List<Long> teamIds = teams.stream().map(l -> l.getTeamId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(teamIds)) {
                    search.setTeamIds(teamIds);
                } else {
                    log.warn("机构：{} 包含的团队id列表为空.", user.getOrgId());
                    throw new ServiceException("org_not_team", "你所在机构不包含任何团队,请确认~");
                }
            }
        } else if (UserTypeEnum.LEADER.getCode().equals(currentRole.getRoleKey())) { //团队负责人，可以查看到他团队的房间数据
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (teamEmployee == null) {
                log.warn("当前用户为团队负责人，但未找到用户对应的团队。userId={}", user.getUserId());
                throw new ServiceException("notfound_team", "当前用户不归属于任何团队~");
            } else {
                search.setTeamIds(Lists.newArrayList(teamEmployee.getTeamId()));
            }
        } else {
            throw new ServiceException("permission_denied", "当前用户角色无法访问房间数据。");
        }

    }

    public void buildSearchConsortiaParamForCurUser(MultiLiveConsortiaSearchOfUM search) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new ServiceException("un_login", "未登录，请先登录~");
        }
        // 在直播公会的角色
        RoleVo currentRole = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        // 不是机构负责人，没有权限访问
        if (!UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())) {
            throw new ServiceException("permission_denied", "您不是机构负责人，无法访问~");
        }
        List<Team> teams = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
        if (CollectionUtils.isNotEmpty(teams)) {
            List<Long> teamIds = teams.stream().map(l -> l.getTeamId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(teamIds)) {
                search.setTeamIdList(teamIds);
                if (search.getTeamId() != null && search.getTeamId() > 0) {
                    if (!search.getTeamIdList().contains(search.getTeamId())) {
                        throw new ServiceException("unvalid_team_select", "你选择的团队不属于于您所在机构，请确认~");
                    }
                }
            } else {
                log.warn("机构：{} 包含的团队id列表为空.", user.getOrgId());
                throw new ServiceException("org_not_team", "你所在机构不包含任何团队~");
            }
        } else {
            log.warn("机构：{} 包含的团队列表为空.", user.getOrgId());
            throw new ServiceException("org_not_team", "你所在机构不包含任何团队~");
        }


    }


    public void buildSearchParamForCurUser(MultiLiveHostSearchOfUM search) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new ServiceException("un_login", "未登录，请先登录~");
        }
        // 在直播公会的角色
        RoleVo currentRole = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        if (UserTypeEnum.AGENTER.getCode().equals(currentRole.getRoleKey())) {//角色：经纪人 -->> 获取管理的艺人列表
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (null == teamEmployee || teamEmployee.getTeamId().equals(0)) {
                throw new ServiceException("notfound_team", "当前用户不归属于任何团队~");
            }
            List<TeamHost> hostList = teamHostService.getListByEmployeeId(teamEmployee.getEmployeeId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (CollectionUtils.isEmpty(hostList)) {
                throw new ServiceException("empty_host_list", "您当前未管理任何主播~");
            }
            List<String> hostUuids = hostList.stream().map(l -> l.getHostUuid()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(hostUuids)) { // 经纪人的主播列表为空
                throw new ServiceException("empty_host_list", "您当前未管理任何主播~");
            }
            // 接下来是：经纪人有管理的主播列表的情况
            if(StringUtils.isNotBlank(search.getHostUuid())){
                if(hostUuids.contains(search.getHostUuid())){  // 如果经纪人的主播列表包含了要搜索的主播，那就可以搜索该主播（前面已经赋值了，这里直接return就好了）
                    return;
                }
                // 搜索的主播不在经纪人的管理列表里面，直接抛出业务异常。
                throw new ServiceException("unvalid_host_uuid", "您不是指定搜索的主播的经纪人，请确认~");
            }else {
                search.setHostUuids(hostUuids);
            }
        } else if (UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())) { //角色：机构管理员 -->>获取机构下的团队
            List<Team> teams = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (CollectionUtils.isNotEmpty(teams)) {
                List<Long> teamIds = teams.stream().map(l -> l.getTeamId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(teamIds)) {
                    search.setConsortiaIds(teamIds);
                    return;
                }
            }
            throw new ServiceException("notfound_team", "当前用户不归属于任何团队~");
        } else if (UserTypeEnum.LEADER.getCode().equals(currentRole.getRoleKey())) { //角色:团队负责人 -->>获取对应的团队ID
            // 获取对应的团队
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            if (teamEmployee == null) {
                log.warn("当前用户为团队负责人，但未找到用户对应的团队。userId={}", user.getUserId());
                throw new ServiceException("notfound_team", "当前用户不归属于任何团队~");
            } else {
                search.setConsortiaIds(Lists.newArrayList(teamEmployee.getTeamId()));
            }
        } else {
            throw new ServiceException("unknown_role", "未知角色，请确认~");
        }
    }

}
