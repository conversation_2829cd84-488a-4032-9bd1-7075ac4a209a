package cn.taqu.gonghui.system.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class HostAmountTask {

    @JSONField(name = "list")
    private List<ListDTO> list;
    @JSONField(name = "reach_host_num")
    private String reachHostNum;
    @JSO<PERSON>ield(name = "new_host_num")
    private String newHostNum;
    @JSONField(name = "new_reach_host_num")
    private String newReachHostNum;
    @JSONField(name = "total")
    private String total;

    @NoArgsConstructor
    @Data
    public static class ListDTO {
        @JSONField(name = "month")
        private String month;
        @JSONField(name = "nickname")
        private String nickname;
        @JSONField(name = "avatar")
        private String avatar;
        @JSONField(name = "host_create_time")
        private String hostCreateTime;
        @JSONField(name = "sex_type")
        private String sexType;
        @J<PERSON>NField(name = "live_no")
        private String liveNo;
        @J<PERSON><PERSON>ield(name = "host_uuid")
        private String hostUuid;
        @JSONField(name = "apply_level")
        private String applyLevel;
        @JSONField(name = "real_name")
        private String realName;
        @JSONField(name = "total_duration")
        private String totalDuration;
        @JSONField(name = "valid_live")
        private String validLive;
        @JSONField(name = "total_amount")
        private String totalAmount;
        @JSONField(name = "panel_amount")
        private String panelAmount;
        @JSONField(name = "game_amount")
        private String gameAmount;
        @JSONField(name = "system_activity_amount")
        private String systemActivityAmount;
    }
}
