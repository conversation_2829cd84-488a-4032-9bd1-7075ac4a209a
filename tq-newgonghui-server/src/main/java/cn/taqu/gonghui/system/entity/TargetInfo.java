package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName(value = "target_info")
public class TargetInfo extends BaseDO{

    @TableField(value = "money")
    private Long money;
    @TableField(value = "survival_rate")
    private Integer survivalRate;
    @TableField(value = "number")
    private Long number;


}
