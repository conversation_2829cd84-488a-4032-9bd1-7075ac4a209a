package cn.taqu.gonghui.system.entity;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

@Data
@TableName(value = "operator_log", autoResultMap = true)
public class OperatorLog {
    private Long id;

    private String operator;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String oldContent;

    /**
     * 旧内容密文信息
     */
    @TableField(value = "old_content_cipher",jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String oldContentCipher;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String newContent;

    /**
     * 新内容密文信息
     */
    @TableField(value = "new_content_cipher",jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String newContentCipher;

    private String auditMsg;

    private Long createTime;

    private Long updateTime;

    private Integer type;

    private String orgName;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String chargePersonPhone;

    /**
     * 负责人手机号（密文）
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String chargePersonPhoneCipher;

    /**
     * 负责人手机号（摘要）
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = Sm3EncryptTypeHandler.class)
    private String chargePersonPhoneDigest;

    public String getChargePersonPhone() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.chargePersonPhoneCipher;
        }
        if(StringUtils.isBlank(this.chargePersonPhone) && StringUtils.isNotBlank(this.chargePersonPhoneCipher)){
            return this.chargePersonPhoneCipher;
        }
        return chargePersonPhone;
    }

    public String getChargePersonPhoneDigest() {
        return getChargePersonPhone();
    }

    public String getOldContent(){
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.oldContentCipher;
        }
        if(StringUtils.isBlank(this.oldContent) && StringUtils.isNotBlank(this.oldContentCipher)){
            return this.oldContentCipher;
        }
        return this.oldContent;
    }

    public String getNewContent(){
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.newContentCipher;
        }
        if(StringUtils.isBlank(this.newContent) && StringUtils.isNotBlank(this.newContentCipher)){
           return this.newContentCipher;
        }
        return this.newContent;
    }


    public void setChargePersonPhone(String chargePersonPhone) {
        this.chargePersonPhone = chargePersonPhone;
        this.chargePersonPhoneCipher = chargePersonPhone;
        this.chargePersonPhoneDigest = chargePersonPhone;
    }

    public void setOldContent(String oldContent){
        this.oldContent = oldContent;
        this.oldContentCipher = oldContent;
    }

    public void setNewContent(String newContent){
        this.newContent = newContent;
        this.newContentCipher = newContent;
    }
//
//    public String getChargePersonPhone(){
//        if(StringUtils.isBlank(this.chargePersonPhone) && StringUtils.isNotBlank(this.chargePersonPhoneCipher)){
//            return this.chargePersonPhoneCipher;
//        }
//        return this.chargePersonPhone;
//    }

}
