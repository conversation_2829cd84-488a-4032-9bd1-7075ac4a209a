package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.search.TeamSearch;
import cn.taqu.gonghui.system.vo.TeamOrgInfoVO;
import cn.taqu.gonghui.system.vo.TeamTreeVo;
import cn.taqu.gonghui.system.vo.TeamVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TeamMapper extends BaseMapper<Team> {
    int deleteByPrimaryKey(Long teamId);

    int insertTeam(Team record);

    int insertSelective(Team record);

    Team selectByPrimaryKey(Long teamId);

    int updateByPrimaryKeySelective(Team record);

    int updateByPrimaryKey(Team record);

    /**
     * 条件筛选查询团队列表信息
     *
     * @param query
     * @return
     */
    List<TeamVo> selectTeamListByCondition(TeamSearch query);

    /**
     * 获取有效团队列表
     */
    List<Team> selectTeamList(@Param("orgId") Long orgId, @Param("type") Integer type);

    /**
     * 校验团队名称是否已经存在
     */
    Team teamNameHasExist(@Param("teamName") String teamName, @Param("type") Integer type);

    /**
     * 校验该团队下是否还关联有成员
     */
    Integer memberHasExist(Long teamId);

    /**
     * 校验该团队下是否还关联有艺人
     */
    Integer liveHostHasExist(Long teamId);

    /**
     * 查询该团队下的所有成员（工作人员）
     */
    List<String> findMembersByTeamId(Long teamId);

    /**
     * 查询该团队下的所有艺人（主播）
     */
    List<String> findHostsByTeamId(Long teamId);

    /**
     * 获取该团队下的主播数量
     */
    Integer findHostNumberByTeamId(Long teamId);

    /**
     * 获取该团队下的工作人员数量
     */
    Integer findEmployeeNumberByTeamId(Long teamId);

    /**
     * 获取该团队下的所有负责人
     *
     * @return
     */
    List<String> findTeamLeaders(@Param("teamId") Long teamId, @Param("teamType") Integer teamType);

    /**
     * 根据团队id查询 leader
     * @param teamId
     * @param teamType
     * @return
     */
    TeamOrgInfoVO selectOneTeamLeader(@Param("teamId") Long teamId, @Param("teamType") Integer teamType);

    List<Team> teamListByTeamIds(List<Long> teamIds);

    /**
     * 获取机构下的默认团队
     */
    Team getDefaultTeam(@Param("orgId") Long orgId, @Param("type") Integer type);

    /**
     * 更改团队状态
     */
    void updateStatusByTeamId(@Param("status") Integer status, @Param("teamId") Long teamId);

    /**
     * 根据机构id获取该机构下开启了哪几种业务类型
     */
    List<Integer> getTypesByOrgId(Long orgId);

    List<Team> getTeamTreeByRole(@Param("accountUuid") String accountUuid, @Param("type") Integer type, @Param("orgId") Long orgId);


    /**
     * 获取有效团队列表id
     */
    List<Long> selectTeamListIds(@Param("orgId") Long orgId, @Param("type") Integer type);

    List<TeamTreeVo> selectTeamListByType(@Param("type") Integer type);

    /**
     * 获取机构下是否存在周结团队
     *
     * @param search
     */
    List<Team> isWeekType(TeamSearch search);

    /**
     * 查询团队机构基本信息(机构id，名称，团队id，名称)
     *
     * @param teamId
     * @return
     */
    TeamOrgInfoVO selectTeamOrgInfoByTeamId(@Param("teamId") Long teamId);

    /**
     * 批量获取团队基本信息
     *
     * @param teamIds
     * @return
     */
    List<TeamOrgInfoVO> selectTeamOrgInfoByTeamIds(List<Long> teamIds);

    // -----------------------------  以下为数据迁移需要用到的接口  -------------------------------------
    void deleteByOrgId(Long orgId);

    List<Team> selectChatTeamList();
}
