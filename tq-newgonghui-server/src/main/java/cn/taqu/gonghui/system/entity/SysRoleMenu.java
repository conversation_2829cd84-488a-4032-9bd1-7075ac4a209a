package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class SysRoleMenu  {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 角色ID */

    private Long roleId;

    /** 菜单ID */
    private Long menuId;

    private Long createTime;

    private Long updateTime;
}
