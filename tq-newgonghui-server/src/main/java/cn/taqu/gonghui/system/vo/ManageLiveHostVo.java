package cn.taqu.gonghui.system.vo;

import lombok.Data;

@Data
public class ManageLiveHostVo {
    /**
     * 经纪人uuid
     */
    private String businessman_uuid;
    /**
     * 经纪人姓名
     */
    private String businessman_name;
    /**
     * 主播uuid
     */
    private String host_uuid;

    /**
     * 他趣id(直播号)
     */
    private String live_no;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 昵称
     */
    private String nickName;
    /**
     * 机构uuid
     */
    private String consortia_id;
    /**
     * 机构名称
     */
    private String consortia_name;

    /**
     * 有效天数
     */
    private String valid_live;
    /**
     * 收益(趣豆)
     */
    private String amount;


    //新增主播相关
    private String host_amount; //收益
    private String valid_day; //有效天数
    private String card_id; //身份证
    private String add_consortia_time; //加入公会时间

    public ManageLiveHostVo(LiveHostVo vo){
        this.businessman_uuid = vo.getBusinessman_uuid();
        this.businessman_name = vo.getBusinessman_name();
        this.host_uuid = vo.getHost_uuid();
        this.live_no = vo.getLive_no();
        this.avatar = vo.getAvatar();
        this.nickName = vo.getNickName();
        this.consortia_id = vo.getConsortia_id();
        this.consortia_name = vo.getConsortia_name();
        this.valid_live = vo.getValid_live();
        this.amount = vo.getAmount();
        this.host_amount = vo.getHost_amount();
        this.valid_day = vo.getValid_day();
        this.card_id = vo.getCard_id();
        this.add_consortia_time = vo.getAdd_consortia_time();
    }
}
