package cn.taqu.gonghui.system.entity;

import lombok.Data;

@Data
public class QuestionFeedback {
    /**
     * 流水号
     */
    private Long id;

    /**
     * 反馈用户uuid
     */
    private String feedbackUuid;

    /**
     * 反馈机构id
     */
    private Long orgId;

    /**
     * 反馈团队id
     */
    private Long teamId;

    /**
     * 反馈页面地址
     */
    private String pageUrl;

    /**
     * 反馈页面名称（反馈菜单）
     */
    private String pageName;

    /**
     * 反馈时间
     */
    private Long feedbackTime;

    /**
     * 反馈类型
     */
    private Integer type;

    /**
     * 反馈标题
     */
    private String title;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 上传图片地址
     */
    private String imageUrl;

    /**
     * 状态（0-待处理，1-已处理）
     */
    private Integer status;

    /**
     * 跟进人
     */
    private String followUpPerson;

    /**
     * 处理后备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}