package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.HostSharingProfitRecord;
import cn.taqu.gonghui.system.search.SharingProfitRecordSearch;
import cn.taqu.gonghui.system.vo.HostSharingProfitRecordVo;
import cn.taqu.gonghui.system.vo.SharingResultVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface HostSharingProfitRecordMapper {
    int insertSelective(HostSharingProfitRecord record);

    int updateByPrimaryKey(HostSharingProfitRecord record);

    /**
     * 根据条件获取一个记录
     * @return
     */
    HostSharingProfitRecord getOneRecordBySearch(SharingProfitRecordSearch search);

    /**
     * 获取最新的一条待生效或者待确认或者已生效记录
     * @return
     */
    HostSharingProfitRecord getLastedOneRecord(SharingProfitRecordSearch search);

    /**
     * 获取满足条件的记录数量
     * @param search
     * @return
     */
    Integer countBySearch(SharingProfitRecordSearch search);

    /**
     * 更新部分信息
     */
    void updateInfo(HostSharingProfitRecord record);

    /**
     * 管理端分页列表
     */
    List<HostSharingProfitRecordVo> manageList(SharingProfitRecordSearch search);

    /**
     * 管理端分页列表
     */
    List<HostSharingProfitRecordVo> clientList(SharingProfitRecordSearch search);

    SharingResultVo detail(Long id,String hostUuid);

    List<HostSharingProfitRecord> idListBySearch(SharingProfitRecordSearch search);

    void autoExpire(List<Long> idList);

    /**
     * 批量写入
     * @param recordList
     */
    void batchInsert(List<HostSharingProfitRecord> recordList);

}
