package cn.taqu.gonghui.system.mapper;


import cn.taqu.gonghui.system.entity.ChargePerson;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

public interface ChargePersonMapper extends BaseMapper<ChargePerson> {
    int deleteByPrimaryKey(Integer id);

    int insert(ChargePerson record);

    int insertSelective(ChargePerson record);

    ChargePerson selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(ChargePerson record);

    int updateByPrimaryKey(ChargePerson record);

    int deleteByUuid(String uuid);

    List<ChargePerson> selectByOrgUuid(String orgUuid);
}