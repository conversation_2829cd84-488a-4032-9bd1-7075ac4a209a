package cn.taqu.gonghui.system.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class StatData implements Serializable {


    @JSONField(name = "day_amount_info")
    private DayAmountInfoDTO dayAmountInfo;
    @JSONField(name = "yesterday_amount_info")
    private YesterdayAmountInfoDTO yesterdayAmountInfo;
    @J<PERSON><PERSON>ield(name = "day_time_info")
    private DayTimeInfoDTO dayTimeInfo;
    @JSONField(name = "yesterday_time_info")
    private YesterdayTimeInfoDTO yesterdayTimeInfo;

    @NoArgsConstructor
    @Data
    public static class DayAmountInfoDTO {
        @JSONField(name = "value")
        private String value;
        @JSONField(name = "compare_ratio")
        private String compareRatio;
        @JSONField(name = "compare_value")
        private String compareValue;
    }

    @NoArgsConstructor
    @Data
    public static class YesterdayAmountInfoDTO {
        @J<PERSON><PERSON>ield(name = "value")
        private String value;
        @JSONField(name = "compare_ratio")
        private String compareRatio;
        @JSONField(name = "compare_value")
        private String compareValue;
    }

    @NoArgsConstructor
    @Data
    public static class DayTimeInfoDTO {
        @JSONField(name = "value")
        private String value;
        @JSONField(name = "compare_ratio")
        private String compareRatio;
        @JSONField(name = "compare_value")
        private String compareValue;
    }

    @NoArgsConstructor
    @Data
    public static class YesterdayTimeInfoDTO {
        @JSONField(name = "value")
        private String value;
        @JSONField(name = "compare_ratio")
        private String compareRatio;
        @JSONField(name = "compare_value")
        private String compareValue;
    }
}
