package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.entity.OrgLegalPersonPhone;
import cn.taqu.gonghui.system.entity.Organization;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/3 16 32
 * discription
 */
public interface IOrgnizatonService extends IService<Organization> {


    Map<Long, String> MapOrgName(List<Long> orgIdList);

    List<OrgLegalPersonPhone> selectLegalPersonPhoneByRange(Long curStartId, Long curEndId);

    void batchUpdateLegalPersonPhoneCipher(List<OrgLegalPersonPhone> list);

    void updateLegalPersonPhoneCipher(Long orgId, String legalPersonPhoneCipher);
}
