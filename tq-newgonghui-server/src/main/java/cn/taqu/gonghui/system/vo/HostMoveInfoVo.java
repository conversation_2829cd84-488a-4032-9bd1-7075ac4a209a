package cn.taqu.gonghui.system.vo;

import lombok.Data;

/**
 * @Description 艺人转会记录
 * <AUTHOR>
 * @Date 2022/5/16 7:19 下午
 **/
@Data
public class HostMoveInfoVo {
    /**
     * uuid
     */
    private String uuid;
    /**
     * 1-直播，2-趣聊，3-聊天室
     */
    private Integer teamType;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 昵称
     */
    private String accountName;
    /**
     * 他趣id
     */
    private String accountId;
    /**
     * 移除的机构名称
     */
    private String oldOrgName;
    /**
     * 移除的团队名称
     */
    private String oldTeamName;
    /**
     * 移入的机构名称
     */
    private String newOrgName;
    /**
     * 移入的团队名称
     */
    private String newTeamName;
    /**
     * 移动时间
     */
    private Long createTime;
    /**
     * 移动理由
     */
    private String reason;
    /**
     * 提交凭证
     */
    private String files;
    /**
     * 操作人
     */
    private String operator;
}
