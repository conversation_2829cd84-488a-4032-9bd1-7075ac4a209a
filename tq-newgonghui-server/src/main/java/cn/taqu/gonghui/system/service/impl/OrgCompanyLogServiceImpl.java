package cn.taqu.gonghui.system.service.impl;

import cn.taqu.gonghui.system.entity.OrgBankLog;
import cn.taqu.gonghui.system.entity.OrgCompanyLog;
import cn.taqu.gonghui.system.mapper.OrgBankLogMapper;
import cn.taqu.gonghui.system.mapper.OrgCompanyLogMapper;
import cn.taqu.gonghui.system.service.IOrgBankLogService;
import cn.taqu.gonghui.system.service.IOrgCompanyLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/4 10 50
 * 机构公司修改日志服务实现
 */
@Service
public class OrgCompanyLogServiceImpl extends ServiceImpl<OrgCompanyLogMapper, OrgCompanyLog> implements IOrgCompanyLogService {

    @Override
    public OrgCompanyLog getLastByRelevanceId(Long relevanceId) {
        LambdaQueryWrapper<OrgCompanyLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrgCompanyLog::getRelevanceId, relevanceId).orderByDesc(OrgCompanyLog::getId).last(" limit 1");
        List<OrgCompanyLog> orgCompanyLogs = this.baseMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(orgCompanyLogs)){
            return null;
        }
        return orgCompanyLogs.get(0);
    }
}
