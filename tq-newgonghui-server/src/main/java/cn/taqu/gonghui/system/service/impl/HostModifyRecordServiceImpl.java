package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.gonghui.common.constant.HostOperateTypeEnum;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.soa.InfoService;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.entity.HostModifyRecord;
import cn.taqu.gonghui.system.mapper.HostModifyRecordMapper;
import cn.taqu.gonghui.system.mapper.TeamMapper;
import cn.taqu.gonghui.system.service.HostModifyRecordService;
import cn.taqu.gonghui.system.vo.HostMoveInfoVo;
import cn.taqu.gonghui.system.vo.TeamOrgInfoVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.Host;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-10 11:54
 */
@Slf4j
@Service
public class HostModifyRecordServiceImpl extends ServiceImpl<HostModifyRecordMapper, HostModifyRecord> implements HostModifyRecordService {

    @Autowired
    private HostModifyRecordMapper hostModifyRecordMapper;
    @SoaReference("account")
    private InfoService infoService;
    @Autowired
    private TeamMapper teamMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRecord(String hostUuid, Integer teamType, String batchId, HostOperateTypeEnum operateTypeEnum, ModifyRecordInfoDTO info, String reason, Integer status, String failMsg, String operator) {
        if (StringUtils.isBlank(operator)) {
            operator = SoaBaseParams.fromThread().getToken();
        }
        if (StringUtils.isBlank(batchId)) {
            batchId = System.currentTimeMillis() + "";
        }
        HostModifyRecord record = new HostModifyRecord();
        record.setHostUuid(hostUuid);
        record.setTeamType(teamType);
        record.setBatchId(batchId);
        record.setOperateType(operateTypeEnum.ordinal());
        record.setInfo(info == null ? "" : JsonUtils.objectToString2(info));
        record.setReason(reason);
        record.setStatus(status);
        record.setOperator(operator);
        record.setCreateTime(DateUtil.currentTimeSeconds());
        record.setUpdateTime(DateUtil.currentTimeSeconds());
        hostModifyRecordMapper.insert(record);
    }

    @Override
    public List<String> getUuidByBatchId(String batchId, Integer status) {
        return hostModifyRecordMapper.findUuidByBatchId(batchId, status);
    }

    @Override
    public List<HostMoveInfoVo> getHostMoveList(String uuid, Integer teamType, Integer pageNo, Integer pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        List<HostModifyRecord> recordList = hostModifyRecordMapper.findAllByHostAndCreateTime(uuid, null, 1, teamType);
        if (CollectionUtils.isEmpty(recordList)) {
            return new ArrayList<>();
        }

        Set<Long> teamIds = new HashSet<>();
        Set<String> hostUuids = new HashSet<>();
        for (HostModifyRecord record : recordList) {
            String reason = record.getReason();
            hostUuids.add(record.getHostUuid());
            if (StringUtils.isNotEmpty(reason)) {
                ModifyRecordInfoDTO infoDTO = JsonUtils.stringToObject(reason, ModifyRecordInfoDTO.class);
                if (infoDTO != null) {
                    if (infoDTO.getOldTeamId() != null) {
                        teamIds.add(infoDTO.getOldTeamId());
                    }
                    if (infoDTO.getNewTeamId() != null) {
                        teamIds.add(infoDTO.getNewTeamId());
                    }
                }
            }
        }
        Map<Long, TeamOrgInfoVO> teamMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(teamIds)) {
            List<TeamOrgInfoVO> teamInfos = teamMapper.selectTeamOrgInfoByTeamIds(new ArrayList<>(teamIds));
            teamMap.putAll(teamInfos.stream().collect(Collectors.toMap(TeamOrgInfoVO::getTeamId, v -> v, (m1, m2) -> m2)));
        }

        // 获取用户信息
        String[] fields = {"account_card_id", "account_name", "avatar"};
        Map<String, Map<String, Object>> infoMap = infoService.getInfoByUuid(hostUuids.toArray(new String[]{}), fields, null, false, false);

        // 拼接结果
        return recordList.stream()
                .map(record -> {
                    HostMoveInfoVo vo = new HostMoveInfoVo();
                    if (infoMap != null) {
                        Map<String, Object> userInfo = infoMap.get(record.getHostUuid());
                        if (MapUtils.isNotEmpty(userInfo)) {
                            vo.setAccountId(MapUtils.getString(userInfo, "account_card_id"));
                            vo.setAccountName(MapUtils.getString(userInfo, "account_name"));
                            vo.setAvatar(MapUtils.getString(userInfo, "avatar"));
                        }
                    }
                    vo.setUuid(record.getHostUuid());
                    vo.setTeamType(record.getTeamType());
                    vo.setCreateTime(record.getCreateTime());
                    vo.setOperator(record.getOperator());
                    String reason = record.getReason();
                    if (StringUtils.isNotEmpty(reason)) {
                        ModifyRecordInfoDTO infoDTO = JsonUtils.stringToObject(reason, ModifyRecordInfoDTO.class);
                        if (infoDTO != null) {
                            vo.setReason(infoDTO.getReason() == null ? "" : infoDTO.getReason());
                            vo.setFiles(infoDTO.getFiles() == null ? "" : infoDTO.getFiles());
                            if (infoDTO.getOldTeamId() != null) {
                                TeamOrgInfoVO oldTeam = teamMap.get(infoDTO.getOldTeamId());
                                if (oldTeam != null) {
                                    vo.setOldOrgName(oldTeam.getOrgName());
                                    vo.setOldTeamName(oldTeam.getTeamName());
                                }
                            }
                            if (infoDTO.getNewTeamId() != null) {
                                TeamOrgInfoVO newTeam = teamMap.get(infoDTO.getNewTeamId());
                                if (newTeam != null) {
                                    vo.setNewOrgName(newTeam.getOrgName());
                                    vo.setNewTeamName(newTeam.getTeamName());
                                }
                            }
                        }
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public Long countHostMoveList(String uuid, Integer teamType) {
        return hostModifyRecordMapper.countByHostUuidAndCreateTime(uuid, teamType, 1);
    }

    /**
     * 获取更新记录信息
     * @param hostUuid
     * @param typeEnum
     * @return
     */
    @Override
    public ModifyRecordInfoDTO getModifyRecordInfo(String hostUuid, HostOperateTypeEnum typeEnum) {
        HostModifyRecord condition = new HostModifyRecord();
        condition.setHostUuid(hostUuid);
        condition.setOperateType(typeEnum.ordinal());
        HostModifyRecord hostModifyRecord = hostModifyRecordMapper.selectByOne(condition);
        ModifyRecordInfoDTO dto = new ModifyRecordInfoDTO();
        if (Objects.nonNull(hostModifyRecord) && Objects.nonNull(hostModifyRecord.getInfo())) {
            dto = JsonUtils.stringToObject(hostModifyRecord.getInfo(), ModifyRecordInfoDTO.class);
        }

        return dto;
    }


}
