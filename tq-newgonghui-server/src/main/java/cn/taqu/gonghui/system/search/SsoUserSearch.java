package cn.taqu.gonghui.system.search;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class SsoUserSearch {
    private String loginname;
    private String name;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;
    private Long organizationId;//部门id
    private List<Long> organizationIds;//部门ids
    private Integer status;// 状态(0:停用;1:启用)
    private Integer isDeparture;// 在职状态(0:离职;1:在职)
}
