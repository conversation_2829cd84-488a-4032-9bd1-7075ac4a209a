package cn.taqu.gonghui.system.vo;

import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/16 2:57 下午
 **/
@Data
public class TeamOperateResponseVo {

    /**
     * 执行批次号，根据这个去获取操作的艺人列表
     */
    private String batchId;
    /**
     * 迁移时间，10位秒级时间戳
     */
    private Long createTime;
    /**
     * 迁出机构名称
     */
    private String oldOrgName;
    /**
     * 迁出团队名称
     */
    private String oldTeamName;
    /**
     * 迁入机构名称
     */
    private String newOrgName;
    /**
     * 迁入团队名称
     */
    private String newTeamName;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 成功人数
     */
    private Integer successNum;
    /**
     * 失败人数
     */
    private Integer failNum;
}
