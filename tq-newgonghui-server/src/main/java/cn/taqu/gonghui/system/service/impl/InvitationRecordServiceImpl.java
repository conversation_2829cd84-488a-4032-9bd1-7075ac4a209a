package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.domain.CommonPage;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.soa.GonghuiService;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.service.*;
import cn.taqu.gonghui.system.vo.RoleVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/5/10
 */
@Service
public class InvitationRecordServiceImpl  implements InvitationRecordService {

    @SoaReference("liveV1")
    private GonghuiService gonghuiService;
    @Autowired
    private HostService hostService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private TeamEmployeeService teamEmployeeService;

    @Override
    public CommonPage<Map<String,Object>> findByPageForAdmin(String orgUuid,int pageNo, int pageSize) {

        List<Long> teamIdList = new ArrayList<>();
        if(StringUtils.isNotEmpty(orgUuid)){
            Organization organization = organizationMapper.getByUuid(orgUuid);

            List<Team> teams = teamService.selectTeamList(organization.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            teamIdList = teams.stream().map(Team::getTeamId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(teamIdList)){
                return new CommonPage<Map<String, Object>>(0);
            }
        }
        CommonPage<Map<String, Object>> inviteList = gonghuiService.getInviteList(pageNo, pageSize, null, teamIdList, null);
         if(inviteList == null){
            return new CommonPage<Map<String, Object>>(0);
        }
        List<Map<String, Object>> list = inviteList.getList();
       if(CollectionUtils.isEmpty(list)){
           return new CommonPage<Map<String, Object>>(0);
       }
        List<String> accountUuids = list.stream().map(item -> MapUtils.getString(item, "oper_uuid")).collect(Collectors.toList());
        List<SysUser> userListByUuIds = sysUserService.getUserListByAccountUuids(accountUuids);
        List<String> hostUuidList = list.stream().map(item -> MapUtils.getString(item, "host_uuid")).collect(Collectors.toList());
        String[] fileds = new String[]{"account_name","avatar"};
        Map<String, Map<String, Object>> infoByUuid = hostService.getInfoByUuids(hostUuidList.stream().toArray(String[]::new), fileds);
        if(CollectionUtils.isNotEmpty(list)){
            list.stream().forEach(item->{
                String hostUuid = MapUtils.getString(item, "host_uuid");
                Map<String, Object> map = infoByUuid.get(hostUuid);
                item.put("account_name", MapUtils.getString(map, "account_name")==null?"": MapUtils.getString(map, "account_name"));
                item.put("avatar", MapUtils.getString(map, "avatar"));
                String operUuid = MapUtils.getString(item, "oper_uuid");
                SysUser sysUser = userListByUuIds.stream().filter(obj -> obj.getAccountUuid().equals(operUuid)).findFirst().orElse(null);
                if(sysUser!=null){
                    item.put("oper_name",sysUser.getUserName());
                    item.put("org_name",sysUser.getOrgName());
                }else{
                    item.put("oper_name","");
                    item.put("org_name","");
                }
            });
        }
        return inviteList;
    }


    @Override
    public CommonPage<Map<String,Object>> findByPageForUser(int pageNo, int pageSize) {

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        List<Long> teamIdList = new ArrayList<>();
        List<String> accountUuidList = new ArrayList<>();
        RoleVo roleVo = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        if(UserTypeEnum.AGENTER.getCode().equals(roleVo.getRoleKey())){
            accountUuidList.add(user.getAccountUuid());
        }else if(UserTypeEnum.LEADER.getCode().equals(roleVo.getRoleKey())){
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(),TeamTypeEnum.LIVE_TEAM.getValue());
            teamIdList.add(teamEmployee.getTeamId());
        }else if(UserTypeEnum.MANAGER.getCode().equals(roleVo.getRoleKey())){
            List<Team> teams = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            teamIdList = teams.stream().map(Team::getTeamId).collect(Collectors.toList());
        }
        CommonPage<Map<String, Object>> inviteList = gonghuiService.getInviteList(pageNo, pageSize, null, teamIdList, accountUuidList);
        if(inviteList == null){
            return new CommonPage<Map<String, Object>>(0);
        }
        List<Map<String, Object>> list = inviteList.getList();
        if(CollectionUtils.isEmpty(list)){
            return new CommonPage<Map<String, Object>>(0);
        }
        List<String> accountUuids = list.stream().map(item -> MapUtils.getString(item, "oper_uuid")).collect(Collectors.toList());
        List<SysUser> userListByUuIds = sysUserService.getUserListByAccountUuids(accountUuids);
        List<String> hostUuidList = list.stream().map(item -> MapUtils.getString(item, "host_uuid")).collect(Collectors.toList());
        String[] fileds = new String[]{"account_name","avatar"};
        Map<String, Map<String, Object>> infoByUuid = hostService.getInfoByUuids(hostUuidList.stream().toArray(String[]::new), fileds);
        if(CollectionUtils.isNotEmpty(list)){
            list.stream().forEach(item->{
                String hostUuid = MapUtils.getString(item, "host_uuid");
                Map<String, Object> map = infoByUuid.get(hostUuid);
                item.put("account_name", MapUtils.getString(map, "account_name")==null?"": MapUtils.getString(map, "account_name"));
                item.put("avatar", MapUtils.getString(map, "avatar"));
                String operUuid = MapUtils.getString(item, "oper_uuid");
                SysUser sysUser = userListByUuIds.stream().filter(obj -> obj.getAccountUuid().equals(operUuid)).findFirst().orElse(null);
                if(sysUser!=null){
                    item.put("oper_name",sysUser.getUserName());
                    item.put("org_name",sysUser.getOrgName());
                }else{
                    item.put("oper_name","");
                    item.put("org_name","");
                }
            });
        }
        return inviteList;
    }

}
