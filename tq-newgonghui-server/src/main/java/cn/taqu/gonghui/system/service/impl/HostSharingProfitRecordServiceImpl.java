package cn.taqu.gonghui.system.service.impl;

import afu.org.checkerframework.checker.oigj.qual.O;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.RedisUtil;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.req.ResetHostReq;
import cn.taqu.gonghui.common.vo.res.LiveGrayUuidRes;
import cn.taqu.gonghui.live.service.LiveSoaService;
import cn.taqu.gonghui.live.util.LiveDateUtils;
import cn.taqu.gonghui.system.dto.HostChangeInfoDTO;
import cn.taqu.gonghui.system.dto.HostLastProfitDTO;
import cn.taqu.gonghui.system.dto.HostSharingProfitDto;
import cn.taqu.gonghui.system.dto.SharingProfitRecordDto;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import cn.taqu.gonghui.system.search.SharingProfitRecordSearch;
import cn.taqu.gonghui.system.service.HostService;
import cn.taqu.gonghui.system.service.HostSharingProfitRecordService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.vo.HostSharingProfitRecordVo;
import cn.taqu.gonghui.system.vo.RoleVo;
import cn.taqu.gonghui.system.vo.SharingResultVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
public class HostSharingProfitRecordServiceImpl implements HostSharingProfitRecordService {

    private Logger logger = LoggerFactory.getLogger(LiveRecommendApplyServiceImpl.class);

    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    HostSharingProfitRecordMapper sharingProfitRecordMapper;
    @Value("${sharing.count}")
    private String profitCount;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysUserService userService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private HostService hostService;
    @Autowired
    private TeamHostOperateLogMapper operateLogMapper;
    @Autowired
    private TeamMapper teamMapper;
    @Value("${sharing.expireTime}")
    private String expireTime;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private LiveSoaService liveSoaService;

    @Autowired
    private OrganizationMapper organizationMapper;

    /**
     * 修改主播分润比例
     * 增加主播确认流程
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeSharingProfitByHost(HostSharingProfitDto dto) {
        if (StringUtils.isBlank(dto.getSharingProfitRate())) {
            throw new ServiceException("param_error","请选择分润比例");
        }
        if (Integer.parseInt(dto.getSharingProfitRate()) > 45) {
            throw new ServiceException("param_error","根据平台政策，分成比不得高于45%");
        }
        if (StringUtils.isBlank(dto.getHostUuid())) {
            throw new ServiceException("param_error","请选择需要操作的主播");
        }
        if (StringUtils.isBlank(dto.getAccountUuid())) {
            throw new ServiceException("param_error","操作人不能为空");
        }
        // 获取主播信息
        TeamHost teamHost = teamHostMapper.getOneByHostUuid(dto.getHostUuid(),TeamTypeEnum.LIVE_TEAM.getValue());
        if (null == teamHost) {
            throw new ServiceException("invalid_host","当前主播数据还未迁移");
        }
        if(TeamTypeEnum.TALK_TEAM.getValue() == teamHost.getTeamType()){
            throw new ServiceException("invalid_host","聊主不能操作");
        }
        // 当用户端发起时没有携带hostType 保持原有teamhost即可
        if (Objects.isNull(dto.getHostType())) {
            dto.setHostType(teamHost.getHostType());
        }

        // 如果是用户端操作，则需要校验操作权限
        if (SharingProfitOperateTypeEnum.CLIENT.getValue() == dto.getType()){
            // 获取当前用户权限
            RoleVo role = userService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
            // 管理员
            if (UserTypeEnum.MANAGER.getCode().equals(role.getRoleKey())) {
                if (!getOrgIdByAccountUuid().equals(teamHost.getOrgId())) {
                    logger.warn("当前操作人没有权限操作该主播，操作人uuid:{},主播uuid:{}",dto.getAccountUuid(),teamHost.getHostUuid());
                    throw new ServiceException("no_permission","您没有权限操作改主播");
                }
            }
            // 负责人
            else if (UserTypeEnum.LEADER.getCode().equals(role.getRoleKey())) {
                if (!teamIdByAccountUuid(TeamTypeEnum.LIVE_TEAM.getValue()).equals(teamHost.getTeamId())) {
                    logger.warn("当前操作人没有权限操作该主播，操作人uuid:{},主播uuid:{}",dto.getAccountUuid(),teamHost.getHostUuid());
                    throw new ServiceException("no_permission","您没有权限操作改主播");
                }
            }
            // 经纪人
            else {
                if (null == teamHost.getEmployeeId() || 0 == teamHost.getEmployeeId() || !sysUserMapper.getEmployeeIdByAccountUuid(dto.getAccountUuid(),TeamTypeEnum.LIVE_TEAM.getValue()).equals(teamHost.getEmployeeId())) {
                    logger.warn("当前操作人没有权限操作该主播，操作人uuid:{},主播uuid:{}",dto.getAccountUuid(),teamHost.getHostUuid());
                    throw new ServiceException("no_permission","您没有权限操作改主播");
                }
            }
        }

        // 校验当前主播是否有待确认的修改分润比例记录，如果有则不允许调整
        SharingProfitRecordSearch search = new SharingProfitRecordSearch();
        search.setHostUuid(dto.getHostUuid());
        search.setStatus(HostSharingProfitStatusEnum.PENDING.getValue());
        search.setOrgId(teamHost.getOrgId());
        HostSharingProfitRecord resultRecord = sharingProfitRecordMapper.getOneRecordBySearch(search);
        if (null != resultRecord) {
            logger.warn("当前主播本月存在待确认分润调整，请先行处理,主播uuid:{}",dto.getHostUuid());
            throw new ServiceException("invalid_operate","当前主播存在待确认分润调整，请先行处理");
        }

        // 校验当前主播用户端调整分润次数是否超过限制（待生效的），超过限制不允许修改（只限制用户端操作类型，管理端不用限制）
        if (SharingProfitOperateTypeEnum.CLIENT.getValue() == dto.getType()) {
            search.setStatus(HostSharingProfitStatusEnum.TO_BE_EFFECTIVE.getValue());
            search.setType(SharingProfitOperateTypeEnum.CLIENT.getValue());
            search.setStartTime(LiveDateUtils.getFirstTimeOfMonth());
            search.setEndTime(System.currentTimeMillis()/1000);
            Integer count = sharingProfitRecordMapper.countBySearch(search);
            if (null != count && count >= Integer.valueOf(profitCount)) {
                throw new ServiceException("invalid_operate","当前主播本月分润调整次数已经超过限制，请下个月再进行调整");
            }
        }

        // 所有校验都通过，就往分润记录表插入记录
        HostSharingProfitRecord record = new HostSharingProfitRecord();
        record.setHostUuid(teamHost.getHostUuid());
        record.setAccountUuid(dto.getAccountUuid());
        record.setType(dto.getType());
        record.setOrgId(teamHost.getOrgId());
        record.setTeamId(teamHost.getTeamId());
        record.setStatus(HostSharingProfitStatusEnum.PENDING.getValue());
        record.setCurrentSharingProfitRate(teamHost.getCurrentSharingProfitRate());
        record.setNewSharingProfitRate(dto.getSharingProfitRate());
        record.setChangeTime(System.currentTimeMillis()/1000);
        record.setEffectiveTime(0L);
        record.setCreateTime(System.currentTimeMillis()/1000);
        record.setUpdateTime(System.currentTimeMillis()/1000);
        record.setTmpHostType(dto.getHostType());
        sharingProfitRecordMapper.insertSelective(record);

        // 调用php接口  把当前记录相关信息传递
        try {
            Team team = teamMapper.selectByPrimaryKey(record.getTeamId());
            if (null != team) {
                Organization organization = organizationMapper.selectByPrimaryKey(team.getOrgId());
                String orgName = Optional.ofNullable(organization.getOrgName()).orElse("");
                hostService.sendSpiltRatioNotice(record.getId(),record.getHostUuid(),orgName,record.getChangeTime());
                logger.info("当前主播调整了分润比例触发主播确认通知，主播uuid：{}",record.getHostUuid());
            } else {
                hostService.sendSpiltRatioNotice(record.getId(),record.getHostUuid(),"",record.getChangeTime());
                logger.warn("团队不存在，主播uuid:{}", record.getHostUuid());
                logger.info("当前主播调整了分润比例触发主播确认通知，主播uuid：{}",record.getHostUuid());
            }
        } catch (Exception e){
            logger.error("调整主播分润比例触发通知失败，主播uuid:{},失败原因：{}",record.getHostUuid(),e);
        }

    }

    /**
     * 获取主播是否是灰度期间 首次调整
     * @param hostUuid
     * @return
     */
    @Override
    public Integer getHostGrayFirst(String hostUuid) {
        // 此处要获取直播提供接口
        LiveGrayUuidRes uuidRes = liveSoaService.getSettltmentTestUuidInfo();
        List<String> grayHostList = uuidRes.getNormal_consortia_test_uuids();

        // 如果不在灰度则按原流程
        if (!grayHostList.contains(hostUuid)) {
            return Constants.NO_0;
        }

        // 判断是否当月已申请过 如果存在key 则走次月生效流程
        String firstKey = RedisKeyConstant.MONTH_FIRST_APPLY.setArg(hostUuid);
        if (StringUtils.isNotEmpty(redisUtil.get(firstKey))) {
            return Constants.NO_0;
        }

        return Constants.YES_1;
    }

    /**
     * 主播分润比例确认回调
     * @param dto
     */
    @Override
    public Map<String,String> hostConfirmSharingRate(SharingProfitRecordDto dto) {
        logger.info("主播确认回调，回调信息：{}",JsonUtils.objectToString(dto));
        if (null == dto.getId()) {
            throw new ServiceException("change_sharing_rate_invalid_id_param","分润记录id不能为空");
        }
        if (null == dto.getStatus() || dto.getStatus() < 1 || dto.getStatus() > 4) {
            throw new ServiceException("change_sharing_rate_invalid_status_params","状态值无效");
        }
        if (StringUtils.isBlank(dto.getHostUuid())) {
            throw new ServiceException("change_sharing_rate_invalid_uuid_params","主播uuid不能为空");
        }

        // 获取当前主播信息
        TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(dto.getHostUuid(),TeamTypeEnum.LIVE_TEAM.getValue());
        if (null == oneByHostUuid) {
            logger.warn("主播确认分润比例回调主播不存在，主播uuid:{},调整记录id:{}",dto.getHostUuid(),dto.getId());
            throw new ServiceException("change_sharing_rate_invalid_record","当前分润操作记录无效，无法操作");
        }

        // 校验当前记录是否存在
        SharingProfitRecordSearch search = new SharingProfitRecordSearch();
        search.setId(dto.getId());
        search.setStatus(HostSharingProfitStatusEnum.PENDING.getValue());
        search.setHostUuid(dto.getHostUuid());
        search.setOrgId(oneByHostUuid.getOrgId());
        HostSharingProfitRecord record = sharingProfitRecordMapper.getOneRecordBySearch(search);
        if (null == record) {
            logger.warn("当前分润操作记录无效,回调记录id：{}",dto.getId());
            throw new ServiceException("change_sharing_rate_invalid_record","当前分润操作记录无效或者当前主播已经转会，无法操作同意");
        }

        Map<String,String> resultMap = new HashMap<>();

        Long currentTime = System.currentTimeMillis()/1000;
        record.setUpdateTime(currentTime);
        // 主播是否点的同意
        if (HostSharingProfitStatusEnum.TO_BE_EFFECTIVE.getValue() == dto.getStatus()) {
            // 校验当前记录是否已经超过72小时，如果过了有效期则不允许操作且状态更新为拒绝
            if ((currentTime - record.getChangeTime()) > Long.valueOf(expireTime) * 60 * 60) {
                record.setStatus(HostSharingProfitStatusEnum.REJECT.getValue());
                record.setEffectiveTime(0L);
                sharingProfitRecordMapper.updateInfo(record);
                logger.warn("当前分润已经超过有效期，无法操作同意，默认归属拒绝,分润修改记录信息：{}",JsonUtils.objectToString(record));
                throw new ServiceException("change_sharing_rate_operate_expire","当前分润已经超过有效期，无法操作同意，默认归属拒绝");
            }
            // 没过有效期则继续走同意的流程
            else {
                // 更新分润记录
                // 同意后立即生效 @link https://o15vj1m4ie.feishu.cn/wiki/wikcnHQhrpTyCqsLIHInIsR8lSZ
                Long nowTime = System.currentTimeMillis() / 1000;
                Integer isGrayFirst = getHostGrayFirst(dto.getHostUuid());
                if (isGrayFirst.equals(Constants.YES_1)) {
                    record.setStatus(HostSharingProfitStatusEnum.EFFECTIVE.getValue());
                    record.setUpdateTime(nowTime);
                    record.setEffectiveTime(nowTime);
                } else {
                    record.setStatus(dto.getStatus());
                    record.setEffectiveTime(LiveDateUtils.getFirstTimeOfNextMonth() + 30);
                }
                sharingProfitRecordMapper.updateInfo(record);
                // 将记录的调整后的分润比例设置到主播表的new_sharing_profit_rate,并将is_update设置为1供定时任务消费
                TeamHost teamHost = teamHostMapper.getOneByHostUuid(record.getHostUuid(),TeamTypeEnum.LIVE_TEAM.getValue());
                if (null == teamHost) {
                    logger.warn("当前主播为无效主播，记录信息：{}",JsonUtils.objectToString(record));
                    throw new ServiceException("invalid_host","当前主播无效");
                }
                String currentProfit = teamHost.getCurrentSharingProfitRate();
                if (isGrayFirst.equals(Constants.YES_1)) {
                    teamHost.setNewSharingProfitRate(record.getNewSharingProfitRate());
                    teamHost.setIsUpdate(SharingProfitRateIsUpdateEnum.NO.getValue());
                } else {
                    teamHost.setIsUpdate(SharingProfitRateIsUpdateEnum.YES.getValue());
                }
                teamHost.setChangeTime(nowTime);
                teamHost.setCurrentSharingProfitRate(record.getNewSharingProfitRate());
                teamHost.setHostType(record.getTmpHostType());
                // 如果是自提类型 需要把isGroup = 0
                if (record.getTmpHostType().equals(HostTypeEnum.SELF_LIFT.getCode())) {
                    teamHost.setIsGroup(Constants.NO_0);
                }
                teamHostMapper.updateProfitAndIsUpdateAndChangeTime(teamHost);

                // 如果是灰度用户首次申请 完成后需要标记 后续当月二次申请需要次月生效
                if (isGrayFirst.equals(Constants.YES_1)) {
                    String setFirstkey = RedisKeyConstant.MONTH_FIRST_APPLY.setArg(dto.getHostUuid());
                    redisUtil.set(setFirstkey, "1", 30L, TimeUnit.DAYS);
                }

                resultMap.put("newSharingProfitRate",record.getNewSharingProfitRate());

                // 记录调整日志
                try {
                    Map<String,String> map = new HashMap<>();
                    map.put("oldValue",currentProfit);
                    map.put("newValue",teamHost.getNewSharingProfitRate());
                    TeamHostOperateLog log = new TeamHostOperateLog();
                    log.setHostUuid(record.getHostUuid());
                    log.setType(TeamHostOperateLogEnum.CHANGE_SHARING_PROFIT_RATE.getValue());
                    log.setCreateTime(DateUtil.currentTimeSeconds());
                    log.setContent(JsonUtils.objectToString(map));
                    if (SharingProfitOperateTypeEnum.CLIENT.getValue() == record.getType()) {
                        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("account_uuid",record.getAccountUuid());
                        SysUser sysUser = sysUserMapper.selectOne(queryWrapper);
                        if (null != sysUser) {
                            log.setOperator(sysUser.getUserName());
                        }
                    } else {
                        log.setOperator(record.getAccountUuid());
                    }
                    operateLogMapper.insert(log);
                } catch (Exception e){
                    logger.error("调整分润比例日志记录失败，主播uuid：{},调整时间：{}，失败原因：{}",teamHost.getHostUuid(),System.currentTimeMillis()/1000,e);
                }
            }
        }
        // 表示点击的拒绝
        else {
            record.setStatus(dto.getStatus());
            record.setEffectiveTime(0L);
            sharingProfitRecordMapper.updateInfo(record);
        }

        logger.info("主播确认回调，回调信息：{},分润记录信息：{}",JsonUtils.objectToString(dto),JsonUtils.objectToString(record));

        return resultMap;

    }

    /**
     * 管理端分润比例记录列表
     * @param search
     * @return
     */
    @Override
    public List<HostSharingProfitRecordVo> manageList(SharingProfitRecordSearch search) {
        PageHelper.startPage(search.getPage() == null ? 1 : search.getPage(),search.getPageSize() == null ? 20 : search.getPageSize());
        List<HostSharingProfitRecordVo> voList = sharingProfitRecordMapper.manageList(search);
        processHostNickNameAndAvatar(voList);
        return voList;
    }

    /**
     * 用户端分润比例记录列表
     * @param search
     * @return
     */
    @Override
    public List<HostSharingProfitRecordVo> clientList(SharingProfitRecordSearch search) {

        List<HostSharingProfitRecordVo> voList = null;

        // 获取当前登录用户角色
        RoleVo currentRole = userService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());

        PageHelper.startPage(search.getPage() == null ? 1 : search.getPage(),search.getPageSize() == null ? 20 : search.getPageSize());
        // 设置查询orgId
        search.setOrgId(getOrgIdByAccountUuid());
        search.setType(SharingProfitOperateTypeEnum.CLIENT.getValue());
        // 机构管理员
        if (currentRole.getRoleKey().equals(UserTypeEnum.MANAGER.getCode())){
            voList = sharingProfitRecordMapper.clientList(search);
        }
        // 负责人
        else if (currentRole.getRoleKey().equals(UserTypeEnum.LEADER.getCode())) {
            search.setTeamId(teamIdByAccountUuid(TeamTypeEnum.LIVE_TEAM.getValue()));
            voList = sharingProfitRecordMapper.clientList(search);
        }
        // 经纪人
        else if (currentRole.getRoleKey().equals(UserTypeEnum.AGENTER.getCode())) {
            search.setTeamId(teamIdByAccountUuid(TeamTypeEnum.LIVE_TEAM.getValue()));
            search.setUuidList(getHostUuidsByAccountUuid(TeamTypeEnum.LIVE_TEAM.getValue()));
            voList = sharingProfitRecordMapper.clientList(search);
        }

        // 设置主播信息
        processHostNickNameAndAvatar(voList);
        return voList;
    }

    /**
     * 获取分润记录详情
     * @param id
     * @return
     */
    @Override
    public SharingResultVo detail(Long id,String hostUuid){
        if (null == id) {
            throw new ServiceException("query_sharing_rate_fail","记录id不能为空");
        }
        if (StringUtils.isBlank(hostUuid)) {
            throw new ServiceException("query_sharing_rate_fail","host_uuid不能为空");
        }
        SharingResultVo detail = sharingProfitRecordMapper.detail(id,hostUuid);
        if (SharingProfitOperateTypeEnum.MANAGER.getValue() == detail.getType()) {
            detail.setTeamName("直播官方");
        }
        detail.setChangeEndTime(detail.getChangeTime() + 72 * 60 * 60);

        /**
         * 判断是否灰度期间主播 如果是的话 首次立即生效 当月二次申请次月生效
         * 申请时携带标识给业务侧（直播） 是立即生效 or 次月生效
         * 同意回调时 根据条件执行
         */
        Integer isGrayFirst = getHostGrayFirst(hostUuid);
        detail.setIsGrayFirst(isGrayFirst);
        log.info("获取分润详情,{}", detail);

        // 是马上生效还是次月生效 类型 默认1次月生效
        int effectiveType = 1;
        if (isGrayFirst.equals(1)) {
            effectiveType = 2;
        }
        // 如果打上标识 并且是已生效
        String firstKey = RedisKeyConstant.MONTH_FIRST_APPLY.setArg(hostUuid);
        if (StringUtils.isNotEmpty(redisUtil.get(firstKey)) && detail.getStatus().equals(HostSharingProfitStatusEnum.EFFECTIVE.getValue())) {
            effectiveType = 2;
        }
        detail.setEffectiveType(effectiveType);

        return detail;
    }

    /**
     * 获取当前用户所属机构id
     * @return
     */
    private Long getOrgIdByAccountUuid(){
        Long orgId = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getOrgId();
        if (null ==  orgId) {
            throw new ServiceException(CodeStatus.ORG_NOT_FOUNT_ERROR.value(),"当前用户所属机构无效");
        }
        return orgId;
    }



    /**
     * 获取负责人所属teamId
     */
    public Long teamIdByAccountUuid(Integer businessType){
        if(businessType == null || businessType>3){
            throw new ServiceException("invalid_user","当前无此业务类型");
        }
        String accountUuid = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getAccountUuid();
        if (StringUtils.isBlank(accountUuid)) {
            throw new ServiceException("invalid_user","当前用户无效");
        }
        Long teamId = sysUserMapper.getTeamIdByAccountUuid(accountUuid,businessType);
        if (0 == teamId || null == teamId) {
            throw new ServiceException("invalid_user","当前登录人所属团队不存在");
        }
        return teamId;
    }

    /**
     * 获取当前用户下的有效主播uuid
     */
    private List<String> getHostUuidsByAccountUuid(Integer type){
        String accountUuid = tokenService.getLoginUser(ServletUtils.getRequest()).getUser().getAccountUuid();
        List<String> hostUuids = null;
        Long employeeId = sysUserMapper.getEmployeeIdByAccountUuid(accountUuid,type);
        if (null == employeeId) {
            logger.warn("当前经纪人是个无效成员");
            return  hostUuids;
        }
        hostUuids = teamHostMapper.getHostUuidsByAgenter(employeeId);
        return hostUuids;
    }


    private void processHostNickNameAndAvatar(List<HostSharingProfitRecordVo> voList){
        if (CollectionUtils.isNotEmpty(voList)) {
            List<String> uuidList = voList.stream().map(HostSharingProfitRecordVo::getHostUuid).collect(Collectors.toList());
            String[] uuids = uuidList.toArray(new String[uuidList.size()]);
            Map<String, Map<String, Object>> uuidMap = hostService.getInfoByUuids(uuids, new String[]{"account_name", "avatar","default_card_id"});
            if (MapUtils.isNotEmpty(uuidMap)) {
                for (HostSharingProfitRecordVo item : voList) {
                    Map<String, Object> map = uuidMap.get(item.getHostUuid());
                    if (MapUtils.isNotEmpty(map)) {
                        item.setAvatar(String.valueOf(map.get("avatar")));
                        item.setNickName(String.valueOf(map.get("account_name")));
                        item.setLiveNo(String.valueOf(map.get("default_card_id")));
                    }
                }
            }
        }
    }


    /**
     *
     * 每个小时执行一次，自动过期掉 当前月份已经过期的待确认分润调整记录
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoExpire(){

        logger.info("[autoExpire]自动过期失效的分润调整记录--开始执行了");
        Long currentTime = System.currentTimeMillis()/1000;

        SharingProfitRecordSearch search = new SharingProfitRecordSearch();
        search.setStartTime(LiveDateUtils.getFirstTimeOfMonth());
        search.setEndTime(currentTime);
        logger.info("获取到待过期记录参数：{}",JsonUtils.objectToString(search));
        List<HostSharingProfitRecord> recordList = sharingProfitRecordMapper.idListBySearch(search);
        logger.info("获取到待过期记录recordList：{}",JsonUtils.objectToString(recordList));
        if (CollectionUtils.isNotEmpty(recordList)) {
            // 过滤出来已经过期的记录
            List<HostSharingProfitRecord> expireList = recordList.stream()
                    .filter(record -> ((currentTime - record.getChangeTime()) >= Long.valueOf(expireTime) * 60 * 60)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(expireList)) {
                logger.info("获取到待过期记录expireList：{}",JsonUtils.objectToString(expireList));
                List<Long> collect = expireList.stream().map(HostSharingProfitRecord::getId).collect(Collectors.toList());
                sharingProfitRecordMapper.autoExpire(collect);
            }
        }

        logger.info("[autoExpire]自动过期失效的分润调整记录--执行结束了");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> resetProfit(ResetHostReq resetHostReq) {
        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("team_type", TeamTypeEnum.LIVE_TEAM.getValue());
        if (resetHostReq.getType().equals(ResetTypeEnum.LIVE_SOURCE.getCode())) {
            // 调用直播接口
            LiveGrayUuidRes uuidRes = liveSoaService.getSettltmentTestUuidInfo();
            if (Objects.isNull(uuidRes.getNormal_consortia_test_uuids())) {
                throw new ServiceException("empty_error", "直播接口无用户数据");
            }
            queryWrapper.in("host_uuid", uuidRes.getNormal_consortia_test_uuids());
        }
        if (resetHostReq.getType().equals(ResetTypeEnum.TEAM.getCode())) {
            queryWrapper.eq("team_id", resetHostReq.getTeamId());
        }
        if (resetHostReq.getType().equals(ResetTypeEnum.ORG.getCode())) {
            queryWrapper.eq("org_id", resetHostReq.getOrgId());
        }
        List<TeamHost> teamHostList = teamHostMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(teamHostList)) {
            logger.info("查无主播数据");
            return new ArrayList<>();
        }
        for (TeamHost teamHost : teamHostList) {
            runResetProfit(teamHost);
        }

        return teamHostList.stream().map(TeamHost::getHostUuid).collect(Collectors.toList());
    }

    /**
     * 执行重置分润
     * @param teamHost
     */
    @Override
    public void runResetProfit(TeamHost teamHost) {
        Long nowTime = System.currentTimeMillis() / 1000;
        // 判断是否重置过 如果重置过则不处理
        TeamHostOperateLog logRes = operateLogMapper.selectOneByUuidAndType(teamHost.getHostUuid(), TeamHostOperateLogEnum.LIVE_RESET_PROFIT.getValue()
                , teamHost.getTeamId());
        if (Objects.nonNull(logRes)) {
            logger.info("该主播已重置过分润,{}", teamHost.getHostUuid());
            return;
        }

        // 查询主播是否有待确认分润记录
        SharingProfitRecordSearch search = new SharingProfitRecordSearch();
        search.setHostUuid(teamHost.getHostUuid());
        search.setOrgId(teamHost.getOrgId());
        HostSharingProfitRecord record = sharingProfitRecordMapper.getOneRecordBySearch(search);
        if (Objects.nonNull(record)) {
            if (record.getStatus().equals(HostSharingProfitStatusEnum.PENDING.getValue())
                    || record.getStatus().equals(HostSharingProfitStatusEnum.TO_BE_EFFECTIVE.getValue())) {
                // 存在待处理分润 自动失效
                record.setStatus(HostSharingProfitStatusEnum.REJECT.getValue());
                record.setUpdateTime(nowTime);
                record.setEffectiveTime(nowTime);
                sharingProfitRecordMapper.updateInfo(record);
            }
        }

        // 插入分润记录为0 自动生效
        HostSharingProfitRecord profitRecord = new HostSharingProfitRecord();
        profitRecord.setHostUuid(teamHost.getHostUuid());
        profitRecord.setAccountUuid("admin");
        profitRecord.setType(2);
        profitRecord.setOrgId(teamHost.getOrgId());
        profitRecord.setTeamId(teamHost.getTeamId());
        profitRecord.setStatus(HostSharingProfitStatusEnum.EFFECTIVE.getValue());
        profitRecord.setCurrentSharingProfitRate(teamHost.getCurrentSharingProfitRate());
        profitRecord.setNewSharingProfitRate("0");
        profitRecord.setChangeTime(nowTime);
        profitRecord.setEffectiveTime(0L);
        profitRecord.setCreateTime(nowTime);
        profitRecord.setUpdateTime(nowTime);
        sharingProfitRecordMapper.insertSelective(profitRecord);

        // 改写team_host分润
        String tmpOldProfit = teamHost.getCurrentSharingProfitRate();
        teamHost.setNewSharingProfitRate("0");
        teamHost.setChangeTime(nowTime);
        teamHost.setIsUpdate(SharingProfitRateIsUpdateEnum.NO.getValue());
        teamHost.setCurrentSharingProfitRate("0");
        teamHostMapper.updateByPrimaryKey(teamHost);

        // 记录主播分润被重置
        TeamHostOperateLog log = new TeamHostOperateLog();
        log.setHostUuid(teamHost.getHostUuid());
        log.setTeamId(teamHost.getTeamId());
        log.setType(TeamHostOperateLogEnum.LIVE_RESET_PROFIT.getValue());
        log.setCreateTime(DateUtil.currentTimeSeconds());
        log.setContent("重置分润为0, 历史分润：" + tmpOldProfit);
        log.setOperator("admin");
        operateLogMapper.insert(log);
        logger.info("resetProfit重置分润成功,{}", teamHost.getHostUuid());
    }

    @Override
    public HostChangeInfoDTO getResetChangeInfo(String hostUuid) {
        HostChangeInfoDTO hostChangeInfoDTO = new HostChangeInfoDTO();
        TeamHost teamHost = teamHostMapper.selectHostOneByUuid(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
        // 查询是否存在重置记录
        TeamHostOperateLog log = operateLogMapper.selectOneByUuidAndType(hostUuid, TeamHostOperateLogEnum.LIVE_RESET_PROFIT.getValue(), teamHost.getTeamId());
        if (Objects.isNull(log)) {
            hostChangeInfoDTO.setIsResetAndChange(0);
            return hostChangeInfoDTO;
        }

        // 如果存在 则查询是否后在重置时间后运营调整分润记录
        SharingProfitRecordSearch search = new SharingProfitRecordSearch();
        search.setHostUuid(hostUuid);
        // 此处同一时间 条件+1s
        search.setStartTime(log.getCreateTime() + 1);
        search.setTeamId(teamHost.getTeamId());
        HostSharingProfitRecord record = sharingProfitRecordMapper.getOneRecordBySearch(search);
        if (Objects.nonNull(record)) {
            hostChangeInfoDTO.setIsResetAndChange(1);
            return hostChangeInfoDTO;
        } else {
            hostChangeInfoDTO.setIsResetAndChange(0);
            return hostChangeInfoDTO;
        }
    }

    @Override
    public HostLastProfitDTO getLastProfit(String hostUuid) {
        HostLastProfitDTO hostLastProfitDTO = new HostLastProfitDTO();
        // 查询最近已生效分润记录
        SharingProfitRecordSearch search = new SharingProfitRecordSearch();
        search.setHostUuid(hostUuid);
        search.setType(HostSharingProfitStatusEnum.EFFECTIVE.getValue());
        HostSharingProfitRecord record = sharingProfitRecordMapper.getOneRecordBySearch(search);
        if (Objects.nonNull(record)) {
            hostLastProfitDTO.setLastProfit(record.getCurrentSharingProfitRate());
            return hostLastProfitDTO;
        }
        // 如果没有则查询当前的
        TeamHost teamHost = teamHostMapper.getOneByHostUuid(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
        if (Objects.nonNull(teamHost)) {
            hostLastProfitDTO.setLastProfit(teamHost.getCurrentSharingProfitRate());
            return hostLastProfitDTO;
        } else {
            hostLastProfitDTO.setLastProfit("0");
            return hostLastProfitDTO;
        }
    }
}
