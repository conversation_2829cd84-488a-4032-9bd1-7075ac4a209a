package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.domain.CommonPage;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/5/10
 */
public interface InvitationRecordService {

    /**
     * 分页查询邀请记录
     * @param orgUuid
     * @param pageNo
     * @param pageSize
     * @return
     */
    CommonPage<Map<String,Object>> findByPageForAdmin(String orgUuid, int pageNo, int pageSize);


    CommonPage<Map<String,Object>> findByPageForUser(int pageNo, int pageSize);


}
