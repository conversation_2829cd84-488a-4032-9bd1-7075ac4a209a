package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.system.dto.SysRoleDto;
import cn.taqu.gonghui.system.entity.SysRole;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
public interface SysRoleService extends IService<SysRole> {


    List<SysRole> selectRoleList(SysRole sysRole);


    IPage<SysRole> selectRoleList(int pageNo, int pageSize);


    void addRole(SysRoleDto sysRoleDto);


    void editRole(SysRoleDto sysRoleDto);


    void editRoleMenu(Long roleId,Long[] menuIdArr);

    /**
     * 根据角色id查询角色详情
     */
    SysRole selectRoleByRoleId(Long roleId);

    /**
     * 判断当前业务类型下角色名称是否已经存在
     */
    Boolean roleNameIsExisted(SysRoleDto sysRoleDto);

    /**
     * 判断当前业务下角色权限标识符是否已经存在
     */
    Boolean roleKeyIsExisted(SysRoleDto sysRoleDto);

   List<CommonSelect> getLeaderAndAgent(Integer type);

   void deleteRole(Long roleId);

    List<CommonSelect> getRoleSelect();
}
