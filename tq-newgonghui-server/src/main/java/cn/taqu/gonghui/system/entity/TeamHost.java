package cn.taqu.gonghui.system.entity;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * <AUTHOR>
 * @Date 2021/5/11
 */
@Data
@TableName(value = "team_host", autoResultMap = true)
public class TeamHost {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 主播uuid
     */
    private String hostUuid;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 团队类型
     */
    private Integer teamType;

    /**
     * 所属经纪人
     */
    private Long employeeId;

    /**
     * 邀请时间
     */
    private Long inviteTime;

    /**
     * 真实姓名
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String realName;

    /**
     * 真实姓名--密文
     */
    @TableField(typeHandler = EncryptTypeHandler.class)
    private String realNameCipher;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 最后更新时间
     */
    private Long updateTime;

    /**
     * 调整分润时间
     */
    private Long changeTime;

    /**
     * 主播当前分润比例
     */
    private String currentSharingProfitRate;

    /**
     * 主播调整后的分润比例
     */
    private String newSharingProfitRate;

    /**
     * 主播是否调整了分润比例（1-是，0-否）
     */
    private Integer isUpdate;

    /**
     * 是否团播 0否 1是
     */
    private Integer isGroup;

    /**
     * 主播类型 1自营 2自提
     */
    private Integer hostType;

    @TableField(exist = false)
    private String amount; //总收益

    @TableField(exist = false)
    private String liveNum; //开播人数

    @TableField(exist = false)
    private String noLiveNum; //未开播人数

    @TableField(exist = false)
    private String totalLiveTime; //总时长

    @TableField(exist = false)
    private String lost; //流失主播

    @TableField(exist = false)
    private String orgUuid;//机构uuid

    @TableField(exist = false)
    private String[] businessmanUuids; //经纪人uuid数组

    @TableField(exist = false)
    private String liveStatus;

    @TableField(exist = false)
    private String applyLevel;

    @TableField(exist = false)
    private String liveNo;   //房间号

    @TableField(exist = false)
    private Integer page;

    @TableField(exist = false)
    private Integer pageSize;

    @TableField(exist = false)
    private String hostStatus;

    @TableField(exist = false)
    private String startTime;

    @TableField(exist = false)
    private String endTime;


    public String getRealName() {
        if (EncryptSwitchConfig.readFrom.equals(2)) {
            return this.realNameCipher;
        }
        if (StringUtils.isBlank(this.realName) && StringUtils.isNotBlank(this.realNameCipher)) {
            return this.realNameCipher;
        }
        return this.realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
        this.realNameCipher = realName;
    }
}
