package cn.taqu.gonghui.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 机构协议管理
 * <AUTHOR>
 */
@Data
@TableName(value = "organization_agreement_info")
public class AgreementInfo {
    /**
     * 协议id
     */
    @TableId(value = "agr_id",type = IdType.AUTO)
    private Long agrId;

    /**
     * 协议顺序
     */
    private Integer orderLevel;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 所属业务类型（1-直播，2-趣聊，3-聊天室）
     */
    private Integer type;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 标题
     */
    private String title;

    /**
     * 状态（1-开启，0-关闭）
     */
    private Integer valid;

    /**
     * 协议内容
     */
    private String content;
}
