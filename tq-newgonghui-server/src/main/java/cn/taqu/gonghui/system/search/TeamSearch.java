package cn.taqu.gonghui.system.search;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
@Data
public class TeamSearch {

    /**
     * 所属机构id
     */
    private Integer orgId;

    /**
     * 业务类型（1-直播，2-趣聊，3-聊天室）
     */
    private Integer type;

    /**
     * 团队标识符
     */
    private String signKey;

    /**
     * 是否启用（1-启用，0-禁用）
     */
    private Integer status;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 负责人手机号
     */
    private String chargeMobile;

    private Integer pageNum;

    private Integer pageSize;

    private Integer teamDefault;
}
