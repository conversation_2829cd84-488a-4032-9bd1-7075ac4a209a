package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.OperatorLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public interface OperatorLogMapper extends BaseMapper<OperatorLog> {
//    int deleteByPrimaryKey(Long id);

    int insert(OperatorLog record);

//    int insertSelective(OperatorLog record);

    OperatorLog selectByPrimaryKey(Long id);

//    int updateByPrimaryKeySelective(OperatorLog record);

//    int updateByPrimaryKeyWithBLOBs(OperatorLog record);

//    int updateByPrimaryKey(OperatorLog record);
}