package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.entity.Notice;
import cn.taqu.gonghui.system.search.NoticeSearch;

import java.util.List;

public interface NoticeService {

    List<Notice> getNoticeByNoticeType(Integer noticeType);
    
    List<Notice> pagelist(NoticeSearch search);

    Notice detail(Long id);

    void add(Notice record);

    void modify(Notice record);

    void remove(Long id);

    Notice detailBytitle(String title);


}
