package cn.taqu.gonghui.system.common;

import cn.taqu.gonghui.common.client.EncryptDecryptClient;
import cn.taqu.gonghui.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.TypeHandler;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2024/7/2 09 43
 * discription
 */
@Slf4j
@Service
@MappedJdbcTypes(JdbcType.VARCHAR)
public class EncryptTypeHandler extends BaseTypeHandler<String> {

    @Resource
    private EncryptDecryptClient encryptDecryptClient;
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {

        if(StringUtils.isBlank(parameter)){
            ps.setString(i, "");
            return;
        }
        String encryptedContent = encryptDecryptClient.encrypt(parameter);
        ps.setString(i, encryptedContent);
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {

        String encryptedContent = rs.getString(columnName);
        if(StringUtils.isBlank(encryptedContent)){
            return "";
        }
        String clearTextContent = encryptDecryptClient.decrypt(encryptedContent);
        return clearTextContent;
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String  encryptedContent = rs.getString(columnIndex);

        if(StringUtils.isBlank(encryptedContent)){
            return "";
        }
        String clearTextContent = encryptDecryptClient.decrypt(encryptedContent);
        return clearTextContent;
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String encryptedContent = cs.getNString(columnIndex);
        if(StringUtils.isBlank(encryptedContent)){
            return "";
        }
        String clearTextContent = encryptDecryptClient.decrypt(encryptedContent);
        return clearTextContent;
    }
}
