package cn.taqu.gonghui.system.search;

import cn.taqu.core.orm.PageSearch;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class LivePerDayStatisticSearch  extends PageSearch{

    //公用
    private String order_type; //排序类型 1趣豆 2有效天数
    private String sort_type; //排序方式 1倒序 2正序
    private String orgUuid; //公会id
    private Integer page;
    private Integer pageSize;
    private Long orgId;

    private String start;
    private String end;
    private String type;
    private String host_uuid;
    private String apply_level;
    private String live_status;
    private String agentManageUuid;
    private String date; //格式 202103
    private Integer export; //
    private List<String> hostUuidList;
    private List<Long> teamIdList;
    private Long teamId;
}
