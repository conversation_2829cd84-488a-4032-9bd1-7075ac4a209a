package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.Notice;
import cn.taqu.gonghui.system.search.NoticeSearch;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface NoticeMapper extends BaseMapper<Notice> {

    List<Notice> selectNoticePageList(NoticeSearch search);

    Notice selectByPrimaryKey(Long id);

    void updateByPrimaryKeySelective(Notice record);

    void deleteByPrimaryKey(Long id);

    int insertNotice(Notice record);

    List<Notice> findNoticeByNoticeType(@Param("noticeType")Integer noticeType, @Param("limitNum")Integer limtNum);


    void insertBatch(List<Notice> list);
}
