package cn.taqu.gonghui.system.service;

import cn.taqu.gonghui.system.dto.AgreementDto;
import cn.taqu.gonghui.system.entity.AgreementInfo;
import cn.taqu.gonghui.system.search.AgreementSignLogSearch;
import cn.taqu.gonghui.system.vo.AgreementSignLogVo;
import cn.taqu.gonghui.system.vo.AgreementVo;

import java.util.List;
import java.util.Map;

/**
 * 协议管理
 */
public interface AgreementInfoService {

    /**
     * 根据协议id获取协议详情
     * @param agrId
     * @return
     */
    AgreementInfo getById(Long agrId);

    /**
     * 根据协议id和状态获取协议详情
     * @param agrId
     * @return
     */
    AgreementInfo getAgreementLogById(Long agrId);

    /**
     * 获取有效协议列表（无分页）
     * @return
     */
    List<AgreementInfo> findList(String title);

    /**
     * 获取有效协议下拉tree
     * @return
     */
    List<Map<String,Object>> findMapForSearch();

    /**
     * 添加协议
     * @param agreementDto
     */
    void add(AgreementDto agreementDto);

    /**
     * 更改协议
     * @param agreementDto
     */
    void update(AgreementDto agreementDto);

    /**
     * 关闭当前协议
     * @param agrId
     */
    void close(Long agrId);

    /**
     * 获取机构有效的签署协议记录
     * @param search
     * @return
     */
    List<AgreementSignLogVo> findAgreementSignLogPageList(AgreementSignLogSearch search);

    /**
     * 根据机构id和协议顺序获取机构前属协议记录
     * @param orgId
     * @param orderLevel
     * @return
     */
    List<AgreementVo> findAgreementByOrgIdAndOrderLevel(Long orgId,Integer orderLevel);

    // --------------------------  以下为用户端服务 -------------------------------
    /**
     * 返回的签署map
     *
     * @param orgId
     * @return
     */
    List<Map<String, Object>> getSignMap(Long orgId);

    /**
     * 查看是否已经全部签署
     *
     * @param signMapList
     * @return
     */
    Boolean checkSign(List<Map<String, Object>> signMapList);

    /**
     * 根据协议id获取协议
     * @param agrId
     * @return
     */
    AgreementInfo getByAgrId(Long agrId);

    /**
     * 协议签署
     * @param agrId
     */
    void sign(Long agrId);
}
