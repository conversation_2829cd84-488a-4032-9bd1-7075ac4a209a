package cn.taqu.gonghui.system.mapper;

import cn.taqu.gonghui.system.entity.SysUserRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2021/4/30
 */
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    int deleteUserId(Long userId);

    int deleteUserIdAndRoleId(Long userId, Long roleId);

    int deleteUserRoleByRoleId(Long roleId);

    // ----------------------  以下是数据迁移需要用到的接口  -------------------------------
    void batchDeleteByIds(Set<Long> idSet);

    List<SysUserRole> getListByUserId(Long userId);
}
