package cn.taqu.gonghui.system.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.client.EncryptDecryptClient;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserStatus;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.mapper.GeneralUserListMapper;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.service.VerifyService;
import cn.taqu.gonghui.common.utils.DateTimeUtil;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.utils.UUID;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.common.vo.req.LinkUserReq;
import cn.taqu.gonghui.common.vo.res.GeneralUserListRes;
import cn.taqu.gonghui.soa.GonghuiService;
import cn.taqu.gonghui.soa.SsomsService;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.mapper.SysUserRoleMapper;
import cn.taqu.gonghui.system.search.SsoUserSearch;
import cn.taqu.gonghui.system.search.UserSearch;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamHostService;
import cn.taqu.gonghui.system.vo.*;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/4/25
 */
@Service
@Slf4j
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private TokenService tokenService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private TeamEmployeeService teamEmployeeService;
    @Autowired
    private TeamHostService teamHostService;
    @Autowired
    private VerifyService verifyService;
    @SoaReference("liveV1")
    private GonghuiService gonghuiService;
    @Autowired
    private SysUserService sysUserService;
    @SoaReference(application = "ssoms", value = "ssoms")
    private SsomsService ssomsService;
    @Autowired
    private GeneralUserListMapper generalUserListMapper;

    @Resource
    private EncryptDecryptClient encryptDecryptClient;

    @Resource
    private EncryptSwitchConfig encryptSwitchConfig;


    @Override
    public SysUser selectUserByMobile(String mobile) {
        if(EncryptSwitchConfig.selectByDigest.equals(1)){
           return selectUserByMobileDigest(mobile);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("mobile", mobile);
        return this.getOne(queryWrapper);
    }

    public SysUser selectUserByMobileDigest(String mobile){
        if(StringUtils.isBlank(mobile)){
            return null;
        }
        String mobileDigest = encryptDecryptClient.encode(mobile);
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getMobileDigest, mobileDigest);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<SysUser> selectUserListByMobile(String mobile) {
        if(EncryptSwitchConfig.selectByDigest.equals(1)){
            return selectUserListByMobileDigest(mobile);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("mobile", mobile);
        return this.list(queryWrapper);
    }

    public List<SysUser> selectUserListByMobileDigest(String mobile){
        if(StringUtils.isBlank(mobile)){
            return new ArrayList<>();
        }
        String mobileDigest = encryptDecryptClient.encode(mobile);
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getMobileDigest, mobileDigest);
        return this.list(queryWrapper);
    }

    @Override
    public List<SysUser> selectUserListByMobile2(String mobile) {
        if(EncryptSwitchConfig.selectByDigest.equals(1)){
            return selectUserListByMobileDigest2(mobile);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("mobile", mobile);
        queryWrapper.eq("status", 1);
        return this.list(queryWrapper);
    }

    public List<SysUser> selectUserListByMobileDigest2(String mobile){
        if(StringUtils.isBlank(mobile)){
            return new ArrayList<>();
        }
        String mobileDigest = encryptDecryptClient.encode(mobile);
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getMobileDigest, mobileDigest);
        queryWrapper.eq(SysUser::getStatus, 1);
        return this.list(queryWrapper);
    }


    private List<SysUser> selectUserListByMobileAndNotUserId(String mobile, Long userId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("mobile", mobile);
        queryWrapper.ne("user_id", userId);
        return this.list(queryWrapper);
    }


    @Override
    public SysUser selectUserByUserId(Long userId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("user_id", userId);
        return this.getOne(queryWrapper);
    }

    @Override
    public IPage<SysUserVo> selectUserByPage(UserSearch search, int pageNo, int pageSize) {
        IPage<SysUserVo> page = new Page<>(pageNo, pageSize);
        IPage<SysUserVo> sysUserVoIPage = baseMapper.selectUserBySearch(page, search);
        getTeamNameAndRoleByUserId(sysUserVoIPage.getRecords());
        return sysUserVoIPage;
    }

    private void getTeamNameAndRoleByUserId(List<SysUserVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (SysUserVo vo : list) {
            if (UserTypeEnum.DEFAULT.getType().equals(vo.getUserType())) {
                continue;
            }
            if (UserTypeEnum.MANAGER.getType().equals(vo.getUserType())) {
                Organization organization = organizationMapper.selectByPrimaryKey(vo.getOrgId());
                if (organization.getLivePermissions() != null && organization.getLivePermissions().equals(1)) {
                    vo.setLiveRoleName("机构管理员");
                }
                if (organization.getChatRoomPermissions() != null && organization.getChatRoomPermissions().equals(1)) {
                    vo.setChatRoleName("机构管理员");
                }
                continue;
            }
            List<UserTeamAndRoleVo> teamNameAndRoleNameList = baseMapper.getTeamNameAndRoleNameByUserId(vo.getUserId());
            if (CollectionUtils.isEmpty(teamNameAndRoleNameList)) {
                continue;
            }
            for (UserTeamAndRoleVo userTeamAndRoleVo : teamNameAndRoleNameList) {
                if (userTeamAndRoleVo.getBusinessType() != null && TeamTypeEnum.LIVE_TEAM.getValue() == userTeamAndRoleVo.getBusinessType().intValue()) {
                    RoleVo roleByUserId = sysUserService.getRoleByUserId(vo.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
                    vo.setLiveTeamId(userTeamAndRoleVo.getTeamId());
                    vo.setLiveTeamName(userTeamAndRoleVo.getTeamName());
                    if (roleByUserId != null) {
                        vo.setLiveRoleName(roleByUserId.getRoleName());
                    }
                }
                if (userTeamAndRoleVo.getBusinessType() != null && TeamTypeEnum.TALK_TEAM.getValue() == userTeamAndRoleVo.getBusinessType().intValue()) {
                    RoleVo roleByUserId = sysUserService.getRoleByUserId(vo.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
                    vo.setChatTeamId(userTeamAndRoleVo.getTeamId());
                    vo.setChatTeamName(userTeamAndRoleVo.getTeamName());
                    if (roleByUserId != null) {
                        vo.setChatRoleName(roleByUserId.getRoleName());
                    }
                }
            }
        }

    }

    @Override
    public void updateStatus(String uuid, Integer status) {
        if (StringUtils.isEmpty(uuid)) {
            throw new ServiceException("uuid_is_empty", "uuid为空");
        }
        SysUser sysUser = selectUserByAccountUuid(uuid);
        if (sysUser == null) {
            log.warn("当前用户{}，未找到", uuid);
            throw new ServiceException("user_not_found", "当前用户不存在");
        }
        if (UserStatus.DELETED.getCode().equals(sysUser.getStatus())) {
            throw new ServiceException("user_not_found", "当前用户已注销");
        }
        UpdateWrapper<SysUser> updateWrapper = new UpdateWrapper();
        updateWrapper.set("status", status).eq("account_uuid", uuid);
        List<TeamEmployee> teamEmployeeList = teamEmployeeService.getOneByUserId(sysUser.getUserId());
        if (!CollectionUtils.isEmpty(teamEmployeeList)) {
            for (TeamEmployee teamEmployee : teamEmployeeList) {
                if (null != teamEmployee) {
                    UpdateWrapper<TeamEmployee> updateWrapperTeamEmployee = new UpdateWrapper();
                    updateWrapperTeamEmployee.set("status", status).eq("employee_id", teamEmployee.getEmployeeId());
                    teamEmployeeService.update(updateWrapperTeamEmployee);
                }
            }
        }
        this.update(updateWrapper);
    }


    /**
     * @param
     */
    @Override
    public SysUser registerAccount(String mobile, UserTypeEnum userTypeEnum) {
        if (StringUtils.isEmpty(mobile)) {
            throw new ServiceException("mobile_is_empty", "请输入正确手机号");
        }
        if (null == userTypeEnum) {
            throw new ServiceException("user_type_is_empty", "请选择角色类型");
        }
        SysUser sysUser = new SysUser();
        sysUser.setAccountUuid(genUniqueUuid());
        sysUser.setUserName("机构管理员");
        sysUser.setUserType(userTypeEnum.getType());
        sysUser.setStatus(UserStatus.OK.getCode());
        sysUser.setMobile(mobile);
        sysUser.setCreateTime(DateUtil.currentTimeSeconds());
        sysUser.setUpdateTime(DateUtil.currentTimeSeconds());
        this.save(sysUser);
        return sysUser;
    }


    /**
     * 邀请成员时注册用户
     *
     * @param
     */
    @Override
    public SysUser registerAccountForMember(String mobile, UserTypeEnum userTypeEnum, String userName) {
        if (StringUtils.isEmpty(mobile)) {
            throw new ServiceException("mobile_is_empty", "请输入正确手机号");
        }
        if (null == userTypeEnum) {
            throw new ServiceException("user_type_is_empty", "请选择角色类型");
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser sysUser = new SysUser();
        sysUser.setAccountUuid(genUniqueUuid());
        sysUser.setOrgName(loginUser.getUser().getOrgName());
        sysUser.setOrgId(loginUser.getUser().getOrgId());
        sysUser.setUserType(userTypeEnum.getType());
        sysUser.setStatus(UserStatus.OK.getCode());
        sysUser.setMobile(mobile);
        sysUser.setCreateTime(DateUtil.currentTimeSeconds());
        sysUser.setUpdateTime(DateUtil.currentTimeSeconds());
        sysUser.setUserName(userName);
        this.save(sysUser);
        return sysUser;
    }


    @Override
    public String selectOrgManager(Long orgId) {
        LambdaQueryWrapper<SysUser>  queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(SysUser::getUserName, SysUser::getUserNameCipher)
                .eq(SysUser::getOrgId, orgId)
                .eq(SysUser::getUserType, 1)
                .last(" limit 1");
        List<SysUser> list = list(queryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return "";
        }
        return list.get(0).getUserName();

//        return baseMapper.selectOrgManager(orgId);
    }

    @Override
    public void updateLastLoginTime(Long userId) {
        UpdateWrapper<SysUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("update_time", DateUtil.currentTimeSeconds());
        updateWrapper.eq("user_id", userId);
        this.update(updateWrapper);
    }

    @Override
    public SysUser registerAccountByChangeUser(String mobile, Long orgId, String orgName) {
        if (StringUtils.isEmpty(mobile)) {
            throw new ServiceException("mobile_is_empty", "请输入正确手机号");
        }
        SysUser sysUser = new SysUser();
        sysUser.setAccountUuid(genUniqueUuid());
        sysUser.setOrgId(orgId);
        sysUser.setUserName("机构管理员");
        sysUser.setUserType(UserTypeEnum.MANAGER.getType());
        sysUser.setStatus(UserStatus.OK.getCode());
        sysUser.setMobile(mobile);
        sysUser.setCreateTime(DateUtil.currentTimeSeconds());
        sysUser.setUpdateTime(DateUtil.currentTimeSeconds());
        sysUser.setOrgName(orgName);
        this.save(sysUser);
        return sysUser;
    }

    public String genUniqueUuid() {
        String uuid = UUID.genUuid();
        while (true) {
            SysUser sysUser = selectUserByAccountUuid(uuid);
            if (sysUser == null) {
                return uuid;
            } else {
                uuid = UUID.genUuid();
            }
        }
    }

    @Override
    public SysUser selectUserByAccountUuid(String accountUuid) {
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper();
        queryWrapper.eq("account_uuid", accountUuid);
        return this.getOne(queryWrapper);
    }

    /**
     * 获取当前账户下，所有经纪人列表（有效+机构直属）
     *
     * @return
     */
    @Override
    public List<SysUser> getAllAgentManage() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        Organization organization = organizationMapper.selectByPrimaryKey(loginUser.getUser().getOrgId());
        SysUser sysUser = new SysUser();
        sysUser.setOrgId(organization.getOrgId());
        sysUser.setUserType(UserTypeEnum.AGENTER.getType());
        List<SysUser> agentManageList = null;
        try {
            agentManageList = baseMapper.selectUserAgent(sysUser);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return agentManageList;
    }

    /**
     * 根据业务 获取当前用户角色信息
     *
     * @param
     * @return
     */
    @Override
    public RoleVo getRoleByAccountUuid(Long userId, Integer type) {
        List<RoleVo> roleVoList = this.baseMapper.getRoleByAccountUuid(userId);
        if (CollectionUtils.isEmpty(roleVoList)) {
            log.warn("用户id:{},尚未分配角色", userId);
            throw new ServiceException(CodeStatus.ROLE_ERROR.value(), "当前用户尚未分配角色");
        }
        // 由于管理员与业务无关，所以先筛选
        for (RoleVo vo : roleVoList) {
            if (UserTypeEnum.MANAGER.getCode().equals(vo.getRoleKey())) {
                return vo;
            }
        }
        for (RoleVo vo : roleVoList) {
            if (vo.getType() == type) {
                return vo;
            }
        }
        return null;
    }

    /**
     * 获取用户所有业务角色,给登录用
     */
    @Override
    public List<RoleVo> getRoleByUserId(Long userId) {
        return this.baseMapper.getRoleByAccountUuid(userId);

    }


    @Override
    public RoleVo getRoleByUserId(Long userId, Integer type) {
        return this.baseMapper.getRoleByUserId(userId, type);
    }

    @Override
    public List<SysUser> getUserListByIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.in("user_id", userIds);
        return this.list(queryWrapper);
    }

    @Override
    public List<SysUser> getUserListByAccountUuids(List<String> accountUuids) {
        if (CollectionUtils.isEmpty(accountUuids)) {
            return null;
        }
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("account_uuid", accountUuids);
        return this.list(queryWrapper);
    }


    @Override
    public Map<String, String> getAgentSelect() {
        //获取有效的经纪人(有效)
        List<SysUser> agentManageList = getAllAgentManage();
        Map<String, String> map = agentManageList.stream().collect(Collectors.toMap(
                SysUser::getAccountUuid,
                SysUser::getUserName,
                (oldVal, newVal) -> oldVal
        ));
        return map;
    }


    @Override
    public void updateNameByUserId(Long userId, String name) {
        this.baseMapper.updateNameByUserId(userId, name);
    }

    /**
     * 注销用户操作
     *
     * @param uuid
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelUser(String uuid) {
        SysUser sysUser = selectUserByAccountUuid(uuid);
        if (sysUser == null) {
            log.warn("当前用户{}，未找到", uuid);
            throw new ServiceException("user_not_found", "当前用户不存在");
        }
        if (UserStatus.DELETED.getCode().equals(sysUser.getStatus())) {
            throw new ServiceException("user_not_found", "当前用户已注销");
        }
        //1 更新用户信息
        if (UserTypeEnum.MANAGER.getType().equals(sysUser.getUserType())) {
            Organization organization = new Organization();
            organization.setOrgId(sysUser.getOrgId());
            organization.setChargePersonPhone("");
            organizationMapper.updateByPrimaryKeySelective(organization);
        }
        UpdateWrapper<SysUser> updateWraper = new UpdateWrapper<>();
        updateWraper.set("mobile", "").set("user_type", UserTypeEnum.DEFAULT.getType()).set("org_name", "").set("org_id", null)
                .set("status", UserStatus.DELETED.getCode()).set("update_time", DateUtil.currentTimeSeconds());
        updateWraper.eq("user_id", sysUser.getUserId());
        this.update(updateWraper);
        //2 清除角色
        List<SysUserRole> list = sysUserRoleMapper.getListByUserId(sysUser.getUserId());
        if (CollectionUtils.isNotEmpty(list)) {
            Set<Long> idSet = list.stream().map(SysUserRole::getId).collect(Collectors.toSet());
            sysUserRoleMapper.deleteBatchIds(idSet);
        }


        //3 删除团队成员
        List<TeamEmployee> teamEmployeeList = teamEmployeeService.getOneByUserId(sysUser.getUserId());
        if (CollectionUtils.isNotEmpty(teamEmployeeList)) {
            teamEmployeeService.deleteByUserId(sysUser.getUserId());
            // 主播经纪人关联关系清除
            for (TeamEmployee teamEmployee : teamEmployeeList) {
                List<TeamHost> teamHosts = teamHostService.getListByEmployeeId(teamEmployee.getEmployeeId(), TeamTypeEnum.LIVE_TEAM.getValue());
                if (CollectionUtils.isNotEmpty(teamHosts)) {
                    List<String> hostUuids = teamHosts.stream().map(TeamHost::getHostUuid).collect(Collectors.toList());
                    log.info("机构id:{},团队id下{}：主播列表：{}转成无主经纪人", teamEmployee.getOrgId(), teamEmployee.getTeamId(), StringUtils.join(",", hostUuids));
                    for (TeamHost teamHost : teamHosts) {
                        teamHost.setEmployeeId(null);
                        // 通知php 将主播变成无主经纪人
                        gonghuiService.changeHostBusinessman(teamHost.getHostUuid(), "");
                    }
                    Set<Long> ids = teamHosts.stream().map(TeamHost::getId).collect(Collectors.toSet());
                    teamHostService.updateBatchEmpIdById(ids);
                }
            }

        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editUser(String uuid, String code, String mobile, String vcode, String userName) {
        valideParam(uuid, code, mobile, vcode, userName);
        SysUser sysUser = this.selectUserByAccountUuid(uuid);
        if (sysUser == null) {
            throw new ServiceException("user_not_found", "当前用户不存在");
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            throw new ServiceException("user_cannot_edit", "当前用户被封禁,不能编辑");
        }
        if (UserStatus.DELETED.getCode().equals(sysUser.getStatus())) {
            throw new ServiceException("user_cannot_edit", "当前用户被注销,不能编辑");
        }
        if (!sysUser.getMobile().equals(mobile)) {
            if (StringUtils.isEmpty(code)) {
                throw new ServiceException("code_id_empty", "验证码类型");
            }
            if (StringUtils.isEmpty(vcode)) {
                throw new ServiceException("vcode_id_empty", "请输入验证码");
            }
            verifyService.verifyCommon(code, mobile, vcode);
        }

        List<SysUser> sysUsers = this.selectUserListByMobileAndNotUserId(mobile, sysUser.getUserId());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(sysUsers)) {
            throw new ServiceException("mobile_is_exist", "手机号：" + mobile + " 的用户已存在了，请换一个手机号");
        }
        //机构管理员 修改机构管理员的手机号
        if (!sysUser.getMobile().equals(mobile) && UserTypeEnum.MANAGER.getType().equals(sysUser.getUserType())) {
            Organization organization = new Organization();
            organization.setOrgId(sysUser.getOrgId());
            organization.setChargePersonPhone(mobile);
            organizationMapper.updateByPrimaryKeySelective(organization);
        }

        //团队成员
        String mobileCipher = encryptDecryptClient.encrypt(mobile);
        String mobileDigest = encryptDecryptClient.encode(mobile);
        String employeeNameCipher =encryptDecryptClient.encrypt(userName);
        String employeeNameDigest = encryptDecryptClient.encode(userName);
        List<TeamEmployee> teamEmployeeList = teamEmployeeService.getOneByUserId(sysUser.getUserId());
        if (CollectionUtils.isNotEmpty(teamEmployeeList)) {
            for (TeamEmployee teamEmployee : teamEmployeeList) {
                if (teamEmployee != null) {
                    UpdateWrapper<TeamEmployee> updateEmpWraper = new UpdateWrapper<>();

                    if(EncryptSwitchConfig.doubleWrite.equals(1)) {

                        updateEmpWraper.set("mobile", mobile)
                                .set("employee_name", userName)
                                .set("mobile_cipher", mobileCipher)
                                .set("mobile_digest", mobileDigest)
                                .set("employee_name_cipher", employeeNameCipher)
                                .set("employee_name_digest", employeeNameDigest);
                    }else {
                        updateEmpWraper
                                .set("mobile", "")
                                .set("employee_name", "")
                                .set("mobile_cipher", mobileCipher)
                                .set("mobile_digest", mobileDigest)
                                .set("employee_name_cipher", employeeNameCipher)
                                .set("employee_name_digest", employeeNameDigest);
                    }
                    updateEmpWraper.set("update_time", DateUtil.currentTimeSeconds());
                    updateEmpWraper.eq("employee_id", teamEmployee.getEmployeeId());
                    teamEmployeeService.update(updateEmpWraper);
                }
            }
        }
        UpdateWrapper<SysUser> updateWraper = new UpdateWrapper<>();
        if(EncryptSwitchConfig.doubleWrite.equals(1)) { //双写
            updateWraper.set("mobile", mobile)
                    .set("user_name", userName)
                    .set("mobile_cipher", mobileCipher)
                    .set("mobile_digest", mobileDigest)
                    .set("user_name_cipher", employeeNameCipher);
        }else {
            updateWraper
                    .set("mobile", "")
                    .set("user_name", "")
                    .set("mobile_cipher", mobileCipher)
                    .set("mobile_digest", mobileDigest)
                    .set("user_name_cipher", employeeNameCipher);
        }
        updateWraper.set("update_time", DateUtil.currentTimeSeconds());
        updateWraper.eq("user_id", sysUser.getUserId());
        this.update(updateWraper);
    }

    /**
     * 获取用户信息
     *
     * @return
     */
    @Override
    public UserResultVo getUserInfo(String accountUuid) {
        QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
        wrapper.eq("account_uuid", accountUuid);
        SysUser sysUser = this.baseMapper.selectOne(wrapper);
        if (null == sysUser) {
            throw new ServiceException("invalid_user", "当前用户不存在");
        }

        UserResultVo vo = new UserResultVo();
        vo.setAccountUuid(accountUuid);
        vo.setUserName(sysUser.getUserName());

        RoleVo role = getRoleByAccountUuid(sysUser.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
        if (null == role) {
            throw new ServiceException(CodeStatus.ROLE_ERROR.value(), "当前用户尚未分配角色");
        }

        vo.setPosition(role.getRoleKey());
        if (UserTypeEnum.MANAGER.getCode().equals(role.getRoleKey())) {
            vo.setPositionName("管理员");
        } else if (UserTypeEnum.LEADER.getCode().equals(role.getRoleKey())) {
            vo.setPositionName("负责人");
        } else {
            vo.setPositionName("经纪人");
        }
        return vo;
    }

    private void valideParam(String uuid, String code, String mobile, String vcode, String userName) {
        if (StringUtils.isEmpty(uuid)) {
            throw new ServiceException("uuid_id_empty", "uuid参数为空");
        }
        if (StringUtils.isEmpty(mobile)) {
            throw new ServiceException("mobile_id_empty", "请输入手机号");
        } else {
            if (mobile.length() > 20) {
                throw new ServiceException("userName_is_too_long", "手机号码超出长度");
            }
        }
        if (StringUtils.isEmpty(userName)) {
            throw new ServiceException("userName_id_empty", "请输入用户姓名");
        } else {
            if (userName.length() > 20) {
                throw new ServiceException("userName_is_too_long", "用户姓名超出长度");
            }
        }
    }

    ;

    /**
     * 给用户端用 - 通过业务类型 获取当前登陆用户角色
     * 用户在一个业务类型中只能拥有一个角色
     * 机构管理员无 无业务类型区分 所以要特别处理
     */
    @Override
    public RoleVo getCurrentRole(Integer type) {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        RoleVo roleVo = this.getRoleByAccountUuid(user.getUserId(), type);
        if (null == roleVo) {
            throw new ServiceException(CodeStatus.ROLE_ERROR.value(), "当前用户尚未分配角色");
        }
        return roleVo;
    }

    @Override
    public List<Long> selectUserIdsByRoleId(Long orgId, Long roleId) {
        return this.baseMapper.selectUserIdsByRoleId(orgId, roleId);
    }

    @Override
    public List<SsoUserCombobox> pointAllUser() {
        SsoUserSearch ssoUserSearch = new SsoUserSearch();
        JSONArray array = ssomsService.searchEffectiveSsoUsersV2(ssoUserSearch);
        return JsonUtils.stringToObject(JsonUtils.objectToString(array), new TypeReference<List<SsoUserCombobox>>() {});
    }

    @Override
    public void addLinkUser(LinkUserReq req) {
        // 判断用户名是否存在
        QueryWrapper<GeneralUserList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", 0);
        queryWrapper.eq("scene", 1);
        queryWrapper.eq("login_name", req.getLoginName());
        GeneralUserList exists = generalUserListMapper.selectOne(queryWrapper);
        if (Objects.nonNull(exists)) {
            throw new ServiceException("user_exists", req.getLoginName() + " 用户已存在");
        }
        GeneralUserList generalUserList = new GeneralUserList();
        generalUserList.setScene(1);
        generalUserList.setCreateTime(DateUtil.currentTimeSeconds());
        generalUserList.setCreateUname(req.getOperator());
        generalUserList.setLoginName(req.getLoginName());
        generalUserList.setTrueName(req.getTrueName());
//        generalUserList.setTrueNameCipher(req.getTrueName());
        generalUserList.setModifyUname(req.getOperator());
        generalUserListMapper.insert(generalUserList);
    }
    @Override
    public void delLinkUser(LinkUserReq req) {
        QueryWrapper<GeneralUserList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", req.getId());
        GeneralUserList exists = generalUserListMapper.selectOne(queryWrapper);
        if (Objects.isNull(exists)) {
            throw new ServiceException("user_exists", "删除用户不存在");
        }
        exists.setIsDeleted(1);
        exists.setModifyUname(req.getOperator());
        generalUserListMapper.updateById(exists);
    }

    @Override
    public List<GeneralUserListRes> linkUserList(LinkUserReq req) {
        QueryWrapper<GeneralUserList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", 0);
        if (Objects.nonNull(req.getTrueName())) {
            queryWrapper.like("true_name", req.getTrueName());
        }
        List<GeneralUserList> list = generalUserListMapper.selectList(queryWrapper);
        List<GeneralUserListRes> resList = new ArrayList<>();
        for (GeneralUserList user : list) {
            GeneralUserListRes res = new GeneralUserListRes();
            BeanUtils.copyProperties(user, res);
            res.setCreateDate(DateTimeUtil.milliToStringSecond(user.getCreateTime() * 1000));
            resList.add(res);
        }
        return resList;
    }

    @Override
    public boolean isManager(Long userId, Integer type) {
        RoleVo currentRole = getRoleByAccountUuid(userId, type);
        if (null == currentRole) {
            log.error("用户尚未分配角色 {}", userId);
            return false;
        }
        if (!UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())) {
            return false;
        }
        return true;


    }
}
