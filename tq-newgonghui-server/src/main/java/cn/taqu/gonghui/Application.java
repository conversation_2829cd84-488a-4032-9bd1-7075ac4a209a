package cn.taqu.gonghui;

import cn.taqu.core.soa.EnableSoa;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.client.EncryptDecryptClient;
import cn.taqu.gonghui.common.utils.EncryptUtil;
import cn.taqu.gonghui.common.utils.SpringUtils;
import cn.taqu.gonghui.config.annotation.EnableSoaForm;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.sentinel.annotation.EnableSentinelNacosSource;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import com.google.common.collect.ImmutableMap;
import jdk.nashorn.internal.ir.annotations.Immutable;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;


@ServletComponentScan
@SpringBootApplication
@MapperScan("cn.taqu.gonghui.**.mapper")
@EnableSoa
@EnableScheduling
@EnableAsync
@EnableSoaForm
@EnableMethodCache(basePackages = {"cn.taqu.gonghui"})
@ComponentScan(basePackages = {"cn.taqu"})
@ComponentScan(basePackages = {"com.alibaba.*"})
@EnableSentinelNacosSource
@Slf4j
public class Application {

    public static void main(String[] args) {

        val ac = SpringApplication.run(Application.class, args);
        log.info("公会服务启动成功");
        log.info("加密开关配置为,是否双写：{}[0-关闭 1-开启], 优先读取从:{}[1-优先从明文 2-只从密文], 是否使用摘要查询:{}[0-否  1-是]",
                EncryptSwitchConfig.doubleWrite,
                EncryptSwitchConfig.readFrom,
                EncryptSwitchConfig.selectByDigest);
        val map = ac.getBean(EncryptDecryptClient.class).batchEncrypt(ImmutableMap.of("mobile", "12208522584"));
        val m = ac.getBean(EncryptDecryptClient.class).batchSm3(ImmutableMap.of("mobile", "12208522584"));
        log.info("加密结果为:{}", JsonUtils.objectToString(map));
        System.out.println();
    }

}
