package cn.taqu.gonghui.cron.encrypt;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.StringUtil;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.entity.OrgCompanyLog;
import cn.taqu.gonghui.system.service.IOrgCompanyLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/8 18 48
 * 机构公司信息修改日志--存量数据同步加密任务
 */
@Component
@Slf4j
public class OrgCompanyLogSyncToEncryptionProcessor implements BasicProcessor {


    @Resource
    private IOrgCompanyLogService iOrgCompanyLogService;

    /**
     * 表名
     */
    private static String TABLE_NAME = "org_company_log";

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        OmsLogger omsLogger = taskContext.getOmsLogger();
        omsLogger.info("orgAccountLogSyncToEncryptionProcessor start process, context is {}.", taskContext.getJobParams());
        syncEncryptApprovalFlowNode(taskContext);
        return new ProcessResult(true,"process successfully~");
    }

    public void syncEncryptApprovalFlowNode(TaskContext context){
        OmsLogger logger = context.getOmsLogger();
        logger.info("开始处理{}的存量数据，参数为：{}", TABLE_NAME, context.getJobParams());

        EncryptJobParam encryptJobParam = JsonUtils.stringToObject(context.getJobParams(), EncryptJobParam.class);
        Long startId = encryptJobParam.getStartId(); // 开始id
        Long endId = encryptJobParam.getEndId(); // 结束id
        Long step = encryptJobParam.getStep(); //步数

        Long curStartId = startId;
        Long curEndId = startId + step;

        while (curStartId < endId){
            LambdaQueryWrapper<OrgCompanyLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.between(OrgCompanyLog::getId, curStartId, curEndId);
            List<OrgCompanyLog> byRange = iOrgCompanyLogService.list(queryWrapper);
            if(CollectionUtils.isEmpty(byRange)){
                logger.info("表{},id区间：{}~{},没有记录，继续~",TABLE_NAME, curStartId, curEndId);
            }else {
                logger.info("表{},拉取id区间：{}~{},记录:{}条~", TABLE_NAME, curStartId, curEndId, byRange.size());
                List<OrgCompanyLog> list = byRange.stream().filter(l ->
                        StringUtils.isNotBlank(l.getNewLegalPerson()) ||
                                StringUtils.isNotBlank(l.getOldLegalPerson()) ||
                                StringUtils.isNotBlank(l.getNewLegalPersonPhone()) ||
                                StringUtils.isNotBlank(l.getOldLegalPersonPhone()) ||
                                StringUtils.isNotBlank(l.getNewLegalPersonidCard()) ||
                                StringUtils.isNotBlank(l.getOldLegalPersonidCard())

                ).collect(Collectors.toList());
                logger.info("过滤完要加密的明文字段为空的后，还剩：{}", list.size() );
                if(CollectionUtils.isEmpty(list)){
                    logger.info("过滤完要加密的明文字段为空的后，为空不需要处理", list.size() );
                }else {
                    //2024/7/8 更新数据
                    list.forEach(l -> {
                        l.setNewLegalPerson(l.getNewLegalPerson());
                        l.setOldLegalPerson(l.getOldLegalPerson());
                        l.setNewLegalPersonPhone(l.getNewLegalPersonPhone());
                        l.setOldLegalPersonPhone(l.getOldLegalPersonPhone());
                        l.setNewLegalPersonidCard(l.getNewLegalPersonidCard());
                        l.setOldLegalPersonidCard(l.getOldLegalPersonidCard());
                    });
                    iOrgCompanyLogService.updateBatchById(list);
                }
            }
            curStartId= curEndId + 1;
            curEndId = curStartId + (step-1);
            if(curEndId >= endId){
                curEndId = endId;
            }
        }

    }
}
