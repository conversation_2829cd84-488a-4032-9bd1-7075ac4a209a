package cn.taqu.gonghui.cron.manage;

import cn.taqu.core.task.annotation.SingleTask;
import cn.taqu.gonghui.chatroom.service.RoomDataBizService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/10/27 18:36
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ExportDataTask {

    private final RoomDataBizService roomDataBizService;

    /**
     * 每周一早上5:58 导出艺人数据 （报备房）
     */
    @SingleTask
    @Scheduled(cron = "0 58 5 ? * MON")
    public void mondayExportHostData() {
        roomDataBizService.mondayExportHostData(null);
    }

}
