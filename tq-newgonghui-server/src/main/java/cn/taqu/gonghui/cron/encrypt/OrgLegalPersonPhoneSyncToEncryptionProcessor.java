package cn.taqu.gonghui.cron.encrypt;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.client.EncryptDecryptClient;
import cn.taqu.gonghui.system.entity.OrgLegalPersonPhone;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.service.IOrgnizatonService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/8 18 48
 * 机构-法人手机号信息--存量数据同步加密任务
 */
@Component
@Slf4j
public class OrgLegalPersonPhoneSyncToEncryptionProcessor implements BasicProcessor {


    @Resource
    private IOrgnizatonService iOrgnizatonService;

    @Resource
    private EncryptDecryptClient encryptDecryptClient;

    /**
     * 表名
     */
    private static String TABLE_NAME = "organization";

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        OmsLogger omsLogger = taskContext.getOmsLogger();
        omsLogger.info("OrgLegalPersonPhoneSyncToEncryptionProcessor start process, context is {}.", taskContext.getJobParams());
        syncLegalPersonPhone(taskContext);
        return new ProcessResult(true, "process successfully~");
    }

    public void syncLegalPersonPhone(TaskContext context) {
        OmsLogger logger = context.getOmsLogger();
        logger.info("开始处理{}的存量数据-机构<法人手机号>，参数为：{}", TABLE_NAME, context.getJobParams());

        EncryptJobParam encryptJobParam = JsonUtils.stringToObject(context.getJobParams(), EncryptJobParam.class);
        Long startId = encryptJobParam.getStartId(); // 开始id
        Long endId = encryptJobParam.getEndId(); // 结束id
        Long step = encryptJobParam.getStep(); //步数

        Long curStartId = startId;
        Long curEndId = startId + step;

        while (curStartId < endId) {
            List<OrgLegalPersonPhone> byRange = iOrgnizatonService.selectLegalPersonPhoneByRange(curStartId, curEndId);
            if (CollectionUtils.isEmpty(byRange)) {
                logger.info("表{},id区间：{}~{},没有记录，继续~", TABLE_NAME, curStartId, curEndId);
            } else {
                logger.info("表{},拉取id区间：{}~{},记录:{}条~", TABLE_NAME, curStartId, curEndId, byRange.size());
                List<OrgLegalPersonPhone> list = byRange.stream()
                        .filter(l ->StringUtil.isNotBlank(l.getLegalPersonPhone())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(list)){
                    logger.info("过滤完需要加密的legal_person_phone明文全为空的情况，列表为空");
                }else {
                    logger.info("过滤完需要加密的legal_person_phone明文全为空的情况，列表个数为:{}", list.size());
                    //2024/7/8 更新数据
                    Map<String,String> legalPersonPhoneMap = list.stream().collect(Collectors.toMap(l->l.getOrgId().toString(), l -> l.getLegalPersonPhone(),(key1, key2)->key1));
                    Map<String, String> legalPersonPhoneCipherMap = encryptDecryptClient.batchEncrypt(legalPersonPhoneMap);
                    // 开始更新legal_person_phone的密文信息
                    logger.info("即将开始更新记录....");
                    for(OrgLegalPersonPhone item: list) {
                        String legalPersonPhoneCipher = legalPersonPhoneCipherMap.getOrDefault(item.getOrgId().toString(),"");
                        item.setLegalPersonPhoneCipher(legalPersonPhoneCipher);
//                        String legalPersonPhoneCipher = encryptDecryptClient.encrypt(legalPersonPhone.getLegalPersonPhone());
//                        legalPersonPhone.setLegalPersonPhoneCipher(legalPersonPhoneCipher);
                        iOrgnizatonService.updateLegalPersonPhoneCipher(item.getOrgId(), legalPersonPhoneCipher);

                    }
//                    iOrgnizatonService.batchUpdateLegalPersonPhoneCipher(list);
                    logger.info("更新完成{}条记录",list.size());
                }
            }
            curStartId = curEndId + 1;
            curEndId = curStartId + (step - 1);
            if (curEndId >= endId) {
                curEndId = endId;
            }
        }

    }

//    public void queryAndUpdateLegalPersonPhoneCipher(Long curStartId, Long curEndId) {
//        List<OrgLegalPersonPhone> byRange = iOrgnizatonService.selectLegalPersonPhoneByRange(curStartId, curEndId);
//        if (CollectionUtils.isEmpty(byRange)) {
//            log.info("表{},id区间：{}~{},没有记录，继续~", TABLE_NAME, curStartId, curEndId);
//        } else {
//            log.info("表{},拉取id区间：{}~{},记录:{}条~", TABLE_NAME, curStartId, curEndId, byRange.size());
//            List<OrgLegalPersonPhone> list = byRange.stream()
//                    .filter(l ->StringUtil.isNotBlank(l.getLegalPersonPhone())).collect(Collectors.toList());
//            if(CollectionUtils.isEmpty(list)){
//                log.info("过滤完需要加密的legal_person_phone明文全为空的情况，列表为空");
//            }else {
//                log.info("过滤完需要加密的legal_person_phone明文全为空的情况，列表个数为:{}", list.size());
//                //2024/7/8 更新数据
//                Map<String,String> legalPersonPhoneMap = list.stream().collect(Collectors.toMap(l->l.getOrgId().toString(), l -> l.getLegalPersonPhone(),(key1, key2)->key1));
//                Map<String, String> legalPersonPhoneCipherMap = encryptDecryptClient.batchEncrypt(legalPersonPhoneMap);
//                for(OrgLegalPersonPhone item: list) {
//                    String legalPersonPhoneCipher = legalPersonPhoneCipherMap.getOrDefault(item.getOrgId().toString(),"");
//                    item.setLegalPersonPhoneCipher(legalPersonPhoneCipher);
//                }
//                iOrgnizatonService.batchUpdateLegalPersonPhoneCipher(list);
//            }
//        }
//    }
}
