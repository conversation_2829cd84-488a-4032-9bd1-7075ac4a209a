package cn.taqu.gonghui.cron.user;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.task.annotation.SingleTask;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.entity.ApprovalFlowLog;
import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.common.mapper.ApprovalFlowLogMapper;
import cn.taqu.gonghui.common.mapper.ApprovalFlowMapper;
import cn.taqu.gonghui.common.mapper.ApprovalFlowNodeMapper;
import cn.taqu.gonghui.common.service.GeneralService;
import cn.taqu.gonghui.common.service.flow.QuitGuildFlow;
import cn.taqu.gonghui.common.utils.RedisUtil;
import cn.taqu.gonghui.common.utils.SmsUtil;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.ApprovalCustomVO;
import cn.taqu.gonghui.common.vo.CanQuitVO;
import cn.taqu.gonghui.common.vo.PassRejectVO;
import cn.taqu.gonghui.live.service.LiveSoaService;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.service.HostModifyRecordService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamHostService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ArrayStack;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/27 18:36
 */
@Component
@Slf4j
public class QuitGuildTask {

    @Resource
    private ApprovalFlowNodeMapper approvalFlowNodeMapper;

    @Resource
    private ApprovalFlowMapper approvalFlowMapper;

    @Resource
    private ApprovalFlowLogMapper approvalFlowLogMapper;

    @Resource
    private GeneralService generalService;

    @Resource
    private HostModifyRecordService hostModifyRecordService;

    @Resource
    private TeamHostService teamHostService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private LiveSoaService liveSoaService;

    @Autowired
    private QuitGuildFlow quitGuildFlow;

    /**
     * 7天有效期
     */
    private final static Integer expireTime = 60 * 60 * 24 * 7;

    private final static String SYS_USER = "system";

    /**
     * 4天超时未审批提醒()
     */
    @SingleTask
    @Scheduled(cron = "0 0 8,10,12,14,16,18,20 * * ?")
    public void fourDayOverTime() {
        log.info("fourDayOverTime,start:{}", DateUtil.now());
        DateTime beforeFourDateTime = DateUtil.offsetDay(new DateTime(), -4);
        ApprovalCustomVO approvalCustomVO = new ApprovalCustomVO();
        approvalCustomVO.setNodeStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        approvalCustomVO.setPreCreateTime(beforeFourDateTime.toString());
        approvalCustomVO.setFlowType(FlowTypeEnum.QUIT_GUILD.getCode());

        // 查询超过4天 & node_status状态为0
        List<ApprovalFlowNode> nodeList = approvalFlowNodeMapper.selectByCustom(approvalCustomVO);
        if (CollectionUtils.isEmpty(nodeList)) {
            log.info(DateUtil.now() + "无退会超时审批数据");
            return;
        }

        // 分组取第一个
        Map<Long, ApprovalFlowNode> nodeMap = nodeList.stream().collect(Collectors.groupingBy(ApprovalFlowNode::getFlowId,
                Collectors.collectingAndThen(Collectors.toList(),n -> n.get(0))));
        List<Long> flowIdList = nodeList.stream().map(ApprovalFlowNode::getFlowId).distinct().collect(Collectors.toList());
        List<ApprovalFlow> approvalFlowList = approvalFlowMapper.selectBatchIdList(flowIdList);
        Map<Long, String> flowMap = approvalFlowList.stream().collect(Collectors.toMap(ApprovalFlow::getId, ApprovalFlow::getNickName));

        // 遍历 & 发送短信提醒
        nodeMap.forEach((k, v) -> {
            if (StringUtils.isNotEmpty(v.getMobile())) {
                String redisKey = "quit_guild:fourDayOverTime:" + v.getFlowId().toString() + ":" + v.getMobile();
                if (StringUtils.isNotEmpty(redisUtil.get(redisKey))) {
                    return;
                }
                String nickName = Optional.ofNullable(flowMap.get(v.getFlowId())).orElse("");
                String content = "主播"+nickName+"发起了退会申请，请前往公会系统审批，3天内未审批将自动通过，登录地址：「https://union.taqu.cn」";
                SmsUtil.sendMsg(v.getMobile(), content);
                log.info("[fourDayOverTime],accountUuid:{}", v.getNodeUser());
                redisUtil.set(redisKey,"1", expireTime.longValue(), TimeUnit.SECONDS);
            }
        });
    }

    /**
     * 一周后自动审批(每天8点,20点执行)
     */
    @SingleTask
    @Scheduled(cron = "0 0 8,20 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void oneWeekAutoReview() {
        DateTime beforeWeekDateTime = DateUtil.offsetDay(new DateTime(), -7);
        ApprovalCustomVO approvalCustomVO = new ApprovalCustomVO();
        approvalCustomVO.setNodeStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        approvalCustomVO.setPreCreateTime(beforeWeekDateTime.toString());
        approvalCustomVO.setFlowType(FlowTypeEnum.QUIT_GUILD.getCode());

        // 查询超过7天 & node_status状态为0
        List<ApprovalFlowNode> nodeList = approvalFlowNodeMapper.selectByCustom(approvalCustomVO);
        if (CollectionUtils.isEmpty(nodeList)) {
            log.info(DateUtil.now() + "无超7天退会未审核");
            return;
        }

        List<Long> flowIdList = new ArrayList<>();
        List<Long> nodeIdList = new ArrayList<>();

        // 这里查询把 hostuuid 替换成 createUser字段
        for (ApprovalFlowNode node : nodeList) {
            // 允许退会则走自动审批逻辑
            if (!flowIdList.contains(node.getFlowId())) {
                flowIdList.add(node.getFlowId());
            }
            if (!nodeIdList.contains(node.getId())) {
                nodeIdList.add(node.getId());
            }
        }

        if (CollectionUtils.isEmpty(flowIdList)) {
            log.info(DateUtil.now() + "过滤不允许退会数据，无超7天退会未审核,筛选前数据:" + JsonUtils.objectToString(nodeList));
            return;
        }

        approvalFlowMapper.updateBatchStatus(flowIdList);
        List<ApprovalFlowNode> approvalFlowNodeList = new ArrayList<>();
        for (Long nodeId : nodeIdList) {
            ApprovalFlowNode approvalFlowNode = new ApprovalFlowNode();
            approvalFlowNode.setId(nodeId);
            approvalFlowNode.setNodeStatus(ApprovalStatusEnum.PASS.getCode());
            approvalFlowNode.setModifyUser(SYS_USER);
            approvalFlowNodeList.add(approvalFlowNode);
        }
        approvalFlowNodeMapper.updateBatch(approvalFlowNodeList);

        // 记录日志
        List<ApprovalFlowLog> logList = new ArrayList<>();
        for (Long flowId : flowIdList) {
            ApprovalFlowLog approvalFlowLog = new ApprovalFlowLog();
            approvalFlowLog.setFlowId(flowId);
            approvalFlowLog.setFlowStatus(ApprovalStatusEnum.PASS.getCode());
            approvalFlowLog.setCreateUser(SYS_USER);
            approvalFlowLog.setRemark("超过7天,系统自动审核通过");
            approvalFlowLog.setUserTimeInfo("system " + DateUtil.now());
            logList.add(approvalFlowLog);
        }
        approvalFlowLogMapper.insertBatch(logList);

        // 通知主播
        List<ApprovalFlow> approvalFlowList = approvalFlowMapper.selectBatchIdList(flowIdList);
        for (ApprovalFlow item : approvalFlowList) {
            // 中转公会
            teamHostService.transferGuild(item.getHostUuid());

            ModifyRecordInfoDTO dto = hostModifyRecordService.getModifyRecordInfo(item.getHostUuid(), HostOperateTypeEnum.QUIT_GUILD_MOVE);
            StringBuilder sb = new StringBuilder("您已成功退出");
            if (Objects.nonNull(dto)) {
                sb.append(dto.getOldOrgName());
            }
            sb.append("公会，现归属于中转公会，可直接开播或自行选择接受新的公会邀约");
            generalService.sendSecretary(item.getHostUuid(), sb.toString());
            log.info("[oneWeekAutoReview],hostUuid:{}", item.getHostUuid());
        }
    }

}
