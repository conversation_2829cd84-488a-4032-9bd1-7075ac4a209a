package cn.taqu.gonghui.cron.manage;

import cn.taqu.core.task.annotation.SingleTask;
import cn.taqu.gonghui.system.service.OrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 机构结算方式批量操作aa
 */
@Component
@Slf4j
public class LiveSettlementeTypeTask {

    @Autowired
    private OrganizationService organizationService;

//    @SingleTask
//    @Scheduled(cron = "0 00 16 29 * ?")
    public void liveSettlementeTypeOperate(){
        log.info("机构结算数据操作开始！");
        organizationService.liveSettlementeTypeOperate();
        log.info("机构结算数据操作完毕！");
    }
}
