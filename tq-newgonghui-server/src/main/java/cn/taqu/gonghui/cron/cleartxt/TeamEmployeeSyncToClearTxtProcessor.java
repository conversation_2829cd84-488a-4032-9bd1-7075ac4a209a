package cn.taqu.gonghui.cron.cleartxt;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.cron.encrypt.EncryptJobParam;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.mapper.TeamEmployeeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/8/8 09 51
 * 公会成员信息--清空明文信息
 */
@Component
@Slf4j
public class TeamEmployeeSyncToClearTxtProcessor implements BasicProcessor {


    @Resource
    private TeamEmployeeMapper teamEmployeeMapper;

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        OmsLogger omsLogger = taskContext.getOmsLogger();
        omsLogger.info("TeamEmployeeSyncToClearTxtProcessor start proccess, context is {}.", taskContext.getJobParams());
        setClearTxtToNull(taskContext);
        return new ProcessResult(true,"process successfully~");
    }

    public void setClearTxtToNull(TaskContext context){
        OmsLogger logger = context.getOmsLogger();
        logger.info("开始处理team_employee的明文数据，参数为：{}", context.getJobParams());
        EncryptJobParam encryptJobParam = JsonUtils.stringToObject(context.getJobParams(), EncryptJobParam.class);
        Long startId = encryptJobParam.getStartId(); // 开始id
        Long endId = encryptJobParam.getEndId(); // 结束id
        Long step = encryptJobParam.getStep(); //步数

        Long curStartId = startId;
        Long curEndId = startId + (step-1);

        while (curStartId < endId){
            teamEmployeeMapper.updateClearTxtByRange(curStartId, curEndId);
            logger.info("完成处理team_employee的明文数据，参数为：{}", context.getJobParams());
            curStartId= curEndId + 1;
            curEndId = curStartId + (step-1);
            if(curEndId >= endId){
                curEndId = endId;
            }
        }

    }
}
