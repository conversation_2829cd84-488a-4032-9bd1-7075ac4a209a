package cn.taqu.gonghui.cron.manage;

import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.task.annotation.SingleTask;
import cn.taqu.gonghui.common.service.AuditOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 定时检查工单审核情况
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping(path = "/api",params = "service=auditOrderTask")
public class AuditOrderTask {

    private final AuditOrderService auditOrderService;
    @EtcdValue(key = "biz.auditOrderTaskBatchSize",defaultValue = "100")
    private static int auditOrderTaskBatchSize = 100;

    @SingleTask
    @Scheduled(cron = "0/30 * * * * ?")
    public void scheduledExecute(){
        this.execute();
    }

    @RequestMapping(params = "method=execute")
    public void execute() {
        log.info("AuditOrderTask checkAuditOrder start");
        long start = System.currentTimeMillis();
        auditOrderService.checkAuditOrder(auditOrderTaskBatchSize);
        log.info("AuditOrderTask checkAuditOrder over cost:{}",
                System.currentTimeMillis() - start);

    }
}
