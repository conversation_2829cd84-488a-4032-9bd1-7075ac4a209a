package cn.taqu.gonghui.cron.encrypt;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamHostService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/8 18 48
 * 公会主播信息--存量数据同步加密任务
 */
@Component
@Slf4j
public class TeamHostSyncToEncryptionProcessor implements BasicProcessor {


    @Resource
    private TeamHostService teamHostService;

    /**
     * 表名
     */
    private static String TABLE_NAME = "team_host";

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        OmsLogger omsLogger = taskContext.getOmsLogger();
        omsLogger.info("roomSyncToEncryptionProcessor start process, context is {}.", taskContext.getJobParams());
        syncEncryptApprovalFlowNode(taskContext);
        return new ProcessResult(true, "process successfully~");
    }

    public void syncEncryptApprovalFlowNode(TaskContext context) {
        OmsLogger logger = context.getOmsLogger();
        logger.info("开始处理{}的存量数据，参数为：{}", TABLE_NAME, context.getJobParams());

        EncryptJobParam encryptJobParam = JsonUtils.stringToObject(context.getJobParams(), EncryptJobParam.class);
        Long startId = encryptJobParam.getStartId(); // 开始id
        Long endId = encryptJobParam.getEndId(); // 结束id
        Long step = encryptJobParam.getStep(); //步数

        Long curStartId = startId;
        Long curEndId = startId + step;

        while (curStartId < endId) {
            LambdaQueryWrapper<TeamHost> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                    .between(TeamHost::getId, curStartId, curEndId);
            List<TeamHost> byRange = teamHostService.list(queryWrapper);
            if (CollectionUtils.isEmpty(byRange)) {
                logger.info("表{},id区间：{}~{},没有记录，继续~", TABLE_NAME, curStartId, curEndId);
            } else {
                logger.info("表{},拉取id区间：{}~{},记录:{}条~", TABLE_NAME, curStartId, curEndId, byRange.size());
                List<TeamHost> list = byRange.stream().filter(l ->
                        StringUtil.isNotBlank(l.getRealName())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(list)) {
                    logger.info("过滤完需要加密的明文全为空的情况，列表为空");
                } else {
                    logger.info("过滤完需要加密的明文全为空的情况，列表个数为:{}", list.size());
                    //2024/7/8 更新数据
                    list.forEach(l -> {l.setRealName(l.getRealName());
                       teamHostService.updateBatchById(list);
                    });
                }
            }
            curStartId = curEndId + 1;
            curEndId = curStartId + (step - 1);
            if (curEndId >= endId) {
                curEndId = endId;
            }
        }

    }
}
