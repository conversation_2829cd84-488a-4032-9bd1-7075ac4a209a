package cn.taqu.gonghui.cron.manage;

import cn.taqu.core.task.annotation.SingleTask;
import cn.taqu.gonghui.live.service.OrgStatisticsCronService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * 每天统计前一天的主播数据,aa
 */
@Component
@Lazy(false)
public class OrgStatisticsTask {

    @Autowired
    private OrgStatisticsCronService orgStatisticsCronService;

//    @SingleTask
//    @Scheduled(cron = "0 0 7 * * ?")
    public void teamDailyStatistics(){
        orgStatisticsCronService.reportLiveTeamStatisticsDay();
    }


    @SingleTask
    @Scheduled(cron = "0 0 10 1 * ?")
    public void consortiaMonthStatistics(){
            orgStatisticsCronService.reportLiveOrgStatisticsMonth();
    }
}
