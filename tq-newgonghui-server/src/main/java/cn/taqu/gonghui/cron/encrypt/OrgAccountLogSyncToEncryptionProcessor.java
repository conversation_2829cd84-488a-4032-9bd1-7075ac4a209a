package cn.taqu.gonghui.cron.encrypt;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.system.entity.OperatorLog;
import cn.taqu.gonghui.system.entity.OrgAccountLog;
import cn.taqu.gonghui.system.mapper.OrgAccountLogMapper;
import cn.taqu.gonghui.system.service.IOperatorLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/8 18 48
 * 机构账户修改日志--存量数据同步加密任务
 */
@Component
@Slf4j
public class OrgAccountLogSyncToEncryptionProcessor implements BasicProcessor {


    @Resource
    private OrgAccountLogMapper orgAccountLogMapper;

    /**
     * 表名
     */
    private static String TABLE_NAME = "org_account_log";

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        OmsLogger omsLogger = taskContext.getOmsLogger();
        omsLogger.info("orgAccountLogSyncToEncryptionProcessor start process, context is {}.", taskContext.getJobParams());
        syncEncryptApprovalFlowNode(taskContext);
        return new ProcessResult(true,"process successfully~");
    }

    public void syncEncryptApprovalFlowNode(TaskContext context){
        OmsLogger logger = context.getOmsLogger();
        logger.info("开始处理{}的存量数据，参数为：{}", TABLE_NAME, context.getJobParams());

        EncryptJobParam encryptJobParam = JsonUtils.stringToObject(context.getJobParams(), EncryptJobParam.class);
        Long startId = encryptJobParam.getStartId(); // 开始id
        Long endId = encryptJobParam.getEndId(); // 结束id
        Long step = encryptJobParam.getStep(); //步数

        Long curStartId = startId;
        Long curEndId = startId + step;

        while (curStartId < endId){
            LambdaQueryWrapper<OrgAccountLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.between(OrgAccountLog::getId, curStartId, curEndId);
            List<OrgAccountLog> byRange = orgAccountLogMapper.selectList(queryWrapper);
            if(CollectionUtils.isEmpty(byRange)){
                logger.info("表{},id区间：{}~{},没有记录，继续~",TABLE_NAME, curStartId, curEndId);
            }else {
                logger.info("表{},拉取id区间：{}~{},记录:{}条~", TABLE_NAME, curStartId, curEndId, byRange.size());

                //2024/7/8 更新数据
                byRange.forEach(l->{
                    l.setOldPhone(l.getOldPhone());
                    l.setNewPhone(l.getNewPhone());
                    l.setOldPrincipal(l.getOldPrincipal());
                    l.setNewPrincipal(l.getNewPrincipal());
                    l.setOldMobilePhone(l.getOldMobilePhone());
                    l.setNewMobilePhone(l.getNewMobilePhone());

                    l.setOldIdentity(l.getOldIdentity());
                    l.setNewIdentity(l.getNewIdentity());
                    l.setOldSite(l.getOldSite());
                    l.setNewSite(l.getNewSite());

                    l.setOldMailbox(l.getOldMailbox());
                    l.setNewMailbox(l.getNewMailbox());
                    l.setOldIdentityFront(l.getOldIdentityFront());
                    l.setNewIdentityFront(l.getNewIdentityFront());

                    l.setOldIdentityReverse(l.getOldIdentityReverse());
                    l.setNewIdentityReverse(l.getNewIdentityReverse());
                    l.setOldIdentityHand(l.getOldIdentityHand());
                    l.setNewIdentityHand(l.getNewIdentityHand());
                    orgAccountLogMapper.updateById(l);
                });


            }
            curStartId= curEndId + 1;
            curEndId = curStartId + (step-1);
            if(curEndId >= endId){
                curEndId = endId;
            }
        }

    }
}
