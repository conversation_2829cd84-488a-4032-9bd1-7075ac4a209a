package cn.taqu.gonghui.cron.encrypt;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.system.entity.OrgAccountLog;
import cn.taqu.gonghui.system.entity.OrgBankLog;
import cn.taqu.gonghui.system.mapper.OrgAccountLogMapper;
import cn.taqu.gonghui.system.service.IOrgBankLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/8 18 48
 * 机构银行信息修改日志--存量数据同步加密任务
 */
@Component
@Slf4j
public class OrgBankLogSyncToEncryptionProcessor implements BasicProcessor {


    @Resource
    private IOrgBankLogService iOrgBankLogService;

    /**
     * 表名
     */
    private static String TABLE_NAME = "org_bank_log";

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        OmsLogger omsLogger = taskContext.getOmsLogger();
        omsLogger.info("orgAccountLogSyncToEncryptionProcessor start process, context is {}.", taskContext.getJobParams());
        syncEncryptApprovalFlowNode(taskContext);
        return new ProcessResult(true,"process successfully~");
    }

    public void syncEncryptApprovalFlowNode(TaskContext context){
        OmsLogger logger = context.getOmsLogger();
        logger.info("开始处理{}的存量数据，参数为：{}", TABLE_NAME, context.getJobParams());

        EncryptJobParam encryptJobParam = JsonUtils.stringToObject(context.getJobParams(), EncryptJobParam.class);
        Long startId = encryptJobParam.getStartId(); // 开始id
        Long endId = encryptJobParam.getEndId(); // 结束id
        Long step = encryptJobParam.getStep(); //步数

        Long curStartId = startId;
        Long curEndId = startId + step;

        while (curStartId < endId){
            LambdaQueryWrapper<OrgBankLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.between(OrgBankLog::getId, curStartId, curEndId);
            List<OrgBankLog> byRange = iOrgBankLogService.list(queryWrapper);
            if(CollectionUtils.isEmpty(byRange)){
                logger.info("表{},id区间：{}~{},没有记录，继续~",TABLE_NAME, curStartId, curEndId);
            }else {
                logger.info("表{},拉取id区间：{}~{},记录:{}条~", TABLE_NAME, curStartId, curEndId, byRange.size());

                //2024/7/8 更新数据
                byRange.forEach(l->{
                    l.setOldAccount(l.getOldAccount());
                    l.setNewAccount(l.getNewAccount());
                    l.setOldAddress(l.getOldAddress());
                    l.setNewAddress(l.getNewAddress());
                    l.setOldBankName(l.getOldBankName());
                    l.setNewBankName(l.getNewBankName());
                    l.setOldBank(l.getOldBank());
                    l.setNewBank(l.getNewBank());
                });
                iOrgBankLogService.updateBatchById(byRange);
            }
            curStartId= curEndId + 1;
            curEndId = curStartId + (step-1);
            if(curEndId >= endId){
                curEndId = endId;
            }
        }

    }
}
