package cn.taqu.gonghui.cron.encrypt;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.system.service.iservice.IApprovalFlowNodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tech.powerjob.worker.annotation.PowerJobHandler;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/8 18 48
 * 审批节点--存量数据同步加密任务
 */
@Component
@Slf4j
public class ApprovalFlowNodeSyncToEncryptionProcessor implements BasicProcessor {


    @Resource
    private IApprovalFlowNodeService iApprovalFlowNodeService;

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        OmsLogger omsLogger = taskContext.getOmsLogger();
        omsLogger.info("stockDataSyncToEncryptionProcessor start proccess, context is {}.", taskContext.getJobParams());
        syncEncryptApprovalFlowNode(taskContext);
        return new ProcessResult(true,"process successfully~");
    }

    @PowerJobHandler(name = "syncUpdateApprovalFlowNodeEncryptText")
    public void syncEncryptApprovalFlowNode(TaskContext context){
        OmsLogger logger = context.getOmsLogger();
        logger.info("开始处理approval_flow_node的存量数据，参数为：{}", context.getJobParams());

        EncryptJobParam encryptJobParam = JsonUtils.stringToObject(context.getJobParams(), EncryptJobParam.class);
        Long startId = encryptJobParam.getStartId(); // 开始id
        Long endId = encryptJobParam.getEndId(); // 结束id
        Long step = encryptJobParam.getStep(); //步数

        Long curStartId = startId;
        Long curEndId = startId + step;

        while (curStartId < endId){
            List<ApprovalFlowNode> byRange = iApprovalFlowNodeService.getByRange(curStartId, curEndId);
            if(CollectionUtils.isEmpty(byRange)){
                logger.info("区间：{}~{},没有记录，继续~", curStartId, curEndId);
            }else {
                logger.info("拉取区间：{}~{},记录:{}条~", curStartId, curEndId, byRange.size());

                //2024/7/8 更新数据
                byRange.forEach(l->l.setMobile(l.getMobile()));
                iApprovalFlowNodeService.updateBatchById(byRange);
            }
            curStartId= curEndId + 1;
            curEndId = curStartId + (step-1);
            if(curEndId >= endId){
                curEndId = endId;
            }
        }

    }
}
