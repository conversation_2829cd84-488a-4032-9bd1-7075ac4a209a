package cn.taqu.gonghui.cron.manage;

import cn.taqu.core.task.annotation.SingleTask;
import cn.taqu.gonghui.live.util.LiveDateUtils;
import cn.taqu.gonghui.system.service.TimedTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Component
@Slf4j
public class BusinessTask {
    @Autowired
    TimedTaskService timedTaskService;

//    @SingleTask
//    @Scheduled(cron = "0 0 10 * * ?")
    public void timedTask() throws ParseException {
        log.info("BusinessTask 定时任务开始执行");
        Date yesterday =  LiveDateUtils.getBeforeDay(new Date());
        this.timedTaskByDate(yesterday);
        log.info("BusinessTask 定时任务执行完成");

    }



    public void timedTaskByDate(Date executeDate) throws ParseException {
        log.info("timedTaskByDate开始执行");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String dateStr = sdf.format(executeDate);
        log.info("BusinessTask 定时任务请求参数={}", dateStr);
        timedTaskService.myDataTimedTask(dateStr);
        log.info("myDataTimedTask 定时任务执行完毕");
        timedTaskService.myPerformance(dateStr);
        log.info("myPerformance 定时任务执行完毕");


    }


}
