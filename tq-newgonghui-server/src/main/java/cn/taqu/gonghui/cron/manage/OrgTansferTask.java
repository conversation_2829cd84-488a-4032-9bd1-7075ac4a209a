package cn.taqu.gonghui.cron.manage;

import cn.taqu.core.task.annotation.SingleTask;

import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.utils.UUID;
import cn.taqu.gonghui.common.vo.req.ResetHostReq;
import cn.taqu.gonghui.live.service.ManangeOrgTransferService;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import cn.taqu.gonghui.system.service.HostService;
import cn.taqu.gonghui.system.service.HostSharingProfitRecordService;
import cn.taqu.gonghui.system.service.impl.TeamHostServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/6/24
 */
@Component
@Slf4j
public class OrgTansferTask {

    @Autowired
    private ManangeOrgTransferService manangeOrgTransferService;

    @Autowired
    private TeamHostServiceImpl teamHostService;

    @Autowired
    private TeamHostMapper teamHostMapper;

    @Autowired
    private BackstageOperateLogMapper backstageOperateLogMapper;

    @Autowired
    private HostModifyRecordMapper hostModifyRecordMapper;

    @Autowired
    private HostService hostService;

    @Autowired
    private HostSharingProfitRecordService hostSharingProfitRecordService;

    @Autowired
    private HostSharingProfitRecordMapper hostSharingProfitRecordMapper;

//    @SingleTask
//    @Scheduled(cron = "0 20 16 05 * ?")
    public void moveNoticeAndAgr(){
        manangeOrgTransferService.moveNoticeAndAgr();
    }

//    @SingleTask
//    @Scheduled(cron = "0 55 17 13 * ?")
    public void orgTansfer(){
        log.info("所有数据迁移开始！");
        List<String> uuidList = testUUids();
        for(String guildUuid:uuidList){
            manangeOrgTransferService.autoTransfer(guildUuid);
        }
        log.info("所有数据迁移完毕！");
    }


//    @SingleTask
//    @Scheduled(cron = "0 25 14 13 * ?")
    public void orgTansferAgent(){

        log.info("修改运营人员名称开始！");
        manangeOrgTransferService.updateAgentName();
        log.info("修改运营人员名称完毕！");
    }

//    @SingleTask
//    @Scheduled(cron = "0 00 15 03 * ?")
    public void orgTaquTansfer(){
        log.info("素人公会数据迁移开始！");
        manangeOrgTransferService.trasnferTaquGonghguiHost();
        log.info("素人公会数据迁移完毕！");
    }


    private List<String> alphaTestUuids(){

        List<String> uuidList= new ArrayList();
        uuidList.add("110697");
        uuidList.add("110051");
        return  uuidList;
    }

    private  List<String> testUUids(){
        List<String> uuidList= new ArrayList();
        uuidList.add("110000");
        uuidList.add("110001");
        uuidList.add("110002");
        uuidList.add("110003");
        uuidList.add("110004");
        uuidList.add("110005");
        uuidList.add("110006");
        uuidList.add("110007");
        uuidList.add("110008");
        uuidList.add("110009");
        uuidList.add("110010");
        uuidList.add("110011");
        uuidList.add("110012");
        uuidList.add("110013");
        uuidList.add("110014");
        uuidList.add("110016");
        uuidList.add("110017");
        uuidList.add("110018");
        uuidList.add("110019");
        uuidList.add("110020");
        uuidList.add("110021");
        uuidList.add("110022");
        uuidList.add("110023");
        uuidList.add("110024");
        uuidList.add("110025");
        uuidList.add("110026");
        uuidList.add("110027");
        uuidList.add("110028");
        uuidList.add("110030");
        uuidList.add("110031");
        uuidList.add("110032");
        uuidList.add("110033");
        uuidList.add("110034");
        uuidList.add("110035");
        uuidList.add("110036");
        uuidList.add("110037");
        uuidList.add("110038");
        return  uuidList;
    }


    private  List<String> onlineUUids(){
        List<String> uuidList= new ArrayList();
        //线上迁移公会uuid
        uuidList.add("110512");
        uuidList.add("110572");
        uuidList.add("110626");
        uuidList.add("110241");
        uuidList.add("110240");

        uuidList.add("110700");
        uuidList.add("110699");
        uuidList.add("110694");
        uuidList.add("820");
        uuidList.add("110685");


        uuidList.add("110671");
        uuidList.add("110667");
        uuidList.add("15");
        uuidList.add("110646");
        uuidList.add("110640");


        uuidList.add("110638");
        uuidList.add("110616");
        uuidList.add("110615");
        uuidList.add("110589");
        uuidList.add("110588");

        uuidList.add("825");
        uuidList.add("560");
        uuidList.add("461");
        uuidList.add("736");
        uuidList.add("762");

        uuidList.add("927");
        uuidList.add("954");
        uuidList.add("969");
        uuidList.add("993");
        uuidList.add("110001");

        uuidList.add("110003");
        uuidList.add("110004");
        uuidList.add("110010");
        uuidList.add("110051");
        uuidList.add("110054");

        uuidList.add("110264");
        uuidList.add("110274");
        uuidList.add("110317");
        uuidList.add("110426");
        uuidList.add("110447");

        uuidList.add("110489");
        uuidList.add("110510");
        uuidList.add("110563");
        uuidList.add("110569");
        uuidList.add("110574");


        uuidList.add("110587");
        uuidList.add("110597");
        uuidList.add("110596");
        uuidList.add("110661");
        uuidList.add("110469");

        uuidList.add("110679");
        uuidList.add("110618");
        uuidList.add("110672");
        uuidList.add("110660");
        uuidList.add("110673");


        uuidList.add("110647");
        uuidList.add("110670");
        uuidList.add("110687");
        uuidList.add("110686");
        uuidList.add("110537");


        uuidList.add("110698");
        uuidList.add("110697");
        uuidList.add("110359");
        uuidList.add("110690");
        uuidList.add("110689");

        uuidList.add("110682");
        uuidList.add("110705");
        uuidList.add("110624");
        uuidList.add("110703");
        uuidList.add("110560");

        uuidList.add("110547");
        uuidList.add("110570");
        uuidList.add("705");
        uuidList.add("417");
        uuidList.add("110631");


        uuidList.add("110592");
        uuidList.add("37");
        uuidList.add("110555");
        uuidList.add("110630");
        uuidList.add("110602");

        uuidList.add("110509");
        uuidList.add("110573");
        uuidList.add("39");
        uuidList.add("110468");
        uuidList.add("110299");

        uuidList.add("110655");
        uuidList.add("110599");
        uuidList.add("110476");
        uuidList.add("110177");
        uuidList.add("110427");


        uuidList.add("110242");
        uuidList.add("110127");
        uuidList.add("110415");
        uuidList.add("110318");
        uuidList.add("110546");


        uuidList.add("110369");
        uuidList.add("110559");
        uuidList.add("110267");
        uuidList.add("110471");
        uuidList.add("110294");

        uuidList.add("952");
        uuidList.add("110403");
        uuidList.add("110542");
        uuidList.add("110371");
        uuidList.add("110388");


        uuidList.add("110384");
        uuidList.add("110497");
        uuidList.add("110556");
        uuidList.add("110496");
        uuidList.add("110521");


        uuidList.add("110524");
        uuidList.add("110507");
        uuidList.add("110411");
        uuidList.add("110505");
        uuidList.add("110584");


        uuidList.add("110608");
        uuidList.add("110632");
        uuidList.add("110627");
        uuidList.add("110629");
        uuidList.add("110628");

        uuidList.add("110625");
       // uuidList.add("134");
        uuidList.add("144");

       // uuidList.add("386");
        uuidList.add("753");

        uuidList.add("110558");

        uuidList.add("36");

        uuidList.add("714");
        uuidList.add("110564");
        uuidList.add("100153");
        uuidList.add("110076");
        uuidList.add("405");
        uuidList.add("8");
        uuidList.add("100150");
        uuidList.add("110461");
        uuidList.add("110017");
        uuidList.add("110578");

        uuidList.add("110193");
        uuidList.add("110246");
        uuidList.add("110557");
        uuidList.add("110074");
        uuidList.add("69");
        uuidList.add("814");
        uuidList.add("110256");
        uuidList.add("110295");
        uuidList.add("823");
        uuidList.add("110676");
        uuidList.add("16");
        uuidList.add("995");
        uuidList.add("110226");
        uuidList.add("110568");
        uuidList.add("110488");
        uuidList.add("110417");
        uuidList.add("110418");
        uuidList.add("110506");

        uuidList.add("110654");
        uuidList.add("110665");


//        uuidList.add("367");
//        uuidList.add("1134");
//        uuidList.add("568");
//        uuidList.add("646");
//        uuidList.add("16");
//
//        uuidList.add("50");
//        uuidList.add("561");
//        uuidList.add("1131");
//        uuidList.add("1148");
//        uuidList.add("587");
//
//
//        uuidList.add("110193");
//        uuidList.add("816");
//        uuidList.add("1127");
//        uuidList.add("644");
//        uuidList.add("5");
//
//        uuidList.add("429");
//        uuidList.add("826");
//        uuidList.add("865");
//        uuidList.add("436");
//        uuidList.add("1246");
//
//        uuidList.add("58");
//        uuidList.add("557");
//        uuidList.add("796");
//        uuidList.add("1138");
//        uuidList.add("1058");
//
//
//        uuidList.add("988");
//        uuidList.add("987");
//        uuidList.add("1076");
//        uuidList.add("1224");
//        uuidList.add("1235");
        return  uuidList;
    }


    private List<String> zombieOnlineUuids(){

        List<String> uuidList = new ArrayList<>();
        uuidList.add("767");
        uuidList.add("630");
        uuidList.add("570");
        uuidList.add("2");
        return  uuidList;
    }

    /**
     * 批量调整分润
     */
    public void liveAllProfitReset() {
        // 查询需要中转的host
        int limit = 100;
        List<TeamHost> teamHostList = teamHostMapper.selectResetRow(100);
        recursionProfitReset(teamHostList);
    }

    /**
     * 递归重置分润
     * @param teamHostList
     */
    private void recursionProfitReset(List<TeamHost> teamHostList) {
        if (CollectionUtils.isEmpty(teamHostList)) {
            log.info("无数据");
            return;
        }
        for (TeamHost teamHost : teamHostList) {
            hostSharingProfitRecordService.runResetProfit(teamHost);
        }

        // sleep
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        List<TeamHost> nextList = teamHostMapper.selectResetRow(100);
        if (CollectionUtils.isNotEmpty(nextList)) {
            recursionProfitReset(nextList);
        }
    }

    /**
     * 直播全部迁移中转
     * 测试环境
     * 素人公会id 110016
     * 中转公会id 40
     *
     * 线上环境
     * 素人公会id 110166
     * 中转公会id 110648
     */
    public void liveAllTransfer() {
        // 查询需要中转的host
        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        Long normalTeamId = teamHostService.getEnvTeamId(1);
        queryWrapper.eq("team_id", normalTeamId);
        queryWrapper.orderByAsc("id");
        queryWrapper.last("limit 100");
        List<TeamHost> teamHostList = teamHostMapper.selectList(queryWrapper);
        recursionTransfer(queryWrapper, teamHostList);
    }

    /**
     * 递归中转公会
     * @param queryWrapper
     * @param teamHostList
     */
    @Transactional(rollbackFor = Exception.class)
    public void recursionTransfer(QueryWrapper queryWrapper, List<TeamHost> teamHostList) {
        if (CollectionUtils.isEmpty(teamHostList)) {
            log.info("无数据");
            return;
        }
        long midOrgId = 1000703L;
        long midTeamId = teamHostService.getEnvTeamId(2);
        long now = DateUtil.currentTimeSeconds();
        List<BackstageOperateLog> logList = new ArrayList<>();
        List<HostModifyRecord> modifyList = new ArrayList<>();
        List<TeamHost> updateTeamHostList = new ArrayList<>();
        List<HostSharingProfitRecord> profitRecordList = new ArrayList<>();
        List<String> uuidList = new ArrayList<>();
        Long nowTime = System.currentTimeMillis() / 1000;
        for (TeamHost teamHost : teamHostList) {
            // 通知直播
            uuidList.add(teamHost.getHostUuid());

            // 添加修改日志
            BackstageOperateLog log = new BackstageOperateLog();
            log.setBatchId(UUID.genUuid());
            log.setOperateType(BackstageOperateLog.OperateTypeEnum.CHANGE_BUSINESS.getValue());
            log.setCreateTime(now);
            log.setOperator("system");
            log.setInfo("素人批量中转公会,主播uuid:" + teamHost.getHostUuid());
            log.setUpdateTime(now);
            logList.add(log);

            // 添加操作日志
            HostModifyRecord record = new HostModifyRecord();
            record.setHostUuid(teamHost.getHostUuid());
            record.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
            record.setBatchId(UUID.genUuid());
            record.setOperateType(HostOperateTypeEnum.BATCH_MOVE.ordinal());
            ModifyRecordInfoDTO modifyRecordInfo = new ModifyRecordInfoDTO();
            modifyRecordInfo.setOldOrgId(teamHost.getOrgId());
            modifyRecordInfo.setOldTeamId(teamHost.getTeamId());
            modifyRecordInfo.setNewOrgId(midOrgId);
            modifyRecordInfo.setNewTeamId(midTeamId);
            record.setInfo(JsonUtils.objectToString(modifyRecordInfo));
            record.setReason("批量中转公会");
            record.setStatus(Constants.YES_1);
            record.setOperator("system");
            record.setCreateTime(now);
            record.setUpdateTime(now);
            modifyList.add(record);

            // 修改公会id
            teamHost.setOrgId(midOrgId);
            teamHost.setTeamId(midTeamId);
            teamHost.setEmployeeId(null);
            teamHost.setCurrentSharingProfitRate(Constants.MID_PROFIT);
            teamHost.setNewSharingProfitRate(Constants.MID_PROFIT);
            teamHost.setStatus(EmployeeStatusEnum.DEPAETURE.getValue());
            teamHost.setUpdateTime(now);
            updateTeamHostList.add(teamHost);

            // 插入分润记录 自动生效
            HostSharingProfitRecord profitRecord = new HostSharingProfitRecord();
            profitRecord.setHostUuid(teamHost.getHostUuid());
            profitRecord.setAccountUuid("admin");
            profitRecord.setType(2);
            profitRecord.setOrgId(teamHost.getOrgId());
            profitRecord.setTeamId(teamHost.getTeamId());
            profitRecord.setStatus(HostSharingProfitStatusEnum.EFFECTIVE.getValue());
            profitRecord.setCurrentSharingProfitRate(teamHost.getCurrentSharingProfitRate());
            profitRecord.setNewSharingProfitRate(Constants.MID_PROFIT);
            profitRecord.setChangeTime(nowTime);
            profitRecord.setEffectiveTime(0L);
            profitRecord.setCreateTime(nowTime);
            profitRecord.setUpdateTime(nowTime);
            profitRecordList.add(profitRecord);
        }

        // 执行sql
        backstageOperateLogMapper.batchInsert(logList);
        hostModifyRecordMapper.batchInsert(modifyList);
        teamHostMapper.batchUpdate(teamHostList);
        hostSharingProfitRecordMapper.batchInsert(profitRecordList);

        // 通知直播
        hostService.changeHostConsortia(String.valueOf(midTeamId), uuidList);
        log.info("素人批量中转公会recursionTransfer,uuidList:{}", uuidList);

        // sleep
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        List<TeamHost> nextList = teamHostMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(nextList)) {
            recursionTransfer(queryWrapper, nextList);
        }
    }

}
