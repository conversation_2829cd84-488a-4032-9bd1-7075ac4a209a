package cn.taqu.gonghui.cron.encrypt;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.system.entity.OrgCompanyLog;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.service.IOrgCompanyLogService;
import cn.taqu.gonghui.system.service.IOrgnizatonService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/8 18 48
 * 机构信息--存量数据同步加密任务
 */
@Component
@Slf4j
public class OrganizationSyncToEncryptionProcessor implements BasicProcessor {


    @Resource
    private IOrgnizatonService iOrgnizatonService;

    /**
     * 表名
     */
    private static String TABLE_NAME = "organization";

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        OmsLogger omsLogger = taskContext.getOmsLogger();
        omsLogger.info("orgAccountLogSyncToEncryptionProcessor start process, context is {}.", taskContext.getJobParams());
        syncEncryptApprovalFlowNode(taskContext);
        return new ProcessResult(true, "process successfully~");
    }

    public void syncEncryptApprovalFlowNode(TaskContext context) {
        OmsLogger logger = context.getOmsLogger();
        logger.info("开始处理{}的存量数据，参数为：{}", TABLE_NAME, context.getJobParams());

        EncryptJobParam encryptJobParam = JsonUtils.stringToObject(context.getJobParams(), EncryptJobParam.class);
        Long startId = encryptJobParam.getStartId(); // 开始id
        Long endId = encryptJobParam.getEndId(); // 结束id
        Long step = encryptJobParam.getStep(); //步数

        Long curStartId = startId;
        Long curEndId = startId + step;

        while (curStartId < endId) {
            LambdaQueryWrapper<Organization> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(Organization::getOrgId,
                            Organization::getOrgName,
                            Organization::getOrgUuid,
                            Organization::getChargePerson,
                            Organization::getChargePersonIdCard,
                            Organization::getChargePersonPhone,
                            Organization::getChargePersonEmail,
                            Organization::getReceivingAddress,
                            Organization::getLegalPerson,
                            Organization::getLegalPersonPhone,
                            Organization::getLegalPersonIdCard,
                            Organization::getPublicReceivingBankAccount,
                            Organization::getBusinessPerson,
                            Organization::getContactPhone
                    )
                    .between(Organization::getOrgId, curStartId, curEndId);
            List<Organization> byRange = iOrgnizatonService.list(queryWrapper);
            if (CollectionUtils.isEmpty(byRange)) {
                logger.info("表{},id区间：{}~{},没有记录，继续~", TABLE_NAME, curStartId, curEndId);
            } else {
                logger.info("表{},拉取id区间：{}~{},记录:{}条~", TABLE_NAME, curStartId, curEndId, byRange.size());
                List<Organization> list = byRange.stream().filter(l ->
                        StringUtil.isNotBlank(l.getChargePerson()) ||
                                StringUtil.isNotBlank(l.getChargePersonIdCard()) ||
                                StringUtil.isNotBlank(l.getChargePersonPhone()) ||
                                StringUtil.isNotBlank(l.getChargePersonEmail()) ||
                                StringUtil.isNotBlank(l.getReceivingAddress()) ||
                                StringUtil.isNotBlank(l.getLegalPerson()) ||
                                StringUtil.isNotBlank(l.getLegalPersonIdCard()) ||
                                StringUtil.isNotBlank(l.getPublicReceivingBankAccount()) ||
                                StringUtil.isNotBlank(l.getBusinessPerson()) ||
                                StringUtil.isNotBlank(l.getLegalPersonPhone()) ||
                                StringUtil.isNotBlank(l.getContactPhone()) ||
                                StringUtil.isNotBlank(l.getLegalPersonPhone())
                ).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(list)){
                    logger.info("过滤完需要加密的明文全为空的情况，列表为空");
                }else {
                    logger.info("过滤完需要加密的明文全为空的情况，列表个数为:{}", list.size());
                    //2024/7/8 更新数据
                    list.forEach(l -> {
                        l.setChargePerson(l.getChargePerson());
                        l.setChargePersonIdCard(l.getChargePersonIdCard());
                        l.setChargePersonPhone(l.getChargePersonPhone());
                        l.setChargePersonEmail(l.getChargePersonEmail());
                        l.setReceivingAddress(l.getReceivingAddress());
                        l.setLegalPerson(l.getLegalPerson());
                        l.setLegalPersonIdCard(l.getLegalPersonIdCard());
                        l.setPublicReceivingBankAccount(l.getPublicReceivingBankAccount());
                        l.setBusinessPerson(l.getBusinessPerson());
                        l.setLegalPersonPhone(l.getLegalPersonPhone());
                        l.setContactPhone(l.getContactPhone());
                        l.setLegalPersonPhone(l.getLegalPersonPhone());
                    });
                    iOrgnizatonService.updateBatchById(list);
                }
            }
            curStartId = curEndId + 1;
            curEndId = curStartId + (step - 1);
            if (curEndId >= endId) {
                curEndId = endId;
            }
        }

    }
}
