package cn.taqu.gonghui.cron.user;

import cn.taqu.core.task.annotation.SingleTask;
import cn.taqu.gonghui.system.service.HostSharingProfitRecordService;
import cn.taqu.gonghui.system.service.TeamHostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定时轮询，主播调整分润比例，本月调整次月生效
 */
@Component
@Lazy(false)
public class HostSharingProfitRateTask {

    @Autowired
    private TeamHostService teamHostService;

    @Autowired
    private HostSharingProfitRecordService sharingProfitRecordService;

    /**
     * 每个月零晨00:00:05秒执行一次
     * 邀约主播入驻成功后当前分润比例存入currentSharingProfitRate字段，newSharingProfitRate字段为空，isUpdate字段默认为0
     * 定时任务轮询 isUpdate为1的有效主播，将主播调整后的分润比例（newSharingProfitRate字段）的值更新到主播当前分润比例（currentSharingProfitRate字段）中去，并将isUpdate置为0
     * 当用户端执行调整主播分润比例操作 将调整后的分润比例存入（newSharingProfitRate）字段并将isUpdate字段置为1
     */
    @SingleTask
    @Scheduled(cron = "5 0 0 1 * ?")
    public void updateSharingProfitRate(){
        teamHostService.updateSharingProfitRateCron();
    }

    /**
     * 每天的6点，12点，15点，20点各执行一次
     * 过期掉无效的分润记录
     */
    @SingleTask
    @Scheduled(cron = "0 0 6,12,15,20 * * ?")
    public void hostSharingRecordAutoExpire(){
        sharingProfitRecordService.autoExpire();
    }

}
