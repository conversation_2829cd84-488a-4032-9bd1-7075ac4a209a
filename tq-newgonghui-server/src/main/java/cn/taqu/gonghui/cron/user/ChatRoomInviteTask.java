package cn.taqu.gonghui.cron.user;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.task.annotation.SingleTask;
import cn.taqu.gonghui.common.constant.ApprovalStatusEnum;
import cn.taqu.gonghui.common.constant.FlowTypeEnum;
import cn.taqu.gonghui.common.constant.HostOperateTypeEnum;
import cn.taqu.gonghui.common.constant.InviteStatusEnum;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.entity.ApprovalFlowLog;
import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.common.mapper.ApprovalFlowLogMapper;
import cn.taqu.gonghui.common.mapper.ApprovalFlowMapper;
import cn.taqu.gonghui.common.mapper.ApprovalFlowNodeMapper;
import cn.taqu.gonghui.common.service.GeneralService;
import cn.taqu.gonghui.common.utils.RedisUtil;
import cn.taqu.gonghui.common.utils.SmsUtil;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.ApprovalCustomVO;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.service.HostModifyRecordService;
import cn.taqu.gonghui.system.service.TeamHostService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotNull;
import static com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank;

/**
 * <AUTHOR>
 * @date 2022/10/27 18:36
 */
@Component
@Slf4j
public class ChatRoomInviteTask {

    @Resource
    private ApprovalFlowNodeMapper approvalFlowNodeMapper;

    @Resource
    private ApprovalFlowMapper approvalFlowMapper;

    @Resource
    private GeneralService generalService;

    @Autowired
    private RedisUtil redisUtil;

    @EtcdValue("biz.chatroom.inviteLinkUrl")
    private static String inviteLinkUrl;

    /**
     * 7天有效期
     */
    private final static Integer expireTime = 60 * 60 * 24 * 7;

    private final static String SYS_USER = "system";

    /**
     * 3天超时未审批提醒()
     */
    @SingleTask
    @Scheduled(cron = "0 0 8,10,12,14,16,18,20,22 * * ?")
    public void chatThreeDayOverTime() {
        log.info("chatThreeDayOverTime,start:{}", DateUtil.now());
        DateTime beforeFourDateTime = DateUtil.offsetDay(new DateTime(), -2);
        ApprovalCustomVO approvalCustomVO = new ApprovalCustomVO();
        approvalCustomVO.setFlowStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        approvalCustomVO.setPreCreateTime(beforeFourDateTime.toString());
        approvalCustomVO.setFlowType(FlowTypeEnum.CHATROOM_INVITE.getCode());

        // 查询超过3天 & flow_status状态为0
        List<ApprovalFlow> list = approvalFlowMapper.selectByCustom(approvalCustomVO);
        if (CollectionUtils.isEmpty(list)) {
            log.info(DateUtil.now() + "无超时审批数据");
            return;
        }

        list.forEach( item -> {
            String redisKey = "chatroom_invite:threeDayOverTime:" + item.getId().toString();
            if (StringUtils.isNotEmpty(redisUtil.get(redisKey))) {
                return;
            }

            // 发送小秘书
            String content = item.getOrgName() + "邀请您加入他们的大家庭，收益奖励丰厚诱人，还剩一天过期，请点击查看详情";
            String replaceContent = item.getOrgName() + "邀请您加入他们的大家庭，收益奖励丰厚诱人，还剩一天过期，请点击%s";
            String replaceStr = "查看详情";
            String link = inviteLinkUrl + "?inviteId=" + item.getId();
            generalService.sendLinkSecretary(item.getHostUuid(), content, replaceContent, replaceStr, link);

            log.info("[chatThreeDayOverTime],id:{},hostUuid:{}", item.getId(),item.getHostUuid());
            redisUtil.set(redisKey,"1", expireTime.longValue(), TimeUnit.SECONDS);
        });
    }

    /**
     * 4天超时未审批提醒()
     */
    @SingleTask
    @Scheduled(cron = "0 0 8,10,12,14,16,18,20,22 * * ?")
    public void chatFourDayOverTime() {
        log.info("chatFourDayOverTime,start:{}", DateUtil.now());
        DateTime beforeFourDateTime = DateUtil.offsetDay(new DateTime(), -3);
        ApprovalCustomVO approvalCustomVO = new ApprovalCustomVO();
        approvalCustomVO.setFlowStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        approvalCustomVO.setPreCreateTime(beforeFourDateTime.toString());
        approvalCustomVO.setFlowType(FlowTypeEnum.CHATROOM_INVITE.getCode());

        // 查询超过4天 & flow_status状态为0
        List<ApprovalFlow> list = approvalFlowMapper.selectByCustom(approvalCustomVO);
        if (CollectionUtils.isEmpty(list)) {
            log.info(DateUtil.now() + "无超4时审批数据");
            return;
        }

        list.forEach( item -> {
            String redisKey = "chatroom_invite:fourDayOverTime:" + item.getId().toString();
            if (StringUtils.isNotEmpty(redisUtil.get(redisKey))) {
                return;
            }
            // 修改数据
            ApprovalFlow approvalFlow = new ApprovalFlow();
            BeanUtils.copyProperties(item, approvalFlow);
            approvalFlow.setFlowStatus(InviteStatusEnum.EXPIRE.getCode());
            approvalFlow.setModifyUser(SYS_USER);
            approvalFlowMapper.updateByRecord(approvalFlow);
            ApprovalFlowNode node = new ApprovalFlowNode();
            node.setFlowId(approvalFlow.getId());
            node.setNodeStatus(InviteStatusEnum.REJECT.getCode());
            node.setModifyUser(SYS_USER);
            approvalFlowNodeMapper.updateByRecord(node);
            // 发送小秘书
            String content = item.getOrgName() + "的邀约入会已过期!";
            generalService.sendSecretary(item.getHostUuid(), content);

            log.info("[chatFourDayOverTime],id:{},hostUuid:{}", item.getId(),item.getHostUuid());
            redisUtil.set(redisKey,"1", expireTime.longValue(), TimeUnit.SECONDS);
        });
    }

}
