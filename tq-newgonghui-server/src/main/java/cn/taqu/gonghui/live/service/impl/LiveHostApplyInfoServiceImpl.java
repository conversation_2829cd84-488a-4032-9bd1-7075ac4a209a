package cn.taqu.gonghui.live.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.EncryptUtil;
import cn.taqu.gonghui.common.utils.PageResult;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.live.entity.LiveHostApplyInfo;
import cn.taqu.gonghui.live.entity.LiveHostInfo;
import cn.taqu.gonghui.live.mapper.LiveHostApplyInfoMapper;
import cn.taqu.gonghui.live.mapper.LiveHostInfoMapper;
import cn.taqu.gonghui.live.service.LiveHostApplyInfoService;
import cn.taqu.gonghui.live.vo.CertificationVo;
import cn.taqu.gonghui.live.vo.LivesHostApplyInfoVo;
import cn.taqu.gonghui.soa.CertificationService;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.service.*;
import cn.taqu.gonghui.system.vo.RoleVo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/6/17
 */
@Service
@Slf4j
public class LiveHostApplyInfoServiceImpl implements LiveHostApplyInfoService {

    @Autowired
    private LiveHostApplyInfoMapper liveHostApplyInfoMapper;
    @Autowired
    private HostService hostService;
    @Autowired
    private LiveHostInfoMapper liveHostInfoMapper;
    @SoaReference("account")
    private CertificationService certificationService;
    @Autowired
    private EncryptUtil encryptUtil;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private TeamEmployeeService teamEmployeeService;
    @Autowired
    private TeamService teamService;

    /**
     * 保存/提交"主播申请"
     *
     * @param entity
     * @param operStatus
     */
    @Override
    public void saveHostApply(LiveHostApplyInfo entity, Integer operStatus) {
        //校验参数
        int status;
        switch (operStatus) {//1-提交，2-保存
            case 1:
                status = 3;//待审核
                break;
            case 2:
                status = 2;//未提交
                break;
            default:
                throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "操作状态未定义");
        }
        if (StringUtils.isEmpty(entity.getAccountMobile())) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "他趣账号不能为空");
        }
        if (null == entity.getConsortiaId()) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "请选择一个团队");
        }
        String accountUuid = hostService.getUuidByMobile(entity.getAccountMobile());
        if (StringUtils.isEmpty(accountUuid)) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "他趣账号不存在");
        }

        if(countByNotIdAccountUuidAndStatus(entity.getId(),accountUuid)>0){
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "该账号已存在申请记录，不可重复申请");
        }

        if (Objects.nonNull(getByHostUuid(accountUuid))) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "该账号已是主播，不可重复申请");
        }
        Map<String, Map<String,Object>> certMap = certificationService.getByUuids(accountUuid);
        if(MapUtils.isEmpty(certMap)){
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "请主播先登录APP实名认证");
        }
        Map<String, Object> map = certMap.get(accountUuid);
        CertificationVo certificationVo = new CertificationVo();
        certificationVo.setIsChecked(MapUtils.getInteger(map,"is_checked"));
        certificationVo.setBizNo(MapUtils.getString(map,"biz_no"));
        certificationVo.setIdentityNo(MapUtils.getString(map,"identity_no"));
        certificationVo.setRealName(MapUtils.getString(map,"real_name"));
        certificationVo.setRewardAccount(MapUtils.getString(map,"reward_account"));
        if (Objects.isNull(certificationVo) || !Objects.equals(certificationVo.getIsChecked(), 1)) {
            throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "请主播先登录APP实名认证");
        }
        if (Objects.equals(operStatus, 1)) {
            if (StringUtils.isEmpty(entity.getCardZmUrl()) ||
                    StringUtils.isEmpty(entity.getCardFmUrl()) ||
                    StringUtils.isEmpty(entity.getCardScUrl())) {
                throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "身份证信息填写不完整");
            }
            if (StringUtils.isEmpty(entity.getCoverUrl())) {
                throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "主播封面信息填写不完整");
            }
            if (StringUtils.isEmpty(entity.getVideo())) {
                throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "主播视频不能为空");
            }
        }

        mergeLiveHostApplyInfo(entity, status, accountUuid, certificationVo);
    }



    /**
     * merge到数据库
     */
    public void mergeLiveHostApplyInfo(LiveHostApplyInfo entity, Integer status, String accountUuid, CertificationVo certificationVo) {
        Map<String, String> encryptParam = new HashMap<>();
        encryptParam.put("alipay_account_cipher", certificationVo.getRewardAccount());
        encryptParam.put("account_mobile_cipher", entity.getAccountMobile());
        encryptParam.put("real_name_cipher", certificationVo.getRealName());
        encryptParam.put("id_card_cipher", certificationVo.getIdentityNo());
        Map<String, String> encryptResult = encryptUtil.batchEncrypt(EncryptUtil.LIVE_API, encryptParam);
        Map<String, String> sm3Result = encryptUtil.sm3(encryptParam);
        LiveHostApplyInfo dbEntity;
        if (Objects.isNull(entity.getId())) {
            //第一次填写
            dbEntity = entity;
            dbEntity.setCreateTime(DateUtil.currentTimeSeconds());

        } else {
            //非第一次填写
            dbEntity = Optional
                    .ofNullable(getById(entity.getId()))
                    .orElseThrow(() ->
                            new ServiceException(CodeStatus.DATA_NOT_FOUND_ERROR.value(), CodeStatus.DATA_NOT_FOUND_ERROR.getReasonPhrase()));
            dbEntity.setBusinessmanUuid(entity.getBusinessmanUuid());
            dbEntity.setCardZmUrl(entity.getCardZmUrl());
            dbEntity.setCardFmUrl(entity.getCardFmUrl());
            dbEntity.setCardScUrl(entity.getCardScUrl());
            dbEntity.setCoverUrl(entity.getCoverUrl());
            dbEntity.setWidth(entity.getWidth());
            dbEntity.setHeight(entity.getHeight());
            dbEntity.setVideo(entity.getVideo());
            dbEntity.setUpdateTime(DateUtil.currentTimeSeconds());
        }
        dbEntity.setAccountMobile("");
        dbEntity.setAccountMobileCipher(encryptResult.get("account_mobile_cipher"));
        dbEntity.setAccountMobileSm3(sm3Result.get("account_mobile_cipher"));
        dbEntity.setAlipayAccount("");
        dbEntity.setAlipayAccountCipher(encryptResult.get("alipay_account_cipher"));
        dbEntity.setAlipayAccountSm3(sm3Result.get("alipay_account_cipher"));
        String filed = "account_name";//昵称
        Map<String, Object> o = hostService.getInfoByUuids(new String[]{accountUuid},new String[]{filed}).get(accountUuid);
        String accountName = String.valueOf(o.get(filed));
        dbEntity.setAccountName(accountName);
        dbEntity.setAccountUuid(accountUuid);
        dbEntity.setRealName("");
        dbEntity.setRealNameCipher(encryptResult.get("real_name_cipher"));
        dbEntity.setRealNameSm3(sm3Result.get("real_name_cipher"));
        dbEntity.setIdCard("");
        dbEntity.setIdCardCipher(encryptResult.get("id_card_cipher"));
        dbEntity.setIdCardSm3(sm3Result.get("id_card_cipher"));
        if(StringUtils.isNotBlank(dbEntity.getBusinessmanUuid())){
            TeamEmployee teamEmployee = teamEmployeeService.getOne(Long.valueOf(dbEntity.getBusinessmanUuid()));
            SysUser sysUser = sysUserService.selectUserByUserId(teamEmployee.getUserId());
           if(null != sysUser ){
               dbEntity.setBusinessmanUuid(sysUser.getAccountUuid());
           }else{
               dbEntity.setBusinessmanUuid("");
           }
        }
        dbEntity.setStatus(status);
        dbEntity.setIsDownload(false);
        dbEntity.setApplyFrom(1);
        dbEntity.setAgreementVideo("");
        dbEntity.setConsortiaId(entity.getConsortiaId());
        save(dbEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void save(LiveHostApplyInfo liveHostApplyInfo){
        if(liveHostApplyInfo.getId() != null){
            liveHostApplyInfoMapper.updateByPrimaryKeySelective(liveHostApplyInfo);
        }else{
            liveHostApplyInfoMapper.insertSelective(liveHostApplyInfo);
        }
    }
   private int countByNotIdAccountUuidAndStatus(Long id,String accountUuid){
       return liveHostApplyInfoMapper.countByNotIdAccountUuidAndStatus(id,accountUuid);
    }
   private LiveHostInfo getByHostUuid(String accountUuid){
        return liveHostInfoMapper.selectByHostUuid(accountUuid);
    }
   private LiveHostApplyInfo getById(Long  id){
       return liveHostApplyInfoMapper.selectByPrimaryKey(id);
    }




    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelHostApplyById(Long id) {
        LiveHostApplyInfo livesHostApplyInfo = Optional.ofNullable(getById(id))
                .orElseThrow(() -> new ServiceException(CodeStatus.DATA_NOT_FOUND_ERROR.value(), CodeStatus.DATA_NOT_FOUND_ERROR.getReasonPhrase()));
        if (!Objects.equals(livesHostApplyInfo.getStatus(), 3)) {
            throw new ServiceException(CodeStatus.OPERATION_FAIL_ERROR.value(), "操作失败，该申请无法被撤销");
        }
        int status = 2;//撤销 == 未提交
        livesHostApplyInfo.setStatus(status);
        livesHostApplyInfo.setUpdateTime(DateUtil.currentTimeSeconds());
        liveHostApplyInfoMapper.updateByPrimaryKeySelective(livesHostApplyInfo);
    }

    @Override
    public PageResult<LivesHostApplyInfoVo> getHostApplyList(String nickname, String hostUuid, Integer applyStatus, Integer page, Integer pageSize) {
        applyStatus = apiStatus2Db(applyStatus);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        List<LiveHostApplyInfo> list = null;
        RoleVo roleVo = sysUserService.getCurrentRole(TeamTypeEnum.LIVE_TEAM.getValue());
        if(UserTypeEnum.MANAGER.getCode().equals(roleVo.getRoleKey())){
            List<Team> teams = teamService.selectTeamList(loginUser.getUser().getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            List<Long> teamIdList = teams.stream().map(Team::getTeamId).collect(Collectors.toList());
            PageHelper.startPage(page == null ? 1 : page, pageSize == null ? 15 : pageSize);
            list = liveHostApplyInfoMapper.selectListByParams(teamIdList, null, nickname, hostUuid, applyStatus);
        }else if(UserTypeEnum.LEADER.getCode().equals(roleVo.getRoleKey())){
            List<Long> teamIdList = new ArrayList<>();
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(loginUser.getUser().getUserId(),TeamTypeEnum.LIVE_TEAM.getValue());
            teamIdList.add(teamEmployee.getTeamId());
            PageHelper.startPage(page == null ? 1 : page, pageSize == null ? 15 : pageSize);
            list = liveHostApplyInfoMapper.selectListByParams(teamIdList, null, nickname, hostUuid, applyStatus);
        }else if(UserTypeEnum.AGENTER.getCode().equals(roleVo.getRoleKey())){
            PageHelper.startPage(page == null ? 1 : page, pageSize == null ? 15 : pageSize);
            list = liveHostApplyInfoMapper.selectListByParams(null, loginUser.getUser().getAccountUuid(), nickname, hostUuid, applyStatus);
        }
        PageHelper.clearPage();
        if(CollectionUtils.isEmpty(list)){
            return  new PageResult<LivesHostApplyInfoVo>(0L,Lists.newArrayList());
        }
        List<LivesHostApplyInfoVo> data = Lists.newArrayList();
        list.stream().forEach(item -> data.add(LivesHostApplyInfo2Vo(item)));
        decryptAgentManageList(data);
        Page<LiveHostApplyInfo> pageResult=(Page<LiveHostApplyInfo>) list;
        return new PageResult<LivesHostApplyInfoVo>(pageResult.getTotal(),data) ;
    }



    private LivesHostApplyInfoVo LivesHostApplyInfo2Vo(LiveHostApplyInfo entity) {
        LivesHostApplyInfoVo vo = new LivesHostApplyInfoVo();
        vo.setId(entity.getId());
        vo.setName(entity.getRealName());
        vo.setNickname(entity.getAccountName());
        vo.setHostUuid(entity.getAccountUuid());
        vo.setIdCard(entity.getIdCard());
        vo.setCardZmUrl(entity.getCardZmUrl());
        vo.setCardFmUrl(entity.getCardFmUrl());
        vo.setCardScUrl(entity.getCardScUrl());
        vo.setPhone(entity.getAccountMobile());
        vo.setTqAccount(entity.getAccountMobile());
        vo.setAlipayAccount(entity.getAlipayAccount());
        vo.setCreateTime(entity.getCreateTime());
        vo.setIdCardUrl(new String[]{replaceImgUri(entity.getCardZmUrl()), replaceImgUri(entity.getCardFmUrl()), replaceImgUri(entity.getCardScUrl())});
        vo.setHostCoverUrl(entity.getCoverUrl());
        vo.setWidth(entity.getWidth());
        vo.setHeight(entity.getHeight());
        vo.setHostVideoUrl(entity.getVideo());
        vo.setApplyStatus(dbStatus2Api(entity.getStatus()));
        vo.setRefuseReason(entity.getStatus().intValue() != -1 ? "" : entity.getRefuseReason());
        vo.setAgentUuid(entity.getBusinessmanUuid());

        vo.setAlipayAccountCipher(entity.getAlipayAccountCipher());
        vo.setAccountMobileCipher(entity.getAccountMobileCipher());
        vo.setRealNameCipher(entity.getRealNameCipher());
        vo.setIdCardCipher(entity.getIdCardCipher());
        vo.setConsortiaId(entity.getConsortiaId());
        return vo;
    }

    /**
     * 特殊处理 为/taqu开头的七牛key的图片  返回 taqu
     * 历史原因..我也不知道为什么
     * 大致是因为app申请主播的时候传的是/taqu,服务端会把/taqu变成taqu,所以我们这边也要跟着处理
     *
     * @param imgUri
     * @return
     */
    public String replaceImgUri(String imgUri) {
        if (StringUtils.isBlank(imgUri)) {
            return "";
        }
        return imgUri.indexOf("/taqu") == 0 ? imgUri.substring(1) : imgUri;
    }

    /**
     * "数据库定义的字典项"映射为"接口定义的字典项"
     *
     * @param applyStatus
     * @return
     */
    private Integer dbStatus2Api(Integer applyStatus) {
        switch (applyStatus) {
            case -1://拒绝
                return 2;
            case 1://通过
                return 1;
            case 2://待提交
                return 4;
            case 3://待审核
                return 3;
            default:
                throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "未定义的申请状态");
        }
    }

    /**
     * "接口定义的字典项"映射为"数据库定义的字典项"
     *
     * @param applyStatus
     * @return
     */
    private Integer apiStatus2Db(Integer applyStatus) {
        switch (applyStatus) {
            case 0://全部
                return null;
            case 1://通过
                return 1;
            case 2://拒绝
                return -1;
            case 3://待审核
                return 3;
            case 4://待提交
                return 2;
            default:
                throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "未定义的申请状态");
        }
    }

    public void decryptAgentManageList(List<LivesHostApplyInfoVo> voList) {

        List<Map<String, String>> decryptMapList = new ArrayList<>();
        voList.stream().forEach(livesHostApplyInfo -> {
            Map<String, String> decryptMap = BuildDecryptMap(livesHostApplyInfo);
            decryptMapList.add(decryptMap);
        });
        List<Map<String, String>> resultMapList = encryptUtil.batchDecryptList(EncryptUtil.LIVE_API, decryptMapList);

        for (int i = 0; i < decryptMapList.size(); i++) {
            LivesHostApplyInfoVo livesHostApplyInfo = voList.get(i);
            setDecryptBean(resultMapList.get(i), livesHostApplyInfo);
        }
    }

    /**
     * 从数据库取出加密信息后,构建解密map
     *
     * @param vo
     */
    public Map<String, String> BuildDecryptMap(LivesHostApplyInfoVo vo) {

        Map<String, String> map = new HashMap<>();
        String realNameCipher = vo.getRealNameCipher();
        String idCardCipher = vo.getIdCardCipher();
        String accountMobileCipher = vo.getAccountMobileCipher();
        String alipayAccountCipher = vo.getAlipayAccountCipher();

        if (org.apache.commons.lang3.StringUtils.isNotBlank(realNameCipher)) {
            map.put("real_name_cipher", realNameCipher);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(idCardCipher)) {
            map.put("id_card_cipher", idCardCipher);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(accountMobileCipher)) {
            map.put("account_mobile_cipher", accountMobileCipher);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(alipayAccountCipher)) {
            map.put("alipay_account_cipher", alipayAccountCipher);
        }

        return map;
    }

    /**
     * 解密后的数据设置到LivesHostApplyInfoVo
     *
     * @param decryptMap 解密后的map
     * @param vo
     */
    public void setDecryptBean(Map<String, String> decryptMap, LivesHostApplyInfoVo vo) {
        vo.setName(decryptMap.get("agent_name_cipher"));
        vo.setIdCard(decryptMap.get("id_card_cipher"));
        vo.setPhone(decryptMap.get("account_mobile_cipher"));
        vo.setTqAccount(decryptMap.get("account_mobile_cipher"));
        vo.setAlipayAccount(decryptMap.get("alipay_account_cipher"));
    }


    @Override
    public Map<String, Object> getInfoByMobile(String mobile) {
        if(cn.taqu.gonghui.common.utils.StringUtils.isEmpty(mobile)){
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("nickName", "");
            resultMap.put("zhimaCertification", null);
            return resultMap;
        }
        return hostService.getNicknameByMobile(mobile);

    }


    /**
     * 根据id查询主播申请
     *
     * @param id
     * @return
     */
    @Override
    public LivesHostApplyInfoVo getHostApplyById(Long id) {
        LiveHostApplyInfo livesHostApplyInfo = Optional.ofNullable(liveHostApplyInfoMapper.selectByPrimaryKey(id))
                .orElseThrow(() -> new ServiceException(CodeStatus.DATA_NOT_FOUND_ERROR.value(), CodeStatus.DATA_NOT_FOUND_ERROR.getReasonPhrase()));
        LivesHostApplyInfoVo livesHostApplyInfoVo = LivesHostApplyInfo2Vo(livesHostApplyInfo);

        Map<String, String> paramDecryptMap = BuildDecryptMap(livesHostApplyInfoVo);
        List<Map<String,String>> list = new ArrayList<>();
        list.add(paramDecryptMap);
        List<Map<String, String>> listMap = encryptUtil.batchDecryptList(EncryptUtil.LIVE_API, list);
        if(CollectionUtils.isNotEmpty(listMap)){
            setDecryptBean(listMap.get(0), livesHostApplyInfoVo);
        }
        if(StringUtils.isNotBlank(livesHostApplyInfoVo.getAgentUuid())){
            SysUser sysUser = sysUserService.selectUserByAccountUuid(livesHostApplyInfoVo.getAgentUuid());
            if(sysUser !=null){
                TeamEmployee oneByUserId = teamEmployeeService.getOneByUserIdAndType(sysUser.getUserId(),TeamTypeEnum.LIVE_TEAM.getValue());
                livesHostApplyInfoVo.setBusinessmanUuid(oneByUserId == null?"":String.valueOf(oneByUserId.getEmployeeId()));
            }else{
                livesHostApplyInfoVo.setBusinessmanUuid("");
            }
        }else{
            livesHostApplyInfoVo.setBusinessmanUuid("");
        }
        return livesHostApplyInfoVo;
    }
}
