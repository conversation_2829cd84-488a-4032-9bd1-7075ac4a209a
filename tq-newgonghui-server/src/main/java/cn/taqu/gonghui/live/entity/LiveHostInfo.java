package cn.taqu.gonghui.live.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 主播信息表
 */
@Data
public class LiveHostInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    /**
     * 主播UUID
     */
    private String hostUuid;

    /**
     * 房间封面地址
     */
    private String coverUrl;

    /**
     * 封面图片宽度
     */
    private Short width;

    /**
     * 封面图片高度
     */
    private Short height;

    /**
     * 主播个人介绍
     */
    private String hostIntro;

    /**
     * 房间标题
     */
    private String title;

    /**
     * 直播状态（0删除，1正常，2停止，3禁播，4暂离）
     */
    private Byte liveStatus;

    /**
     * 开播时间
     */
    private Integer liveTime;

    /**
     * 流ID
     */
    private String streamId;

    /**
     * 播放地址
     */
    private String rtmpUrl;

    /**
     * 直播图片地址
     */
    private String liveImgUrl;

    /**
     * 创建时间
     */
    private Integer createTime;

    /**
     * 最后更新时间
     */
    private Integer updateTime;

    /**
     * 最后在线时间
     */
    private Integer lastOnlineTime;

    /**
     * 房间机器人最大值
     */
    private Integer maxNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证
     */
    private String idCard;

    /**
     * 直播号
     */
    private Integer liveNo;

    /**
     * 主题id
     */
    private Integer themeId;

    /**
     * 标签id
     */
    private String tagId;

    private Integer consortiaId;

    /**
     * 关播次数
     */
    private Integer stopTimes;

    /**
     * 警告次数
     */
    private Integer warnTimes;

    /**
     * 审核评级
     */
    private String applyLevel;

    /**
     * 首次开播时间
     */
    private Integer firstLiveTime;

    /**
     * 封面标签ID
     */
    private Integer labelId;

    /**
     * 智能标签
     */
    private String recommendTagId;

    /**
     * 经济人uuid
     */
    private String businessmanUuid;

    /**
     * 真实姓名(加密后数据)
     */
    private String realNameCipher;

    /**
     * 身份证(加密后数据)
     */
    private String idCardCipher;

    /**
     * 真实姓名(加密后数据,用于查询)
     */
    private String realNameSm3;

    /**
     * 身份证(加密后数据,用于查询)
     */
    private String idCardSm3;

    /**
     * PC开播权限
     */
    private Byte canOpenPc;

    /**
     * 省份id
     */
    private String provinceId;

    /**
     * 城市id
     */
    private String cityId;

    /**
     * 是否个人主播 1是 0不是
     */
    private Byte personal;

    /**
     * 处罚次数
     */
    private Integer punishTimes;

    /**
     * 加入公会时间
     */
    private Integer addConsortiaTime;



}
