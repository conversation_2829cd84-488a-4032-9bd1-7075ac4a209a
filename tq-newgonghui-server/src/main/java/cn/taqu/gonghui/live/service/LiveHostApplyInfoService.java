package cn.taqu.gonghui.live.service;

import cn.taqu.gonghui.common.utils.PageResult;
import cn.taqu.gonghui.live.entity.LiveHostApplyInfo;
import cn.taqu.gonghui.live.vo.LivesHostApplyInfoVo;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/6/17
 */
public interface LiveHostApplyInfoService {


    void saveHostApply(LiveHostApplyInfo entity, Integer operStatus);


    void cancelHostApplyById(Long id);

    PageResult<LivesHostApplyInfoVo> getHostApplyList(String nickname, String hostUuid, Integer applyStatus, Integer page, Integer pageSize);

    Map<String,Object> getInfoByMobile(String mobile);

    LivesHostApplyInfoVo getHostApplyById(Long id);
}
