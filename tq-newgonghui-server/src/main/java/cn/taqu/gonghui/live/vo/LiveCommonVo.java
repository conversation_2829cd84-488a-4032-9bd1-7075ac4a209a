package cn.taqu.gonghui.live.vo;

import lombok.Data;

@Data
public class LiveCommonVo {

    /**
     * 日期
     */
    private Long dayTime;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 主播数量
     */
    private Integer hostNum;

    /**
     * 收益
     */
    private Integer amount;
    /**
     * 新主播数量
     */
    private Integer newHostNum;

    /**
     * 新收益
     */
    private Integer newAmount;
    /**
     * 鲜花数量
     */
    private Integer flower;

    /**
     * 直播时长
     */
    private Integer totalLiveTime;

    /**
     * 观看人数
     */
    private Integer viewer;

    /**
     * 送礼人数
     */
    private Integer send;

    /**
     * 消息数量
     */
    private Integer message;

    /**
     * 粉丝人数
     */
    private Integer fans;
    /**
     * 机构数量
     */
    private Integer orgNum;
    /**
     * 机构收益
     */
    private Integer orgAmount;
    /**
     * 新增机构数量
     */
    private Integer newOrgNum;
    /**
     * 新增机构收益
     */
    private Integer newOrgAmount;
    /**
     * top200主播数量
     */
    private Integer topNum;
    /**
     * top 200主播收益
     */
    private Integer topAmount;
    /**
     * 运营人员名称
     */
    private String operatorName;

}
