package cn.taqu.gonghui.live.vo.multilive.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 53
 * 多人娱乐主播数据
 */

@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
//fillBackgroundColor = 9,
//,fillForegroundColor = 9
)
@Data
public class MultiLiveHostDataDto implements Serializable {

    /**
     * 日期 '2024-01-01'
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
    @JsonProperty("time")
    private String time;

    /**
     * （主播）昵称
     */
    @ColumnWidth(20)
    @ExcelProperty("主播昵称")
    @JsonProperty("host_name")
    private String hostName;

    /**
     * 主播uuid
     */
    @ColumnWidth(20)
    @ExcelProperty("主播uuid")
    @JsonProperty("host_uuid")
    private String hostUuid;

    /**
     * 团队id
     */
    @ColumnWidth(20)
    @ExcelProperty("团队Id")
    @ExcelIgnore
    @JsonProperty("consortia_id")
    private Long consortiaId;

    /**
     * （主播所属）机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("所属机构名称")
    @JsonProperty("organ_name")
    private String organName;

    /**
     * 开(房)播总时长（小时）
     */
    @ColumnWidth(20)
    @ExcelProperty("开房总时长（小时）")
    @JsonProperty("total_live_duration")
    private String totalLiveDuration;

    /**
     * 开(房)播有效天数
     */
    @ColumnWidth(20)
    @ExcelProperty("开房播有效天数")
    @JsonProperty("valid_live_days")
    private String validLiveDays;

    /**
     * 房间有效主播数量
     */
    @ColumnWidth(20)
    @ExcelProperty("房间有效主播数量")
    @JsonProperty("valid_host_num")
    private String validHostNum;

    /**
     * 上麦总时长（小时）
     */
    @ColumnWidth(20)
    @ExcelProperty("上麦总时长（小时）")
    @JsonProperty("total_up_meet_duration")
    private String totalUpMeetDuration;

    /**
     * 上麦有效天数
     */
    @ColumnWidth(20)
    @ExcelProperty("上麦有效天数")
    @JsonProperty("valid_up_meet_days")
    private String  validUpMeetDays;

    /**
     * 礼物总收益
     */
    @ColumnWidth(20)
    @ExcelProperty("礼物总收益")
    @JsonProperty("total_amount")
    private String totalAmount;

    /**
     * 房主总收益（房主礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("房主礼物收益")
    @JsonProperty("room_amount")
    private String roomAmount;

    /**
     * 房间分成收益（归属房主抽成礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("归属房主抽成礼物收益")
    @JsonProperty("room_split_amount")
    private String roomSplitAmount;

    /**
     * 连麦收益（连麦礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("连麦礼物收益")
    @JsonProperty("meet_amount")
    private String meetAmount;

    /**
     * 绑定娱乐房收益（在绑定娱乐房收到的礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("在绑定娱乐房收到的礼物收益")
    @JsonProperty("bind_room_amount")
    private String bindRoomAmount;

    /**
     * 本公会总收益（在本公会收到礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("在本公会收到礼物收益")
    @JsonProperty("consortia_amount")
    private String consortiaAmount;

    /**
     * 其他公会收益（在其他公会房收到礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("在其他公会房收到礼物收益")
    @JsonProperty("other_consortia_amount")
    private String otherConsortiaAmount;

    @ColumnWidth(20)
    @JsonProperty("has_priv")
    private Integer hasPriv;

    @JsonProperty("total_score")
    private String totalScore;

    @JsonProperty("shell_amount")
    private String shellAmount;

    @JsonProperty("shell_ratio")
    private String shellRatio;

    @JsonProperty("room_total_score")
    private String roomTotalScore;

    @JsonProperty("room_shell_amount")
    private String roomShellAmount;

    @JsonProperty("meet_total_score")
    private String meetTotalScore;

    @JsonProperty("meet_shell_amount")
    private String meetShellAmount;

}
