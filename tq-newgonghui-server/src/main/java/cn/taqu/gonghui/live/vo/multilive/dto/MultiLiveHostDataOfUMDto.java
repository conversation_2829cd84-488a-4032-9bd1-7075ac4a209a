package cn.taqu.gonghui.live.vo.multilive.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/23 19 52
 * 用户管理端的多人娱乐主播数据 （UM=User Manage）
 */
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
//fillBackgroundColor = 9,
//,fillForegroundColor = 9
)
@Data
public class MultiLiveHostDataOfUMDto implements Serializable {

    /**
     * 日期
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
    @JsonProperty("time")
    private  String time;

    /**
     * 主播uuid
     */
    @ColumnWidth(20)
    @ExcelProperty("主播uuid")
    @JsonProperty("host_uuid")
    private String hostUuid;

    /**
     * 主播名字
     */
    @ColumnWidth(20)
    @ExcelProperty("主播名字")
    @JsonProperty("host_name")
    private String hostName;

    /**
     * 团队id
     */
    @ColumnWidth(20)
    @ExcelProperty("团队id")
    @JsonProperty("consortia_id")
    private Long consortiaId;

    /**
     * （主播所属）机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("（主播所属）机构名称")
    @JsonProperty("organ_name")
    private String organName;


    /**
     * 开播总时长(小时)
     */
    @ColumnWidth(20)
    @ExcelProperty("开播总时长(小时)")
    @JsonProperty("total_live_duration")
    private String totalLiveDuration;

    /**
     * 开播有效天数
     */
    @ColumnWidth(20)
    @ExcelProperty("开播有效天数")
    @JsonProperty("valid_live_days")
    private String validLiveDays;


    /**
     * 上（连）麦总时长（小时）
     */
    @ColumnWidth(20)
    @ExcelProperty("上（连）麦总时长（小时）")
    @JsonProperty("total_up_meet_duration")
    private String totalUpMeetDuration;

    /**
     * 上（连）麦有效天数
     */
    @ColumnWidth(20)
    @ExcelProperty("上（连）麦有效天数")
    @JsonProperty("valid_up_meet_days")
    private String validUpMeetDays;



    /**
     * 礼物总收益
     */
    @ColumnWidth(20)
    @ExcelProperty("礼物总收益")
    @JsonProperty("total_amount")
    private String totalAmount;

    /**
     * 房主总收益
     */
    @ColumnWidth(20)
    @ExcelProperty("房主总收益")
    @JsonProperty("room_amount")
    private String roomAmount;

    /**
     * 连麦收益
     */
    @ColumnWidth(20)
    @ExcelProperty("连麦收益")
    @JsonProperty("meet_amount")
    private String meetAmount;

    /**
     * 绑定娱乐房（收到的礼物）收益
     */
    @ColumnWidth(20)
    @ExcelProperty("绑定娱乐房（收到的礼物）收益")
    @JsonProperty("bind_room_amount")
    private String bindRoomAmount;

    /**
     * 在本公会（收到礼物）总收益
     */
    @ColumnWidth(20)
    @ExcelProperty("在本公会（收到礼物）总收益")
    @JsonProperty("consortia_amount")
    private String consortiaAmount;

    /**
     * 在其他公会（收到礼物）收益
     */
    @ColumnWidth(20)
    @ExcelProperty("在其他公会（收到礼物）收益")
    @JsonProperty("other_consortia_amount")
    private String otherConsortiaAmount;

    @JsonProperty("room_total_score")
    private String roomTotalScore;

    @JsonProperty("room_shell_amount")
    private String roomShellAmount;

    @JsonProperty("room_shell_ratio")
    private String roomShellRatio;

    @JsonProperty("meet_total_score")
    private String meetTotalScore;

    @JsonProperty("meet_shell_amount")
    private String meetShellAmount;

}
