package cn.taqu.gonghui.live.mapper;


import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.search.OrgStatisticsSearch;
import cn.taqu.gonghui.system.entity.LiveConsortiaStatisticsDay;
import cn.taqu.gonghui.system.vo.DailyStatisticsVo;

import java.util.List;


public interface LiveConsortiaStatisticsDayMapper {

    @TargetDataSource("livedb")
    List<DailyStatisticsVo> queryByCondition(OrgStatisticsSearch search);

    @TargetDataSource("livedb")
    List<DailyStatisticsVo> queryByCondition2(OrgStatisticsSearch search);
}
