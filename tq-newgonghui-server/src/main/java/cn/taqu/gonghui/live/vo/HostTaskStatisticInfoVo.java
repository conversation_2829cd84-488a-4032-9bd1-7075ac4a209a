package cn.taqu.gonghui.live.vo;

import cn.taqu.gonghui.soa.dto.HostTaskStatisticInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 主播成长任务统计信息
 * <AUTHOR>
 * @date 2024/2/18 17 34
 * discription
 */

@Data
public class HostTaskStatisticInfoVo implements Serializable {

    /**
     * 统计月份
     */
    private String  date;

    /**
     * 月份时间戳
     */
    private String  month;

    /**
     * 主播uuid
     */
    @JsonProperty("host_uuid")
    private String hostUuid;

    /**
     * 主播昵称
     */
    private String nickname;


    /**
     * 累计获得星星数
     */
    @JsonProperty("star_num")
    private String starNum;

    /**
     * 任务id
     */
    @JsonProperty("task_id")
    private String taskId;

    /**
     * 任务标题
     */
    @JsonProperty("task_title")
    private String taskTitle;

    /**
     * 月有效天数
     */
    @JsonProperty("effective_day")
    private String  effectiveDay;


    /**
     * 月有效天完成奖励说明
     */
    @JsonProperty("month_desc")
    private String monthDesc;


    /**
     * 是否完成有效月 0未完成 1已完成
     */
    @JsonProperty("is_finish")
    private String isFinish;

    /**
     * 折叠列表
     */
    private List<HostTaskStatisticInfo> children;

}
