package cn.taqu.gonghui.live.mapper;


import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.LiveHostInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * LiveHostInfoDAO继承基类
 */

public interface LiveHostInfoMapper  {

    @TargetDataSource("livedb")
    LiveHostInfo selectByPrimaryKey(Integer id);

    @TargetDataSource("livedb")
    int insertSelective(LiveHostInfo liveHostInfo);

    @TargetDataSource("livedb")
    int updateByPrimaryKeySelective(LiveHostInfo liveHostInfo);

    @TargetDataSource("livedb")
    LiveHostInfo selectByHostUuid(String hostUuid);

    @TargetDataSource("livedb")
    LiveHostInfo selectByLiveNo(String liveNo);

    @TargetDataSource("livedb")
    List<LiveHostInfo> selectByHostUuidList(List<String> hostUuidList);

    @TargetDataSource("livedb")
    List<LiveHostInfo> getHostListByBusinessUuid(String businessUuid);

    @TargetDataSource("livedb")
    List<LiveHostInfo> getHostListNoBusinessUuid(Integer consortiaId);

    @TargetDataSource("livedb")
    List<LiveHostInfo> getHostListByConsortiaId(Integer consortiaId);

    @TargetDataSource("livedb")
    List<LiveHostInfo> getHostListByConsortiaIdAndId(@Param("consortiaId") Integer consortiaId, @Param("idStart")Integer idStart, @Param("idEnd")Integer idEnd);
}
