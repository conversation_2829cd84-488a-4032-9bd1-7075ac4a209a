package cn.taqu.gonghui.live.vo.multilive;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 53
 * 多人娱乐主播数据
 */

@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
//fillBackgroundColor = 9,
//,fillForegroundColor = 9
)
@Data
public class MultiLiveRoomDataVo implements Serializable {

    /**
     * 日期 '2024-01-01'
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
//    @JsonProperty("dt")
    private String dt;

    /**
     * 房主昵称
     */
    @ColumnWidth(20)
    @ExcelProperty("房主昵称")
//    @JsonProperty("multi_live_name")
    private String multiLiveName;

    /**
     * 房主uuid
     */
    @ColumnWidth(20)
    @ExcelProperty("房主uuid")
//    @JsonProperty("multi_live_uuid")
    private String multiLiveUuid;

    /**
     * 机构id
     */
//    @JsonProperty("consortia_id")
    @ExcelIgnore
    private Long consortiaId;

    /**
     * 房主所属机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("所属机构")
//    @JsonProperty("consortia_name")
    private String consortiaName;

    /**
     * 开房有效天
     */
    @ColumnWidth(20)
    @ExcelProperty("开房有效天")
//    @JsonProperty("is_active_multi_live")
    private String isActiveMultiLive;

    /**
     * 开房时长（小时）
     */
    @ColumnWidth(20)
    @ExcelProperty("开房时长（分钟）")
//    @JsonProperty("multi_live_duration_h")
    private String multiLiveDurationH;

    @ColumnWidth(20)
    @ExcelProperty("房间总分值")
    private String multiLiveTotalAmt1d;

    /**
     * 房间收礼总流水
     */
    @ColumnWidth(20)
    @ExcelProperty("趣币总分值")
    private String multiLiveReceiveAmt1d;

    @ColumnWidth(20)
    @ExcelProperty("贝壳总分值")
    private String multiLiveShellAmt1d;

    @ColumnWidth(20)
    @ExcelProperty("贝壳分值占比")
    private String multiLiveShellRatio;

    /**
     * 有效主播数量
     */
    @ColumnWidth(20)
    @ExcelProperty("有效主播数量")
//    @JsonProperty("multi_live_active_host_cnt_1d")
    private String multiLiveActiveHostCnt1d;

//    /**
//     * 房间曝光人数
//     */
//    @ColumnWidth(20)
//    @ExcelProperty("房间曝光人数")
////    @JsonProperty("multi_live_exposure_cnt_1d")
//    private String  multiLiveExposureCnt1d;

    /**
     * 房间在大厅曝光人数
     */
    @ColumnWidth(20)
    @ExcelProperty("房间大厅曝光")
//    @JsonProperty("multi_live_hall_exposure_cnt_1d")
    private String multiLiveHallExposureCnt1d;

    /**
     * 进入房间的人数
     */
    @ColumnWidth(20)
    @ExcelProperty("DAU")
//    @JsonProperty("multi_live_active_cnt_1d")
    private String multiLiveActiveCnt1d;

    /**
     * 房间上麦数
     */
    @ColumnWidth(20)
    @ExcelProperty("上麦数")
//    @JsonProperty("multi_live_meeting_cnt_1d")
    private String multiLiveMeetingCnt1d;

    /**
     * 房间付费人数
     */
    @ColumnWidth(20)
    @ExcelProperty("付费人数")
//    @JsonProperty("multi_live_consume_cnt_1d")
    private String multiLiveConsumeCnt1d;

    /**
     * 房间付费率
     */
    @ColumnWidth(20)
    @ExcelProperty("付费率")
//    @JsonProperty("multi_live_consume_ratio")
    private String multiLiveConsumeRatio;

    /**
     * 房间次日留存率
     */
    @ColumnWidth(20)
    @ExcelProperty("次日留存率")
//    @JsonProperty("multi_live_retention_ratio_2d")
    private String multiLiveRetentionRatio2d;

    /**
     * 房间新用户dau
     */
    @ColumnWidth(20)
    @ExcelProperty("新用户dau")
    private String multiLiveNewActiveCnt1d;




}
