package cn.taqu.gonghui.live.vo.multilive;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/23 19 52
 * 用户管理端的多人娱乐主播数据 （UM=User Manage）
 */

@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
public class MultiLiveRoomDataOfUMVo implements Serializable {

    /**
     * 日期
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
    private String time;

    /**
     * 房主名字
     */
    @ColumnWidth(20)
    @ExcelProperty("房主昵称")
    private String roomName;

    /**
     * 房间uuid
     */
    @ColumnWidth(20)
    @ExcelProperty("房主uuid")
    private String roomUuid;


    /**
     * 团队id
     */
    @ExcelIgnore
    private Long consortiaId;


    /**
     * （主播所属）机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("所属机构")
    private String organName;


    /**
     * 开播时间段
     */
    @ColumnWidth(20)
    @ExcelProperty("开播时间段")
    private String liveTimeInfo;

    /**
     * 开播有效天数
     */
    @ColumnWidth(20)
    @ExcelProperty("开房有效天")
    private String validLiveDays;


    /**
     * 开播总时长
     */
    @ColumnWidth(20)
    @ExcelProperty("开房时长（分钟）")
    private String totalLiveDuration;

    @ColumnWidth(20)
    @ExcelProperty("房主总分值")
    private String roomTotalScore;


    /**
     * 房间总收益
     */
    @ColumnWidth(20)
    @ExcelProperty("房间趣币分值")
    private String totalAmount;



    @ColumnWidth(20)
    @ExcelProperty("房间贝壳值")
    private String roomShellAmount;

    @ColumnWidth(20)
    @ExcelProperty("房间贝壳占比")
    private String roomShellRatio;

    /**
     * 有效主播数量
     */
    @ColumnWidth(20)
    @ExcelProperty("有效主播数量")
    private String validHostNum;


}
