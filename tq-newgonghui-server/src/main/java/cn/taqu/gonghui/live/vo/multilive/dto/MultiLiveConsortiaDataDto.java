package cn.taqu.gonghui.live.vo.multilive.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 53
 * 多人娱乐公会数据
 */

@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
public class MultiLiveConsortiaDataDto implements Serializable {

    /**
     * 日期
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
    @JsonProperty("time")
    private String time;

    /**
     * 公会id
     */
    @ColumnWidth(20)
    @ExcelProperty("公会Id")
    @JsonProperty("consortia_id")
    private Long consortiaId;

    /**
     * （主播所属）机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("机构名称")
    @JsonProperty("organ_name")
    private String organName;

    /**
     * 公会收礼总流水
     */
    @ColumnWidth(20)
    @ExcelProperty("公会收礼总流水")
    @JsonProperty("total_amount")
    private String totalAmount;

    /**
     * 本公会娱乐房收礼总流水
     */
    @ColumnWidth(20)
    @ExcelProperty("本公会娱乐房收礼总流水")
    @JsonProperty("consortia_amount")
    private String consortiaAmount;

    /**
     * 公会有效娱乐房数量
     */
    @ColumnWidth(20)
    @ExcelProperty("公会有效娱乐房数量")
    @JsonProperty("valid_room_num")
    private String  validRoomNum;

    /**
     * 公会有效娱乐主播数量
     */
    @ColumnWidth(20)
    @ExcelProperty("公会有效娱乐主播数量")
    @JsonProperty("valid_host_num")
    private String validHostNum;

    /**
     * 上麦有效天数
     */
    @ColumnWidth(20)
    @ExcelProperty("上麦有效天数")
    @JsonProperty("valid_up_meet_days")
    private String validUpMeetDays;

    /**
     * 开播主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("开播主播人数")
    @JsonProperty("live_host_num")
    private String liveHostNum;

    /**
     * 新开播主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("新开播主播人数")
    @JsonProperty("new_live_host_num")
    private String newLiveHostNum;

    /**
     * 上麦主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("上麦主播人数")
    @JsonProperty("meet_host_num")
    private String meetHostNum;

    /**
     * 新上麦主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("新上麦主播人数")
    @JsonProperty("new_meet_host_num")
    private String  newMeetHostNum;

    /**
     * 收礼主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("收礼主播人数")
    @JsonProperty("receive_host_num")
    private String receiveHostNum;

    /**
     * 新收礼主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("新收礼主播人数")
    @JsonProperty("new_receive_host_num")
    private String newReceiveHostNum;


    /**
     * 消费人数
     */
    @ColumnWidth(20)
    @ExcelProperty("消费人数")
    @JsonProperty("cost_num")
    private String costNum;

    /**
     * 本公会个播流水
     */
    @ColumnWidth(20)
    @ExcelProperty("本公会个播流水")
    @JsonProperty("single_amount")
    private String singleAmount;

    @JsonProperty("total_score")
    private String totalScore;

    @JsonProperty("shell_amount")
    private String shellAmount;

    @JsonProperty("shell_ratio")
    private String shellRatio;

    @JsonProperty("consortia_total_score")
    private String consortiaTotalScore;

    @JsonProperty("single_total_score")
    private String singleTotalScore;

    @JsonProperty("single_shell_amount")
    private String singleShellAmount;

    @JsonProperty("single_shell_ratio")
    private String singleShellRatio;
}
