package cn.taqu.gonghui.live.search;

import lombok.Data;

import java.util.List;

@Data
public class OrgStatisticsSearch {

    /**
     * 机构uuid
     */
    private String consortiaId;

    private List<Long> consortiaIdList;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 经纪人uuid
     */
    private String agentUuid;

    /**
     * 经纪人id
     */
    private Long agentId;

    /**
     * 统计开始时间
     */
    private Long startTime;

    /**
     * 统计结束时间
     */
    private Long endTime;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 是否导出
     * 1-是，0-否
     */
    private Integer export;
    /**
     * 运营人员名称
     */
    private String businessPerson;
}
