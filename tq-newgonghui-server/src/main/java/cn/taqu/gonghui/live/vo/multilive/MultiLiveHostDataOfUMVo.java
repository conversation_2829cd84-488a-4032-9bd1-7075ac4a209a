package cn.taqu.gonghui.live.vo.multilive;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/23 19 52
 * 用户管理端的多人娱乐主播数据 （UM=User Manage）
 */
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
//fillBackgroundColor = 9,
//,fillForegroundColor = 9
)
@Data
public class MultiLiveHostDataOfUMVo implements Serializable {

    /**
     * 日期
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
//    @JsonProperty("time")
    private  String time;

    /**
     * 主播uuid
     */
    @ColumnWidth(20)
    @ExcelProperty("主播uuid")
//    @JsonProperty("host_uuid")
    private String hostUuid;

    /**
     * 主播名字
     */
    @ColumnWidth(20)
    @ExcelProperty("主播昵称")
//    @JsonProperty("nickname")
    private String hostName;

    /**
     * 团队id
     */
    @ColumnWidth(20)
    @ExcelProperty("团队id")
    @ExcelIgnore
    private Long consortiaId;

    /**
     * （主播所属）机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("所属机构")
//    @JsonProperty("organ_name")
    private String organName;

    @ColumnWidth(20)
    @ExcelProperty("开房有效天")
    private String validLiveDays;


    /**
     * 开播总时长(小时)
     */
    @ColumnWidth(20)
    @ExcelProperty("开房时长(分钟)")
//    @JsonProperty("total_live_duration")
    private String totalLiveDuration;

    /**
     * 上（连）麦有效天数
     */
    @ColumnWidth(20)
    @ExcelProperty("连麦有效天")
//    @JsonProperty("valid_up_meet_days")
    private String validUpMeetDays;


    /**
     * 上（连）麦总时长（小时）
     */
    @ColumnWidth(20)
    @ExcelProperty("连麦时长（分钟）")
//    @JsonProperty("total_up_meet_duration")
    private String totalUpMeetDuration;

    /**
     * 礼物总收益
     */
    @ColumnWidth(20)
    @ExcelProperty("礼物总收益")
    private String totalAmount;

    @ColumnWidth(20)
    @ExcelProperty("房主总分值")
    private String roomTotalScore;

    /**
     * 房主总收益
     */
    @ColumnWidth(20)
    @ExcelProperty("房主趣币分值")
    private String roomAmount;

    @ColumnWidth(20)
    @ExcelProperty("房主贝壳分值")
    private String roomShellAmount;

    @ColumnWidth(20)
    @ExcelProperty("房间贝壳占比")
    private String roomShellRatio;

    @ColumnWidth(20)
    @ExcelProperty("连麦总分值")
    private String meetTotalScore;

    /**
     * 连麦收益
     */
    @ColumnWidth(20)
    @ExcelProperty("连麦趣币分值")
    private String meetAmount;

    @ColumnWidth(20)
    @ExcelProperty("连麦贝壳分值")
    private String meetShellAmount;

    /**
     * 在本公会（收到礼物）总收益
     */
    @ColumnWidth(20)
    @ExcelProperty("在本公会收到的趣币收益")
    private String consortiaAmount;

//    /**
//     * 绑定娱乐房（收到的礼物）收益
//     */
//    @ColumnWidth(20)
//    @ExcelProperty("在绑定娱乐房收到的礼物收益")
////    @JsonProperty("bind_room_amount")
//    private String bindRoomAmount;



    /**
     * 在其他公会（收到礼物）收益
     */
    @ColumnWidth(20)
    @ExcelProperty("在其他公会房收到趣币收益")
    private String otherConsortiaAmount;

}
