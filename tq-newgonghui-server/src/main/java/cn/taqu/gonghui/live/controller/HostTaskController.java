package cn.taqu.gonghui.live.controller;

import cn.hutool.core.date.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.chatroom.search.ChatRoomSearch;
import cn.taqu.gonghui.live.param.HostTaskDetailQueryParam;
import cn.taqu.gonghui.live.param.HostTaskSearchParam;
import cn.taqu.gonghui.live.vo.HostTaskStatisticInfoVo;
import cn.taqu.gonghui.soa.HostTaskSoaService;
import cn.taqu.gonghui.soa.dto.HostTaskStatisticInfo;
import cn.taqu.gonghui.system.search.LivePerDayStatisticSearch;
import cn.taqu.gonghui.system.service.HostTaskService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 主播成长任务
 * <AUTHOR>
 * @date 2024/2/18 15 18
 * discription
 */

@RestController
@RequestMapping(value = "/api",params = "service=hostTask")
public class HostTaskController {

    @Resource
    private HostTaskSoaService hostTaskSoaService;

    @Resource
    private HostTaskService hostTaskService;



    /**
     * 用户端-主播成长任务-
     * @return
     */

    @RequestMapping(params = "method=statData")
//    @PreAuthorize("@ss.hasPermi('hostTask:statData')")
    public JsonResult pageStatData(RequestParams params) {
//        Integer page = params.getFormIntegerDefault(0, 1);
//        Integer pageSize = params.getFormIntegerDefault(1, 10);
//        Long monthDate = params.getFormLongDefault(2, getMonthBegin(System.currentTimeMillis()));
//        String hostUuid = params.getFormStringDefault(3, ""); // 主播uuid（有传时按这个为主，没传时，按当前用户所在的公会的主播进行分页处理）

        String paramJson = params.getFormStringOption(0);
        HostTaskSearchParam search = JSON.parseObject(paramJson, new TypeReference<HostTaskSearchParam>() {});
         Integer page = search.getPage();
        Integer pageSize = search.getPageSize();
        Long monthDate = search.getMonthDate()==null ? getMonthBegin(System.currentTimeMillis()): search.getMonthDate();
        String hostUuid = search.getHostUuid(); // 主播uuid（有传时按这个为主，没传时，按当前用户所在的公会的主播进行分页处理）
        IPage<HostTaskStatisticInfoVo> iPage = hostTaskService.pageStatDataBySearch(page, pageSize, monthDate, hostUuid);
        return JsonResult.success(iPage);
    }

    /**
     * 用户端-主播成长任务-
     * @return
     */

    @RequestMapping(params = "method=detailData")
//    @PreAuthorize("@ss.hasPermi('hostTask:detailData')")
    public JsonResult listDetailData(RequestParams params) {
//        String hostUuid = params.getFormStringDefault(0, ""); // 主播uuid
//        Integer taskId = params.getFormIntegerDefault(1, 0); //
//        Long month = params.getFormLongDefault(2, getMonthBegin(System.currentTimeMillis()));
        String paramJson = params.getFormStringOption(0);
        HostTaskDetailQueryParam queryParam = JSON.parseObject(paramJson, new TypeReference<HostTaskDetailQueryParam>() {});
        HostTaskDetailQueryParam.checkParam(queryParam);
        JSONObject jsonObject = hostTaskService.listDetailData(queryParam.getHostUuid(), queryParam.getTaskId(), queryParam.getMonth());
        return JsonResult.success(jsonObject);
    }


    public static Long getMonthBegin(Long dateTimeMillis) {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date(dateTimeMillis));

        //设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND,0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        // 获取本月第一天的时间戳
        return c.getTimeInMillis() / 1000;
    }
}
