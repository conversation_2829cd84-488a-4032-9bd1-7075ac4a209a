package cn.taqu.gonghui.live.vo;

import cn.taqu.gonghui.live.entity.AgentManage;
import cn.taqu.gonghui.live.entity.Employee;
import cn.taqu.gonghui.live.entity.GuildInfo;
import cn.taqu.gonghui.live.entity.LiveHostInfo;
import cn.taqu.gonghui.live.mapper.GuildInfoMapper;
import lombok.Data;

import java.util.List;
import java.util.Map;


@Data
public class EmployeeAndAgentDto {

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 直播业务类型下经纪人角色roleId
     */
    private Long AgentRoleId;

    /**
     * 直播业务类型下负责人角色roleId
     */
    private Long leaderRoleId;

    /**
     * 直播业务类型下管理员角色roleId
     */
    private Long managerRoleId;

    /**
     * 老公会信息
     */
    private GuildInfo guildInfo;

    /**
     * 经纪人下所有主播
     */
    private Map<String,List<LiveHostInfo>> hostMap;

    /**
     * 无经纪人的主播
     */
    private List<LiveHostInfo> liveHostInfoList;

    /**
     * agent_manage表信息
     */
    private List<AgentManage> agentManageList;

    /**
     * employee表信息
     */
    private List<Employee> employeeList;
}
