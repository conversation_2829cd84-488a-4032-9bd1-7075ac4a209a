package cn.taqu.gonghui.live.entity;

import lombok.Data;

@Data
public class LiveOrgStatisticsMonth {
    /**
     * ID
     */
    private Integer id;
    /**
     * 运营人员名字
     */
    private String operatorName;
    /**
     * 当月时间
     */
    private Long monthTime;
    /**
     * 运营人员手下的机构数目
     */
    private Integer orgNum;
    /**
     * 运营人员手下的机构流水
     */
    private Integer orgFlow;
    /**
     * 运营人员手下的机构直播时常
     */
    private String orgLiveTimeSum;
    /**
     * 运营人员手下的机构新增数目
     */
    private Integer newOrgNum;
    /**
     * 当月新增公会流水
     */
    private Integer newOrgFlow;
    /**
     * 当月新增主播人数
     */
    private Integer newHostNum;
    /**
     * 当月新增主播流水
     */
    private Integer newHostFlow;
    /**
     * 直播时长
     */
    private Integer totalLiveTime;
    /**
     * 当月top主播人数
     */
    private Integer topHostNum;
    /**
     * 当月top主播流水
     */
    private Integer topHostFlow;
    /**
     * 创建时间
     */
    private Long createTime;
}