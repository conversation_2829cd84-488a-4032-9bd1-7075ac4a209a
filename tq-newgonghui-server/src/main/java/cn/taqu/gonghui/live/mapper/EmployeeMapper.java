package cn.taqu.gonghui.live.mapper;

import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.Employee;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/6/17
 */
public interface EmployeeMapper {

    @TargetDataSource("livedb")
    List<Employee> selectAll();

    @TargetDataSource("livedb")
    List<Employee> getEmployeeList(String orgUuid);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @TargetDataSource("livedb")
    Employee selectById(Long id);

    @TargetDataSource("livedb")
    List<Employee> selectByUuidList(List<String> uuidList);

    /**
     * 查询单条数据
     * @param id
     * @return
     */
    Employee selectOne(Long id);

}
