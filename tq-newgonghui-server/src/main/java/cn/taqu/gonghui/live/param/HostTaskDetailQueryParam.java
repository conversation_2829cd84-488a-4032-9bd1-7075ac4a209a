package cn.taqu.gonghui.live.param;

import cn.taqu.core.exception.ServiceException;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/20 15 45
 * 主播成长任务明细查询参数对象
 */
@Data
public class HostTaskDetailQueryParam implements Serializable {

    /**
     * 主播uuid
     */
    @JsonProperty("host_uuid")
    private String hostUuid;

    /**
     * 任务id
     */
    @JsonProperty("task_id")
    private Integer taskId;


    /**
     * 月份时间戳
     */
    private Long month;


    public static void checkParam(HostTaskDetailQueryParam param){
        if(param.getTaskId() == null){
            throw new ServiceException("param_invalid", "任务id为空");
        }
        if(param.getMonth() == null){
            throw new ServiceException("param_invalid", "月份时间戳为空");
        }
        if(param.getHostUuid() == null){
            throw new ServiceException("param_invalid", "主播uuid为空");
        }
    }
}
