package cn.taqu.gonghui.live.vo.multilive;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 53
 * 多人娱乐公会数据
 */

@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
public class MultiLiveConsortiaDataVo implements Serializable {

    /**
     * 日期
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
    private String time;

    /**
     * （主播所属）机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("公会名称")
    private String organName;

    /**
     * 公会id
     */
    @ColumnWidth(20)
    @ExcelProperty("公会Id")
    private Long consortiaId;

    @ColumnWidth(20)
    @ExcelProperty("公会总分值")
    private String totalScore;

    /**
     * 公会趣币总流水
     */
    @ColumnWidth(20)
    @ExcelProperty("趣币总分值")
    private String totalAmount;

    @ColumnWidth(20)
    @ExcelProperty("贝壳总分值")
    private String shellAmount;

    @ColumnWidth(20)
    @ExcelProperty("贝壳分值占比")
    private String shellRatio;

    @ColumnWidth(20)
    @ExcelProperty("在本公会娱乐房总分值")
    private String consortiaTotalScore;

    /**
     * 本公会娱乐房趣币总流水
     */
    @ColumnWidth(20)
    @ExcelProperty("在本公会娱乐房趣币总分值")
    private String consortiaAmount;

    @ColumnWidth(20)
    @ExcelProperty("个播总分值")
    private String singleTotalScore;

    /**
     * 本公会趣币流水
     */
    @ColumnWidth(20)
    @ExcelProperty("个播趣币分值")
    private String singleAmount;

    @ColumnWidth(20)
    @ExcelProperty("个播贝壳分值")
    private String singleShellAmount;

    @ColumnWidth(20)
    @ExcelProperty("个播贝壳分值占比")
    private String singleShellRatio;

    /**
     * 公会有效娱乐房数量
     */
    @ColumnWidth(20)
    @ExcelProperty("公会有效娱乐房数量")
    private String validRoomNum;

    /**
     * 公会有效娱乐主播数量
     */
    @ColumnWidth(20)
    @ExcelProperty("公会有效娱乐主播数量")
    private String validHostNum;

    /**
     * 开播主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("开播主播数")
    private String liveHostNum;

    /**
     * 新开播主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("新开播主播数")
    private String newLiveHostNum;

    /**
     * 上麦主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("上麦主播数")
    private String meetHostNum;

    /**
     * 新上麦主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("新上麦主播数")
    private String newMeetHostNum;

    /**
     * 收礼主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("收礼主播数")
    private String receiveHostNum;

    /**
     * 新收礼主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("新收礼主播人数")
    private String newReceiveHostNum;

    /**
     * 消费人数
     */
    @ColumnWidth(20)
    @ExcelProperty("消费人数")
    private String costNum;

}
