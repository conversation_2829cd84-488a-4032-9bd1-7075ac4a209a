package cn.taqu.gonghui.live.mapper;

import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.LiveTeamStatisticsDay;
import cn.taqu.gonghui.live.search.OrgStatisticsSearch;
import cn.taqu.gonghui.system.vo.DailyStatisticsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LiveTeamStatisticsDayMapper {

    @TargetDataSource("livedb")
    int insert(LiveTeamStatisticsDay record);

    @TargetDataSource("livedb")
    List<DailyStatisticsVo> queryByCondition(OrgStatisticsSearch search);

    @TargetDataSource("livedb")
    void deleteByDayTime(@Param("startTime") Long startTime,@Param("endTime") Long endTime);
}
