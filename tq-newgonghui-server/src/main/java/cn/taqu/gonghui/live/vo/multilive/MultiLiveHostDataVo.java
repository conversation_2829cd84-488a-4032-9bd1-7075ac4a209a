package cn.taqu.gonghui.live.vo.multilive;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 53
 * 多人娱乐主播数据
 */

@HeadFontStyle(fontName = "宋体", fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体", fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
//fillBackgroundColor = 9,
//,fillForegroundColor = 9
)
@Data
public class MultiLiveHostDataVo implements Serializable {


    /**
     * 日期 '2024-01-01'
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
    private String time;

    /**
     * （主播）昵称
     */
    @ColumnWidth(20)
    @ExcelProperty("主播昵称")
    private String hostName;

    /**
     * 主播uuid
     */
    @ColumnWidth(20)
    @ExcelProperty("主播uuid")
    private String hostUuid;

    /**
     * （主播所属）机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("所属机构")
    private String organName;

    /**
     * 开(房)播有效天数
     */
    @ColumnWidth(20)
    @ExcelProperty("开房有效天")
    private String validLiveDays;

    /**
     * 开(房)播总时长（小时）
     */
    @ColumnWidth(20)
    @ExcelProperty("开房时长（分钟）")
    private String totalLiveDuration;

    /**
     * 房间有效主播数量
     */
    @ColumnWidth(20)
    @ExcelProperty("房间内有效主播数量")
    private String validHostNum;

    /**
     * 上麦有效天数
     */
    @ColumnWidth(20)
    @ExcelProperty("连麦有效天")
    private String validUpMeetDays;

    /**
     * 上麦总时长（小时）
     */
    @ColumnWidth(20)
    @ExcelProperty("连麦时长（分钟）")
    private String totalUpMeetDuration;

    @ColumnWidth(20)
    @ExcelProperty("总分值")
    private String totalScore;

    @ColumnWidth(20)
    @ExcelProperty("趣币总分值")
    private String totalAmount;

    @ColumnWidth(20)
    @ExcelProperty("贝壳总分值")
    private String shellAmount;

    @ColumnWidth(20)
    @ExcelProperty("贝壳总分值占比")
    private String shellRatio;

    @ColumnWidth(20)
    @ExcelProperty("房主总分值")
    private String roomTotalScore;

    /**
     * 房主总收益（房主礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("房主趣币分值")
    private String roomAmount;

    @ColumnWidth(20)
    @ExcelProperty("房主贝壳分值")
    private String roomShellAmount;

    /**
     * 房间分成收益（归属房主抽成礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("归属房主抽成礼物收益")
    private String roomSplitAmount;

    @ColumnWidth(20)
    @ExcelProperty("连麦总分值")
    private String meetTotalScore;

    /**
     * 连麦收益（连麦礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("连麦趣币分值")
    private String meetAmount;

    @ColumnWidth(20)
    @ExcelProperty("连麦贝壳分值")
    private String meetShellAmount;

    /**
     * 本公会总收益（在本公会收到礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("在本公会收到的趣币收益")
    private String consortiaAmount;

    /**
     * 其他公会收益（在其他公会房收到礼物收益）
     */
    @ColumnWidth(20)
    @ExcelProperty("在其他公会房收到的趣币收益")
    private String otherConsortiaAmount;

    @ColumnWidth(20)
    @ExcelProperty("多人娱乐权限")
    private Integer hasPriv;

    /**
     * 团队id
     */
    @ExcelIgnore
    private Long consortiaId;

}
