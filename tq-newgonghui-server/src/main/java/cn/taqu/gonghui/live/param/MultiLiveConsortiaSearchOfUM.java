package cn.taqu.gonghui.live.param;

import cn.hutool.core.date.DateUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.chatroom.vo.req.BasePageReqV2;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 24
 * 多人娱乐-公会数据搜索（用户端使用）
 */
@Data
public class MultiLiveConsortiaSearchOfUM extends BasePageReqV2 implements Serializable {


    /**
     * 开始时间（时间戳）
     */
    private Long startTime;

    /**
     * 结束时间（时间戳）
     */
    private Long endTime;

    /**
     * 所属团队id
     */
    private Long teamId;

    /**
     * 团队id列表
     */
    private List<Long> teamIdList;

    /**
     * 是否导出  0-否 1-是
     */
    private Integer export = 0;


    public String getStartTimeFormat(){
        if(this.startTime ==null){
            throw new ServiceException("startTime_required", "起始时间为空");
//            return "";
        }
        return DateUtil.format(new Date(this.startTime),"yyyyMMdd");
    }

    public String getEndTimeFormat(){
        if(this.endTime ==null){
            throw new ServiceException("endTime_required", "结束时间为空");
//            return "";
        }
        return DateUtil.format(new Date(this.endTime),"yyyyMMdd");
    }
}
