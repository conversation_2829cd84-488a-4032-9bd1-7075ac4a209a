package cn.taqu.gonghui.live.vo;

import cn.taqu.gonghui.live.entity.AgentManage;
import cn.taqu.gonghui.live.entity.Employee;
import cn.taqu.gonghui.live.entity.GuildInfo;
import cn.taqu.gonghui.live.entity.LiveHostInfo;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class MoveDto {

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 老公会信息
     */
    private GuildInfo guildInfo;

    /**
     * 公会下经纪人
     */
    private List<AgentManage> agentManages;

    /**
     * 经纪人下主播
     */
    private Map<String,List<LiveHostInfo>> hasAgentMap;

    /**
     * 无经纪人主播（经纪人为空 和 经纪人所属公会与主播所属公会不一致）
     */
    private List<LiveHostInfo> noAgentList;

    /**
     * 老公会下职员列表
     */
    private List<Employee> employeeList;

    /**
     * 经纪人角色roleId
     */
    private Long agentRoleId;

    /**
     * 负责人角色roleId
     */
    private Long leaderRoleId;

    /**
     * 管理员角色roleId
     */
    private Long managerRoleId;
}
