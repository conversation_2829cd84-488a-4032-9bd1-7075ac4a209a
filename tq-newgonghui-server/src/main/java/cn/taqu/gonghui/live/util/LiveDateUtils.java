package cn.taqu.gonghui.live.util;


import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

@Slf4j
public class LiveDateUtils {

    private static final SimpleDateFormat SHORT_YEAR_FORMATOR = new SimpleDateFormat("yyyyMMdd");

    private static Logger logger = LoggerFactory.getLogger(LiveDateUtils.class);

    /**
     * 获取前一天的日期
     * 返回格式：20210601
     *
     * @return
     */
    public static Long getBeforeDay() {
        //获取的是系统当前时间
        Calendar calendar = Calendar.getInstance();
        //得到前一天
        calendar.add(Calendar.DATE, -1);
        String yesterdayDate = new SimpleDateFormat("yyyyMMdd").format(calendar.getTime());
        return Long.valueOf(yesterdayDate);
    }

    /**
     * 获取后一天日期（yyyyMMdd）
     *
     * @param dayTime
     * @return
     */
    public static Long getAfterDay(String dayTime) {
        //设置日期
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(SHORT_YEAR_FORMATOR.parse(dayTime));
            //得到前一天
            calendar.add(Calendar.DATE, 1);
        } catch (Exception e) {
            logger.error("[LiveDateUtils.getAfterDay]获取后一天日期失败,失败原因：{}", e);
        }
        String tomorrowDate = new SimpleDateFormat("yyyyMMdd").format(calendar.getTime());
        return Long.valueOf(tomorrowDate);
    }

    /**
     * 获取当月日期
     * 返回格式：202106
     *
     * @return
     */
    public static Long getMonth() {
        //获取的是系统当前时间
        Calendar calendar = Calendar.getInstance();
        //得到前一天
        calendar.add(Calendar.MONTH, -1);
        String montDate = new SimpleDateFormat("yyyyMM").format(calendar.getTime());
        return Long.valueOf(montDate);
    }

    /**
     * 获取当月开始时间戳
     *
     * @param timeStamp 毫秒级时间戳
     * @param timeZone  如 GMT+8:00
     * @return
     */
    public static Long getMonthStartTime(Long timeStamp, String timeZone) {
        Calendar calendar = Calendar.getInstance();// 获取当前日期
        calendar.setTimeZone(TimeZone.getTimeZone(timeZone));
        calendar.setTimeInMillis(timeStamp);
        calendar.add(Calendar.YEAR, 0);
        calendar.add(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);// 设置为1号,当前日期既为本月第一天
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    /**
     * 获取当月的结束时间戳
     *
     * @param timeStamp 毫秒级时间戳
     * @param timeZone  如 GMT+8:00
     * @return
     */
    public static Long getMonthEndTime(Long timeStamp, String timeZone) {
        Calendar calendar = Calendar.getInstance();// 获取当前日期
        calendar.setTimeZone(TimeZone.getTimeZone(timeZone));
        calendar.setTimeInMillis(timeStamp);
        calendar.add(Calendar.YEAR, 0);
        calendar.add(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));// 获取当前月最后一天
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTimeInMillis();
    }


    /**
     * 获取当前月1号00：00：00秒数
     *
     * @return
     */
    public static Long getFirstTimeOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime().getTime() / 1000;
    }

    /**
     * 获取下个月1号00：00：00秒数
     *
     * @return
     */
    public static Long getFirstTimeOfNextMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.MONTH, 1);
        return calendar.getTime().getTime() / 1000;
    }

    /**
     * 获取上个月1号00：00：00秒数
     *
     * @return
     */
    public static Long getFirstTimeOfBeforeMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.MONTH, -1);
        return calendar.getTime().getTime() / 1000;
    }


    // 工会新需求

    /**
     * 获取指定日期所在月份开始的时间戳
     *
     * @param date 指定日期
     * @return
     */
    public static Long getMonthBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        // 获取本月第一天的时间戳
        return c.getTimeInMillis();
    }

    /**
     * 获取指定日期所在月份开始的时间戳
     *
     * @param date 指定日期
     * @return
     */
    public static Long getMonthBeginByStr(String date) throws ParseException {

        return  LiveDateUtils.getMonthBegin(LiveDateUtils.dateToStamp(date));
    }

    /**
     * 获取指定日期所在月份开始的时间戳
     *
     * @param date 指定日期
     * @return
     */
    public static Long getMonthEndByStr(String date) throws ParseException {

        return  LiveDateUtils.getMonthEnd(LiveDateUtils.dateToStamp(date));
    }

    /**
     * 获取指定日期所在月份结束的时间戳
     *
     * @param date 指定日期
     * @return
     */
    public static Long getMonthEnd(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为当月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND, 59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 999);
        // 获取本月最后一天的时间戳
        return c.getTimeInMillis();
    }

    /**
     * 获取当月
     * 返回格式：202106
     *
     * @return
     */
    public static Long getCurrentMonth(Date date) {
        //获取的是系统当前时间
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        String yesterdayDate = new SimpleDateFormat("yyyyMM").format(c.getTime());
        return Long.valueOf(yesterdayDate);
    }

    /**
     * 时间转换成时间戳,参数和返回值都是字符串
     *
     * @param s
     * @return res
     * @throws
     */
    public static Date dateToStamp(String s) throws ParseException {
        String res;
        //设置时间模版
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMM");
        Date date = simpleDateFormat.parse(s);

        return date;
    }



    public static Date getBeforeDay(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterday = c.getTime();//昨天
        return yesterday;
    }

    /**
     * 时间转换成时间戳,参数和返回值都是字符串
     *
     * @param s
     * @return res
     * @throws
     */
    public static Date strToDate(String s) throws ParseException {
        String res;
        //设置时间模版
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        Date date = simpleDateFormat.parse(s);

        return date;
    }

    public static void main(String[] args) throws ParseException {
//        logger.info("获取指定日期所在月份开始的时间戳{}",LiveDateUtils.getMonthBegin(new Date()));
//        logger.info("获取指定日期所在月份结束的时间戳{}",LiveDateUtils.getMonthEnd(new Date()));
//        logger.info("获取当前月{}",LiveDateUtils.getCurrentMonth(new Date()));
//        log.info("时间转换成时间戳={}",LiveDateUtils.dateToStamp("202202").getTime());
//        int gameAmount = 9;
//        int  gameAmount1 = (int) Math.round(gameAmount * 0.9);

        double f = 111231.5585;

        BigDecimal bg = new BigDecimal(f);
        double f1 = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        System.out.println(f1);
    }
}
