package cn.taqu.gonghui.live.param;

import cn.hutool.core.date.DateUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.chatroom.vo.req.BasePageReqV2;
import cn.taqu.gonghui.common.utils.StringUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 24
 * discription
 */
@Data
public class MultiLiveHostSearchOfUM extends BasePageReqV2 implements Serializable {

//    /**
//     * 时间样式
//     */
//    private Integer timeType;

    /**
     * 开始时间（时间戳）
     */
    private Long startTime;

    /**
     * 结束时间（时间戳）
     */
    private Long endTime;

    /**
     * 主播id
     */
    private String  hostUuid;

    /**
     * 主播uuid 列表
     */
    private List<String> hostUuids;

    public List<String> getHostUuids() {
        if(CollectionUtils.isEmpty(this.hostUuids) && StringUtils.isNotBlank(this.hostUuid)){
           List<String> list = new ArrayList<>();
           list.add(this.hostUuid);
           return list;
        }
        return this.hostUuids;
    }

    /**
     * 公会id列表
     */
    private List<Long> consortiaIds;


    /**
     * 是否导出 0-否  1-是
     */
    private Integer export = 0;

    public String getStartTimeFormat(){
        if(this.startTime ==null){
            throw new ServiceException("startTime_required", "起始时间为空");
//            return "";
        }
        return DateUtil.format(new Date(this.startTime),"yyyyMMdd");
    }

    public String getEndTimeFormat(){
        if(this.endTime ==null){
            throw new ServiceException("endTime_required", "结束时间为空");
//            return "";
        }
        return DateUtil.format(new Date(this.endTime),"yyyyMMdd");
    }


}
