package cn.taqu.gonghui.live.vo.multilive;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/23 19 52
 * 用户管理端的多人娱乐公会数据
 */
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
public class MultiLiveConsortiaDataOfUMVo implements Serializable {

    /**
     * 日期
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
    private String time;
    /**
     * 团队id
     */
    @ColumnWidth(20)
    @ExcelProperty("团队id")
    @ExcelIgnore
    private Long  consortiaId;

    @ColumnWidth(20)
    @ExcelProperty("所属团队")
    private String teamName;

    /**
     * （所属）机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("所属机构")
    private String organName;

    /**
     * 开播主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("开播主播数")
    private String liveHostNum;
    /**
     * 新开播主播人数
     */
    @ColumnWidth(20)
    @ExcelProperty("新开播主播数")
    private String newLiveHostNum;
    /**
     * 消费人数
     */
    @ColumnWidth(20)
    @ExcelProperty("消费人数")
    private String costNum;
    /**
     * 公会有效娱乐房间数量
     */
    @ColumnWidth(20)
    @ExcelProperty("公会有效娱乐房数量")
    private String validRoomNum;
    /**
     * 公会有效娱乐主播数量
     */
    @ColumnWidth(20)
    @ExcelProperty("公会有效娱乐主播数量")
    private String validHostNum;
    /**
     * 公会主播收礼
     */
    @ColumnWidth(20)
    @ExcelProperty("公会收礼总流水")
    private String totalAmount;

    /**
     * 本公会娱乐房收礼
     */
    @ColumnWidth(20)
    @ExcelProperty("本公会娱乐房收礼总流水")
    private String consortiaTotalAmount;

//    @ColumnWidth(20)
//    @ExcelProperty("房主总分值")
//    private String roomTotalScore;
//
//    @ColumnWidth(20)
//    @ExcelProperty("房主贝壳值")
//    private String roomShellAmount;
//
//    @ColumnWidth(20)
//    @ExcelProperty("房间贝壳占比")
//    private String roomShellRatio;
}
