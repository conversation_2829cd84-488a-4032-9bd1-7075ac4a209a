package cn.taqu.gonghui.live.mapper;


import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.LiveHostStatisticsMonth;
import cn.taqu.gonghui.live.vo.TopHost;
import cn.taqu.gonghui.live.search.LiveCommonSearch;
import cn.taqu.gonghui.live.vo.LiveCommonVo;

import java.util.List;

public interface LiveHostStatisticsMonthMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LiveHostStatisticsMonth record);

    int insertSelective(LiveHostStatisticsMonth record);

    LiveHostStatisticsMonth selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LiveHostStatisticsMonth record);

    int updateByPrimaryKey(LiveHostStatisticsMonth record);

    /**
     * 根据uuidList获取统计后的数据
     */
    @TargetDataSource("livedb")
    LiveCommonVo getNumByOrgUuid(LiveCommonSearch search);


    /**
     * 根据uuid获取统计后的月主播数数据
     */
    @TargetDataSource("livedb")
    List<TopHost> getNumByHostUuid(LiveCommonSearch search);
}