package cn.taqu.gonghui.live.service;

import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.Employee;
import cn.taqu.gonghui.live.mapper.EmployeeMapper;
import cn.taqu.gonghui.system.entity.SysMenu;
import cn.taqu.gonghui.system.service.SysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/6/17
 */
@Service
public class EmployeeService {

    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private SysMenuService sysMenuService;

    public List<Employee> getAll(){
        return  employeeMapper.selectAll();
    }

    public Map<String,Object> getMap(){
        Map<String,Object> map = new HashMap<>();
        map.put("menu", sysMenuService.selectMenuList(new SysMenu()));
        map.put("livedb",getAll());
        return map;
    }


}
