package cn.taqu.gonghui.live.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/20 15 37
 * 主播成长任务搜索参数
 */
@Data
public class HostTaskSearchParam implements Serializable {

    /**
     * 页号
     */
    private Integer page;

    /**
     * 页大小
     */
    @JsonProperty("page_size")
    private Integer pageSize;

    /**
     * 月份时间戳
     */
    @JsonProperty("month_date")
    private Long monthDate;

    /**
     * 主播uuid
     */
    @JsonProperty("host_uuid")
    private String hostUuid;
}
