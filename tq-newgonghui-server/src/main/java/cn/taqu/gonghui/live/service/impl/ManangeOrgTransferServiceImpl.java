package cn.taqu.gonghui.live.service.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.soa.mq.TqmqResponse;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.common.utils.EncryptUtil;
import cn.taqu.gonghui.live.entity.AgentManage;
import cn.taqu.gonghui.live.entity.BusinessLicense;
import cn.taqu.gonghui.live.entity.ChargePerson;
import cn.taqu.gonghui.live.entity.Employee;
import cn.taqu.gonghui.live.entity.HostScreenshot;
import cn.taqu.gonghui.live.entity.LegalPerson;
import cn.taqu.gonghui.live.entity.Notice;
import cn.taqu.gonghui.live.entity.OpeningPermit;
import cn.taqu.gonghui.live.mapper.EmployeeMapper;
import cn.taqu.gonghui.live.mapper.GuildAgentManageMapper;
import cn.taqu.gonghui.live.search.LiveCommonSearch;
import cn.taqu.gonghui.live.service.ManangeOrgTransferService;
import cn.taqu.gonghui.live.util.LiveDateUtils;
import cn.taqu.gonghui.live.vo.EmployeeAndAgentDto;
import cn.taqu.gonghui.live.vo.LiveCommonVo;
import cn.taqu.gonghui.live.vo.MoveDto;
import cn.taqu.gonghui.soa.InfoService;
import cn.taqu.gonghui.soa.TqmqService;
import cn.taqu.gonghui.system.dto.TeamDto;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import cn.taqu.gonghui.system.service.TeamService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import cn.taqu.gonghui.live.entity.*;
import cn.taqu.gonghui.live.mapper.*;
import cn.taqu.gonghui.system.entity.Agreement;
import cn.taqu.gonghui.system.entity.AgreementInfo;
import cn.taqu.gonghui.system.entity.OrgCooperationFlow;
import cn.taqu.gonghui.system.entity.Organization;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2021/6/21
 */
@Service
@Slf4j
public class ManangeOrgTransferServiceImpl implements ManangeOrgTransferService {


    @Autowired
    private GuildInfoMapper guildInfoMapper;
    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private GuildChargePersonMapper guildChargePersonMapper;
    @Autowired
    private ChargePersonMapper chargePersonMapper;

    @Autowired
    private GuildCooperationFlowMapper guildCooperationFlowMapper;
    @Autowired
    private OrgCooperationFlowMapper orgCooperationFlowMapper;

    @Autowired
    private GuildLegalPersonMapper guildLegalPersonMapper;
    @Autowired
    private LegalPersonMapper legalPersonMapper;


    @Autowired
    private GuildBusinessLicenseMapper guildBusinessLicenseMapper;
    @Autowired
    private BusinessLicenseMapper businessLicenseMapper;

    @Autowired
    private GuildHostScreenshotMapper guildHostScreenshotMapper;
    @Autowired
    private HostScreenshotMapper hostScreenshotMapper;

    @Autowired
    private GuildOpeningPermitMapper guildOpeningPermitMapper;
    @Autowired
    private OpeningPermitMapper openingPermitMapper;

    @Autowired
    private GuildNoticeMapper guildNoticeMapper;
    @Autowired
    private NoticeMapper noticeMapper;

    @Autowired
    private GuildAgreementInfoMapper guildAgreementInfoMapper;
    @Autowired
    private AgreementInfoMapper agreementInfoMapper;

    @Autowired
    private GuildAgreementMapper guildAgreementMapper;
    @Autowired
    private AgreementMapper agreementMapper;

    @Autowired
    private TeamService teamService;


    private static int orgIdStep = 1000000;

    @Override
    public void moveNoticeAndAgr() {
        log.info("非业务数据开始迁移~");
        noticeMove();
        agreementMove();
        agreementInfoMove();
        log.info("非业务数据开始结束~");
    }

    @Override
    public void autoTransfer(String orgUuid) {
        try {
            log.info("机构uuid:{},开始迁移", orgUuid);
            Organization organization = orgInfo(orgUuid);
            if (organization == null) {
                log.warn("未查询到机构uuid:{}的数据，请注意！", orgUuid);
                return;
            }
            autoOperateByHost(orgUuid, organization);
            log.info("机构uuid:{},迁移完毕", orgUuid);
        } catch (Exception e) {
            log.warn("当前公会迁移失败，公会uuid:{},失败原因：{}", orgUuid, e);
        }
    }


    private Organization orgInfo(String orgUuid) {

        GuildInfo guildInfo = guildInfoMapper.selectByUuid(Integer.valueOf(orgUuid));
        if (guildInfo == null) {
            log.info("当前迁移机构不存在，老公会uuid:{}", orgUuid);
            return null;
        }
        //todo 机构
        Organization organization = guildInfoMove(guildInfo);


        //charge_person
        List<ChargePerson> guildChargePeoples = guildChargePersonMapper.selectByGuildUuid(orgUuid);


        //guild_cooperation_flow
        List<GuildCooperationFlow> guildCooperationFlows = guildCooperationFlowMapper.selectByGuildUuid(orgUuid);

        //legal_person
        List<LegalPerson> guildLegalPeoples = guildLegalPersonMapper.selectByGuidUuid(orgUuid);


        //business_license
        List<BusinessLicense> guildBusinessLicenses = guildBusinessLicenseMapper.selectByGuildUuid(orgUuid);


        //host_screenshot
        List<HostScreenshot> guildHostScreenshots = guildHostScreenshotMapper.selectByGuildUuid(orgUuid);

        //opening_permit
        List<OpeningPermit> guildOpeningPermits = guildOpeningPermitMapper.selectByGuildUuid(orgUuid);

        orgTansfer(guildInfo, organization, guildChargePeoples, guildCooperationFlows, guildLegalPeoples, guildBusinessLicenses, guildHostScreenshots, guildOpeningPermits);
        return organization;
    }


    private void orgTansfer(GuildInfo guildInfo, Organization organization, List<ChargePerson> guildChargePeoples, List<GuildCooperationFlow> guildCooperationFlows,
                            List<LegalPerson> guildLegalPeoples, List<BusinessLicense> guildBusinessLicenses, List<HostScreenshot> guildHostScreenshots, List<OpeningPermit> guildOpeningPermits
    ) {

        log.info("机构相关数据开始迁移~");
        organizationMapper.insertSelective(organization);

        //charge_person
        for (ChargePerson person : guildChargePeoples) {
            chargePersonMapper.insertSelective(chargePersonMove(organization.getOrgUuid(), person));
        }

        //guild_cooperation_flow
        for (GuildCooperationFlow guildCooperationFlow : guildCooperationFlows) {
            orgCooperationFlowMapper.insert(orgCooperationFlowMove(organization.getOrgUuid(), guildCooperationFlow));
        }

        //legal_person
        for (LegalPerson guildLegalPerson : guildLegalPeoples) {
            legalPersonMapper.insertSelective(legalPersonMove(organization.getOrgUuid(), guildLegalPerson));
        }

        //business_license
        for (BusinessLicense guildBusinessLicense : guildBusinessLicenses) {
            businessLicenseMapper.insertSelective(businessLicenseMove(organization.getOrgUuid(), guildBusinessLicense));
        }

        //host_screenshot
        for (HostScreenshot guildHostScreenshot : guildHostScreenshots) {
            hostScreenshotMapper.insertSelective(hostScreenshotNove(organization.getOrgUuid(), guildHostScreenshot));
        }

        //opening_permit
        for (OpeningPermit guildOpeningPermit : guildOpeningPermits) {
            openingPermitMapper.insertSelective(openingPermitMove(organization.getOrgUuid(), guildOpeningPermit));
        }
        // 创建默认团队以及当前公会团队
        TeamDto dto = new TeamDto();
        dto.setOrgId(organization.getOrgId());
        dto.setTeamId(Long.valueOf(guildInfo.getUuid()));
        dto.setTeamName(guildInfo.getGuildName());
        Map<Integer, Integer> typeMap = new HashMap<>();
        typeMap.put(1, 1);
        typeMap.put(2, 0);
        typeMap.put(3, 0);
        dto.setTypeMap(typeMap);
        dto.setCreateBy("admin");
        teamService.insertDefaultTeamByMove(dto);

        log.info("机构相关数据开始迁移~");

    }

    private void noticeMove() {
        List<Notice> guildNotices = guildNoticeMapper.selectAll();
        convertNotice(guildNotices);
    }

    //guild_agreement_info
    private void agreementInfoMove() {
        List<GuildAgreementInfo> guildAgreementInfos = guildAgreementInfoMapper.selectAll();
        convertArgInfo(guildAgreementInfos);
    }

    //guild_agreement
    private void agreementMove() {
        List<GuildAgreement> guildAgreements = guildAgreementMapper.selectAll();
        convertArg(guildAgreements);
    }

    private void convertNotice(List<Notice> guildNotices) {
        List<cn.taqu.gonghui.system.entity.Notice> orgNotices = Lists.newArrayList();

        for (Notice notice : guildNotices) {
            cn.taqu.gonghui.system.entity.Notice orgNotice = new cn.taqu.gonghui.system.entity.Notice();
            orgNotice.setTitle(notice.getTitle());
            orgNotice.setContent(notice.getContent());
            orgNotice.setOperator(notice.getOperator());
            orgNotice.setNoticeType(notice.getNoticeType());
            orgNotice.setCreateTime(Long.valueOf(notice.getCreateTime()));
            orgNotice.setUpdateTime(Long.valueOf(notice.getUpdateTime()));
            orgNotice.setFileKey(notice.getFileKey());
            orgNotice.setBusinessType(notice.getGuildType());
            orgNotices.add(orgNotice);
        }
        if (CollectionUtils.isEmpty(orgNotices)) {
            return;
        }
        noticeMapper.insertBatch(orgNotices);
    }


    private void convertArgInfo(List<GuildAgreementInfo> guildAgreementInfos) {
        List<AgreementInfo> agreementInfos = Lists.newArrayList();
        for (GuildAgreementInfo guildAgreementInfo : guildAgreementInfos) {
            AgreementInfo agreementInfo = new AgreementInfo();
            agreementInfo.setAgrId(guildAgreementInfo.getId() + orgIdStep);
            agreementInfo.setOrderLevel(guildAgreementInfo.getOrderLevel());
            agreementInfo.setVersion(guildAgreementInfo.getVersion());
            agreementInfo.setType(1);
            agreementInfo.setContent(guildAgreementInfo.getContent());
            agreementInfo.setCreateTime(Long.valueOf(guildAgreementInfo.getCreateTime()));
            agreementInfo.setUpdateTime(Long.valueOf(guildAgreementInfo.getUpdateTime()));
            agreementInfo.setOperator(guildAgreementInfo.getOperator());
            agreementInfo.setTitle(guildAgreementInfo.getTitle());
            agreementInfo.setValid(guildAgreementInfo.getValid());
            agreementInfos.add(agreementInfo);
        }
        if (CollectionUtils.isEmpty(agreementInfos)) {
            return;
        }
        agreementInfoMapper.insertBatch(agreementInfos);
    }

    private void convertArg(List<GuildAgreement> guildAgreements) {
        List<Agreement> agreements = Lists.newArrayList();
        for (GuildAgreement guildAgreement : guildAgreements) {
            Agreement agreement = new Agreement();
            agreement.setAgrId(guildAgreement.getId() + orgIdStep);
            agreement.setOrgId(Long.valueOf(guildAgreement.getGuildId() + orgIdStep));
            agreement.setCreateTime(Long.valueOf(guildAgreement.getCreateTime()));
            agreements.add(agreement);
        }
        if (CollectionUtils.isEmpty(agreements)) {
            return;
        }
        agreementMapper.insertBatch(agreements);
    }


    private Organization guildInfoMove(GuildInfo guildInfo) {
        Organization organization = new Organization();
        //老字段数据同步到新表中
        organization.setOrgId(guildInfo.getId() + orgIdStep);
        Long maxOrgUuid = organizationMapper.getMaxOrgUuid();
        Long orgUuid = maxOrgUuid == null ? 210000L : maxOrgUuid++;
        Organization gi = organizationMapper.getByUuid(orgUuid.toString());
        //一开始设置的字段是uuid,设置为varchar类型,但是旧机构后台是以主键作为机构id,所以当时取max(uuid)的时候会出问题.要改动uuid为long类型改动较多,先简单处理一下
        while (null != gi) {
            orgUuid += 1;
            gi = organizationMapper.getByUuid(orgUuid.toString());
        }
        organization.setOrgUuid(orgUuid.toString());
        organization.setOrgName(guildInfo.getGuildName());
        organization.setOrgStatus(guildInfo.getGuildStatus());
        organization.setChargePerson(guildInfo.getChargePerson());
        organization.setChargePersonPhone(guildInfo.getChargePersonPhone());
        organization.setChargePersonEmail(guildInfo.getChargePersonEmail());
        organization.setChargePersonIdCard(guildInfo.getChargePersonIdCard());
        organization.setChargePersonBirthday(Optional.ofNullable(guildInfo.getChargePersonBirthday()).orElse(1).longValue());
        organization.setReceivingAddress(guildInfo.getReceivingAddress());
        organization.setLegalPerson(guildInfo.getLegalPerson());
        organization.setLegalPersonIdCard(guildInfo.getLegalPersonIdCard());
        organization.setPublicReceivingBankAccount(guildInfo.getPublicReceivingBankAccount());
        organization.setAccountName(guildInfo.getAccountName());
        organization.setAccountBankName(guildInfo.getAccountBankName());
        organization.setProvince(guildInfo.getProvince());
        organization.setProvinceId(guildInfo.getProvinceId());
        organization.setCity(guildInfo.getCity());
        organization.setCityId(guildInfo.getCityId());
        organization.setSubBranchName(guildInfo.getSubBranchName());
        organization.setApplyStatus(guildInfo.getApplyStatus());
        organization.setBusinessPerson(guildInfo.getBusinessPerson());
        organization.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
        organization.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
        organization.setChargePersonVx(guildInfo.getChargePersonVx());
        organization.setFormStatus(guildInfo.getFormStatus());
        organization.setAccountUuid(guildInfo.getAccountUuid());
        organization.setAuditMsg(guildInfo.getAuditMsg());
        organization.setChargePersonPhoneCipher(guildInfo.getChargePersonPhoneCipher());
        organization.setChargePersonEmailCipher(guildInfo.getChargePersonEmailCipher());
        organization.setChargePersonCipher(guildInfo.getChargePersonCipher());
        organization.setChargePersonVxCipher(guildInfo.getChargePersonVxCipher());
        organization.setChargePersonIdCardCipher(guildInfo.getChargePersonIdCardCipher());
        organization.setReceivingAddressCipher(guildInfo.getReceivingAddressCipher());
        organization.setLegalPersonCipher(guildInfo.getLegalPersonCipher());
        organization.setLegalPersonIdCardCipher(guildInfo.getLegalPersonIdCardCipher());
        organization.setPublicReceivingBankAccountCipher(guildInfo.getPublicReceivingBankAccountCipher());
        organization.setAccountNameCipher(guildInfo.getAccountNameCipher());
        organization.setAccountBankNameCipher(guildInfo.getAccountBankNameCipher());
        organization.setSubBranchNameCipher(guildInfo.getSubBranchNameCipher());
        organization.setBusinessPersonCipher(guildInfo.getBusinessPersonCipher());
        //新机构权限默认打开直播权限其他关闭（注：这里目前只赋予权限不建立默认团队）
        organization.setLivePermissions(OrgStatusEnum.OPEN.getValue());
        organization.setQuliaoPermissions(OrgStatusEnum.DEFAULT.getValue());
        organization.setChatRoomPermissions(OrgStatusEnum.DEFAULT.getValue());
        return organization;
    }

    /**
     * charge_person表数据迁移
     * 负责人信息身份证表
     *
     * @param chargePersonOld
     * @return
     */
    private cn.taqu.gonghui.system.entity.ChargePerson chargePersonMove(String orgUuid, ChargePerson chargePersonOld) {
        cn.taqu.gonghui.system.entity.ChargePerson chargePersonNew = new cn.taqu.gonghui.system.entity.ChargePerson();
        chargePersonNew.setOrgUuid(chargePersonOld.getGuildUuid());
        chargePersonNew.setUrl(chargePersonOld.getUrl());
        chargePersonNew.setCreateTime(Optional.ofNullable(chargePersonOld.getCreateTime()).orElse(0).longValue());
        chargePersonNew.setUpdateTime(Optional.ofNullable(chargePersonOld.getUpdateTime()).orElse(0).longValue());
        chargePersonNew.setType(chargePersonOld.getType());
        return chargePersonNew;
    }

    /**
     * guild_cooperation_flow表数据迁移
     * 公会外站合作流水表
     *
     * @param guildCooperationFlow
     * @return
     */
    private OrgCooperationFlow orgCooperationFlowMove(String orgUuid, cn.taqu.gonghui.live.entity.GuildCooperationFlow guildCooperationFlow) {
        OrgCooperationFlow orgCooperationFlow = new OrgCooperationFlow();
        orgCooperationFlow.setOrgUuid(orgUuid);
        orgCooperationFlow.setUrl(guildCooperationFlow.getUrl());
        orgCooperationFlow.setCreateTime(Optional.ofNullable(guildCooperationFlow.getCreateTime()).orElse(0).longValue());
        orgCooperationFlow.setUpdateTime(Optional.ofNullable(guildCooperationFlow.getUpdateTime()).orElse(0).longValue());
        return orgCooperationFlow;
    }

    /**
     * legal_person表数据迁移
     * 法人信息身份证表
     *
     * @param legalPersonOld
     * @return
     */
    private cn.taqu.gonghui.system.entity.LegalPerson legalPersonMove(String orgUuid, cn.taqu.gonghui.live.entity.LegalPerson legalPersonOld) {
        cn.taqu.gonghui.system.entity.LegalPerson legalPersonNew = new cn.taqu.gonghui.system.entity.LegalPerson();
        legalPersonNew.setOrgUuid(orgUuid);
        legalPersonNew.setUrl(legalPersonOld.getUrl());
        legalPersonNew.setCreateTime(Optional.ofNullable(legalPersonOld.getCreateTime()).orElse(0).longValue());
        legalPersonNew.setUpdateTime(Optional.ofNullable(legalPersonOld.getUpdateTime()).orElse(0).longValue());
        legalPersonNew.setType(legalPersonOld.getType());
        return legalPersonNew;
    }

    /**
     * business_license表数据迁移
     * 营业执照表
     *
     * @param businessLicenseOld
     * @return
     */
    private cn.taqu.gonghui.system.entity.BusinessLicense businessLicenseMove(String orgUuid, cn.taqu.gonghui.live.entity.BusinessLicense businessLicenseOld) {
        cn.taqu.gonghui.system.entity.BusinessLicense businessLicenseNew = new cn.taqu.gonghui.system.entity.BusinessLicense();
        businessLicenseNew.setOrgUuid(orgUuid);
        businessLicenseNew.setUrl(businessLicenseOld.getUrl());
        businessLicenseNew.setCreateTime(Optional.ofNullable(businessLicenseOld.getCreateTime()).orElse(0).longValue());
        businessLicenseNew.setUpdateTime(Optional.ofNullable(businessLicenseOld.getUpdateTime()).orElse(0).longValue());
        return businessLicenseNew;
    }

    /**
     * host_screenshot表数据迁移
     * 公司主播截图
     *
     * @param hostScreenshotOld
     * @return
     */
    private cn.taqu.gonghui.system.entity.HostScreenshot hostScreenshotNove(String orgUuid, cn.taqu.gonghui.live.entity.HostScreenshot hostScreenshotOld) {
        cn.taqu.gonghui.system.entity.HostScreenshot hostScreenshotNew = new cn.taqu.gonghui.system.entity.HostScreenshot();
        hostScreenshotNew.setOrgUuid(orgUuid);
        hostScreenshotNew.setUrl(hostScreenshotOld.getUrl());
        hostScreenshotNew.setCreateTime(Optional.ofNullable(hostScreenshotOld.getCreateTime()).orElse(0).longValue());
        hostScreenshotNew.setUpdateTime(Optional.ofNullable(hostScreenshotOld.getUpdateTime()).orElse(0).longValue());
        return hostScreenshotNew;
    }

    /**
     * opening_permit表数据迁移
     * 开户许可证表
     *
     * @param openingPermitOld
     * @return
     */
    private cn.taqu.gonghui.system.entity.OpeningPermit openingPermitMove(String orgUuid, cn.taqu.gonghui.live.entity.OpeningPermit openingPermitOld) {
        cn.taqu.gonghui.system.entity.OpeningPermit openingPermitNew = new cn.taqu.gonghui.system.entity.OpeningPermit();
        openingPermitNew.setOrgUuid(orgUuid);
        openingPermitNew.setUrl(openingPermitOld.getUrl());
        openingPermitNew.setCreateTime(Optional.ofNullable(openingPermitOld.getCreateTime()).orElse(0).longValue());
        openingPermitNew.setUpdateTime(Optional.ofNullable(openingPermitOld.getUpdateTime()).orElse(0).longValue());
        return openingPermitNew;
    }


    @Autowired
    private GuildAgentManageMapper guildAgentManageMapper;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private TeamEmployeeMapper teamEmployeeMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private EncryptUtil encryptUtil;
    @Autowired
    private LiveHostInfoMapper liveHostInfoMapper;
    @Autowired
    private TeamHostMapper teamHostMapper;


//    /**
//     * 自动迁移用户
//     * employee
//     * agent_manage
//     */
//    public void autoOperateAgentAndEmployee(String orgUuid,Organization organization){
//        log.info("自动迁移用户部分开始，待迁移公会uuid:{}",orgUuid);
//        if (StringUtils.isBlank(orgUuid)) {
//            throw new ServiceException("param_error","请输入公会uuid");
//        }
//        if (null == organization) {
//            throw new ServiceException("param_error","此机构无效");
//        }
//        log.info("获取organization机构信息：{},机构uuid:{}",JsonUtils.objectToString2(organization),orgUuid);
//        EmployeeAndAgentDto dto = new EmployeeAndAgentDto();
//        dto.setTeamId(Long.valueOf(orgUuid));
//        dto.setOrgId(organization.getOrgId());
//        dto.setOrgName(organization.getOrgName());
//        // 获取老公会信息
//        getGuildInfo(orgUuid,dto);
//        // 获取该机构下所有有效agentManage
//        getAgentManages(orgUuid,dto);
//        // 获取该机构下所有有效employee
//        getEmployees(orgUuid,dto);
//        // 获取所有经纪人下的主播信息
//        getLiveHostInfo(dto);
//        // 获取老公会下无经纪人的主播
//        getLiveHostNoAgent(dto,orgUuid);
//        // 获取相关信息
//        getRelevanceInfo(orgUuid,dto);
//        // 迁移employee,agent_manage
//        autoMoveEmployeeAndAgent(dto);
//
//    }

//    /**
//     * 手动迁移用户前先进行校验
//     */
//    @Override
//    public Map<String,String> handOperateValid(String orgUuid, Long orgId){
//        Map<String,String> map = new HashMap<>();
//        if (StringUtils.isBlank(orgUuid)) {
//            map.put("code","stop");
//            map.put("msg","请输入要迁出的公会");
//            return map;
//        }
//        if (null == orgId) {
//            map.put("code","stop");
//            map.put("msg","请输入要迁入的机构");
//            return map;
//        }
////        if (null == teamId) {
////            map.put("code","stop");
////            map.put("msg","请输入要迁入的团队");
////            return map;
////        }
//        // 获取老公会信息
//        GuildInfo guildInfo = guildInfoMapper.selectByUuid(Integer.valueOf(orgUuid));
//        if (null == guildInfo || 0 == guildInfo.getGuildStatus()) {
//            map.put("code","stop");
//            map.put("msg","要迁出公会无效");
//            return map;
//        }
//        // 校验迁入机构信息
//        Organization organization = organizationMapper.selectByPrimaryKey(orgId);
//        if (null == organization || 0 == organization.getOrgStatus()) {
//            map.put("code","stop");
//            map.put("msg","要迁入机构无效");
//            return map;
//        }
//        // 要迁入团队信息
////        Team team = teamMapper.selectByPrimaryKey(teamId);
////        if (null == team || 0 == team.getStatus()) {
////            map.put("code","stop");
////            map.put("msg","要迁入团队无效");
////            return map;
////        }
//        // 此公会是否已经迁移过
//        String accountUuid = guildInfo.getAccountUuid();
//        if (StringUtils.isBlank(accountUuid)) {
//            map.put("code","stop");
//            map.put("msg","要迁出公会无效");
//        } else {
//            List<SysUser> userList = sysUserMapper.userListByAccountUuid(accountUuid);
//            if (CollectionUtils.isNotEmpty(userList)) {
//                map.put("code","stop");
//                map.put("msg","该公会已经迁移");
//            }
//        }
//        map.put("code","pass");
//        map.put("msg","校验通过");
//        return map;
//    }

//    /**
//     * 手动迁移用户
//     * employee
//     * agent_manage
//     */
//    @Override
//    public void handOperateAgentAndEmployee(String orgUuid,Long orgId){
//        if (StringUtils.isBlank(orgUuid)) {
//            throw new ServiceException("param_error","请输入要迁出的机构uuid");
//        }
//        if (null == orgId) {
//            throw new ServiceException("param_error","请输入要迁入的机构id");
//        }
////        if (null == teamId) {
////            throw new ServiceException("param_error","请输入团队id");
////        }
//        // 获取老公会信息
//        GuildInfo guildInfo = guildInfoMapper.selectByUuid(Integer.valueOf(orgUuid));
//        log.info("手动迁移获取guild_info老公会信息：{}，公会uuid:{}",JsonUtils.objectToString2(guildInfo),orgUuid);
//        if (null == guildInfo || 0 == guildInfo.getGuildStatus()) {
//            throw new ServiceException("invalid guild","要迁出公会无效");
//        }
//        // 校验迁入机构信息
//        Organization organization = organizationMapper.selectByPrimaryKey(orgId);
//        log.info("手动迁移获取organization新机构信息：{}，公会uuid:{}",JsonUtils.objectToString2(organization),orgUuid);
//        if (null == organization || 0 == organization.getOrgStatus()) {
//            throw new ServiceException("invalid organization","要迁入机构无效");
//        }
////        // 获取要迁入的团队
////        Team team = teamMapper.selectByPrimaryKey(teamId);
////        log.info("手动迁移获取team要迁入团队信息：{}，公会uuid:{}",JsonUtils.objectToString2(team),orgUuid);
////        if (null == team || 0 == team.getStatus()) {
////            throw new ServiceException("invalid team","要迁入团队无效");
////        }
//        // 此公会是否已经迁移过
//        String accountUuid = guildInfo.getAccountUuid();
//        if (StringUtils.isBlank(accountUuid)) {
//            throw new ServiceException("invalid guild","要迁出公会无效");
//        } else {
//            List<SysUser> userList = sysUserMapper.userListByAccountUuid(accountUuid);
//            if (CollectionUtils.isNotEmpty(userList)) {
//                throw new ServiceException("move_stopped","该公会已经迁移过");
//            }
//        }
//        // 为当前老公会创建默认团队
//        Team team = new Team();
//        team.setTeamId(Long.valueOf(guildInfo.getUuid()));
//        team.setOrgId(orgId);
//        team.setTeamName(guildInfo.getGuildName());
//        team.setSignKey("liveTeam1");
//        team.setType(1);
//        team.setIsDefault(TeamDefaultEnum.UN_DEFAULT.getValue());
//        team.setStatus(1);
//        team.setCreateBy("admin");
//        team.setCreateTime(System.currentTimeMillis()/1000);
//        team.setUpdateTime(System.currentTimeMillis()/1000);
//        teamMapper.insertTeam(team);
//        // 获取直播类型经纪人角色 roleId
//        Long agentRoleId = sysRoleMapper.getAgentRoleId();
//        log.info("手动迁移获取agentRoleId：{}，公会uuid:{}",agentRoleId,orgUuid);
//        if (null == agentRoleId || 0L == agentRoleId) {
//            throw new ServiceException("param_error","直播业务类型经纪人角色不存在，请创建");
//        }
//        // 获取直播类型负责人角色 roleId
//        Long leaderRoleId = sysRoleMapper.getLeaderRoleId();
//        log.info("手动迁移获取leaderRoleId：{}，公会uuid:{}",leaderRoleId,orgUuid);
//        if (null == leaderRoleId || 0L == leaderRoleId) {
//            throw new ServiceException("param_error","直播业务类型负责人角色不存在，请创建");
//        }
//        EmployeeAndAgentDto dto = new EmployeeAndAgentDto();
//        // 获取该机构下所有有效agentManage
//        getAgentManages(orgUuid,dto);
//        // 获取该机构下所有有效employee
//        getEmployees(orgUuid,dto);
//        // 获取经纪人下主播
//        getLiveHostInfo(dto);
//        // 获取老公会下无经纪人主播
//        getLiveHostNoAgent(dto,orgUuid);
//        // 设置相关信息
//        dto.setOrgId(organization.getOrgId());
//        dto.setOrgName(organization.getOrgName());
//        dto.setTeamId(team.getTeamId());
//        dto.setAgentRoleId(agentRoleId);
//        dto.setLeaderRoleId(leaderRoleId);
//        dto.setGuildInfo(guildInfo);
//        // 迁移employee,agent_manage
//        handMoveEmployeeAndAgent(dto);
//        // 迁移成功发送短信通知给机构所有员工
//        sendMessage(dto);
//    }

    /**
     * 获取所有有效老公会下拉
     *
     * @return
     */
    @Override
    public List<CommonSelect> tree() {
        List<GuildInfo> guildList = guildInfoMapper.getGuildList();
        return guildList.stream().map(CommonSelect::new).collect(Collectors.toList());
    }

    /**
     * 自动迁移employee，agent
     */
    public void autoMoveEmployeeAndAgent(EmployeeAndAgentDto employeeDto) {

//        log.info("成员经纪人主播数据开始迁移");
//
//        List<AgentManage> agentManageList = employeeDto.getAgentManageList();
//        List<Employee> employeeList = employeeDto.getEmployeeList();
//        Map<String, List<LiveHostInfo>> hostMap = employeeDto.getHostMap();
//        List<LiveHostInfo> hostListNoAgent = employeeDto.getLiveHostInfoList();
//        GuildInfo guildInfo = employeeDto.getGuildInfo();
//        Long orgId = employeeDto.getOrgId();
//        String orgName = employeeDto.getOrgName();
//        Long teamId = employeeDto.getTeamId();
//        Long agentRoleId = employeeDto.getAgentRoleId();
//        Long managerRoleId = employeeDto.getManagerRoleId();
//        Set<Long> sysUserRoleSet = new HashSet<>();
//        try {
//            // 迁移guild_info表会长 - 老公会会长角色迁移的时候作为机构管理员角色插入
//            // 先新增sys_user表
//            SysUser sysUserRecord = new SysUser();
//            sysUserRecord.setOrgId(orgId);
//            sysUserRecord.setOrgName(orgName);
//            sysUserRecord.setAccountUuid(guildInfo.getAccountUuid());
//
//            // 调用j2获取手机号码和名称
//            String mobile = "";
//            String accountName = "";
//            Map<String, Object> userInfo = getInfoByUuids(guildInfo.getAccountUuid());
//            if (MapUtils.isNotEmpty(userInfo)) {
//                mobile = (String)userInfo.get("mobile");
//                accountName = (String)userInfo.get("account_name");
//            }
//            sysUserRecord.setUserName(accountName);
//            sysUserRecord.setMobile(mobile);
//
//            sysUserRecord.setUserType(UserTypeEnum.MANAGER.getType());
//            sysUserRecord.setStatus(UserStatus.OK.getCode());
//            sysUserRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
//            sysUserRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
//            sysUserMapper.insert(sysUserRecord);
//            // 后新增sys_user_role表
//            SysUserRole sysUserRoleRecord = new SysUserRole();
//            sysUserRoleRecord.setRoleId(managerRoleId);
//            sysUserRoleRecord.setUserId(sysUserRecord.getUserId());
//            sysUserRoleRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
//            sysUserRoleRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
//            sysUserRoleMapper.insert(sysUserRoleRecord);
//            // 对已经成功插入到sys_user_role表的数据id备份
//            sysUserRoleSet.add(sysUserRoleRecord.getId());
//
//
//            // 新增无经纪人主播
//            if (CollectionUtils.isNotEmpty(hostListNoAgent)) {
//                List<TeamHost> list = new ArrayList<>();
//                for (LiveHostInfo host : hostListNoAgent) {
//                    TeamHost teamHost = new TeamHost();
//                    teamHost.setHostUuid(host.getHostUuid());
//                    teamHost.setOrgId(orgId);
//                    teamHost.setTeamId(teamId);
//                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                    teamHost.setStatus(1);
//                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                    teamHost.setCurrentSharingProfitRate("0");
//                    teamHost.setNewSharingProfitRate("");
//                    teamHost.setIsUpdate(0);
//                    list.add(teamHost);
//                }
//                // 批量插入
//                if (CollectionUtils.isNotEmpty(list)) {
//                    teamHostMapper.batchInsertHost(list);
//                }
//            }
//
//            // 迁移 agent_manage表
//            if (CollectionUtils.isNotEmpty(agentManageList)) {
//                for (AgentManage agent : agentManageList) {
//                    // 判断当前经纪人是否是会长
//                    List<SysUser> userList = sysUserMapper.userIsHasExisted(agent.getAgentUuid());
//                    if (CollectionUtils.isNotEmpty(userList)) {
//                        if (MapUtils.isNotEmpty(hostMap)) {
//                            List<LiveHostInfo> liveHostInfoList = hostMap.get(agent.getAgentUuid());
//                            if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                                List<TeamHost> list = new ArrayList<>();
//                                for (LiveHostInfo host : liveHostInfoList) {
//                                    TeamHost teamHost = new TeamHost();
//                                    teamHost.setHostUuid(host.getHostUuid());
//                                    teamHost.setOrgId(orgId);
//                                    teamHost.setTeamId(teamId);
//                                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setStatus(1);
//                                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setCurrentSharingProfitRate("0");
//                                    teamHost.setNewSharingProfitRate("");
//                                    teamHost.setIsUpdate(0);
//                                    list.add(teamHost);
//                                }
//                                // 批量插入
//                                if (CollectionUtils.isNotEmpty(list)) {
//                                    teamHostMapper.batchInsertHost(list);
//                                }
//                            }
//                        }
//                        continue;
//                    }
//                    // 先新增sys_user表
//                    SysUser sysUser = new SysUser();
//                    sysUser.setOrgId(orgId);
//                    sysUser.setOrgName(orgName);
//                    sysUser.setAccountUuid(agent.getAgentUuid());
//                    sysUser.setUserName(agent.getAgentName());
//                    sysUser.setUserType(UserTypeEnum.AGENTER.getType());
//                    sysUser.setMobile(agent.getPhone());
//                    sysUser.setStatus(UserStatus.OK.getCode());
//                    sysUser.setCreateTime(agent.getCreateTime());
//                    sysUser.setUpdateTime(agent.getUpdateTime());
//                    sysUserMapper.insert(sysUser);
//                    // 其次新增sys_user_role表
//                    SysUserRole sysUserRole = new SysUserRole();
//                    sysUserRole.setRoleId(agentRoleId);
//                    sysUserRole.setUserId(sysUser.getUserId());
//                    sysUserRole.setCreateTime(agent.getCreateTime());
//                    sysUserRole.setUpdateTime(agent.getUpdateTime());
//                    sysUserRoleMapper.insert(sysUserRole);
//                    // 对已经成功插入到sys_user_role表的数据id备份
//                    sysUserRoleSet.add(sysUserRole.getId());
//                    // 后新增team_employee表
//                    TeamEmployee teamEmployee = new TeamEmployee();
//                    teamEmployee.setOrgId(orgId);
//                    teamEmployee.setOrgName(orgName);
//                    teamEmployee.setTeamId(teamId);
//                    teamEmployee.setUserId(sysUser.getUserId());
//                    teamEmployee.setMobile(agent.getPhone());
//                    teamEmployee.setEmployeeName(agent.getAgentName());
//                    teamEmployee.setType(UserTypeEnum.AGENTER.getType());
//                    teamEmployee.setInviteCode(agent.getInvitationCode());
//                    teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
//                    teamEmployee.setInviteTime(agent.getCreateTime());
//                    teamEmployee.setCreateTime(agent.getCreateTime());
//                    teamEmployee.setUpdateTime(agent.getUpdateTime());
//                    teamEmployeeMapper.insert(teamEmployee);
//                    // 最后新增team_host表
//                    if (MapUtils.isNotEmpty(hostMap)) {
//                        List<LiveHostInfo> liveHostInfoList = hostMap.get(agent.getAgentUuid());
//                        if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                            List<TeamHost> list = new ArrayList<>();
//                            for (LiveHostInfo host : liveHostInfoList) {
//                                TeamHost teamHost = new TeamHost();
//                                teamHost.setHostUuid(host.getHostUuid());
//                                teamHost.setOrgId(orgId);
//                                teamHost.setTeamId(teamId);
//                                teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                teamHost.setEmployeeId(teamEmployee.getEmployeeId());
//                                teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                teamHost.setStatus(1);
//                                teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                teamHost.setCurrentSharingProfitRate("0");
//                                teamHost.setNewSharingProfitRate("");
//                                teamHost.setIsUpdate(0);
//                                list.add(teamHost);
//                            }
//                            // 批量插入
//                            if (CollectionUtils.isNotEmpty(list)) {
//                                teamHostMapper.batchInsertHost(list);
//                            }
//                        }
//                    }
//                }
//            }
//            // 迁移employee表
//            if (CollectionUtils.isNotEmpty(employeeList)) {
//                for (Employee employee : employeeList) {
//                    // 先新增sys_user表
//                    SysUser sysUser = new SysUser();
//                    sysUser.setOrgId(orgId);
//                    sysUser.setOrgName(orgName);
//                    sysUser.setAccountUuid(employee.getEmployeeUuid());
//                    sysUser.setUserName(employee.getEmployeeName());
//                    sysUser.setUserType(UserTypeEnum.AGENTER.getType());
//                    // 解密
//                    sysUser.setMobile(getDecryptMobile("employee_mobile_cipher", employee.getEmployeeMobileCipher()));
//                    sysUser.setStatus(UserStatus.OK.getCode());
//                    sysUser.setCreateTime(employee.getCreateTime());
//                    sysUser.setUpdateTime(employee.getUpdateTime());
//                    sysUserMapper.insert(sysUser);
//                    // 其次新增sys_user_role表
//                    SysUserRole sysUserRole = new SysUserRole();
//                    sysUserRole.setRoleId(agentRoleId);
//                    sysUserRole.setUserId(sysUser.getUserId());
//                    sysUserRole.setCreateTime(employee.getCreateTime());
//                    sysUserRole.setUpdateTime(employee.getUpdateTime());
//                    sysUserRoleMapper.insert(sysUserRole);
//                    // 对已经成功插入到sys_user_role表的数据id备份
//                    sysUserRoleSet.add(sysUserRole.getId());
//                    // 后新增team_employee表
//                    TeamEmployee teamEmployee = new TeamEmployee();
//                    teamEmployee.setOrgId(orgId);
//                    teamEmployee.setOrgName(orgName);
//                    teamEmployee.setTeamId(teamId);
//                    teamEmployee.setUserId(sysUser.getUserId());
//                    // 解密
//                    teamEmployee.setMobile(getDecryptMobile("employee_mobile_cipher", employee.getEmployeeMobileCipher()));
//                    teamEmployee.setEmployeeName(employee.getEmployeeName());
//                    teamEmployee.setType(UserTypeEnum.AGENTER.getType());
//                    teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
//                    teamEmployee.setInviteTime(employee.getCreateTime());
//                    teamEmployee.setCreateTime(employee.getCreateTime());
//                    teamEmployee.setUpdateTime(employee.getUpdateTime());
//                    teamEmployeeMapper.insert(teamEmployee);
//                }
//            }
//        } catch (Exception e){
//            // 发生异常，所有插入操作数据全部回滚（删除）
//            // 删除sys_user表中已经插入的当前orgId下所有用户
//            sysUserMapper.deleteByOrgId(orgId);
//            // 删除sys_user_role表中已经插入的所有关联数据
//            if (CollectionUtils.isNotEmpty(sysUserRoleSet)) {
//                sysUserRoleMapper.batchDeleteByIds(sysUserRoleSet);
//            }
//            // 删除team_employee表中已经插入的当前orgId下所有用户
//            teamEmployeeMapper.deleteByOrgId(orgId);
//            // 删除team_host表中已经插入的当前orgId下所有主播
//            teamHostMapper.deleteByOrgId(orgId);
//            // 删除team表中创建的默认团队
//            teamMapper.deleteByOrgId(orgId);
//            log.error("move fail - 数据迁移失败，失败原因：{},失败机构id：{}",e,orgId);
//            throw new ServiceException("move_fail - 数据迁移失败，失败原因：{}",e.getMessage());
//        }

    }

    /**
     * 手动迁移employee，agent
     */
    public void handMoveEmployeeAndAgent(EmployeeAndAgentDto employeeDto) {
        List<AgentManage> agentManageList = employeeDto.getAgentManageList();
        List<Employee> employeeList = employeeDto.getEmployeeList();
        Map<String, List<LiveHostInfo>> hostMap = employeeDto.getHostMap();
        List<LiveHostInfo> hostListNoAgent = employeeDto.getLiveHostInfoList();
        GuildInfo guildInfo = employeeDto.getGuildInfo();
        Long orgId = employeeDto.getOrgId();
        String orgName = employeeDto.getOrgName();
        Long teamId = employeeDto.getTeamId();
        Long agentRoleId = employeeDto.getAgentRoleId();
        Long leaderRoleId = employeeDto.getLeaderRoleId();
        Set<Long> sysUserRoleSet = new HashSet<>();
        Set<Long> sysUserSet = new HashSet<>();
        Set<Long> teamEmployeeSet = new HashSet<>();
        try {
            // 迁移guild_info表会长 - 老公会会长角色迁移后变成团队负责人角色
            // 先新增sys_user表
            SysUser sysUserRecord = new SysUser();
            sysUserRecord.setOrgId(orgId);
            sysUserRecord.setOrgName(orgName);
            sysUserRecord.setAccountUuid(guildInfo.getAccountUuid());

            // 调用j2获取手机号码和名称
            String mobile = "";
            String accountName = "";
            Map<String, Object> userInfo = getInfoByUuids(guildInfo.getAccountUuid());
            if (MapUtils.isNotEmpty(userInfo)) {
                mobile = (String) userInfo.get("mobile");
                accountName = (String) userInfo.get("account_name");
            }
            sysUserRecord.setUserName(accountName);
            sysUserRecord.setMobile(mobile);

            sysUserRecord.setUserType(UserTypeEnum.LEADER.getType());
            sysUserRecord.setStatus(UserStatus.OK.getCode());
            sysUserRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
            sysUserRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
            sysUserMapper.insert(sysUserRecord);
            // 对已经成功插入到sys_user表的数据id备份
            sysUserSet.add(sysUserRecord.getUserId());
            // 其次新增sys_user_role表
            SysUserRole sysUserRoleRecord = new SysUserRole();
            sysUserRoleRecord.setRoleId(leaderRoleId);
            sysUserRoleRecord.setUserId(sysUserRecord.getUserId());
            sysUserRoleRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
            sysUserRoleRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
            sysUserRoleMapper.insert(sysUserRoleRecord);
            // 对已经成功插入到sys_user_role表的数据id备份
            sysUserRoleSet.add(sysUserRoleRecord.getId());
            // 后新增team_employee表
            TeamEmployee teamEmployeeRecord = new TeamEmployee();
            teamEmployeeRecord.setOrgId(orgId);
            teamEmployeeRecord.setOrgName(orgName);
            teamEmployeeRecord.setTeamId(teamId);
            teamEmployeeRecord.setUserId(sysUserRecord.getUserId());

            // 本来是有会长电话号码的，但是要调用j2,因此先不给值，后面通过sql脚本统一修改
            teamEmployeeRecord.setMobile(mobile);
            // 本来是有会长名称的，但是要调用j2,因此先不给值，后面通过sql脚本统一修改
            teamEmployeeRecord.setEmployeeName(accountName);

            teamEmployeeRecord.setType(UserTypeEnum.LEADER.getType());
            teamEmployeeRecord.setInviteCode(null);
            teamEmployeeRecord.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
            teamEmployeeRecord.setInviteTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
            teamEmployeeRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
            teamEmployeeRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
            teamEmployeeMapper.insert(teamEmployeeRecord);
            teamEmployeeSet.add(teamEmployeeRecord.getEmployeeId());

            // 新增无经纪人主播
            if (CollectionUtils.isNotEmpty(hostListNoAgent)) {
                List<TeamHost> list = new ArrayList<>();
                for (LiveHostInfo host : hostListNoAgent) {
                    TeamHost teamHost = new TeamHost();
                    teamHost.setHostUuid(host.getHostUuid());
                    teamHost.setOrgId(orgId);
                    teamHost.setTeamId(teamId);
                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setStatus(1);
                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setCurrentSharingProfitRate("0");
                    teamHost.setNewSharingProfitRate("");
                    teamHost.setIsUpdate(0);
                    list.add(teamHost);
                }
                // 批量插入
                if (CollectionUtils.isNotEmpty(list)) {
                    teamHostMapper.batchInsertHost(list);
                }
            }


            // 迁移agent_manage表
            if (CollectionUtils.isNotEmpty(agentManageList)) {
                for (AgentManage agent : agentManageList) {
                    // 先判断当前经纪人是否是会长
                    List<SysUser> userList = sysUserMapper.userIsHasExisted(agent.getAgentUuid());
                    if (CollectionUtils.isNotEmpty(userList)) {
                        if (MapUtils.isNotEmpty(hostMap)) {
                            List<LiveHostInfo> liveHostInfoList = hostMap.get(agent.getAgentUuid());
                            if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
                                List<TeamHost> list = new ArrayList<>();
                                for (LiveHostInfo host : liveHostInfoList) {
                                    TeamHost teamHost = new TeamHost();
                                    teamHost.setHostUuid(host.getHostUuid());
                                    teamHost.setOrgId(orgId);
                                    teamHost.setTeamId(teamId);
                                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
                                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                    teamHost.setStatus(1);
                                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                    teamHost.setCurrentSharingProfitRate("0");
                                    teamHost.setNewSharingProfitRate("");
                                    teamHost.setIsUpdate(0);
                                    list.add(teamHost);
                                }
                                // 批量插入
                                if (CollectionUtils.isNotEmpty(list)) {
                                    teamHostMapper.batchInsertHost(list);
                                }
                            }
                        }
                        continue;
                    }
                    // 先新增sys_user表
                    SysUser sysUser = new SysUser();
                    sysUser.setOrgId(orgId);
                    sysUser.setOrgName(orgName);
                    sysUser.setAccountUuid(agent.getAgentUuid());
                    sysUser.setUserName(agent.getAgentName());
                    sysUser.setUserType(UserTypeEnum.AGENTER.getType());
                    sysUser.setMobile(agent.getPhone());
                    sysUser.setStatus(UserStatus.OK.getCode());
                    sysUser.setCreateTime(agent.getCreateTime());
                    sysUser.setUpdateTime(agent.getUpdateTime());
                    sysUserMapper.insert(sysUser);
                    sysUserSet.add(sysUser.getUserId());
                    // 其次新增sys_user_role表
                    SysUserRole sysUserRole = new SysUserRole();
                    sysUserRole.setRoleId(agentRoleId);
                    sysUserRole.setUserId(sysUser.getUserId());
                    sysUserRole.setCreateTime(agent.getCreateTime());
                    sysUserRole.setUpdateTime(agent.getUpdateTime());
                    sysUserRoleMapper.insert(sysUserRole);
                    // 对已经成功插入到sys_user_role表的数据id备份
                    sysUserRoleSet.add(sysUserRole.getId());
                    // 后新增team_employee表
                    TeamEmployee teamEmployee = new TeamEmployee();
                    teamEmployee.setOrgId(orgId);
                    teamEmployee.setOrgName(orgName);
                    teamEmployee.setTeamId(teamId);
                    teamEmployee.setUserId(sysUser.getUserId());
                    teamEmployee.setMobile(agent.getPhone());
                    teamEmployee.setEmployeeName(agent.getAgentName());
                    teamEmployee.setType(UserTypeEnum.AGENTER.getType());
                    teamEmployee.setInviteCode(agent.getInvitationCode());
                    teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
                    teamEmployee.setInviteTime(agent.getCreateTime());
                    teamEmployee.setCreateTime(agent.getCreateTime());
                    teamEmployee.setUpdateTime(agent.getUpdateTime());
                    teamEmployeeMapper.insert(teamEmployee);
                    teamEmployeeSet.add(teamEmployee.getEmployeeId());
                    // 最后新增team_host表
                    if (MapUtils.isNotEmpty(hostMap)) {
                        List<LiveHostInfo> liveHostInfoList = hostMap.get(agent.getAgentUuid());
                        if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
                            List<TeamHost> list = new ArrayList<>();
                            for (LiveHostInfo host : liveHostInfoList) {
                                TeamHost teamHost = new TeamHost();
                                teamHost.setHostUuid(host.getHostUuid());
                                teamHost.setOrgId(orgId);
                                teamHost.setTeamId(teamId);
                                teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
                                teamHost.setEmployeeId(teamEmployee.getEmployeeId());
                                teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                teamHost.setStatus(1);
                                teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                teamHost.setCurrentSharingProfitRate("0");
                                teamHost.setNewSharingProfitRate("");
                                teamHost.setIsUpdate(0);
                                list.add(teamHost);
                            }
                            // 批量插入
                            if (CollectionUtils.isNotEmpty(list)) {
                                teamHostMapper.batchInsertHost(list);
                            }
                        }
                    }
                }
            }
            // 迁移employee表
            if (CollectionUtils.isNotEmpty(employeeList)) {
                for (Employee employee : employeeList) {
                    // 先新增sys_user表
                    SysUser sysUser = new SysUser();
                    sysUser.setOrgId(orgId);
                    sysUser.setOrgName(orgName);
                    sysUser.setAccountUuid(employee.getEmployeeUuid());
                    sysUser.setUserName(employee.getEmployeeName());
                    sysUser.setUserType(UserTypeEnum.AGENTER.getType());
                    // 解密
                    sysUser.setMobile(getDecryptMobile("employee_mobile_cipher", employee.getEmployeeMobileCipher()));
                    sysUser.setStatus(UserStatus.OK.getCode());
                    sysUser.setCreateTime(employee.getCreateTime());
                    sysUser.setUpdateTime(employee.getUpdateTime());
                    sysUserMapper.insert(sysUser);
                    sysUserSet.add(sysUser.getUserId());
                    // 其次新增sys_user_role表
                    SysUserRole sysUserRole = new SysUserRole();
                    sysUserRole.setRoleId(agentRoleId);
                    sysUserRole.setUserId(sysUser.getUserId());
                    sysUserRole.setCreateTime(employee.getCreateTime());
                    sysUserRole.setUpdateTime(employee.getUpdateTime());
                    sysUserRoleMapper.insert(sysUserRole);
                    // 对已经成功插入到sys_user_role表的数据id备份
                    sysUserRoleSet.add(sysUserRole.getId());
                    // 后新增team_employee表
                    TeamEmployee teamEmployee = new TeamEmployee();
                    teamEmployee.setOrgId(orgId);
                    teamEmployee.setOrgName(orgName);
                    teamEmployee.setTeamId(teamId);
                    teamEmployee.setUserId(sysUser.getUserId());
                    // 解密
                    teamEmployee.setMobile(getDecryptMobile("employee_mobile_cipher", employee.getEmployeeMobileCipher()));
                    teamEmployee.setEmployeeName(employee.getEmployeeName());
                    teamEmployee.setType(UserTypeEnum.AGENTER.getType());
                    teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
                    teamEmployee.setInviteTime(employee.getCreateTime());
                    teamEmployee.setCreateTime(employee.getCreateTime());
                    teamEmployee.setUpdateTime(employee.getUpdateTime());
                    teamEmployeeMapper.insert(teamEmployee);
                    teamEmployeeSet.add(teamEmployee.getEmployeeId());
                }
            }
            // 将老公会的状态置为不可用
            guildInfoMapper.updateGuildInfoStatus(2, guildInfo.getUuid());
        } catch (Exception e) {
            // 发生异常回滚
            // 将老公会的状态置恢复为可用
            guildInfoMapper.updateGuildInfoStatus(1, guildInfo.getUuid());
            // 删除sys_user表中已经插入的当前orgId下所有用户
            if (CollectionUtils.isNotEmpty(sysUserSet)) {
                sysUserMapper.batchDeleteByIds(sysUserSet);
            }
            // 删除sys_user_role表中已经插入的所有关联数据
            if (CollectionUtils.isNotEmpty(sysUserRoleSet)) {
                sysUserRoleMapper.batchDeleteByIds(sysUserRoleSet);
            }
            // 删除team_employee表中已经插入的当前orgId下所有用户
            if (CollectionUtils.isNotEmpty(teamEmployeeSet)) {
                teamEmployeeMapper.batchDeleteByIds(teamEmployeeSet);
            }
            // 删除team_host表中已经插入的当前orgId下所有主播
            teamHostMapper.deleteByTeamId(teamId);
            // 删除team表新增的团队
            teamMapper.deleteByPrimaryKey(teamId);
            log.error("move fail - 数据迁移失败，失败原因：{},失败机构id：{}", e, orgId);
            throw new ServiceException("move_fail - 数据迁移失败，失败原因：{}", e);

        }

    }

    /**
     * 获取经纪人下live_host_info
     */
    public void getLiveHostInfo(EmployeeAndAgentDto dto) {
        if (CollectionUtils.isNotEmpty(dto.getAgentManageList())) {
            Map<String, List<LiveHostInfo>> map = new HashMap<>();
            for (AgentManage agent : dto.getAgentManageList()) {
                List<LiveHostInfo> liveHostInfoList = liveHostInfoMapper.getHostListByBusinessUuid(agent.getAgentUuid());
                if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
                    map.put(agent.getAgentUuid(), liveHostInfoList);
                }
            }
            dto.setHostMap(map);
        }
        log.info("获取到live_host_info主播信息:{},机构id:{}", JsonUtils.objectToString(dto.getHostMap()), dto.getOrgId());
    }

    /**
     * 获取无经纪人的主播
     *
     * @param dto
     * @param orgUuid
     */
    public void getLiveHostNoAgent(EmployeeAndAgentDto dto, String orgUuid) {
        List<LiveHostInfo> liveHostInfoList = liveHostInfoMapper.getHostListNoBusinessUuid(Integer.valueOf(orgUuid));
        dto.setLiveHostInfoList(liveHostInfoList);
    }

    /**
     * 获取guild_info
     */
    private void getGuildInfo(String orgUuid, EmployeeAndAgentDto dto) {
        GuildInfo guildInfo = guildInfoMapper.selectByUuid(Integer.valueOf(orgUuid));
        log.info("获取到guild_info老公会信息：{},公会uuid", JsonUtils.objectToString(guildInfo), orgUuid);
        if (null == guildInfo) {
            throw new ServiceException("invalid guild", "要迁出公会无效");
        }
        dto.setGuildInfo(guildInfo);
    }

    /**
     * 获取employees
     */
    private void getEmployees(String orgUuid, EmployeeAndAgentDto dto) {
        List<Employee> employeeList = employeeMapper.getEmployeeList(orgUuid);
        log.info("获取到employee职员信息：{},公会uuid:{}", JsonUtils.objectToString(employeeList), orgUuid);
        dto.setEmployeeList(employeeList);
    }

    /**
     * 获取agentManages
     */
    private void getAgentManages(String orgUuid, EmployeeAndAgentDto dto) {
        // 获取公会下所有agent
        List<AgentManage> agentList = guildAgentManageMapper.getAgentList(orgUuid);
        log.info("获取到agent_manage经纪人信息：{},公会uuid:{}", JsonUtils.objectToString(agentList), orgUuid);
        dto.setAgentManageList(agentList);
    }

    /**
     * 获取相关联信息
     *
     * @param orgUuid
     * @param dto
     */
    public void getRelevanceInfo(String orgUuid, EmployeeAndAgentDto dto) {
        // 获取机构id和机构名称
//        Organization organization = organizationMapper.getOrgIdByOrgUuid(orgUuid);
//        log.info("获取organization机构信息：{},机构uuid:{}",JsonUtils.objectToString2(organization),orgUuid);
//        if (null == organization) {
//            throw new ServiceException("param_error","此机构无效");
//        }
        // 获取当前机构下默认团队id
//        Team defaultTeam = teamMapper.getDefaultTeam(organization.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
//        log.info("获取team默认团队信息：{},机构uuid:{}",JsonUtils.objectToString2(defaultTeam),orgUuid);
//        if (null == defaultTeam) {
//            throw new ServiceException("param_error","该机构下无所属默认团队");
//        }
        // 获取直播类型经纪人角色 roleId
        Long agentRoleId = sysRoleMapper.getAgentRoleId();
        log.info("获取agentRoleId：{},机构uuid:{}", agentRoleId, orgUuid);
        if (null == agentRoleId || 0L == agentRoleId) {
            throw new ServiceException("param_error", "直播业务类型经纪人角色不存在，请创建");
        }
        // 获取直播类型管理员角色 roleId
        Long managerRoleId = sysRoleMapper.getManagerRoleId();
        log.info("获取managerRoleId：{},机构uuid:{}", managerRoleId, orgUuid);
        if (null == managerRoleId || 0L == managerRoleId) {
            throw new ServiceException("param_error", "直播业务类型管理员角色不存在，请创建");
        }
//        dto.setOrgId(organization.getOrgId());
//        dto.setOrgName(organization.getOrgName());
//        dto.setTeamId(defaultTeam.getTeamId());
        dto.setAgentRoleId(agentRoleId);
        dto.setManagerRoleId(managerRoleId);
    }

    /**
     * 解密后内容
     */
    private String getDecryptMobile(String key, String content) {
        List<Map<String, String>> list = new ArrayList<>();
        Map<String, String> map = new HashedMap();
        map.put(key, content);
        list.add(map);
        List<Map<String, String>> returnList = encryptUtil.batchDecryptList("gonghui", list);
        if (CollectionUtils.isNotEmpty(returnList)) {
            return returnList.get(0).get(key);
        }
        return content;
    }

    @SoaReference("account")
    private InfoService infoService;

    @Async
    public void sendMessage(EmployeeAndAgentDto dto) {
        List<String> mobileList = new ArrayList<>();
        GuildInfo guildInfo = dto.getGuildInfo();
        List<AgentManage> agentManageList = dto.getAgentManageList();
        List<Employee> employeeList = dto.getEmployeeList();
        // 机构负责人
        if (StringUtils.isNotBlank(guildInfo.getChargePersonPhone())) {
            mobileList.add(guildInfo.getChargePersonPhone());
        }
        // 会长
        if (StringUtils.isNotBlank(guildInfo.getAccountUuid())) {
            Map<String, Object> map = getInfoByUuids(guildInfo.getAccountUuid());
            if (MapUtils.isNotEmpty(map)) {
                mobileList.add((String) map.get("mobile"));
            }
        }
        // 经纪人
        if (CollectionUtils.isNotEmpty(agentManageList)) {
            for (AgentManage agent : agentManageList) {
                if (StringUtils.isNotBlank(agent.getPhone())) {
                    mobileList.add(agent.getPhone());
                }
            }
        }
        // 职员
        if (CollectionUtils.isNotEmpty(employeeList)) {
            for (Employee employee : employeeList) {
                if (StringUtils.isNotBlank(employee.getEmployeeMobileCipher())) {
                    String mobile = getDecryptMobile("employee_mobile_cipher", employee.getEmployeeMobileCipher());
                    mobileList.add(mobile);
                }
            }
        }
        // todo 这里调用j39发送短信
        sendSmsToMembers(mobileList);

    }

    @SoaReference("tqmq")
    private TqmqService tqmqService;

    @Value("${sms.url}")
    private String targetUrl;

    // 迁移成功给当前迁移机构的所有员工发送迁移成功短信
    public void sendSmsToMembers(List<String> mobiles) {
        if (CollectionUtils.isNotEmpty(mobiles)) {
            String[] mobileArr = mobiles.toArray(new String[mobiles.size()]);
            String content = "您所在的他趣直播公会登陆地址已经迁移到新网址：" + targetUrl;
            String tagCode = "gonghui_modify_notice";
            Integer appcode = 1;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("service", "businessSms");
            //jsonObject.put("method", "sendBatchSmsForAdvert");
            jsonObject.put("method", "processBusinessSmsList");
            JSONObject data = new JSONObject();
            //data.put("mobile", mobileArr);
            data.put("phoneArr", mobileArr);
            data.put("content", content);
            data.put("tagCode", tagCode);
            data.put("appcode", appcode);
            jsonObject.put("asyncforms", data);

            TqmqResponse response = tqmqService.push("mp_sms_async_invoke_queue", jsonObject.toJSONString(), 0L);
            if (StringUtils.isBlank(response.getMsgId())) {
                log.error("机构数据迁移通知短信发送失败，content={}", content);
            } else {
                log.info("机构数据迁移通知短信发送成功");
            }
        }
    }

    /**
     * 通过用户uuid调用j2获取用户信息
     *
     * @param uuid
     * @return
     */
    public Map<String, Object> getInfoByUuids(String uuid) {
        Map<String, Object> infoMap = null;
        if (StringUtils.isNotBlank(uuid)) {
            String[] uuidArr = {uuid};
            String[] fields = {"mobile", "account_name", "email"};
            Map<String, Map<String, Object>> map = infoService.getInfoByUuidsNoSecret(uuidArr, fields, null, false, false);
            if (MapUtils.isNotEmpty(map)) {
                infoMap = map.get(uuid);
            }
        }
        return infoMap;
    }


    @Autowired
    private LiveHostStatisticsDayMapper liveHostStatisticsDayMapper;
    @Autowired
    private LiveTeamStatisticsDayMapper liveTeamStatisticsDayMapper;
    @Autowired
    private OperationSpecialistMapper operationSpecialistMapper;

    /**
     * 迁移主播日统计数据到团队维度统计表
     */
    @Override
    public void moveTeamDailyStatistics() {
//        Long startTime = 20200101L;
//        Long endTime = 20210630L;
        Long startTime = 20200101L;
        Long endTime = 20200315L;
        reportHostStatisticsDay(startTime, endTime);
    }

    @Override
    public void updateAgentName() {
        List<Organization> organizations = organizationMapper.getALLByList();
        List<OperationSpecialist> operationSpecialists = operationSpecialistMapper.selectAll();
        if (CollectionUtils.isNotEmpty(organizations)) {
            for (Organization organization : organizations) {
                if (StringUtils.isNotEmpty(organization.getBusinessPerson())) {
                    List<OperationSpecialist> collect = operationSpecialists.stream().filter(item -> organization.getBusinessPerson().equals(String.valueOf(item.getId()))).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)) {
                        Organization organization1 = new Organization();
                        organization1.setOrgId(organization.getOrgId());
                        organization1.setBusinessPerson(collect.get(0).getName());
                        organizationMapper.updateByPrimaryKeySelective(organization1);
                    }

                }
            }
        }
    }


    /**
     * 递归历史每天的数据进行统计
     */
    public void reportHostStatisticsDay(Long startTime, Long endTime) {
        try {
            if (startTime <= endTime) {
                if (startTime == 20200110L) {
                    throw new ServiceException("test_exception", "测试异常");
                }
                reportLiveTeamStatisticsDay(startTime);
                reportHostStatisticsDay(LiveDateUtils.getAfterDay(String.valueOf(startTime)), endTime);
            }
        } catch (Exception e) {
            // 发生异常所有插入进行回滚
            liveTeamStatisticsDayMapper.deleteByDayTime(startTime, endTime);
            log.error("[ManangeOrgTransferServiceImpl.reportHostStatisticsDay]迁移主播日统计数据到团队维度统计表失败，失败原因：{}", e);
            throw new ServiceException("[ManangeOrgTransferServiceImpl.reportHostStatisticsDay]迁移主播日统计数据到团队维度统计表失败，失败原因：", e);
        }
    }


    /**
     * 统计团队维度主播每天直播数据
     */
    public void reportLiveTeamStatisticsDay(Long dayTime) {
        // 查询有效团队
        List<Team> teams = teamMapper.selectTeamList(null, null);
        if (CollectionUtils.isNotEmpty(teams)) {
            for (Team team : teams) {
                // 查询团队下的有效主播
                List<String> uuidList = teamMapper.findHostsByTeamId(team.getTeamId());
                // 获取团队维度主播天统计数据
                LiveCommonVo liveCommonVo = getLiveTeamStatisticsDay(uuidList, dayTime);
                liveCommonVo.setDayTime(dayTime);
                liveCommonVo.setTeamId(team.getTeamId());
                // 保存统计的数据到团队天统计表中去
                saveLiveTeamStatisticsDay(liveCommonVo);
            }
        }
    }

    /**
     * 获取团队维度主播天统计数据
     *
     * @param uuidList
     */
    private LiveCommonVo getLiveTeamStatisticsDay(List<String> uuidList, Long dayTime) {
        LiveCommonVo vo = null;
        if (CollectionUtils.isNotEmpty(uuidList)) {
            LiveCommonSearch search = new LiveCommonSearch();
            search.setUuidList(uuidList);
            search.setDayTime(dayTime);
            vo = liveHostStatisticsDayMapper.getData(search);
        } else {
            vo = new LiveCommonVo();
            vo.setAmount(0);
            vo.setFans(0);
            vo.setFlower(0);
            vo.setHostNum(0);
            vo.setMessage(0);
            vo.setSend(0);
            vo.setTotalLiveTime(0);
            vo.setViewer(0);
        }
        log.info("主播uuidList：{}，统计数据：{}", JsonUtils.objectToString(uuidList), JsonUtils.objectToString(vo));
        return vo;
    }

    /**
     * 保存统计的数据到团队天统计表中去
     *
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveLiveTeamStatisticsDay(LiveCommonVo vo) {
        LiveTeamStatisticsDay record = new LiveTeamStatisticsDay();
        record.setAmount(vo.getAmount());
        record.setDayTime(vo.getDayTime());
        record.setFans(vo.getFans());
        record.setFlower(vo.getFlower());
        record.setMessage(vo.getMessage());
        record.setHostNum(vo.getHostNum());
        record.setSend(vo.getSend());
        record.setViewer(vo.getViewer());
        record.setTotalLiveTime(vo.getTotalLiveTime());
        record.setTeamId(vo.getTeamId());
        record.setCreateTime(System.currentTimeMillis() / 1000);
        liveTeamStatisticsDayMapper.insert(record);
    }


    // ------------------------------------ 重写迁移代码 ---------------------------------------------


    /**
     * 以主播为角度迁移
     */
    public void transferHost(String orgUUid, MoveDto dto) {

        /**
         * 第一步所有迁移当前主播数量
         */
        GuildInfo guildInfo = guildInfoMapper.selectByUuid(Integer.valueOf(orgUUid));
        List<LiveHostInfo> hostInfoList = liveHostInfoMapper.getHostListByConsortiaId(Integer.valueOf(orgUUid));
        if (CollectionUtils.isEmpty(hostInfoList)) {
            log.info("公会uuid:{},迁移的主播迁移的数量为0", orgUUid);
            dto.setGuildInfo(guildInfo);
            return;
        }
        log.info("公会uuid:{},迁移的主播迁移的数量为:{}", orgUUid, hostInfoList.size());

        Map<String, List<LiveHostInfo>> hasAgentMap = new HashMap<>();
        List<LiveHostInfo> noAgenteList = new ArrayList<>();

        List<LiveHostInfo> hasAgenterHosts = hostInfoList.stream().filter(item -> cn.taqu.gonghui.common.utils.StringUtils.isNotEmpty(item.getBusinessmanUuid())).collect(Collectors.toList());

        Set<String> buiessmanUuids = hasAgenterHosts.stream().map(LiveHostInfo::getBusinessmanUuid).collect(Collectors.toSet());

        List<AgentManage> agentManages = null;
        if (CollectionUtils.isEmpty(buiessmanUuids)) {
            log.info("公会uuid:{},迁移的主播不存在经纪人", orgUUid);
            noAgenteList = hostInfoList;
        } else {
            agentManages = guildAgentManageMapper.selectListByAgentUuids(new ArrayList<>(buiessmanUuids));
            // 真正是这个公会的经纪人的uuid
            Set<String> agentUuids = new HashSet<>();
            agentManages.forEach(item -> {
                if (cn.taqu.gonghui.common.utils.StringUtils.equals(guildInfo.getAccountUuid(), item.getAccountUuid()) && !cn.taqu.gonghui.common.utils.StringUtils.equals(guildInfo.getAccountUuid(), item.getAgentUuid())) {
                    agentUuids.add(item.getAgentUuid());
                }
            });
            agentManages = agentManages.stream().filter(item -> !guildInfo.getAccountUuid().equals(item.getAgentUuid())).collect(Collectors.toList());
            Map<String, List<LiveHostInfo>> map = hasAgenterHosts.stream().collect(Collectors.groupingBy(LiveHostInfo::getBusinessmanUuid));
            // 剔除会长对应的主播
            if (map.containsKey(guildInfo.getAccountUuid())) {
                map.remove(guildInfo.getAccountUuid());
            }

            // 获取agent不存在的
            if (CollectionUtils.isNotEmpty(agentUuids)) {
                if (MapUtils.isNotEmpty(map)) {
                    Iterator<Map.Entry<String, List<LiveHostInfo>>> it = map.entrySet().iterator();
                    while (it.hasNext()) {
                        Map.Entry<String, List<LiveHostInfo>> next = it.next();
                        if (!agentUuids.contains(next.getKey())) {
                            it.remove();
                        }

                    }
                }
                if (MapUtils.isNotEmpty(map)) {
                    Set<String> set = map.keySet();
                    noAgenteList = hostInfoList.stream().filter(item -> !set.contains(item.getBusinessmanUuid())).collect(Collectors.toList());
                    List<AgentManage> collect = agentManages.stream().filter(item -> set.contains(item.getAgentUuid())).collect(Collectors.toList());
                    agentManages = collect;
                    hasAgentMap = map;
                } else {
                    noAgenteList = hostInfoList;
                }

            } else {
                noAgenteList = hostInfoList;
            }
        }


        dto.setGuildInfo(guildInfo);
        dto.setAgentManages(agentManages);
        dto.setHasAgentMap(hasAgentMap);
        dto.setNoAgentList(noAgenteList);

    }


    /**
     * 手动迁移用户前先进行校验
     */
    @Override
    public Map<String, String> handOperateValid(String orgUuid, Long orgId) {
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isBlank(orgUuid)) {
            map.put("code", "stop");
            map.put("msg", "请输入要迁出的公会");
            return map;
        }
        if (null == orgId) {
            map.put("code", "stop");
            map.put("msg", "请输入要迁入的机构");
            return map;
        }
        // 获取老公会信息
        GuildInfo guildInfo = guildInfoMapper.selectByUuid(Integer.valueOf(orgUuid));
        if (null == guildInfo || 0 == guildInfo.getGuildStatus()) {
            map.put("code", "stop");
            map.put("msg", "要迁出公会无效");
            return map;
        }
        // 校验迁入机构信息
        Organization organization = organizationMapper.selectByPrimaryKey(orgId);
        if (null == organization || 0 == organization.getOrgStatus()) {
            map.put("code", "stop");
            map.put("msg", "要迁入机构无效");
            return map;
        }
        // 此公会是否已经迁移过
        String accountUuid = guildInfo.getAccountUuid();
        if (StringUtils.isBlank(accountUuid)) {
            map.put("code", "stop");
            map.put("msg", "要迁出公会无效");
        } else {
            List<SysUser> userList = sysUserMapper.userListByAccountUuid(accountUuid);
            if (CollectionUtils.isNotEmpty(userList)) {
                map.put("code", "stop");
                map.put("msg", "该公会已经迁移");
            }
        }
        // 此公会是否已经迁移过
        Team returnTeam = teamMapper.selectByPrimaryKey(Long.valueOf(orgUuid));
        if (null != returnTeam) {
            map.put("code", "stop");
            map.put("msg", "该公会已经迁移");
        }
        map.put("code", "pass");
        map.put("msg", "校验通过");
        return map;
    }

    /**
     * 以主播为维度手动迁移用户
     * guild_info
     * live_host_info
     * employee
     * agent_manage
     */
    @Override
    public void handOperateAgentAndEmployee(String orgUuid, Long orgId) {
        if (StringUtils.isBlank(orgUuid)) {
            log.warn("请输入要迁出的机构uuid");
            throw new ServiceException("param_error", "请输入要迁出的机构uuid");
        }
        if (null == orgId) {
            log.warn("请输入要迁入的机构id");
            throw new ServiceException("param_error", "请输入要迁入的机构id");
        }
        MoveDto dto = new MoveDto();
        transferHost(orgUuid, dto);
        // 获取老公会信息
        GuildInfo guildInfo = dto.getGuildInfo();
        if (null == guildInfo || 0 == guildInfo.getGuildStatus()) {
            throw new ServiceException("invalid guild", "要迁出公会无效");
        }
        // 校验迁入机构信息
        Organization organization = organizationMapper.selectByPrimaryKey(orgId);
        if (null == organization || 0 == organization.getOrgStatus()) {
            log.warn("要迁入机构无效");
            throw new ServiceException("invalid organization", "要迁入机构无效");
        }
        // 此公会是否已经迁移过
        String accountUuid = guildInfo.getAccountUuid();
        if (StringUtils.isBlank(accountUuid)) {
            log.warn("要迁出公会无效");
            throw new ServiceException("invalid guild", "要迁出公会无效");
        } else {
            List<SysUser> userList = sysUserMapper.userListByAccountUuid(accountUuid);
            if (CollectionUtils.isNotEmpty(userList)) {
                log.warn("该公会已经迁移过");
                throw new ServiceException("move_stopped", "该公会已经迁移过");
            }
        }
        // 此公会是否已经迁移过
        Team returnTeam = teamMapper.selectByPrimaryKey(Long.valueOf(orgUuid));
        if (null != returnTeam) {
            log.warn("该公会已经迁移过");
            throw new ServiceException("move_stopped", "该公会已经迁移过");
        }
        // 为当前老公会创建默认团队
        Team team = new Team();
        team.setTeamId(Long.valueOf(guildInfo.getUuid()));
        team.setOrgId(orgId);
        team.setTeamName(guildInfo.getGuildName());
        team.setSignKey("liveTeam1");
        team.setType(1);
        team.setIsDefault(TeamDefaultEnum.UN_DEFAULT.getValue());
        team.setStatus(1);
        team.setCreateBy("admin");
        team.setCreateTime(System.currentTimeMillis() / 1000);
        team.setUpdateTime(System.currentTimeMillis() / 1000);
        team.setInviteCode("");
        teamMapper.insertTeam(team);
        // 获取直播类型经纪人角色 roleId
        Long agentRoleId = sysRoleMapper.getAgentRoleId();
        log.info("手动迁移获取agentRoleId：{}，公会uuid:{}", agentRoleId, orgUuid);
        if (null == agentRoleId || 0L == agentRoleId) {
            log.warn("直播业务类型经纪人角色不存在，请创建");
            throw new ServiceException("param_error", "直播业务类型经纪人角色不存在，请创建");
        }
        // 获取直播类型负责人角色 roleId
        Long leaderRoleId = sysRoleMapper.getLeaderRoleId();
        log.info("手动迁移获取leaderRoleId：{}，公会uuid:{}", leaderRoleId, orgUuid);
        if (null == leaderRoleId || 0L == leaderRoleId) {
            log.warn("直播业务类型负责人角色不存在，请创建");
            throw new ServiceException("param_error", "直播业务类型负责人角色不存在，请创建");
        }

        // 获取该机构下所有employee
        employees(orgUuid, dto);
        // 设置相关信息
        dto.setOrgId(organization.getOrgId());
        dto.setOrgName(organization.getOrgName());
        dto.setTeamId(team.getTeamId());
        dto.setAgentRoleId(agentRoleId);
        dto.setLeaderRoleId(leaderRoleId);

        log.info("获取到公会信息：{}，公会uuid:{}", JsonUtils.objectToString(dto.getGuildInfo()), dto.getGuildInfo().getUuid());
        log.info("获取到无经纪人主播列表：{}，公会uuid:{}", JsonUtils.objectToString(dto.getNoAgentList()), dto.getGuildInfo().getUuid());
        log.info("获取到有经纪人主播列表：{}，公会uuid:{}", JsonUtils.objectToString(dto.getHasAgentMap()), dto.getGuildInfo().getUuid());
        log.info("获取到经纪人列表：{}，公会uuid:{}", JsonUtils.objectToString(dto.getAgentManages()), dto.getGuildInfo().getUuid());
        log.info("获取到职员列表：{}，公会uuid:{}", JsonUtils.objectToString(dto.getEmployeeList()), dto.getGuildInfo().getUuid());
        // 迁移employee,agent_manage
        handMoveByHost(dto);
        // 迁移成功发送短信通知给机构所有员工
//        sendSmsMessage(dto);
    }


    public void handMoveByHost(MoveDto dto) {
        List<AgentManage> agentManageListRecord = dto.getAgentManages();
        List<Employee> employeeList = dto.getEmployeeList();
        Map<String, List<LiveHostInfo>> hostMap = dto.getHasAgentMap();
        List<LiveHostInfo> hostListNoAgent = dto.getNoAgentList();
        GuildInfo guildInfo = dto.getGuildInfo();
        Long orgId = dto.getOrgId();
        String orgName = dto.getOrgName();
        Long teamId = dto.getTeamId();
        Long agentRoleId = dto.getAgentRoleId();
        Long leaderRoleId = dto.getLeaderRoleId();

        Set<Long> sysUserRoleSet = new HashSet<>();
        Set<Long> sysUserSet = new HashSet<>();
        Set<Long> teamEmployeeSet = new HashSet<>();

        // 过滤agent_manage
        List<AgentManage> agentManageList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(agentManageListRecord)) {
            for (AgentManage agent : agentManageListRecord) {
                if (StringUtils.isNotBlank(agent.getAgentUuid()) && StringUtils.isNotBlank(guildInfo.getAccountUuid())) {
                    if (guildInfo.getAccountUuid().equals(agent.getAccountUuid()) && !guildInfo.getAccountUuid().equals(agent.getAgentUuid())) {
                        agentManageList.add(agent);
                    } else {
                        log.info("手动迁移agentManage当前经纪人不属于这个公会或者当前经纪人也是会长，经纪人uuid：{}，会长uuid：{}，公会uuid：{}", agent.getAgentUuid(), guildInfo.getAccountUuid(), guildInfo.getUuid());
                    }
                } else {
                    log.info("手动迁移经纪人uuid或者会长account_uuid为空，经纪人uuid：{}，会长uuid：{}，公会uuid：{}", agent.getAgentUuid(), guildInfo.getAccountUuid(), guildInfo.getUuid());
                }
            }
        }
        try {
            // 迁移guild_info表会长 - 老公会会长角色迁移后变成团队负责人角色
            if (StringUtils.isNotBlank(guildInfo.getAccountUuid())) {
                // 判断会长是否已经存在
                List<SysUser> userList = sysUserMapper.userIsHasExisted(guildInfo.getAccountUuid());
                if (CollectionUtils.isEmpty(userList)) {
                    // 先新增sys_user表
                    SysUser sysUserRecord = new SysUser();
                    sysUserRecord.setOrgId(orgId);
                    sysUserRecord.setOrgName(orgName);
                    sysUserRecord.setAccountUuid(guildInfo.getAccountUuid());

                    // 调用j2获取手机号码和名称
                    String mobile = "";
                    String accountName = "";
                    Map<String, Object> userInfo = getInfoByUuids(guildInfo.getAccountUuid());
                    if (MapUtils.isNotEmpty(userInfo)) {
                        mobile = (String) userInfo.get("mobile");
                        accountName = (String) userInfo.get("account_name");
                    }
                    sysUserRecord.setUserName(accountName);
                    sysUserRecord.setMobile(mobile);

                    sysUserRecord.setUserType(UserTypeEnum.LEADER.getType());
                    sysUserRecord.setStatus(UserStatus.OK.getCode());
                    sysUserRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
                    sysUserRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
                    sysUserMapper.insert(sysUserRecord);
                    // 对已经成功插入到sys_user表的数据id备份
                    sysUserSet.add(sysUserRecord.getUserId());
                    // 其次新增sys_user_role表
                    SysUserRole sysUserRoleRecord = new SysUserRole();
                    sysUserRoleRecord.setRoleId(leaderRoleId);
                    sysUserRoleRecord.setUserId(sysUserRecord.getUserId());
                    sysUserRoleRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
                    sysUserRoleRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
                    sysUserRoleMapper.insert(sysUserRoleRecord);
                    // 对已经成功插入到sys_user_role表的数据id备份
                    sysUserRoleSet.add(sysUserRoleRecord.getId());
                    // 后新增team_employee表
                    TeamEmployee teamEmployeeRecord = new TeamEmployee();
                    teamEmployeeRecord.setOrgId(orgId);
                    teamEmployeeRecord.setOrgName(orgName);
                    teamEmployeeRecord.setTeamId(teamId);
                    teamEmployeeRecord.setUserId(sysUserRecord.getUserId());

                    teamEmployeeRecord.setMobile(mobile);
                    teamEmployeeRecord.setEmployeeName(accountName);

                    teamEmployeeRecord.setType(UserTypeEnum.LEADER.getType());
                    teamEmployeeRecord.setInviteCode(null);
                    teamEmployeeRecord.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
                    teamEmployeeRecord.setInviteTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
                    teamEmployeeRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
                    teamEmployeeRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
                    teamEmployeeMapper.insert(teamEmployeeRecord);
                    teamEmployeeSet.add(teamEmployeeRecord.getEmployeeId());
                } else {
                    log.info("手动迁移会长在用户表中已经存在，用户信息：{}，公会信息：{}", JsonUtils.objectToString(userList), JsonUtils.objectToString(guildInfo));
                }
            } else {
                log.info("手动迁移agentManage当前公会会长无效，公会信息：{}", JsonUtils.objectToString(guildInfo));
            }

            // 新增无经纪人主播
            if (CollectionUtils.isNotEmpty(hostListNoAgent)) {
                List<TeamHost> list = new ArrayList<>();
                for (LiveHostInfo host : hostListNoAgent) {
                    if (StringUtils.isBlank(host.getHostUuid())) {
                        log.info("手动迁移agentManage当前主播uuid为空，公会信息:{}", JsonUtils.objectToString(guildInfo));
                        continue;
                    }
                    TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
                    if (null != oneByHostUuid) {
                        log.info("手动迁移当前主播已经存在，公会信息:{}，主播信息：{}", JsonUtils.objectToString(guildInfo), JsonUtils.objectToString(oneByHostUuid));
                        // 将已经存在得主播teamId orgId 修改为当前公会
                        TeamHost teamHost = new TeamHost();
                        teamHost.setHostUuid(host.getHostUuid());
                        teamHost.setOrgId(orgId);
                        teamHost.setTeamId(teamId);
                        teamHostMapper.updateByHostUuid(teamHost);
                        continue;
                    }
                    TeamHost teamHost = new TeamHost();
                    teamHost.setHostUuid(host.getHostUuid());
                    teamHost.setOrgId(orgId);
                    teamHost.setTeamId(teamId);
                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setStatus(1);
                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setCurrentSharingProfitRate("0");
                    teamHost.setNewSharingProfitRate("");
                    teamHost.setIsUpdate(0);
                    list.add(teamHost);
                }
                // 批量插入
                if (CollectionUtils.isNotEmpty(list)) {
                    teamHostMapper.batchInsertHost(list);
                }
            }


            // 迁移agent_manage表
            if (CollectionUtils.isNotEmpty(agentManageList)) {
                for (AgentManage agent : agentManageList) {
                    // 判断非空
                    if (StringUtils.isBlank(agent.getAgentUuid())) {
                        log.info("手动迁移agentManage当前经纪人uuid为空，公会信息：{}，经纪人信息：{}", JsonUtils.objectToString(guildInfo), JsonUtils.objectToString(agent));
                        continue;
                    }
                    // 判断当前经纪人是否属于这个公会并且当前经纪人不是会长
//                    if (!guildInfo.getAccountUuid().equals(agent.getAccountUuid()) || guildInfo.getAccountUuid().equals(agent.getAgentUuid())) {
//                        log.info("手动迁移agentManage当前经纪人不属于这个公会或者当前经纪人也是会长，经纪人uuid：{}，会长uuid：{}，公会uuid：{}",agent.getAgentUuid(),guildInfo.getAccountUuid(), guildInfo.getUuid());
//                        // 新增team_host表
//                        if (MapUtils.isNotEmpty(hostMap)) {
//                            List<LiveHostInfo> liveHostInfoList = hostMap.get(agent.getAgentUuid());
//                            if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                                List<TeamHost> list = new ArrayList<>();
//                                for (LiveHostInfo host : liveHostInfoList) {
//                                    if (StringUtils.isBlank(host.getHostUuid())) {
//                                        log.info("手动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                                        continue;
//                                    }
//                                    TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                                    if (null != oneByHostUuid) {
//                                        log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                                        continue;
//                                    }
//                                    TeamHost teamHost = new TeamHost();
//                                    teamHost.setHostUuid(host.getHostUuid());
//                                    teamHost.setOrgId(orgId);
//                                    teamHost.setTeamId(teamId);
//                                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setStatus(1);
//                                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setCurrentSharingProfitRate("0");
//                                    teamHost.setNewSharingProfitRate("");
//                                    teamHost.setIsUpdate(0);
//                                    list.add(teamHost);
//                                }
//                                // 批量插入
//                                if (CollectionUtils.isNotEmpty(list)) {
//                                    teamHostMapper.batchInsertHost(list);
//                                }
//                            }
//                        }
//                        continue;
//                    }
                    List<SysUser> userList = sysUserMapper.userIsHasExisted(agent.getAgentUuid());
                    if (CollectionUtils.isNotEmpty(userList)) {
                        log.info("手动迁移agentManage当前经纪人在用户表中已经存在,用户信息：{}，公会信息：{}", JsonUtils.objectToString(userList), JsonUtils.objectToString(guildInfo));
                        continue;
                    }
                    // 通过uuid获取用户电话号码
                    String mobile = "";
                    Map<String, Object> infoByUuids = getInfoByUuids(agent.getAgentUuid());
                    if (MapUtils.isEmpty(infoByUuids) || ObjectUtils.isEmpty(infoByUuids.get("mobile"))) {
                        log.info("手动迁移agentManage当前用户手机号码为空，用户信息：{}，公会信息", JsonUtils.objectToString(agent), JsonUtils.objectToString(guildInfo));
                        mobile = "***********";
                    } else {
                        mobile = String.valueOf(infoByUuids.get("mobile"));
                    }
                    // 先新增sys_user表
                    SysUser sysUser = new SysUser();
                    sysUser.setOrgId(orgId);
                    sysUser.setOrgName(orgName);
                    sysUser.setAccountUuid(agent.getAgentUuid());
                    sysUser.setUserName(agent.getAgentName());
                    sysUser.setUserType(UserTypeEnum.AGENTER.getType());
                    sysUser.setMobile(mobile);
                    sysUser.setStatus(UserStatus.OK.getCode());
                    sysUser.setCreateTime(agent.getCreateTime());
                    sysUser.setUpdateTime(agent.getUpdateTime());
                    sysUserMapper.insert(sysUser);
                    sysUserSet.add(sysUser.getUserId());
                    // 其次新增sys_user_role表
                    SysUserRole sysUserRole = new SysUserRole();
                    sysUserRole.setRoleId(agentRoleId);
                    sysUserRole.setUserId(sysUser.getUserId());
                    sysUserRole.setCreateTime(agent.getCreateTime());
                    sysUserRole.setUpdateTime(agent.getUpdateTime());
                    sysUserRoleMapper.insert(sysUserRole);
                    // 对已经成功插入到sys_user_role表的数据id备份
                    sysUserRoleSet.add(sysUserRole.getId());
                    // 后新增team_employee表
                    TeamEmployee teamEmployee = new TeamEmployee();
                    teamEmployee.setOrgId(orgId);
                    teamEmployee.setOrgName(orgName);
                    teamEmployee.setTeamId(teamId);
                    teamEmployee.setUserId(sysUser.getUserId());
                    teamEmployee.setMobile(mobile);
                    teamEmployee.setEmployeeName(agent.getAgentName());
                    teamEmployee.setType(UserTypeEnum.AGENTER.getType());
                    teamEmployee.setInviteCode(agent.getInvitationCode());
                    teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
                    teamEmployee.setInviteTime(agent.getCreateTime());
                    teamEmployee.setCreateTime(agent.getCreateTime());
                    teamEmployee.setUpdateTime(agent.getUpdateTime());
                    teamEmployeeMapper.insert(teamEmployee);
                    teamEmployeeSet.add(teamEmployee.getEmployeeId());
                    // 最后新增team_host表
                    if (MapUtils.isNotEmpty(hostMap)) {
                        List<LiveHostInfo> liveHostInfoList = hostMap.get(agent.getAgentUuid());
                        if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
                            List<TeamHost> list = new ArrayList<>();
                            for (LiveHostInfo host : liveHostInfoList) {
                                if (StringUtils.isBlank(host.getHostUuid())) {
                                    log.info("当前主播uuid为空，公会信息:{}", JsonUtils.objectToString(guildInfo));
                                    continue;
                                }
                                TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
                                if (null != oneByHostUuid) {
                                    log.info("手动迁移当前主播已经存在，公会信息:{}，主播信息：{}", JsonUtils.objectToString(guildInfo), JsonUtils.objectToString(oneByHostUuid));
                                    // 将已经存在得主播teamId orgId 修改为当前公会
                                    TeamHost teamHost = new TeamHost();
                                    teamHost.setHostUuid(host.getHostUuid());
                                    teamHost.setOrgId(orgId);
                                    teamHost.setTeamId(teamId);
                                    teamHostMapper.updateByHostUuid(teamHost);
                                    continue;
                                }
                                TeamHost teamHost = new TeamHost();
                                teamHost.setHostUuid(host.getHostUuid());
                                teamHost.setOrgId(orgId);
                                teamHost.setTeamId(teamId);
                                teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
                                teamHost.setEmployeeId(teamEmployee.getEmployeeId());
                                teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                teamHost.setStatus(1);
                                teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                teamHost.setCurrentSharingProfitRate("0");
                                teamHost.setNewSharingProfitRate("");
                                teamHost.setIsUpdate(0);
                                list.add(teamHost);
                            }
                            // 批量插入
                            if (CollectionUtils.isNotEmpty(list)) {
                                teamHostMapper.batchInsertHost(list);
                            }
                        }
                    }
                }
            }
            // 迁移employee表
            if (CollectionUtils.isNotEmpty(employeeList)) {
                for (Employee employee : employeeList) {
                    if (StringUtils.isBlank(employee.getEmployeeUuid())) {
                        log.info("手动迁移employee当前职员uuid为空，公会信息：{}，职员信息：{}", JsonUtils.objectToString(guildInfo), JsonUtils.objectToString(employee));
                        continue;
                    }
                    List<SysUser> userList = sysUserMapper.userIsHasExisted(employee.getEmployeeUuid());
                    if (CollectionUtils.isNotEmpty(userList)) {
                        log.info("手动迁移employee用户信息已经存在，公会信息：{}，用户信息：{}", JsonUtils.objectToString(guildInfo), JsonUtils.objectToString(userList));
                        continue;
                    }
                    // 通过uuid获取用户电话号码
                    String mobile = "";
                    Map<String, Object> infoByUuids = getInfoByUuids(employee.getEmployeeUuid());
                    if (MapUtils.isEmpty(infoByUuids) || ObjectUtils.isEmpty(infoByUuids.get("mobile"))) {
                        log.info("手动迁移employee当前用户手机号码为空，用户信息：{}，公会信息", JsonUtils.objectToString(employee), JsonUtils.objectToString(guildInfo));
                        mobile = "***********";
                    } else {
                        mobile = String.valueOf(infoByUuids.get("mobile"));
                    }
                    // 先新增sys_user表
                    SysUser sysUser = new SysUser();
                    sysUser.setOrgId(orgId);
                    sysUser.setOrgName(orgName);
                    sysUser.setAccountUuid(employee.getEmployeeUuid());
                    sysUser.setUserName(employee.getEmployeeName());
                    sysUser.setUserType(UserTypeEnum.AGENTER.getType());
                    // 解密
                    sysUser.setMobile(mobile);
                    sysUser.setStatus(UserStatus.OK.getCode());
                    sysUser.setCreateTime(employee.getCreateTime());
                    sysUser.setUpdateTime(employee.getUpdateTime());
                    sysUserMapper.insert(sysUser);
                    sysUserSet.add(sysUser.getUserId());
                    // 其次新增sys_user_role表
                    SysUserRole sysUserRole = new SysUserRole();
                    sysUserRole.setRoleId(agentRoleId);
                    sysUserRole.setUserId(sysUser.getUserId());
                    sysUserRole.setCreateTime(employee.getCreateTime());
                    sysUserRole.setUpdateTime(employee.getUpdateTime());
                    sysUserRoleMapper.insert(sysUserRole);
                    // 对已经成功插入到sys_user_role表的数据id备份
                    sysUserRoleSet.add(sysUserRole.getId());
                    // 后新增team_employee表
                    TeamEmployee teamEmployee = new TeamEmployee();
                    teamEmployee.setOrgId(orgId);
                    teamEmployee.setOrgName(orgName);
                    teamEmployee.setTeamId(teamId);
                    teamEmployee.setUserId(sysUser.getUserId());
                    // 解密
                    teamEmployee.setMobile(mobile);
                    teamEmployee.setEmployeeName(employee.getEmployeeName());
                    teamEmployee.setType(UserTypeEnum.AGENTER.getType());
                    teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
                    teamEmployee.setInviteTime(employee.getCreateTime());
                    teamEmployee.setCreateTime(employee.getCreateTime());
                    teamEmployee.setUpdateTime(employee.getUpdateTime());
                    teamEmployeeMapper.insert(teamEmployee);
                    teamEmployeeSet.add(teamEmployee.getEmployeeId());
                }
            }
        } catch (Exception e) {
            // 发生异常回滚
            // 删除sys_user表中已经插入的当前orgId下所有用户
            if (CollectionUtils.isNotEmpty(sysUserSet)) {
                sysUserMapper.batchDeleteByIds(sysUserSet);
            }
            // 删除sys_user_role表中已经插入的所有关联数据
            if (CollectionUtils.isNotEmpty(sysUserRoleSet)) {
                sysUserRoleMapper.batchDeleteByIds(sysUserRoleSet);
            }
            // 删除team_employee表中已经插入的当前orgId下所有用户
            if (CollectionUtils.isNotEmpty(teamEmployeeSet)) {
                teamEmployeeMapper.batchDeleteByIds(teamEmployeeSet);
            }
            // 删除team_host表中已经插入的当前orgId下所有主播
            teamHostMapper.deleteByTeamId(teamId);
            // 删除team表新增的团队
            teamMapper.deleteByPrimaryKey(teamId);
            log.error("move fail - 数据迁移失败，失败原因：{},失败机构id：{}", e, orgId);
            throw new ServiceException("move_fail - 数据迁移失败，失败原因：{}", e);

        }
    }

//    public void handMoveByHost(MoveDto dto){
//        List<AgentManage> agentManageList = dto.getAgentManages();
//        List<Employee> employeeList = dto.getEmployeeList();
//        Map<String, List<LiveHostInfo>> hostMap = dto.getHasAgentMap();
//        List<LiveHostInfo> hostListNoAgent = dto.getNoAgentList();
//        GuildInfo guildInfo = dto.getGuildInfo();
//        Long orgId = dto.getOrgId();
//        String orgName = dto.getOrgName();
//        Long teamId = dto.getTeamId();
//        Long agentRoleId = dto.getAgentRoleId();
//        Long leaderRoleId = dto.getLeaderRoleId();
//
//        Set<Long> sysUserRoleSet = new HashSet<>();
//        Set<Long> sysUserSet = new HashSet<>();
//        Set<Long> teamEmployeeSet = new HashSet<>();
//        try {
//            // 迁移guild_info表会长 - 老公会会长角色迁移后变成团队负责人角色
//            if (StringUtils.isNotBlank(guildInfo.getAccountUuid())) {
//                // 判断会长是否已经存在
//                List<SysUser> userList = sysUserMapper.userIsHasExisted(guildInfo.getAccountUuid());
//                if (CollectionUtils.isEmpty(userList)) {
//                    // 先新增sys_user表
//                    SysUser sysUserRecord = new SysUser();
//                    sysUserRecord.setOrgId(orgId);
//                    sysUserRecord.setOrgName(orgName);
//                    sysUserRecord.setAccountUuid(guildInfo.getAccountUuid());
//
//                    // 调用j2获取手机号码和名称
//                    String mobile = "";
//                    String accountName = "";
//                    Map<String, Object> userInfo = getInfoByUuids(guildInfo.getAccountUuid());
//                    if (MapUtils.isNotEmpty(userInfo)) {
//                        mobile = (String)userInfo.get("mobile");
//                        accountName = (String)userInfo.get("account_name");
//                    }
//                    sysUserRecord.setUserName(accountName);
//                    sysUserRecord.setMobile(mobile);
//
//                    sysUserRecord.setUserType(UserTypeEnum.LEADER.getType());
//                    sysUserRecord.setStatus(UserStatus.OK.getCode());
//                    sysUserRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
//                    sysUserRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
//                    sysUserMapper.insert(sysUserRecord);
//                    // 对已经成功插入到sys_user表的数据id备份
//                    sysUserSet.add(sysUserRecord.getUserId());
//                    // 其次新增sys_user_role表
//                    SysUserRole sysUserRoleRecord = new SysUserRole();
//                    sysUserRoleRecord.setRoleId(leaderRoleId);
//                    sysUserRoleRecord.setUserId(sysUserRecord.getUserId());
//                    sysUserRoleRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
//                    sysUserRoleRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
//                    sysUserRoleMapper.insert(sysUserRoleRecord);
//                    // 对已经成功插入到sys_user_role表的数据id备份
//                    sysUserRoleSet.add(sysUserRoleRecord.getId());
//                    // 后新增team_employee表
//                    TeamEmployee teamEmployeeRecord = new TeamEmployee();
//                    teamEmployeeRecord.setOrgId(orgId);
//                    teamEmployeeRecord.setOrgName(orgName);
//                    teamEmployeeRecord.setTeamId(teamId);
//                    teamEmployeeRecord.setUserId(sysUserRecord.getUserId());
//
//                    teamEmployeeRecord.setMobile(mobile);
//                    teamEmployeeRecord.setEmployeeName(accountName);
//
//                    teamEmployeeRecord.setType(UserTypeEnum.LEADER.getType());
//                    teamEmployeeRecord.setInviteCode(null);
//                    teamEmployeeRecord.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
//                    teamEmployeeRecord.setInviteTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
//                    teamEmployeeRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
//                    teamEmployeeRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
//                    teamEmployeeMapper.insert(teamEmployeeRecord);
//                    teamEmployeeSet.add(teamEmployeeRecord.getEmployeeId());
//                } else {
//                    log.info("手动迁移会长在用户表中已经存在，用户信息：{}，公会信息：{}",JsonUtils.objectToString(userList),JsonUtils.objectToString(guildInfo));
//                }
//            } else {
//                log.info("手动迁移agentManage当前公会会长无效，公会信息：{}",JsonUtils.objectToString(guildInfo));
//            }
//
//            // 新增无经纪人主播
//            if (CollectionUtils.isNotEmpty(hostListNoAgent)) {
//                List<TeamHost> list = new ArrayList<>();
//                for (LiveHostInfo host : hostListNoAgent) {
//                    if (StringUtils.isBlank(host.getHostUuid())) {
//                        log.info("手动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                        continue;
//                    }
//                    TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                    if (null != oneByHostUuid) {
//                        log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                        continue;
//                    }
//                    TeamHost teamHost = new TeamHost();
//                    teamHost.setHostUuid(host.getHostUuid());
//                    teamHost.setOrgId(orgId);
//                    teamHost.setTeamId(teamId);
//                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                    teamHost.setStatus(1);
//                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                    teamHost.setCurrentSharingProfitRate("0");
//                    teamHost.setNewSharingProfitRate("");
//                    teamHost.setIsUpdate(0);
//                    list.add(teamHost);
//                }
//                // 批量插入
//                if (CollectionUtils.isNotEmpty(list)) {
//                    teamHostMapper.batchInsertHost(list);
//                }
//            }
//
//
//            // 迁移agent_manage表
//            if (MapUtils.isNotEmpty(hostMap)) {
//                Map<String, AgentManage> agentManageMap = null;
//                if (CollectionUtils.isNotEmpty(agentManageList)) {
//                    agentManageMap = agentManageList.stream().collect(Collectors.toMap(AgentManage::getAgentUuid, Function.identity(), (key1, key2) -> key2));
//                }
//                for (Map.Entry<String, List<LiveHostInfo>> entry : hostMap.entrySet()) {
//                    if (MapUtils.isNotEmpty(agentManageMap)) {
//                        AgentManage agent = agentManageMap.get(entry.getKey());
//                        if (null != agent) {
//                            // 判断当前经纪人是否属于这个公会并且当前经纪人不是会长
//                            if (guildInfo.getAccountUuid().equals(agent.getAccountUuid()) && !guildInfo.getAccountUuid().equals(agent.getAgentUuid())) {
//                                // 判断当前经纪人是否已经迁移
//                                List<SysUser> userList = sysUserMapper.userIsHasExisted(agent.getAgentUuid());
//                                if (CollectionUtils.isNotEmpty(userList)) {
//                                    log.info("自动迁移agentManage当前经纪人在用户表中已经存在：{}",JsonUtils.objectToString(userList));
//                                    continue;
//                                }
//                                // 先新增sys_user表
//                                SysUser sysUser = new SysUser();
//                                sysUser.setOrgId(orgId);
//                                sysUser.setOrgName(orgName);
//                                sysUser.setAccountUuid(agent.getAgentUuid());
//                                sysUser.setUserName(agent.getAgentName());
//                                sysUser.setUserType(UserTypeEnum.AGENTER.getType());
//                                sysUser.setMobile(agent.getPhone());
//                                sysUser.setStatus(UserStatus.OK.getCode());
//                                sysUser.setCreateTime(agent.getCreateTime());
//                                sysUser.setUpdateTime(agent.getUpdateTime());
//                                sysUserMapper.insert(sysUser);
//                                sysUserSet.add(sysUser.getUserId());
//                                // 其次新增sys_user_role表
//                                SysUserRole sysUserRole = new SysUserRole();
//                                sysUserRole.setRoleId(agentRoleId);
//                                sysUserRole.setUserId(sysUser.getUserId());
//                                sysUserRole.setCreateTime(agent.getCreateTime());
//                                sysUserRole.setUpdateTime(agent.getUpdateTime());
//                                sysUserRoleMapper.insert(sysUserRole);
//                                sysUserRoleSet.add(sysUserRole.getId());
//                                // 后新增team_employee表
//                                TeamEmployee teamEmployee = new TeamEmployee();
//                                teamEmployee.setOrgId(orgId);
//                                teamEmployee.setOrgName(orgName);
//                                teamEmployee.setTeamId(teamId);
//                                teamEmployee.setUserId(sysUser.getUserId());
//                                teamEmployee.setMobile(agent.getPhone());
//                                teamEmployee.setEmployeeName(agent.getAgentName());
//                                teamEmployee.setType(UserTypeEnum.AGENTER.getType());
//                                teamEmployee.setInviteCode(agent.getInvitationCode());
//                                teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
//                                teamEmployee.setInviteTime(agent.getCreateTime());
//                                teamEmployee.setCreateTime(agent.getCreateTime());
//                                teamEmployee.setUpdateTime(agent.getUpdateTime());
//                                teamEmployeeMapper.insert(teamEmployee);
//                                teamEmployeeSet.add(teamEmployee.getEmployeeId());
//
//                                List<LiveHostInfo> liveHostInfoList = entry.getValue();
//                                if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                                    List<TeamHost> list = new ArrayList<>();
//                                    for (LiveHostInfo host : liveHostInfoList) {
//                                        if (StringUtils.isBlank(host.getHostUuid())) {
//                                            log.info("自动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                                            continue;
//                                        }
//                                        TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                                        if (null != oneByHostUuid) {
//                                            log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                                            continue;
//                                        }
//                                        TeamHost teamHost = new TeamHost();
//                                        teamHost.setHostUuid(host.getHostUuid());
//                                        teamHost.setOrgId(orgId);
//                                        teamHost.setTeamId(teamId);
//                                        teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                        teamHost.setEmployeeId(teamEmployee.getEmployeeId());
//                                        teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setStatus(1);
//                                        teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setCurrentSharingProfitRate("0");
//                                        teamHost.setNewSharingProfitRate("");
//                                        teamHost.setIsUpdate(0);
//                                        list.add(teamHost);
//                                    }
//                                    // 批量插入
//                                    if (CollectionUtils.isNotEmpty(list)) {
//                                        teamHostMapper.batchInsertHost(list);
//                                    }
//                                }
//                            } else {
//                                log.info("自动迁移agentManage当前经纪人不属于这个公会或者当前经纪人也是会长，经纪人uuid：{}，会长uuid：{}，公会uuid：{}",agent.getAgentUuid(),guildInfo.getAccountUuid(), guildInfo.getUuid());
//                                List<LiveHostInfo> liveHostInfoList = entry.getValue();
//                                if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                                    List<TeamHost> list = new ArrayList<>();
//                                    for (LiveHostInfo host : liveHostInfoList) {
//                                        if (StringUtils.isBlank(host.getHostUuid())) {
//                                            log.info("自动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                                            continue;
//                                        }
//                                        TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                                        if (null != oneByHostUuid) {
//                                            log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                                            continue;
//                                        }
//                                        TeamHost teamHost = new TeamHost();
//                                        teamHost.setHostUuid(host.getHostUuid());
//                                        teamHost.setOrgId(orgId);
//                                        teamHost.setTeamId(teamId);
//                                        teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                        teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setStatus(1);
//                                        teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setCurrentSharingProfitRate("0");
//                                        teamHost.setNewSharingProfitRate("");
//                                        teamHost.setIsUpdate(0);
//                                        list.add(teamHost);
//                                    }
//                                    // 批量插入
//                                    if (CollectionUtils.isNotEmpty(list)) {
//                                        teamHostMapper.batchInsertHost(list);
//                                    }
//                                }
//                            }
//                        } else {
//                            List<LiveHostInfo> liveHostInfoList = entry.getValue();
//                            if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                                List<TeamHost> list = new ArrayList<>();
//                                for (LiveHostInfo host : liveHostInfoList) {
//                                    if (StringUtils.isBlank(host.getHostUuid())) {
//                                        log.info("自动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                                        continue;
//                                    }
//                                    TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                                    if (null != oneByHostUuid) {
//                                        log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                                        continue;
//                                    }
//                                    TeamHost teamHost = new TeamHost();
//                                    teamHost.setHostUuid(host.getHostUuid());
//                                    teamHost.setOrgId(orgId);
//                                    teamHost.setTeamId(teamId);
//                                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setStatus(1);
//                                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setCurrentSharingProfitRate("0");
//                                    teamHost.setNewSharingProfitRate("");
//                                    teamHost.setIsUpdate(0);
//                                    list.add(teamHost);
//                                }
//                                // 批量插入
//                                if (CollectionUtils.isNotEmpty(list)) {
//                                    teamHostMapper.batchInsertHost(list);
//                                }
//                            }
//                        }
//                    } else {
//                        List<LiveHostInfo> liveHostInfoList = entry.getValue();
//                        if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                            List<TeamHost> list = new ArrayList<>();
//                            for (LiveHostInfo host : liveHostInfoList) {
//                                if (StringUtils.isBlank(host.getHostUuid())) {
//                                    log.info("自动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                                    continue;
//                                }
//                                TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                                if (null != oneByHostUuid) {
//                                    log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                                    continue;
//                                }
//                                TeamHost teamHost = new TeamHost();
//                                teamHost.setHostUuid(host.getHostUuid());
//                                teamHost.setOrgId(orgId);
//                                teamHost.setTeamId(teamId);
//                                teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                teamHost.setStatus(1);
//                                teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                teamHost.setCurrentSharingProfitRate("0");
//                                teamHost.setNewSharingProfitRate("");
//                                teamHost.setIsUpdate(0);
//                                list.add(teamHost);
//                            }
//                            // 批量插入
//                            if (CollectionUtils.isNotEmpty(list)) {
//                                teamHostMapper.batchInsertHost(list);
//                            }
//                        }
//                    }
//                }
//            }
//            // 迁移employee表
//            if (CollectionUtils.isNotEmpty(employeeList)) {
//                for (Employee employee : employeeList) {
//                    if (StringUtils.isBlank(employee.getEmployeeUuid())) {
//                        log.info("手动迁移employee当前职员uuid为空，公会信息：{}，职员信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(employee));
//                        continue;
//                    }
//                    List<SysUser> userList = sysUserMapper.userIsHasExisted(employee.getEmployeeUuid());
//                    if (CollectionUtils.isNotEmpty(userList)) {
//                        log.info("手动迁移employee用户信息已经存在，公会信息：{}，用户信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(userList));
//                        continue;
//                    }
//                    // 先新增sys_user表
//                    SysUser sysUser = new SysUser();
//                    sysUser.setOrgId(orgId);
//                    sysUser.setOrgName(orgName);
//                    sysUser.setAccountUuid(employee.getEmployeeUuid());
//                    sysUser.setUserName(employee.getEmployeeName());
//                    sysUser.setUserType(UserTypeEnum.AGENTER.getType());
//                    // 解密
//                    sysUser.setMobile(getDecryptMobile("employee_mobile_cipher", employee.getEmployeeMobileCipher()));
//                    sysUser.setStatus(UserStatus.OK.getCode());
//                    sysUser.setCreateTime(employee.getCreateTime());
//                    sysUser.setUpdateTime(employee.getUpdateTime());
//                    sysUserMapper.insert(sysUser);
//                    sysUserSet.add(sysUser.getUserId());
//                    // 其次新增sys_user_role表
//                    SysUserRole sysUserRole = new SysUserRole();
//                    sysUserRole.setRoleId(agentRoleId);
//                    sysUserRole.setUserId(sysUser.getUserId());
//                    sysUserRole.setCreateTime(employee.getCreateTime());
//                    sysUserRole.setUpdateTime(employee.getUpdateTime());
//                    sysUserRoleMapper.insert(sysUserRole);
//                    // 对已经成功插入到sys_user_role表的数据id备份
//                    sysUserRoleSet.add(sysUserRole.getId());
//                    // 后新增team_employee表
//                    TeamEmployee teamEmployee = new TeamEmployee();
//                    teamEmployee.setOrgId(orgId);
//                    teamEmployee.setOrgName(orgName);
//                    teamEmployee.setTeamId(teamId);
//                    teamEmployee.setUserId(sysUser.getUserId());
//                    // 解密
//                    teamEmployee.setMobile(getDecryptMobile("employee_mobile_cipher", employee.getEmployeeMobileCipher()));
//                    teamEmployee.setEmployeeName(employee.getEmployeeName());
//                    teamEmployee.setType(UserTypeEnum.AGENTER.getType());
//                    teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
//                    teamEmployee.setInviteTime(employee.getCreateTime());
//                    teamEmployee.setCreateTime(employee.getCreateTime());
//                    teamEmployee.setUpdateTime(employee.getUpdateTime());
//                    teamEmployeeMapper.insert(teamEmployee);
//                    teamEmployeeSet.add(teamEmployee.getEmployeeId());
//                }
//            }
//        } catch (Exception e){
//            // 发生异常回滚
//            // 删除sys_user表中已经插入的当前orgId下所有用户
//            if (CollectionUtils.isNotEmpty(sysUserSet)) {
//                sysUserMapper.batchDeleteByIds(sysUserSet);
//            }
//            // 删除sys_user_role表中已经插入的所有关联数据
//            if (CollectionUtils.isNotEmpty(sysUserRoleSet)) {
//                sysUserRoleMapper.batchDeleteByIds(sysUserRoleSet);
//            }
//            // 删除team_employee表中已经插入的当前orgId下所有用户
//            if (CollectionUtils.isNotEmpty(teamEmployeeSet)) {
//                teamEmployeeMapper.batchDeleteByIds(teamEmployeeSet);
//            }
//            // 删除team_host表中已经插入的当前orgId下所有主播
//            teamHostMapper.deleteByTeamId(teamId);
//            // 删除team表新增的团队
//            teamMapper.deleteByPrimaryKey(teamId);
//            log.error("move fail - 数据迁移失败，失败原因：{},失败机构id：{}",e,orgId);
//            throw new ServiceException("move_fail - 数据迁移失败，失败原因：{}",e);
//
//        }
//    }

    /**
     * 以主播为维度自动迁移
     */
    public void autoOperateByHost(String orgUuid, Organization organization) {
        log.info("自动迁移用户部分开始，待迁移公会uuid:{}", orgUuid);
        if (StringUtils.isBlank(orgUuid)) {
            throw new ServiceException("param_error", "请输入公会uuid");
        }
        if (null == organization) {
            throw new ServiceException("param_error", "此机构无效");
        }
        log.info("获取organization机构信息：{},机构uuid:{}", JsonUtils.objectToString(organization), orgUuid);
        MoveDto dto = new MoveDto();
        transferHost(orgUuid, dto);

        // 获取该机构下所有有效employee
        employees(orgUuid, dto);


        // 获取相关信息
        relevanceInfo(orgUuid, dto);

        // 设置参数
        dto.setTeamId(Long.valueOf(orgUuid));
        dto.setOrgId(organization.getOrgId());
        dto.setOrgName(organization.getOrgName());

        log.info("获取到公会信息：{}，公会uuid:{}", JsonUtils.objectToString(dto.getGuildInfo()), dto.getGuildInfo().getUuid());
        log.info("获取到无经纪人主播列表：{}，公会uuid:{}", JsonUtils.objectToString(dto.getNoAgentList()), dto.getGuildInfo().getUuid());
        log.info("获取到有经纪人主播列表：{}，公会uuid:{}", JsonUtils.objectToString(dto.getHasAgentMap()), dto.getGuildInfo().getUuid());
        log.info("获取到经纪人列表：{}，公会uuid:{}", JsonUtils.objectToString(dto.getAgentManages()), dto.getGuildInfo().getUuid());
        log.info("获取到职员列表：{}，公会uuid:{}", JsonUtils.objectToString(dto.getEmployeeList()), dto.getGuildInfo().getUuid());

        // 迁移
        autoMoveByHost(dto);

    }

    public void autoMoveByHost(MoveDto dto) {
        log.info("成员经纪人主播数据开始迁移");

        List<AgentManage> agentManageListRecord = dto.getAgentManages();
        List<Employee> employeeList = dto.getEmployeeList();
        Map<String, List<LiveHostInfo>> hostMap = dto.getHasAgentMap();
        List<LiveHostInfo> hostListNoAgent = dto.getNoAgentList();
        GuildInfo guildInfo = dto.getGuildInfo();
        Long orgId = dto.getOrgId();
        String orgName = dto.getOrgName();
        Long teamId = dto.getTeamId();
        Long agentRoleId = dto.getAgentRoleId();
        Long managerRoleId = dto.getManagerRoleId();
        // 过滤agent_manage
        List<AgentManage> agentManageList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(agentManageListRecord)) {
            for (AgentManage agent : agentManageListRecord) {
                if (StringUtils.isNotBlank(agent.getAgentUuid()) && StringUtils.isNotBlank(guildInfo.getAccountUuid())) {
                    if (guildInfo.getAccountUuid().equals(agent.getAccountUuid()) && !guildInfo.getAccountUuid().equals(agent.getAgentUuid())) {
                        agentManageList.add(agent);
                    } else {
                        log.info("自动迁移agentManage当前经纪人不属于这个公会或者当前经纪人也是会长，经纪人uuid：{}，会长uuid：{}，公会uuid：{}", agent.getAgentUuid(), guildInfo.getAccountUuid(), guildInfo.getUuid());
                    }
                } else {
                    log.info("自动迁移经纪人uuid或者会长account_uuid为空，经纪人uuid：{}，会长uuid：{}，公会uuid：{}", agent.getAgentUuid(), guildInfo.getAccountUuid(), guildInfo.getUuid());
                }
            }
        }
        try {
            // 迁移guild_info表会长 - 老公会会长角色迁移的时候作为机构管理员角色插入
            if (StringUtils.isNotBlank(guildInfo.getAccountUuid())) {
                List<SysUser> userList = sysUserMapper.userIsHasExisted(guildInfo.getAccountUuid());
                if (CollectionUtils.isEmpty(userList)) {
                    // 先新增sys_user表
                    SysUser sysUserRecord = new SysUser();
                    sysUserRecord.setOrgId(orgId);
                    sysUserRecord.setOrgName(orgName);
                    sysUserRecord.setAccountUuid(guildInfo.getAccountUuid());

                    // 调用j2获取手机号码和名称
                    String mobile = "";
                    String accountName = "";
                    Map<String, Object> userInfo = getInfoByUuids(guildInfo.getAccountUuid());
                    if (MapUtils.isNotEmpty(userInfo)) {
                        mobile = (String) userInfo.get("mobile");
                        accountName = (String) userInfo.get("account_name");
                    }
                    sysUserRecord.setUserName(accountName);
                    sysUserRecord.setMobile(mobile);

                    sysUserRecord.setUserType(UserTypeEnum.MANAGER.getType());
                    sysUserRecord.setStatus(UserStatus.OK.getCode());
                    sysUserRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
                    sysUserRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
                    sysUserMapper.insert(sysUserRecord);
                    // 后新增sys_user_role表
                    SysUserRole sysUserRoleRecord = new SysUserRole();
                    sysUserRoleRecord.setRoleId(managerRoleId);
                    sysUserRoleRecord.setUserId(sysUserRecord.getUserId());
                    sysUserRoleRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
                    sysUserRoleRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
                    sysUserRoleMapper.insert(sysUserRoleRecord);
                } else {
                    log.info("自动迁移当前公会会长已经存在，公会信息：{},用户信息：{}", JsonUtils.objectToString(guildInfo), JsonUtils.objectToString(userList));
                }
            } else {
                log.info("自动迁移当前公会会长无效，公会信息：{}", JsonUtils.objectToString(guildInfo));
            }

            // 新增无经纪人主播
            if (CollectionUtils.isNotEmpty(hostListNoAgent)) {
                List<TeamHost> list = new ArrayList<>();
                for (LiveHostInfo host : hostListNoAgent) {
                    if (StringUtils.isBlank(host.getHostUuid())) {
                        log.info("自动迁移当前主播uuid为空，公会信息:{}", JsonUtils.objectToString(guildInfo));
                        continue;
                    }
                    TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
                    if (null != oneByHostUuid) {
                        log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}", JsonUtils.objectToString(guildInfo), JsonUtils.objectToString(oneByHostUuid));
                        continue;
                    }
                    TeamHost teamHost = new TeamHost();
                    teamHost.setHostUuid(host.getHostUuid());
                    teamHost.setOrgId(orgId);
                    teamHost.setTeamId(teamId);
                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setStatus(1);
                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setCurrentSharingProfitRate("0");
                    teamHost.setNewSharingProfitRate("");
                    teamHost.setIsUpdate(0);
                    list.add(teamHost);
                }
                // 批量插入
                if (CollectionUtils.isNotEmpty(list)) {
                    teamHostMapper.batchInsertHost(list);
                }
            }

            // 迁移 agent_manage表
            if (CollectionUtils.isNotEmpty(agentManageList)) {
                for (AgentManage agent : agentManageList) {
                    // 非空判断
                    if (StringUtils.isBlank(agent.getAgentUuid())) {
                        log.info("自动迁移agentManage当前经纪人uuid为空，公会信息:{},经纪人信息：{}", JsonUtils.objectToString(guildInfo), JsonUtils.objectToString(agent));
                        continue;
                    }
                    // 判断当前经纪人是否属于这个公会并且当前经纪人不是会长
//                    if (!guildInfo.getAccountUuid().equals(agent.getAccountUuid()) || guildInfo.getAccountUuid().equals(agent.getAgentUuid())) {
//                        log.info("自动迁移agentManage当前经纪人不属于这个公会或者当前经纪人也是会长，经纪人uuid：{}，会长uuid：{}，公会uuid：{}",agent.getAgentUuid(),guildInfo.getAccountUuid(), guildInfo.getUuid());
//                        // 最后新增team_host表
//                        if (MapUtils.isNotEmpty(hostMap)) {
//                            List<LiveHostInfo> liveHostInfoList = hostMap.get(agent.getAgentUuid());
//                            if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                                List<TeamHost> list = new ArrayList<>();
//                                for (LiveHostInfo host : liveHostInfoList) {
//                                    if (StringUtils.isBlank(host.getHostUuid())) {
//                                        log.info("自动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                                        continue;
//                                    }
//                                    TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                                    if (null != oneByHostUuid) {
//                                        log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                                        continue;
//                                    }
//                                    TeamHost teamHost = new TeamHost();
//                                    teamHost.setHostUuid(host.getHostUuid());
//                                    teamHost.setOrgId(orgId);
//                                    teamHost.setTeamId(teamId);
//                                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setStatus(1);
//                                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setCurrentSharingProfitRate("0");
//                                    teamHost.setNewSharingProfitRate("");
//                                    teamHost.setIsUpdate(0);
//                                    list.add(teamHost);
//                                }
//                                // 批量插入
//                                if (CollectionUtils.isNotEmpty(list)) {
//                                    teamHostMapper.batchInsertHost(list);
//                                }
//                            }
//                        }
//                        continue;
//                    }
                    // 判断当前经纪人是否已经迁移
                    List<SysUser> userList = sysUserMapper.userIsHasExisted(agent.getAgentUuid());
                    if (CollectionUtils.isNotEmpty(userList)) {
                        log.info("自动迁移agentManage当前经纪人在用户表中已经存在,用户信息：{}，公会信息：{}", JsonUtils.objectToString(userList), JsonUtils.objectToString(guildInfo));
                        continue;
                    }
                    // 通过uuid获取用户电话号码
                    String mobile = "";
                    Map<String, Object> infoByUuids = getInfoByUuids(agent.getAgentUuid());
                    if (MapUtils.isEmpty(infoByUuids) || ObjectUtils.isEmpty(infoByUuids.get("mobile"))) {
                        log.info("自动迁移agentManage当前用户手机号码为空，用户信息：{}，公会信息", JsonUtils.objectToString(agent), JsonUtils.objectToString(guildInfo));
                        mobile = "***********";
                    } else {
                        mobile = String.valueOf(infoByUuids.get("mobile"));
                    }
                    // 先新增sys_user表
                    SysUser sysUser = new SysUser();
                    sysUser.setOrgId(orgId);
                    sysUser.setOrgName(orgName);
                    sysUser.setAccountUuid(agent.getAgentUuid());
                    sysUser.setUserName(agent.getAgentName());
                    sysUser.setUserType(UserTypeEnum.AGENTER.getType());
                    sysUser.setMobile(mobile);
                    sysUser.setStatus(UserStatus.OK.getCode());
                    sysUser.setCreateTime(agent.getCreateTime());
                    sysUser.setUpdateTime(agent.getUpdateTime());
                    sysUserMapper.insert(sysUser);
                    // 其次新增sys_user_role表
                    SysUserRole sysUserRole = new SysUserRole();
                    sysUserRole.setRoleId(agentRoleId);
                    sysUserRole.setUserId(sysUser.getUserId());
                    sysUserRole.setCreateTime(agent.getCreateTime());
                    sysUserRole.setUpdateTime(agent.getUpdateTime());
                    sysUserRoleMapper.insert(sysUserRole);
                    // 后新增team_employee表
                    TeamEmployee teamEmployee = new TeamEmployee();
                    teamEmployee.setOrgId(orgId);
                    teamEmployee.setOrgName(orgName);
                    teamEmployee.setTeamId(teamId);
                    teamEmployee.setUserId(sysUser.getUserId());
                    teamEmployee.setMobile(mobile);
                    teamEmployee.setEmployeeName(agent.getAgentName());
                    teamEmployee.setType(UserTypeEnum.AGENTER.getType());
                    teamEmployee.setInviteCode(agent.getInvitationCode());
                    teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
                    teamEmployee.setInviteTime(agent.getCreateTime());
                    teamEmployee.setCreateTime(agent.getCreateTime());
                    teamEmployee.setUpdateTime(agent.getUpdateTime());
                    teamEmployeeMapper.insert(teamEmployee);
                    // 最后新增team_host表
                    if (MapUtils.isNotEmpty(hostMap)) {
                        List<LiveHostInfo> liveHostInfoList = hostMap.get(agent.getAgentUuid());
                        if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
                            List<TeamHost> list = new ArrayList<>();
                            for (LiveHostInfo host : liveHostInfoList) {
                                if (StringUtils.isBlank(host.getHostUuid())) {
                                    log.info("自动迁移agentManage当前主播uuid为空，公会信息:{}", JsonUtils.objectToString(guildInfo));
                                    continue;
                                }
                                TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
                                if (null != oneByHostUuid) {
                                    log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}", JsonUtils.objectToString(guildInfo), JsonUtils.objectToString(oneByHostUuid));
                                    continue;
                                }
                                TeamHost teamHost = new TeamHost();
                                teamHost.setHostUuid(host.getHostUuid());
                                teamHost.setOrgId(orgId);
                                teamHost.setTeamId(teamId);
                                teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
                                teamHost.setEmployeeId(teamEmployee.getEmployeeId());
                                teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                teamHost.setStatus(1);
                                teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                                teamHost.setCurrentSharingProfitRate("0");
                                teamHost.setNewSharingProfitRate("");
                                teamHost.setIsUpdate(0);
                                list.add(teamHost);
                            }
                            // 批量插入
                            if (CollectionUtils.isNotEmpty(list)) {
                                teamHostMapper.batchInsertHost(list);
                            }
                        }
                    }
                }
            }
            // 迁移employee表
            if (CollectionUtils.isNotEmpty(employeeList)) {
                for (Employee employee : employeeList) {
                    if (StringUtils.isBlank(employee.getEmployeeUuid())) {
                        log.info("自动迁移employee当前职员uuid为空，公会信息:{},职员信息：{}", JsonUtils.objectToString(guildInfo), JsonUtils.objectToString(employee));
                        continue;
                    }
                    List<SysUser> userList = sysUserMapper.userIsHasExisted(employee.getEmployeeUuid());
                    if (CollectionUtils.isNotEmpty(userList)) {
                        log.info("自动迁移employee当前用户已经存在，用户信息：{}，公会信息：{}", JsonUtils.objectToString(userList), JsonUtils.objectToString(guildInfo));
                        continue;
                    }
                    // 通过uuid获取用户电话号码
                    String mobile = "";
                    Map<String, Object> infoByUuids = getInfoByUuids(employee.getEmployeeUuid());
                    if (MapUtils.isEmpty(infoByUuids) || ObjectUtils.isEmpty(infoByUuids.get("mobile"))) {
                        log.info("自动迁移employee当前用户手机号码为空，用户信息：{}，公会信息", JsonUtils.objectToString(employee), JsonUtils.objectToString(guildInfo));
                        mobile = "***********";
                    } else {
                        mobile = String.valueOf(infoByUuids.get("mobile"));
                    }
                    // 先新增sys_user表
                    SysUser sysUser = new SysUser();
                    sysUser.setOrgId(orgId);
                    sysUser.setOrgName(orgName);
                    sysUser.setAccountUuid(employee.getEmployeeUuid());
                    sysUser.setUserName(employee.getEmployeeName());
                    sysUser.setUserType(UserTypeEnum.AGENTER.getType());
                    // 解密
                    sysUser.setMobile(mobile);
                    sysUser.setStatus(UserStatus.OK.getCode());
                    sysUser.setCreateTime(employee.getCreateTime());
                    sysUser.setUpdateTime(employee.getUpdateTime());
                    sysUserMapper.insert(sysUser);
                    // 其次新增sys_user_role表
                    SysUserRole sysUserRole = new SysUserRole();
                    sysUserRole.setRoleId(agentRoleId);
                    sysUserRole.setUserId(sysUser.getUserId());
                    sysUserRole.setCreateTime(employee.getCreateTime());
                    sysUserRole.setUpdateTime(employee.getUpdateTime());
                    sysUserRoleMapper.insert(sysUserRole);
                    // 后新增team_employee表
                    TeamEmployee teamEmployee = new TeamEmployee();
                    teamEmployee.setOrgId(orgId);
                    teamEmployee.setOrgName(orgName);
                    teamEmployee.setTeamId(teamId);
                    teamEmployee.setUserId(sysUser.getUserId());
                    // 解密
                    teamEmployee.setMobile(mobile);
                    teamEmployee.setEmployeeName(employee.getEmployeeName());
                    teamEmployee.setType(UserTypeEnum.AGENTER.getType());
                    teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
                    teamEmployee.setInviteTime(employee.getCreateTime());
                    teamEmployee.setCreateTime(employee.getCreateTime());
                    teamEmployee.setUpdateTime(employee.getUpdateTime());
                    teamEmployeeMapper.insert(teamEmployee);
                }
            }
        } catch (Exception e) {
            log.error("move fail - 数据迁移失败，失败原因：{},失败机构id：{}", e, orgId);
            throw new ServiceException("move_fail - 数据迁移失败，失败原因：{}", e.getMessage());
        }
    }

//    public void autoMoveByHost(MoveDto dto){
//        log.info("成员经纪人主播数据开始迁移");
//
//        List<AgentManage> agentManageList = dto.getAgentManages();
//        List<Employee> employeeList = dto.getEmployeeList();
//        Map<String, List<LiveHostInfo>> hostMap = dto.getHasAgentMap();
//        List<LiveHostInfo> hostListNoAgent = dto.getNoAgentList();
//        GuildInfo guildInfo = dto.getGuildInfo();
//        Long orgId = dto.getOrgId();
//        String orgName = dto.getOrgName();
//        Long teamId = dto.getTeamId();
//        Long agentRoleId = dto.getAgentRoleId();
//        Long managerRoleId = dto.getManagerRoleId();
//        try {
//            // 迁移guild_info表会长 - 老公会会长角色迁移的时候作为机构管理员角色插入
//            if (StringUtils.isNotBlank(guildInfo.getAccountUuid())) {
//                List<SysUser> userList = sysUserMapper.userIsHasExisted(guildInfo.getAccountUuid());
//                if (CollectionUtils.isEmpty(userList)) {
//                    // 先新增sys_user表
//                    SysUser sysUserRecord = new SysUser();
//                    sysUserRecord.setOrgId(orgId);
//                    sysUserRecord.setOrgName(orgName);
//                    sysUserRecord.setAccountUuid(guildInfo.getAccountUuid());
//
//                    // 调用j2获取手机号码和名称
//                    String mobile = "";
//                    String accountName = "";
//                    Map<String, Object> userInfo = getInfoByUuids(guildInfo.getAccountUuid());
//                    if (MapUtils.isNotEmpty(userInfo)) {
//                        mobile = (String)userInfo.get("mobile");
//                        accountName = (String)userInfo.get("account_name");
//                    }
//                    sysUserRecord.setUserName(accountName);
//                    sysUserRecord.setMobile(mobile);
//
//                    sysUserRecord.setUserType(UserTypeEnum.MANAGER.getType());
//                    sysUserRecord.setStatus(UserStatus.OK.getCode());
//                    sysUserRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
//                    sysUserRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
//                    sysUserMapper.insert(sysUserRecord);
//                    // 后新增sys_user_role表
//                    SysUserRole sysUserRoleRecord = new SysUserRole();
//                    sysUserRoleRecord.setRoleId(managerRoleId);
//                    sysUserRoleRecord.setUserId(sysUserRecord.getUserId());
//                    sysUserRoleRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
//                    sysUserRoleRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
//                    sysUserRoleMapper.insert(sysUserRoleRecord);
//                } else {
//                    log.info("自动迁移当前公会会长已经存在，公会信息：{},用户信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(userList));
//                }
//            } else {
//                log.info("自动迁移当前公会会长无效，公会信息：{}",JsonUtils.objectToString(guildInfo));
//            }
//
//            // 新增无经纪人主播
//            if (CollectionUtils.isNotEmpty(hostListNoAgent)) {
//                List<TeamHost> list = new ArrayList<>();
//                for (LiveHostInfo host : hostListNoAgent) {
//                    if (StringUtils.isBlank(host.getHostUuid())) {
//                        log.info("自动迁移当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                        continue;
//                    }
//                    TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                    if (null != oneByHostUuid) {
//                        log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                        continue;
//                    }
//                    TeamHost teamHost = new TeamHost();
//                    teamHost.setHostUuid(host.getHostUuid());
//                    teamHost.setOrgId(orgId);
//                    teamHost.setTeamId(teamId);
//                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                    teamHost.setStatus(1);
//                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                    teamHost.setCurrentSharingProfitRate("0");
//                    teamHost.setNewSharingProfitRate("");
//                    teamHost.setIsUpdate(0);
//                    list.add(teamHost);
//                }
//                // 批量插入
//                if (CollectionUtils.isNotEmpty(list)) {
//                    teamHostMapper.batchInsertHost(list);
//                }
//            }
//
//            // 迁移agent_manage表
//            if (MapUtils.isNotEmpty(hostMap)) {
//                Map<String, AgentManage> agentManageMap = null;
//                if (CollectionUtils.isNotEmpty(agentManageList)) {
//                    agentManageMap = agentManageList.stream().collect(Collectors.toMap(AgentManage::getAgentUuid, Function.identity(), (key1, key2) -> key2));
//                }
//                for (Map.Entry<String, List<LiveHostInfo>> entry : hostMap.entrySet()) {
//                    if (MapUtils.isNotEmpty(agentManageMap)) {
//                        AgentManage agent = agentManageMap.get(entry.getKey());
//                        if (null != agent) {
//                            // 判断当前经纪人是否属于这个公会并且当前经纪人不是会长
//                            if (guildInfo.getAccountUuid().equals(agent.getAccountUuid()) && !guildInfo.getAccountUuid().equals(agent.getAgentUuid())) {
//                                // 判断当前经纪人是否已经迁移
//                                List<SysUser> userList = sysUserMapper.userIsHasExisted(agent.getAgentUuid());
//                                if (CollectionUtils.isNotEmpty(userList)) {
//                                    log.info("自动迁移agentManage当前经纪人在用户表中已经存在：{}",JsonUtils.objectToString(userList));
//                                    continue;
//                                }
//                                // 先新增sys_user表
//                                SysUser sysUser = new SysUser();
//                                sysUser.setOrgId(orgId);
//                                sysUser.setOrgName(orgName);
//                                sysUser.setAccountUuid(agent.getAgentUuid());
//                                sysUser.setUserName(agent.getAgentName());
//                                sysUser.setUserType(UserTypeEnum.AGENTER.getType());
//                                sysUser.setMobile(agent.getPhone());
//                                sysUser.setStatus(UserStatus.OK.getCode());
//                                sysUser.setCreateTime(agent.getCreateTime());
//                                sysUser.setUpdateTime(agent.getUpdateTime());
//                                sysUserMapper.insert(sysUser);
//                                // 其次新增sys_user_role表
//                                SysUserRole sysUserRole = new SysUserRole();
//                                sysUserRole.setRoleId(agentRoleId);
//                                sysUserRole.setUserId(sysUser.getUserId());
//                                sysUserRole.setCreateTime(agent.getCreateTime());
//                                sysUserRole.setUpdateTime(agent.getUpdateTime());
//                                sysUserRoleMapper.insert(sysUserRole);
//                                // 后新增team_employee表
//                                TeamEmployee teamEmployee = new TeamEmployee();
//                                teamEmployee.setOrgId(orgId);
//                                teamEmployee.setOrgName(orgName);
//                                teamEmployee.setTeamId(teamId);
//                                teamEmployee.setUserId(sysUser.getUserId());
//                                teamEmployee.setMobile(agent.getPhone());
//                                teamEmployee.setEmployeeName(agent.getAgentName());
//                                teamEmployee.setType(UserTypeEnum.AGENTER.getType());
//                                teamEmployee.setInviteCode(agent.getInvitationCode());
//                                teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
//                                teamEmployee.setInviteTime(agent.getCreateTime());
//                                teamEmployee.setCreateTime(agent.getCreateTime());
//                                teamEmployee.setUpdateTime(agent.getUpdateTime());
//                                teamEmployeeMapper.insert(teamEmployee);
//
//                                List<LiveHostInfo> liveHostInfoList = entry.getValue();
//                                if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                                    List<TeamHost> list = new ArrayList<>();
//                                    for (LiveHostInfo host : liveHostInfoList) {
//                                        if (StringUtils.isBlank(host.getHostUuid())) {
//                                            log.info("自动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                                            continue;
//                                        }
//                                        TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                                        if (null != oneByHostUuid) {
//                                            log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                                            continue;
//                                        }
//                                        TeamHost teamHost = new TeamHost();
//                                        teamHost.setHostUuid(host.getHostUuid());
//                                        teamHost.setOrgId(orgId);
//                                        teamHost.setTeamId(teamId);
//                                        teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                        teamHost.setEmployeeId(teamEmployee.getEmployeeId());
//                                        teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setStatus(1);
//                                        teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setCurrentSharingProfitRate("0");
//                                        teamHost.setNewSharingProfitRate("");
//                                        teamHost.setIsUpdate(0);
//                                        list.add(teamHost);
//                                    }
//                                    // 批量插入
//                                    if (CollectionUtils.isNotEmpty(list)) {
//                                        teamHostMapper.batchInsertHost(list);
//                                    }
//                                }
//                            } else {
//                                log.info("自动迁移agentManage当前经纪人不属于这个公会或者当前经纪人也是会长，经纪人uuid：{}，会长uuid：{}，公会uuid：{}",agent.getAgentUuid(),guildInfo.getAccountUuid(), guildInfo.getUuid());
//                                List<LiveHostInfo> liveHostInfoList = entry.getValue();
//                                if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                                    List<TeamHost> list = new ArrayList<>();
//                                    for (LiveHostInfo host : liveHostInfoList) {
//                                        if (StringUtils.isBlank(host.getHostUuid())) {
//                                            log.info("自动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                                            continue;
//                                        }
//                                        TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                                        if (null != oneByHostUuid) {
//                                            log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                                            continue;
//                                        }
//                                        TeamHost teamHost = new TeamHost();
//                                        teamHost.setHostUuid(host.getHostUuid());
//                                        teamHost.setOrgId(orgId);
//                                        teamHost.setTeamId(teamId);
//                                        teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                        teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setStatus(1);
//                                        teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                        teamHost.setCurrentSharingProfitRate("0");
//                                        teamHost.setNewSharingProfitRate("");
//                                        teamHost.setIsUpdate(0);
//                                        list.add(teamHost);
//                                    }
//                                    // 批量插入
//                                    if (CollectionUtils.isNotEmpty(list)) {
//                                        teamHostMapper.batchInsertHost(list);
//                                    }
//                                }
//                            }
//                        } else {
//                            List<LiveHostInfo> liveHostInfoList = entry.getValue();
//                            if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                                List<TeamHost> list = new ArrayList<>();
//                                for (LiveHostInfo host : liveHostInfoList) {
//                                    if (StringUtils.isBlank(host.getHostUuid())) {
//                                        log.info("自动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                                        continue;
//                                    }
//                                    TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                                    if (null != oneByHostUuid) {
//                                        log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                                        continue;
//                                    }
//                                    TeamHost teamHost = new TeamHost();
//                                    teamHost.setHostUuid(host.getHostUuid());
//                                    teamHost.setOrgId(orgId);
//                                    teamHost.setTeamId(teamId);
//                                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setStatus(1);
//                                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                    teamHost.setCurrentSharingProfitRate("0");
//                                    teamHost.setNewSharingProfitRate("");
//                                    teamHost.setIsUpdate(0);
//                                    list.add(teamHost);
//                                }
//                                // 批量插入
//                                if (CollectionUtils.isNotEmpty(list)) {
//                                    teamHostMapper.batchInsertHost(list);
//                                }
//                            }
//                        }
//                    } else {
//                        List<LiveHostInfo> liveHostInfoList = entry.getValue();
//                        if (CollectionUtils.isNotEmpty(liveHostInfoList)) {
//                            List<TeamHost> list = new ArrayList<>();
//                            for (LiveHostInfo host : liveHostInfoList) {
//                                if (StringUtils.isBlank(host.getHostUuid())) {
//                                    log.info("自动迁移agentManage当前主播uuid为空，公会信息:{}",JsonUtils.objectToString(guildInfo));
//                                    continue;
//                                }
//                                TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid());
//                                if (null != oneByHostUuid) {
//                                    log.info("自动迁移当前主播已经存在，公会信息:{}，主播信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(oneByHostUuid));
//                                    continue;
//                                }
//                                TeamHost teamHost = new TeamHost();
//                                teamHost.setHostUuid(host.getHostUuid());
//                                teamHost.setOrgId(orgId);
//                                teamHost.setTeamId(teamId);
//                                teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
//                                teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                teamHost.setStatus(1);
//                                teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
//                                teamHost.setCurrentSharingProfitRate("0");
//                                teamHost.setNewSharingProfitRate("");
//                                teamHost.setIsUpdate(0);
//                                list.add(teamHost);
//                            }
//                            // 批量插入
//                            if (CollectionUtils.isNotEmpty(list)) {
//                                teamHostMapper.batchInsertHost(list);
//                            }
//                        }
//                    }
//                }
//            }
//            // 迁移employee表
//            if (CollectionUtils.isNotEmpty(employeeList)) {
//                for (Employee employee : employeeList) {
//                    if (StringUtils.isBlank(employee.getEmployeeUuid())) {
//                        log.info("自动迁移employee当前职员uuid为空，公会信息:{},职员信息：{}",JsonUtils.objectToString(guildInfo),JsonUtils.objectToString(employee));
//                        continue;
//                    }
//                    List<SysUser> userList = sysUserMapper.userIsHasExisted(employee.getEmployeeUuid());
//                    if (CollectionUtils.isNotEmpty(userList)) {
//                        log.info("自动迁移employee当前用户已经存在，用户信息：{}，公会信息：{}",JsonUtils.objectToString(userList),JsonUtils.objectToString(guildInfo));
//                        continue;
//                    }
//                    // 先新增sys_user表
//                    SysUser sysUser = new SysUser();
//                    sysUser.setOrgId(orgId);
//                    sysUser.setOrgName(orgName);
//                    sysUser.setAccountUuid(employee.getEmployeeUuid());
//                    sysUser.setUserName(employee.getEmployeeName());
//                    sysUser.setUserType(UserTypeEnum.AGENTER.getType());
//                    // 解密
//                    sysUser.setMobile(getDecryptMobile("employee_mobile_cipher", employee.getEmployeeMobileCipher()));
//                    sysUser.setStatus(UserStatus.OK.getCode());
//                    sysUser.setCreateTime(employee.getCreateTime());
//                    sysUser.setUpdateTime(employee.getUpdateTime());
//                    sysUserMapper.insert(sysUser);
//                    // 其次新增sys_user_role表
//                    SysUserRole sysUserRole = new SysUserRole();
//                    sysUserRole.setRoleId(agentRoleId);
//                    sysUserRole.setUserId(sysUser.getUserId());
//                    sysUserRole.setCreateTime(employee.getCreateTime());
//                    sysUserRole.setUpdateTime(employee.getUpdateTime());
//                    sysUserRoleMapper.insert(sysUserRole);
//                    // 后新增team_employee表
//                    TeamEmployee teamEmployee = new TeamEmployee();
//                    teamEmployee.setOrgId(orgId);
//                    teamEmployee.setOrgName(orgName);
//                    teamEmployee.setTeamId(teamId);
//                    teamEmployee.setUserId(sysUser.getUserId());
//                    // 解密
//                    teamEmployee.setMobile(getDecryptMobile("employee_mobile_cipher", employee.getEmployeeMobileCipher()));
//                    teamEmployee.setEmployeeName(employee.getEmployeeName());
//                    teamEmployee.setType(UserTypeEnum.AGENTER.getType());
//                    teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
//                    teamEmployee.setInviteTime(employee.getCreateTime());
//                    teamEmployee.setCreateTime(employee.getCreateTime());
//                    teamEmployee.setUpdateTime(employee.getUpdateTime());
//                    teamEmployeeMapper.insert(teamEmployee);
//                }
//            }
//        } catch (Exception e){
//            log.error("move fail - 数据迁移失败，失败原因：{},失败机构id：{}",e,orgId);
//            throw new ServiceException("move_fail - 数据迁移失败，失败原因：{}",e.getMessage());
//        }
//    }


    /**
     * 获取employees
     */
    private void employees(String orgUuid, MoveDto dto) {
        List<Employee> employeeList = employeeMapper.getEmployeeList(orgUuid);
        dto.setEmployeeList(employeeList);
    }

    @Async
    public void sendSmsMessage(MoveDto dto) {
        List<String> mobileList = new ArrayList<>();
        GuildInfo guildInfo = dto.getGuildInfo();
        List<AgentManage> agentManageList = dto.getAgentManages();
        List<Employee> employeeList = dto.getEmployeeList();
        // 机构负责人
        if (StringUtils.isNotBlank(guildInfo.getChargePersonPhone())) {
            mobileList.add(guildInfo.getChargePersonPhone());
        }
        // 会长
        if (StringUtils.isNotBlank(guildInfo.getAccountUuid())) {
            Map<String, Object> map = getInfoByUuids(guildInfo.getAccountUuid());
            if (MapUtils.isNotEmpty(map)) {
                mobileList.add((String) map.get("mobile"));
            }
        }
        // 经纪人
        if (CollectionUtils.isNotEmpty(agentManageList)) {
            for (AgentManage agent : agentManageList) {
                if (StringUtils.isNotBlank(agent.getPhone())) {
                    mobileList.add(agent.getPhone());
                }
            }
        }
        // 职员
        if (CollectionUtils.isNotEmpty(employeeList)) {
            for (Employee employee : employeeList) {
                if (StringUtils.isNotBlank(employee.getEmployeeMobileCipher())) {
                    String mobile = getDecryptMobile("employee_mobile_cipher", employee.getEmployeeMobileCipher());
                    mobileList.add(mobile);
                }
            }
        }
        // todo 这里调用j39发送短信
        sendSmsToMembers(mobileList);

    }

    /**
     * 获取相关联信息
     *
     * @param orgUuid
     * @param dto
     */
    public void relevanceInfo(String orgUuid, MoveDto dto) {
        // 获取直播类型经纪人角色 roleId
        Long agentRoleId = sysRoleMapper.getAgentRoleId();
        log.info("获取agentRoleId：{},机构uuid:{}", agentRoleId, orgUuid);
        if (null == agentRoleId || 0L == agentRoleId) {
            throw new ServiceException("param_error", "直播业务类型经纪人角色不存在，请创建");
        }
        // 获取直播类型管理员角色 roleId
        Long managerRoleId = sysRoleMapper.getManagerRoleId();
        log.info("获取managerRoleId：{},机构uuid:{}", managerRoleId, orgUuid);
        if (null == managerRoleId || 0L == managerRoleId) {
            throw new ServiceException("param_error", "直播业务类型管理员角色不存在，请创建");
        }
        dto.setAgentRoleId(agentRoleId);
        dto.setManagerRoleId(managerRoleId);
    }


    @Override
    public void trasnferTaquGonghguiHost() {
        Integer idStart = 0;
        Integer step = 2000;

        while (idStart < 240000) {
            Integer idEnd = idStart + step;
            List<LiveHostInfo> hostInfoList = liveHostInfoMapper.getHostListByConsortiaIdAndId(110166, idStart, idEnd);
            List<TeamHost> list = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(hostInfoList)) {
                for (LiveHostInfo host : hostInfoList) {
                    if (StringUtils.isBlank(host.getHostUuid())) {
                        continue;
                    }
                    TeamHost oneByHostUuid = teamHostMapper.getOneByHostUuid(host.getHostUuid(), TeamTypeEnum.LIVE_TEAM.getValue());
                    if (null != oneByHostUuid) {
                        continue;
                    }
                    TeamHost teamHost = new TeamHost();
                    teamHost.setHostUuid(host.getHostUuid());
                    teamHost.setOrgId(1001281L);
                    teamHost.setTeamId(110166L);
                    teamHost.setEmployeeId(null);
                    teamHost.setTeamType(TeamTypeEnum.LIVE_TEAM.getValue());
                    teamHost.setInviteTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setStatus(1);
                    teamHost.setCreateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setUpdateTime(Optional.ofNullable(host.getAddConsortiaTime()).orElse(0).longValue());
                    teamHost.setCurrentSharingProfitRate("0");
                    teamHost.setNewSharingProfitRate("");
                    teamHost.setIsUpdate(0);
                    list.add(teamHost);
                }
                // 批量插入
                if (CollectionUtils.isNotEmpty(list)) {
                    teamHostMapper.batchInsertHost(list);
                }
            }
            idStart = idStart + step;
            try {
                TimeUnit.SECONDS.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        trasnferTaquGonghguiUser(110166);

    }

    public void trasnferTaquGonghguiUser(Integer guildUuid) {
        log.info("[trasnferTaquGonghguiUser]迁移素人公会公会会长作为团队管理员，公会uuid:{}", guildUuid);
        GuildInfo guildInfo = guildInfoMapper.selectByUuid(guildUuid);
        if (null == guildInfo) {
            log.warn("[trasnferTaquGonghguiUser]迁移素人公会当前公会无效，公会uuid:{}", guildUuid);
            throw new ServiceException("invalid_guild", "[trasnferTaquGonghguiUser]公会uuid为空");
        }
        // 查询机构名称
        Organization organization = organizationMapper.selectByPrimaryKey(1001281L);
        if (null == organization) {
            log.warn("[trasnferTaquGonghguiUser]迁移素人公会当前机构无效");
            throw new ServiceException("invalid_organization", "[trasnferTaquGonghguiUser]机构无效");
        }
        // 获取直播类型负责人角色 roleId
        Long leaderRoleId = sysRoleMapper.getLeaderRoleId();
        log.info("素人迁移获取leaderRoleId：{}，公会uuid:{}", leaderRoleId, guildUuid);
        if (null == leaderRoleId || 0L == leaderRoleId) {
            throw new ServiceException("param_error", "直播业务类型负责人角色不存在，请创建");
        }
        // 迁移guild_info表会长 - 老公会会长角色迁移后变成团队负责人角色
        if (StringUtils.isNotBlank(guildInfo.getAccountUuid())) {
            // 判断会长是否已经存在
            List<SysUser> userList = sysUserMapper.userIsHasExisted(guildInfo.getAccountUuid());
            if (CollectionUtils.isEmpty(userList)) {
                // 先新增sys_user表
                SysUser sysUserRecord = new SysUser();
                sysUserRecord.setOrgId(1001281L);
                sysUserRecord.setOrgName(organization.getOrgName());
                sysUserRecord.setAccountUuid(guildInfo.getAccountUuid());

                // 调用j2获取手机号码和名称
                String mobile = "";
                String accountName = "";
                Map<String, Object> userInfo = getInfoByUuids(guildInfo.getAccountUuid());
                if (MapUtils.isNotEmpty(userInfo)) {
                    mobile = (String) userInfo.get("mobile");
                    accountName = (String) userInfo.get("account_name");
                }
                sysUserRecord.setUserName(accountName);
                sysUserRecord.setMobile(mobile);

                sysUserRecord.setUserType(UserTypeEnum.LEADER.getType());
                sysUserRecord.setStatus(UserStatus.OK.getCode());
                sysUserRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
                sysUserRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
                sysUserMapper.insert(sysUserRecord);
                // 其次新增sys_user_role表
                SysUserRole sysUserRoleRecord = new SysUserRole();
                sysUserRoleRecord.setRoleId(leaderRoleId);
                sysUserRoleRecord.setUserId(sysUserRecord.getUserId());
                sysUserRoleRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
                sysUserRoleRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
                sysUserRoleMapper.insert(sysUserRoleRecord);
                // 后新增team_employee表
                TeamEmployee teamEmployeeRecord = new TeamEmployee();
                teamEmployeeRecord.setOrgId(1001281L);
                teamEmployeeRecord.setOrgName(organization.getOrgName());
                teamEmployeeRecord.setTeamId(Long.valueOf(guildUuid));
                teamEmployeeRecord.setUserId(sysUserRecord.getUserId());

                teamEmployeeRecord.setMobile(mobile);
                teamEmployeeRecord.setEmployeeName(accountName);

                teamEmployeeRecord.setType(UserTypeEnum.LEADER.getType());
                teamEmployeeRecord.setInviteCode(null);
                teamEmployeeRecord.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
                teamEmployeeRecord.setInviteTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
                teamEmployeeRecord.setCreateTime(Optional.ofNullable(guildInfo.getCreateTime()).orElse(0).longValue());
                teamEmployeeRecord.setUpdateTime(Optional.ofNullable(guildInfo.getUpdateTime()).orElse(0).longValue());
                teamEmployeeMapper.insert(teamEmployeeRecord);
            } else {
                log.info("素人迁移会长在用户表中已经存在，用户信息：{}，公会信息：{}", JsonUtils.objectToString(userList), JsonUtils.objectToString(guildInfo));
            }
        } else {
            log.info("素人迁移当前公会会长无效，公会信息：{}", JsonUtils.objectToString(guildInfo));
        }
    }
}
