package cn.taqu.gonghui.live.vo.multilive.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/7/23 19 52
 * 用户管理端的多人娱乐主播数据 （UM=User Manage）
 */

@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
public class MultiLiveRoomDataOfUMDto implements Serializable {

    /**
     * 日期
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
    @JsonProperty("time")
    private String time;

    /**
     * 房间uuid
     */
    @ColumnWidth(20)
    @ExcelProperty("房间uuid")
    @JsonProperty("room_uuid")
    private String roomUuid;

    /**
     * 房主名字
     */
    @ColumnWidth(20)
    @ExcelProperty("房主名字")
    @JsonProperty("room_name")
    private String roomName;

    /**
     * 团队id
     */
    @JsonProperty("consortia_id")
    private Long consortiaId;


    /**
     * （主播所属）机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("机构名称")
    @JsonProperty("organ_name")
    private String organName;


    /**
     * 开播时间段
     */
    @ColumnWidth(20)
    @ExcelProperty("开播时间段")
    @JsonProperty("live_time_info")
    private String liveTimeInfo;


    /**
     * 开播总时长
     */
    @ColumnWidth(20)
    @ExcelProperty("开播总时长")
    @JsonProperty("total_live_duration")
    private String totalLiveDuration;

    /**
     * 开播有效天数
     */
    @ColumnWidth(20)
    @ExcelProperty("开播有效天数")
    @JsonProperty("valid_live_days")
    private String validLiveDays;

    /**
     * 房间总收益
     */
    @ColumnWidth(20)
    @ExcelProperty("房间总收益")
    @JsonProperty("total_amount")
    private String totalAmount;

    /**
     * 有效主播数量
     */
    @ColumnWidth(20)
    @ExcelProperty("有效主播数量")
    @JsonProperty("valid_host_num")
    private String validHostNum;

    @ColumnWidth(20)
    @JsonProperty("room_total_score")
    private String roomTotalScore;

    @ColumnWidth(20)
    @JsonProperty("room_shell_amount")
    private String roomShellAmount;

    @ColumnWidth(20)
    @JsonProperty("room_shell_ratio")
    private String roomShellRatio;
}
