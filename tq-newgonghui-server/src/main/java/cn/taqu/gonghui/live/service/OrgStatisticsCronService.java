package cn.taqu.gonghui.live.service;

import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.live.entity.LiveHostStatisticsMonth;
import cn.taqu.gonghui.live.entity.LiveOrgStatisticsMonth;
import cn.taqu.gonghui.live.entity.LiveTeamStatisticsDay;
import cn.taqu.gonghui.live.mapper.*;
import cn.taqu.gonghui.live.search.LiveCommonSearch;
import cn.taqu.gonghui.live.search.OrgStatisticsSearch;
import cn.taqu.gonghui.live.util.LiveDateUtils;
import cn.taqu.gonghui.live.vo.LiveCommonVo;
import cn.taqu.gonghui.live.vo.TopHost;
import cn.taqu.gonghui.soa.SsomsService;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.TeamMapper;
import cn.taqu.gonghui.system.search.SsoUserSearch;
import cn.taqu.gonghui.system.vo.SsoUserCombobox;
import com.alibaba.fastjson.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 定时生成团队维度主播天数据统计
 */
@Service
public class OrgStatisticsCronService {

    private static Logger logger = LoggerFactory.getLogger(OrgStatisticsCronService.class);

    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private LiveHostStatisticsDayMapper liveHostStatisticsDayMapper;
    @Autowired
    private LiveTeamStatisticsDayMapper liveTeamStatisticsDayMapper;
    @SoaReference(application ="ssoms",value = "ssoms")
    private SsomsService ssomsService;
    @Value("${contact.department}")
    private String department;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private LiveHostStatisticsMonthMapper liveHostStatisticsMonthMapper;
    @Autowired
    private LiveNewHostStatisticsMonthMapper liveNewHostStatisticsMonthMapper;
    @Autowired
    private LiveOrgStatisticsMonthMapper liveOrgStatisticsMonthMapper;

    /**
     * 统计团队维度主播每天直播数据
     */
    public void reportLiveTeamStatisticsDay(){
        logger.info("团队维度统计定时任务开始执行，执行时间:{}",DateUtil.currentTimeSeconds());
        // 查询有效团队
        List<Team> teams = teamMapper.selectTeamList(null, null);
        logger.info("待执行团队信息：{}",JsonUtils.objectToString2(teams));
        if (CollectionUtils.isNotEmpty(teams)) {
            for (Team team : teams) {
                // 查询团队下的有效主播
                List<String> uuidList = teamMapper.findHostsByTeamId(team.getTeamId());
                logger.info("团队信息：{}，主播uuidList：{}",JsonUtils.objectToString2(team),JsonUtils.objectToString2(uuidList));
                // 获取团队维度主播天统计数据
                LiveCommonVo liveCommonVo = getLiveTeamStatisticsDay(uuidList);
                liveCommonVo.setDayTime(LiveDateUtils.getBeforeDay());
                liveCommonVo.setTeamId(team.getTeamId());
                // 保存统计的数据到团队天统计表中去
                saveLiveTeamStatisticsDay(liveCommonVo);
            }
        }
    }

    /**
     * 获取团队维度主播天统计数据
     * @param uuidList
     */
    private LiveCommonVo getLiveTeamStatisticsDay(List<String> uuidList){
        LiveCommonVo vo = null;
        if (CollectionUtils.isNotEmpty(uuidList)) {
            LiveCommonSearch search = new LiveCommonSearch();
            search.setUuidList(uuidList);
            search.setDayTime(LiveDateUtils.getBeforeDay());
            vo = liveHostStatisticsDayMapper.getData(search);
        } else {
            vo = new LiveCommonVo();
            vo.setAmount(0);
            vo.setFans(0);
            vo.setFlower(0);
            vo.setHostNum(0);
            vo.setMessage(0);
            vo.setSend(0);
            vo.setTotalLiveTime(0);
            vo.setViewer(0);
        }
        logger.info("主播uuidList：{}，统计数据：{}",JsonUtils.objectToString2(uuidList),JsonUtils.objectToString2(vo));
        return vo;
    }

    /**
     * 保存统计的数据到团队天统计表中去
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveLiveTeamStatisticsDay(LiveCommonVo vo){
        LiveTeamStatisticsDay record = new LiveTeamStatisticsDay();
        record.setAmount(vo.getAmount());
        record.setDayTime(vo.getDayTime());
        record.setFans(vo.getFans());
        record.setFlower(vo.getFlower());
        record.setMessage(vo.getMessage());
        record.setHostNum(vo.getHostNum());
        record.setSend(vo.getSend());
        record.setViewer(vo.getViewer());
        record.setTotalLiveTime(vo.getTotalLiveTime());
        record.setTeamId(vo.getTeamId());
        record.setCreateTime(System.currentTimeMillis()/1000);
        liveTeamStatisticsDayMapper.insert(record);
    }


    /**
     * 统计运营人员维度机构每月直播数据
     */
    public void reportLiveOrgStatisticsMonth() {
        // 查询所有运营人员
        String[] departmentIdList = department.split(",");
        List<SsoUserCombobox> ssoUserComboboxes =new ArrayList<SsoUserCombobox>();
        for (int i = 0; i < departmentIdList.length; i++) {
            SsoUserSearch ssoUserSearch = new SsoUserSearch();
            ssoUserSearch.setOrganizationId(Long.parseLong(departmentIdList[i]));
            JSONArray array = ssomsService.searchEffectiveSsoUsersV2(ssoUserSearch);
            List<SsoUserCombobox> ssoUserComboboxeOne = JsonUtils.stringToObject(JsonUtils.objectToString(array), new com.fasterxml.jackson.core.type.TypeReference<List<SsoUserCombobox>>() {
            });
            ssoUserComboboxes.addAll(ssoUserComboboxeOne);
        }

        ssoUserComboboxes.forEach(ssoName->{
            LiveCommonVo liveCommonVo = getLiveOrgStatisticsMonth(ssoName.getName());
            liveCommonVo.setDayTime(LiveDateUtils.getMonth());
            liveCommonVo.setOperatorName(ssoName.getName());
            saveLiveOrgStatisticsMonth(liveCommonVo);
        });
    }

    /**
     * 获取当月统计数据
     * @param ssoName
     */
    private LiveCommonVo getLiveOrgStatisticsMonth(String ssoName){
        LiveCommonVo vo = null;
        if (StringUtils.isNotEmpty(ssoName)) {
            List<String> orgUuid = organizationMapper.getByOrg(ssoName);
            //
            if(CollectionUtils.isNotEmpty(orgUuid)){
                LiveCommonSearch search = new LiveCommonSearch();
                search.setUuidList(orgUuid);
                search.setDayTime(LiveDateUtils.getMonth());
                //获取当前月的机构的总数，总收益和总直播时长
                LiveCommonVo numByOrgUuid = liveHostStatisticsMonthMapper.getNumByOrgUuid(search);
                vo.setOperatorName(ssoName);
                vo.setOrgNum(orgUuid.size());
                vo.setOrgAmount(numByOrgUuid.getOrgAmount());
                vo.setTotalLiveTime(numByOrgUuid.getTotalLiveTime());
                //获取当前月的开始时间和结束时间的时间戳
                Long currentTime = System.currentTimeMillis();
                Long monthEnd = LiveDateUtils.getMonthEndTime(currentTime, "GMT+8:00")/1000;
                Long monthStart = LiveDateUtils.getMonthStartTime(currentTime, "GMT+8:00")/1000;
                OrgStatisticsSearch orgStatisticsSearch =new OrgStatisticsSearch();
                orgStatisticsSearch.setStartTime(monthStart);
                orgStatisticsSearch.setEndTime(monthEnd);
                orgStatisticsSearch.setBusinessPerson(ssoName);
                //获取当前月的新增机构的总数，总收益和总直播时长
                List<String> organizations = organizationMapper.getNewOrgList(orgStatisticsSearch);
                LiveCommonSearch searchNew = new LiveCommonSearch();
                searchNew.setBusinessPerson(ssoName);
                searchNew.setUuidList(organizations);
                searchNew.setDayTime(LiveDateUtils.getMonth());
                LiveCommonVo numByOrgUuidNew = liveHostStatisticsMonthMapper.getNumByOrgUuid(search);
                vo.setNewOrgNum(organizations.size());
                vo.setNewOrgAmount(numByOrgUuidNew.getOrgAmount());
                //获取当前月新增主播的总数，总收益
                LiveCommonVo numByNewHost = liveNewHostStatisticsMonthMapper.getNewNumByOrgUuid(searchNew);
                vo.setNewAmount(numByNewHost.getNewAmount());
                //当前运营人员手下top200的主播数量和收益
                LiveCommonSearch top = new LiveCommonSearch();
                top.setDayTime(LiveDateUtils.getMonth());
                top.setUuidList(orgUuid);
                List<TopHost> topHost = liveHostStatisticsMonthMapper.getNumByHostUuid(top);
                int topHostNumCast = 0;
                int topHostNumCastAmount = 0;
                for (int i = 0; i <topHost.size() ; i++) {
                    topHostNumCast = topHostNumCast + topHost.get(i).getTopHostNum();
                    topHostNumCastAmount = topHostNumCastAmount + topHost.get(i).getTopHostAmount();
                }
                vo.setTopNum(topHostNumCast);
                vo.setTopAmount(topHostNumCastAmount);
            }else {
                vo = new LiveCommonVo();
                vo = setLiveCommonVo(vo);
            }
        } else {
            vo = new LiveCommonVo();
            vo = setLiveCommonVo(vo);
        }
        return vo;
    }

    /**
     * 设置运营人员的默认数值
     * @param liveCommonVo
     * @return
     */
    public LiveCommonVo setLiveCommonVo(LiveCommonVo liveCommonVo){
        liveCommonVo.setAmount(0);
        liveCommonVo.setHostNum(0);
        liveCommonVo.setNewHostNum(0);
        liveCommonVo.setNewAmount(0);
        liveCommonVo.setTotalLiveTime(0);
        liveCommonVo.setOrgNum(0);
        liveCommonVo.setOrgAmount(0);
        liveCommonVo.setNewOrgNum(0);
        liveCommonVo.setNewOrgAmount(0);
        liveCommonVo.setTopNum(0);
        liveCommonVo.setTopAmount(0);
        return liveCommonVo;
    }

    /**
     * 保存统计的数据到团队天统计表中去
     * @param
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveLiveOrgStatisticsMonth(LiveCommonVo vo){
        LiveOrgStatisticsMonth record = new LiveOrgStatisticsMonth();
        record.setOperatorName(vo.getOperatorName());
        record.setMonthTime(vo.getDayTime());
        record.setOrgNum(vo.getOrgNum());
        record.setOrgFlow(vo.getOrgAmount());
        record.setNewOrgNum(vo.getNewOrgNum());
        record.setNewOrgFlow(vo.getNewOrgAmount());
        record.setNewHostNum(vo.getNewHostNum());
        record.setNewHostFlow(vo.getNewAmount());
        record.setTotalLiveTime(vo.getTotalLiveTime());
        record.setCreateTime(System.currentTimeMillis()/1000);
        record.setTopHostNum(vo.getTopNum());
        record.setTopHostFlow(vo.getTopAmount());
        liveOrgStatisticsMonthMapper.insertSelective(record);
    }

}
