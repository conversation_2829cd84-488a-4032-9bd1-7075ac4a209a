package cn.taqu.gonghui.live.service;

import cn.taqu.gonghui.common.vo.CanQuitVO;
import cn.taqu.gonghui.common.vo.req.LiveAccountDetailReq;
import cn.taqu.gonghui.common.vo.req.LiveConsortiaCashReq;
import cn.taqu.gonghui.common.vo.req.LiveGiftReq;
import cn.taqu.gonghui.common.vo.res.LiveGrayUuidRes;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/1/16 11:43
 */
public interface LiveSoaService {

    /**
     * 自提结算灰度名单
     * @return
     */
    LiveGrayUuidRes getSettltmentTestUuidInfo();

    /**
     * 收礼明细
     *
     * @param liveGiftReq
     * @return
     */
    JSONObject getHostIncomeLog(LiveGiftReq liveGiftReq);

    /**
     * 账户明细
     * @param liveAccountDetailReq
     * @return
     */
    JSONObject getConsortiaHostEarningsList(LiveAccountDetailReq liveAccountDetailReq);

    /**
     * 账户提现明细
     * @param liveConsortiaCashReq
     * @return
     */
    JSONObject getConsortiaCashList(LiveConsortiaCashReq liveConsortiaCashReq);

    /**
     * 是否可转会
     * @param hostUuid
     * @return
     */
    CanQuitVO canQuitNormalConsortia(String hostUuid);

}
