package cn.taqu.gonghui.live.mapper;


import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.LiveNewHostStatisticsMonth;
import cn.taqu.gonghui.live.search.LiveCommonSearch;
import cn.taqu.gonghui.live.vo.LiveCommonVo;

public interface LiveNewHostStatisticsMonthMapper {
    int deleteByPrimaryKey(Long id);

    int insert(LiveNewHostStatisticsMonth record);

    int insertSelective(LiveNewHostStatisticsMonth record);

    LiveNewHostStatisticsMonth selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(LiveNewHostStatisticsMonth record);

    int updateByPrimaryKey(LiveNewHostStatisticsMonth record);

    /**
     * 根据uuidList获取统计后的数据
     */
    @TargetDataSource("livedb")
    LiveCommonVo getNewNumByOrgUuid(LiveCommonSearch search);
}