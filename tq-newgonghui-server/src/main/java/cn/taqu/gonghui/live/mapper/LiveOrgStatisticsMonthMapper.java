package cn.taqu.gonghui.live.mapper;

import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.LiveOrgStatisticsMonth;
import cn.taqu.gonghui.live.search.OrgStatisticsSearch;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface LiveOrgStatisticsMonthMapper {

    @TargetDataSource("livedb")
    int insert(LiveOrgStatisticsMonth record);

    @TargetDataSource("livedb")
    int insertSelective(LiveOrgStatisticsMonth record);

    @TargetDataSource("livedb")
    List<LiveOrgStatisticsMonth> queryByCondition(OrgStatisticsSearch search);
}