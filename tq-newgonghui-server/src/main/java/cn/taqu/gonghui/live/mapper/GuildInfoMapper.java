package cn.taqu.gonghui.live.mapper;

import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.GuildInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GuildInfoMapper {



    @TargetDataSource("livedb")
    GuildInfo selectByUuid(Integer uuid);

    @TargetDataSource("livedb")
    List<GuildInfo> getGuildList();

    @TargetDataSource("livedb")
    void updateGuildInfoStatus(@Param("status") Integer status, @Param("uuid") Integer uuid);
}
