package cn.taqu.gonghui.live.vo.multilive;

import cn.taqu.gonghui.chatroom.vo.MasterApprenticeDataVO;
import com.alibaba.excel.EasyExcel;
import jodd.util.StringUtil;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/7/24 19 15
 * discription
 */
public class FileUtil {
    public static File convertListToExcelFile(List<?> list, Class clazz, String fileName, String sheetName) {
        fileName = StringUtil.isBlank(fileName) ? UUID.randomUUID().toString() : fileName;
        sheetName = StringUtil.isBlank(sheetName) ? "sheet1" : sheetName;

        File xlsxFile = null;
        try {
            xlsxFile = File.createTempFile(fileName, ".xlsx");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        EasyExcel.write(xlsxFile, clazz).sheet(sheetName).doWrite(list);
        return xlsxFile;

    }
}
