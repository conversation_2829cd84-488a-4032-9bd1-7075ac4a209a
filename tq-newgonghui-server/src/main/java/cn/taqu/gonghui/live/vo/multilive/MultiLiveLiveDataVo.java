package cn.taqu.gonghui.live.vo.multilive;

import cn.taqu.gonghui.chatroom.vo.MasterApprenticeDataVO;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import jodd.util.StringUtil;
import lombok.Data;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 53
 * 多人娱乐直播数据
 */

@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
//fillBackgroundColor = 9,
//,fillForegroundColor = 9
)
@Data
public class MultiLiveLiveDataVo implements Serializable {

    /**
     * 日期 '2024-01-01'
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
//    @JsonProperty("dt")
    private String dt;

    /**
     * 多人娱乐直播间当日活跃用户数
     */
    @ColumnWidth(20)
    @ExcelProperty("活跃用户数")
//    @JsonProperty("multi_live_active_unt_1d")
    private String multiLiveActiveUnt1d;

    /**
     * 多人娱乐直播间次日留存率
     */
    @ColumnWidth(20)
    @ExcelProperty("次日留存率")
//    @JsonProperty("multi_live_retation_ratio_2d")
    private String multiLiveRetationRatio2d;

    @ColumnWidth(20)
    @ExcelProperty("总分值")
    private String  multiLiveTotalAmt1d;


    @ColumnWidth(20)
    @ExcelProperty("趣币总分值")
    private String  multiLiveTqbeanAmt1d;


    @ColumnWidth(20)
    @ExcelProperty("贝壳总分值")
    private String  multiLiveShellAmt1d;

    @ColumnWidth(20)
    @ExcelProperty("贝壳分值占比")
    private String  multiLiveShellRatio;




    /**
     * 多人娱乐直播间当日消费用户数
     */
    @ColumnWidth(20)
    @ExcelProperty("消费用户数")
//    @JsonProperty("multi_live_consume_unt_1d")
    private String multiLiveConsumeUnt1d;

    /**
     * 多人娱乐直播间当日活跃主播数
     */
    @ColumnWidth(20)
    @ExcelProperty("活跃房主数")
//    @JsonProperty("multi_live_active_host_unt_1d")
    private String multiLiveActiveHostUnt1d;

    /**
     * 多人娱乐直播间当日上麦主播数
     */
    @ColumnWidth(20)
    @ExcelProperty("活跃上麦数")
//    @JsonProperty("multi_live_meeting_host_unt_1d")
    private String multiLiveMeetHostUnt1d;

    /**
     * 多人娱乐直播消费金额/活跃用户数
     */
    @ColumnWidth(20)
    @ExcelProperty("ARPU")
//    @JsonProperty("ARPU")
    private String ARPU;

    /**
     * 多人娱乐直播消费金额/消费用户数
     */
    @ColumnWidth(20)
    @ExcelProperty("ARPPU")
//    @JsonProperty("ARPPU")
    private String  ARPPU;



}
