package cn.taqu.gonghui.live.entity;

import lombok.Data;

@Data
public class GuildInfo {
    private Long id;

    private Integer uuid;

    private String guildName;

    private String chargePerson;

    private String chargePersonIdCard;

    private String chargePersonPhone;

    private String chargePersonEmail;

    private Integer chargePersonBirthday;

    private String receivingAddress;

    private String legalPerson;

    private String legalPersonIdCard;

    private String publicReceivingBankAccount;

    private String accountName;

    private String accountBankName;

    private String province;

    private Integer provinceId;

    private String city;

    private Integer cityId;

    private String subBranchName;

    private Integer guildStatus;

    private Integer applyStatus;

    private String businessPerson;

    private Integer createTime;

    private Integer updateTime;

    private String chargePersonVx;

    private Integer formStatus;

    private String accountUuid;

    private String auditMsg;

    private String chargePersonPhoneCipher;

    private String chargePersonEmailCipher;

    private String chargePersonCipher;

    private String chargePersonVxCipher;

    private String chargePersonIdCardCipher;

    private String receivingAddressCipher;

    private String legalPersonCipher;

    private String legalPersonIdCardCipher;

    private String publicReceivingBankAccountCipher;

    private String accountNameCipher;

    private String accountBankNameCipher;

    private String subBranchNameCipher;

    private String businessPersonCipher;
}
