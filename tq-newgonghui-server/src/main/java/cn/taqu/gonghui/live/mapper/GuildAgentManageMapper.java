package cn.taqu.gonghui.live.mapper;

import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.AgentManage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GuildAgentManageMapper {


    @TargetDataSource("livedb")
    List<AgentManage> getAgentList(String orgUuid);

    @TargetDataSource("livedb")
    List<AgentManage> selectListByAgentUuids(@Param("agentUuidList") List<String> agentUuidList);

}
