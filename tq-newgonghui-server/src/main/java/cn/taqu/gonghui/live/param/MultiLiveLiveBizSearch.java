package cn.taqu.gonghui.live.param;

import cn.hutool.core.date.DateUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.chatroom.vo.req.BasePageReqV2;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 24
 * 直播业务搜索参数
 */
@Data
public class MultiLiveLiveBizSearch extends BasePageReqV2 implements Serializable {

    /**
     * 时间样式   1-日 2-周 3-月
     */
    private Integer timeType;

    /**
     * 开始时间（时间戳）
     */
    private Long startTime;

    /**
     * 结束时间（时间戳）
     */
    private Long endTime;



//    /**
//     * 机构id
//     */
//    private Long orgId;
//
//    /**
//     * 团队id列表
//     */
//    private List<Long> teamIds;



    /**
     * 是否导出 0-否  1-是
     */
    private Integer export = 0;

    public String getStartTimeFormat(){
        if(this.startTime ==null){
            throw new ServiceException("startTime_required", "起始时间为空");
//            return "";
        }
        return DateUtil.format(new Date(this.startTime),"yyyyMMdd");
    }

    public String getEndTimeFormat(){
        if(this.endTime ==null){
            throw new ServiceException("endTime_required", "结束时间为空");
//            return "";
        }
        return DateUtil.format(new Date(this.endTime),"yyyyMMdd");
    }


}
