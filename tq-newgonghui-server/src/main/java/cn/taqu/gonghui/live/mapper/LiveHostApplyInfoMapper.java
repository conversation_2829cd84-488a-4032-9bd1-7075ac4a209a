package cn.taqu.gonghui.live.mapper;


import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.LiveHostApplyInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface LiveHostApplyInfoMapper {


   @TargetDataSource("livedb")
   LiveHostApplyInfo selectByPrimaryKey(Long id);

   @TargetDataSource("livedb")
   int insertSelective(LiveHostApplyInfo liveHostApplyInfo);

   @TargetDataSource("livedb")
   int updateByPrimaryKeySelective(LiveHostApplyInfo liveHostApplyInfo);

   @TargetDataSource("livedb")
   int countByNotIdAccountUuidAndStatus(@Param("id") Long id, @Param("accountUuid") String accountUuid);

   @TargetDataSource("livedb")
   List<LiveHostApplyInfo> selectListByParams(@Param("consortiaIdList") List<Long> consortiaIdList, @Param("businessmanUuid") String businessmanUuid,@Param("accountName") String accountName,  @Param("accountUuid") String accountUuid, @Param("status") Integer status);

   /**
    * 查询申请状态
    * @param accountUuid
    * @return
    */
   @TargetDataSource("livedb")
   LiveHostApplyInfo selectByUuid(@Param("accountUuid") String accountUuid);
}
