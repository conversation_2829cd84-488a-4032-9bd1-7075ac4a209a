package cn.taqu.gonghui.live.entity;

import cn.taqu.core.orm.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 主播申请信息表
 */
@Data
public class LiveHostApplyInfo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 房间封面地址
     */
    private String coverUrl;

    /**
     * 身份证正面图片地址
     */
    private String cardZmUrl;

    /**
     * 身份证反面图片地址
     */
    private String cardFmUrl;

    /**
     * 身份证手持图片地址
     */
    private String cardScUrl;

    /**
     * 封面图片宽度
     */
    private Integer width;

    /**
     * 封面图片高度
     */
    private Integer height;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 他趣账号绑定手机
     */
    private String accountMobile;

    /**
     * 他趣昵称
     */
    private String accountName;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 主播视频
     */
    private String video;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证
     */
    private String idCard;

    /**
     * 主题id
     */
    private Integer themeId;

    /**
     * 标签id
     */
    private String tagId;

    private Integer consortiaId;

    /**
     * 2未提交1审核通过;3待审核;-1不通过
     */
    private Integer status;

    /**
     * 申请时间
     */
    private Long createTime;

    /**
     * 拒绝理由
     */
    private String refuseReason;

    /**
     * 审核操作人
     */
    private String adminName;

    /**
     * 主播介绍
     */
    private String hostIntro;

    /**
     * 是否下载过 0否 1是
     */
    private Boolean isDownload;

    /**
     * 标签ID
     */
    private Integer labelId;

    /**
     * 申请来源 1公会后台 2客户端公会 3客户端个人
     */
    private Integer applyFrom;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 同意协议视频地址
     */
    private String agreementVideo;

    /**
     * 经济人uuid
     */
    private String businessmanUuid;

    /**
     * 拒绝次数
     */
    private Integer refuseNum;

    /**
     * 支付宝账号(加密后数据)
     */
    private String alipayAccountCipher;

    /**
     * 账号绑定手机(加密后数据)
     */
    private String accountMobileCipher;

    /**
     * 真实姓名(加密后数据)
     */
    private String realNameCipher;

    /**
     * 身份证(加密后数据)
     */
    private String idCardCipher;

    /**
     * 支付宝账号(加密后数据,用于查询)
     */
    private String alipayAccountSm3;

    /**
     * 账号绑定手机(加密后数据,用于查询)
     */
    private String accountMobileSm3;

    /**
     * 真实姓名(加密后数据,用于查询)
     */
    private String realNameSm3;

    /**
     * 身份证(加密后数据,用于查询)
     */
    private String idCardSm3;








}
