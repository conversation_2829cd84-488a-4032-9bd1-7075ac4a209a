package cn.taqu.gonghui.live.mapper;

import cn.taqu.core.jdbc.datasource.TargetDataSource;
import cn.taqu.gonghui.live.entity.LiveHostStatisticsDay;
import cn.taqu.gonghui.live.search.LiveCommonSearch;
import cn.taqu.gonghui.live.search.LiveHostSearch;
import cn.taqu.gonghui.live.vo.LiveCommonVo;

import java.util.List;


public interface LiveHostStatisticsDayMapper {

    /**
     * 根据uuidList获取统计后的数据
     */
    @TargetDataSource("livedb")
    LiveCommonVo getData(LiveCommonSearch search);

    /**
     * 根据uuidList获取统计后的数据
     */
    @TargetDataSource("livedb")
    List<LiveHostStatisticsDay> getLiveHostStatisticsDayByHostUuidList(LiveHostSearch liveHostSearch);
}
