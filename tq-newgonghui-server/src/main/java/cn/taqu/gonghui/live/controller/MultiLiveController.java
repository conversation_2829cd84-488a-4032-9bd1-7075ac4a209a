package cn.taqu.gonghui.live.controller;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.chatroom.search.MasterAndApprenticeSearch;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.live.param.*;
import cn.taqu.gonghui.system.service.multilive.MultiLiveStatsService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/7/18 15 36
 * 多人娱乐--用户端
 */
@RestController
@RequestMapping(value = "/api",params = "service=multiLive")
public class MultiLiveController {

    @Resource
    private MultiLiveStatsService multiLiveStatsService;

    /**
     * 直播公会-数据统计(新)-多人娱乐主播数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=hostData")
    public JsonResult multiRecreationHostData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        MultiLiveHostSearchOfUM search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveHostSearchOfUM>() {
        });
        return JsonResult.success(multiLiveStatsService.searchHostData(search));
    }

    /**
     *
     * 直播公会-数据统计(新)-多人娱乐主播数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=hostDataDownload")
    public JsonResult multiRecreationHostDataDownload(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");

        MultiLiveHostSearchOfUM search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveHostSearchOfUM>() {
        });
        String url = multiLiveStatsService.getHostDataDownloadUrl(search);
        JsonResult result = JsonResult.success();
        result.setData(url);
        return result;
    }


    /**
     * 直播公会-数据统计(新)-多人娱乐公会数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=teamData")
    public JsonResult multiRecreationTeamData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        MultiLiveConsortiaSearchOfUM search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveConsortiaSearchOfUM>() {
        });
        return JsonResult.success(multiLiveStatsService.searchConsortiaData(search));
    }

    /**
     *
     * 直播公会-数据统计(新)-多人娱乐公会数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=teamDataDownload")
    public JsonResult multiRecreationTeamDataDownload(RequestParams params) {

        String paramStr = params.getFormStringDefault(0, "");

        MultiLiveConsortiaSearchOfUM search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveConsortiaSearchOfUM>() {
        });
        search.setExport(1);
        JsonResult result = JsonResult.success();
        String url = multiLiveStatsService.getConsortiaDataDownloadUrl(search);
        result.setData(url);
        return result;
    }


    /**
     * 直播公会-数据统计(新)-多人娱乐房间数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=roomData")
    public JsonResult multiRecreationRoomData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        MultiLiveRoomSearchOfUM search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveRoomSearchOfUM>() {
        });
        PageDataResult result = multiLiveStatsService.searchRoomData(search);
        return JsonResult.success(result);
    }

    /**
     *
     * 直播公会-数据统计(新)-多人娱乐房间数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=roomDataDownload")
    public JsonResult multiRecreationRoomDataDownload(RequestParams params) {

        String paramStr = params.getFormStringDefault(0, "");
        MultiLiveRoomSearchOfUM search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveRoomSearchOfUM>() {
        });
        search.setExport(1);
        JsonResult result = JsonResult.success();
        String url = multiLiveStatsService.getRoomDataDownloadUrl(search);
        result.setData(url);
        return result;
    }




}
