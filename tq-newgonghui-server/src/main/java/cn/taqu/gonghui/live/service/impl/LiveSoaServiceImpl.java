package cn.taqu.gonghui.live.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.vo.CanQuitVO;
import cn.taqu.gonghui.common.vo.req.LiveAccountDetailReq;
import cn.taqu.gonghui.common.vo.req.LiveConsortiaCashReq;
import cn.taqu.gonghui.common.vo.req.LiveGiftReq;
import cn.taqu.gonghui.common.vo.res.LiveGrayUuidRes;
import cn.taqu.gonghui.live.service.LiveSoaService;
import cn.taqu.gonghui.soa.SOAUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/1/16 11:47
 */
@Service
@Slf4j
public class LiveSoaServiceImpl implements LiveSoaService {

    private static final String LIVE_URL = "/soa/application/live/trade";

    @Override
    public LiveGrayUuidRes getSettltmentTestUuidInfo() {
        String service = "HostSettlement";
        String method = "getSettltmentTestUuidInfo";

        try {
            SoaResponse soaResponse = SOAUtil.create(LIVE_URL)
                    .call(service, method);
            if (soaResponse.fail()) {
                log.error("getSettltmentTestUuidInfo,失败原因{}", soaResponse.getMsg());
                return null;
            }
            String soaResult = soaResponse.getData();
            if (StringUtils.isBlank(soaResult) || "[]".equals(soaResult) || "{}".equals(soaResult)) {
                log.warn("调用getSettltmentTestUuidInfo为空");
                return null;
            }
            log.info("getSettltmentTestUuidInfo: {}", soaResult);
            return JsonUtils.stringToObject(soaResult, new TypeReference<LiveGrayUuidRes>() {});
        } catch (Exception e) {
            log.error("调用getSettltmentTestUuidInfo出错: ", e);
            return null;
        }
    }

    @Override
    public JSONObject getHostIncomeLog(LiveGiftReq liveGiftReq) {
        String service = "AdminIncome";
        String method = "getHostIncomeLog";
        Object[] param = {liveGiftReq.getPage(), liveGiftReq.getPage_size(), liveGiftReq.getHost_uuid()
                , liveGiftReq.getStart_time(), liveGiftReq.getEnd_time()};

        try {
            SoaResponse soaResponse = SOAUtil.create(LIVE_URL)
                    .call(service, method, param);
            if (soaResponse.fail()) {
                log.error("getHostIncomeLog,失败原因{}", soaResponse.getMsg());
                return null;
            }
            String soaResult = soaResponse.getData();
            if (StringUtils.isBlank(soaResult) || "[]".equals(soaResult) || "{}".equals(soaResult)) {
                log.warn("调用getHostIncomeLog为空");
                return null;
            }
            log.info("getHostIncomeLog: {}", soaResult);
            return JSON.parseObject(soaResponse.getData());
        } catch (Exception e) {
            log.error("调用getHostIncomeLog出错: ", e);
            return null;
        }
    }

    @Override
    public JSONObject getConsortiaHostEarningsList(LiveAccountDetailReq req) {
        String service = "HostEarnings";
        String method = "getConsortiaHostEarningsList";
        List<String> uuidList = new ArrayList<>();
        if (StringUtils.isNotBlank(req.getHost_uuid())) {
            uuidList.add(req.getHost_uuid());
        }
        Object[] param = {req.getPage(), req.getPage_size(), req.getConsortia_id()
                , uuidList, req.getLive_no()};

        try {
            SoaResponse soaResponse = SOAUtil.create(LIVE_URL)
                    .call(service, method, param);
            if (soaResponse.fail()) {
                log.error("getConsortiaHostEarningsList,失败原因{}", soaResponse.getMsg());
                return null;
            }
            String soaResult = soaResponse.getData();
            if (StringUtils.isBlank(soaResult) || "[]".equals(soaResult) || "{}".equals(soaResult)) {
                log.warn("调用getConsortiaHostEarningsList为空");
                return null;
            }
            log.info("getConsortiaHostEarningsList: {}", soaResult);
            return JSON.parseObject(soaResponse.getData());
        } catch (Exception e) {
            log.error("调用getConsortiaHostEarningsList出错: ", e);
            return null;
        }
    }

    @Override
    public JSONObject getConsortiaCashList(LiveConsortiaCashReq req) {
        String service = "HostEarnings";
        String method = "getConsortiaCashList";
        List<String> uuidList = new ArrayList<>();
        if (StringUtils.isNotBlank(req.getHost_uuid())) {
            uuidList.add(req.getHost_uuid());
        }
        Object[] param = {req.getPage(), req.getPage_size(), req.getConsortia_id()
                , uuidList, req.getLive_no(), req.getStart_time(), req.getEnd_time()
                , req.getExport()};

        try {
            SoaResponse soaResponse = SOAUtil.create(LIVE_URL)
                    .call(service, method, param);
            if (soaResponse.fail()) {
                log.error("getConsortiaCashList,失败原因{}", soaResponse.getMsg());
                return null;
            }
            String soaResult = soaResponse.getData();
            if (StringUtils.isBlank(soaResult) || "[]".equals(soaResult) || "{}".equals(soaResult)) {
                log.warn("调用getConsortiaCashList为空");
                return null;
            }
            log.info("getConsortiaCashList: {}", soaResult);
            return JSON.parseObject(soaResponse.getData());
        } catch (Exception e) {
            log.error("调用getConsortiaCashList出错: ", e);
            return null;
        }
    }

    @Override
    public CanQuitVO canQuitNormalConsortia(String hostUuid) {
        String service = "HostEarnings";
        String method = "canQuitNormalConsortia";
        Object[] param = {hostUuid};

        try {
            SoaResponse soaResponse = SOAUtil.create(LIVE_URL)
                    .call(service, method, param);
            if (soaResponse.fail()) {
                log.error("canQuitNormalConsortia,失败原因{}", soaResponse.getMsg());
                return null;
            }
            String soaResult = soaResponse.getData();
            if (StringUtils.isBlank(soaResult) || "[]".equals(soaResult) || "{}".equals(soaResult)) {
                log.warn("调用canQuitNormalConsortia为空");
                return null;
            }
            log.info("canQuitNormalConsortia: {}", soaResult);
            return JsonUtils.stringToObject(soaResult, new TypeReference<CanQuitVO>() {});
        } catch (Exception e) {
            log.error("调用canQuitNormalConsortia出错: ", e);
            return null;
        }
    }

}
