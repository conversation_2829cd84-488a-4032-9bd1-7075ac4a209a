package cn.taqu.gonghui.live.entity;


import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/6/17
 */
@Data
public class Employee {

    private Integer id;

    private Long employeeId; //职员id

    private String employeeName; //职员姓名

    private String employeeIdCardCipher; //职员身份证(加密)

    private String employeeMobileCipher; //职员联系号码(加密)

    private String employeeMobileSm3; //sm3加密 手机号

    private String employeeIdCardSm3; //sm3加密 身份证

    private String accountUuid; //会长uuid

    private String employeeUuid; //公司职员uuid

    private Integer valid; //公司职员uuid

    private Long createTime; //创建时间

    private Long updateTime; //更新时间
}
