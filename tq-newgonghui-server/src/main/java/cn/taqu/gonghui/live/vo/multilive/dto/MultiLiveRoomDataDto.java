package cn.taqu.gonghui.live.vo.multilive.dto;

import cn.taqu.gonghui.chatroom.vo.MasterApprenticeDataVO;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import jodd.util.StringUtil;
import lombok.Data;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/7/22 17 53
 * 多人娱乐主播数据
 */

@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
//fillBackgroundColor = 9,
//,fillForegroundColor = 9
)
@Data
public class MultiLiveRoomDataDto implements Serializable {

    /**
     * 日期 '2024-01-01'
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
    @JsonProperty("dt")
    private String dt;

    /**
     * 房主昵称
     */
    @ColumnWidth(20)
    @ExcelProperty("房主昵称")
    @JsonProperty("multi_live_name")
    private String multiLiveName;

    /**
     * 房主uuid
     */
    @ColumnWidth(20)
    @ExcelProperty("房主uuid")
    @JsonProperty("multi_live_uuid")
    private String multiLiveUuid;

    /**
     * 机构id
     */
    @JsonProperty("consortia_id")
    private Long consortiaId;

    /**
     * 房主所属机构名称
     */
    @ColumnWidth(20)
    @ExcelProperty("房主所属机构名称")
    @JsonProperty("consortia_name")
    private String consortiaName;

    /**
     * 开房有效天
     */
    @ColumnWidth(20)
    @ExcelProperty("开房有效天")
    @JsonProperty("is_active_multi_live")
    private String isActiveMultiLive;

    /**
     * 开房时长（小时）
     */
    @ColumnWidth(20)
    @ExcelProperty("开房时长（分钟）")
    @JsonProperty("multi_live_duration_h")
    private String multiLiveDurationH;

    /**
     * 房间收礼总流水
     */
    @ColumnWidth(20)
    @ExcelProperty("房间收礼总流水")
    @JsonProperty("multi_live_receive_amt_1d")
    private String multiLiveReceiveAmt1d;

    /**
     * 有效主播数量
     */
    @ColumnWidth(20)
    @ExcelProperty("有效主播数量")
    @JsonProperty("multi_live_active_host_cnt_1d")
    private String multiLiveActiveHostCnt1d;

//    /**
//     * 房间曝光人数
//     */
//    @ColumnWidth(20)
//    @ExcelProperty("房间曝光人数")
//    @JsonProperty("multi_live_exposure_cnt_1d")
//    private String  multiLiveExposureCnt1d;

    /**
     * 房间在大厅曝光人数
     */
    @ColumnWidth(20)
    @ExcelProperty("房间在大厅曝光人数")
    @JsonProperty("multi_live_hall_exposure_cnt_1d")
    private String multiLiveHallExposureCnt1d;

    /**
     * 进入房间的人数
     */
    @ColumnWidth(20)
    @ExcelProperty("进入房间的人数")
    @JsonProperty("multi_live_active_cnt_1d")
    private String multiLiveActiveCnt1d;

    /**
     * 房间上麦数
     */
    @ColumnWidth(20)
    @ExcelProperty("房间上麦数")
    @JsonProperty("multi_live_meeting_cnt_1d")
    private String multiLiveMeetingCnt1d;

    /**
     * 房间付费人数
     */
    @ColumnWidth(20)
    @ExcelProperty("房间付费人数")
    @JsonProperty("multi_live_consume_cnt_1d")
    private String multiLiveConsumeCnt1d;

    /**
     * 房间付费率
     */
    @ColumnWidth(20)
    @ExcelProperty("房间付费率")
    @JsonProperty("multi_live_consume_ratio")
    private String multiLiveConsumeRatio;

    /**
     * 房间次日留存率
     */
    @ColumnWidth(20)
    @ExcelProperty("房间次日留存率")
    @JsonProperty("multi_live_retention_ratio_2d")
    private String multiLiveRetentionRatio2d;

    /**
     * 房间新用户dau
     */
    @ColumnWidth(20)
    @ExcelProperty("房间新用户dau")
    @JsonProperty("multi_live_new_active_cnt_1d")
    private String multiLiveNewActiveCnt1d;


    @JsonProperty("multi_live_total_amt_1d")
    private String multiLiveTotalAmt1d;


    @JsonProperty("multi_live_shell_amt_1d")
    private String multiLiveShellAmt1d;



    @JsonProperty("multi_live_shell_ratio")
    private String multiLiveShellRatio;

//    /**
//     * 房间新用户付费人数
//     */
//    @ColumnWidth(20)
//    @ExcelProperty("房间新用户付费人数")
//    @JsonProperty("multi_live_new_consume_cnt_1d")
//    private String multiLiveNewConsumeCnt1d;

//    /**
//     * 房间新用户付费率
//     */
//    @ColumnWidth(20)
//    @ExcelProperty("房间新用户付费率")
//    @JsonProperty("multi_live_new_consume_ratio")
//    private String multiLiveNewConsumeRatio;

    public static File convertListToExcelFile(List<MultiLiveRoomDataDto> list, String fileName, String sheetName) {
        fileName = StringUtil.isBlank(fileName) ? UUID.randomUUID().toString() : fileName;
        sheetName = StringUtil.isBlank(sheetName) ? "sheet1" : sheetName;

        File xlsxFile = null;
        try {
            xlsxFile = File.createTempFile(fileName, ".xlsx");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        EasyExcel.write(xlsxFile, MasterApprenticeDataVO.class).sheet(sheetName).doWrite(list);
        return xlsxFile;

    }
}
