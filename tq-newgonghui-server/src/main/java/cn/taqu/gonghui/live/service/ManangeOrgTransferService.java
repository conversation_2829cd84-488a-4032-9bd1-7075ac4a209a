package cn.taqu.gonghui.live.service;

import cn.taqu.gonghui.common.domain.CommonSelect;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/6/21
 */

public interface ManangeOrgTransferService {

    /**
     * 手动迁移机构
     * @param orgUuid
     * @param orgId
     */
    void handOperateAgentAndEmployee(String orgUuid,Long orgId);

    Map<String,String> handOperateValid(String oldOrgUuid, Long orgId);

    List<CommonSelect> tree();


    void moveNoticeAndAgr();


    void autoTransfer(String orgUuid);

    void moveTeamDailyStatistics();

    void updateAgentName();

    void trasnferTaquGonghguiHost();

}
