package cn.taqu.gonghui.live.entity;

import lombok.Data;

@Data
public class AgentManage {
    private Long id;

    private String agentName;

    private String agentIdCard;

    private String invitationCode;

    private String phone;

    private String agentUuid;

    private String accountUuid;

    private Long createTime;

    private Long updateTime;

    private Integer valid;

    private String agentNameCipher;

    private String agentIdCardCipher;

    private String phoneCipher;

    private String phoneSm3;

    private String agentIdCardSm3;
}
