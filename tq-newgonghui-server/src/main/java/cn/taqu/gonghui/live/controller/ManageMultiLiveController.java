package cn.taqu.gonghui.live.controller;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.chatroom.search.MasterAndApprenticeSearch;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.live.param.MultiLiveConsortiaSearch;
import cn.taqu.gonghui.live.param.MultiLiveHostSearch;
import cn.taqu.gonghui.live.param.MultiLiveLiveBizSearch;
import cn.taqu.gonghui.live.param.MultiLiveRoomSearch;
import cn.taqu.gonghui.system.service.multilive.MultiLiveStatsService;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/7/18 15 36
 * 多人娱乐-管理端
 */
@Slf4j
@RestController
@RequestMapping(value = "/api",params = "service=manageMultiLive")
public class ManageMultiLiveController {

    @Resource
    private MultiLiveStatsService multiLiveStatsService;

    /**
     * 直播公会-数据统计(新)-多人娱乐主播数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=hostData")
    public JsonResult multiRecreationHostData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        MultiLiveHostSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveHostSearch>() {
        });
        search.setPage(page);
        search.setPageSize(pageSize);
        PageDataResult result = multiLiveStatsService.searchAdminHostData(search);
        result.setTotal(0L);
        return JsonResult.success(result);
    }

    /**
     *
     * 直播公会-数据统计(新)-多人娱乐主播数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=hostDataDownload")
    public JsonResult multiRecreationHostDataDownload(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        MultiLiveHostSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveHostSearch>() {
        });
        search.setPage(page);
        search.setPageSize(pageSize);
        String url = multiLiveStatsService.getAdminHostDataDownloadUrl(search);
        JsonResult result = JsonResult.success();
        result.setData(url);
        return result;
    }


    /**
     * 直播公会-数据统计(新)-多人娱乐公会数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=teamData")
    public JsonResult multiRecreationTeamData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        MultiLiveConsortiaSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveConsortiaSearch>() {
        });
        search.setPage(page);
        search.setPageSize(pageSize);
        PageDataResult result = multiLiveStatsService.searchAdminConsortiaData(search);
        result.setTotal(0L);
        return JsonResult.success(result);
    }

    /**
     *
     * 直播公会-数据统计(新)-多人娱乐公会数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=teamDataDownload")
    public JsonResult multiRecreationTeamDataDownload(RequestParams params) {

        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        MultiLiveConsortiaSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveConsortiaSearch>() {
        });
        search.setPage(page);
        search.setPageSize(pageSize);
        search.setExport(1);
        JsonResult result = JsonResult.success();
        String url = multiLiveStatsService.getAdminConsortiaDataDownloadUrl(search);
        result.setData(url);
        return result;
    }


    /**
     * 直播公会-数据统计(新)-多人娱乐房间数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=roomData")
    public JsonResult multiRecreationRoomData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        MultiLiveRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveRoomSearch>() {
        });
        search.setPage(page);
        search.setPageSize(pageSize);
        PageDataResult<?> result = multiLiveStatsService.searchAdminRoomData(search);
        result.setTotal(0L);
        return JsonResult.success(result);
    }

    /**
     *
     * 直播公会-数据统计(新)-多人娱乐房间数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=roomDataDownload")
    public JsonResult multiRecreationRoomDataDownload(RequestParams params) {

        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        MultiLiveRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveRoomSearch>() {
        });
        search.setPage(page);
        search.setPageSize(pageSize);
        String url = multiLiveStatsService.getAdminRoomDataDownloadUrl(search);
        JsonResult result = JsonResult.success();
        result.setData(url);
        return result;
    }


    /**
     * 直播公会-数据统计(新)-多人娱乐直播业务数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=liveBizData")
    public JsonResult multiRecreationLiveBizData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        MultiLiveLiveBizSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveLiveBizSearch>() {
        });
        search.setPage(page);
        search.setPageSize(pageSize);
        PageDataResult<?> result = multiLiveStatsService.searchAdminLiveBizData(search);
        return JsonResult.success(result);
    }

    /**
     *
     * 直播公会-数据统计(新)-多人娱乐直播业务数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=liveBizDataDownload")
    public JsonResult multiRecreationLiveBizDataDownload(RequestParams params) {

        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        MultiLiveLiveBizSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MultiLiveLiveBizSearch>() {
        });
        search.setPage(page);
        search.setPageSize(pageSize);
        String url = multiLiveStatsService.getAdminLiveBizDataDownloadUrl(search);
        JsonResult result = JsonResult.success();
        result.setData(url);
        return  result;
    }

}
