package cn.taqu.gonghui.chatroom.service;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.chatroom.constant.RoomSourceEnum;
import cn.taqu.gonghui.chatroom.constant.RoomStatusEnum;
import cn.taqu.gonghui.chatroom.entity.ExportRecord;
import cn.taqu.gonghui.chatroom.entity.Room;
import cn.taqu.gonghui.chatroom.mapper.ExportRecordMapper;
import cn.taqu.gonghui.chatroom.vo.RoomSubmitReq;
import cn.taqu.gonghui.chatroom.vo.req.RoomList4ManagerReq;
import cn.taqu.gonghui.chatroom.vo.req.RoomList4UserReq;
import cn.taqu.gonghui.chatroom.vo.RoomListVo;
import cn.taqu.gonghui.chatroom.vo.RoomApplyReq;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.entity.AuditItem;
import cn.taqu.gonghui.common.entity.AuditOrder;
import cn.taqu.gonghui.common.service.AuditOrderService;
import cn.taqu.gonghui.common.service.BaseBizService;
import cn.taqu.gonghui.common.utils.SecurityUtils;
import cn.taqu.gonghui.common.utils.UUID;
import cn.taqu.gonghui.common.vo.AuditOrderVO;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.soa.CertificationManager;
import cn.taqu.gonghui.soa.InfoManager;
import cn.taqu.gonghui.soa.dto.CertificationInfo;
import cn.taqu.gonghui.soa.dto.Info;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.taqu.gonghui.common.constant.CodeStatus.EXEC_ERROR;
import static cn.taqu.gonghui.common.constant.CodeStatus.ORG_NOT_FOUNT_ERROR;
import static cn.taqu.gonghui.common.constant.TeamTypeEnum.TALK_TEAM;

@Slf4j
@RequiredArgsConstructor
@Service
public class RoomBizService extends BaseBizService {

    private final RoomService roomService;

    private final AuditOrderService auditOrderService;

    private final TeamHostService teamHostService;

    private final SysUserService sysUserService;


    private final OrganizationService organizationService;

    private final TeamService teamService;


    private final InfoManager infoManager;

    private final CertificationManager certificationManager;

    private final PlatformTransactionManager transactionManager;
    private final ExportRecordMapper exportRecordMapper;

    private List<AuditOrderVO.ItemVO> buildApplyItems() {
        AuditOrderVO.ItemVO itemVO = new AuditOrderVO.ItemVO();
        itemVO.setLayer(0);
        itemVO.setAuditorUuid(null);
        return Collections.singletonList(itemVO);
    }

    private String getAccountUuidByAccountId(String accountId) {
        Info accountInfo = infoManager.getInfoByNormalCard(accountId);
        return Optional.ofNullable(accountInfo)
                .map(Info::getAccountUuid)
                .orElse(null);
    }

    private String buildRoomNo() {
        return UUID.genUuid();
    }

    /**
     * 管理端创建
     * @param roomSubmitReq
     */
    public void submit(RoomSubmitReq roomSubmitReq){
        if (StringUtils.isNotEmpty(roomSubmitReq.getRemark())) {
            if (roomSubmitReq.getRemark().length() > 100) {
                throw new ServiceException(EXEC_ERROR.value(), "备注长度不得超过100");
            }
        }

        if (StringUtils.isEmpty(roomSubmitReq.getTimeScope())) {
            throw new ServiceException(EXEC_ERROR.value(), "时间段必填");
        }

        String accountUuid = getAccountUuidByAccountId(roomSubmitReq.getAccountId());
        if(StringUtils.isEmpty(accountUuid)){
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "账户信息不存在");
        }
        //根据 他趣uuid 查 team
        TeamHost teamHost = teamHostService.getHostByUuidAndType(accountUuid, TeamTypeEnum.TALK_TEAM.getValue());
        if (teamHost == null) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "未关联团队");
        }

        Info info = infoManager.getInfoByUuidNoSecret(accountUuid);
        if (info == null) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "房主信息不存在");
        }

        CertificationInfo certificationInfo = certificationManager.getInfoByUuid(accountUuid);
        if (certificationInfo == null) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "房主信息不存在");
        }
        Organization organization = organizationService.getOrgInfo(teamHost.getOrgId());
        if (organization == null) {
            //机构不存在
            throw new ServiceException(ORG_NOT_FOUNT_ERROR);
        }

        Room room = new Room();
        BeanUtils.copyProperties(roomSubmitReq, room);
        room.setRoomNo(buildRoomNo());
        room.setMobile(info.getMobile());
        room.setOwnerName(certificationInfo.getRealName());
        room.setAccountUuid(accountUuid);
        room.setTeamId(teamHost.getTeamId());
        room.setOrgUuid(organization.getOrgUuid());
        room.setStatus(RoomStatusEnum.ACCEPT.getCode());
        room.setAuditOrderNo("");
        room.setSource(RoomSourceEnum.MANAGER.getCode());
        room.setCreateBy(Optional.ofNullable(roomSubmitReq.getOperator()).orElse("system"));
        room.setUpdateBy(Optional.ofNullable(roomSubmitReq.getOperator()).orElse("system"));
        roomService.save(room);
    }

    /**
     * 申请
     *
     * @param roomApplyReq
     */

    public void apply(RoomApplyReq roomApplyReq) {

        if (StringUtils.isNotEmpty(roomApplyReq.getRemark())) {
            if (roomApplyReq.getRemark().length() > 100) {
                throw new ServiceException(EXEC_ERROR.value(), "备注长度不得超过100");
            }
        }

        if (StringUtils.isEmpty(roomApplyReq.getTimeScope())) {
            throw new ServiceException(EXEC_ERROR.value(), "时间段必填");
        }
        LocalTime timeScopeStart_ = null;
        LocalTime timeScopeEnd_ = null;
        try {
            String[] timeScopes = StringUtils.split(roomApplyReq.getTimeScope(), "-");
            String timeScopeStart = timeScopes[0];
            String timeScopeEnd = timeScopes[1];
            timeScopeStart_ =
                    LocalTime.parse(timeScopeStart, DateTimeFormatter.ofPattern("HH:mm"));
            timeScopeEnd_ =
                    LocalTime.parse(timeScopeEnd, DateTimeFormatter.ofPattern("HH:mm"));

        } catch (Exception e) {
            throw new ServiceException(EXEC_ERROR.value(), "时间段格式不匹配");
        }
        if (timeScopeStart_.plusHours(4).isAfter(timeScopeEnd_)) {
            throw new ServiceException(EXEC_ERROR.value(), "时间段需要大于4小时");
        }


        LoginUser loginUser = getLoginUser();
        if (loginUser == null || loginUser.getUser() == null) {
            throw new ServiceException(EXEC_ERROR.value(), "用户未登陆");
        }
        boolean isManager = sysUserService.isManager(loginUser.getUser().getUserId(), TALK_TEAM.getValue());
        if (!isManager) {
            throw new ServiceException(CodeStatus.ROLE_ERROR.value(), "当前用户不是机构管理员");
        }


        String accountUuid = getAccountUuidByAccountId(roomApplyReq.getAccountId());
        if(StringUtils.isEmpty(accountUuid)){
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "账户信息不存在");
        }
        //根据 他趣uuid 查 team
        TeamHost teamHost = teamHostService.getHostByUuidAndType(accountUuid, TeamTypeEnum.TALK_TEAM.getValue());
        if (teamHost == null) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "未关联团队");
        }

        Info info = infoManager.getInfoByUuidNoSecret(accountUuid);
        if (info == null) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "房主信息不存在");
        }
        if (!StringUtils.equals(info.getMobile(), roomApplyReq.getMobile())) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "手机号不正确");
        }

        CertificationInfo certificationInfo = certificationManager.getInfoByUuid(accountUuid);
        if (certificationInfo == null) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "房主信息不存在");
        }

        if (!StringUtils.equals(certificationInfo.getRealName(), StringUtils.trim(roomApplyReq.getOwnerName()))) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "房主名字不正确");
        }


        Long orgId = loginUser.getUser().getOrgId();
        Organization organization = organizationService.getOrgInfo(orgId);
        if (organization == null) {
            //机构不存在
            throw new ServiceException(ORG_NOT_FOUNT_ERROR);
        }


        if (!Objects.equals(teamHost.getOrgId(), orgId)) {
            //机构不存在
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "该他趣ID不属于本公会不可申请");
        }


        Room room = new Room();
        BeanUtils.copyProperties(roomApplyReq, room);
        room.setRoomNo(buildRoomNo());
        room.setAccountUuid(accountUuid);
        room.setTeamId(teamHost.getTeamId());
        room.setOrgUuid(organization.getOrgUuid());
        room.setStatus(RoomStatusEnum.INIT.getCode());
        room.setSource(RoomSourceEnum.USER.getCode());
        AuditOrderVO auditOrderVO = new AuditOrderVO();
        auditOrderVO.setType(AuditOrderTypeEnum.REPORT_ROOM.getCode());
        auditOrderVO.setRemark(roomApplyReq.getRemark());
        auditOrderVO.setApplyItems(buildApplyItems());

        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        transactionTemplate.executeWithoutResult((status) -> {
            AuditOrder auditOrder = auditOrderService.save(auditOrderVO);
            room.setAuditOrderNo(auditOrder.getNo());
            roomService.save(room);
        });

    }


    public Page<RoomListVo> page4User(RoomList4UserReq roomList4UserReq,
                                      Integer pageNo,
                                      Integer pageSize) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        boolean isManager = sysUserService.isManager(user.getUserId(), TALK_TEAM.getValue());
        if (!isManager) {
            throw new ServiceException(CodeStatus.ROLE_ERROR.value(), "当前用户不是机构管理员");
        }

        Long orgId = user.getOrgId();
        return page(roomList4UserReq.getHostUuid(),
                roomList4UserReq.getAccountId(),
                null, orgId, null,
                RoomSourceEnum.USER.getCode(),
                pageNo, pageSize);
    }

    public Page<RoomListVo> page4Manager(RoomList4ManagerReq roomList4ManagerReq,
                                         Integer pageNo,
                                         Integer pageSize) {


        return page(roomList4ManagerReq.getHostUuid(),
                roomList4ManagerReq.getAccountId(),
                null, null,
                roomList4ManagerReq.getTeamId(),
                roomList4ManagerReq.getSource(),
                pageNo, pageSize);
    }

    /**
     * @param accountUuid
     * @param accountId
     * @param orgUuid
     * @param orgId
     * @param teamId
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<RoomListVo> page(
            String accountUuid,
            String accountId,
            String orgUuid,
            Long orgId,
            Long teamId,
            Integer source,
            Integer pageNo,
            Integer pageSize
    ) {

        Page<RoomListVo> pageResult = new Page<>(
                pageNo,
                pageSize);

        if(StringUtils.isNotEmpty(accountId) &&
                StringUtils.isNotEmpty(accountUuid)){
            throw new ServiceException(EXEC_ERROR.value(),"他趣ID和UUID筛选只可二选一");
        }

        String accountUuid_getByTaquId = null;
        if(StringUtils.isNotEmpty(accountId)){
            accountUuid_getByTaquId = getAccountUuidByAccountId(accountId);
            if(accountUuid_getByTaquId == null){
                throw new ServiceException(EXEC_ERROR.value(),"请输入正确的他趣ID");
            }
        }


        if (StringUtils.isEmpty(orgUuid) && orgId != null && orgId > 0) {
            Organization organization = organizationService.getOrgInfo(orgId);
            orgUuid = organization.getOrgUuid();
        }

        Integer status = null;

        if(source == null){
            //source为空 只展示申请成功的
            status = RoomStatusEnum.ACCEPT.getCode();
        }
        Integer neStatus  = RoomStatusEnum.REMOVE.getCode();



        IPage<Room> page = roomService.page(accountUuid,accountUuid_getByTaquId, orgUuid, teamId,source,status,neStatus, pageNo, pageSize);


        pageResult.setTotal(page.getTotal());

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return pageResult;
        }

        List<String> accountUuids = new ArrayList<>();
        List<Long> teamIds = new ArrayList<>();
        List<String> auditOrderNos = new ArrayList<>();
        page.getRecords()
                .forEach(room -> {
                    accountUuids.add(room.getAccountUuid());
                    teamIds.add(room.getTeamId());
                    auditOrderNos.add(room.getAuditOrderNo());
                });


        Map<String, Info> infoMap = infoManager.getInfoByUuidsNoSecret(accountUuids);
        Map<Long, Team> teamMap = teamService.getMapByTeamIds(teamIds);
        Map<String, AuditOrder> auditOrderMap = auditOrderService.getMapByOrderNos(auditOrderNos);

        Map<String, List<AuditItem>> auditItemsMap = auditOrderService.getItemListMapByOrderNos(auditOrderNos);

        List<RoomListVo> roomListVos = page.getRecords().stream()
                .map(room -> {
                    RoomListVo roomListVo = new RoomListVo();
                    BeanUtils.copyProperties(room, roomListVo);
                    //hostUuid 兼容下
                    roomListVo.setHostUuid(room.getAccountUuid());
                    roomListVo.setId(room.getId());
                    roomListVo.setModifyTime(room.getModifyTime());
                    roomListVo.setUpdateBy(room.getUpdateBy());
                    roomListVo.setCreateBy(room.getCreateBy());

                    Info info = infoMap.get(room.getAccountUuid());
                    if (info != null) {
                        roomListVo.setNickname(info.getAccountName());
                        roomListVo.setAccountId(info.getDefaultCardId());
                        roomListVo.setMobile(info.getMobile());
                        if (!StringUtils.startsWith(info.getAvatar(), "https://avatar01.jiaoliuqu.com")) {
                            roomListVo.setAvatar("https://avatar01.jiaoliuqu.com" + info.getAvatar());
                        } else {
                            roomListVo.setAvatar(info.getAvatar());
                        }
                    }

                    Team team = teamMap.get(room.getTeamId());
                    if (team != null) {
                        roomListVo.setTeamName(team.getTeamName());
                    }

                   AuditOrder auditOrder = auditOrderMap.get(room.getAuditOrderNo());
                    if(auditOrder != null){
                        roomListVo.setStatus(auditOrder.getStatus());
                        roomListVo.setRemark(auditOrder.getRemark());
                    }

                    Optional.ofNullable(auditItemsMap.get(room.getAuditOrderNo()))
                            .filter(o -> !o.isEmpty())
                            .orElse(Collections.emptyList())
                            .stream()
                            .findFirst()
                            .ifPresent(auditItem -> {
                                roomListVo.setAuditItemNo(auditItem.getNo());
                                roomListVo.setStatus(RoomStatusEnum.INIT.getCode());

                                int auditItemStatus = auditItem.getStatus();
                                if (auditItemStatus == AuditItemStatusEnum.REJECT.getCode()) {
                                    roomListVo.setStatus(RoomStatusEnum.REJECT.getCode());
                                    roomListVo.setAuditTime(auditItem.getModifyTime());
                                    roomListVo.setAuditRemark(auditItem.getRemark());
                                    roomListVo.setAuditorName(auditItem.getUpdateBy());
                                    return;
                                }
                                if (auditItemStatus == AuditItemStatusEnum.ACCEPT.getCode()) {
                                    roomListVo.setStatus(RoomStatusEnum.ACCEPT.getCode());
                                    roomListVo.setAuditTime(auditItem.getModifyTime());
                                    roomListVo.setAuditRemark(auditItem.getRemark());
                                    roomListVo.setAuditorName(auditItem.getUpdateBy());
                                    return;
                                }



                            });


                    return roomListVo;
                })
                .collect(Collectors.toList());
        pageResult.setRecords(roomListVos);
        return pageResult;


    }


    public void checkRoomStatus(int roomTaskBatchSize) {

        Long lastId = 0L;
        while (true) {
            List<Room> rooms = roomService.listByStatusOrderByIdDesc(
                    ImmutableList.of(RoomStatusEnum.INIT.getCode()),
                    lastId,
                    roomTaskBatchSize);

            if (CollectionUtils.isEmpty(rooms)) {
                break;
            }
            List<String> auditOrderNos = rooms.stream().map(Room::getAuditOrderNo)
                    .collect(Collectors.toList());
            Map<String, AuditOrder> auditOrderMapByNo
                    = auditOrderService.getMapByOrderNos(auditOrderNos);
            for (Room room : rooms) {
                String auditOrderNo = room.getAuditOrderNo();


                Optional.ofNullable(auditOrderMapByNo.get(auditOrderNo))
                        .map(AuditOrder::getStatus)
                        .ifPresent(status -> {
                            if (Objects.equals(AuditOrderStatusEnum.ALL_ACCEPT.getCode(), status)) {
                                roomService.updateStatus(room.getId(), RoomStatusEnum.ACCEPT.getCode());
                                return;
                            }
                            if (Objects.equals(AuditOrderStatusEnum.REJECT.getCode(), status)) {
                                roomService.updateStatus(room.getId(), RoomStatusEnum.REJECT.getCode());
                                return;
                            }
                            //do noting
                        });
            }
            if (CollectionUtils.isNotEmpty(rooms)) {
                lastId = rooms.get(rooms.size() - 1).getId();
            }
        }

    }

    public void remove(String roomNo){
        if(StringUtils.isEmpty(roomNo)){
            throw new ServiceException(EXEC_ERROR.value(),"roomNo为空");
        }
        roomService.removeByRoomNo(roomNo);

    }

    public void edit(RoomSubmitReq req) {
        if(Objects.isNull(req.getId())){
            throw new ServiceException(EXEC_ERROR.value(),"id不能为空");
        }
        roomService.edit(req.getId(), req);
    }

    public String getMondayDateById(Integer exportId) {
        ExportRecord record = exportRecordMapper.selectById(exportId);
        if (Objects.isNull(record)) {
            return null;
        }
        String dateStr = record.getDateRange();
        // 按空格分割字符串
        String[] parts = dateStr.split("\\s+");
        // 获取开始日期
        String startDate = parts[0];
        // 去除连接符
        String leftDate = startDate.replaceAll("-", "");
        return leftDate;
    }
}
