package cn.taqu.gonghui.chatroom.service;

import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.chatroom.entity.ForumChatRoomInfo;
import cn.taqu.gonghui.chatroom.entity.LiveHostConsortia;
import cn.taqu.gonghui.chatroom.mapper.ForumChatRoomInfoMapper;
import cn.taqu.gonghui.chatroom.mapper.LiveHostConsortiaMapper;
import cn.taqu.gonghui.common.constant.ApplyStatusEnum;
import cn.taqu.gonghui.common.constant.FormStatusEnum;
import cn.taqu.gonghui.common.constant.OrgStatusEnum;
import cn.taqu.gonghui.common.constant.TeamDefaultEnum;
import cn.taqu.gonghui.common.constant.TeamStatusEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserStatus;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.soa.InfoService;
import cn.taqu.gonghui.system.config.TeamSignConfig;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.SysUserRole;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.SysRoleMapper;
import cn.taqu.gonghui.system.mapper.SysUserRoleMapper;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.mapper.TeamMapper;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamHostService;
import cn.taqu.gonghui.system.service.TeamService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChatRoomTransferService {

    @Autowired
    private LiveHostConsortiaMapper liveHostConsortiaMapper;
    @Autowired
    private ForumChatRoomInfoMapper forumChatRoomInfoMapper;
    @SoaReference("account")
    private InfoService infoService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private TeamHostService teamHostService;
    @Autowired
    private TeamSignConfig teamSignConfig;
    @Autowired
    private TeamHostMapper teamHostMapper;


    public void transferChat() {
        List<LiveHostConsortia> liveHostConsortiaList = liveHostConsortiaMapper.selectAllChatRoomConsortia();
        if (CollectionUtils.isEmpty(liveHostConsortiaList)) {
            log.info("没有迁移的聊天室公会");
            return;
        }
        List<Long> sucessList = new ArrayList<>();
        List<LiveHostConsortia> failList = new ArrayList<>();
        log.info("聊天室公会开始迁移，要迁移的总数量:{}", liveHostConsortiaList.size());
        for (LiveHostConsortia liveHostConsortia : liveHostConsortiaList) {
            Team team = teamService.detail(liveHostConsortia.getId());
            if (team != null) {
                log.info("聊天室迁移：公会id:{}，已经在公会系统存在", liveHostConsortia.getId());
                failList.add(liveHostConsortia);
                continue;
            }
            // 调用j2获取手机号码和名称
            String mobile = "";
            String accountName = "";
            if (StringUtils.isNotBlank(liveHostConsortia.getPresidentUuid())) {
                Map<String, Object> userInfo = getInfoByUuids(liveHostConsortia.getPresidentUuid());
                if (MapUtils.isNotEmpty(userInfo)) {
                    mobile = (String) userInfo.get("mobile");
                    accountName = (String) userInfo.get("account_name");
                } else {
                    mobile = genMobile();
                    accountName = "机构管理员";
                }
            } else {
                liveHostConsortia.setPresidentUuid(sysUserService.genUniqueUuid());
                mobile = genMobile();
                accountName = "机构管理员";
            }

            if (StringUtils.isBlank(mobile)) {
                log.info("聊天室迁移：公会id:{}，会长uuid:{} 未找到用户手机号码", liveHostConsortia.getId(), liveHostConsortia.getPresidentUuid());
                failList.add(liveHostConsortia);
                continue;
            }
            List<SysUser> sysUsers = sysUserService.selectUserListByMobile(mobile);
            if (CollectionUtils.isNotEmpty(sysUsers) && sysUsers.size() > 1) {
                log.info("聊天室迁移：会长uuid:{},存在多个，异常", liveHostConsortia.getPresidentUuid());
                failList.add(liveHostConsortia);
                continue;
            }
            if (CollectionUtils.isNotEmpty(sysUsers) && sysUsers.size() == 1) {
                SysUser sysUser = sysUsers.get(0);
                if (!sysUser.getUserType().equals(UserTypeEnum.DEFAULT.getType())) {
                    log.info("聊天室迁移：会长uuid:{},已经存在并且关联了机构，机构id:{}", liveHostConsortia.getPresidentUuid(), sysUser.getOrgId());
                    failList.add(liveHostConsortia);
                    continue;
                }
            }
            try {
                inserOrgAndTeam(liveHostConsortia, mobile, accountName);
                sucessList.add(liveHostConsortia.getId());
            } catch (Exception ex) {
                log.warn("聊天室迁移-迁移公会:{} 失败了,原因：{},正在进行数据清理", liveHostConsortia.getId(), ex.getMessage());
                clearDirtyData(liveHostConsortia.getId(), mobile);
                failList.add(liveHostConsortia);
            }
        }
        log.info("迁移完毕，公会迁移成功数量:{},失败的数量:{},失败的公会集合:{}", sucessList.size(), failList.size(), failList);
    }


    private String genMobile() {
        Long mobile = 12_234_000_000L;
        while (true) {
            List<SysUser> sysUsers = sysUserService.selectUserListByMobile(String.valueOf(mobile));
            if (CollectionUtils.isEmpty(sysUsers)) {
                return String.valueOf(mobile);
            }
            mobile++;
        }
    }

    /**
     * 生成 1 机构  2 聊天室团队 3 sysusr 4 userrole
     */
    private void inserOrgAndTeam(LiveHostConsortia liveHostConsortia, String mobile, String accountName) {
        String orgName = liveHostConsortia.getTitle();
        if (StringUtils.isBlank(orgName)) {
            log.info("聊天室迁移：会长id:{},公会名字为空,已id作为机构名字", liveHostConsortia.getId());
            orgName = String.valueOf(liveHostConsortia.getId());
        }
        if (orgName.length() > 100) {
            log.info("聊天室迁移：会长id:{},公会名字:{},超出了长度，只能进行截取", orgName);
            orgName = orgName.substring(0, 99);
        }
        if (StringUtils.isBlank(accountName)) {
            accountName = "机构管理员";
        }
        orgInfoMove(liveHostConsortia, orgName, mobile, accountName);
    }

    private void orgInfoMove(LiveHostConsortia liveHostConsortia, String orgName, String mobile, String accountName) {

        SysUser sysUser = moveUser(liveHostConsortia.getPresidentUuid(), mobile, accountName);

        Organization organization = new Organization();
        Long maxOrgUuid = organizationMapper.getMaxOrgUuid();
        Long orgUuid = maxOrgUuid == null ? 210000L : maxOrgUuid++;
        Organization gi = organizationMapper.getByUuid(orgUuid.toString());
        //一开始设置的字段是uuid,设置为varchar类型,但是旧机构后台是以主键作为机构id,所以当时取max(uuid)的时候会出问题.要改动uuid为long类型改动较多,先简单处理一下
        while (null != gi) {
            log.info("uuid相同,重新生成,机构信息：{}", JsonUtils.objectToString(gi));
            orgUuid += 1;
            gi = organizationMapper.getByUuid(orgUuid.toString());
        }
        organization.setOrgUuid(orgUuid.toString());
        organization.setOrgName(orgName);
        organization.setOrgStatus(OrgStatusEnum.OPEN.getValue());
        organization.setChargePerson(accountName);
        organization.setChargePersonPhone(mobile);
        organization.setLegalPerson(accountName);
        organization.setApplyStatus(ApplyStatusEnum.SUCCESS.getValue());
        organization.setCreateTime(liveHostConsortia.getCreateTime());
        organization.setUpdateTime(liveHostConsortia.getUpdateTime());
        organization.setJoinTime(liveHostConsortia.getCreateTime());
        organization.setFormStatus(FormStatusEnum.SIX.getValue());
        organization.setAccountUuid(sysUser.getAccountUuid());
        //新机构权限默认打开直播权限其他关闭（注：这里目前只赋予权限不建立默认团队）
        organization.setLivePermissions(TeamStatusEnum.NO_VALID.getValue());
        organization.setQuliaoPermissions(TeamStatusEnum.NO_VALID.getValue());
        organization.setChatRoomPermissions(TeamStatusEnum.VALID.getValue());
        organizationMapper.insertSelective(organization);
        // 关联机构id与名称
        sysUser.setOrgId(organization.getOrgId());
        sysUser.setOrgName(orgName);
        sysUserService.updateById(sysUser);
        // 迁移团队
        moveTeam(liveHostConsortia.getId(), organization.getOrgId(), orgName, liveHostConsortia.getInviteCode());
    }

    private void moveTeam(Long teamId, Long orgId, String teamName, String inviteCode) {
        // 为当前老公会创建默认团队
        Team team = new Team();
        team.setTeamId(teamId);
        team.setOrgId(orgId);
        team.setTeamName(teamName);
        team.setSignKey("ChatRoomTeam1");
        team.setType(TeamTypeEnum.TALK_TEAM.getValue());
        team.setIsDefault(TeamDefaultEnum.DEFAULT.getValue());
        team.setStatus(1);
        team.setCreateBy("admin");
        team.setCreateTime(System.currentTimeMillis() / 1000);
        team.setUpdateTime(System.currentTimeMillis() / 1000);
        team.setInviteCode(inviteCode);
        teamMapper.insertSelective(team);

        // 创建该机构下直播默认团队
        teamService.insertDefaultTeamByType(orgId, "直播默认团队 - " + orgId, TeamTypeEnum.LIVE_TEAM.getValue(),
                0, "admin", teamSignConfig.getTeamSignByType(TeamTypeEnum.LIVE_TEAM.getValue()));
        // 创建该机构下趣聊默认团队
        teamService.insertDefaultTeamByType(orgId, "趣聊默认团队 - " + orgId, TeamTypeEnum.CALL_TEAM.getValue(),
                0, "admin", teamSignConfig.getTeamSignByType(TeamTypeEnum.CALL_TEAM.getValue()));
        //迁移聊主
        moveTeamHost(teamId, orgId);
    }

    private void moveTeamHost(Long teamId, Long orgId) {
        List<ForumChatRoomInfo> forumChatRoomInfoList = forumChatRoomInfoMapper.selectChatRoomByConsortiaId(teamId);
        if (CollectionUtils.isEmpty(forumChatRoomInfoList)) {
            log.info("公会id:{} 没有要迁移的艺人");
            return;
        }
        List<TeamHost> list = new ArrayList<>();
        for (ForumChatRoomInfo forumChatRoomInfo : forumChatRoomInfoList) {
            TeamHost teamHost = new TeamHost();
            teamHost.setHostUuid(forumChatRoomInfo.getChatUuid());
            teamHost.setOrgId(orgId);
            teamHost.setTeamId(teamId);
            teamHost.setTeamType(TeamTypeEnum.TALK_TEAM.getValue());
            teamHost.setEmployeeId(null);
            teamHost.setStatus(1);
            teamHost.setUpdateTime(forumChatRoomInfo.getUpdateTime() == null ? forumChatRoomInfo.getCreateTime() : forumChatRoomInfo.getUpdateTime());
            teamHost.setCreateTime(forumChatRoomInfo.getCreateTime());
            teamHost.setInviteTime(forumChatRoomInfo.getCreateTime());
            teamHost.setChangeTime(null);
            teamHost.setCurrentSharingProfitRate("");
            teamHost.setNewSharingProfitRate("");
            teamHost.setIsUpdate(0);
            list.add(teamHost);
        }
        // 批量插入
        if (CollectionUtils.isNotEmpty(list)) {
            teamHostMapper.batchInsertHost(list);
        }
        log.info("聊天室迁移：公会id:{},迁移聊主的数量:{}", teamId, forumChatRoomInfoList.size());
    }

    /**
     * 上线在跑一次
     *
     * @return
     */

    public void moveTeamHostOnline() {
        List<Team> chatRoomList = teamService.getChatRoomList();
        for (Team team : chatRoomList) {
            List<ForumChatRoomInfo> forumChatRoomInfoList = forumChatRoomInfoMapper.selectChatRoomByConsortiaId(team.getTeamId());
            if (CollectionUtils.isNotEmpty(forumChatRoomInfoList)) {
                int i = 0;
                for (ForumChatRoomInfo forumChatRoomInfo : forumChatRoomInfoList) {
                    TeamHost one = teamHostMapper.getOneByHostUuid(forumChatRoomInfo.getChatUuid(), TeamTypeEnum.TALK_TEAM.getValue());
                    if (one == null) {
                        TeamHost teamHost = new TeamHost();
                        teamHost.setHostUuid(forumChatRoomInfo.getChatUuid());
                        teamHost.setOrgId(team.getOrgId());
                        teamHost.setTeamId(team.getTeamId());
                        teamHost.setTeamType(TeamTypeEnum.TALK_TEAM.getValue());
                        teamHost.setEmployeeId(null);
                        teamHost.setStatus(1);
                        teamHost.setUpdateTime(forumChatRoomInfo.getUpdateTime() == null ? forumChatRoomInfo.getCreateTime() : forumChatRoomInfo.getUpdateTime());
                        teamHost.setCreateTime(forumChatRoomInfo.getCreateTime());
                        teamHost.setInviteTime(forumChatRoomInfo.getCreateTime());
                        teamHost.setChangeTime(null);
                        teamHost.setCurrentSharingProfitRate("");
                        teamHost.setNewSharingProfitRate("");
                        teamHost.setIsUpdate(0);
                        teamHostService.saveTeamHost(teamHost);
                        i++;
                    }
                }
                log.info("聊天室迁移：公会id:{},迁移聊主的数量:{}", team.getTeamId(), i);
            }
        }


    }

    private SysUser moveUser(String uuid, String mobile, String accountName) {
        SysUser sysUser = sysUserService.selectUserByAccountUuid(uuid);
        if (sysUser != null) {
            log.info("聊天室迁移：会长uuid:{},以存在系统中，只能重新注册用户", uuid);
            sysUser = sysUserService.registerAccount(mobile, UserTypeEnum.MANAGER);

        } else {
            sysUser = new SysUser();
            sysUser.setUserType(UserTypeEnum.MANAGER.getType());
            sysUser.setUserName(accountName);
            sysUser.setMobile(mobile);
            sysUser.setStatus(UserStatus.OK.getCode());
            sysUser.setCreateTime(DateUtil.currentTimeSeconds());
            sysUser.setUpdateTime(DateUtil.currentTimeSeconds());
            sysUser.setAccountUuid(uuid);
            sysUserService.save(sysUser);
        }
        // 获取管理员角色 roleId
        Long managerRoleId = sysRoleMapper.getManagerRoleId();
        SysUserRole sysUserRoleRecord = new SysUserRole();
        sysUserRoleRecord.setRoleId(managerRoleId);
        sysUserRoleRecord.setUserId(sysUser.getUserId());
        sysUserRoleRecord.setCreateTime(DateUtil.currentTimeSeconds());
        sysUserRoleRecord.setUpdateTime(DateUtil.currentTimeSeconds());
        sysUserRoleMapper.insert(sysUserRoleRecord);

        return sysUser;
    }


    /**
     * 通过用户uuid调用j2获取用户信息
     *
     * @param uuid
     * @return
     */
    private Map<String, Object> getInfoByUuids(String uuid) {
        Map<String, Object> infoMap = null;
        if (StringUtils.isNotBlank(uuid)) {
            String[] uuidArr = {uuid};
            String[] fields = {"mobile", "account_name", "email"};
            Map<String, Map<String, Object>> map = infoService.getInfoByUuidsNoSecret(uuidArr, fields, null, false, false);
            if (MapUtils.isNotEmpty(map)) {
                infoMap = map.get(uuid);
            }
        }
        return infoMap;
    }

    private void clearDirtyData(Long teamId, String mobile) {
        SysUser sysUser = sysUserService.selectUserByMobile(mobile);
        if (sysUser != null) {
            QueryWrapper<SysUser> userQueryWrapper = new QueryWrapper<>();
            userQueryWrapper.eq("mobile", mobile);
            sysUserService.remove(userQueryWrapper);
            List<SysUserRole> list = sysUserRoleMapper.getListByUserId(sysUser.getUserId());
            if (CollectionUtils.isNotEmpty(list)) {
                Set<Long> idSet = list.stream().map(SysUserRole::getId).collect(Collectors.toSet());
                sysUserRoleMapper.deleteBatchIds(idSet);
            }
        }
        organizationMapper.deleteByPrimaryKey(sysUser.getOrgId());
        teamService.deleteTeamById(teamId);
        QueryWrapper<TeamHost> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("team_id", teamId);
        teamHostService.remove(queryWrapper);
    }

}
