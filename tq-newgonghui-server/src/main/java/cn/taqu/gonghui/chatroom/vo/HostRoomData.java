package cn.taqu.gonghui.chatroom.vo;

/**
 * <AUTHOR>
 * @date 2023/9/8 14:49
 */
import lombok.Data;
import java.io.Serializable;

@Data
public class HostRoomData implements Serializable  {
    /**
     * 日期
     */
    private String dt;

    /**
     * 周范围
     */
    private String weekRange;

    /**
     * 他趣id
     */
    private String taquId;

    /**
     * 房主呢称
     */
    private String chatroomName;

    /**
     * 房主uuid
     */
    private String chatroomUuid;

    /**
     * 房主所属公会昵称
     */
    private String chatConsortiaName;

    /**
     * 房间类型
     */
    private String roomTypeDesc;

    /**
     * 开播时间段
     */
    private String timeScope;

    /**
     * 开房时长（秒）
     */
    private Integer openChatroomDuration;

    /**
     * 开房有效天数
     */
    private Integer openChatroomValid;

    /**
     * 房间收礼流水
     */
    private Integer chatBeforeTqbeanCnt;

    /**
     * 房间游戏礼物价值
     */
    private Integer chatGameGiftBeforeTqbeanCnt;

    /**
     * 房间非游戏礼物价值
     */
    private Integer chatNonGameGiftBeforeTqbeanCnt;

    /**
     * 游戏礼物占比
     */
    private String chatGameGiftRate;

    /**
     * 付费人数
     */
    private Integer chatCostUserCnt;

    /**
     * 进房DAU
     */
    private Integer enterRoomUserCnt;

    /**
     * 付费率
     */
    private String costRate;

    /**
     * 复购率
     */
    private String repurchaseRate;

    /**
     * 留存率
     */
    private String retentionRate;

    /**
     * 新用户DAU
     */
    private Integer enterChatroomNewUserCnt;

    /**
     * 新用户付费人数
     */
    private Integer costNewUserCnt;

    /**
     * 新用户付费率
     */
    private String newUserCostRate;
}