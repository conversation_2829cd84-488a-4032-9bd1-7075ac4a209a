package cn.taqu.gonghui.chatroom.cron;

import cn.taqu.core.task.annotation.SingleTask;
import cn.taqu.gonghui.chatroom.service.ChatRoomTransferService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ChatRoomTransferTask {

    @Autowired
    private ChatRoomTransferService chatRoomTransferService;

//    @SingleTask
//    @Scheduled(cron = "0 30 14 6 4 ?")
//    public void dataTransfer(){
//        chatRoomTransferService.transferChat();
//    }

//    @SingleTask
//    @Scheduled(cron = "0 30 13 7 4 ?")
    public void dataTransferOnline(){
        chatRoomTransferService.moveTeamHostOnline();
    }

}
