package cn.taqu.gonghui.chatroom.mapper;

import cn.taqu.gonghui.chatroom.entity.Room;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RoomMapper extends BaseMapper<Room> {
    List<Room> listByStatusOrderByIdDesc( @Param("statusList") List<Integer> status,
                                          @Param("lastId") Long lastId,
                                          @Param("limit") Integer limit);

    void updateCiperById(@Param("room") Room room);
}
