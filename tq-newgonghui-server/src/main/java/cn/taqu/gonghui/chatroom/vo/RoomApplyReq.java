package cn.taqu.gonghui.chatroom.vo;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.Collections;

@Getter
@Setter
public class RoomApplyReq {

    /**
     * 房主 他趣 uuid
     */
    private String accountId;

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 房主名字
     */
    private String ownerName;
    /**
     * 报备 时间范围
     */
    private String timeScope;
    /**
     * 备注
     */
    private String remark;
    /**
     * 房间类型
     * 点唱房、男模房、女模房、娱乐交友房、拍拍会房、其他
     */
    private Integer type;

    public static void main(String[] args) {
        RoomApplyReq req = new RoomApplyReq();
        req.setAccountId("1");
        req.setMobile("***********");
        req.setTimeScope("11:00-15:00");
        req.setType(1);
        req.setOwnerName("林伟健");
        req.setRemark("备注");
        System.out.println(JSON.toJSONString(req));

        System.out.println(JSON.toJSONString(Collections.singletonList(JSON.toJSONString(req))));

    }
}
