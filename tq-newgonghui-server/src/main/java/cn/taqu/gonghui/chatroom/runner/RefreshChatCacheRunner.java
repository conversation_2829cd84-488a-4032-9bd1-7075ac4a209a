package cn.taqu.gonghui.chatroom.runner;

import cn.taqu.gonghui.chatroom.service.ChatRoomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @date: 2022/9/09
 * @Description:
 */
@Component
public class RefreshChatCacheRunner implements ApplicationRunner, Ordered {
    @Autowired
    private ChatRoomService chatRoomService;


    @Override
    public int getOrder() {
        return 1;//通过设置这里的数字来知道指定顺序
    }



    @Override
    public void run(ApplicationArguments var1) throws Exception {
        chatRoomService.refreshChatCache();

    }




}
