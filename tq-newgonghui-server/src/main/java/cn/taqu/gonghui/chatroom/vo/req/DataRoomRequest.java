package cn.taqu.gonghui.chatroom.vo.req;

import cn.taqu.gonghui.common.domain.DataSortRequest;
import cn.taqu.gonghui.common.service.DataManager;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class DataRoomRequest {
    private String hostUuid;

    private String startTime;

    private String endTime;

    private Integer teamId;


    private String sort = "dt";

    private String sortType = "DESC";

    private Integer page = 1;

    private Integer pageSize = 10;

    List<DataSortRequest> sortingList = new ArrayList<>(2);
}
