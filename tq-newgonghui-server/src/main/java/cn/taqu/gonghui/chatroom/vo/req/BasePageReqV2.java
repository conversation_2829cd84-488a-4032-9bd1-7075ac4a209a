package cn.taqu.gonghui.chatroom.vo.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/8 11:48
 */
@Data
public class BasePageReqV2 implements Serializable {
    private Integer page;
    private Integer pageSize;

    public Integer getPageSize() {
        if (this.pageSize == null) {
            return 20;
        }
        return pageSize;
    }

    public Integer getPage() {
        if (this.page == null) {
            return 1;
        }
        return page;
    }
}
