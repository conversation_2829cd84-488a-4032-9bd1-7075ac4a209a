package cn.taqu.gonghui.chatroom.vo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/27 16 28
 * 才艺榜
 */
@HeadFontStyle(fontName = "宋体", fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体", fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment = HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
)
@Data
public class TalentRankDataExcel implements Serializable {

    /**
     * 日期
     */
    @ColumnWidth(20)
    @ExcelProperty("日期")
    private String dt;

    /**
     * 时间段(小时)
     */
    @ColumnWidth(20)
    @ExcelProperty("时间段")
    private String duration;

    /**
     * 才艺值排名
     */
    @ColumnWidth(20)
    @ExcelProperty("排名")
    private Integer rank;

    /**
     * 艺人uuid
     */
    @ColumnWidth(20)
    @ExcelProperty("UUID")
    private String accountUuid;

    /**
     * 他趣id
     */
    @ColumnWidth(20)
    @ExcelProperty("他趣id")
    private String accountId;

    /**
     * 工会昵称
     */
    @ColumnWidth(20)
    @ExcelProperty("所属公会")
    private String consortiaName;

    /**
     * 上麦时长(单位:秒)
     */
    @ColumnWidth(20)
    @ExcelProperty("上麦时长")
    private String meetingDuration;

    /**
     * 开麦时长(单位:秒)
     */
    @ColumnWidth(20)
    @ExcelProperty("开麦时长")
    private String meetingOpenDuration;

    /**
     * 才艺值
     */
    @ColumnWidth(20)
    @ExcelProperty("才艺值")
    private Integer receiveAmt;

}
