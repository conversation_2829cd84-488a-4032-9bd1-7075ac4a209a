package cn.taqu.gonghui.chatroom.controller;


import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.chatroom.search.ChatRoomSearch;
import cn.taqu.gonghui.chatroom.search.MasterAndApprenticeSearch;
import cn.taqu.gonghui.chatroom.service.ChatRoomService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api",params = "service=chatRoom")
public class ChatRoomController {

    @Autowired
    private ChatRoomService chatRoomService;

    /**
     * 用户端-每日运营数据--折线图与报表
     * @return
     */

    @RequestMapping(params = "method=dailyOperationData")
    @PreAuthorize("@ss.hasPermi('chatRoom:dailyOperationData')")
    public JsonResult dailyOperationDataLine(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        ChatRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<ChatRoomSearch>() {});
        return JsonResult.success(chatRoomService.dailyOperationDataLineAndReportForUser(search,page,pageSize));
    }



    /**
     * 用户端-艺人数据--小结与报表
     * @return
     */
    @RequestMapping(params = "method=chatData")
    @PreAuthorize("@ss.hasPermi('chatRoom:chatData')")
    public JsonResult chatData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        ChatRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<ChatRoomSearch>() {});
        return JsonResult.success(chatRoomService.chatDataForUser(search,page,pageSize));
    }

    /**
     * 用户端-艺人详情--详情与报表
     * @return
     */
    @RequestMapping(params = "method=chatDetail")
    @PreAuthorize("@ss.hasPermi('chatRoom:chatDetail')")
    public JsonResult chatDetail(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        ChatRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<ChatRoomSearch>() {});
        return JsonResult.success(chatRoomService.chatDetail(search,page,pageSize));
    }

    /**
     * 用户端-艺人列表
     * @return
     */
    @RequestMapping(params = "method=chatList")
    @PreAuthorize("@ss.hasPermi('chatRoom:chatList')")
    public JsonResult chatList(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        ChatRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<ChatRoomSearch>() {});
        return JsonResult.success(chatRoomService.getChatListForUser(search,page,pageSize));
    }

    @RequestMapping(params = "method=getInviteCode")
    public JsonResult getInviteCode(RequestParams params) {
        return JsonResult.success(chatRoomService.getInviteCode());
    }


    @RequestMapping(params = "method=testNickName")
    public JsonResult testNickName(RequestParams params) {
        return JsonResult.success(chatRoomService.getInviteCode());
    }


    /**
     * 用户端-师徒数据
     * @return
     */
    @RequestMapping(params = "method=masterApprenticeData")
//    @PreAuthorize("@ss.hasPermi('chatRoom:masterApprenticeData')")
    public JsonResult masterApprenticeData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
//        Integer page = params.getFormIntegerDefault(1, 0);
//        Integer pageSize = params.getFormIntegerDefault(2, 10);
        MasterAndApprenticeSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MasterAndApprenticeSearch>() {});
        return JsonResult.success(chatRoomService.pageMasterApprenticeChatDataForUser(search));
    }

    /**
     *
     * 聊天室公会-数据统计-师徒数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=masterApprenticeDataDownload")
    public JsonResult masterApprenticeDataDownload(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        MasterAndApprenticeSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MasterAndApprenticeSearch>() {
        });
        JsonResult success = JsonResult.success();
        success.setData(chatRoomService.getDownloadUrlForCurUser(search));
        return success;
    }
}
