package cn.taqu.gonghui.chatroom.controller;


import cn.taqu.core.jdbc.pagehelper.PageRequest;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.filter.annotation.CallbackApi;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.biz.blockhandler.GetConsortiaIdByUuidBlockHandler;
import cn.taqu.gonghui.biz.fallbackhandler.GetConsortiaIdByUuidFallback;
import cn.taqu.gonghui.chatroom.search.ChatRoomSearch;
import cn.taqu.gonghui.chatroom.search.MasterAndApprenticeSearch;
import cn.taqu.gonghui.chatroom.search.ShellSearch;
import cn.taqu.gonghui.chatroom.search.TalentRankSearch;
import cn.taqu.gonghui.chatroom.service.ChatRoomService;
import cn.taqu.gonghui.chatroom.service.chatroom.ShellDataService;
import cn.taqu.gonghui.chatroom.service.chatroom.TalentRankDataService;
import cn.taqu.gonghui.chatroom.vo.ChatVo;
import cn.taqu.gonghui.common.constant.FlowTypeEnum;
import cn.taqu.gonghui.common.constant.HostOperateTypeEnum;
import cn.taqu.gonghui.common.constant.TeamStatusEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.exception.CustomException;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.common.vo.req.InviteHostReq;
import cn.taqu.gonghui.common.vo.res.InviteHostItemRes;
import cn.taqu.gonghui.common.vo.res.ShellDataResp;
import cn.taqu.gonghui.common.vo.res.TalentRankDataResp;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.service.TeamHostService;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@RequestMapping(value = "/api", params = "service=manageChatRoom")
@Slf4j
public class ManageChatRoomController {

    @Autowired
    private ChatRoomService chatRoomService;

    @Autowired
    private TeamHostService teamHostService;

    @Autowired
    private TalentRankDataService talentRankingDataService;

    @Autowired
    private ShellDataService shellDataService;

    /**
     * 给app 客户端调用
     *
     * @return
     */
    @RequestMapping(params = "method=inviteChatHost", method = RequestMethod.POST)
    @ResponseBody
    @CallbackApi
    public Map inviteChatHost(String uuid, String inviteCode) {
        log.debug("inviteChatHost方法开始,uuid={},inviteCode={}",uuid,inviteCode);
        Map map = new HashMap();
        map.put("response_status", "success");
        map.put("msg", "请求成功");
        try {
            Long aLong = chatRoomService.inviteChat(uuid, inviteCode);
            Map info = new HashMap();
            info.put("data", aLong);
            info.put("extra", "");
            map.put("info", info);
            log.debug("inviteChatHost方法返回1,Map={}",JSON.toJSONString(map));
            return map;
        } catch (CustomException customException) {
            Map info = new HashMap();
            info.put("data", null);
            info.put("extra", "");
            map.put("info", info);
            if (customException.getCode() != null && customException.getCode() == 1) {
                map.put("response_status", "not_allow_bind");
            } else {
                map.put("response_status", "failed");
            }
            map.put("msg", customException.getMessage());
            log.debug("inviteChatHost方法返回2,Map={}",JSON.toJSONString(map));
            return map;
        } catch (Exception e) {
            Map info = new HashMap();
            info.put("data", null);
            info.put("extra", "");
            map.put("info", info);
            map.put("response_status", "failed");
            map.put("msg", e.getMessage());
            log.debug("inviteChatHost方法返回3,Map={}",JSON.toJSONString(map));
            return map;
        }
    }

    /**
     * 给php 调用
     *
     * @return
     */
    @RequestMapping(params = "method=inviteChatHostForPhp")
    public JsonResult inviteChatHostForPhp(RequestParams params) {
        String chatUuid = params.getFormStringDefault(0, "");
        String inviteCode = params.getFormStringDefault(1, "");
        return JsonResult.success(chatRoomService.inviteChat(chatUuid, inviteCode));
    }

    /**
     * 给app 客户端调用 通过邀请码获取团队名称
     *
     * @return
     */
    @RequestMapping(params = "method=getConsortiaName", method = RequestMethod.POST)
    @ResponseBody
    @CallbackApi
    public Map getConsortiaName(String inviteCode) {
        Map map = new HashMap();
        map.put("response_status", "success");
        map.put("msg", "请求成功");
        try {
            String name = chatRoomService.getConsortiaName(inviteCode).getTeamName();
            Map info = new HashMap();
            Map map1 = new HashMap();
            map1.put("title", name);
            info.put("data", map1);
            info.put("extra", "");
            map.put("info", info);
            return map;
        } catch (Exception e) {
            Map info = new HashMap();
            info.put("data", null);
            info.put("extra", "");
            map.put("info", info);
            map.put("response_status", "failed");
            map.put("msg", e.getMessage());
            return map;
        }
    }

    /**
     * 给php 客户端调用
     * 通过uuid获取 公会id
     *
     * @return
     */
    @RequestMapping(params = "method=getConsortiaIdByUuid")
    @SentinelResource(value = "getConsortiaIdByUuid_resource",blockHandler = "getConsortiaIdByUuidBlockHandler", blockHandlerClass = GetConsortiaIdByUuidBlockHandler.class, fallback = "getConsortiaIdByUuidFallback", fallbackClass = GetConsortiaIdByUuidFallback.class)
    public JsonResult getConsortiaIdByUuid(RequestParams params) {
        String chatUuid = params.getFormStringDefault(0, "");
        log.debug("getConsortiaIdByUuid参数chatUuid={}", chatUuid);
        //JsonResult jsonResult = JsonResult.success(chatRoomService.getTeamIdByUuid(chatUuid));
//        int  i= 0;
//        int a = 1/i;
        JsonResult jsonResult = JsonResult.success(chatRoomService.getTeamId(chatUuid));
        log.debug("getConsortiaIdByUuid返回jsonResult={}", JSON.toJSONString(jsonResult));

        return jsonResult;
    }

    /**
     * 给php 客户端调用
     * 通过uuids获取 公会ids 批量
     *
     * @return
     */
    @RequestMapping(params = "method=getBatchConsortiaIdByUuids")
    @SentinelResource(value = "getBatchConsortiaIdByUuids_resource", blockHandler = "getBatchConsortiaIdByUuidsBlockHandler", blockHandlerClass = GetConsortiaIdByUuidBlockHandler.class, fallback = "getBatchConsortiaIdByUuidsFallback", fallbackClass = GetConsortiaIdByUuidFallback.class)
    public JsonResult getBatchConsortiaIdByUuids(RequestParams params) {
        String[] chatUuids = params.getFormStringArray(0);
        List<String> list = Arrays.asList(chatUuids);
        Map chatMap = new HashMap<>(10);
        for (String str : list) {
            Long teamId = chatRoomService.getTeamId(str);
            chatMap.put(str, teamId);
        }
        return JsonResult.success(chatMap);
    }

    /**
     * 给php 客户端调用
     * 获取所以聊天室艺人的uuid 分页
     *
     * @return
     */
    @RequestMapping(params = "method=getChatListByPage")
    public JsonResult getChatListByPage(RequestParams params) {
        Integer pageNo = params.getFormIntegerDefault(0, 1);
        Integer pageSize = params.getFormIntegerDefault(1, 10);
        List<ChatVo> chatList = chatRoomService.getChatList(pageNo, pageSize);
        return JsonResult.success(new PageInfo<>(chatList));
    }

    /**
     * 给php 客户端调用
     * 通过团队ids获取 公会id-name 批量
     *
     * @return
     */
    @RequestMapping(params = "method=getBatchConsortiaNameByIds")
    public JsonResult getBatchConsortiaNameByIds(RequestParams params) {
        Long[] ids = params.getFormLongArray(0);
        return JsonResult.success(chatRoomService.getBatchTeamNameByIds(ids));
    }

    /**
     * 给php 客户端调用
     * 通过邀请码获取公会名称和id
     *
     * @return
     */
    @RequestMapping(params = "method=getConsortiaByInviteCode")
    public JsonResult getConsortiaByInviteCode(RequestParams params) {
        String inviteCode = params.getFormStringDefault(0, "");
        Map map = new HashMap();
        try {
            Team team = chatRoomService.getConsortiaName(inviteCode);
            if (team.getStatus() == TeamStatusEnum.NO_VALID.getValue()) {
                map.put("teamName", "");
                map.put("teamId", 0L);
            } else {
                map.put("teamName", team.getTeamName());
                map.put("teamId", team.getTeamId());
            }
            return JsonResult.success(map);
        } catch (Exception e) {
            map.put("teamName", "");
            map.put("teamId", 0L);
            return JsonResult.success(map);
        }

    }

    /**
     * 获取艺人详情
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getChatHostInfo")
    public JsonResult getChatInfo(RequestParams params) {
        String chatUuid = params.getFormStringDefault(0, "");
        return JsonResult.success(chatRoomService.getChatHostInfo(chatUuid));
    }


    /**
     * 管理端-每日运营数据--报表
     * 聊天室公会-数据统计-公会数据
     *
     * @return
     */
    @RequestMapping(params = "method=dailyOperationData")
    public JsonResult dailyOperationDataLine(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        ChatRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<ChatRoomSearch>() {
        });
        return JsonResult.success(chatRoomService.dailyOperationDataLineAndReportForManage(search, page, pageSize));
    }

    /**
     * 管理端-艺人数据--概览与报表
     * 聊天室公会-数据统计-艺人数据
     *
     * @return
     */
    @RequestMapping(params = "method=roomChatData")
    public JsonResult roomChatData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        ChatRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<ChatRoomSearch>() {
        });
        return JsonResult.success(chatRoomService.getRoomChatDataForManage(search, page, pageSize));
    }

    /**
     * 聊天室公会-数据统计-师徒数据
     *
     * @return
     */
    @RequestMapping(params = "method=masterApprenticeData")
    public JsonResult masterAndApprenticeData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        MasterAndApprenticeSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MasterAndApprenticeSearch>() {
        });
        search.setPage(page);
        search.setPageSize(pageSize);
        return JsonResult.success(chatRoomService.getMasterApprenticeDataForManage(search));
    }

    /**
     *
     * 聊天室公会-数据统计-师徒数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=masterApprenticeDataDownload")
    public JsonResult masterApprenticeDataDownload(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        MasterAndApprenticeSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<MasterAndApprenticeSearch>() {
        });
        JsonResult success = JsonResult.success();
        success.setData(chatRoomService.getDownloadUrl(search));
        return success;
    }


    @RequestMapping(params = "method=exportData")
    public JsonResult exportData(RequestParams params) {
        Integer total = params.getFormIntegerDefault(0, 100000);
        Integer type = params.getFormIntegerDefault(1, 1);
        String paramStr = params.getFormStringDefault(2, "");
        ChatRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<ChatRoomSearch>() {
        });
        search.setTotal(total);
        search.setType(type);
        chatRoomService.exportData(search);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=getChatRoomExportData")
    public JsonResult getChatRoomExportData(RequestParams params) {
        Integer page = params.getFormIntegerDefault(0, 0);
        String taskId = params.getFormStringDefault(1, "");
        return JsonResult.success(chatRoomService.getExportData(page, taskId));
    }


    /**
     * 管理端-艺人列表
     *
     * @return
     */
    @RequestMapping(params = "method=chatList")
    public JsonResult chatList(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        ChatRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<ChatRoomSearch>() {
        });
        return JsonResult.success(chatRoomService.getChatListForManage(search, page, pageSize));
    }

    /**
     * 管理端-艺人详情--详情与报表
     *
     * @return
     */
    @RequestMapping(params = "method=chatDetail")
    public JsonResult chatDetail(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        Integer page = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        ChatRoomSearch search = JsonUtils.stringToObject(paramStr, new TypeReference<ChatRoomSearch>() {
        });
        return JsonResult.success(chatRoomService.chatDetail(search, page, pageSize));
    }

    /**
     * 设定公会
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=changeTeamForChat")
    public JsonResult setTeam(RequestParams params) {
        String uuid = params.getFormStringDefault(0, "");
        Long teamId = params.getFormLongDefault(1, null);
        String reason = params.getFormStringDefault(2, "");
        String file = params.getFormStringDefault(3, "");
        // 设置info
        ModifyRecordInfoDTO infoDTO = new ModifyRecordInfoDTO();
        infoDTO.setNewTeamId(teamId);
        infoDTO.setReason(reason);
        infoDTO.setFiles(file);
        chatRoomService.changeTeamForChat(uuid, teamId, JsonUtils.objectToString(infoDTO), "", HostOperateTypeEnum.HOST_MOVE, TeamTypeEnum.TALK_TEAM);
        return JsonResult.success();
    }

    /**
     * 邀约记录
     * @param params
     * @return
     */
    @RequestMapping(params = "method=inviteRecordList")
    public JsonResult inviteRecordList(RequestParams params) {
        String form = params.getFormStringDefault(0, "");
        InviteHostReq inviteHostReq = JsonUtils.stringToObject(form, InviteHostReq.class);
        PageRequest pageRequest = new PageRequest(params.getFormIntegerDefault(1, 1), params.getFormIntegerDefault(2, 10));
        // 查询用户对应的机构
        ApprovalFlow approvalFlow = new ApprovalFlow();
        approvalFlow.setFlowType(FlowTypeEnum.CHATROOM_INVITE.getCode());
        approvalFlow.setHostUuid(Objects.nonNull(inviteHostReq.getUuid()) ? inviteHostReq.getUuid() : null);
        approvalFlow.setLiveNo(Objects.nonNull(inviteHostReq.getAccountId()) ? inviteHostReq.getAccountId() : null);
        approvalFlow.setOrgId(Objects.nonNull(inviteHostReq.getOrgId()) ? inviteHostReq.getOrgId() : null);

        PageInfo<InviteHostItemRes> detailResPageInfo = teamHostService.getInvitePageList(approvalFlow, pageRequest);

        return JsonResult.success(detailResPageInfo);
    }

    /**
     * 管理端-才艺榜数据
     *
     * @return
     */
    @RequestMapping(params = "method=talentRankingData")
    public JsonResult talentRankingData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        TalentRankSearch search = JsonUtils.stringToObject(paramStr, TalentRankSearch.class);
        PageDataResult<TalentRankDataResp> page = talentRankingDataService.talentRankingData(search);
        page.setTotal(0L);
        return JsonResult.success(page);
    }

    /**
     * 管理端-才艺榜数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=talentRankingDataExport")
    public JsonResult talentRankingDataExport(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        TalentRankSearch search = JsonUtils.stringToObject(paramStr, TalentRankSearch.class);
        JsonResult success = JsonResult.success();
        success.setData(ImmutableMap.of("url", talentRankingDataService.getDownloadUrl(search)));
        return success;
    }

    /**
     * 管理端-贝壳数据
     *
     * @return
     */
    @RequestMapping(params = "method=shellData")
    public JsonResult shellData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        ShellSearch search = JsonUtils.stringToObject(paramStr, ShellSearch.class);
        PageDataResult<ShellDataResp> page = shellDataService.shellData(search);
        page.setTotal(0L);
        return JsonResult.success(page);
    }

    /**
     * 管理端-贝壳数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=shellDataExport")
    public JsonResult shellDataExport(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        ShellSearch search = JsonUtils.stringToObject(paramStr, ShellSearch.class);
        JsonResult success = JsonResult.success();
        success.setData(ImmutableMap.of("url", shellDataService.getDownloadUrl(search)));
        return success;
    }

}
