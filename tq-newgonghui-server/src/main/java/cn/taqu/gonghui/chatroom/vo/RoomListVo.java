package cn.taqu.gonghui.chatroom.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class RoomListVo {

    private Long id;

    private String roomNo;
    /**
     * 房主 他趣 uuid
     */
    private String hostUuid;
    /**
     * 房主 他趣 ID
     */
    private String accountId;
    /**
     * 房主 昵称
     */
    private String nickname;

    private String ownerName;
    /**
     * 团队 名字
     */
    private String teamName;
    /**
     * 报备 时间范围
     */
    private String timeScope;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 审核项目编号 （用于审核通过/拒绝）
     */
    private String auditItemNo;

    /**
     * 房间类型
     */
    private Integer type;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 审核备注
     */
    private String auditRemark;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date auditTime;
    /**
     * 审核人名字
     */
    private String auditorName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date modifyTime;

    private String updateBy;

    private String createBy;
}
