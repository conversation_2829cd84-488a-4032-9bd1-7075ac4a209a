package cn.taqu.gonghui.chatroom.util;

import java.util.ArrayList;
import java.util.List;


public class MultiFilterConditionBuilder {

    private MultiFilterConditionBuilder() {

    }

    public static MultiFilterConditionBuilder builder() {
        return new MultiFilterConditionBuilder();
    }

    private final List<FilterCondition> filterConditions = new ArrayList<>();

    public void add(FilterCondition filterCondition) {
        if (filterCondition != null) {
            filterConditions.add(filterCondition);
        }
    }

    public List<FilterCondition> build() {
        return filterConditions;
    }

}
