package cn.taqu.gonghui.chatroom.util;

import cn.taqu.gonghui.common.constant.ConditionEnum;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterCondition {

    /**
     * 属性 key
     */
    private String key;

    /**
     * 运算符<br>
     * 1-范围运算：如日期范围，则 value 为数组，index 为 0 表示起始时间，index 为 1 表示截止时间，index 索引对应值为 null 则不过滤，
     * 如 value=[2022-03-01, 2022-03-07] 则构成 `key >= 2022-03-01 and key <= 2022-03-07`,若 value=[2022-03-01, null]
     * 则构成 `key >= 2022-03-01`；<br>
     * 2-全等运算：value 为单一值，构成 `key=value`；<br>
     * 3-最左模糊匹配：value 为单一值，构成 `key like '%value'`；<br>
     * 4-完全模糊匹配：value 为单一值，构成 `key like '%value%'`；<br>
     * 5-数组运算：value 为数组，构成 `key in (value[0], value[1], ..., value[n])`；<br>
     */
    private Integer operator;

    /**
     * 对应属性 key 的值
     */
    private Object value;

    public static FilterCondition buildDt(String startTime, String endTime) {
        return FilterCondition.builder()
                .key("dt")
                .operator(ConditionEnum.BETWEEN.getValue())
                .value(ImmutableList.of(startTime, endTime))
                .build();

    }

    public static FilterCondition buildEq(String key, Object value) {
        return FilterCondition.builder()
                .key(key)
                .operator(ConditionEnum.EQ.getValue())
                .value(value)
                .build();

    }


}
