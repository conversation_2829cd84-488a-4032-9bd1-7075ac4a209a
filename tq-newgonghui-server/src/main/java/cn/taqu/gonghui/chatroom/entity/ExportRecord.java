package cn.taqu.gonghui.chatroom.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 导出记录表
 * <AUTHOR>
 */
@Data
@TableName("export_record")
public class ExportRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private Integer bizType;
    private String dateRange;
    private String downloadUrl;
    private Date createTime;
    private Date modifyTime;
    private Integer isDeleted;

}
