package cn.taqu.gonghui.chatroom.service;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.chatroom.constant.RoomStatusEnum;
import cn.taqu.gonghui.chatroom.entity.ExportRecord;
import cn.taqu.gonghui.chatroom.entity.Room;
import cn.taqu.gonghui.chatroom.mapper.ExportRecordMapper;
import cn.taqu.gonghui.chatroom.vo.HostRoomData;
import cn.taqu.gonghui.chatroom.vo.WeekRecordVO;
import cn.taqu.gonghui.chatroom.vo.req.BasePageReq;
import cn.taqu.gonghui.chatroom.vo.req.DataRoomRequest;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.domain.DataPageRequest;
import cn.taqu.gonghui.common.domain.DataPageResult;
import cn.taqu.gonghui.common.domain.DataSortRequest;
import cn.taqu.gonghui.common.service.BaseBizService;
import cn.taqu.gonghui.common.service.DataManager;
import cn.taqu.gonghui.common.utils.OssHandler;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamHostService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.taqu.gonghui.common.constant.TeamTypeEnum.TALK_TEAM;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoomDataBizService extends BaseBizService {


    private final DataManager dataManager;

    private final TeamHostService teamHostService;

    private final SysUserService sysUserService;
    private final RoomService roomService;
    private final OssHandler ossHandler;

    private final ExportRecordMapper exportRecordMapper;

    public DataPageResult<Map<String, Object>> page4User(String apiKey, DataRoomRequest req) {
        ImmutableMap.Builder<String, Object> paramMapBuilder = ImmutableMap.builder();
        SysUser user = getLoginUser().getUser();

        boolean isManager = sysUserService.isManager(user.getUserId(), TALK_TEAM.getValue());
        if (!isManager) {
            throw new ServiceException(CodeStatus.ROLE_ERROR.value(), "当前用户不是机构管理员");
        }
        Long orgId = user.getOrgId();
        paramMapBuilder.put("org_id", orgId);


        String hostUuid = req.getHostUuid();
        if (StringUtils.isNotEmpty(hostUuid)) {
            TeamHost teamHost = teamHostService.getHostByUuidAndType(hostUuid, TALK_TEAM.getValue());
            if (teamHost == null || !Objects.equals(teamHost.getOrgId(), orgId)) {
                throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "该机构下不存在该房主");
            }
            paramMapBuilder.put("chat_room_uuid", hostUuid);
        }
        paramMapBuilder.put("dt", ImmutableList.of(req.getStartTime(), req.getEndTime()));


        List<DataSortRequest> sortList = Lists.newArrayList();
        sortList.add(new DataSortRequest(req.getSort(), req.getSortType()));
        // todo tbd
//        sortList.add(new DataSortRequest("id", "asc"));
        return dataManager.dateApiSoaV2(apiKey,
                paramMapBuilder.build(),
                new DataPageRequest(req.getPage(), req.getPageSize()),
                sortList
        );
    }

    public DataPageResult<Map<String, Object>> page4Manager(String apiKey, DataRoomRequest req) {
        ImmutableMap.Builder<String, Object> paramMapBuilder = ImmutableMap.builder();
        String hostUuid = req.getHostUuid();
        if (StringUtils.isNotEmpty(hostUuid)) {
            paramMapBuilder.put("chat_room_uuid", hostUuid);
        }


        //所属公会ID
        Integer teamId = req.getTeamId();
        if (teamId != null) {
            paramMapBuilder.put("chat_consortia_id", teamId);
        }

        paramMapBuilder.put("dt", ImmutableList.of(req.getStartTime(), req.getEndTime()));

        List<DataSortRequest> sortList = Lists.newArrayList();
        sortList.add(new DataSortRequest(req.getSort(), req.getSortType()));
        if (CollectionUtils.isNotEmpty(req.getSortingList())) {
            sortList.addAll(req.getSortingList());
        }
        return dataManager.dateApiSoaV2(apiKey,
                paramMapBuilder.build(),
                new DataPageRequest(req.getPage(), req.getPageSize()),
                sortList);
    }


    public DataPageResult<Map<String, Object>> filterRoomRemoved(DataPageResult<Map<String, Object>> dataPageResult) {

        if(CollectionUtils.isEmpty(dataPageResult.getList())){
            return dataPageResult;
        }

        List<String> accountUuids = dataPageResult.getList().stream()
                .map(m -> (String) m.get("chatroomUuid"))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());



        Map<String, List<Room>> roomsMap = roomService.getMapByAccountUuidsAndStatusGroupByAccountUuid(accountUuids,
                RoomStatusEnum.ACCEPT.getCode());
        dataPageResult.getList().removeIf(m -> {
            String chatroomUuid = (String) m.get("chatroomUuid");
            if (chatroomUuid == null) {
                return true;
            }
            if (!roomsMap.containsKey(chatroomUuid)) {
                return true;
            }
            return false;
        });
        return dataPageResult;
    }

    public PageInfo<WeekRecordVO> getHistoryWeekRecord(BasePageReq req) {
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<ExportRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExportRecord::getIsDeleted, 0);
        queryWrapper.orderByDesc(ExportRecord::getId);
        List<ExportRecord> list = exportRecordMapper.selectList(queryWrapper);
        PageInfo resPageInfo = new PageInfo(list);
        List<WeekRecordVO> dataVOList = new ArrayList<>();
        for (ExportRecord item : list) {
            WeekRecordVO weekRecordVO = new WeekRecordVO();
            BeanUtils.copyProperties(item, weekRecordVO);
            dataVOList.add(weekRecordVO);
        }
        resPageInfo.setList(dataVOList);

        return resPageInfo;
    }

    public String getDownUrl(String mondayDate) {
        if (StringUtils.isBlank(mondayDate)) {
            // 获取当前日期
            LocalDate currentDate = LocalDate.now();
            // 获取上周的开始日期（上周一）
            LocalDate lastWeekMonday = currentDate.minusWeeks(1).with(DayOfWeek.MONDAY);
            // 格式化日期为字符串
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            mondayDate = lastWeekMonday.format(formatter);
        }

        // 调用大数据接口
        List<HostRoomData> hostRoomData = dataManager.weeklyReportingDownload(mondayDate);

        // 转出中文
        List<Map<String, Object>> dataList = convertToChineseFieldNames(hostRoomData);

        // 上传得到url
        String url = ossHandler.json2DownloadUrl(JsonUtils.objectToString(dataList));

        return url;
    }

    /**
     * 将字段名转换成中文
     *
     * @param dataList List<HostRoomData> 数据列表
     * @return List<Map<String, Object>> 转换后的数据列表
     */
    public List<Map<String, Object>> convertToChineseFieldNames(List<HostRoomData> dataList) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (HostRoomData data : dataList) {
            Map<String, Object> dataMap = new LinkedHashMap<>(32);
            dataMap.put("时间周期", data.getWeekRange());
            dataMap.put("房主uuid", data.getChatroomUuid());
            dataMap.put("房主呢称", data.getChatroomName());
            dataMap.put("他趣id", data.getTaquId());
            dataMap.put("所属公会", data.getChatConsortiaName());
            dataMap.put("房间类型", data.getRoomTypeDesc());
            dataMap.put("开播时间段", data.getTimeScope());
            dataMap.put("开房时长（秒）", data.getOpenChatroomDuration());
            dataMap.put("开房有效天数", data.getOpenChatroomValid());
            dataMap.put("房间收礼流水", data.getChatBeforeTqbeanCnt());
            dataMap.put("房间游戏礼物价值", data.getChatGameGiftBeforeTqbeanCnt());
            dataMap.put("房间非游戏礼物价值", data.getChatNonGameGiftBeforeTqbeanCnt());
            dataMap.put("游戏礼物占比", data.getChatGameGiftRate());
            dataMap.put("付费人数", data.getChatCostUserCnt());
            dataMap.put("付费率", data.getCostRate());
            dataMap.put("复购率", data.getRepurchaseRate());
            dataMap.put("留存率", data.getRetentionRate());
            dataMap.put("新用户DAU", data.getEnterChatroomNewUserCnt());
            dataMap.put("新用户付费人数", data.getCostNewUserCnt());
            dataMap.put("新用户付费率", data.getNewUserCostRate());

            result.add(dataMap);
        }
        return result;
    }

    public String mondayExportHostData(String mondayDate) {
        String dateRangeStr = getDateRangeStr();

        // 先查询 获取到下载地址
        String url = getDownUrl(mondayDate);
        if (StringUtils.isBlank(url)) {
            log.info("获取到的url为空", dateRangeStr);
            return null;
        }

        // 判断是否已创建
        LambdaQueryWrapper<ExportRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExportRecord::getBizType, 1);
        queryWrapper.eq(ExportRecord::getDateRange, dateRangeStr);
        queryWrapper.eq(ExportRecord::getIsDeleted, 0);
        int count = exportRecordMapper.selectCount(queryWrapper);
        if (count > 0) {
            log.info("本周数据已存在");
            return url;
        }

        ExportRecord exportRecord = new ExportRecord();
        exportRecord.setBizType(1);
        exportRecord.setDateRange(dateRangeStr);
        exportRecord.setDownloadUrl(url);
        exportRecord.setCreateTime(new Date());
        exportRecordMapper.insert(exportRecord);
        log.info(dateRangeStr + "导出成功");
        return url;
    }

    private String getDateRangeStr() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 获取上周的开始日期（上周一）
        LocalDate lastWeekMonday = currentDate.minusWeeks(1).with(DayOfWeek.MONDAY);

        // 获取上周的结束日期（上周日）
        LocalDate lastWeekSunday = lastWeekMonday.plusDays(6);

        // 格式化日期为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String startDateString = lastWeekMonday.format(formatter);
        String endDateString = lastWeekSunday.format(formatter);

        // 时间范围字符串
        return startDateString + " ~ " + endDateString;
    }

}
