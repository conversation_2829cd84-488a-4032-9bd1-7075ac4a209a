package cn.taqu.gonghui.chatroom.service.chatroom;

import cn.taqu.gonghui.chatroom.search.ShellSearch;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.common.vo.res.ShellDataResp;

/**
 * 聊天室贝壳值
 *
 * <AUTHOR>
 * @date 2024/9/26 1:40 下午
 */
public interface ShellDataService {

    /**
     * 贝壳榜数据
     *
     * @param search
     * @return
     */
    PageDataResult<ShellDataResp> shellData(ShellSearch search);

    /**
     * 贝壳榜数据
     *
     * @param search
     * @return
     */
    PageDataResult<ShellDataResp> clientShellData(ShellSearch search);

    /**
     * 获取下载路径
     *
     * @param search
     * @return
     */
    String getDownloadUrl(ShellSearch search);

    /**
     * 获取下载路径
     *
     * @param search
     * @return
     */
    String clientGetDownloadUrl(ShellSearch search);

}
