package cn.taqu.gonghui.chatroom.controller;


import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.chatroom.search.ShellSearch;
import cn.taqu.gonghui.chatroom.search.TalentRankSearch;
import cn.taqu.gonghui.chatroom.service.chatroom.ShellDataService;
import cn.taqu.gonghui.chatroom.service.chatroom.TalentRankDataService;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.common.vo.res.ShellDataResp;
import cn.taqu.gonghui.common.vo.res.TalentRankDataResp;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api", params = "service=clientChatRoom")
@Slf4j
public class ClientChatRoomController {

    @Autowired
    private TalentRankDataService talentRankingDataService;

    @Autowired
    private ShellDataService shellDataService;

    /**
     * 管理端-才艺榜数据
     *
     * @return
     */
    @RequestMapping(params = "method=talentRankingData")
    @PreAuthorize("@ss.hasPermi('clientChatRoom:talentRankingData')")
    public JsonResult talentRankingData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        TalentRankSearch search = JsonUtils.stringToObject(paramStr, TalentRankSearch.class);
        PageDataResult<TalentRankDataResp> page = talentRankingDataService.clientTalentRankingData(search);
        return JsonResult.success(page);
    }

    /**
     * 管理端-才艺榜数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=talentRankingDataExport")
    @PreAuthorize("@ss.hasPermi('clientChatRoom:talentRankingDataExport')")
    public JsonResult talentRankingDataExport(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        TalentRankSearch search = JsonUtils.stringToObject(paramStr, TalentRankSearch.class);
        JsonResult success = JsonResult.success();
        success.setData(ImmutableMap.of("url", talentRankingDataService.clientGetDownloadUrl(search)));
        return success;
    }

    /**
     * 管理端-贝壳数据
     *
     * @return
     */
    @RequestMapping(params = "method=shellData")
    @PreAuthorize("@ss.hasPermi('clientChatRoom:shellData')")
    public JsonResult shellData(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        ShellSearch search = JsonUtils.stringToObject(paramStr, ShellSearch.class);
        PageDataResult<ShellDataResp> page = shellDataService.clientShellData(search);
        return JsonResult.success(page);
    }

    /**
     * 管理端-贝壳数据（下载）
     *
     * @return
     */
    @RequestMapping(params = "method=shellDataExport")
    @PreAuthorize("@ss.hasPermi('clientChatRoom:shellDataExport')")
    public JsonResult shellDataExport(RequestParams params) {
        String paramStr = params.getFormStringDefault(0, "");
        ShellSearch search = JsonUtils.stringToObject(paramStr, ShellSearch.class);
        JsonResult success = JsonResult.success();
        success.setData(ImmutableMap.of("url", shellDataService.clientGetDownloadUrl(search)));
        return success;
    }

}
