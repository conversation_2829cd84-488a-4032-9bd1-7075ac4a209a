package cn.taqu.gonghui.chatroom.search;

import lombok.Data;

@Data
public class ChatRoomSearch {

    private String startTime; //YYYY-mm-dd

    private String endTime;

    private Long orgId;

    private Long teamId;

    private Integer export;

    private Integer status;

    private String chatUuid;

    private String liveNo;

    private String nickName;

    /**
     * 导出的总数
     */
    private Integer total;

    /**
     * 导出类型，1-公会数据，2-艺人数据
     */
    private Integer type;
    /**
     * 现在标识，true-下载，false/null-非下载
     */
    private Boolean download;
}
