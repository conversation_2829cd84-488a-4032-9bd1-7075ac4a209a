package cn.taqu.gonghui.chatroom.service;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheUpdate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ToolService {

    /**
     * 移除缓存
     */
    @CacheInvalidate(name = "getTeamIdByUuid::", key = "#chatUuid")
    public void expireChatTeamId(String chatUuid){}

    /**
     * 更新缓存
     */
    @CacheUpdate(name = "getTeamIdByUuid::", key = "#chatUuid", value = "#teamId")
    public void updateChatTeamId(String chatUuid, Long teamId){}

}
