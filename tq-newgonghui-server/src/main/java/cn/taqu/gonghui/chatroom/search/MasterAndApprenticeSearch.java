package cn.taqu.gonghui.chatroom.search;

import cn.hutool.core.date.DateUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.chatroom.vo.req.BasePageReq;
import cn.taqu.gonghui.chatroom.vo.req.BasePageReqV2;
import com.github.pagehelper.page.PageParams;
import lombok.Data;
import org.springframework.data.domain.PageRequest;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.date.DateUtil.format;

/**
 * 师徒数据搜索
 */
@Data
public class MasterAndApprenticeSearch extends BasePageReqV2 implements Serializable {

    /**
     * 起始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 公会id 列表
     */
    private List<Long> teamIds;

    /**
     * 指定的艺人uuid列表
     */
    private List<String> uuids;

    public String getStartTimeFormat(){
        if(this.startTime ==null){
            throw new ServiceException("startTime_required", "起始时间为空");
//            return "";
        }
         return DateUtil.format(new Date(this.startTime),"yyyyMMdd");
    }

    public String getEndTimeFormat(){
        if(this.endTime ==null){
            throw new ServiceException("endTime_required", "结束时间为空");
//            return "";
        }
        return DateUtil.format(new Date(this.endTime),"yyyyMMdd");
    }

}
