package cn.taqu.gonghui.chatroom.vo;

import cn.hutool.poi.excel.ExcelFileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.alibaba.excel.util.FileUtils;
import jodd.util.StringUtil;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/3/5 16 28
 * 师徒数据
 */
@HeadFontStyle(fontName = "宋体",fontHeightInPoints = 12)
@ContentFontStyle(fontName = "宋体",fontHeightInPoints = 11)
//标题样式
@HeadStyle(wrapped = BooleanEnum.FALSE,
        horizontalAlignment= HorizontalAlignmentEnum.CENTER,
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN
//fillBackgroundColor = 9,
//,fillForegroundColor = 9
)
@Data
public class MasterApprenticeDataVO implements Serializable {


    /**
     * 他趣ID
     */
    @ColumnWidth(15)
    @ExcelProperty("他趣Id")
    private String taquId;

    /**
     * 用户uuid
     */
    @ColumnWidth(20)
    @ExcelProperty("用户uuid")
    private String userUuid;

    /**
     * 师傅昵称
     */
    @ColumnWidth(20)
    @ExcelProperty("师傅昵称")
    private String  nickName;

    /**
     * 所属团队
     */
    @ColumnWidth(20)
    @ExcelProperty("所属团队")
    private String  teamName;

    /**
     * 师傅分成金额
     */
    @ColumnWidth(20)
    @ExcelProperty("师傅分成金额")
    private String  shareAmount;

    public static File convertListToExcelFile(List<MasterApprenticeDataVO> list, String fileName, String sheetName) {
        fileName = StringUtil.isBlank(fileName) ? UUID.randomUUID().toString() : fileName;
        sheetName = StringUtil.isBlank(sheetName) ? "sheet1" : sheetName;

        File xlsxFile = null;
        try {
//            xlsxFile = FileUtils.createTmpFile(fileName + ".xlsx");
            xlsxFile = File.createTempFile(fileName, ".xlsx");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        EasyExcel.write(xlsxFile, MasterApprenticeDataVO.class).sheet(sheetName).doWrite(list);
        return xlsxFile;
    }
}
