package cn.taqu.gonghui.chatroom.service.chatroom.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.chatroom.search.TalentRankSearch;
import cn.taqu.gonghui.chatroom.service.chatroom.TalentRankDataService;
import cn.taqu.gonghui.chatroom.vo.excel.TalentRankDataExcel;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.domain.DataPageRequest;
import cn.taqu.gonghui.common.domain.DataPageResult;
import cn.taqu.gonghui.common.domain.DataSortRequest;
import cn.taqu.gonghui.common.service.DataManager;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.OssHandler;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.dto.TalentRankDataDTO;
import cn.taqu.gonghui.common.vo.res.TalentRankDataResp;
import cn.taqu.gonghui.constant.MetaContentTypeEnum;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.vo.RoleVo;
import com.alibaba.excel.EasyExcel;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/26 1:40 下午
 */
@Slf4j
@Service
public class TalentRankDataServiceImpl implements TalentRankDataService {

    @Resource
    private OssHandler ossHandler;

    @Resource
    private DataManager dataManager;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private TokenService tokenService;

    @Resource
    private TeamService teamService;

    @Resource
    private TeamEmployeeService teamEmployeeService;

    @Override
    public PageDataResult<TalentRankDataResp> talentRankingData(TalentRankSearch search) {
        ImmutableMap.Builder<String, Object> paramMap = ImmutableMap.builder();
        paramMap.put("dt", new String[]{search.getStartTimeFormat(), search.getEndTimeFormat()});
        if (StringUtils.isNotBlank(search.getHostUuid())) {
            paramMap.put("host_uuid", search.getHostUuid());
        }
        if (search.getConsortiaId() != null) {
            paramMap.put("consortia_id", search.getConsortiaId());
        }
        if (CollectionUtils.isNotEmpty(search.getTeamId())) {
            paramMap.put("team_id", search.getTeamId());
        }

        List<DataSortRequest> sortList = Lists.newArrayList();
        sortList.add(new DataSortRequest("talent_score", "desc"));
        sortList.add(new DataSortRequest("dt", "desc"));
        sortList.add(new DataSortRequest("host_uuid", "desc"));
        sortList.add(new DataSortRequest("time_point", "desc"));
        DataPageResult<Map<String, Object>> pageMap = dataManager.dateApiSoaV2("talentRankData",
                paramMap.build(),
                new DataPageRequest(search.getPage(), search.getPageSize()),
                sortList
        );

        return transferPageResp(pageMap);
    }

    @Override
    public PageDataResult<TalentRankDataResp> clientTalentRankingData(TalentRankSearch search) {
        List<Long> teamId = listTeamId();
        search.setTeamId(teamId);
        return talentRankingData(search);
    }

    /**
     * 转化dto
     *
     * @param pageMap
     * @return
     */
    private PageDataResult<TalentRankDataResp> transferPageResp(DataPageResult<Map<String, Object>> pageMap) {
        List<TalentRankDataDTO> list = JsonUtils.mapper().convertValue(pageMap.getList(), new TypeReference<List<TalentRankDataDTO>>() {
        });

        List<TalentRankDataResp> collect = list.stream().map(dto -> {
            TalentRankDataResp resp = new TalentRankDataResp();
            resp.setAccountUuid(dto.getHostUuid());
            resp.setAccountId(dto.getTaquId());
            resp.setRank(dto.getTalentRank());
            resp.setConsortiaName(dto.getConsortiaName());
            resp.setDt(dto.getDt());
            resp.setDuration(dto.getTimePoint());
            resp.setMeetingDuration(dto.getMeetingSeatDuration());
            resp.setMeetingOpenDuration(dto.getMeetingDuration());
            resp.setReceiveAmt(dto.getTalentScore());
            return resp;
        }).collect(Collectors.toList());

        PageDataResult<TalentRankDataResp> resultPage = new PageDataResult<>();
        resultPage.setPage((int) pageMap.getPage());
        resultPage.setPageSize((int) pageMap.getPageSize());
        resultPage.setList(collect);
        resultPage.setTotal(pageMap.getTotal());
        return resultPage;
    }

    @Override
    public String getDownloadUrl(TalentRankSearch search) {
        int page = 1;
        search.setPage(page);
        search.setPageSize(1000);
        PageDataResult<TalentRankDataResp> pageResult = talentRankingData(search);
        List<TalentRankDataExcel> allDataList = new ArrayList<>();
        transferAndAppend(pageResult.getList(), allDataList);

        while (CollectionUtils.isNotEmpty(pageResult.getList())) {
            page++;
            search.setPage(page);
            pageResult = talentRankingData(search);
            if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                transferAndAppend(pageResult.getList(), allDataList);
            }
        }
        return buildFileToUpload(search, allDataList);
    }

    @Override
    public String clientGetDownloadUrl(TalentRankSearch search) {
        List<Long> teamId = listTeamId();
        search.setTeamId(teamId);
        return getDownloadUrl(search);
    }

    /**
     * 构建下载路径
     *
     * @param search
     * @param allDataList
     * @return
     */
    private String buildFileToUpload(TalentRankSearch search, List<TalentRankDataExcel> allDataList) {
        // 转化列表数据为Excel文件
        String fileName = "才艺榜数据" + search.getStartTimeFormat() + "-" + search.getEndTimeFormat() + "-";
        File xlsxFile = buildExcelFile(allDataList, fileName, "才艺榜数据");
        // 获取到下载地址
        return ossHandler.getDownloadUrlWithFileAndContentType(xlsxFile, MetaContentTypeEnum.XLSX.getCode());
    }

    /**
     * 生成excel文件
     *
     * @param list
     * @param fileName
     * @param sheetName
     * @return
     */
    public static File buildExcelFile(List<TalentRankDataExcel> list, String fileName, String sheetName) {
        fileName = StringUtil.isBlank(fileName) ? UUID.randomUUID().toString() : fileName;
        sheetName = StringUtil.isBlank(sheetName) ? "sheet1" : sheetName;

        File xlsxFile;
        try {
            xlsxFile = File.createTempFile(fileName, ".xlsx");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        EasyExcel.write(xlsxFile, TalentRankDataExcel.class).sheet(sheetName).doWrite(list);
        return xlsxFile;
    }

    /**
     * 转化处理
     *
     * @param sourceList
     * @param resultList
     */
    private void transferAndAppend(List<TalentRankDataResp> sourceList, List<TalentRankDataExcel> resultList) {
        List<TalentRankDataExcel> collect = sourceList.stream().map(resp -> {
            TalentRankDataExcel excel = new TalentRankDataExcel();
            BeanUtils.copyProperties(resp, excel);
            return excel;
        }).collect(Collectors.toList());

        resultList.addAll(collect);
    }

    /**
     * 团队id
     *
     * @return
     */
    private List<Long> listTeamId() {
        RoleVo currentRole = sysUserService.getCurrentRole(TeamTypeEnum.TALK_TEAM.getValue());
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        List<Long> teamIds;
        log.info("当前角色：{},当前用户id:{}", JsonUtils.objectToString(currentRole), user.getUserId());
        if (UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())) {
            teamIds = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue()).stream().map(Team::getTeamId).collect(Collectors.toList());
        } else if (UserTypeEnum.LEADER.getCode().equals(currentRole.getRoleKey())) {
            TeamEmployee oneByUserIdAndType = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            teamIds = new ArrayList<>();
            teamIds.add(oneByUserIdAndType.getTeamId());
        } else if (UserTypeEnum.AGENTER.getCode().equals(currentRole.getRoleKey())) {
            // 经纪人角色 应该看不到
            TeamEmployee oneByUserIdAndType = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            teamIds = new ArrayList<>();
            teamIds.add(oneByUserIdAndType.getTeamId());
        } else {
            throw new ServiceException("error_token", "无权限查询数据");
        }

        return teamIds;
    }
}
