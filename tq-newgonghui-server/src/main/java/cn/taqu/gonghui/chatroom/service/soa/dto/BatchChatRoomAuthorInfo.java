package cn.taqu.gonghui.chatroom.service.soa.dto;

import cn.taqu.core.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 房主信息
 */
@Getter
@Setter
public class BatchChatRoomAuthorInfo {
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 房主头像
     */
    private String avatar;
    /**
     * 他趣ID
     */
    @JsonProperty("account_id")
    private String accountId;
    /**
     * 状态 opening-开房中 resting-休息中 baning-禁播中 delete-非聊天室房主
     */
    private String status;
    /**
     * 房主UUID
     */
    @JsonProperty("chat_uuid")
    private String chatUuid;

}
