package cn.taqu.gonghui.chatroom.service.soa.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/8 16 46
 * discription
 */
@Data
public class MasterApprenticeData implements Serializable {

    /**
     * 时间分区
     */
    private String dt;

    /**
     * 用户id
     */
//    @JsonProperty("account_uuid")
    private String account_uuid;

    /**
     * 公会id
     */
//    @JsonProperty("consortia_id")
    private Long consortia_id;

    /**
     * 公会名称
     */
//    @JsonProperty("consortia_name")
    private String consortia_name;

    /**
     * 师傅分成金额(趣豆)
     */
//    @JsonProperty("income_amt_1d")
    private Long master_income_amt;

}
