package cn.taqu.gonghui.chatroom.service.soa;

import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.chatroom.service.soa.dto.BatchChatRoomAuthorInfo;
import cn.taqu.gonghui.chatroom.service.soa.dto.ChatRoomAuthorInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdminForumChatManager {
    @SoaReference(application = "liveV1", value = "liveV1")
    private AdminForumChatClient adminForumChatClient;

    public ChatRoomAuthorInfo getChatRoomAuthorInfo(String chatUuid) {
        return adminForumChatClient.getChatRoomAuthorInfo(chatUuid);
    }
    public Map<String, BatchChatRoomAuthorInfo> getChatRoomAuthorInfoMapByChatUuid(List<String> chatUuids) {
        try{
            List<Map<String, Object>> mapList =
                    adminForumChatClient.batchGetChatRoomAuthorInfo(chatUuids);


            return mapList.stream()
                    .map(JsonUtils::objectToString2)
                    .map(s -> JsonUtils.stringToObject2(s, BatchChatRoomAuthorInfo.class))
                    .collect(Collectors.toMap(BatchChatRoomAuthorInfo::getChatUuid, Function.identity(), (o, n) -> n));

        }catch (Exception e){
            log.error("获取聊天房失败"+e.getMessage(),e);
            return Collections.emptyMap();
        }




    }

}
