package cn.taqu.gonghui.chatroom.vo;

import lombok.Data;

@Data
public class TeamHostVo {
    /**
     * 主播uuid
     */
    private String hostUuid;

    /**
     * 机构name
     */
    private String orgName;

    /**
     * 团队name
     */
    private String teamName;

    /**
     * 团队类型
     */
    private Integer teamType;


    /**
     * 所属经纪人
     */
    private String employeeName;

    /**
     * 成为房主时间
     */
    private Long inviteTime;

    /**
     * 加入时间
     */
    private String inviteDate;

    /**
     * 实名
     */
    private String realName;

    /**
     * 入会时间
     */
    private Long createTime;

    /**
     * 注册时间来自j2
     */
    private Long regsterTime;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 他趣Id
     */
    private String accountId;

    /**
     * 状态
     * status 状态 opening-开发中 resting-休息中 baning-禁播中 delete-非聊天室房主
     */
    private String status;
}
