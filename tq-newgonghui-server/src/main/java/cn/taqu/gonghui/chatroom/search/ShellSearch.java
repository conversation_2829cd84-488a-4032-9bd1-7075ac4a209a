package cn.taqu.gonghui.chatroom.search;

import cn.hutool.core.date.DateUtil;
import cn.taqu.core.exception.ServiceException;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 贝壳值检索
 *
 * <AUTHOR>
 */
@Data
public class ShellSearch {

    /**
     * 起止时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 用户uuid
     */
    private String hostUuid;

    /**
     * 工会id
     */
    private Long consortiaId;

    /**
     * 团队id
     */
    private List<Long> teamId;

    /**
     * 起始页
     */
    private Integer page;

    /**
     * 页码
     */
    private Integer pageSize;

    public String getStartTimeFormat() {
        if (this.startTime == null) {
            throw new ServiceException("startTime_required", "起始时间为空");
        }

        return DateUtil.format(new Date(this.startTime), "yyyyMMdd");
    }

    public String getEndTimeFormat() {
        if (this.endTime == null) {
            throw new ServiceException("endTime_required", "结束时间为空");
        }
        return DateUtil.format(new Date(this.endTime), "yyyyMMdd");
    }

}
