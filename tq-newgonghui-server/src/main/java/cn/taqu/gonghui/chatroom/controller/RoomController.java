package cn.taqu.gonghui.chatroom.controller;

import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.chatroom.constant.RoomTypeEnum;
import cn.taqu.gonghui.chatroom.service.RoomBizService;
import cn.taqu.gonghui.chatroom.vo.RoomApplyReq;
import cn.taqu.gonghui.chatroom.vo.req.RoomList4UserReq;
import cn.taqu.gonghui.chatroom.vo.req.DataRoomRequest;
import cn.taqu.gonghui.common.domain.DataSortRequest;
import cn.taqu.gonghui.common.domain.EnumVo;
import cn.taqu.gonghui.chatroom.service.RoomDataBizService;
import cn.taqu.gonghui.common.utils.PageWrapper;
import cn.taqu.gonghui.config.annotation.SoaForm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/api", params = "service=room")
@Slf4j
@RequiredArgsConstructor
public class RoomController {

    private final RoomBizService roomBizService;

    private final RoomDataBizService roomDataBizService;


    @RequestMapping(params = "method=page")
    public JsonResult page(@SoaForm RoomList4UserReq req) {
        return JsonResult.success(new PageWrapper<>(roomBizService.page4User(req,
                req.getPage(),
                req.getPageSize()))
        );
    }

    @RequestMapping(params = "method=submit")
    public JsonResult submit(@SoaForm RoomApplyReq req) {
        roomBizService.apply(req);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=userPaiPaiRoom")
    public JsonResult userPaiPaiRoom(@SoaForm DataRoomRequest req) {
        req.getSortingList().add(new DataSortRequest("chat_room_uuid", "asc"));
        return JsonResult.success(roomDataBizService.page4User("userPaiPaiRoom", req));
    }

    @RequestMapping(params = "method=userReportingRoom")
    public JsonResult userReportingRoom(@SoaForm DataRoomRequest req) {
        req.getSortingList().add(new DataSortRequest("chat_room_uuid", "asc"));
        return JsonResult.success(roomDataBizService.filterRoomRemoved(roomDataBizService.page4User("userReportingRoom", req)));
    }

    @RequestMapping(params = "method=userPaiPaiRoomReceiveDetail")
    public JsonResult userPaiPaiRoomReceiveDetail(@SoaForm  DataRoomRequest req) {

        return JsonResult.success( roomDataBizService.page4User("userPaiPaiRoomReceiveDetail", req));
    }

    @RequestMapping(params = "method=listRoomTypes")
    public JsonResult listRoomTypes() {
        List<EnumVo> enumVoList = new ArrayList<>();
        for (RoomTypeEnum roomTypeEnum : RoomTypeEnum.values()) {
            enumVoList.add(new EnumVo(roomTypeEnum.getCode(), roomTypeEnum.getName()));
        }
        return JsonResult.success(enumVoList);
    }


}
