package cn.taqu.gonghui.chatroom.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.PageUtil;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.jdbc.pagehelper.PageResult;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.utils.SpringContextHolder;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.gonghui.chatroom.search.ChatRoomSearch;
import cn.taqu.gonghui.chatroom.search.MasterAndApprenticeSearch;
import cn.taqu.gonghui.chatroom.service.soa.AdminForumChatService;
import cn.taqu.gonghui.chatroom.service.soa.ForumChatRoomService;
import cn.taqu.gonghui.chatroom.service.soa.dto.MasterApprenticeData;
import cn.taqu.gonghui.chatroom.vo.ChatVo;
import cn.taqu.gonghui.chatroom.vo.MasterApprenticeDataVO;
import cn.taqu.gonghui.chatroom.vo.TeamHostVo;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.constant.chatroom.ChatRoomDataEnum;
import cn.taqu.gonghui.common.constant.chatroom.ExportDataType;
import cn.taqu.gonghui.common.constant.chatroom.OrganizationDataEnum;
import cn.taqu.gonghui.common.domain.DataPageRequest;
import cn.taqu.gonghui.common.domain.DataSortRequest;
import cn.taqu.gonghui.common.exception.CustomException;
import cn.taqu.gonghui.common.service.DataManager;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.*;
import cn.taqu.gonghui.common.utils.UUID;
import cn.taqu.gonghui.common.vo.IdNameVO;
import cn.taqu.gonghui.constant.MetaContentTypeEnum;
import cn.taqu.gonghui.soa.*;
import cn.taqu.gonghui.soa.dto.BusinessRequest;
import cn.taqu.gonghui.soa.dto.Info;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.HostModifyRecordMapper;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.TeamHostOperateLogMapper;
import cn.taqu.gonghui.system.service.*;
import cn.taqu.gonghui.system.vo.RoleVo;
import cn.taqu.gonghui.system.vo.TeamOrgInfoVO;
import com.alibaba.fastjson.JSON;
import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChatRoomService {

    @Autowired
    private TeamService teamService;
    @Autowired
    private TeamHostService teamHostService;
    @SoaReference(application = "liveV1", value = "liveV1")
    private ForumChatRoomService forumChatRoomService;
    @SoaReference(application = "liveV1", value = "liveV1")
    private AdminForumChatService adminForumChatService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private TeamEmployeeService teamEmployeeService;
    @SoaReference("account")
    private InfoService infoService;
    @SoaReference("account")
    private CertWhiteListService certWhiteListService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private TeamHostOperateLogMapper teamHostOperateLogMapper;
    @Autowired
    private HostModifyRecordMapper hostModifyRecordMapper;
    @Autowired
    private HostModifyRecordService hostModifyRecordService;
    @Autowired
    private ToolService toolService;
    @Resource
    private OssHandler ossHandler;
    @Resource
    private FinanceSoaService financeSoaService;

    @Resource
    private InfoManager infoManager;

    @Resource
    private IOrgnizatonService iOrgnizatonService;

    private final static SoaServer DATA_API = SoaServer.JAVA.DATA_API;

    // 定义时间格式化对象和定义格式化样式
    private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private StringRedisTemplate masterStringRedisTemplate;
    @Autowired
    private PushService pushService;
    @Autowired
    private RedisUtil redisUtil;

    @Value("${chatroom.whitelist}")
    private String whitelist;
    @Value("${chatroom.exporturl}")
    private String exportUrl;

    @EtcdValue(value = "biz.chatroom.hostMoveCert", defaultValue = "true")
    public static String hostMoveCert;
    @Autowired
    private DataManager dataManager;

    private final static String CHATROOM_TEAM_MAP = "chatroom_team_map";


    //每日运营数据页-折线图
    private final static String DAILY_OPERATION_DATA_LINE = "/consortiaService/dailyOperationData/line";
    //每日运营数据页-报表
    private final static String DAILY_OPERATION_DATA_REPORT = "/consortiaService/dailyOperationData/report";

    //艺人数据-小结
    private final static String CHAT_DATA_COUNT = "/consortiaService/chatData/count";
    //艺人数据-报表
    private final static String CHAT_DATA_REPORT = "/consortiaService/chatData/report";

    //艺人详情页-报表
    private final static String CHAT_DETAIL_REPORT = "/consortiaService/chatDetail/report";


    //聊天室数据统计-艺人数据-概览
    private final static String ROOM_CHAT_DATA_OVERVIEW = "/consortiaService/roomChatData/overview";
    //聊天室数据统计-艺人数据-列表
    private final static String ROOM_CHAT_DATA_LIST = "/consortiaService/roomChatData/list";

    @EtcdValue(key = "biz.statistic.switchFinanceTime", defaultValue = "1719763200000")
    private static long switchFinanceTime = 1719763200000L;


    @Transactional(rollbackFor = Exception.class)
    public Long inviteChat(String chatUuid, String inviteCode) throws CustomException {
        log.info("聊天室邀请主播:{},邀请码:{}", chatUuid, inviteCode);
        if (StringUtils.isBlank(chatUuid)) {
            log.warn("聊天室邀请聊主失败，聊主uuid为空");
            throw new CustomException("聊主uuid为空", 0);
        }
        if (StringUtils.isBlank(inviteCode)) {
            log.warn("聊天室邀请聊主失败，邀请码为空");
            throw new CustomException("邀请码为空", 0);
        }
        checkSexType(chatUuid);
        Team team = teamService.getTeamByInviteCodeAndtype(inviteCode, TeamTypeEnum.TALK_TEAM.getValue());
        if (team == null || Objects.equals(team.getStatus(), Constants.NO_0)) {
            log.warn("邀请码所在团队不存在，邀请码{}", inviteCode);
            throw new CustomException("当前邀请码的聊天室团队不存在", 0);
        }
        Organization organization = organizationMapper.selectByPrimaryKey(team.getOrgId());
        if (organization == null) {
            log.warn("邀请码所在机构不存在，邀请码{}", inviteCode);
            throw new CustomException("当前邀请码的聊天室机构不存在", 0);
        }
        if (OrgStatusEnum.CLOSED.getValue() == organization.getOrgStatus()) {
            log.warn("邀请码所在机构已经关闭，无法加入，邀请码{}", inviteCode);
            throw new CustomException("该公会当前无法加入，请联系公会负责人或者在线客服了解具体情况", 1);
        }

        TeamHost host = teamHostService.getHostByUuidAndType(chatUuid, TeamTypeEnum.TALK_TEAM.getValue());
        if (host != null) {
            // 判断用户是否有直播业务，如果有则需要判断是否同一个机构
            checkChatOrgSameWithLive(host.getOrgId(), chatUuid, true);
            log.warn("当前聊主已经存在，不能在邀请，聊主Uuid:{}", chatUuid);
            throw new CustomException("当前聊主已经在，不能在邀请", 0);
        }

        TeamHost teamHost = new TeamHost();
        teamHost.setTeamId(team.getTeamId());
        teamHost.setOrgId(team.getOrgId());
        teamHost.setTeamType(TeamTypeEnum.TALK_TEAM.getValue());
        teamHost.setHostUuid(chatUuid);
        teamHost.setUpdateTime(DateUtil.currentTimeSeconds());
        teamHost.setCreateTime(DateUtil.currentTimeSeconds());
        teamHost.setInviteTime(DateUtil.currentTimeSeconds());
        teamHost.setChangeTime(0l);
        teamHost.setStatus(1);
        teamHost.setEmployeeId(null);
        teamHost.setNewSharingProfitRate("");
        teamHost.setCurrentSharingProfitRate("");
        teamHost.setIsUpdate(0);
        teamHostService.saveTeamHost(teamHost);
        // 插入日志
        ModifyRecordInfoDTO modifyRecordInfo = new ModifyRecordInfoDTO();
        modifyRecordInfo.setOldOrgId(0L);
        modifyRecordInfo.setOldTeamId(0L);
        modifyRecordInfo.setNewOrgId(teamHost.getOrgId());
        modifyRecordInfo.setNewTeamId(teamHost.getTeamId());
        hostModifyRecordService.addRecord(chatUuid, TeamTypeEnum.TALK_TEAM.getValue(), UUID.genUuid(), HostOperateTypeEnum.INVITE_CHAT_ROOM_TEAM, modifyRecordInfo, "", Constants.YES_1, "", "system");
        SpringUtils.getBean(ChatRoomService.class).refreshChatCache();
        toolService.expireChatTeamId(chatUuid);
        try {
            // 加入白名单
            certWhiteListService.addCertWhiteListFromServer(chatUuid, 2, "加入聊天室公会", "系统自动加入");
        } catch (Exception e) {
            log.warn("加入认证白名单失败", e);
        }
        return team.getTeamId();
    }

    private void checkSexType(String uuid) {
        String[] fileds = new String[]{"uuid", "sex_type"};
        Map<String, String> map = infoService.getInfoByUuidOrNicknameOrCardId(uuid, fileds);
        if (MapUtils.isEmpty(map)) {
            log.warn("聊天室邀请用户{} 在用户系统中为找到", uuid);
            throw new ServiceException("use_is_not_exsit", "当前用户不存在");
        }
        Map info = MapUtils.getMap(map, "account_info");
        if (MapUtils.isEmpty(info)) {
            log.warn("聊天室邀请用户{} 在用户系统中为找到", uuid);
            throw new ServiceException("use_is_not_exsit", "您无法自助加入公会，请联系对接运营处理");
        }
        Integer sexType = MapUtils.getInteger(info, "sex_type");
        if (sexType == null || sexType != 2) {
            log.warn("聊天室邀请用户{} 失败，只能邀请女性用户", uuid);
            throw new ServiceException("sex_type_error", "您无法自助加入公会，请联系对接运营处理");
        }

    }

    public Team getConsortiaName(String inviteCode) {
        if (StringUtils.isBlank(inviteCode)) {
            log.warn("聊天室邀请聊主失败，邀请码为空");
            throw new CustomException("邀请码为空");
        }
        Team team = teamService.getTeamByInviteCodeAndtype(inviteCode, TeamTypeEnum.TALK_TEAM.getValue());
        if (team == null) {
            log.warn("邀请码所在团队不存在，邀请码{}", inviteCode);
            throw new CustomException("当前邀请码的聊天室团队不存在");
        }
        Organization organization = organizationMapper.selectByPrimaryKey(team.getOrgId());
        if (organization.getOrgStatus() == OrgStatusEnum.CLOSED.getValue()) {
            team.setTeamId(0L);
            team.setTeamName("");
        }
        return team;
    }

    public Map<String, Object> getChatHostInfo(String chatUuid) {
        if (StringUtils.isBlank(chatUuid)) {
            throw new ServiceException("invite_code_empty", "当前聊主uuid为空");
        }
        Map<String, Object> chatHostInfo = adminForumChatService.getChatRoomAuthorInfo(chatUuid);
        if (MapUtils.isEmpty(chatHostInfo)) {
            throw new ServiceException("chat_host_not_found", "艺人信息未找到");
        }
        TeamHost teamHost = teamHostService.getHostByUuidAndType(chatUuid, TeamTypeEnum.TALK_TEAM.getValue());
        if (teamHost != null) {
            if (teamHost.getTeamId() != null) {
                chatHostInfo.put("inviteTime", teamHost.getCreateTime());
            } else {
                chatHostInfo.put("inviteTime", 0);
            }
            if (teamHost.getEmployeeId() != null && teamHost.getEmployeeId() != 0) {
                TeamEmployee teamEmployee = teamEmployeeService.getOne(teamHost.getEmployeeId());
                if (teamEmployee != null) {
                    chatHostInfo.put("agenterName", teamEmployee.getEmployeeName());
                } else {
                    chatHostInfo.put("agenterName", "");
                }
            } else {
                chatHostInfo.put("agenterName", "");
            }
        } else {
            chatHostInfo.put("inviteTime", 0);
            chatHostInfo.put("agenterName", "");
            log.warn("艺人uuid:{},未在team_host查询到", chatUuid);
        }
        return chatHostInfo;
    }


    public Map dailyOperationDataLineAndReportForUser(ChatRoomSearch search, Integer page, Integer pageSize) {
        validateTime(search);
        RoleVo currentRole = sysUserService.getCurrentRole(TeamTypeEnum.TALK_TEAM.getValue());
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        List<Long> teamIds = null;
        log.info("当前角色：{},当前用户id:{}", JsonUtils.objectToString(currentRole), user.getUserId());
        if (UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())) {
            teamIds = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue()).stream().map(Team::getTeamId).collect(Collectors.toList());

            if (search.getTeamId() != null) {
                if (teamIds.contains(search.getTeamId())) {
                    teamIds = Arrays.asList(search.getTeamId());
                } else {
                    throw new ServiceException("end_time_null", "当前所选团队不在该机构中");
                }
            }
        }
        if (UserTypeEnum.LEADER.getCode().equals(currentRole.getRoleKey())) {
            TeamEmployee oneByUserIdAndType = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            teamIds = new ArrayList<>();
            teamIds.add(oneByUserIdAndType.getTeamId());
            if (search.getTeamId() != null && !search.getTeamId().equals(oneByUserIdAndType.getTeamId())) {
                throw new ServiceException("end_time_null", "你不能查询该团队数据");
            }
        }
        if (UserTypeEnum.AGENTER.getCode().equals(currentRole.getRoleKey())) {
            // 经纪人角色 应该看不到
            TeamEmployee oneByUserIdAndType = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            teamIds = new ArrayList<>();
            teamIds.add(oneByUserIdAndType.getTeamId());
            if (search.getTeamId() != null && !search.getTeamId().equals(oneByUserIdAndType.getTeamId())) {
                throw new ServiceException("end_time_null", "你不能查询该团队数据");
            }
        }

        if (search.getExport() == null) {
            search.setExport(0);
        }
        Map map = new HashMap();

        List<DataManager.Sorting> sortingList = new ArrayList<>();
        sortingList.add(new DataManager.Sorting("dt", "desc"));
        sortingList.add(new DataManager.Sorting("consortia_id", "asc"));
        if (search.getExport() == 0) {
            DataManager.sortingHolder.set(Collections.singletonList(new DataManager.Sorting("dt", "asc")));
            List lineList = dataApiSoa(DAILY_OPERATION_DATA_LINE, search.getStartTime(), search.getEndTime(), teamIds, null, null, null, List.class);
            Map pageMap = new HashMap();
            pageMap.put("page", page);
            pageMap.put("pageSize", pageSize);
            DataManager.sortingHolder.set(sortingList);
            PageResult reportData = dataApiSoa(DAILY_OPERATION_DATA_REPORT, search.getStartTime(), search.getEndTime(), teamIds, null, null, pageMap, PageResult.class);
            getTeamName(reportData.getData());
            map.put("lineData", lineList);
            map.put("reportData", reportData);
        } else if (search.getExport() == 1) {
            DataManager.sortingHolder.set(sortingList);
            List<Map> reportData = dataApiSoa(DAILY_OPERATION_DATA_REPORT, search.getStartTime(), search.getEndTime(), teamIds, null, null, null, List.class);
            getTeamName(reportData);
            map.put("reportData", reportData);
        }
        return map;
    }

    /**
     * 聊天室公会-数据统计-公会数据
     *
     * @param search
     * @param page
     * @param pageSize
     * @return
     */
    public PageResult dailyOperationDataLineAndReportForManage(ChatRoomSearch search, Integer page, Integer pageSize) {
        // validateTime(search);
        List<Long> teamIds = null;
        if (search.getOrgId() != null) {
            teamIds = teamService.selectTeamList(search.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue()).stream().map(Team::getTeamId).collect(Collectors.toList());
            if (search.getTeamId() != null) {
                if (teamIds.contains(search.getTeamId())) {
                    teamIds = Arrays.asList(search.getTeamId());
                } else {
                    throw new ServiceException("end_time_null", "当前所选团队不在该机构中");
                }
            }
        }
        if (search.getTeamId() != null) {
            teamIds = Arrays.asList(search.getTeamId());
        }
        List<DataManager.Sorting> sortingList = new ArrayList<>();
        sortingList.add(new DataManager.Sorting("dt", "desc"));
        sortingList.add(new DataManager.Sorting("consortia_id", "asc"));
        if (search.getDownload() == null || !search.getDownload()) {
            Map pageMap = new HashMap();
            pageMap.put("page", page);
            pageMap.put("pageSize", pageSize);
            DataManager.sortingHolder.set(sortingList);
            PageResult result = dataApiSoa(DAILY_OPERATION_DATA_REPORT, search.getStartTime(), search.getEndTime(), teamIds, null, null, pageMap, PageResult.class);
            getTeamName(result.getData());
            getOrgName(result.getData());
            return result;
        }
        // 下载的就去循环拉取
        int p = 1;
        PageResult result = new PageResult(new ArrayList(), 0L);
        while (true) {
            Map pageMap = new HashMap();
            pageMap.put("page", p++);
            pageMap.put("pageSize", 1000);
            DataManager.sortingHolder.set(sortingList);
            PageResult soaResult = dataApiSoa(DAILY_OPERATION_DATA_REPORT, search.getStartTime(), search.getEndTime(), teamIds, null, null, pageMap, PageResult.class);
            if (CollectionUtils.isEmpty(soaResult.getData())) {
                break;
            }
            getTeamName(soaResult.getData());
            getOrgName(soaResult.getData());
            result.setTotal(soaResult.getTotal());
            result.getData().addAll(soaResult.getData());
        }
        return result;
    }

    public Map chatDataForUser(ChatRoomSearch search, Integer page, Integer pageSize) {
        log.info("chatDataForUser方法开始,search={},page={},pageSize={}", JSON.toJSONString(search), page, pageSize);
        validateTime(search);
        RoleVo currentRole = sysUserService.getCurrentRole(TeamTypeEnum.TALK_TEAM.getValue());
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        List<Long> teamIds = null;
        List<String> hostUUidList = null;
        if (UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())) {
            teamIds = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue()).stream().map(Team::getTeamId).collect(Collectors.toList());

            if (search.getTeamId() != null) {
                if (teamIds.contains(search.getTeamId())) {
                    teamIds = Arrays.asList(search.getTeamId());
                } else {
                    throw new ServiceException("end_time_null", "当前所选团队不在该机构中");
                }
            }
        }
        if (UserTypeEnum.LEADER.getCode().equals(currentRole.getRoleKey())) {
            TeamEmployee oneByUserIdAndType = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            teamIds = new ArrayList<>();
            teamIds.add(oneByUserIdAndType.getTeamId());
            if (search.getTeamId() != null && !search.getTeamId().equals(oneByUserIdAndType.getTeamId())) {
                throw new ServiceException("end_time_null", "你不能查询该团队数据");
            }
        }
        if (UserTypeEnum.AGENTER.getCode().equals(currentRole.getRoleKey())) {
            TeamEmployee oneByUserIdAndType = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            teamIds = new ArrayList<>();
            teamIds.add(oneByUserIdAndType.getTeamId());
            if (search.getTeamId() != null && !search.getTeamId().equals(oneByUserIdAndType.getTeamId())) {
                throw new ServiceException("end_time_null", "你不能查询该团队数据");
            }
            List<TeamHost> teamHostList = teamHostService.getListByEmployeeId(oneByUserIdAndType.getEmployeeId(), TeamTypeEnum.TALK_TEAM.getValue());
            if (CollectionUtils.isEmpty(teamHostList)) {
                Map map = new HashMap();
                map.put("chatDataCount", null);
                map.put("chatDataReport", null);
                return map;
            }
            hostUUidList = teamHostList.stream().map(TeamHost::getHostUuid).collect(Collectors.toList());
        }

        if (search.getExport() == null) {
            search.setExport(0);
        }
        Map map = new HashMap();
        if (search.getExport() == 0) {
            log.info("chatDataForUser请求dataApiSoa={}", CHAT_DATA_COUNT);
            List<Map> chatDataCount = dataApiSoa(CHAT_DATA_COUNT, search.getStartTime(), search.getEndTime(), teamIds, hostUUidList, search.getStatus(), null, List.class);
            log.info("chatDataForUser返回dataApiSoa={}", CHAT_DATA_COUNT);
            Map pageMap = new HashMap();
            pageMap.put("page", page);
            pageMap.put("pageSize", pageSize);
            log.info("chatDataForUser请求dataApiSoa={}", CHAT_DATA_REPORT);
            PageResult chatDataReport = dataApiSoa(CHAT_DATA_REPORT, search.getStartTime(), search.getEndTime(), teamIds, hostUUidList, search.getStatus(), pageMap, PageResult.class);
            log.info("chatDataForUser返回dataApiSoa={}", CHAT_DATA_REPORT);
            getTeamName(chatDataReport.getData());
            getAgenterName(chatDataReport.getData());
            getNickNameAndAccountId(chatDataReport.getData());

            map.put("chatDataCount", getChatDataCount(chatDataCount));
            map.put("chatDataReport", chatDataReport);
        } else if (search.getExport() == 1) {
            log.info("chatDataForUser请求dataApiSoa={}", CHAT_DATA_REPORT);
            List chatDataReport = dataApiSoa(CHAT_DATA_REPORT, search.getStartTime(), search.getEndTime(), teamIds, hostUUidList, search.getStatus(), null, List.class);
            log.info("chatDataForUser返回dataApiSoa={}", CHAT_DATA_REPORT);
            getTeamName(chatDataReport);
            getAgenterName(chatDataReport);
            getNickNameAndAccountId(chatDataReport);
            map.put("chatDataReport", chatDataReport);
        }

        log.info("chatDataForUser方法结束,map大小={}", map.size());

        return map;
    }

    private Map<String, Integer> getChatDataCount(List<Map> chatDataCount) {
        Integer consume_cnt = 0;
        Integer receive_amt = 0;
        Integer chat_receive_amt = 0;
        Map<String, Integer> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(chatDataCount)) {
            return chatDataCount.get(0);
        }
        map.put("consume_cnt", consume_cnt);
        map.put("receive_amt", receive_amt);
        map.put("chat_receive_amt", chat_receive_amt);
        return map;
    }

    private void getNickNameAndAccountId(List<Map> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Set<String> uuidSet = list.stream().map(item -> MapUtils.getString(item, "account_uuid")).collect(Collectors.toSet());
        List<String> uuids = new ArrayList<>(uuidSet);
        int pageSize = 100;
        // 需要分页处理
        List<Map<String, Object>> allMaps = new ArrayList<>();
        int size = uuids.size();
        int pageCount = size % pageSize == 0 ? size / pageSize : (size / pageSize) + 1;
        for (int page = 1; page <= pageCount; page++) {
            int pageStart = (page - 1) * pageSize;
            int pageEnd = page * pageSize;
            if(page == pageCount){
                pageEnd = size;
            }
            List<String> pageUuids = uuids.subList(pageStart, pageEnd);
            List<Map<String, Object>> maps = adminForumChatService.batchGetChatRoomAuthorInfo(pageUuids);
            if (CollectionUtils.isNotEmpty(maps)) {
                allMaps.addAll(maps);
            }
        }

//        List<Map<String, Object>> maps = adminForumChatService.batchGetChatRoomAuthorInfo(uuids);
        for (Map map : list) {
            String accountUuid = MapUtils.getString(map, "account_uuid");
            Map<String, Object> mapByUuid = getMapByUuid(allMaps, accountUuid);
            map.putAll(mapByUuid);
        }
    }

    public Map<String, String> getInviteCode() {
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        return teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue()).stream().filter(item -> StringUtils.isNotBlank(item.getInviteCode())).collect(Collectors.toMap(Team::getTeamName, Team::getInviteCode));
    }

    public Map chatDetail(ChatRoomSearch search, Integer page, Integer pageSize) {
        validateTime(search);
        if (StringUtils.isBlank(search.getChatUuid())) {
            throw new ServiceException("uuid_null", "请选择一个艺人");
        }
        List<String> chatUuids = Arrays.asList(search.getChatUuid());
        List<DataManager.Sorting> sortingList = new ArrayList<>();
        sortingList.add(new DataManager.Sorting("dt", "asc"));
        sortingList.add(new DataManager.Sorting("account_uuid", "asc"));
        Map map = new HashMap();
        if (search.getExport() == 0) {
            Map pageMap = new HashMap();
            pageMap.put("page", page);
            pageMap.put("pageSize", pageSize);
            Map<String, Object> chatDetailInfo = getChatHostInfo(search.getChatUuid());
            map.put("chatDetailInfo", chatDetailInfo);

            DataManager.sortingHolder.set(sortingList);
            PageResult chatDetailReport = dataApiSoa(CHAT_DETAIL_REPORT, search.getStartTime(), search.getEndTime(), null, chatUuids, null, pageMap, PageResult.class);
            map.put("chatDetailReport", chatDetailReport);
        } else if (search.getExport() == 1) {
            DataManager.sortingHolder.set(sortingList);
            List chatDetailReport = dataApiSoa(CHAT_DETAIL_REPORT, search.getStartTime(), search.getEndTime(), null, chatUuids, null, null, List.class);
            map.put("chatDetailReport", chatDetailReport);
        }
        return map;
    }

    /**
     * 给管理端用的
     * 拉取师徒数据
     *
     * @param search 搜索条件
     * @return
     */
    public PageDataResult getMasterApprenticeDataForManage(MasterAndApprenticeSearch search) {


        ImmutableMap.Builder<String, Object> paramMapBuilder = ImmutableMap.builder();
//        paramMapBuilder.put("dt", ImmutableList.of(search.getStartTime(), search.getEndTime()));
        ImmutableMap.Builder<String, Object> paramMap = paramMapBuilder
                .put("start_dt", search.getStartTimeFormat())
                .put("end_dt", search.getEndTimeFormat());
        if (CollectionUtils.isNotEmpty(search.getUuids())) {
            paramMap.put("account_uuid", search.getUuids());
        }
        if (CollectionUtils.isNotEmpty(search.getTeamIds())) {
            paramMap.put("consortia_id", search.getTeamIds());
        }
        ImmutableMap<String, Object> params = paramMap.build();

        cn.taqu.gonghui.common.domain.DataPageResult<Map<String, Object>> mapDataPageResult = dataManager.dateApiSoaWithExcuteV2("chatroomConsortiaIncomeAmt", params,
                new DataPageRequest(search.getPage(), search.getPageSize()),
                new DataSortRequest("dt", "DESC"));

        List<MasterApprenticeData> dataList = new ArrayList<>();
        List<Map<String, Object>> list = mapDataPageResult.getList();
        for (Map<String, Object> mapItem : list) {
            MasterApprenticeData item = BeanUtil.mapToBean(mapItem, MasterApprenticeData.class, true);
            dataList.add(item);
        }
        List<MasterApprenticeDataVO> voList = convertToVoAndFillAccountInfo(dataList);
        PageDataResult<MasterApprenticeDataVO> result = new PageDataResult<>(search.getPage(), search.getPageSize(),
                mapDataPageResult.getTotal(), voList);
        return result;
    }

    private List<MasterApprenticeDataVO> convertToVoAndFillAccountInfo(List<MasterApprenticeData> dataList) {
        List<MasterApprenticeDataVO> voList = new ArrayList<>();
        List<String> uuidList = dataList.stream().map(l -> l.getAccount_uuid()).collect(Collectors.toList());
        Map<String, Info> accountInfoMap = infoManager.getInfoByUuidsNoSecret(uuidList);
        dataList.forEach(l -> {
            MasterApprenticeDataVO voItem = new MasterApprenticeDataVO();
            Info info = accountInfoMap.get(l.getAccount_uuid());
            voItem.setTeamName(l.getConsortia_name());
            voItem.setShareAmount(l.getMaster_income_amt().toString());
            voItem.setUserUuid(l.getAccount_uuid());
            if (info != null) {
                voItem.setNickName(info.getAccountName());
                voItem.setTaquId(info.getDefaultCardId());
            }
            voList.add(voItem);
        });
        return voList;
    }

    /**
     * 给用户端用的
     * 拉取师徒数据
     *
     * @param search
     * @return
     */
    public PageDataResult pageMasterApprenticeChatDataForUser(MasterAndApprenticeSearch search) {

        buildSearchParamByCurUserRole(search);
        if (CollectionUtils.isEmpty(search.getTeamIds()) && CollectionUtils.isEmpty(search.getUuids())) {
            PageDataResult<MasterApprenticeDataVO> result = new PageDataResult<>(search.getPage(), search.getPageSize(),
                    0, new ArrayList<>());
            return result;
        }

        PageDataResult result = getMasterApprenticeDataForManage(search);
        return result;
    }

    private void buildSearchParamByCurUserRole(MasterAndApprenticeSearch search) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new ServiceException("un_login", "未登录，请先登录~");
        }

        RoleVo currentRole = sysUserService.getCurrentRole(TeamTypeEnum.TALK_TEAM.getValue());
        if (UserTypeEnum.AGENTER.getCode().equals(currentRole.getRoleKey())) {//角色：经纪人 -->> 获取管理的艺人列表
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            List<TeamHost> hostList = teamHostService.getListByEmployeeId(teamEmployee.getEmployeeId(), TeamTypeEnum.TALK_TEAM.getValue());
            if (CollectionUtils.isNotEmpty(hostList)) {
                List<String> hostUuids = hostList.stream().map(l -> l.getHostUuid()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(hostUuids)) {
                    search.setUuids(hostUuids);
                }
            }
        } else if (UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())) { //角色：机构管理员 -->>获取机构下的团队
            List<Team> teams = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue());
            if (CollectionUtils.isNotEmpty(teams)) {
                List<Long> teamIds = teams.stream().map(l -> l.getTeamId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(teamIds)) {
                    search.setTeamIds(teamIds);
                }
            }
        } else if (UserTypeEnum.LEADER.getCode().equals(currentRole.getRoleKey())) { //角色:团队负责人 -->>获取对应的团队ID
            // 获取对应的团队
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            if (teamEmployee == null) {
                log.warn("当前用户为团队负责人，但未找到用户对应的团队。userId={}", user.getUserId());
            } else {
                List<Long> teamIds = new ArrayList<>();
                teamIds.add(teamEmployee.getTeamId());
                search.setTeamIds(teamIds);
            }
        } else {
            throw new ServiceException("unknown_role", "未知角色，请确认~");
        }
    }

    public String getDownloadUrl(MasterAndApprenticeSearch search) {
        Integer page = 1;
        Integer pageSize = 1000;
        search.setPage(page);
        search.setPageSize(pageSize);
        PageDataResult dataResult = getMasterApprenticeDataForManage(search);
        List<MasterApprenticeDataVO> allDataList = new ArrayList<>();
        allDataList.addAll(dataResult.getList());

        long totalPage = PageUtil.totalPage(dataResult.getTotal().intValue(), pageSize);

        while (page < totalPage) {
            page++;
            search.setPage(page);
            PageDataResult pageResult = getMasterApprenticeDataForManage(search);
            if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                allDataList.addAll(pageResult.getList());
            }
        }
        return buildFileToUpload(search, allDataList);
    }

    private String buildFileToUpload(MasterAndApprenticeSearch search, List<MasterApprenticeDataVO> allDataList) {
        // 转化列表数据为Excel文件
        String fileName = "师徒数据" + search.getStartTimeFormat() + "-" + search.getEndTimeFormat() + "-";
        File xlsxFile = MasterApprenticeDataVO.convertListToExcelFile(allDataList, fileName, "师徒分成数据");
        // 获取到下载地址
        String downloadUrl = ossHandler.getDownloadUrlWithFileAndContentType(xlsxFile, MetaContentTypeEnum.XLSX.getCode());
        return downloadUrl;
    }

    public String getDownloadUrlForCurUser(MasterAndApprenticeSearch search) {
        buildSearchParamByCurUserRole(search);
        if (CollectionUtils.isEmpty(search.getTeamIds()) && CollectionUtils.isEmpty(search.getUuids())) {
            return buildFileToUpload(search, new ArrayList<>());
        }
        return getDownloadUrl(search);
    }


    /**
     * 给管理端用
     * 聊天室公会-数据统计-艺人数据
     */
    public Map getRoomChatDataForManage(ChatRoomSearch search, Integer page, Integer pageSize) {
        validateTime(search);
        List<Long> teamIds = null;
        if (search.getOrgId() != null) {
            teamIds = teamService.selectTeamList(search.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue()).stream().map(Team::getTeamId).collect(Collectors.toList());
            if (search.getTeamId() != null) {
                if (teamIds.contains(search.getTeamId())) {
                    teamIds = Arrays.asList(search.getTeamId());
                } else {
                    throw new ServiceException("end_time_null", "当前所选团队不在该机构中");
                }
            }
        }
        if (search.getTeamId() != null) {
            teamIds = Arrays.asList(search.getTeamId());
        }
        List<String> chatUuids = new ArrayList<>();
        if (StringUtils.isNotBlank(search.getChatUuid())) {
            chatUuids.add(search.getChatUuid());
        }
        if (StringUtils.isNotBlank(search.getLiveNo())) {
            String uuid = getUuidByNickNameOrCarId(search.getLiveNo());
            if (StringUtils.isBlank(uuid)) {
                if (CollectionUtils.isEmpty(chatUuids)) {
                    Map map = new HashMap();
                    Map view = new HashMap<>();
                    view.put("receive_amt", 0);
                    view.put("consume_amt", 0);
                    map.put("roomChatDataOverview", view);
                    map.put("roomChatDataList", null);
                    return map;
                }
            } else {
                chatUuids.add(uuid);
            }
        }
        Map map = new HashMap();
        List<Map> roomChatDataOverview = dataApiSoa(ROOM_CHAT_DATA_OVERVIEW, search.getStartTime(), search.getEndTime(), teamIds, chatUuids, null, null, List.class);
        map.put("roomChatDataOverview", getManageChatDataCount(roomChatDataOverview));

        List<DataManager.Sorting> sortingList = new ArrayList<>();
        sortingList.add(new DataManager.Sorting("dt", "desc"));
        sortingList.add(new DataManager.Sorting("account_uuid", "asc"));
        sortingList.add(new DataManager.Sorting("consortia_id", "asc"));
        DataManager.sortingHolder.set(sortingList);
        PageResult roomChatDataList = new PageResult(new ArrayList(), 0L);
        if (search.getDownload() == null || !search.getDownload()) {
            Map pageMap = new HashMap();
            pageMap.put("page", page);
            pageMap.put("pageSize", pageSize);
            roomChatDataList = dataApiSoa(ROOM_CHAT_DATA_LIST, search.getStartTime(), search.getEndTime(), teamIds, chatUuids, null, pageMap, PageResult.class);
            getOrgName(roomChatDataList.getData());
            getTeamName(roomChatDataList.getData());
            getNickNameAndAccountId(roomChatDataList.getData());
        } else {
            int p = 1;
            while (true) {
                Map pageMap = new HashMap();
                pageMap.put("page", p++);
                pageMap.put("pageSize", 1000);
                PageResult soaResult = dataApiSoa(ROOM_CHAT_DATA_LIST, search.getStartTime(), search.getEndTime(), teamIds, chatUuids, null, pageMap, PageResult.class);
                if (CollectionUtils.isEmpty(soaResult.getData())) {
                    break;
                }
                getOrgName(soaResult.getData());
                getTeamName(soaResult.getData());
                getNickNameAndAccountId(soaResult.getData());
                roomChatDataList.setTotal(soaResult.getTotal());
                roomChatDataList.getData().addAll(soaResult.getData());
            }
        }

        map.put("roomChatDataList", roomChatDataList);
        return map;
    }

    public void exportData(ChatRoomSearch search) {
        // 保存参数
        String key = UUID.genUuid();
        String value = JsonUtils.objectToString(search);
        redisUtil.set(key, value, 6L, TimeUnit.HOURS);
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        // 发送到消息队列
        Map<String, Object> messageInfo = new HashMap<>();
        messageInfo.put("url", exportUrl);
        messageInfo.put("service", "manageChatRoom");
        messageInfo.put("method", "getChatRoomExportData");
        messageInfo.put("userName", user.getUserName());
        messageInfo.put("loginName", user.getAccountUuid());
        messageInfo.put("sourceType", "csv");
        messageInfo.put("taskName", ExportDataType.getName(search.getType()));
        messageInfo.put("taskId", key);
        MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push("data_download_resource", messageInfo, null);
    }

    public List<Map<String, Object>> getExportData(Integer page, String taskId) {
        String str = redisUtil.get(taskId);
        if (StringUtils.isEmpty(str)) {
            // 查不到参数就返回空
            return new ArrayList<>();
        }
        // 获取传参
        ChatRoomSearch search = JsonUtils.stringToObject(str, new TypeReference<ChatRoomSearch>() {
        });

        List<Map<String, Object>> resultList = new ArrayList<>();
        // 根据类型获取数据，这里只有两个，就先用if-else，后面多了再处理
        if (search.getType() == null || search.getType() == 1) {
            PageResult pageResult = this.dailyOperationDataLineAndReportForManage(search, page + 1, 100);
            List<Map<String, Object>> data = pageResult.getData();
            if (CollectionUtils.isEmpty(data)) {
                return data;
            }
            // 修改字段名
            for (Map<String, Object> map : data) {
                Map<String, Object> m = new HashMap<>();
                resultList.add(m);
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    m.put(OrganizationDataEnum.getName(entry.getKey()), entry.getValue());
                }
            }
        } else {
            Map<String, Object> roomMap = this.getRoomChatDataForManage(search, page + 1, 100);
            PageResult roomChatDataList = (PageResult) roomMap.get("roomChatDataList");
            if (roomChatDataList == null) {
                return new ArrayList<>();
            }
            List<Map<String, Object>> dataList = roomChatDataList.getData();
            if (CollectionUtils.isEmpty(dataList)) {
                return new ArrayList<>();
            }
            // 修改字段名
            for (Map<String, Object> map : dataList) {
                Map<String, Object> m = new HashMap<>();
                resultList.add(m);
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    m.put(ChatRoomDataEnum.getName(entry.getKey()), entry.getValue());
                }
            }
        }
        return resultList;
    }

    private Map<String, Integer> getManageChatDataCount(List<Map> chatDataCount) {
        Integer receive_amt = 0;
        Integer chat_receive_amt = 0;
        Map<String, Integer> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(chatDataCount)) {
            return chatDataCount.get(0);
        }
        map.put("receive_amt", receive_amt);
        map.put("chat_receive_amt", chat_receive_amt);
        return map;
    }

    /**
     * 用户端-艺人列表
     *
     * @param search
     * @param page
     * @param pageSize
     * @return
     */
    public Map getChatListForUser(ChatRoomSearch search, Integer page, Integer pageSize) {
        if (StringUtils.isNotBlank(search.getLiveNo()) && StringUtils.isNotBlank(search.getNickName())) {
            throw new ServiceException("", "昵称与他趣id查询只能请选择其中一个条件进行查询");
        }
        RoleVo currentRole = sysUserService.getCurrentRole(TeamTypeEnum.TALK_TEAM.getValue());
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        List<Long> teamIds = null;
        List<String> uuids = null;
        String uuid = "";
        if (UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())) {
            teamIds = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue()).stream().map(Team::getTeamId).collect(Collectors.toList());
        }
        if (UserTypeEnum.LEADER.getCode().equals(currentRole.getRoleKey())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            teamIds = new ArrayList<>();
            teamIds.add(teamEmployee.getTeamId());
        }
        if (UserTypeEnum.AGENTER.getCode().equals(currentRole.getRoleKey())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            List<TeamHost> list = teamHostService.getListByEmployeeId(teamEmployee.getEmployeeId(), TeamTypeEnum.TALK_TEAM.getValue());
            uuids = list.stream().map(TeamHost::getHostUuid).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(search.getLiveNo())) {
            uuid = getUuidByNickNameOrCarId(search.getLiveNo());
            if (StringUtils.isBlank(uuid)) {
                Map map = new HashMap();
                map.put("total", 0);
                map.put("data", new ArrayList<>());
                return map;
            }
        }
        if (StringUtils.isNotBlank(search.getNickName())) {
            uuids = infoService.listUuidByAccountName(search.getNickName());
            if (CollectionUtils.isEmpty(uuids)) {
                Map map = new HashMap();
                map.put("total", 0);
                map.put("data", new ArrayList<>());
                return map;
            }
        }

        if (UserTypeEnum.AGENTER.getCode().equals(currentRole.getRoleKey())
                && CollectionUtils.isEmpty(teamIds)
                && CollectionUtils.isEmpty(uuids) && StringUtils.isBlank(uuid)) {
            Map map = new HashMap();
            map.put("total", 0);
            map.put("data", new ArrayList<>());
            return map;
        }
        IPage<TeamHost> byPage = teamHostService.findByPage(user.getOrgId(), teamIds, uuids, uuid, TeamTypeEnum.TALK_TEAM.getValue(), page, pageSize, null);
        Map map = new HashMap();
        map.put("total", byPage.getTotal());
        map.put("data", convertToVo(byPage.getRecords()));
        return map;
    }

    /**
     * 管理端-艺人列表
     *
     * @param search
     */
    public Map getChatListForManage(ChatRoomSearch search, Integer page, Integer pageSize) {
        if (StringUtils.isNotBlank(search.getChatUuid()) && StringUtils.isNotBlank(search.getLiveNo())) {
            throw new ServiceException("", "uuid与他趣id查询只能请选择其中一个条件");
        }
        if (StringUtils.isNotBlank(search.getLiveNo())) {
            String uuid = getUuidByNickNameOrCarId(search.getLiveNo());
            if (StringUtils.isBlank(uuid)) {
                Map map = new HashMap();
                map.put("total", 0);
                map.put("data", null);
                return map;
            }
            search.setChatUuid(uuid);
        }

        IPage<TeamHost> byPage = teamHostService.findByPage(search.getOrgId(), null, null, search.getChatUuid(), TeamTypeEnum.TALK_TEAM.getValue(), page, pageSize, search);
        if (search.getOrgId() == null && StringUtils.isNotBlank(search.getChatUuid()) && CollectionUtils.isEmpty(byPage.getRecords())) {
            TeamHostVo vo = new TeamHostVo();
            vo.setHostUuid(search.getChatUuid());
            boolean flag = getUuidInfo(vo);
            if (flag) {
                Map map = new HashMap();
                map.put("total", 1);
                map.put("data", Arrays.asList(vo));
                return map;
            } else {
                Map map = new HashMap();
                map.put("total", 0);
                map.put("data", null);
                return map;
            }
        }
        Map map = new HashMap();
        map.put("total", byPage.getTotal());
        map.put("data", convertToVo(byPage.getRecords()));
        return map;
    }

    private List<TeamHostVo> convertToVo(List<TeamHost> teamHostList) {
        if (CollectionUtils.isEmpty(teamHostList)) {
            return null;
        }
        List<TeamHostVo> list = new ArrayList<>();
        for (TeamHost teamHost : teamHostList) {
            TeamHostVo vo = new TeamHostVo();
            if (teamHost.getTeamId() != null) {
                Team team = teamService.detail(teamHost.getTeamId());
                Organization organization = organizationMapper.selectByPrimaryKey(teamHost.getOrgId());
                vo.setOrgName(organization.getOrgName());
                vo.setTeamName(team.getTeamName());
            }
            if (teamHost.getEmployeeId() != null && teamHost.getEmployeeId() != 0) {
                TeamEmployee teamEmployee = teamEmployeeService.getOne(teamHost.getEmployeeId());
                vo.setEmployeeName(teamEmployee.getEmployeeName());
            }
            vo.setCreateTime(teamHost.getCreateTime());
            vo.setInviteTime(teamHost.getInviteTime());
            vo.setHostUuid(teamHost.getHostUuid());
            vo.setRealName(teamHost.getRealName());
            vo.setInviteDate(dateFormat.format(teamHost.getInviteTime() * 1000));
            list.add(vo);
        }
        List<String> uuids = teamHostList.stream().map(TeamHost::getHostUuid).collect(Collectors.toList());
        List<Map<String, Object>> maps = adminForumChatService.batchGetChatRoomAuthorInfo(uuids);

        for (TeamHostVo vo : list) {
            Map<String, Object> map = getMapByUuid(maps, vo.getHostUuid());
            vo.setNickName(MapUtils.getString(map, "nickname"));
            vo.setAvatar(MapUtils.getString(map, "avatar"));
            vo.setAccountId(MapUtils.getString(map, "card_id"));
            String status = MapUtils.getString(map, "status");
            if ("delete".equals(status)) {
                vo.setInviteTime(null);
            } else {
                //InviteTime 成为房主时间
                Long createTime = MapUtils.getLong(map, "create_time");
                vo.setInviteTime(createTime);
            }
            vo.setStatus(status);
        }
        regsterTime(list);
        return list;
    }

    private boolean zhimaCertification(String uuid) {
        String field = "zhima_certification";      // 应用级实名认证
        String field2 = "chat_real_certification"; // 聊天室实名认证
        Map<String, Map<String, Object>> infoByUuidMap = infoService.getInfoByUuid(new String[]{uuid}, new String[]{field, field2}, null, true, true);
        if (MapUtils.isEmpty(infoByUuidMap)) {
            return false;
        }
        Map info = MapUtils.getMap(infoByUuidMap, uuid);
        if (MapUtils.isEmpty(info)) {
            return false;
        }
        Integer zhimaCertification = MapUtils.getInteger(info, "zhima_certification");
        Integer chat_real_certification = MapUtils.getInteger(info, "chat_real_certification");
        if (zhimaCertification == 1 || chat_real_certification == 1) {
            return true;
        }
        return false;
    }

    public boolean getUuidInfo(TeamHostVo vo) {
        if (StringUtils.isBlank(vo.getHostUuid())) {
            return false;
        }
        String uuid = vo.getHostUuid();
        String[] fields = {"uuid", "account_name", "account_card_id", "avatar", "create_time", "sex_type"};
        Map<String, Map<String, Object>> infoByUuidMap = infoService.getInfoByUuid(new String[]{uuid}, fields, null, true, true);
        if (MapUtils.isEmpty(infoByUuidMap)) {
            return false;
        }
        Map info = MapUtils.getMap(infoByUuidMap, uuid);
        if (MapUtils.isEmpty(info)) {
            return false;
        }
        Long createTime = MapUtils.getLong(info, "create_time");
        String accountName = MapUtils.getString(info, "account_name");
        String avatar = MapUtils.getString(info, "avatar");
        String accountId = MapUtils.getString(info, "account_card_id");
        vo.setInviteTime(null);
        vo.setCreateTime(null);
        vo.setRegsterTime(createTime);
        vo.setHostUuid(uuid);
        vo.setStatus("delete");
        vo.setNickName(accountName);
        vo.setAccountId(accountId);
        if (StringUtils.startsWith(avatar, "https://avatar01.jiaoliuqu.com")) {
            vo.setAvatar(StringUtils.removeStart(avatar, "https://avatar01.jiaoliuqu.com"));
        } else {
            vo.setAvatar(avatar);
        }
        return true;

    }

    private void regsterTime(List<TeamHostVo> teamHostList) {
        if (CollectionUtils.isEmpty(teamHostList)) {
            return;
        }
        List<String> uuids = teamHostList.stream().map(TeamHostVo::getHostUuid).collect(Collectors.toList());

        String field = "create_time";
        Map<String, Map<String, Object>> infoByUuidMap = infoService.getInfoByUuid(uuids.toArray(new String[uuids.size()]), new String[]{field}, null, true, true);
        if (MapUtils.isEmpty(infoByUuidMap)) {
            return;
        }
        for (TeamHostVo vo : teamHostList) {
            Map uuid = MapUtils.getMap(infoByUuidMap, vo.getHostUuid());
            Long create_time = MapUtils.getLong(uuid, "create_time");
            vo.setRegsterTime(create_time);
        }
    }

    /**
     * 变更聊天室团队
     * PS：该业务操作调用了SOA不可逆，如果外部有事务方法循环调用，需要设置Propagation.REQUIRES_NEW
     *
     * @param chatUuid
     * @param teamId
     */
    @Transactional(rollbackFor = Exception.class, noRollbackFor = ServiceException.class)
    public void changeTeamForChat(String chatUuid, Long teamId, String info, String batchId, HostOperateTypeEnum typeEnum, TeamTypeEnum teamTypeEnum) {
        HostModifyRecord record = new HostModifyRecord();
        record.setHostUuid(chatUuid);
        record.setTeamType(teamTypeEnum == null ? TeamTypeEnum.LIVE_TEAM.getValue() : teamTypeEnum.getValue());
        record.setBatchId(StringUtils.isEmpty(batchId) ? System.currentTimeMillis() + "" : batchId);
        record.setOperateType(typeEnum.ordinal());
        record.setInfo(info == null ? "" : info);
        record.setReason(info == null ? "" : info);
        // 先设置成成功
        record.setStatus(1);
        record.setOperator(SoaBaseParams.fromThread().getToken());
        try {
            long current = System.currentTimeMillis();
            // 自调用加上事务
            SpringUtils.getBean(ChatRoomService.class).changeTeamForChatDetail(chatUuid, teamId, typeEnum);
            long spans = System.currentTimeMillis() - current;
            if (spans > 1000) {
                log.warn("执行changeTeamForChatDetail耗时:{}ms", spans);
            }
        } catch (Exception e) {
            log.error("艺人变更聊天室团队业务异常", e);
            // 设置失败状态和失败理由
            record.setStatus(2);
            record.setFailMsg(e.getMessage());
            throw e;
        } finally {
            record.setCreateTime(System.currentTimeMillis() / 1000);
            record.setUpdateTime(System.currentTimeMillis() / 1000);
            hostModifyRecordMapper.insert(record);

            CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(3000); // 延迟 3 秒
                    toolService.expireChatTeamId(chatUuid);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        }
        // 失效uuid缓存 重新设置
        String key = RedisKeyConstant.UUID_TEAMID_RELATION.setArg(chatUuid);
        masterStringRedisTemplate.delete(key);
    }

    /**
     * 事务放在这一层，尽量调用changeTeamForChat方法，可以写操作日志
     *
     * @param chatUuid
     * @param teamId
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void changeTeamForChatDetail(String chatUuid, Long teamId, HostOperateTypeEnum typeEnum) {
        if (StringUtils.isBlank(chatUuid)) {
            throw new ServiceException("uuid_null", "当前艺人uuid为空");
        }
        TeamOrgInfoVO oldTeamOrgInfoVO = null;
        TeamOrgInfoVO newTeamOrgInfoVO = null;

        TeamHost teamHost = teamHostService.getHostByUuidAndType(chatUuid, TeamTypeEnum.TALK_TEAM.getValue());
        if (teamHost != null && teamHost.getTeamId() == null && teamId == null) {
            throw new ServiceException("team_host_null", "当前艺人已经是无公会，无需重新设定");
        }
        if (teamId != null && teamHost != null && teamHost.getTeamId() != null && teamHost.getTeamId().equals(teamId)) {
            throw new ServiceException("team_host_null", "当前艺人已经加入所选的公会，无需重新设定");
        }
        Team team = null;
        if (teamId != null) {
            team = teamService.detail(teamId);
            if (team == null) {
                throw new ServiceException("team_null", "当前所选团队不存在");
            }
            if (TeamTypeEnum.TALK_TEAM.getValue() != team.getType()) {
                throw new ServiceException("team_error", "当前所选团队不是聊天室团队");
            }
            // 团队迁移不判断艺人是否已经是直播团队
            if (typeEnum != HostOperateTypeEnum.TEAM_MOVE) {
                // 判断用户是否有直播业务，如果有则需要判断是否同一个机构
                checkChatOrgSameWithLive(team.getOrgId(), chatUuid, false);
            }
            // 新的团队信息
            newTeamOrgInfoVO = teamService.getTeamOrgInfoVO(teamId);
        } else {
            // team_id 为空 说明转为无公会
            newTeamOrgInfoVO = new TeamOrgInfoVO();
            newTeamOrgInfoVO.setOrgAndTeamInfo("无公会");
        }

        //检查是否实名认真, 只有设置成有公会才可以，无公会设置的无需实名
        // 满足【应用级实名】或【聊天室实名】其中之一就可以添加或换公会
        if (teamId != null && "true".equals(hostMoveCert) && !zhimaCertification(chatUuid)) {
            throw new ServiceException("zhimaCertification_error", "用户没有通过实名认证，无法完成操作");
        }
        //自然周内不能设置
        if (teamHost != null) {
            if (StringUtils.isNotBlank(whitelist) && whitelist.contains(chatUuid)) {
                log.info("uuid:{},在白名单中，不进行1周限制", chatUuid);
            } else if (typeEnum == HostOperateTypeEnum.TEAM_MOVE || typeEnum == HostOperateTypeEnum.ORG_BUSINESS_CHANGE) {
                // 团队迁移不受限制
            } else {
                this.hostChangeLimit(teamHost);
            }
        }
        // 检查是不是房主，如果是去通知php，换绑公会
        Long changeTeamId = null;
        if (teamId != null) {
            changeTeamId = teamId;
        }
        Long beforeConsortiaId = null;
        if (teamHost != null) {
            beforeConsortiaId = teamHost.getTeamId();
            if (beforeConsortiaId != null) {
                oldTeamOrgInfoVO = teamService.getTeamOrgInfoVO(beforeConsortiaId);
            }
        } else {
            oldTeamOrgInfoVO = new TeamOrgInfoVO();
            oldTeamOrgInfoVO.setOrgAndTeamInfo("无公会");
        }
        log.info("chatUuid:{},changeTeamId:{}", chatUuid, changeTeamId);
        toolService.updateChatTeamId(chatUuid, Optional.ofNullable(changeTeamId).orElse(0L));
//        }
        if (teamHost == null) {
            teamHost = new TeamHost();
            teamHost.setTeamId(teamId);
            teamHost.setTeamType(TeamTypeEnum.TALK_TEAM.getValue());
            teamHost.setHostUuid(chatUuid);
            if (teamId == null) {
                teamHost.setOrgId(null);
            } else {
                teamHost.setOrgId(team.getOrgId());
            }
            teamHost.setCreateTime(DateUtil.currentTimeSeconds());
            teamHost.setUpdateTime(DateUtil.currentTimeSeconds());
            teamHost.setInviteTime(DateUtil.currentTimeSeconds());
            teamHost.setEmployeeId(null);
            teamHost.setStatus(1);
            teamHost.setCurrentSharingProfitRate("");
            teamHost.setNewSharingProfitRate("");
            teamHost.setIsUpdate(0);
            teamHostService.saveTeamHost(teamHost);
            //添加调整记录
            operateLog(chatUuid, 0L, teamId == null ? 0L : teamId);
        } else {
            Long oldTeamId = teamHost.getTeamId();
            Long newTeamId = teamId;
            Long newOrgId = team != null ? team.getOrgId() : null;

            UpdateWrapper<TeamHost> updateWrapper = new UpdateWrapper();
            updateWrapper.set("team_id", newTeamId)
                    .set("org_id", newOrgId)
                    .set("create_time", DateUtil.currentTimeSeconds());
            updateWrapper.eq("id", teamHost.getId());
            teamHostService.update(updateWrapper);
            //添加调整记录
            operateLog(chatUuid, oldTeamId == null ? 0L : oldTeamId, newTeamId == null ? 0L : newTeamId);
        }
        if (changeTeamId != null || beforeConsortiaId != null) {
            log.info("chatUuid:{},changeTeamId:{},beforeConsortiaId:{}", chatUuid, changeTeamId, beforeConsortiaId);
            forumChatRoomService.changeChatRoomConsortia(chatUuid, changeTeamId == null ? 0L : changeTeamId, beforeConsortiaId == null ? 0 : beforeConsortiaId);
            log.info("switchFinanceTime,{},currentTime:{}", switchFinanceTime, System.currentTimeMillis());
            // 必须是聊天室素人转公会的时候0 20240701周一0点 调用业财 用于上线初期过渡 之后就能删除了
            if (Optional.ofNullable(beforeConsortiaId).orElse(0L).equals(0L)
                    && System.currentTimeMillis() >= 1720972800000L) {
                SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
                BusinessRequest businessRequest = new BusinessRequest();
                Integer appCode = Optional.ofNullable(soaBaseParams.getAppcode()).orElse(1);
                Integer cloned = Optional.ofNullable(soaBaseParams.getCloned()).orElse(1);
                businessRequest.setAccount_uuid(chatUuid);
                businessRequest.setAppcode(appCode);
                businessRequest.setCloned(cloned);
                // 调用业财触发 聊天室素人转公会提现 清除素人账户余额
                BigDecimal balance = financeSoaService.getForumAccountChatReward(chatUuid);
                BigDecimal feeRate = SpringContextHolder.getBean(TqTradeCenterService.class).getAccountFee(chatUuid, balance);
                BigDecimal actual = balance.multiply(BigDecimal.ONE.subtract(feeRate)).setScale(2, RoundingMode.DOWN);
                businessRequest.setApply_amount(actual);
                businessRequest.setIncome_amount(balance);
                log.info("{} 素人转工会发起提现 {}", chatUuid, JsonUtils.objectToString(businessRequest));
                financeSoaService.chatUserToGuildWithdrawal(businessRequest);
            }
        }
        try {
            if (teamId != null) {
                // 加入白名单
                certWhiteListService.addCertWhiteListFromServer(chatUuid, 2, "加入聊天室公会", RequestParams.getBaseParam().getToken());
            }
        } catch (Exception e) {
            log.warn("加入认证白名单失败", e);
        }
        long current = System.currentTimeMillis();
        SpringUtils.getBean(ChatRoomService.class).refreshChatCache();
        long spans = System.currentTimeMillis() - current;
        if (current > 1000) {
            log.warn("执行刷新缓存数据艺人uuid与公会的对应关系，花费时间:{}ms", spans);
        }
        // 推送钉钉通知
        pushService.pushTeamHostChangeTeamToDingRobot(chatUuid, SoaBaseParams.fromThread().getToken(), TeamTypeEnum.TALK_TEAM.getValue(), oldTeamOrgInfoVO, newTeamOrgInfoVO);
    }

    /**
     * 艺人转工会限制，如果是迁移团队，则不占用一周内只能迁移一次的次数
     *
     * @param host
     */
    private void hostChangeLimit(TeamHost host) {
        // 获取这周一的时间戳
        long mondaySecond = LocalDate.now()
                .with(ChronoField.DAY_OF_WEEK, 1)
                .atStartOfDay()
                .toEpochSecond(ZoneOffset.of("+8"));
        List<HostModifyRecord> recordList = hostModifyRecordMapper.findAllByHostAndCreateTime(host.getHostUuid(), mondaySecond, 1, null);
        // 没有艺人操作记录就不限制了
        if (CollectionUtils.isEmpty(recordList)) {
            return;
        }
        long moveCount = recordList.stream()
                // 去掉团队迁移的
                .filter(r -> r.getOperateType() != HostOperateTypeEnum.TEAM_MOVE.ordinal())
                .count();
        if (moveCount > 0) {
            throw new ServiceException("in_set_time", "一周只能转一次公会");
        }
    }

    private void operateLog(String uuid, Long oldValue, Long newValue) {
        Map<String, Long> agentMap = new HashMap<>();
        agentMap.put("oldValue", oldValue);
        agentMap.put("newValue", newValue);
        // 记录日志
        TeamHostOperateLog log = new TeamHostOperateLog();
        log.setHostUuid(uuid);
        log.setType(TeamHostOperateLogEnum.CHAT_ROOM_CHANGE_TEAM.getValue());
        log.setCreateTime(DateUtil.currentTimeSeconds());
        log.setContent(JsonUtils.objectToString(agentMap));
        log.setOperator("");
        teamHostOperateLogMapper.insert(log);
    }

    public Map<String, Object> getMapByUuid(List<Map<String, Object>> maps, String uuid) {
        if (CollectionUtils.isEmpty(maps) || StringUtils.isBlank(uuid)) {
            Map map = new HashMap();
            map.put("chat_uuid", "");
            map.put("nickname", "");
            map.put("avatar", "");
            map.put("card_id", "");
            map.put("status", "");
            return map;
        }
        for (Map<String, Object> map : maps) {
            String chatUuid = MapUtils.getString(map, "chat_uuid");
            if (uuid.equals(chatUuid)) {
                return map;
            }
        }
        Map map = new HashMap();
        map.put("chat_uuid", "");
        map.put("nickname", "");
        map.put("avatar", "");
        map.put("card_id", "");
        map.put("status", "");
        return map;
    }

    public String getUuidByNickNameOrCarId(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }
        Map<String, String> infoMap = infoService.getInfoByUuidOrNicknameOrCardId(name, new String[]{"uuid"});
        if (MapUtils.isEmpty(infoMap)) {
            return "";
        }
        Map accountInfo = MapUtils.getMap(infoMap, "account_info");
        if (MapUtils.isEmpty(accountInfo)) {
            return "";
        }
        String uuid = MapUtils.getString(accountInfo, "uuid");
        if (StringUtils.isBlank(uuid)) {
            return "";
        }
        return uuid;
    }

    private void validateTime(ChatRoomSearch search) {
        log.info("查询参数chatRoomSearch:{}", JsonUtils.objectToString(search));
        if (StringUtils.isBlank(search.getStartTime())) {
            throw new ServiceException("start_time_null", "请选择开始时间");
        }
        if (StringUtils.isBlank(search.getEndTime())) {
            throw new ServiceException("end_time_null", "请选择结束时间");
        }
    }


    /**
     * 获取机构名称
     *
     * @param list
     */
    private void getOrgName(List<Map> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> teamIdList = new ArrayList<>();
        for (Map map : list) {
            Long teamId = MapUtils.getLong(map, "consortia_id");
            teamIdList.add(teamId);
//            Team team = teamService.detail(teamId);
//            if (team != null) {
//                Organization organization = organizationMapper.selectByPrimaryKey(team.getOrgId());
//                if (organization != null) {
//                    map.put("orgName", organization.getOrgName());
//                } else {
//                    map.put("orgName", "");
//                }
//            } else {
//                map.put("orgName", "");
//            }
        }
        if (CollectionUtils.isEmpty(teamIdList)) {
            return;
        }
        Map<Long, Long> team2OrgIdMap = teamService.MapOrgId(teamIdList);
        List<Long> orgIdList = team2OrgIdMap.values().stream().filter(l -> l > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgIdList)) {
            return;
        }
        Map<Long, String> orgNameMap = iOrgnizatonService.MapOrgName(orgIdList);
        if (CollectionUtil.isEmpty(orgNameMap)) {
            return;
        }
        for (Map map : list) {
            Long teamId = MapUtils.getLong(map, "consortia_id");
            Long orgId = team2OrgIdMap.getOrDefault(teamId, 0L);
            String orgName = orgNameMap.getOrDefault(orgId, "");
            map.put("orgName", orgName);
        }
    }

    /**
     * 获取团队名称
     *
     * @param list
     */
    private void getTeamName(List<Map> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Long> teamIdList = new ArrayList<>();

        for (Map map : list) {
            Long teamId = MapUtils.getLong(map, "consortia_id");
            teamIdList.add(teamId);
//            Team team = teamService.detail(teamId);
//            if (team != null) {
//                map.put("teamName", team.getTeamName());
//            } else {
//                map.put("teamName", "");
//            }
        }
        Map<Long, String> teamNameMap = teamService.MapName(teamIdList);
        for (Map map : list) {
            Long teamId = MapUtils.getLong(map, "consortia_id");
            String teamName = teamNameMap.getOrDefault(teamId, "");
            map.put("teamName", teamName);
        }
    }

    /**
     * 获取经纪人名称
     *
     * @param list
     */
    private void getAgenterName(List<Map> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> uuidList = new ArrayList<>();
        for (Map map : list) {
            String uuid = MapUtils.getString(map, "account_uuid");
            uuidList.add(uuid);
//            TeamHost host = teamHostService.getHostByUuidAndType(uuid, TeamTypeEnum.TALK_TEAM.getValue());
//            if (host != null) {
//                if (host.getEmployeeId() != null && host.getEmployeeId() != 0) {
//                    TeamEmployee teamEmployee = teamEmployeeService.getOne(host.getEmployeeId());
//                    if (teamEmployee != null) {
//                        map.put("agenterName", teamEmployee.getEmployeeName());
//                    } else {
//                        map.put("agenterName", "");
//                    }
//                } else {
//                    map.put("agenterName", "");
//                }
//            } else {
//                map.put("agenterName", "");
//            }
        }
        Set<String> uuidSet = uuidList.stream().collect(Collectors.toSet());
        Long curTime1 = System.currentTimeMillis();
        Map<String, Long> hostUuid2EmployeeIdMap = mapEmployeeIdWithPage(new ArrayList<>(uuidSet), TeamTypeEnum.TALK_TEAM.getValue(), 1000);
        log.info("根据主播uuid批量查询团队成员id映射集合，hostUuid个数为:{}，耗时:{}ms", uuidSet.size() , System.currentTimeMillis()-curTime1);
        //                teamHostService.mapEmployeeId(new ArrayList<>(uuidSet), TeamTypeEnum.TALK_TEAM.getValue());
        // 通过employeeId列表 批量获取 成员id和成员名称之间的关系
        if (CollectionUtil.isEmpty(hostUuid2EmployeeIdMap)) {
            return;
        }
        Set<Long> employeeIdList = hostUuid2EmployeeIdMap.values().stream().filter(l -> l > 0).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(employeeIdList)) {
            return;
        }
        Long curTime = System.currentTimeMillis();
        Map<Long, String> employeeNameIdNameMap = mapEmployeeNameWithPage(new ArrayList<>(employeeIdList), 200);
        log.info("根据成员id批量查询团队成员名称映射集合，employeeId个数为:{}，耗时:{}ms", employeeIdList.size() , System.currentTimeMillis()-curTime);
                // teamEmployeeService.mapEmployeeName(new ArrayList<>(employeeIdList));
        if (CollectionUtil.isEmpty(employeeNameIdNameMap)) {
            return;
        }

        for (Map map : list) {
            String uuid = MapUtils.getString(map, "account_uuid");
            Long employeeId = hostUuid2EmployeeIdMap.getOrDefault(uuid, 0L);
            String employeeName = employeeNameIdNameMap.getOrDefault(employeeId, "");
            map.put("agenterName", employeeName);

        }
    }

    private Map<String, Long> mapEmployeeIdWithPage(List<String> uuidList, int teamType, int pageSize) {
        if(CollectionUtil.isEmpty(uuidList)){
            return new HashMap<>();
        }
        Map<String, Long> allMap = new HashMap<>();
        int size = uuidList.size();
        int pageCount = size % pageSize == 0 ? size / pageSize : (size / pageSize) + 1;
        for (int page = 1; page <= pageCount; page++) {
            int pageStart = (page - 1) * pageSize;
            int pageEnd = page * pageSize;
            if(page == pageCount){
                pageEnd = size;
            }
            List<String> pageUuids = uuidList.subList(pageStart, pageEnd);
            Map<String, Long> hostUuid2EmployeeIdMap = teamHostService.mapEmployeeId(pageUuids, teamType);
            allMap.putAll(hostUuid2EmployeeIdMap);
        }
        return allMap;
    }

    private Map<Long, String> mapEmployeeNameWithPage(List<Long> employeeIdList, int pageSize) {
        if(CollectionUtil.isEmpty(employeeIdList)){
            return new HashMap<>();
        }
        Map<Long, String> allMap = new HashMap<>();
        int size = employeeIdList.size();
        int pageCount = size % pageSize == 0 ? size / pageSize : (size / pageSize) + 1;
        for (int page = 1; page <= pageCount; page++) {
            int pageStart = (page - 1) * pageSize;
            int pageEnd = page * pageSize;
            if(page == pageCount){
                pageEnd = size;
            }
            List<Long> pageEmployeeIds = employeeIdList.subList(pageStart, pageEnd);
            Map<Long, String> employeeIdNameMap = teamEmployeeService.mapEmployeeName(pageEmployeeIds);
            allMap.putAll(employeeIdNameMap);
        }
        return allMap;
    }


//    private static void TestMapEmployeeIdWithPage(List<String> uuidList, int pageSize) {
//        if(CollectionUtil.isEmpty(uuidList)){
//            return ;
//        }
//        Map<String, Long> allMap = new HashMap<>();
//        int size = uuidList.size();
//        int pageCount = size % pageSize == 0 ? size / pageSize : (size / pageSize) + 1;
//        for (int page = 1; page <= pageCount; page++) {
//            int pageStart = (page - 1) * pageSize;
//            int pageEnd = page * pageSize;
//            if(page == pageCount){
//                pageEnd = size;
//            }
//            List<String> pageUuids = uuidList.subList(pageStart, pageEnd);
//            log.info("第「{}」页数据为：{}", page, JsonUtils.objectToString(pageUuids));
//        }
//    }
//
//    public static void main(String[] args) {
//        List<String> uuidList = new ArrayList<>();
//        uuidList.add("1");
//        uuidList.add("2");
//        uuidList.add("3");
//        uuidList.add("4");
//        uuidList.add("5");
//        uuidList.add("6");
//        uuidList.add("7");
//        uuidList.add("8");
//        uuidList.add("9");
//        uuidList.add("10");
//        uuidList.add("11");
//        uuidList.add("12");
//        uuidList.add("13");
//        uuidList.add("14");
//        TestMapEmployeeIdWithPage(uuidList, 5);
//    }

    /**
     * 通用api接口
     *
     * @param startTime
     * @param endTime
     * @param teamIds
     * @param page
     */
    public <T> T dataApiSoa(String api, String startTime, String endTime, List<Long> teamIds, List<String> chatUuids, Integer chatStatus, Map page, Class<T> clazz) {
        return dataManager.dataApiSoa(api, startTime, endTime, teamIds, chatUuids, chatStatus, page, clazz);
    }

    public <T> T dataApiSoa(String api, String startTime, String endTime, List<Long> teamIds, List<String> chatUuids, Integer chatStatus, Map page, TypeReference<T> clazz) {
        return dataManager.dataApiSoa(api, startTime, endTime, teamIds, chatUuids, chatStatus, page, clazz);
    }

    public Long getTeamIdByUuid(String chatUuid) {
        if (StringUtils.isBlank(chatUuid)) {
            log.warn("chatUuid异常,chatUuid={}", chatUuid);
            return 0L;
        }
        return getTeamIdByChatuuidInCache(chatUuid);
    }

    public Map getBatchTeamIdByUuid(String[] chatUuids) {
        if (chatUuids == null) {
            return null;
        }
        List list = Arrays.asList(chatUuids);
        return getTeamIdsByChatuuidsInCache(list);
    }

    public Map getBatchTeamNameByIds(Long[] teamIds) {
        if (teamIds == null) {
            return null;
        }
        List list = Arrays.asList(teamIds);
        List<Team> teams = teamService.getTeamListByTeamIds(list);
        if (CollectionUtils.isEmpty(teams)) {
            return null;
        }
        Map<String, String> map = new HashMap<>();
        for (Team team : teams) {
            if (StringUtils.isNotBlank(team.getTeamName())) {
                map.put(team.getTeamId().toString(), team.getTeamName());
            }
        }
        return map;
    }

    //缓存数据艺人uuid与公会的对应关系
    @Async
    public void refreshChatCache() {
        log.info("刷新 refreshChatCache");
        List<TeamHost> teamHostList = teamHostService.getBatchHostType(TeamTypeEnum.TALK_TEAM.getValue());
        if (CollectionUtils.isEmpty(teamHostList)) {
            masterStringRedisTemplate.opsForValue().set(CHATROOM_TEAM_MAP, "");
            return;
        }
        Map<String, Long> map = new HashMap<>();
        for (TeamHost teamHost : teamHostList) {
            //UUID_TEAMID_RELATION 用来替换 CHATROOM_TEAM_MAP
            String key = RedisKeyConstant.UUID_TEAMID_RELATION.setArg(teamHost.getHostUuid());

            if (teamHost.getTeamId() != null) {
                masterStringRedisTemplate.opsForValue().set(key, String.valueOf(teamHost.getTeamId()));
                map.put(teamHost.getHostUuid(), teamHost.getTeamId());
            } else {
                //历史情况需要放在 0
                masterStringRedisTemplate.opsForValue().set(key, String.valueOf(0));
                map.put(teamHost.getHostUuid(), 0L);
            }
        }
        // TODO 优化key结构
        masterStringRedisTemplate.opsForValue().set(CHATROOM_TEAM_MAP, JsonUtils.objectToString(map));
        log.info("刷新 refreshChatCache 完成");
    }

    /**
     * 获取团队id
     *
     * @param chatUuid
     * @return
     * @remark @CachePenetrationProtect 只有一个线程去加载，其它线程等待结果 @CacheRefresh 7200s刷新
     */
    @CachePenetrationProtect
    @CacheRefresh(refresh = 3600 * 2, stopRefreshAfterLastAccess = 3600 * 6)
    @Cached(name = "getTeamIdByUuid::", key = "#chatUuid", expire = 3600 * 24, cacheType = CacheType.REMOTE)
    public Long getTeamId(String chatUuid) {
        TeamHost teamHost = teamHostService.getHostByUuidAndType(chatUuid, TeamTypeEnum.TALK_TEAM.getValue());
        return (teamHost != null) ? Optional.ofNullable(teamHost.getTeamId()).orElse(0L) : 0L;
    }

    public Map<String, Long> mapByUuidsAndType(List<String> uuids) {
        if (CollectionUtils.isEmpty(uuids)) {
            return new HashMap<>();
        }
        // 使用uuid，初始化一个map回去
        Map<String, Long> map = initMapByUuids(uuids);
        List<TeamHost> teamHostList = teamHostService.getBatchHostByUuidsAndType(uuids, TeamTypeEnum.TALK_TEAM.getValue());
        if (CollectionUtils.isEmpty(teamHostList)) {
            return map;
        }
        Map<String, TeamHost> teamHostMap = teamHostList.stream().collect(Collectors.toMap(TeamHost::getHostUuid, l -> l, (key1, key2) -> key1));
        for (Map.Entry<String, TeamHost> entry : teamHostMap.entrySet()) {
            map.put(entry.getKey(), Optional.ofNullable(entry.getValue().getTeamId()).orElse(0L));
        }
        return map;
    }

    private Map<String, Long> initMapByUuids(List<String> uuids) {
        if (CollectionUtils.isEmpty(uuids)) {
            return new HashMap<>();
        }
        Map<String, Long> map = new HashMap<>();
        Set<String> uuidSet = uuids.stream().collect(Collectors.toSet());
        for (String uuid : uuidSet) {
            if (map.containsKey(uuid)) {
                continue;
            }
            map.put(uuid, 0L);
        }
        return map;
    }

    public Long getTeamIdByChatuuidInCache(String chatUuid) {

        String value = masterStringRedisTemplate.opsForValue().get(RedisKeyConstant.UUID_TEAMID_RELATION.setArg(chatUuid));
        log.debug("chatUuid获取缓存的数据,value={}", value);
        if (StringUtils.isNotBlank(value)) {
            return Long.valueOf(value);
//            Map<String, Long> map = JsonUtils.stringToObject(value, new TypeReference<Map<String, Long>>() {
//            });
//            if (map.containsKey(chatUuid)) {
//                return map.get(chatUuid);
//            } else {
//                return 0L;
//            }
        } else {
            TeamHost teamHost = teamHostService.getHostByUuidAndType(chatUuid, TeamTypeEnum.TALK_TEAM.getValue());
            log.debug("teamHost数据库数据,teamHost={}", JSON.toJSONString(teamHost));
            if (teamHost == null) {
                return 0L;
            }
            log.warn("getTeamIdByChatuuidInCache刷新redis,chatUuid={}", chatUuid);
            SpringUtils.getBean(ChatRoomService.class).refreshChatCache();
            toolService.expireChatTeamId(chatUuid);
            return teamHost.getTeamId();
        }
    }

    public Map getTeamIdsByChatuuidsInCache(List<String> chatUUids) {
        Map chatMap = new HashMap<>();

        for (String str : chatUUids) {
            Long teamId = this.getTeamIdByChatuuidInCache(str);

            chatMap.put(str, teamId);
        }
        return chatMap;

//        String value = masterStringRedisTemplate.opsForValue().get(CHATROOM_TEAM_MAP);
////        Map chatMap = new HashMap<>();
//        if (StringUtils.isNotBlank(value)) {
//            Map<String, Long> map = JsonUtils.stringToObject(value, new TypeReference<Map<String, Long>>() {
//            });
//            for (String str : chatUUids) {
//                if (map.containsKey(str)) {
//                    chatMap.put(str, map.get(str));
//                } else {
//                    chatMap.put(str, 0L);
//                }
//            }
//        } else {
//            refreshChatCache();
//            List<TeamHost> teamHostList = teamHostService.getBatchHostByUuidsAndType(chatUUids, TeamTypeEnum.TALK_TEAM.getValue());
//            if (CollectionUtils.isEmpty(teamHostList)) {
//                return chatMap;
//            }
//            for (TeamHost teamHost : teamHostList) {
//                if (teamHost.getTeamId() != null) {
//                    chatMap.put(teamHost.getHostUuid(), teamHost.getTeamId());
//                } else {
//                    chatMap.put(teamHost.getHostUuid(), 0L);
//                }
//            }
//
//        }
//        return chatMap;
    }

    public List<ChatVo> getChatList(Integer pageNo, Integer pageSize) {
        return teamHostService.findChatListByPage(pageNo, pageSize);
    }

    /**
     * 校验聊天室机构是否与直播机构同一个（忽略直播机构和聊天室机构）
     *
     * @param chatOrgId        聊天室机构id
     * @param hostUuid         艺人uuid
     * @param throwClientError 是否客户端错误
     */
    private void checkChatOrgSameWithLive(Long chatOrgId, String hostUuid, Boolean throwClientError) {
        // 判断用户是否有直播业务，如果有则需要判断是否同一个机构
        TeamHost liveHost = teamHostService.getHostByUuidAndType(hostUuid, TeamTypeEnum.LIVE_TEAM.getValue());
        if (liveHost != null) {
            Long liveTeamId = liveHost.getTeamId();
            Boolean inTaquOrPersonalTeam = SpringUtils.getBean(HostService.class).isInTaquOrPersonalTeam(liveTeamId);
            // 不属于中转公会和素人公会
            if (!inTaquOrPersonalTeam) {
                Long liveOrgId = liveHost.getOrgId();
                if (!Objects.equals(liveOrgId, chatOrgId)) {
                    Team liveTeam = teamService.detail(liveTeamId);
                    String str = liveTeam != null ? "「" + liveTeam.getTeamName() + "」中" : "直播业务中";
                    if (throwClientError) {
                        throw new CustomException("无法加入该公会，因为用户已经存在 " + str, 0);
                    } else {
                        throw new ServiceException("live", "无法加入该公会，因为用户已经存在 " + str);
                    }
                }
            }
        }
    }

    /**
     * 根据团队类型查询机构列表
     *
     * @param type
     * @return
     */
    public List<IdNameVO> getOrgListByType(Integer type) {
        Organization condition = new Organization();
        condition.setApplyStatus(2);
        condition.setOrgStatus(1);
        if (type.equals(TeamTypeEnum.TALK_TEAM.getValue())) {
            condition.setChatRoomPermissions(1);
        }
        //    where apply_status != 5 and org_status != 2
        //      <if test="accountUuid!=null">
        //       and account_uuid = #{accountUuid}
        //      </if>
        //      <if test="orgUuid!=null">
        //        and org_uuid = #{orgUuid}
        //      </if>
        //      <if test="orgName != null">
        //        and org_name = #{orgName}
        //      </if>
        //    <if test="remitModifyStatus != null">
        //      and remit_modify_status = #{remitModifyStatus}
        //    </if>
        LambdaQueryWrapper<Organization> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Organization::getOrgId, Organization::getOrgName);
        queryWrapper.ne(Organization::getApplyStatus, 5)
                .ne(Organization::getOrgStatus, 2);
        List<Organization> orgList = organizationMapper.selectList(queryWrapper);
//        List<Organization> orgList = organizationMapper.getByList(condition);
        List<IdNameVO> idNameVOList = new ArrayList<>();
        for (Organization org : orgList) {
            IdNameVO vo = new IdNameVO();
            vo.setId(org.getOrgId());
            vo.setName(org.getOrgName());
            idNameVOList.add(vo);
        }
        return idNameVOList;
    }
}
