package cn.taqu.gonghui.chatroom.service.chatroom;

import cn.taqu.gonghui.chatroom.search.TalentRankSearch;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.common.vo.res.TalentRankDataResp;

/**
 * 聊天室才艺榜
 *
 * <AUTHOR>
 * @date 2024/9/26 1:40 下午
 */
public interface TalentRankDataService {

    /**
     * 才艺榜数据
     *
     * @param search
     * @return
     */
    PageDataResult<TalentRankDataResp> talentRankingData(TalentRankSearch search);

    /**
     * 才艺榜数据
     *
     * @param search
     * @return
     */
    PageDataResult<TalentRankDataResp> clientTalentRankingData(TalentRankSearch search);

    /**
     * 获取下载路径
     *
     * @param search
     * @return
     */
    String getDownloadUrl(TalentRankSearch search);

    /**
     * 获取下载路径
     *
     * @param search
     * @return
     */
    String clientGetDownloadUrl(TalentRankSearch search);

}
