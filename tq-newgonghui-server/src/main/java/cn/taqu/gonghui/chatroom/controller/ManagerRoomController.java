package cn.taqu.gonghui.chatroom.controller;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.chatroom.entity.Room;
import cn.taqu.gonghui.chatroom.service.RoomBizService;
import cn.taqu.gonghui.chatroom.service.RoomDataBizService;
import cn.taqu.gonghui.chatroom.vo.RoomRemoveReq;
import cn.taqu.gonghui.chatroom.vo.RoomSubmitReq;
import cn.taqu.gonghui.chatroom.vo.req.BasePageReq;
import cn.taqu.gonghui.chatroom.vo.req.DataRoomRequest;
import cn.taqu.gonghui.chatroom.vo.req.RoomList4ManagerReq;
import cn.taqu.gonghui.common.domain.DataSortRequest;
import cn.taqu.gonghui.common.utils.PageWrapper;
import cn.taqu.gonghui.config.annotation.SoaForm;
import cn.taqu.gonghui.cron.manage.ExportDataTask;
import cn.taqu.gonghui.util.RequestParamsUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api", params = "service=managerRoom")
@Slf4j
@RequiredArgsConstructor
public class ManagerRoomController {

    private final RoomBizService roomBizService;
    private final RoomDataBizService roomDataBizService;
    private final ExportDataTask exportDataTask;

    @RequestMapping(params = "method=page")
    public JsonResult page(@SoaForm RoomList4ManagerReq req){
        return JsonResult.success(new PageWrapper<>(
                roomBizService.page4Manager(req,
                        req.getPage(),
                        req.getPageSize()))
        );
    }

    @RequestMapping(params = "method=submit")
    public JsonResult submit(@SoaForm RoomSubmitReq req){
        roomBizService.submit(req);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=remove")
    public JsonResult remove(@SoaForm RoomRemoveReq req){
        roomBizService.remove(req.getRoomNo());
        return JsonResult.success();
    }

    @RequestMapping(params = "method=edit")
    public JsonResult edit(@SoaForm RoomSubmitReq req){
        roomBizService.edit(req);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=adminPaiPaiRoom")
    public JsonResult adminPaiPaiRoom(@SoaForm DataRoomRequest req ){
        req.getSortingList().add(new DataSortRequest("chat_room_uuid", "asc"));
        return JsonResult.success(roomDataBizService.page4Manager("adminPaiPaiRoom",req));
    }

    @RequestMapping(params = "method=adminReportingRoom")
    public JsonResult adminReportingRoom(@SoaForm DataRoomRequest req){
        req.getSortingList().add(new DataSortRequest("chat_room_uuid", "asc"));
        return JsonResult.success(roomDataBizService.filterRoomRemoved(roomDataBizService.page4Manager("adminReportingRoom",req)));
    }

    @RequestMapping(params = "method=adminPaiPaiRoomReceiveDetail")
    public JsonResult adminPaiPaiRoomReceiveDetail(@SoaForm DataRoomRequest req){
        return JsonResult.success(roomDataBizService.page4Manager("adminPaiPaiRoomReceiveDetail",req));
    }

    /**
     * 艺人数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=historyWeekRecord")
    public JsonResult historyWeekRecord(RequestParams params){
        BasePageReq req = new BasePageReq();
        req.setPageNum(params.getFormIntegerDefault(1, 1));
        req.setPageSize(params.getFormIntegerDefault(2, 10));
        return JsonResult.success(roomDataBizService.getHistoryWeekRecord(req));
    }

    /**
     * 导出数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=exportCsv")
    public JsonResult exportCsv(RequestParams params){
        Integer exportId = params.getFormInteger(0);
        String mondayDate = roomBizService.getMondayDateById(exportId);
        return JsonResult.success(roomDataBizService.mondayExportHostData(mondayDate));
        //return JsonResult.success(roomDataBizService.getDownUrl());
    }

}
