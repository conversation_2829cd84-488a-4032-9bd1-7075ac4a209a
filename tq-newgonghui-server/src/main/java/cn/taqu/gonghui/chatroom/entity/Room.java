package cn.taqu.gonghui.chatroom.entity;

import cn.taqu.gonghui.common.entity.BaseEntity;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;

/**
 * 报备房
 */
@Getter
@Setter
@TableName(value = "room", autoResultMap = true)
public class Room extends BaseEntity{

    /**
     * 房间标识
     */
    private String roomNo;
    /**
     * 审核单号
     */
    private String auditOrderNo;
    /**
     * 机构
     */
    private String orgUuid;
    /**
     * 团队
     */
    private Long teamId;

    /**
     * 房主 他趣 uuid
     */
    private String accountUuid;


    /**
     * 手机号
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String mobile;

    /**
     * 手机号-密文
     */
    @TableField(value = "mobile_cipher", jdbcType = JdbcType.VARCHAR ,typeHandler = EncryptTypeHandler.class)
    private String mobileCipher;


    /**
     * 房主名字
     */
    @TableField(typeHandler = SetNullTypeHandler.class)
    private String ownerName;

    /**
     * 房主名字
     */
    @TableField(value = "owner_name_cipher",jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String ownerNameCipher;

    /**
     * 报备 时间范围
     */
    //TODO time_scope_start -> time_scope
    @TableField("time_scope_start")
    private String timeScope;
    /**
     * 房间类型
     */
    private Integer type;

    private Integer status;

    /**
     * 添加途径
     * @see cn.taqu.gonghui.chatroom.constant.RoomSourceEnum
     */
    private Integer source;

    public String getMobile() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.mobileCipher;
        }
        if(StringUtils.isBlank(this.mobile) && StringUtils.isNotBlank(this.mobileCipher)){
            return this.mobileCipher;
        }
        return this.mobile;
    }

    public String getOwnerName() {
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.ownerNameCipher;
        }
        if(StringUtils.isBlank(this.ownerName) && StringUtils.isNotBlank(this.ownerNameCipher)){
            return this.ownerNameCipher;
        }
        return this.ownerName;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
        this.mobileCipher = mobile;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
        this.ownerNameCipher = ownerName;
    }
}
