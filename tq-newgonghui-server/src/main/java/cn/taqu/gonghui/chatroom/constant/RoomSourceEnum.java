package cn.taqu.gonghui.chatroom.constant;

import cn.taqu.gonghui.constant.BaseEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
@Getter
@RequiredArgsConstructor
public enum RoomSourceEnum implements BaseEnum {
    USER(0, "用户端"),
    MANAGER(1, "管理端");

    private final int code;
    private final String name;


    public static RoomSourceEnum fromCode(Integer code){
        if(code == null){
            return null;
        }
        for (RoomSourceEnum value : values()) {
            if(value.code == code){
                return value;
            }
        }
        return null;
    }



}
