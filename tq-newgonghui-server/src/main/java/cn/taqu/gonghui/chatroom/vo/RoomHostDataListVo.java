package cn.taqu.gonghui.chatroom.vo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RoomHostDataListVo {


    // ID
    private Long id;
    /**
     * 所属公会
     */
    private String teamName;
    /**
     *所属公会
     */
    private Long teamId;
    /**
     * 房间类型
     */
    private Integer type;
    /**
     * 开房时间段
     */
    private String timeScope;

    //数据
    /**
     * 开房时长（秒）： 支持升降序
     */
    private String effectDuration;
    /**
     * 开房有效天数： 支持升降序
     */
    private String effectDays;
    /**
     * 房间收礼流水： 支持升降序
     */
    private String giftFlow;
    /**
     * 房间游戏礼物价值： 支持升降序
     */
    private String giftValueInGame;
    /**
     * 房间非游戏礼物价值： 支持升降序
     */
    private String giftValueNotInGame;
    /**
     * 游戏礼物占比： 支持升降序
     */
    private String giftInGameRatio;
    /**
     * 付费人数： 支持升降序
     */
    private String purchaseCount;
    /**
     * 付费率： 支持升降序
     */
    private String purchaseRatio;
    /**
     * 复购率： 支持升降序
     */
    private String repurchaseRatio;
    /**
     * 留存率： 支持升降序
     */
    private String retentionRatio;

}
