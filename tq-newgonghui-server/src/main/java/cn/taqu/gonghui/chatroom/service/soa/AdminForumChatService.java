package cn.taqu.gonghui.chatroom.service.soa;


import cn.taqu.core.soa.server.annotation.SoaProvider;

import java.util.List;
import java.util.Map;

@SoaProvider("AdminForumChat")
public interface AdminForumChatService {

    /**
     *   "nickname": "xiao30",  昵称
     *   "avatar": "/taqu_android_avatar_101_1637202962024_1_0_72433.JPEG",--头像
     *   "account_id": "*********", --他趣Id
     *   "status": "resting"  --状态：opening-开播中 resting-休息中 baning-禁播中 delete-非聊天室房主
     * @param chatUuid
     * @return
     */
    Map<String,Object> getChatRoomAuthorInfo(String chatUuid);

    /**
     * 批量获取上面接口的数据
     * @param chatUuids
     * @return
     */
    List<Map<String,Object>> batchGetChatRoomAuthorInfo(List<String> chatUuids);
}
