package cn.taqu.gonghui.chatroom.service.chatroom.impl;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.chatroom.search.ShellSearch;
import cn.taqu.gonghui.chatroom.service.chatroom.ShellDataService;
import cn.taqu.gonghui.chatroom.vo.excel.ShellDataExcel;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.domain.DataPageRequest;
import cn.taqu.gonghui.common.domain.DataPageResult;
import cn.taqu.gonghui.common.domain.DataSortRequest;
import cn.taqu.gonghui.common.service.DataManager;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.OssHandler;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.dto.ShellDataDTO;
import cn.taqu.gonghui.common.vo.res.ShellDataResp;
import cn.taqu.gonghui.constant.MetaContentTypeEnum;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.vo.RoleVo;
import com.alibaba.excel.EasyExcel;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/26 1:40 下午
 */
@Slf4j
@Service
public class ShellDataServiceImpl implements ShellDataService {

    @Resource
    private OssHandler ossHandler;

    @Resource
    private DataManager dataManager;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private TokenService tokenService;

    @Resource
    private TeamService teamService;

    @Resource
    private TeamEmployeeService teamEmployeeService;

    /**
     * 生成excel文件
     *
     * @param list
     * @param fileName
     * @param sheetName
     * @return
     */
    public static File buildExcelFile(List<ShellDataExcel> list, String fileName, String sheetName) {
        fileName = StringUtil.isBlank(fileName) ? UUID.randomUUID().toString() : fileName;
        sheetName = StringUtil.isBlank(sheetName) ? "sheet1" : sheetName;

        File xlsxFile;
        try {
            xlsxFile = File.createTempFile(fileName, ".xlsx");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        EasyExcel.write(xlsxFile, ShellDataExcel.class).sheet(sheetName).doWrite(list);
        return xlsxFile;
    }

    @Override
    public PageDataResult<ShellDataResp> shellData(ShellSearch search) {
        ImmutableMap.Builder<String, Object> paramMap = ImmutableMap.builder();
        paramMap.put("dt", new String[]{search.getStartTimeFormat(), search.getEndTimeFormat()});
        if (StringUtils.isNotBlank(search.getHostUuid())) {
            paramMap.put("host_uuid", search.getHostUuid());
        }
        if (search.getConsortiaId() != null) {
            paramMap.put("consortia_id", search.getConsortiaId());
        }
        if (CollectionUtils.isNotEmpty(search.getTeamId())) {
            paramMap.put("team_id", search.getTeamId());
        }

        List<DataSortRequest> sortList = Lists.newArrayList();
        sortList.add(new DataSortRequest("shell_amt", "desc"));
        sortList.add(new DataSortRequest("dt", "desc"));
        sortList.add(new DataSortRequest("host_uuid", "desc"));
        DataPageResult<Map<String, Object>> pageMap = dataManager.dateApiSoaV2("shellData",
                paramMap.build(),
                new DataPageRequest(search.getPage(), search.getPageSize()),
                sortList
        );

        return transferPageResp(pageMap);
    }

    @Override
    public PageDataResult<ShellDataResp> clientShellData(ShellSearch search) {
        List<Long> teamId = listTeamId();
        search.setTeamId(teamId);
        return shellData(search);
    }

    /**
     * 转化dto
     *
     * @param pageMap
     * @return
     */
    private PageDataResult<ShellDataResp> transferPageResp(DataPageResult<Map<String, Object>> pageMap) {
        List<ShellDataDTO> list = JsonUtils.mapper().convertValue(pageMap.getList(), new TypeReference<List<ShellDataDTO>>() {
        });

        List<ShellDataResp> collect = list.stream().map(dto -> {
            ShellDataResp resp = new ShellDataResp();
            resp.setAccountUuid(dto.getHostUuid());
            resp.setAccountId(dto.getTaquId());
            resp.setConsortiaName(dto.getConsortiaName());
            resp.setDt(dto.getDt());
            resp.setMeetingDuration(dto.getMeetingSeatDuration());
            resp.setMeetingOpenDuration(dto.getMeetingDuration());
            resp.setReceiveAmt(dto.getTalentScore());
            resp.setReceiveShell(dto.getShellScore());
            return resp;
        }).collect(Collectors.toList());

        PageDataResult<ShellDataResp> resultPage = new PageDataResult<>();
        resultPage.setPage((int) pageMap.getPage());
        resultPage.setPageSize((int) pageMap.getPageSize());
        resultPage.setList(collect);
        resultPage.setTotal(pageMap.getTotal());
        return resultPage;
    }

    @Override
    public String getDownloadUrl(ShellSearch search) {
        int page = 1;
        search.setPage(page);
        search.setPageSize(100);
        PageDataResult<ShellDataResp> pageResult = shellData(search);
        List<ShellDataExcel> allDataList = new ArrayList<>();
        transferAndAppend(pageResult.getList(), allDataList);

        while (CollectionUtils.isNotEmpty(pageResult.getList())) {
            page++;
            search.setPage(page);
            pageResult = shellData(search);
            if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                transferAndAppend(pageResult.getList(), allDataList);
            }
        }
        return buildFileToUpload(search, allDataList);
    }

    @Override
    public String clientGetDownloadUrl(ShellSearch search) {
        List<Long> teamId = listTeamId();
        search.setTeamId(teamId);
        return getDownloadUrl(search);
    }

    /**
     * 构建下载路径
     *
     * @param search
     * @param allDataList
     * @return
     */
    private String buildFileToUpload(ShellSearch search, List<ShellDataExcel> allDataList) {
        // 转化列表数据为Excel文件
        String fileName = "贝壳数据" + search.getStartTimeFormat() + "-" + search.getEndTimeFormat() + "-";
        File xlsxFile = buildExcelFile(allDataList, fileName, "贝壳数据");
        // 获取到下载地址
        return ossHandler.getDownloadUrlWithFileAndContentType(xlsxFile, MetaContentTypeEnum.XLSX.getCode());
    }

    /**
     * 转化处理
     *
     * @param sourceList
     * @param resultList
     */
    private void transferAndAppend(List<ShellDataResp> sourceList, List<ShellDataExcel> resultList) {
        List<ShellDataExcel> collect = sourceList.stream().map(resp -> {
            ShellDataExcel excel = new ShellDataExcel();
            BeanUtils.copyProperties(resp, excel);
            return excel;
        }).collect(Collectors.toList());

        resultList.addAll(collect);
    }

    /**
     * 团队id
     *
     * @return
     */
    private List<Long> listTeamId() {
        RoleVo currentRole = sysUserService.getCurrentRole(TeamTypeEnum.TALK_TEAM.getValue());
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        List<Long> teamIds;
        log.info("当前角色：{},当前用户id:{}", JsonUtils.objectToString(currentRole), user.getUserId());
        if (UserTypeEnum.MANAGER.getCode().equals(currentRole.getRoleKey())) {
            teamIds = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue()).stream().map(Team::getTeamId).collect(Collectors.toList());
        } else if (UserTypeEnum.LEADER.getCode().equals(currentRole.getRoleKey())) {
            TeamEmployee oneByUserIdAndType = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            teamIds = new ArrayList<>();
            teamIds.add(oneByUserIdAndType.getTeamId());
        } else if (UserTypeEnum.AGENTER.getCode().equals(currentRole.getRoleKey())) {
            // 经纪人角色 应该看不到
            TeamEmployee oneByUserIdAndType = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.TALK_TEAM.getValue());
            teamIds = new ArrayList<>();
            teamIds.add(oneByUserIdAndType.getTeamId());
        } else {
            throw new ServiceException("error_token", "无权限查询数据");
        }

        return teamIds;
    }

}
