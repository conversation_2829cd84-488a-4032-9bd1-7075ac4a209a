package cn.taqu.gonghui.chatroom.service;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.chatroom.constant.RoomStatusEnum;
import cn.taqu.gonghui.chatroom.entity.Room;
import cn.taqu.gonghui.chatroom.mapper.RoomMapper;
import cn.taqu.gonghui.chatroom.vo.RoomSubmitReq;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.service.TokenService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
//@RequiredArgsConstructor
@Service
public class RoomService {
    @Resource
    private RoomMapper roomMapper;
//    private final RoomMapper roomMapper;

    public Room getByRoomNo(String roomNo) {
        return roomMapper.selectOne(new LambdaQueryWrapper<Room>()
                .eq(Room::getRoomNo, roomNo)
        );

    }


    public void cancel(String roomNo, String sysUserUuid) {
        Room room = getByRoomNo(roomNo);
        Room update = new Room();
        update.setId(room.getId());
        update.setStatus(RoomStatusEnum.REJECT.getCode());
        roomMapper.updateById(update);
    }

    public void save(Room room) {

        int existsInitCount = roomMapper.selectCount(new LambdaQueryWrapper<Room>()
                .eq(Room::getAccountUuid, room.getAccountUuid())
                .in(Room::getStatus,
                        ImmutableList.of(RoomStatusEnum.INIT.getCode())));

        if (existsInitCount > 0) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "该房主的房间审批中");
        }

        int existsAcceptCount = roomMapper.selectCount(new LambdaQueryWrapper<Room>()
                .eq(Room::getAccountUuid, room.getAccountUuid())
                .in(Room::getStatus,
                        ImmutableList.of(RoomStatusEnum.ACCEPT.getCode())));
        if (existsAcceptCount > 0) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "该房主的房间已审批");
        }

        this.roomMapper.insert(room);
    }

    public Page<Room> page(String accountUuid,
                           String accountUuid_getByTaquId,
                           String orgUuid,
                           Long teamId,
                           Integer source,
                           Integer status,
                           Integer neStatus,
                           Integer pageNo,
                           Integer pageSize) {
        pageNo = Optional.ofNullable(pageNo).orElse(1);
        pageSize = Optional.ofNullable(pageSize).orElse(10);
        if (pageSize > 10000) {
            pageSize = 10000;
        }
        LambdaQueryWrapper<Room> queryWrapper = new LambdaQueryWrapper();
        if (StringUtils.isNotEmpty(accountUuid)) {
            queryWrapper.eq(Room::getAccountUuid, accountUuid);
        }
        if (StringUtils.isNotEmpty(accountUuid_getByTaquId)) {
            queryWrapper.eq(Room::getAccountUuid, accountUuid_getByTaquId);
        }
        if (StringUtils.isNotEmpty(orgUuid)) {
            queryWrapper.eq(Room::getOrgUuid, orgUuid);
        }
        if (teamId != null && teamId > 0) {
            queryWrapper.eq(Room::getTeamId, teamId);
        }

        if (source != null) {
            queryWrapper.eq(Room::getSource, source);
        }

        if(status != null){
            queryWrapper.eq(Room::getStatus, status);
        }
        if(neStatus != null){
            queryWrapper.ne(Room::getStatus, neStatus);
        }

        Page<Room> page = new Page<>(pageNo, pageSize);
        return roomMapper.selectPage(page, queryWrapper
                .orderByDesc(Room::getId)
        );


    }

    public Room getByAccountUuidAndStatus(String accountUuid, Integer status) {
        return this.roomMapper.selectOne(new LambdaQueryWrapper<Room>()
                .eq(Room::getAccountUuid, accountUuid)
                .eq(Room::getStatus, status)
        );
    }

    public Map<String, List<Room>>
    getMapByAccountUuidsAndStatusGroupByAccountUuid(List<String> accountUuids, Integer status) {
        return this.roomMapper.selectList(new LambdaQueryWrapper<Room>()
                        .in(Room::getAccountUuid, accountUuids)
                        .eq(Room::getStatus, status)
                ).stream()
                .collect(Collectors.groupingBy(Room::getAccountUuid, Collectors.toList()));
    }

    public List<Room> listByStatusOrderByIdDesc(List<Integer> status,
                                                Long lastId, Integer limit) {
        return this.roomMapper.listByStatusOrderByIdDesc(status, lastId, limit);
    }

    public void updateStatus(Long id, Integer status) {
        Room room = new Room();
        room.setId(id);
        room.setStatus(status);
        this.roomMapper.updateById(room);
    }

    public void edit(Long id, RoomSubmitReq req) {
        Room room = new Room();
        room.setId(id);
        room.setRemark(req.getRemark());
        room.setTimeScope(req.getTimeScope());
        room.setType(req.getType());
        room.setUpdateBy(Optional.ofNullable(req.getOperator()).orElse("system"));

        this.roomMapper.updateById(room);
    }

    public void removeByRoomNo(String roomNo) {
        Room room = this.getByRoomNo(roomNo);
        if (room == null) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "房间不存在");
        }
        Room update = new Room();
        update.setId(room.getId());
        update.setStatus(RoomStatusEnum.REMOVE.getCode());
        this.roomMapper.updateById(update);
        //this.roomMapper.deleteById(room.getId());
    }


}
