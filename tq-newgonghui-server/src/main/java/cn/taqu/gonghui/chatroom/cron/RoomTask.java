package cn.taqu.gonghui.chatroom.cron;

import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.task.annotation.SingleTask;
import cn.taqu.gonghui.chatroom.service.RoomBizService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RequestMapping(path = "/api",params = "service=roomTask")
@RestController
public class RoomTask {

    private final RoomBizService roomBizService;
    @EtcdValue(key = "biz.room.roomTaskBatchSize",defaultValue = "100")
    private static int roomTaskBatchSize = 100;
    @SingleTask
    @Scheduled(cron = "0/30 * * * * ?")
    public void scheduledExecute(){
        this.execute();
    }


    @RequestMapping(params = "method=execute")
    public void execute() {
        log.info("AuditOrderTask checkAuditOrder start");
        long start = System.currentTimeMillis();
        roomBizService.checkRoomStatus(roomTaskBatchSize);
        log.info("AuditOrderTask checkAuditOrder over cost:{}",
                System.currentTimeMillis() - start);

    }
}
