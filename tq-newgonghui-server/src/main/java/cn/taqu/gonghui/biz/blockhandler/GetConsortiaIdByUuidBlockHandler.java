package cn.taqu.gonghui.biz.blockhandler;


import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2023/11/28 15 44
 * discription
 */
@Slf4j
public class GetConsortiaIdByUuidBlockHandler {
    public static JsonResult getConsortiaIdByUuidBlockHandler(RequestParams params, BlockException ex) {

        log.info("根据用户uuid获取公会Id，触发限流了，返回默认值（异常）");
        return JsonResult.failed("触发限流了");
    }

    public static JsonResult getBatchConsortiaIdByUuidsBlockHandler(RequestParams params, BlockException ex) {
        log.info("根据用户uuid批量获取公会Id集合，触发限流了，返回默认值");
        return JsonResult.failed("触发限流了");
    }
}
