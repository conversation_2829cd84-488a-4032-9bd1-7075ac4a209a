package cn.taqu.gonghui.biz.fallbackhandler;



import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2023/12/11 17 14
 * discription
 */
@Slf4j
public class GetConsortiaIdByUuidFallback {


    public static JsonResult getConsortiaIdByUuidFallback(RequestParams params, Throwable ex) {
        log.warn("根据用户uuid获取所在公会Id发生异常了，触发了降级（无）", ex);
        return JsonResult.failed("获取所在公会id时发生异常了");
    }

    public static JsonResult getBatchConsortiaIdByUuidsFallback(RequestParams params, Throwable ex) {
        log.warn("根据用户uuid获取所在公会Id发生异常了，触发降级（无））", ex);
        return JsonResult.failed("获取所在公会Id时发生异常了");
    }
}
