package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.live.search.OrgStatisticsSearch;
import cn.taqu.gonghui.system.search.LivePerDayStatisticSearch;
import cn.taqu.gonghui.system.service.OrgStatisticsService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 主播数据统计
 */
@RequestMapping(value = "/api", params = "service=manageOrgStatistic")
@RestController
public class ManageOrgStatisticsController {

    @Autowired
    private OrgStatisticsService orgStatisticsService;



    /**
     * 2021-03-25
     * 新增主播统计需求
     * 计算新增主播的各种数据
     * 由php提供
     *
     * @return
     */
    @RequestMapping(params = "method=getConsortiaNewHostStatForAdmin")
    public JsonResult getConsortiaNewHostStatForAdmin(RequestParams params) {
        String orgId = params.getFormStringOption(0);
        String date = params.getFormStringOption(1);
        Integer export = params.getFormIntegerDefault(2, 0);
        Integer page = params.getFormIntegerDefault(3, 1);
        Integer pageSize = params.getFormIntegerDefault(4, 10);
        return JsonResult.success(orgStatisticsService.getLiveHostStatisticNewForAdmin(orgId,"", date, "", "", export, "", "", "", page, pageSize));
    }

    /**
     * 管理端--数据统计-团队数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getTeamDailyStatistics")
    public JsonResult getTeamDailyStatistics(RequestParams params){
        Long orgId = params.getFormLong(0);
        Long teamId = params.getFormLongOption(1);
        Long startTime = params.getFormLong(2);
        Long endTime = params.getFormLong(3);
        OrgStatisticsSearch search = new OrgStatisticsSearch();
        search.setOrgId(orgId);
        search.setTeamId(teamId);
        search.setStartTime(startTime);
        search.setEndTime(endTime);
        return JsonResult.success(orgStatisticsService.getTeamDailyStatistics(search));
    }

    /**
     * 管理端--数据统计--机构数据
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=findConsortiaPerDayListForAdmin")
    public JsonResult findConsortiaPerDayListForAdmin(RequestParams params) {
        String paramJson = params.getFormString(0);
        LivePerDayStatisticSearch search = JSON.parseObject(paramJson, new TypeReference<LivePerDayStatisticSearch>() {
        });
        return JsonResult.success(orgStatisticsService.getOrgDailyStatistics(search));
    }


    /**
     * 管理端-数据统计-主播数据
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=findConsortiaListForAdmin")
    public JsonResult   findConsortiaListForAdmin(RequestParams params) {
        Long orgId = params.getFormLong(0);
        String startTime = params.getFormString(1);
        String endTime = params.getFormString(2);
        String hostUuid = params.getFormStringOption(3);
        Long teamId = params.getFormLong(4);
        //20201009第六期需求,新增通过经纪人搜索主播
        String agentManageUuid = params.getFormStringOption(5);
        String applyLevel = params.getFormStringOption(6);
        String liveStatus = params.getFormStringOption(7);
        Integer page = params.getFormInteger(8);
        Integer pageSize = params.getFormInteger(9);
        String orderType = params.getFormStringOption(10);
        String sortType = params.getFormStringOption(11);
        //将筛选条件放入对象中
        LivePerDayStatisticSearch search = new LivePerDayStatisticSearch();
        search.setOrgId(orgId);
        search.setPage(page);
        search.setRows(pageSize);
        search.setStart(startTime);
        search.setEnd(endTime);
        search.setHost_uuid(hostUuid);
        search.setApply_level(applyLevel);
        search.setLive_status(liveStatus);
        search.setAgentManageUuid(agentManageUuid);
        search.setTeamId(teamId);
        search.setOrder_type(orderType);
        search.setSort_type(sortType);
        return JsonResult.success(orgStatisticsService.getOrgStatistics(search));
    }


    /**
     * 查詢运营人员统计数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=findOperatorData")
    public JsonResult findOperatorData(RequestParams params) {
        String operatorName = params.getFormStringOption(0);
        Long startTime = params.getFormLong(1);
        Long endTime = params.getFormLong(2);
        OrgStatisticsSearch search = new OrgStatisticsSearch();
        search.setBusinessPerson(operatorName);
        search.setStartTime(startTime);
        search.setEndTime(endTime);
        return JsonResult.success(orgStatisticsService.getOperatorDailyStatistics(search));
    }
}
