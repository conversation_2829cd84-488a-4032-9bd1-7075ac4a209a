package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.service.AccountLoginService;
import cn.taqu.gonghui.common.service.GeneralService;
import cn.taqu.gonghui.common.vo.IdNameVO;
import cn.taqu.gonghui.system.search.TeamHostOperateLogSearch;
import cn.taqu.gonghui.system.vo.TeamHostOperateLogVo;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-10-17 15:45
 */
@RestController
@RequestMapping(value = "/api",params = "service=common")
public class CommonController {

    @Autowired
    private GeneralService generalService;

    @Autowired
    private AccountLoginService accountLoginService;

    @RequestMapping(params = "method=getSelectList")
    public JsonResult getSelectList(RequestParams params){
        String conditionStr = params.getFormStringDefault(0, "");
        Map<String, List<IdNameVO>> map = generalService.getSelectList(conditionStr);

        return JsonResult.success(map);
    }

    @RequestMapping(params = "method=refreshAccount")
    public JsonResult refreshAccountInfo(RequestParams params){
        String accountUuid = params.getFormStringDefault(0, "");
        accountLoginService.refreshAccount(accountUuid);
        return JsonResult.success();
    }

}
