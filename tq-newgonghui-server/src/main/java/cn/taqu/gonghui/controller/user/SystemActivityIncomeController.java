package cn.taqu.gonghui.controller.user;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.search.SystemActivityIncomeSearch;
import cn.taqu.gonghui.system.service.SystemActivityIncomeService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api",params = "service=systemActivityIncome")
public class SystemActivityIncomeController {


    @Autowired
    private SystemActivityIncomeService systemActivityIncomeService;

    @RequestMapping(params = "method=getList")
    @PreAuthorize("@ss.hasPermi('systemActivityIncome:getList')")
    public JsonResult findGuildInfoPageList(RequestParams params) {
        String paramJson = params.getFormStringOption(0);
        Integer page = params.getFormIntegerDefault(1,1);
        SystemActivityIncomeSearch search = JSON.parseObject(paramJson, new TypeReference<SystemActivityIncomeSearch>() {
        });
        return JsonResult.success(systemActivityIncomeService.getSystemProfitServiceListForUser(search,page));
    }
}
