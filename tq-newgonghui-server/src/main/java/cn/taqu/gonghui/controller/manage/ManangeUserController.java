package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.req.LinkUserReq;
import cn.taqu.gonghui.system.search.UserSearch;
import cn.taqu.gonghui.system.service.SysUserService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @Date 2021/4/25
 */
@RestController
@RequestMapping(value = "/api", params = "service=manageUser")
public class ManangeUserController {

    @Autowired
    private SysUserService sysUserService;

    @RequestMapping(params = "method=getList")
    public JsonResult list(RequestParams params) {
        String formStr = params.getFormStringOption(0);
        Integer pageNo = params.getFormIntegerDefault(1,1);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        UserSearch userSearch = JsonUtils.stringToObject(formStr, new TypeReference<UserSearch>() {});
        return JsonResult.success(sysUserService.selectUserByPage(userSearch, pageNo, pageSize));
    }


    @RequestMapping(params = "method=updateStatus")
    public JsonResult updateStatus(RequestParams params) {
        String uuid  = params.getFormString(0);
        Integer status = params.getFormInteger(1);
        sysUserService.updateStatus(uuid,status);
        return JsonResult.success("操作成功");
    }

    /**
     * 用户注销
     * @param params
     * @return
     */
    @RequestMapping(params = "method=cancelUser")
    public JsonResult cancelUser(RequestParams params) {
        String uuid  = params.getFormString(0);
        sysUserService.cancelUser(uuid);
        return JsonResult.success("操作成功");
    }


    /**
     * 用户编辑
     * @param params
     * @return
     */
    @RequestMapping(params = "method=editUser")
    public JsonResult editUser(RequestParams params) {
        String uuid  = params.getFormStringDefault(0,"");
        String code  = params.getFormStringDefault(1,"");
        String mobile  = params.getFormStringDefault(2,"");
        String vcode  = params.getFormStringDefault(3,"");
        String userName  = params.getFormStringDefault(4,"");
        sysUserService.editUser(uuid,code,mobile,vcode,userName);
        return JsonResult.success("编辑成功");
    }

    /**
     * 获取用户职位和用户名称
     */
    @RequestMapping(params = "method=getUserInfo")
    public JsonResult getUserInfo(RequestParams params){
        String accountUuid = params.getFormStringOption(0);
        if (StringUtils.isBlank(accountUuid)) {
            return JsonResult.failed("用户uuid不能为空");
        }
        return JsonResult.success(sysUserService.getUserInfo(accountUuid));
    }

    /**
     * 所有单点有效用户
     */
    @RequestMapping(params = "method=pointAllUser")
    public JsonResult pointAllUser(RequestParams params){
        return JsonResult.success(sysUserService.pointAllUser());
    }

    /**
     * 添加对接运营
     * @param params
     * @return
     */
    @RequestMapping(params = "method=addLinkUser")
    public JsonResult addLinkUser(RequestParams params){
        String form = params.getFormString(0);
        LinkUserReq req = JsonUtils.stringToObject(form, new TypeReference<LinkUserReq>() {});
        sysUserService.addLinkUser(req);
        return JsonResult.success();
    }

    /**
     * 删除对接运营
     * @param params
     * @return
     */
    @RequestMapping(params = "method=delLinkUser")
    public JsonResult delLinkUser(RequestParams params){
        String form = params.getFormString(0);
        LinkUserReq req = JsonUtils.stringToObject(form, new TypeReference<LinkUserReq>() {});
        sysUserService.delLinkUser(req);
        return JsonResult.success();
    }

    /**
     * 获取对接运营列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=linkUserList")
    public JsonResult linkUserList(RequestParams params){
        String form = params.getFormString(0);
        LinkUserReq req = JsonUtils.stringToObject(form, new TypeReference<LinkUserReq>() {});
        return JsonResult.success(sysUserService.linkUserList(req));
    }
}
