package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.dto.SysRoleDto;
import cn.taqu.gonghui.system.entity.SysRole;
import cn.taqu.gonghui.system.service.SysRoleService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
@RestController
@RequestMapping(value = "/api",params = "service=manageRole")
public class ManageSysRoleController {

    @Autowired
     private SysRoleService sysRoleService;

    @RequestMapping(params = "method=getList")
    public JsonResult getList(RequestParams params) {
        return JsonResult.success(sysRoleService.selectRoleList(null));
    }

    @RequestMapping(params = "method=getListByPage")
    public JsonResult getListByPage(RequestParams params) {
        Integer pageNo = params.getFormIntegerDefault(0,1);
        Integer pageSize = params.getFormIntegerDefault(1, 10);
        return JsonResult.success(sysRoleService.selectRoleList(pageNo,pageSize));
    }


    /**
     * 添加角色信息
     * @param params
     * @return
     */
    @RequestMapping(params = "method=add")
    public JsonResult add(RequestParams params) {
        String formStr= params.getFormString(0);
        String createBy = params.getFormString(1);
        SysRoleDto sysRoleDto = JsonUtils.stringToObject(formStr, new TypeReference<SysRoleDto>() {});
        sysRoleDto.setCreateBy(createBy);
        sysRoleService.addRole(sysRoleDto);
        return JsonResult.success("操作成功");
    }

    /**
     * 编辑角色
     * @param params
     * @return
     */
    @RequestMapping(params = "method=edit")
    public JsonResult edit(RequestParams params) {
        String formStr= params.getFormString(0);
        String updateBy = params.getFormString(1);
        SysRoleDto sysRoleDto = JsonUtils.stringToObject(formStr, new TypeReference<SysRoleDto>() {});
        sysRoleDto.setUpdateBy(updateBy);
        sysRoleService.editRole(sysRoleDto);
        return JsonResult.success("操作成功");
    }

    /**
     * 菜单授权
     */
    @RequestMapping(params = "method=editRoleMenu")
    public JsonResult editRole(RequestParams params) {
        Long roleId= params.getFormLong(0);
        Long[] menuArr= params.getFormLongArray(1);
        sysRoleService.editRoleMenu(roleId,menuArr);
        return JsonResult.success("操作成功");
    }


    /**
     * 获取详情
     */
    @RequestMapping(params = "method=detail")
    public JsonResult detail(RequestParams params){
        Long roleId = params.getFormLong(0);
        SysRole sysRole = sysRoleService.selectRoleByRoleId(roleId);
        return JsonResult.success(sysRole);
    }

    /**
     * 删除
     */
    @RequestMapping(params = "method=delete")
    public JsonResult delete(RequestParams params){
        Long roleId = params.getFormLong(0);
        sysRoleService.deleteRole(roleId);
        return JsonResult.success("删除成功");
    }
}
