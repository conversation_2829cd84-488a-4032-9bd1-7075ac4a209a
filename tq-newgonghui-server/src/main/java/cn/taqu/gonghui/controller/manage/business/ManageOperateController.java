package cn.taqu.gonghui.controller.manage.business;

import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.dto.OperationPersonDto;
import cn.taqu.gonghui.system.dto.TargetInfoDto;
import cn.taqu.gonghui.system.entity.OperationPerson;
import cn.taqu.gonghui.system.entity.TargetInfo;
import cn.taqu.gonghui.system.service.ManageOperateService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * chenshuibi
 * 2022/06/22
 */
@RestController
@RequestMapping(value = "/api", params = "service=manageOperate")
@Slf4j
public class ManageOperateController {

    @Autowired
    private ManageOperateService manageOperateService;
    @EtcdValue(value = "biz.manageOperate.inviteLink", defaultValue = "https://union.taqu.cn/login?fromuser=")
    public static String inviteLink;


    /**
     * @param params
     * @return
     */
    @RequestMapping(params = "method=operatorList")
    public JsonResult operatorList(RequestParams params) {
        String loginname = params.getFormStringOption(0);
        String name = params.getFormStringOption(1);
        Integer pageNo = params.getFormIntegerOption(2);
        Integer pageSize = params.getFormIntegerOption(3);
        log.info("operatorList入参数,loginname={},name={},pageNo={},pageSize={}", loginname, name, pageNo, pageSize);

        OperationPersonDto operationPersonDto = new OperationPersonDto();
        operationPersonDto.setPageSize(pageSize);
        operationPersonDto.setPageNo(pageNo);
        operationPersonDto.setLoginname(loginname);
        operationPersonDto.setName(name);
        List<OperationPerson> pagelist = manageOperateService.getOperationPersonDtoList(operationPersonDto);
        return JsonResult.success(new PageInfo<>(pagelist));
    }


    /**
     * 新增
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=operatorAdd")
    public JsonResult operatorAdd(RequestParams params) {
        String loginname = params.getFormStringOption(0);
        String name = params.getFormStringOption(1);
        Integer permission = params.getFormIntegerOption(2);

        log.info("operatorAdd入参,loginname={},name={},permission={}", loginname, name, permission);

        OperationPerson operationPersonTemp = manageOperateService.getOperationPersonByName(name);
        if (operationPersonTemp != null) {
            return JsonResult.failed("name重复了!!!");
        }
        OperationPersonDto operationPersonDto = new OperationPersonDto();
        operationPersonDto.setLoginname(loginname);
        operationPersonDto.setName(name);
        operationPersonDto.setPermission(permission);
        operationPersonDto.setWorkStatus(1);
        int hashCode = name.hashCode() & Integer.MAX_VALUE;

        operationPersonDto.setInviteLink(inviteLink + hashCode);
        OperationPerson operationPerson = new OperationPerson();
        BeanUtils.copyProperties(operationPersonDto, operationPerson);
        manageOperateService.saveOperationPerson(operationPerson);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=operatorEdit")
    public JsonResult operatorEdit(RequestParams params) {
        Long id = params.getFormLongOption(0);
        String loginname = params.getFormStringOption(1);
        String name = params.getFormStringOption(2);
        Integer permission = params.getFormIntegerOption(3);

        log.info("operatorEdit入参,id={},loginname={},name={},permission={}", id, loginname, name, permission);
        OperationPerson operationPersonTemp = manageOperateService.getOperationPersonByName(name);
        if(!operationPersonTemp.getId().equals(id)){
            if (operationPersonTemp != null) {
                return JsonResult.failed("要修改的名字存在重复,不允许修改。");
            }
        }

        OperationPersonDto operationPersonDto = new OperationPersonDto();
        operationPersonDto.setLoginname(loginname);
        operationPersonDto.setName(name);
        operationPersonDto.setPermission(permission);
        operationPersonDto.setId(id);
        int hashCode = name.hashCode() & Integer.MAX_VALUE;
        operationPersonDto.setInviteLink(inviteLink + hashCode);

        OperationPerson operationPerson = new OperationPerson();
        BeanUtils.copyProperties(operationPersonDto, operationPerson);
        manageOperateService.updateOperationPerson(operationPerson);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=operatorDelete")
    public JsonResult operatorDelete(RequestParams params) {
        Long id = params.getFormLongOption(0);
        log.info("operatorDelete入参,id={}", id);
        OperationPerson operationPerson = new OperationPerson();
        operationPerson.setId(id);
        manageOperateService.deleteOperationPerson(operationPerson);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=targetSave")
    public JsonResult targetSave(RequestParams params) {
        Long money = params.getFormLongOption(0);
        Integer survivalRate = params.getFormIntegerOption(1);
        Long number = params.getFormLongOption(2);

        log.info("targetSave入参,money={},survivalRate={},number={}", money, survivalRate, number);

        TargetInfoDto targetInfoDto = new TargetInfoDto();
        targetInfoDto.setMoney(money);
        targetInfoDto.setNumber(number);
        targetInfoDto.setSurvivalRate(survivalRate);
        TargetInfo targetInfo = new TargetInfo();
        BeanUtils.copyProperties(targetInfoDto, targetInfo);
        manageOperateService.saveTargetInfo(targetInfo);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=targetInfo")
    public JsonResult targetInfo(RequestParams params) {
        TargetInfo targetInfo = manageOperateService.getTargetInfo();
        return JsonResult.success(targetInfo);
    }

//    @RequestMapping(params = "method=operatorNameList")
//    public JsonResult operatorNameList(RequestParams params) {
//        //todo oa 调用
//        return JsonResult.success();
//    }


}
