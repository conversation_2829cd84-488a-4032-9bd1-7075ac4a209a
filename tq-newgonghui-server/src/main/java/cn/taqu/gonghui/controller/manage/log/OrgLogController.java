package cn.taqu.gonghui.controller.manage.log;


import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/api", params = "service=orgLog")
@Slf4j
public class OrgLogController {
    @Autowired
    OrgNameLogMapper orgNameLogMapper;
    @Autowired
    OrgOperatorLogMapper orgOperatorLogMapper;
    @Autowired
    OrgBankLogMapper orgBankLogMapper;
    @Autowired
    OrgAccountLogMapper orgAccountLogMapper;
    @Autowired
    OrgCompanyLogMapper orgCompanyLogMapper;

    /**
     * @param params
     * @return
     */
    @RequestMapping(params = "method=orgNameLog")
    public JsonResult orgNameLog(RequestParams params) throws ParseException {
        String relevanceId =  params.getFormString(0);
        Integer pageNum =  params.getFormInteger(1);
        Integer pageSize =  params.getFormInteger(2);
        PageHelper.startPage(pageNum, pageSize);

        QueryWrapper<OrgNameLog> queryWrapper = new QueryWrapper<OrgNameLog>();
        queryWrapper.eq("relevance_id", relevanceId);
        queryWrapper.orderByDesc("create_time");
        List<OrgNameLog> pagelist = orgNameLogMapper.selectList(queryWrapper);
        PageHelper.clearPage();
        return JsonResult.success(new PageInfo<>(pagelist));
    }

    /**
     * @param params
     * @return
     */
    @RequestMapping(params = "method=orgOperatorLog")
    public JsonResult orgOperatorLog(RequestParams params) throws ParseException {
        String relevanceId =  params.getFormString(0);
        Integer pageNum =  params.getFormInteger(1);
        Integer pageSize =  params.getFormInteger(2);
        PageHelper.startPage(pageNum, pageSize);

        QueryWrapper<OrgOperatorLog> queryWrapper = new QueryWrapper<OrgOperatorLog>();
        queryWrapper.eq("relevance_id", relevanceId);
        queryWrapper.orderByDesc("create_time");
        List<OrgOperatorLog> pagelist = orgOperatorLogMapper.selectList(queryWrapper);
        PageHelper.clearPage();
        return JsonResult.success(new PageInfo<>(pagelist));
    }

    /**
     * @param params
     * @return
     */
    @RequestMapping(params = "method=orgBankLog")
    public JsonResult orgBankLog(RequestParams params) throws ParseException {
        String relevanceId =  params.getFormString(0);
        Integer pageNum =  params.getFormInteger(1);
        Integer pageSize =  params.getFormInteger(2);
        PageHelper.startPage(pageNum, pageSize);

        QueryWrapper<OrgBankLog> queryWrapper = new QueryWrapper<OrgBankLog>();
        queryWrapper.eq("relevance_id", relevanceId);
        queryWrapper.orderByDesc("create_time");
        List<OrgBankLog> pagelist = orgBankLogMapper.selectList(queryWrapper);
        PageHelper.clearPage();
        return JsonResult.success(new PageInfo<>(pagelist));
    }


    /**
     * @param params
     * @return
     */
    @RequestMapping(params = "method=orgAccountLog")
    public JsonResult orgAccountLog(RequestParams params) throws ParseException {
        String relevanceId =  params.getFormString(0);
        Integer pageNum =  params.getFormInteger(1);
        Integer pageSize =  params.getFormInteger(2);
        PageHelper.startPage(pageNum, pageSize);

        QueryWrapper<OrgAccountLog> queryWrapper = new QueryWrapper<OrgAccountLog>();
        queryWrapper.eq("relevance_id", relevanceId);
        queryWrapper.orderByDesc("create_time");
        List<OrgAccountLog> pagelist = orgAccountLogMapper.selectList(queryWrapper);
        PageHelper.clearPage();
        return JsonResult.success(new PageInfo<>(pagelist));
    }

    /**
     * @param params
     * @return
     */
    @RequestMapping(params = "method=orgCompanyLog")
    public JsonResult orgCompanyLog(RequestParams params) throws ParseException {
        String relevanceId =  params.getFormString(0);
        Integer pageNum =  params.getFormInteger(1);
        Integer pageSize =  params.getFormInteger(2);
        PageHelper.startPage(pageNum, pageSize);

        QueryWrapper<OrgCompanyLog> queryWrapper = new QueryWrapper<OrgCompanyLog>();
        queryWrapper.eq("relevance_id", relevanceId);
        queryWrapper.orderByDesc("create_time");
        List<OrgCompanyLog> pagelist = orgCompanyLogMapper.selectList(queryWrapper);
        PageHelper.clearPage();
        return JsonResult.success(new PageInfo<>(pagelist));
    }


}
