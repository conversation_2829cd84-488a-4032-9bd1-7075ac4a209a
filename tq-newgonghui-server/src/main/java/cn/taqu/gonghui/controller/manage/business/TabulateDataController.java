package cn.taqu.gonghui.controller.manage.business;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.entity.MyData;
import cn.taqu.gonghui.system.entity.OperationPerson;
import cn.taqu.gonghui.system.entity.Performance;
import cn.taqu.gonghui.system.service.ManageOperateService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据汇总
 */
@RestController
@RequestMapping(value = "/api", params = "service=tabulateData")
@Slf4j
public class TabulateDataController {

    @Autowired
    private ManageOperateService manageOperateService;


    /**
     * @param params
     * @return
     */
    @RequestMapping(params = "method=tabulateDataList")
    public JsonResult tabulateDataList(RequestParams params) throws ParseException {
        String name = params.getFormStringOption(0);
        Integer month = params.getFormIntegerOption(1);

        log.info("tabulateDataList入参,name={},month={}", name, month);


        List<MyData> pagelist = manageOperateService.myDataList(name, month);

        log.info("tabulateDataList 的 pagelist={}", JSON.toJSONString(pagelist));

        Map<String, Object> map = new HashMap<>();
        map.put("list", pagelist);


        OperationPerson operationPerson = manageOperateService.getOperationPersonByName(name);
        if (operationPerson != null) {
            map.put("inviteLink", operationPerson.getInviteLink());
        } else {
            map.put("inviteLink", null);
        }
        return JsonResult.success(map);
    }

    @RequestMapping(params = "method=performanceDataList")
    public JsonResult myPerformanceList(RequestParams params) {
        String name = params.getFormStringOption(0);
        Integer month = params.getFormIntegerOption(1);

        log.info("performanceDataList入参,name={},month={}", name, month);

        List<Performance> pagelist = manageOperateService.myPerformanceList(name, month);
        return JsonResult.success(pagelist);
    }


}
