package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.search.QuestionFeedbackSearch;
import cn.taqu.gonghui.system.service.QuestionFeedbackService;
import cn.taqu.gonghui.system.vo.QuestionFeedbackVo;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 管理端 用户意见反馈aa
 */
@RequestMapping(value = "/api", params = "service=manageFeedback")
@RestController
public class ManageQuestionFeedbackController {

    @Autowired
    QuestionFeedbackService feedbackService;

    /**
     * 管理端 - 用户反馈列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=list")
    public JsonResult list(RequestParams params){
        String title = params.getFormStringOption(0);
        String content = params.getFormStringOption(1);
        Long startTime = params.getFormLongOption(2);
        Long endTime = params.getFormLongOption(3);
        Integer status = params.getFormIntegerOption(4);
        Integer page = params.getFormIntegerDefault(5, 1);
        Integer pageSize = params.getFormIntegerDefault(6, 20);
        QuestionFeedbackSearch search = new QuestionFeedbackSearch();
        search.setTitle(title);
        search.setContent(content);
        search.setStartTime(startTime);
        search.setEndTime(endTime);
        search.setStatus(status);
        search.setPage(page);
        search.setPageSize(pageSize);
        List<QuestionFeedbackVo> vos = feedbackService.manageList(search);
        return JsonResult.success(new PageInfo<>(vos));
    }

    /**
     * 管理端 - 用户反馈状态变更
     * @param params
     * @return
     */
    @RequestMapping(params = "method=changeStatus")
    public JsonResult changeStatus(RequestParams params){
        Long id = params.getFormLongOption(0);
        Integer status = params.getFormIntegerDefault(1,null);
        String remark = params.getFormStringOption(2);
        String operator = params.getFormStringOption(3);
        feedbackService.changeStatus(id,status,remark,operator);
        return JsonResult.success("操作成功");
    }
}
