package cn.taqu.gonghui.controller.user;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.service.InvitationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2021/5/11
 */
@RestController
@RequestMapping(value = "/api", params = "service=invitationRecord")
public class InvitationRecordController {

    @Autowired
    private InvitationRecordService invitationRecordService;

    @RequestMapping(params = "method=getByPage")
    @PreAuthorize("@ss.hasPermi('invitationRecord:getByPage')")
    public JsonResult list(RequestParams params) {
        Integer pageNo = params.getFormIntegerDefault(0,1);
        Integer pageSize = params.getFormIntegerDefault(1, 10);
        return JsonResult.success(invitationRecordService.findByPageForUser(pageNo,pageSize));
    }

}
