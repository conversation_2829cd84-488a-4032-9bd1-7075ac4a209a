package cn.taqu.gonghui.controller.user;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.system.dto.WeddingHostDto;
import cn.taqu.gonghui.system.dto.WeddingHostInfoDto;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.service.WeddingHostService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Wu.D.J
 */
@Slf4j
@RestController
@RequestMapping(value = "/api", params = "service=AdminWeddingHost")
public class WeddingHostController {

    @Autowired
    private WeddingHostService weddingHostService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private TeamEmployeeService teamEmployeeService;

    @Autowired
    private TeamService teamService;

    @RequestMapping(params = "method=getHostStat")
    public JsonResult query(RequestParams params) {
        String param = params.getFormString(0);
        Integer page = params.getFormInteger(1);

        WeddingHostDto requestDto = JSON.parseObject(param, new com.alibaba.fastjson.TypeReference<WeddingHostDto>() {
        });

        if (requestDto.getConsortia_id() == null) {
            requestDto.setConsortia_id(getConsortiaId());
        }

        List<WeddingHostInfoDto> res = weddingHostService.query(requestDto, page);
        return JsonResult.success(res);
    }

    @RequestMapping(params = "method=allOnlineHost")
    public JsonResult allOnlineHost(RequestParams params) {
        Long consortiaId = params.getFormLongOption(0);
        if (consortiaId == null) {
            consortiaId = getConsortiaId();
        }

        List<Map<String, Object>> res = weddingHostService.allOnlineHost(consortiaId);
        return JsonResult.success(res);
    }

    @RequestMapping(params = "method=changeHost")
    public JsonResult changeHost(RequestParams params) {
        String weddingUuid = params.getFormString(0);
        String hostUuid = params.getFormString(1);
        Long consortiaId = params.getFormLongOption(2);
        if (consortiaId == null) {
            consortiaId = getConsortiaId();
        }

        weddingHostService.changeHost(weddingUuid, hostUuid, consortiaId);

        return JsonResult.success();
    }

    @RequestMapping(params = "method=exportHostStat")
    public void export(HttpServletResponse response, RequestParams params) throws IOException {
        String param = params.getFormString(0);

        WeddingHostDto requestDto = JSON.parseObject(param, new com.alibaba.fastjson.TypeReference<WeddingHostDto>() {
        });

        if (requestDto.getConsortia_id() == null) {
            requestDto.setConsortia_id(getConsortiaId());
        }

        List<WeddingHostInfoDto> rows = new ArrayList<>();
        int page = 1;
        List<WeddingHostInfoDto> data;
        do {
            data = weddingHostService.query(requestDto, page);
            if (CollectionUtils.isNotEmpty(data)) {
                rows.addAll(data);
            }
            page++;
        } while (CollectionUtils.isNotEmpty(data));

        rows.forEach(WeddingHostInfoDto::showTransform);

        // 通过工具类创建writer
        ExcelWriter writer = ExcelUtil.getWriter();

        //自定义标题别名
        writer.addHeaderAlias("wedding_uuid", "婚礼id");
        writer.addHeaderAlias("chat_uuid", "房间id");
        writer.addHeaderAlias("wedding_time_str", "婚礼时段");
        writer.addHeaderAlias("wedding_duration", "婚礼时长");
        writer.addHeaderAlias("host_uuid", "司仪id");
        writer.addHeaderAlias("wedding_status_str", "婚礼状态");
        writer.addHeaderAlias("host_on_site_str", "司仪是否到场");
        writer.addHeaderAlias("host_meeting_time", "司仪连麦时长");
        writer.addHeaderAlias("chat_cost", "房间送礼流水");
        writer.addHeaderAlias("host_income", "司仪收礼流水");
        writer.addHeaderAlias("bride_amount", "婚礼彩礼");

        // 默认的，未添加alias的属性也会写出，如果想只写出加了别名的字段，可以调用此方法排除之
        writer.setOnlyAlias(true);

        writer.write(rows, true);

        //response为HttpServletResponse对象
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=wedding_host.xls");
        ServletOutputStream out = response.getOutputStream();

        writer.flush(out, true);
        // 关闭writer，释放内存
        writer.close();
        //此处记得关闭输出Servlet流
        IoUtil.close(out);
    }

    public Long getConsortiaId() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();

        List<Team> teams = teamService.selectTeamList(user.getOrgId(), TeamTypeEnum.TALK_TEAM.getValue());

        if (CollectionUtils.isEmpty(teams)) {
            throw new ServiceException("暂无权限访问!");
        }

        return teams.get(0).getTeamId();
    }
}
