package cn.taqu.gonghui.controller.manage.business;


import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.cron.manage.BusinessTask;
import cn.taqu.gonghui.live.util.LiveDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.Date;


@RestController
@RequestMapping(value = "/api", params = "service=businessTask")
@Slf4j
public class BusinessTaskController {
    @Autowired
    BusinessTask businessTask;
    /**
     * @param params
     * @return
     */
    @RequestMapping(params = "method=timedTaskByDate")
    public JsonResult timedTaskByDate(RequestParams params) throws ParseException {
        String dateStr = params.getFormString(0);
        log.info("timedTaskByDate请求参数dateStr={}", dateStr);

        Date date = LiveDateUtils.strToDate(dateStr);
        Date yesterday = LiveDateUtils.getBeforeDay(date);
        businessTask.timedTaskByDate(yesterday);
        return JsonResult.success();
    }


}
