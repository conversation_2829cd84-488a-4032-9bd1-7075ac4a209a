package cn.taqu.gonghui.controller.user;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.jdbc.pagehelper.PageRequest;
import cn.taqu.core.orm.PageSearch;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.aspect.norepeat.NoRepeatCommit;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.mapper.ApprovalFlowMapper;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.service.flow.QuitGuildFlow;
import cn.taqu.gonghui.common.utils.RedisUtil;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.*;
import cn.taqu.gonghui.common.vo.req.*;
import cn.taqu.gonghui.common.vo.res.InviteHostDetailRes;
import cn.taqu.gonghui.common.vo.res.InviteHostItemRes;
import cn.taqu.gonghui.live.vo.DoPunishVO;
import cn.taqu.gonghui.soa.FinanceSoaService;
import cn.taqu.gonghui.soa.dto.BusinessRequest;
import cn.taqu.gonghui.system.dto.*;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.search.HostStatisticSearch;
import cn.taqu.gonghui.system.search.PunishSearch;
import cn.taqu.gonghui.system.search.WarnSearch;
import cn.taqu.gonghui.system.service.HostModifyRecordService;
import cn.taqu.gonghui.system.service.HostSharingProfitRecordService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamHostService;
import cn.taqu.gonghui.system.service.impl.PunishTicketSoaServiceImpl;
import cn.taqu.gonghui.system.vo.FlowNodeVo;
import cn.taqu.gonghui.system.vo.PunishLogVO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * <AUTHOR> aa
 * @Date 2021/5/11
 */
@RestController
@RequestMapping(value = "/api", params = "service=teamHost")
@Slf4j
public class TeamHostController {

    @Autowired
    private TeamHostService teamHostService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private HostSharingProfitRecordService sharingProfitRecordService;

    @Autowired
    private PunishTicketSoaServiceImpl punishTicketSoaService;

    @Autowired
    private ApprovalFlowMapper approvalFlowMapper;

    @Autowired
    private QuitGuildFlow quitGuildFlow;

    @Autowired
    private HostModifyRecordService hostModifyRecordService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private FinanceSoaService financeSoaService;

    /**
     * 主播信息
     * @param params
     * @return
     */
    @RequestMapping(params = "method=hostInfo")
    public JsonResult hostInfo(RequestParams params) {
        String form = params.getFormString(0);
        HostInfoReq hostInfoReq = JsonUtils.stringToObject(form, new TypeReference<HostInfoReq>() {});
        HostInfoDto hostInfoDto = teamHostService.getHostInfo(hostInfoReq.getHostUuid(), hostInfoReq.getTeamType());

        return JsonResult.success(hostInfoDto);
    }

    /**
     * 批量查询主播公会信息
     * @param params
     * @return
     */
    @RequestMapping(params = "method=multiHostInfo")
    public JsonResult multiHostInfo(RequestParams params) {
        String form = params.getFormString(0);
        List<HostInfoReq> req = JsonUtils.stringToObject(form, new TypeReference<List<HostInfoReq>>() {});
        List<HostOrgTeamDto> res = teamHostService.multiHostInfo(req);

        return JsonResult.success(res);
    }

    /**
     * 邀请主播
     */
    @RequestMapping(params = "method=invitationHost")
    @PreAuthorize("@ss.hasPermi('teamHost:invitationHost')")
    public JsonResult invitationHost(RequestParams params) {
        String form = params.getFormString(0);
        HostDto hostDto = JsonUtils.stringToObject(form, new TypeReference<HostDto>() {});
        teamHostService.invitationHost(hostDto);
        return JsonResult.success("操作成功");
    }

    /**
     * 重新邀请主播
     */

    @RequestMapping(params = "method=reInvitationHost")
    @PreAuthorize("@ss.hasPermi('teamHost:reInvitationHost')")
    public JsonResult reInvitationHost(RequestParams params) {
        Long inviteId = params.getFormLong(0);
        teamHostService.reInvitationHost(inviteId);
        return JsonResult.success("操作成功");
    }

    /**
     * 主播申请资料查询
     * @param params
     * @param search
     * @return
     */
    @RequestMapping(params = "method=getApplyHostinfo")
    @PreAuthorize("@ss.hasPermi('teamHost:getApplyHostinfo')")
    public JsonResult getApplyHostinfo(RequestParams params, PageSearch search) {
        String hostUuid = params.getFormStringDefault(1, null);//主播uuid
        teamHostService.getApplyHostinfo(hostUuid);
        return JsonResult.success();
    }

    /**
     * 调整分润比例
     * @param params
     * @return
     */
    @RequestMapping(params = "method=changeSharingProfit")
    @PreAuthorize("@ss.hasPermi('teamHost:changeSharingProfit')")
    public JsonResult changeSharingProfit(RequestParams params){
        String sharingProfitRate = params.getFormStringOption(0);
        String hostUuid = params.getFormStringOption(1);
        HostSharingProfitDto dto = new HostSharingProfitDto();
        dto.setSharingProfitRate(sharingProfitRate);
        dto.setHostUuid(hostUuid);

        // 获取操作人
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        if (null == user || StringUtils.isBlank(user.getAccountUuid())) {
            return JsonResult.failed("当前为无效登陆人，无法操作");
        }
        dto.setAccountUuid(user.getAccountUuid());
        dto.setType(SharingProfitOperateTypeEnum.CLIENT.getValue());
        sharingProfitRecordService.changeSharingProfitByHost(dto);

        // 根据不同用户 显示提示语
        String defaultTxt = "在下个月";
        Integer isGrayFirst = sharingProfitRecordService.getHostGrayFirst(hostUuid);
        if (isGrayFirst.equals(Constants.YES_1)) {
            defaultTxt = "在主播同意后马上";
        }

        JsonResult result = new JsonResult();
        result.setData("分润比例调整成功，新的分润会" + defaultTxt + "生效");
        result.setCode("0");

        return result;
    }

    /**
     * 调整经纪人或者团队
     */
    @RequestMapping(params = "method=changeTeamOrAgenter")
    @PreAuthorize("@ss.hasPermi('teamHost:changeTeamOrAgenter')")
    public JsonResult changeTeamOrAgenter(RequestParams params){
        Long employeeId = params.getFormLongOption(0);
        String employeeName = params.getFormStringOption(1);
        Long teamId = params.getFormLongOption(2);
        String teamName = params.getFormStringOption(3);
        String hostUuid = params.getFormStringOption(4);
        Integer businessType = params.getFormIntegerDefault(5,null);
        if(businessType == null){
            throw new ServiceException("business_type_null","请确定业务类型");
        }
        // 由于目前聊天室没有经纪人字段，无法同步，所以暂时限制 2022-03-14
        if(TeamTypeEnum.TALK_TEAM.getValue() == businessType){
            throw new ServiceException("business_type_not_allow","聊天室业务类型不能调整经纪人或者团队");
        }

        // 校验是否有操作该主播权限
        teamHostService.checkUserChangeAuth(hostUuid);

        HostTeamOrAgentDto dto = new HostTeamOrAgentDto();
        dto.setEmployeeId(employeeId);
        dto.setEmployeeName(employeeName);
        dto.setTeamId(teamId);
        dto.setTeamName(teamName);
        dto.setHostUuid(hostUuid);
        // 如果存在未迁移主播先执行迁移操作
        List<String> uuidList = new ArrayList<>();
        if (StringUtils.isNotBlank(hostUuid)) {
            uuidList.add(hostUuid);
        }
        dto.setUuidList(uuidList);
        teamHostService.moveHost(dto);

        teamHostService.changeTeamOrAgenter(dto);
        return JsonResult.success("操作成功");
    }

    /**
     * 批量调整经纪人或者团队
     */
    @RequestMapping(params = "method=batchChangeTeamOrAgenter")
    @PreAuthorize("@ss.hasPermi('teamHost:batchChangeTeamOrAgenter')")
    public JsonResult batchChangeTeamOrAgenter(RequestParams params){
        Long employeeId = params.getFormLongOption(0);
        String employeeName = params.getFormStringOption(1);
        Long teamId = params.getFormLongOption(2);
        String teamName = params.getFormStringOption(3);
        String uuidsStr = params.getFormStringOption(4);
        Integer businessType = params.getFormIntegerDefault(5,null);
        if(businessType == null){
            throw new ServiceException("business_type_null","请确定业务类型");
        }
        // 由于目前聊天室没有经纪人字段，无法同步，所以暂时限制 2022-03-14
        if(TeamTypeEnum.TALK_TEAM.getValue() == businessType){
            throw new ServiceException("business_type_not_allow","聊天室业务类型不能调整经纪人或者团队");
        }
        if (StringUtils.isBlank(uuidsStr)) {
            return JsonResult.failed("请选择调整的主播");
        }


        Gson gson = new GsonBuilder().create();
        List<String> uuidList = gson.fromJson(uuidsStr, new TypeToken<List<String>>() {}.getType());
        HostTeamOrAgentDto dto = new HostTeamOrAgentDto();
        dto.setEmployeeId(employeeId);
        dto.setEmployeeName(employeeName);
        dto.setTeamId(teamId);
        dto.setTeamName(teamName);
        dto.setUuidList(uuidList);

        // 校验是否有操作该主播权限
        for (String uuid : uuidList) {
            teamHostService.checkUserChangeAuth(uuid);
        }

        // 如果存在未迁移主播先执行迁移操作
        teamHostService.moveHost(dto);

        teamHostService.batchChangeTeamOrAgenter(dto);
        return JsonResult.success("操作成功");
    }

    /**
     * 根据主播uuid获取主播当前分润比例
     * @return
     */
    @RequestMapping(params = "method=getRateByHostUuids")
    public JsonResult getRateByHostUuids(RequestParams params){
        String[] hostUuids = params.getFormStringArray(0);
        ArrayList<String> uuidList = new ArrayList<>(Arrays.asList(hostUuids)) ;
        return JsonResult.success(teamHostService.getHostSharingProfitInfo(uuidList));
    }


    /**
     * 负责人主播数据详情
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getLiveHostDetails")
    @PreAuthorize("@ss.hasPermi('teamHost:getLiveHostDetails')")
    public JsonResult getLiveHostDetails(RequestParams params) {
        String hostUuid = params.getFormString(0);
        String startTime = params.getFormStringDefault(1,"");
        String endTime = params.getFormStringDefault(2,"");
        TeamHost teamHost = new TeamHost();
        teamHost.setHostUuid(hostUuid);
        teamHost.setStartTime(startTime);
        teamHost.setEndTime(endTime);
        teamHostService.checkUserChangeAuth(hostUuid);
        return JsonResult.success(teamHostService.getLiveHostSelfByAgent(teamHost));
    }



    /**
     * 主播列表
     *
     * @return
     */
    @RequestMapping(params = "method=getLiveHostListUser")
    @PreAuthorize("@ss.hasPermi('teamHost:getLiveHostListUser')")
    public JsonResult getLiveHostListUser(RequestParams params) {
        String liveNo = params.getFormStringDefault(0, null);  //直播号
        String agentIdStr = params.getFormStringDefault(1,"");
        String hostStatus = params.getFormStringDefault(2,"");
        String applyLevel = params.getFormStringDefault(3,"");
        String hostUuid = params.getFormStringDefault(4,"");
        Integer page = params.getFormIntegerDefault(5,1);
        Integer pageSize = params.getFormIntegerDefault(6,20);
        String teamIdStr = params.getFormStringDefault(7,"");
        String orderBy = params.getFormStringDefault(8,"");
        String startTime = params.getFormStringDefault(9,"");
        String endTime = params.getFormStringDefault(10,"");
        Integer isUpdate = params.getFormIntegerDefault(11, null);
        List<Long> agentIds = JsonUtils.stringToObject(agentIdStr, new TypeReference<List<Long>>() {
        });
        List<Long> teamIds = JsonUtils.stringToObject(teamIdStr, new TypeReference<List<Long>>() {
        });
        HostStatisticSearch search = new HostStatisticSearch();
        if (CollectionUtils.isNotEmpty(agentIds)){
            search.setAgentId(agentIds);
        }
        if(CollectionUtils.isNotEmpty(teamIds)){
            search.setTeamIds(teamIds);
        }
        search.setHost_status(hostStatus);
        search.setApply_level(applyLevel);
        search.setLive_no(liveNo);
        search.setHost_uuid(hostUuid);
        search.setPage(page);
        search.setPageSize(pageSize);
        search.setOrder_type(orderBy);
        search.setStart(startTime);
        search.setEnd(endTime);
        search.setIsUpdate(isUpdate);
        return JsonResult.success(teamHostService.getLiveHostListUser(search));
    }

    /**
     * 处罚记录
     * @param params
     */
    @RequestMapping(params = "method=getPunlishLogListUser")
    @PreAuthorize("@ss.hasPermi('teamHost:getPunlishLogListUser')")
    public JsonResult getPunlishLogListUser(RequestParams params){
         String searchForm = params.getFormStringDefault(0,"");
         Integer page = params.getFormIntegerDefault(1, 1);
         Integer pageSize = params.getFormIntegerDefault(2, 20);
         PunishSearch search = JsonUtils.stringToObject2(searchForm, new TypeReference<PunishSearch>() {});
        return JsonResult.success(teamHostService.getPunishList(search,page,pageSize));
    }

    /**
     * 警告记录
     * @param params
     */
    @RequestMapping(params = "method=getWarnLogListUser")
    @PreAuthorize("@ss.hasPermi('teamHost:getWarnLogListUser')")
    public JsonResult getWarnLogListUser(RequestParams params){
        String searchForm = params.getFormStringDefault(0,"");
        Integer page = params.getFormIntegerDefault(1, 1);
        Integer pageSize = params.getFormIntegerDefault(2, 20);
        WarnSearch search = JsonUtils.stringToObject2(searchForm, new TypeReference<WarnSearch>() {});
        return JsonResult.success(teamHostService.getWarnList(search,page,pageSize));
    }

    /**
     * 处罚主播
     * @param params
     */
    @RequestMapping(params = "method=punishHost")
    @PreAuthorize("@ss.hasPermi('teamHost:punishHost')")
    public JsonResult publishHost(RequestParams params){
        String hostUuid = params.getFormStringDefault(0, "");
        Integer publishWay = params.getFormIntegerDefault(1, 0);
        Integer publishHour = params.getFormIntegerDefault(2, 0);
        String reason = params.getFormStringDefault(3, "");
        teamHostService.punishHost(hostUuid,publishWay,publishHour,reason);
        return JsonResult.success("操作成功");
    }

    @RequestMapping(params = "method=createTokenForTest")
    public JsonResult createTokenForTest(RequestParams params) {
        String form = params.getFormStringDefault(0, "");
        String pwd = params.getFormStringDefault(1, "");
        SysUser sysUser = JsonUtils.stringToObject(form, SysUser.class);
        String password = "six";
        if (!password.equals(pwd)) {
            return JsonResult.failed("密码错误");
        }

        LoginUser loginUser = new LoginUser();
        loginUser.setToken("yangxuehe");
        loginUser.setUser(sysUser);
        String token = tokenService.createToken(loginUser);
        return JsonResult.success(token);
    }

    /**
     * 处罚理由列表
     * @param params
     */
    @RequestMapping(params = "method=getPunishTagConfig")
    public JsonResult getPunishTagConfig(RequestParams params){
        String loginName = params.getFormStringDefault(0, "");
        String punishType = params.getFormStringDefault(1, "");
        PunishTagConfigDTO punishTagConfigDTO = new PunishTagConfigDTO();
        punishTagConfigDTO.setLoginName(loginName);
        punishTagConfigDTO.setPunishType(punishType);
        List<Object> res = punishTicketSoaService.getPunishTagConfig(punishTagConfigDTO);
        return JsonResult.success(res);
    }

    /**
     * 获取处罚标签时长
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getPunishTagHour")
    public JsonResult getPunishTagHour(RequestParams params){
        String hostUuid = params.getFormStringDefault(0, "");
        Integer reasonId = params.getFormIntegerDefault(1, 0);
        PunishHourDTO punishHourDTO = new PunishHourDTO();
        punishHourDTO.setPunishId(reasonId);
        punishHourDTO.setUuid(hostUuid);
        Object res = punishTicketSoaService.getPunishTypeById(punishHourDTO);
        return JsonResult.success(res);
    }

    /**
     * 风控-处罚主播
     * @param params
     */
    @NoRepeatCommit
    @RequestMapping(params = "method=doPunish", method = RequestMethod.POST)
    public JsonResult doPunlish(RequestParams params){
        String form = params.getFormStringDefault(0, "");
        DoPunishVO doPunishVO = JsonUtils.stringToObject(form, DoPunishVO.class);

        // 组装请求参数dto
        PunishLogDTO dto = punishTicketSoaService.fillPunishLogDTO(doPunishVO);
        return punishTicketSoaService.doPunish(dto);
    }

    /**
     * 风控-直播处罚列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=riskPunishList")
    public JsonResult riskPunishList(RequestParams params) {
        String searchForm = params.getFormStringDefault(0,"");
        Integer pageNo = params.getFormIntegerDefault(1, 1);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        PunishLogDTO punishLogDTO = JsonUtils.stringToObject(searchForm, PunishLogDTO.class);
        punishLogDTO.setPageNo(pageNo);
        punishLogDTO.setPageSize(pageSize);
        PageInfo<PunishLogVO> pageInfo = punishTicketSoaService.riskPunishList(punishLogDTO);

        return JsonResult.success(pageInfo);
    }

    /**
     * 警告主播
     * @param params
     */
    @RequestMapping(params = "method=warnHost")
    @PreAuthorize("@ss.hasPermi('teamHost:warnHost')")
    public JsonResult warnhHost(RequestParams params){
        String hostUuid = params.getFormStringDefault(0, "");
        String reason = params.getFormStringDefault(1, "");
        teamHostService.warnHost(hostUuid,reason);
        return JsonResult.success("操作成功");
    }

    /**
     * 警告
     * @param params
     */
    @RequestMapping(params = "method=getWarnReasonList")
    public JsonResult getWarnReasonList(RequestParams params){
        return JsonResult.success(teamHostService.getWarnReasonList());
    }

    /**
     * app校验退会申请权限
     * @param params
     * @return
     */
    @RequestMapping(params = "method=validQuitGuild")
    public JsonResult validQuitGuild(RequestParams params) {
        String hostUuid = params.getFormStringDefault(0, "");
        AppValidVO appValidVO = teamHostService.quitGuildAuth(hostUuid);
        return JsonResult.success(appValidVO);
    }

    /**
     * app获取申请退会状态
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getQuitGuildStatus")
    public JsonResult getQuitGuildStatus(RequestParams params) {
        String hostUuid = params.getFormStringDefault(0, "");
        AppQuitGuildBtnVO btnVO = teamHostService.getQuitGuildInfo(hostUuid);
        return JsonResult.success(btnVO);
    }

    /**
     * app申请退会
     * @param params
     * @return
     */
    @RequestMapping(params = "method=applyQuitGuild", method = RequestMethod.POST)
    public JsonResult applyQuitGuild(RequestParams params) {
        String hostUuid = params.getFormStringDefault(0, "");
        Integer type = params.getFormIntegerDefault(1, 0);
        String reason = params.getFormStringDefault(2, "");
        ResultTextVO resultTextVO = new ResultTextVO();
        // 防止重复
        String uniqueKey = Thread.currentThread().getStackTrace()[1].getMethodName() + hostUuid;
        if (redisUtil.requestLock(uniqueKey)) {
            resultTextVO.setText("请耐心等待");
            return JsonResult.success(resultTextVO);
        }
        Integer applyInt = 3;
        if (applyInt.equals(type) && StringUtils.isEmpty(reason)) {
            resultTextVO.setText("申请原因不能为空");
            return JsonResult.success(resultTextVO);
        }

        teamHostService.applyQuitGuild(hostUuid, type, reason);

        String text = "";
        if (type.equals(Constants.TWO)) {
            ModifyRecordInfoDTO dto = hostModifyRecordService.getModifyRecordInfo(hostUuid, HostOperateTypeEnum.QUIT_GUILD_MOVE);
            StringBuilder sb = new StringBuilder("您已成功退出");
            if (Objects.nonNull(dto) && Objects.nonNull(dto.getOldOrgName())) {
                sb.append(dto.getOldOrgName());
            }
            sb.append("公会,现归属于中转公会，可直接开播或自行选择接受新的公会邀约");
            text = sb.toString();
        } else {
            text = "已创建退会申请单，请耐心等待";
        }
        resultTextVO.setText(text);

        return JsonResult.success(resultTextVO);
    }

    /**
     * 退会申请审核
     * @param params
     * @return
     */
    @RequestMapping(params = "method=quitGuildReview")
    public JsonResult quitGuildReview(RequestParams params) {
        Integer flowId = params.getFormIntegerDefault(0, 0);
        Integer actionType = params.getFormIntegerDefault(1, 0);
        String reviewUser = params.getFormStringDefault(2, "");
        String remark = params.getFormStringDefault(3, "");
        // 防止重复
        String uniqueKey = Thread.currentThread().getStackTrace()[1].getMethodName() + reviewUser;
        if (redisUtil.requestLock(uniqueKey)) {
            return JsonResult.failed("点击过于频繁，请耐心等待");
        }

        PassRejectVO passRejectVO = new PassRejectVO();
        passRejectVO.setReviewUser(reviewUser);
        passRejectVO.setFlowId(flowId);
        passRejectVO.setRemark(remark);
        quitGuildFlow.review(actionType, passRejectVO);

        return JsonResult.success();
    }

    @RequestMapping(params = "method=nodeDetail")
    public JsonResult nodeDetail(RequestParams params) {
        Integer flowId = params.getFormIntegerDefault(0, 0);
        List<FlowNodeVo> nodeVoList = quitGuildFlow.nodeDetail(flowId);
        return JsonResult.success(nodeVoList);
    }

    /**
     * 退出公会列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=quitGuildList")
    public JsonResult quitGuildList(RequestParams params) {
        String form = params.getFormStringDefault(0, null);
        PageRequest pageRequest = new PageRequest(params.getFormIntegerDefault(1, 1), params.getFormIntegerDefault(2, 10));
        JsonFormVO jsonFormVO = JsonUtils.stringToObject(form, JsonFormVO.class);
        // 查询用户对应的机构
        SysUser sysUser = sysUserService.selectUserByAccountUuid(jsonFormVO.getAccountUuid());

        ApprovalFlow approvalFlow = new ApprovalFlow();
        approvalFlow.setHostUuid(jsonFormVO.getHostUuid());
        approvalFlow.setOrgId(Objects.nonNull(sysUser) ? sysUser.getOrgId() : null);
        approvalFlow.setFlowType(FlowTypeEnum.QUIT_GUILD.getCode());
        PageInfo<ApprovalFlowItem> flowItemList = teamHostService.getQuitGuildPageList(approvalFlow, pageRequest);

        return JsonResult.success(flowItemList);
    }

    @RequestMapping(params = "method=quitGuildLog")
    public JsonResult quitGuildLog(RequestParams params) {
        Integer flowId = params.getFormIntegerDefault(0, 0);
        List<ApprovalFlowLogItem> itemList = teamHostService.getQuitGuildLog(flowId);

        return JsonResult.success(itemList);
    }

    /**
     * 聊天室艺人邀约入会
     * @param params
     * @return
     */
    @RequestMapping(params = "method=chatRoomInvite")
    public JsonResult chatRoomInvite(RequestParams params) {
        String form = params.getFormStringDefault(0, "");
        ChatRoomInviteVO chatRoomInviteVO = JsonUtils.stringToObject(form, ChatRoomInviteVO.class);
        // 防止重复
        String uniqueKey = Thread.currentThread().getStackTrace()[1].getMethodName() + chatRoomInviteVO.getLiveNo();
        if (redisUtil.requestLock(uniqueKey)) {
            return JsonResult.failed("请勿重复提交");
        }
        teamHostService.chatRoomInvite(chatRoomInviteVO, false);

        return JsonResult.success();
    }

    @RequestMapping(params = "method=reInvite")
    public JsonResult reInvite(RequestParams params) {
        String form = params.getFormStringDefault(0, "");
        ChatRoomInviteVO chatRoomInviteVO = JsonUtils.stringToObject(form, ChatRoomInviteVO.class);
        // 防止重复
        String uniqueKey = Thread.currentThread().getStackTrace()[1].getMethodName() + chatRoomInviteVO.getId();
        if (redisUtil.requestLock(uniqueKey)) {
            return JsonResult.failed("请勿重复提交");
        }
        teamHostService.reInvite(chatRoomInviteVO);

        return JsonResult.success();
    }

    /**
     * 入会邀请详情页
     * @param params
     * @return
     */
    @RequestMapping(params = "method=inviteHostView")
    public JsonResult inviteHostView(RequestParams params) {
        Integer inviteId = params.getFormIntegerDefault(0, 0);
        String hostUuid = params.getFormStringDefault(1, "");
        InviteHostReq inviteHostReq = new InviteHostReq();
        inviteHostReq.setInviteId(inviteId);
        inviteHostReq.setHostUuid(hostUuid);
        InviteHostDetailRes inviteHostDetailRes = teamHostService.getChatRoomInviteDetail(inviteHostReq);

        return JsonResult.success(inviteHostDetailRes);
    }

    @RequestMapping(params = "method=inviteHostReview")
    public JsonResult inviteHostReview(RequestParams params) {
        Integer inviteId = params.getFormIntegerDefault(0, 0);
        String hostUuid = params.getFormStringDefault(1, "");
        Integer status = params.getFormIntegerDefault(2, 0);
        // 防止重复
        String uniqueKey = Thread.currentThread().getStackTrace()[1].getMethodName() + inviteId;
        if (redisUtil.requestLock(uniqueKey)) {
            return JsonResult.failed("请勿重复操作");
        }
        InviteHostReq inviteHostReq = new InviteHostReq();
        inviteHostReq.setInviteId(inviteId);
        inviteHostReq.setHostUuid(hostUuid);
        inviteHostReq.setStatus(status);
        teamHostService.chatRoomInviteReview(inviteHostReq);

        return JsonResult.success(true);
    }

    @RequestMapping(params = "method=inviteRecordList")
    public JsonResult inviteRecordList(RequestParams params) {
        String form = params.getFormStringDefault(0, "");
        InviteHostReq inviteHostReq = JsonUtils.stringToObject(form, InviteHostReq.class);
        PageRequest pageRequest = new PageRequest(params.getFormIntegerDefault(1, 1), params.getFormIntegerDefault(2, 10));

        // 查询用户对应的机构
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        ApprovalFlow approvalFlow = new ApprovalFlow();
        approvalFlow.setOrgId(loginUser.getUser().getOrgId());
        approvalFlow.setHostUuid(Objects.nonNull(inviteHostReq.getUuid()) ? inviteHostReq.getUuid() : null);
        approvalFlow.setLiveNo(Objects.nonNull(inviteHostReq.getAccountId()) ? inviteHostReq.getAccountId() : null);
        approvalFlow.setFlowType(FlowTypeEnum.CHATROOM_INVITE.getCode());

        PageInfo<InviteHostItemRes> detailResPageInfo = teamHostService.getInvitePageList(approvalFlow, pageRequest);

        return JsonResult.success(detailResPageInfo);
    }


    @RequestMapping(params = "method=updateSql")
    public JsonResult updateSql(RequestParams params) {
        //chatRoomInviteTask.chatFourDayOverTime();
        String sql = params.getFormStringDefault(0, "");
        String hostUuid = "bgggjecfffeibieb"; String content = "xxsdf";
        approvalFlowMapper.updateSql(sql);
        return JsonResult.success(sql);
    }

    @RequestMapping(params = "method=runTask")
    public JsonResult runTask(RequestParams params) {
        return JsonResult.success();
        //Integer index = params.getFormIntegerDefault(0, 3);
        //if (index.equals(3)) {
        //    chatRoomInviteTask.chatThreeDayOverTime();
        //} else {
        //    chatRoomInviteTask.chatFourDayOverTime();
        //}
        //return JsonResult.success(index);
    }

    /**
     * 主播账户明细
     * @param params
     * @return
     */
    @RequestMapping(params = "method=hostAccountDetail")
    public JsonResult hostAccountDetail(RequestParams params) {
        String form = params.getFormString(0);
        LiveAccountDetailReq req = JsonUtils.stringToObject(form, new TypeReference<LiveAccountDetailReq>() {});
        UserAuthVO authVO = teamHostService.getUserAuthUuids();
        req.setConsortia_id(authVO.getTeamIdList());
        if (StringUtils.isNotBlank(req.getHost_uuid()) ) {
            if (!authVO.getUuidList().contains(req.getHost_uuid())) {
                throw new ServiceException("user_auth","所查询uuid不在用户权限范围内");
            }
        }
        // 自提需求暂停
        //JSONObject res = liveSoaService.getConsortiaHostEarningsList(req);
        //return JsonResult.success(res);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=hostAccountSettle")
    public JsonResult hostAccountSettle(RequestParams params) {
        String form = params.getFormString(0);
        LiveConsortiaCashReq req =  JsonUtils.stringToObject(form, new TypeReference<LiveConsortiaCashReq>() {});
        UserAuthVO authVO = teamHostService.getUserAuthUuids();
        req.setConsortia_id(authVO.getTeamIdList());
        if (StringUtils.isNotBlank(req.getHost_uuid()) && !authVO.getUuidList().contains(req.getHost_uuid())) {
            throw new ServiceException("user_auth","所查询uuid不在用户权限范围内");
        }
        // 自提需求暂停
        //JSONObject res = liveSoaService.getConsortiaCashList(req);
        //return JsonResult.success(res);
        return JsonResult.success();
    }

    /**
     * 主播收礼明细
     * @param params
     * @return
     */
    @RequestMapping(params = "method=receiveGiftDetail")
    public JsonResult receiveGiftDetail(RequestParams params) {
        String form = params.getFormString(0);
        LiveGiftReq req = JsonUtils.stringToObject(form, new TypeReference<LiveGiftReq>() {});
        UserAuthVO authVO = teamHostService.getUserAuthUuids();
        // 非机构管理员无法查看
        if (!authVO.getUserType().equals(UserTypeEnum.MANAGER.getType())) {
            return JsonResult.success(new PageDataVo<>());
        }
        // 如果没有输入查询uuid 也返回空
        if (StringUtils.isBlank(req.getHost_uuid())) {
            return JsonResult.success(new PageDataVo<>());
        }
        // uuid不为空 判断是否在权限用户内
        if (StringUtils.isNotBlank(req.getHost_uuid()) && !authVO.getUuidList().contains(req.getHost_uuid())) {
            throw new ServiceException("user_auth","所查询uuid不在用户权限范围内");
        }
        // 备注 自提需求暂停
        //JSONObject jsonObject = liveSoaService.getHostIncomeLog(req);
        //return JsonResult.success(jsonObject);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=transferOrg")
    public JsonResult transferOrg(RequestParams params) {
        String form = params.getFormString(0);
        ResetHostReq req = JsonUtils.stringToObject(form, new TypeReference<ResetHostReq>() {});
        if (req.getType().equals(ResetTypeEnum.ALL.getCode())) {
            return JsonResult.failed("http不允许操作全部");
        }
        List<String> res = teamHostService.batchTransferGuild(req);
        //orgTansferTask.liveAllTransfer();
        //orgTansferTask.liveAllProfitReset();

        return JsonResult.success(res);
    }

    @RequestMapping(params = "method=testFinance", method = RequestMethod.POST)
    public JsonResult testFinance(RequestParams params) {
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        BusinessRequest businessRequest = new BusinessRequest();
        Integer appCode = Optional.ofNullable(soaBaseParams.getAppcode()).orElse(1);
        Integer cloned = Optional.ofNullable(soaBaseParams.getCloned()).orElse(1);
        businessRequest.setAccount_uuid("iop44ezeziy");
        businessRequest.setAppcode(appCode);
        businessRequest.setCloned(cloned);
        // 调用业财触发 聊天室素人转公会提现 清除素人账户余额
        financeSoaService.chatUserToGuildWithdrawal(businessRequest);
        //String form = params.getFormString(0);
        //quitGuildTask.oneWeekAutoReview();
        return JsonResult.success();
    }

}




