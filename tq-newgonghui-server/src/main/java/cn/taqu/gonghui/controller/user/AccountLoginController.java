package cn.taqu.gonghui.controller.user;

import cn.taqu.core.utils.LocalConfUtil;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.Constants;
import cn.taqu.gonghui.common.service.AccountLoginService;
import cn.taqu.gonghui.common.service.SildeVerifyService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LoginVo;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/4/25
 */
@Slf4j
@RestController
@RequestMapping(value = "api", params = "service=account")
public class AccountLoginController {

    public static final int COOKIE_MAX_AGE = 24 * 3600;
    public static final int COOKIE_HALF_HOUR = 30 * 60;

    @Autowired
    private AccountLoginService accountLoginService;

    @Autowired
    private SildeVerifyService sildeVerifyService;

    /**
     * 登录方法
     * @return 结果
     */
    @RequestMapping(params = "method=login")
    public JsonResult login(HttpServletResponse response,RequestParams params) {
        String mobile = params.getFormString(0);
        String verifyCode = params.getFormString(1);
        LoginVo login = accountLoginService.login(mobile, verifyCode);
        setCookie("token",login.getToken(),COOKIE_MAX_AGE);
        return JsonResult.success(login);
    }

    /**
     * 保持登录认证
     * @param params
     * @return
     */
    @RequestMapping(params = "method=authLogin")
    public JsonResult authLogin(RequestParams params) {
        return JsonResult.success(1);
    }


    public static void setCookie(String name,String value, int maxValue) {
        if (StringUtils.isBlank(name)) {
            return;
        }
        if (null == value) {
            value = "";
        }
        Cookie cookie = new Cookie(name, value);
        cookie.setPath("/");
        if (maxValue != 0) {
            cookie.setMaxAge(maxValue);
        } else {
            cookie.setMaxAge(COOKIE_HALF_HOUR);
        }
        String localEnv = LocalConfUtil.getLocalEnv();
        log.info("当前环境：{}",localEnv);
        if(!localEnv.startsWith("test")){
            cookie.setSecure(true);
        }
        cookie.setDomain("taqu.cn");
        ServletUtils.getResponse().addCookie(cookie);
    }




    @RequestMapping(params = "method=sildeVerify")
    public JsonResult sildeVerify(HttpServletRequest httpRequest,RequestParams params) {
        String sign =  params.getFormStringDefault(0,"");
        String sessionId = params.getFormStringDefault(1,"");
        String token =  params.getFormStringDefault(2,"");
        String scene =  params.getFormStringDefault(3,"");
        return JsonResult.success(sildeVerifyService.isValid(httpRequest,sign,sessionId,token,scene));
    }


}
