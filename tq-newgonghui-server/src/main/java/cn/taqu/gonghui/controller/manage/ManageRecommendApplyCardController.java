package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.dto.RecommendApplyCardDto;
import cn.taqu.gonghui.system.search.RecommendApplyCardSearch;
import cn.taqu.gonghui.system.service.RecommendApplyCardService;
import cn.taqu.gonghui.system.vo.RecommendApplyCardVo;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 营销管理（推荐位管理）
 */
@RestController
@RequestMapping(value = "/api",params = "service=manageRecommendApplyCard")
public class ManageRecommendApplyCardController {

    @Autowired
    private RecommendApplyCardService applyCardService;

    /**
     * 列表查询
     * @param params
     * @return
     */
    @RequestMapping(params = "method=pageList")
    public JsonResult pageList(RequestParams params){
        String formStr = params.getFormStringOption(0);
        RecommendApplyCardSearch search = JsonUtils.stringToObject2(formStr, RecommendApplyCardSearch.class);
        List<RecommendApplyCardVo> recommendApplyCardVos = applyCardService.pageList(search);
        return JsonResult.success(new PageInfo<>(recommendApplyCardVos));
    }

    /**
     * 作废
     * @param params
     * @return
     */
    @RequestMapping(params = "method=changeStatus")
    public JsonResult changeStatus(RequestParams params){
        Integer status = params.getFormInteger(0);
        Long id = params.getFormLong(1);
        applyCardService.updateStatus(status,id);
        return JsonResult.success("操作成功");
    }

    /**
     * 发放申请卡
     * @return
     */
    @RequestMapping(params = "method=sendApplyCard")
    public JsonResult sendApplyCard(RequestParams params){
        String formStr = params.getFormString(0);
        RecommendApplyCardDto recommendApplyCardDto = JsonUtils.stringToObject2(formStr, RecommendApplyCardDto.class);
        applyCardService.sendApplyCard(recommendApplyCardDto);
        return JsonResult.success("操作成功");
    }
}
