package cn.taqu.gonghui.controller.qiniu;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.service.impl.QiNiuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping(value = "/api", params = "service=qiniu")
@RestController
public class QiNiuController {

    @Autowired
    private QiNiuService qiNiuService;

    @RequestMapping(params = "method=getUpdateToken")
    public JsonResult getUpdateToken(RequestParams params) {
        String bucket = params.getFormString(0);
        return JsonResult.success(qiNiuService.getUpdateToken(bucket));
    }

    @RequestMapping(params = "method=getUpdateVideoToken")
    public JsonResult getUpdateVideoToken(RequestParams params) {
        String type = params.getFormString(0);
        String source = params.getFormString(1);

        return JsonResult.success(qiNiuService.getUpdateVideoToken(type, source));
    }

    /**
     * 私有空间下载地址
     * @param params
     * @return
     */
    @RequestMapping(params = "method=privateDownloadUrl")
    public JsonResult privateDownloadUrl(RequestParams params){
        String bucket = params.getFormString(0);
        String baseUrl = params.getFormString(1);
        return JsonResult.success(qiNiuService.privateDownloadUrl(bucket, baseUrl));
    }
}
