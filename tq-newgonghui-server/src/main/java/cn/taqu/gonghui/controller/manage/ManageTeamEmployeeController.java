package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.search.TeamEmployeeSearch;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
@RestController
@RequestMapping(value = "/api", params = "service=manageTeamEmployee")
public class ManageTeamEmployeeController {

    @Autowired
    private TeamEmployeeService teamEmployeeService;

    /**
     * 成员列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getList")
    public JsonResult list(RequestParams params) {
        String formStr = params.getFormStringOption(0);
        Integer pageNo = params.getFormIntegerDefault(1,1);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        TeamEmployeeSearch search = JsonUtils.stringToObject(formStr, new TypeReference<TeamEmployeeSearch>() {});
        return JsonResult.success(teamEmployeeService.selectTeamEmployeePage(search,pageNo,pageSize));
    }

    /**
     * 成员离职
     * @param params
     * @return
     */
    @RequestMapping(params = "method=employeeDeparture")
    public JsonResult employeeDeparture(RequestParams params) {
        String userId = params.getFormString(0);
        teamEmployeeService.employeeDeparture(userId);
        return JsonResult.success("离职成功");
    }

    /**
     * 获取经纪人下拉tree
     */
    @RequestMapping(params = "method=tree")
    public JsonResult tree(RequestParams params){
        Integer type = params.getFormInteger(0);
        return JsonResult.success(teamEmployeeService.selectAgentList(type));
    }

    /**
     * 获取该机构下经纪人下拉tree
     */
    @RequestMapping(params = "method=treeByOrgId")
    public JsonResult treeByOrgId(RequestParams params){
        Long orgId = params.getFormLongOption(0);
        Integer type = params.getFormIntegerOption(1);
        return JsonResult.success(teamEmployeeService.selectAgentListByOrgId(orgId,type));
    }

}
