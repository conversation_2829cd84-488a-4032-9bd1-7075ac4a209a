package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.jdbc.pagehelper.PageRequest;
import cn.taqu.core.jdbc.pagehelper.PageResult;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.FlowTypeEnum;
import cn.taqu.gonghui.common.constant.SharingProfitOperateTypeEnum;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.vo.ApprovalFlowItem;
import cn.taqu.gonghui.common.vo.ApprovalFlowLogItem;
import cn.taqu.gonghui.system.dto.HostDto;
import cn.taqu.gonghui.system.dto.HostIsGroupDTO;
import cn.taqu.gonghui.system.dto.HostSharingProfitDto;
import cn.taqu.gonghui.system.search.HostStatisticSearch;
import cn.taqu.gonghui.system.search.LiveHostSelfStatisticSearch;
import cn.taqu.gonghui.system.service.HostModifyRecordService;
import cn.taqu.gonghui.system.service.HostSharingProfitRecordService;
import cn.taqu.gonghui.system.service.OrgStatisticUserService;
import cn.taqu.gonghui.system.service.TeamHostService;
import cn.taqu.gonghui.system.vo.TeamHostVo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2021/5/11
 */
@RestController
@RequestMapping(value = "/api", params = "service=manageTeamHost")
public class ManageTeamHostController {

    @Autowired
    private TeamHostService teamHostService;
    @Autowired
    private OrgStatisticUserService orgStatisticUserService;
    @Autowired
    private HostSharingProfitRecordService sharingProfitRecordService;
    @Autowired
    private HostModifyRecordService hostModifyRecordService;

    /**
     * 直播邀约结果
     * 邀约结果
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=hostInvite")
    public JsonResult hostInvite(RequestParams params) {
        String accountUuuid = params.getFormStringDefault(0, "");
        String hostUuid = params.getFormStringDefault(1, "");
        Integer profit = params.getFormIntegerDefault(2, 0);
        Long teamId = params.getFormLongDefault(3, null);
        Integer forceSameWithOtherTeam = params.getFormIntegerDefault(4, 1);
        Integer hostType = params.getFormIntegerDefault(5, 0);
        teamHostService.addTeamHost(accountUuuid, hostUuid, profit, teamId, forceSameWithOtherTeam, hostType);
        return JsonResult.success("邀请主播成功");
    }

    /**
     * 老公会同步主播信息（老公会修改主播所属公会）
     */
    @RequestMapping(params = "method=updateHostInfo")
    public JsonResult updateHostInfo(RequestParams params) {
        Long teamId = params.getFormLongOption(0);
        String hostUuid = params.getFormStringOption(1);
        teamHostService.updateHostInfo(teamId, hostUuid);
        return JsonResult.success("修改公会同步主播信息成功");
    }

    /**
     * 管理端批量修改公会
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=changeHostTeam")
    public JsonResult changeHostTeam(RequestParams params) {
        Long teamId = params.getFormLongOption(0);
        String hostUuidArrStr = params.getFormStringOption(1);
        Gson gson = new GsonBuilder().create();
        List<String> hostUuids = null;
        if (StringUtils.isNotBlank(hostUuidArrStr)) {
            hostUuids = gson.fromJson(hostUuidArrStr, new TypeToken<List<String>>() {
            }.getType());
        }
        teamHostService.changeHostTeam(teamId, hostUuids);
        return JsonResult.success("恭喜你，成功转换了" + hostUuids.size() + "名主播");
    }

    /**
     * 主播数据
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getLiveHostList")
    public JsonResult getLiveHostList(RequestParams params) {
        String consortiaId = params.getFormStringDefault(0, "");
        Long agentId = params.getFormLongDefault(1, null);
        String liveStatus = params.getFormStringDefault(2, "");
        String applyLevel = params.getFormStringDefault(3, "");
        Integer page = params.getFormIntegerDefault(4, 1);
        Integer pageSize = params.getFormIntegerDefault(5, 20);
        String createTimeStart = params.getFormStringDefault(6, "");
        String createTimeEnd = params.getFormStringDefault(7, "");
        String uuid = params.getFormStringDefault(8, "");
        Integer hostType = params.getFormIntegerDefault(9, null);
        HostStatisticSearch search = new HostStatisticSearch();
        search.setAgentIdForAdmin(agentId);
        search.setConsortiaId(consortiaId);
        search.setLive_status(liveStatus);
        search.setApply_level(applyLevel);
        search.setPage(page);
        search.setRows(pageSize);
        search.setCreateTimeStart(createTimeStart);
        search.setCreateTimeEnd(createTimeEnd);
        search.setHost_uuid(uuid);
        search.setHostType(hostType.equals(0) ? null : hostType);
        return JsonResult.success(orgStatisticUserService.getLiveHostListForAdmin(search));
    }

    /**
     * 主播数据详情
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getLiveHostDetails")
    public JsonResult getLiveHostDetails(RequestParams params) {
        String hostUuid = params.getFormString(0);
        String startDate = params.getFormString(1);
        String endDate = params.getFormString(2);
        LiveHostSelfStatisticSearch search = new LiveHostSelfStatisticSearch();
        search.setHostUuid(hostUuid);
        search.setStartTime(startDate);
        search.setEndTime(endDate);
        return JsonResult.success(orgStatisticUserService.getLiveHostSelfStatistic(search));
    }

    /**
     * 管理端调整分润比例
     */
    @RequestMapping(params = "method=changeProfitRate")
    public JsonResult changeProfitRate(RequestParams params) {
        String sharingProfitRate = params.getFormStringOption(0);
        String hostUuid = params.getFormStringOption(1);
        String operater = params.getFormStringOption(2);
        Integer hostType = params.getFormInteger(3);

        HostSharingProfitDto dto = new HostSharingProfitDto();
        dto.setSharingProfitRate(sharingProfitRate);
        dto.setAccountUuid(operater);
        dto.setHostUuid(hostUuid);
        dto.setType(SharingProfitOperateTypeEnum.MANAGER.getValue());
        dto.setHostType(hostType);
        sharingProfitRecordService.changeSharingProfitByHost(dto);
        return JsonResult.success("操作成功");
    }

    @RequestMapping(params = "method=switchGroup")
    public JsonResult switchGroup(RequestParams params) {
        String form = params.getFormString(0);
        HostIsGroupDTO dto = JsonUtils.stringToObject(form, new TypeReference<HostIsGroupDTO>() {});
        teamHostService.setHostIsGroup(dto.getHostUuid(), dto.getIsGroup());
        return JsonResult.success();
    }

    /**
     * 根据批次号，获取该批次号成功或者失败的艺人uuid
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=hostModifyList")
    public JsonResult hostModifyList(RequestParams params) {
        String batchId = params.getFormStringOption(0);
        Integer status = params.getFormIntegerOption(1);
        List<String> uuids = hostModifyRecordService.getUuidByBatchId(batchId, status);
        return JsonResult.success(uuids);
    }

    /**
     * 艺人转会记录
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=hostMoveRecord")
    public JsonResult hostMoveRecord(RequestParams params) {
        Integer pageNo = params.getFormIntegerOption(0);
        Integer pageSize = params.getFormIntegerOption(1);
        String uuid = params.getFormStringDefault(2, null);
        Integer teamType = params.getFormIntegerDefault(3, null);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", hostModifyRecordService.countHostMoveList(uuid, teamType));
        resultMap.put("list", hostModifyRecordService.getHostMoveList(uuid, teamType, pageNo, pageSize));
        return JsonResult.success(resultMap);
    }

    @RequestMapping(params = "method=getHostOrg")
    public JsonResult getHostOrg(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        Map<String, TeamHostVo> map = teamHostService.getHostOrgInfo(Lists.newArrayList(uuids));
        return JsonResult.success(map);
    }

    /**
     * 退出公会列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=quitGuildList")
    public JsonResult quitGuildList(RequestParams params) {
        Long orgId = params.getFormLongDefault(0, null);
        String hostUuid = params.getFormStringDefault(1, null);
        PageRequest pageRequest = new PageRequest(params.getFormIntegerDefault(2, 1), params.getFormIntegerDefault(3, 10));
        ApprovalFlow approvalFlow = new ApprovalFlow();
        approvalFlow.setHostUuid(hostUuid);
        approvalFlow.setOrgId(orgId);
        approvalFlow.setFlowType(FlowTypeEnum.QUIT_GUILD.getCode());
        PageInfo<ApprovalFlowItem> flowItemList = teamHostService.getQuitGuildPageList(approvalFlow, pageRequest);

        return JsonResult.success(flowItemList);
    }

    @RequestMapping(params = "method=quitGuildLog")
    public JsonResult quitGuildLog(RequestParams params) {
        Integer flowId = params.getFormIntegerDefault(0, 0);
        List<ApprovalFlowLogItem> itemList = teamHostService.getQuitGuildLog(flowId);

        return JsonResult.success(itemList);
    }
}
