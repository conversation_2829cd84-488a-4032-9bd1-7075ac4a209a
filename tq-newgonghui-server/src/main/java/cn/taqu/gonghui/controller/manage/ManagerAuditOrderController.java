package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.AuditItemStatusEnum;
import cn.taqu.gonghui.common.service.AuditOrderBizService;
import cn.taqu.gonghui.common.vo.req.AuditItemReq;
import cn.taqu.gonghui.config.annotation.SoaForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping(value = "/api", params = "service=managerAuditOrder")
@RestController
public class ManagerAuditOrderController {
    @Autowired
    private AuditOrderBizService auditOrderBizService;

    @RequestMapping(params = "method=accept")
    public JsonResult accept(@SoaForm AuditItemReq auditItemReq ) {
        auditItemReq.setStatus(AuditItemStatusEnum.ACCEPT.getCode());
        auditOrderBizService.accept4Manager(auditItemReq);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=reject")
    public JsonResult reject(@SoaForm AuditItemReq auditItemReq) {
        auditItemReq.setStatus(AuditItemStatusEnum.REJECT.getCode());
        auditOrderBizService.reject4Manager(auditItemReq);
        return JsonResult.success();
    }
}