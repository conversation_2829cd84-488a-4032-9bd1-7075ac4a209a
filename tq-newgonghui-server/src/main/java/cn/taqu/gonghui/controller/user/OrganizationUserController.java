package cn.taqu.gonghui.controller.user;

import cn.taqu.core.exception.ServiceException;
import cn.hutool.json.JSONUtil;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.Constants;
import cn.taqu.gonghui.common.constant.ModifyStatusEnum;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.service.FeiShuService;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ParamsCheckUtil;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.common.vo.OrganizationVo;
import cn.taqu.gonghui.system.entity.BusinessLicense;
import cn.taqu.gonghui.system.entity.ChargePerson;
import cn.taqu.gonghui.system.entity.LegalPerson;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.mapper.BusinessLicenseMapper;
import cn.taqu.gonghui.system.mapper.ChargePersonMapper;
import cn.taqu.gonghui.system.mapper.LegalPersonMapper;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.service.NoticeService;
import cn.taqu.gonghui.system.service.OrganizationLogService;
import cn.taqu.gonghui.system.service.OrganizationUserService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@RequestMapping(value = "/api", params = "service=userOrganization")
@RestController
@Slf4j
public class OrganizationUserController {

    @Autowired
    private OrganizationUserService organizationUserService;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private OrganizationLogService organizationLogService;
    @Autowired
    private LegalPersonMapper legalPersonMapper;
    @Autowired
    private ChargePersonMapper chargePersonMapper;
    @Autowired
    private FeiShuService feiShuService;
    @Autowired
    private BusinessLicenseMapper businessLicenseMapper;

    /**
     * 机构保存第一步
     * 20200518 需要对字段进行加密
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=saveOrgApplyStepOne")
    public JsonResult saveOrgApplyStepOne(RequestParams params) {
        log.info("机构保存第一步的请求参数为："+ JsonUtils.objectToString(params));
        //1.机构信息
        String orgName = params.getFormStringDefault(0,"");
        String chargePersonEmail = params.getFormStringDefault(1,"");
        String content = params.getFormStringDefault(2,"");
        Integer[] businessPermissions = params.getFormIntegerArray(3);
        OrganizationVo vo = new OrganizationVo();
        vo.setOrgName(orgName);
        vo.setChargePersonEmail(chargePersonEmail);
        vo.setContent(content);
        vo.setBusinessPermissions(Arrays.asList(businessPermissions));

        return JsonResult.success(organizationUserService.saveOrgApplyStepOne(vo));
    }


    @RequestMapping(params = "method=saveOrgApplyStepTwo")
    public JsonResult saveOrgApplyStepTwo(RequestParams params) {
        log.info("机构保存第二步的请求参数为："+ JsonUtils.objectToString(params));
        //2.账号信息
        String chargePerson = params.getFormString(0); //负责人姓名
        String chargePersonVx = params.getFormString(1); //负责人微信 new
        Long chargePersonBirthday = params.getFormLong(2); //负责人出生日期
        String chargePersonIdCard = params.getFormString(3); //负责人身份证 new
        String chargePersonUrl = params.getFormString(4); //负责人身份证照片 用,隔开
        String receivingAddress = params.getFormString(5); //收件地址
        String contactPhone = params.getFormStringDefault(6,""); //机构联系人电话
        OrganizationVo vo = new OrganizationVo();
        vo.setChargePerson(chargePerson);
        vo.setChargePersonVx(chargePersonVx);
        vo.setChargePersonBirthday(chargePersonBirthday);
        vo.setChargePersonIdCard(chargePersonIdCard);
        vo.setChargePersonUrl(chargePersonUrl);
        vo.setReceivingAddress(receivingAddress);
        vo.setContactPhone(contactPhone);
        return JsonResult.success(organizationUserService.saveOrgApplyStepTwo(vo));
    }

    @RequestMapping(params = "method=saveOrgApplyStepThree")
    public JsonResult saveOrgApplyStepThree(RequestParams params) {
        log.info("机构保存第三步的请求参数为："+ JsonUtils.objectToString(params));
        //3.公司信息
        String legalPerson = params.getFormString(0); //法人姓名
        String legalPersonIdCard = params.getFormString(1);//法人身份证号 new
        String legalPersonUrl = params.getFormString(2); //法人身份证
        String openingPermitUrl = params.getFormString(3); //开户许可
        String businessLicenseUrl = params.getFormString(4); //营业执照
        String socialUnifiedCreditCode = params.getFormString(5); //社会统一信用代码
        String enterpriseName = params.getFormString(6); //企业全名
        String legalPersonPhone = params.getFormString(7); //法人手机号
        String premises = params.getFormString(8); //经营场所

        OrganizationVo vo = new OrganizationVo();
        vo.setLegalPerson(legalPerson);
        vo.setLegalPersonIdCard(legalPersonIdCard);
        vo.setLegalPersonUrl(legalPersonUrl);
        vo.setOpeningPermitUrl(openingPermitUrl);
        vo.setBusinessLicenseUrl(businessLicenseUrl);
        vo.setSocialUnifiedCreditCode(socialUnifiedCreditCode);
        vo.setEnterpriseName(enterpriseName);
        vo.setLegalPersonPhone(legalPersonPhone);
        vo.setPremises(premises);
        vo.setRemitModifyStatus(Constants.NO_0);
        return JsonResult.success(organizationUserService.saveOrgApplyStepThree(vo));
    }

    @RequestMapping(params = "method=saveOrgApplyStepFour")
    public JsonResult saveOrgApplyStepFour(RequestParams params) {
        log.info("机构保存第四步的请求参数为："+ JsonUtils.objectToString(params));
        //4.银行信息
        String publicReceivingBankAccount = params.getFormString(0); //对公银行账号
        String accountName = params.getFormString(1); //开户名称
        String accountBankName = params.getFormString(2); //开户行名称
        String province = params.getFormString(3);
        Integer provinceId = params.getFormInteger(4);
        String city = params.getFormString(5);
        Integer cityId = params.getFormInteger(6);
        String subBranchName = params.getFormString(7);
        OrganizationVo vo = new OrganizationVo();
        vo.setPublicReceivingBankAccount(publicReceivingBankAccount);
        vo.setAccountName(accountName);
        vo.setAccountBankName(accountBankName);
        vo.setProvince(province);
        vo.setProvinceId(provinceId);
        vo.setCity(city);
        vo.setCityId(cityId);
        vo.setSubBranchName(subBranchName);
        vo.setRemitModifyStatus(Constants.NO_0);
        return JsonResult.success(organizationUserService.saveOrgApplyStepFour(vo));
    }

    @RequestMapping(params = "method=saveOrgApplyStepFive")
    public JsonResult saveOrgApplyStepFive(RequestParams params) {
        log.info("机构保存第五步的请求参数为："+ JsonUtils.objectToString(params));
        //5.其他信息
        String orgCooperationFlowUrl = params.getFormString(0);
        String hostScreenshotUrl = params.getFormString(1);

        OrganizationVo vo = new OrganizationVo();
        vo.setOrgCooperationFlowUrl(orgCooperationFlowUrl);
        vo.setHostScreenshotUrl(hostScreenshotUrl);
        return JsonResult.success(organizationUserService.saveOrgApplyStepFive(vo));
    }


    /**
     * 机构的所有信息(需要权限)
     * @returni
     */
    @RequestMapping(params = "method=getOrgApplyInfo")
    @PreAuthorize("@ss.hasPermi('userOrganization:getOrgApplyInfo')")
    public JsonResult getOrgApplyInfo() {
        Organization vo = organizationUserService.getOrgApplyInfo();
        return JsonResult.success(vo);
    }

    /**
     * 机构的所有信息(不需要权限)
     * @returni
     */
    @RequestMapping(params = "method=getOrgApplyInfoV2")
    public JsonResult getOrgApplyInfoV2() {
        Organization vo = organizationUserService.getOrgApplyInfo();
        return JsonResult.success(vo);
    }


    /**
     * 公告
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getNoticeListByType")
    public JsonResult getNoticeListByType(RequestParams params) {
        Integer noticeType = params.getFormInteger(0);
        return JsonResult.success(noticeService.getNoticeByNoticeType(noticeType));
    }

    /**
     * 获取机构的审核状态以及联系的qq
     *
     * @return
     */
    @RequestMapping(params = "method=getOrgInfoStatus")
    public JsonResult getOrgInfoStatus() {
        return JsonResult.success(organizationUserService.getOrgInfoStatus());
    }

    /**
     * 审核失败后,点击重新申请调用该接口,便于修改表单步骤
     */
    @RequestMapping(params = "method=changeFormStatus")
    public JsonResult changeFormStatus() {
        organizationUserService.changeForumStatus();
        return JsonResult.success("更改成功");
    }

    /**
     * 用户申请撤销机构的审核
     */
    @RequestMapping(params = "method=changeOrg")
    public JsonResult changeOrg(RequestParams params) {
        organizationUserService.changeOrg();
        return JsonResult.success("撤销申请成功");
    }

    /**
     * 修改机构简介
     * 只有机构管理员拥有权限
     */
    @RequestMapping(params = "method=changeContent")
    @PreAuthorize("@ss.hasPermi('userOrganization:changeContent')")
    public JsonResult changeContent(RequestParams params) {
        Long orgId = params.getFormLongOption(0);
        String content = params.getFormStringOption(1);
        organizationUserService.changeContent(orgId, content);
        return JsonResult.success("操作成功");
    }

    //2022.09.21

//    /**
//     * @param chargePerson       String chargePerson，必填 负责人姓名
//     * @param chargePersonPhone  String chargePersonPhone，必填 机构负责人电话（没有值传空）
//     * @param chargePersonVx     String chargePersonVx，必填 机构负责人的微信
//     * @param receivingAddress   String receivingAddress，必填 收件地址
//     * @param chargePersonEmail  String chargePersonEmail，必填 机构负责人邮箱
//     * @param chargePersonIdCard String chargePersonIdCard，必填 负责人身份证号
//     * @param chargePersonUrl    String chargePersonUrl，必填 负责人身份证照片 用,隔开
//     * @param orgId              String orgId，必填 orgId
//     * @param params
//     * @return
//     */
    @RequestMapping(params = "method=updateOrgInfo")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult updateOrgInfo(RequestParams params) {
        String chargePerson = params.getFormString(0);
        String contactPhone = params.getFormString(1);
        String chargePersonVx = params.getFormString(2);
        String receivingAddress = params.getFormString(3);
        String chargePersonEmail = params.getFormString(4);
        String chargePersonIdCard = params.getFormString(5);
        String chargePersonUrl = params.getFormString(6);
        Long orgId = params.getFormLong(7);


        if (!ParamsCheckUtil.isLegalPattern(chargePersonIdCard) && !org.apache.commons.lang3.StringUtils.isBlank(chargePersonIdCard)) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "身份证号码格式错误");
        }

        Organization organization = new Organization();
        organization.setOrgId(orgId);
        organization.setChargePerson(chargePerson);
        organization.setContactPhone(contactPhone);
        organization.setChargePersonVx(chargePersonVx);
        organization.setReceivingAddress(receivingAddress);
        organization.setChargePersonEmail(chargePersonEmail);
        organization.setChargePersonIdCard(chargePersonIdCard);
        organization.setChargePersonUrl(chargePersonUrl);

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            JsonResult.failed("用户登录信息失效");
        }

        Organization orgQuery = new Organization();
        orgQuery.setOrgId(organization.getOrgId());
        Organization oldOrganization = organizationMapper.getOrgInfo(orgQuery);

        organizationLogService.addLog(organization, oldOrganization, loginUser.getUser().getUserName());
        feiShuService.approvalModifyOrg(organization, oldOrganization.getBusinessPerson());
        organizationMapper.updateModifyStatusByOrgId(orgId, ModifyStatusEnum.SUBMIT.ordinal());
        return JsonResult.success();
    }

//    /**
//     * @param public_receiving_bank_account String public_receiving_bank_account，必填 对公银行账号
//     * @param accountName                   String accountName，必填 开户名称
//     * @param accountBankName               String accountBankName，必填 开户行
//     * @param province                      String province，必填 省
//     * @param provinceId                    Integer provinceId，必填 省对应的编号
//     * @param city                          String city，必填 市
//     * @param cityId                        Integer cityId，必填 市对应的编号
//     * @param subBranchName                 String subBranchName，必填 支行名称
//     * @param orgId                         String orgId，必填 orgId
//     * @param organization
//     * @return
//     */
    @RequestMapping(params = "method=updateFinancialInfo")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult updateFinancialInfo(RequestParams params) {
        String publicReceivingBankAccount = params.getFormString(0);
        String accountName = params.getFormString(1);
        String accountBankName = params.getFormString(2);
        String province = params.getFormString(3);
        Integer provinceId = params.getFormInteger(4);
        String city = params.getFormString(5);
        Integer cityId = params.getFormInteger(6);
        String subBranchName = params.getFormString(7);
        Long orgId = params.getFormLong(8);

        Organization organization = new Organization();
        organization.setPublicReceivingBankAccount(publicReceivingBankAccount);
        organization.setAccountName(accountName);
        organization.setAccountBankName(accountBankName);
        organization.setProvince(province);
        organization.setProvinceId(provinceId);
        organization.setCity(city);
        organization.setCityId(cityId);
        organization.setSubBranchName(subBranchName);
        organization.setOrgId(orgId);
        // 对公打款信息列表 标识处理中
        organization.setRemitModifyStatus(Constants.YES_1);


        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            JsonResult.failed("用户登录信息失效");
        }
        Organization orgQuery = new Organization();
        orgQuery.setOrgId(organization.getOrgId());
        Organization oldOrganization = organizationMapper.getOrgInfo(orgQuery);

        Organization updateOrg = new Organization();
        updateOrg.setOrgId(orgId);
        updateOrg.setModifyStatus(ModifyStatusEnum.SUBMIT.ordinal());
        updateOrg.setRemitModifyStatus(Constants.YES_1);

        organizationLogService.addLog(organization,oldOrganization,loginUser.getUser().getUserName());
        feiShuService.approvalFinancial(organization, oldOrganization.getBusinessPerson());
        organizationMapper.updateByPrimaryKeySelective(updateOrg);
        return JsonResult.success();
    }

    @RequestMapping(params = "method=updateCertInfo")
    @Transactional(rollbackFor = Exception.class)
    public JsonResult updateCertInfo(RequestParams params ) {
        String enterpriseName = params.getFormString(0);
        String legalPerson = params.getFormString(1);
        String legalPersonPhone = params.getFormString(2);
        String legalPersonIdCard = params.getFormString(3);
        String legalPersonUrl = params.getFormString(4);
        String businessLicenseUrl = params.getFormString(5);
        String socialUnifiedCreditCode = params.getFormString(6);
        String premises = params.getFormString(7);
        Long orgId = params.getFormLong(8);

        if (!ParamsCheckUtil.isLegalPattern(legalPersonIdCard) && !org.apache.commons.lang3.StringUtils.isBlank(legalPersonIdCard)) {
            throw new ServiceException(CodeStatus.APPLY_ORG_PARAM_ERROR.value(), "身份证号码格式错误");
        }

        Organization organization = new Organization();
        organization.setEnterpriseName(enterpriseName);
        organization.setLegalPerson(legalPerson);
        organization.setLegalPersonPhone(legalPersonPhone);
        organization.setLegalPersonIdCard(legalPersonIdCard);
        organization.setLegalPersonUrl(legalPersonUrl);
        organization.setBusinessLicenseUrl(businessLicenseUrl);
        organization.setSocialUnifiedCreditCode(socialUnifiedCreditCode);
        organization.setPremises(premises);
        organization.setOrgId(orgId);

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            JsonResult.failed("用户登录信息失效");
        }
        Organization orgQuery = new Organization();
        orgQuery.setOrgId(organization.getOrgId());
        Organization oldOrganization = organizationMapper.getOrgInfo(orgQuery);

        Organization updateOrg = new Organization();
        updateOrg.setOrgId(orgId);
        updateOrg.setModifyStatus(ModifyStatusEnum.SUBMIT.ordinal());
        // 判断企业名称是否有改变 影响打款信息 标记打款信息更改状态
        if (!enterpriseName.equals(oldOrganization.getEnterpriseName())) {
            organization.setRemitModifyStatus(Constants.YES_1);
            updateOrg.setRemitModifyStatus(Constants.YES_1);
        }

        organizationLogService.addLog(organization,oldOrganization,loginUser.getUser().getUserName());
        feiShuService.approvalCert(organization, oldOrganization.getBusinessPerson());
        organizationMapper.updateByPrimaryKeySelective(updateOrg);
        return JsonResult.success();
    }

}
