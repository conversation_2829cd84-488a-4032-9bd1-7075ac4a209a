package cn.taqu.gonghui.controller.manage;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.web.filter.annotation.CallbackApi;
import cn.taqu.gonghui.common.constant.Constants;
import cn.taqu.gonghui.common.constant.ModifyStatusEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.constant.callback.feishu.FeiShuRelationType;
import cn.taqu.gonghui.common.constant.callback.feishu.FeiShuStatusEnum;
import cn.taqu.gonghui.common.constant.callback.feishu.TimeLineTypeEnum;
import cn.taqu.gonghui.common.entity.OrgFlowLog;
import cn.taqu.gonghui.common.mapper.OrgFlowLogMapper;
import cn.taqu.gonghui.common.service.FeiShuService;
import cn.taqu.gonghui.common.vo.feishu.ApprovalInstanceInfo;
import cn.taqu.gonghui.common.vo.feishu.FeiShuCallbackVo;
import cn.taqu.gonghui.soa.SsomsService;
import cn.taqu.gonghui.system.config.FeishuConfig;
import cn.taqu.gonghui.system.entity.ChargePerson;
import cn.taqu.gonghui.system.entity.FeishuAuditRelation;
import cn.taqu.gonghui.system.entity.OrgCooperationFlow;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.mapper.ChargePersonMapper;
import cn.taqu.gonghui.system.mapper.FeishuAuditRelationMapper;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.search.SsoUserSearch;
import cn.taqu.gonghui.system.service.OrganizationLogService;
import cn.taqu.gonghui.system.service.OrganizationService;
import cn.taqu.gonghui.system.service.impl.QiNiuService;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Or;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.taqu.gonghui.common.constant.callback.feishu.FeiShuRecallTypeEnum.event_callback;
import static cn.taqu.gonghui.common.constant.callback.feishu.FeiShuRecallTypeEnum.url_verification;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/14 17:20
 **/
@RestController
@RequestMapping(value = "api", params = "service=callback")
@Slf4j
public class CallBackController {

    @Value("${sms.url}")
    private String smsUrl;
    @Autowired
    private FeishuAuditRelationMapper feishuAuditRelationMapper;
    @SoaReference(application = "ssoms", value = "ssoms")
    private SsomsService ssomsService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private FeiShuService feiShuService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private QiNiuService qiNiuService;
    @Autowired
    private OrganizationLogService organizationLogService;
    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private OrgFlowLogMapper orgFlowLogMapper;

    @CallbackApi
    @PostMapping(params = "method=feishuApprovalRecall")
    public JSONObject feishuApprovalRecall(@RequestBody Map<String, Object> paramMap) {
        log.info("收到飞书回调:{}", paramMap);
        FeiShuCallbackVo vo = JSONUtil.toBean(JSONUtil.toJsonStr(paramMap), FeiShuCallbackVo.class);
        // 校验请求直接返回challenge
        if (url_verification.name().equals(vo.getType())) {
            JSONObject object = new JSONObject();
            object.set("challenge", vo.getChallenge());
            return object;
        }
        if (event_callback.name().equals(vo.getType())) {
            // 历史原因 此处区分机构关闭类型 单独做处理
            //Integer businessType = getBusinessTypeByBackVO(vo);
            //if (businessType.equals(FeiShuRelationType.ORG_CLOSE_REVIEW.ordinal())) {
            //    JSONObject resObject = handleOrgCloseBack(vo);
            //    return resObject;
            //}

            JSONObject object = this.handleCallback(vo);
            if (object != null) {
                return object;
            }
        }

        return new JSONObject();
    }

    /**
     * 处理机构关闭响应对象
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public JSONObject handleOrgCloseBack(FeiShuCallbackVo vo) {
        FeiShuCallbackVo.Event event = vo.getEvent();
        // 更新关联表状态
        FeishuAuditRelation relation = feishuAuditRelationMapper.getByApproval(event.getApprovalCode(), event.getInstanceCode());
        if (relation == null) {
            return null;
        }
        relation.setStatus(event.getStatus());
        relation.setModifyTime(new Date());
        feishuAuditRelationMapper.updateById(relation);

        Organization orgInfo = organizationService.getOrgInfo(relation.getBusinessId());
        // 机构关闭日志
        OrgFlowLog orgFlowLog = new OrgFlowLog();
        orgFlowLog.setOrgId(orgInfo.getOrgId());
        orgFlowLog.setScene(Constants.ONE);
        String userTimeInfoStr = event.getUuid() + event.getOperateTime();
        orgFlowLog.setUserTimeInfo(userTimeInfoStr);
        orgFlowLog.setCreateUser(event.getUuid());
        orgFlowLog.setCreateTime(new Date());
        String remark = "";
        int result = 0;

        if (FeiShuStatusEnum.REJECTED.name().equals(event.getStatus())) {
            remark = "审核拒绝";
            result = Constants.TWO;
        } else if (FeiShuStatusEnum.APPROVED.name().equals(event.getStatus())) {
            remark = "审核通过";
            result = Constants.ONE;
            organizationService.orgClose(orgInfo.getOrgId(), null, null);
        } else if (FeiShuStatusEnum.PENDING.name().equals(event.getStatus())) {
            remark = "审核通过";
            result = Constants.ONE;
        }
        orgFlowLog.setRemark(remark);
        orgFlowLog.setResult(result);
        orgFlowLog.setModifyTime(new Date());

        orgFlowLogMapper.insert(orgFlowLog);

        return null;
    }

    /**
     * 获取bussinesstype
     * @return
     */
    private Integer getBusinessTypeByBackVO(FeiShuCallbackVo vo) {
        FeiShuCallbackVo.Event event = vo.getEvent();
        // 更新关联表状态
        FeishuAuditRelation relation = feishuAuditRelationMapper.getByApproval(event.getApprovalCode(), event.getInstanceCode());
        if (relation == null) {
            return null;
        }
        return relation.getBusinessType();
    }

    private JSONObject handleCallback(FeiShuCallbackVo vo) {
        FeiShuCallbackVo.Event event = vo.getEvent();
        // 更新关联表状态
        FeishuAuditRelation relation = feishuAuditRelationMapper.getByApproval(event.getApprovalCode(), event.getInstanceCode());
        if (relation == null) {
            return null;
        }
        relation.setStatus(event.getStatus());
        relation.setModifyTime(new Date());
        feishuAuditRelationMapper.updateById(relation);
        Organization orgInfo = organizationService.getOrgInfo(relation.getBusinessId());
        if (orgInfo.getModifyStatus() == ModifyStatusEnum.SUCCESS.ordinal() || orgInfo.getModifyStatus() == ModifyStatusEnum.FAIL.ordinal()) {
            log.info("机构修改状态为：{}，不执行后续流程", orgInfo.getModifyStatus());
            return null;
        }
        // 更新机构表状态
        if (FeiShuStatusEnum.REJECTED.name().equals(event.getStatus())) {
            // 获取拒绝信息
            String msg = this.getRejectMsg(event);
            // 调用拒绝接口
            if (relation.getBusinessType() == FeiShuRelationType.ADD_ORG.ordinal()) {
                organizationService.rejectOrg(msg, orgInfo);
            } else {
                // 修改信息只要发送拒绝理由就可以了，也没什么信息可以更新
                organizationService.sendMsg(orgInfo, msg);
                // 删掉最后一条变更记录
                organizationLogService.clearModifyLog(orgInfo, relation);
            }
            Organization organization = new Organization();
            organization.setOrgId(orgInfo.getOrgId());
            organization.setModifyStatus(ModifyStatusEnum.FAIL.ordinal());
            organization.setRemitModifyStatus(Constants.ZERO);
            organizationMapper.updateByPrimaryKeySelective(organization);
            //organizationMapper.updateModifyStatusByOrgId(orgInfo.getOrgId(), ModifyStatusEnum.FAIL.ordinal());
        } else if (FeiShuStatusEnum.APPROVED.name().equals(event.getStatus())) {
            log.info("instanceCode={}，审核成功", event.getInstanceCode());
            // 判断修改了什么
            if (relation.getBusinessType() == FeiShuRelationType.ADD_ORG.ordinal()) {
                // 新增的走通过流程
                organizationService.passOrg(orgInfo, relation);
            } else {
                // 修改的发个短信
                String msg = "";
                if (relation.getBusinessType() == FeiShuRelationType.MODIFY_ORG.ordinal()) {
                    // 修改机构信息
                    msg = "尊敬的「" + orgInfo.getOrgName() + "」，您修改的机构信息已审核通过，请登录公会系统查看，登录地址为:" + smsUrl;
                } else if (relation.getBusinessType() == FeiShuRelationType.MODIFY_CERT.ordinal()) {
                    msg = "尊敬的「" + orgInfo.getOrgName() + "」，您修改的资质信息已审核通过，请登录公会系统查看，登录地址为:" + smsUrl;
                } else if (relation.getBusinessType() == FeiShuRelationType.MODIFY_FINANCIAL.ordinal()) {
                    msg = "尊敬的「" + orgInfo.getOrgName() + "」，您修改的财税信息已审核通过，请登录公会系统查看，登录地址为:" + smsUrl;
                }
                organizationLogService.getModifyInfo(orgInfo, relation);
                // 发送消息
                organizationService.sendPassMsg(orgInfo, msg, "gonghui_modify_notice");
            }
            Organization organization = new Organization();
            organization.setOrgId(orgInfo.getOrgId());
            organization.setModifyStatus(ModifyStatusEnum.SUCCESS.ordinal());
            organization.setRemitModifyStatus(Constants.ZERO);
            organizationMapper.updateByPrimaryKeySelective(organization);
            //organizationMapper.updateModifyStatusByOrgId(orgInfo.getOrgId(), ModifyStatusEnum.SUCCESS.ordinal());
        } else if (FeiShuStatusEnum.PENDING.name().equals(event.getStatus())) {
            // 创建成功发送短信
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("org_id", orgInfo.getOrgId());
            queryWrapper.eq("user_type", UserTypeEnum.MANAGER.getType());
            SysUser sysUser = sysUserMapper.selectOne(queryWrapper);
            if (sysUser != null) {
                Map<String, Object> asyncforms = new HashMap<>();
                asyncforms.put("phone", sysUser.getMobile());
                String content = "您修改的机构信息正在审核中，请耐心等待";
                if (relation.getBusinessType() == FeiShuRelationType.ADD_ORG.ordinal()) {
                    content = "您提交的机构信息正在审核中，请耐心等待";
                }
                asyncforms.put("content", content);
                asyncforms.put("tagCode", "gonghui_modify_notice");
                asyncforms.put("appcode", 1);
                asyncforms.put("cloned", 1);
                Map<String, Object> map = new HashMap<>();
                map.put("service", "businessSms");
                map.put("method", "processBusinessSms");
                map.put("asyncforms", asyncforms);
                MqClient mqClient = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ);
                mqClient.push("mp_sms_async_invoke_queue", map, null);
            }
            organizationMapper.updateModifyStatusByOrgId(orgInfo.getOrgId(), ModifyStatusEnum.AUDITING.ordinal());
        }
        return null;
    }

    private String getRejectMsg(FeiShuCallbackVo.Event event) {
        String msg = "";
        ApprovalInstanceInfo instanceInfo = feiShuService.getApprovalInstanceInfo(event.getInstanceCode());
        if (instanceInfo != null) {
            List<ApprovalInstanceInfo.Timeline> timelineList = instanceInfo.getTimeline();
            if (CollectionUtil.isNotEmpty(timelineList)) {
                // 遍历时间线，找到拒绝理由
                for (ApprovalInstanceInfo.Timeline t : timelineList) {
                    if (!t.getType().equals(TimeLineTypeEnum.REJECT.name())) {
                        continue;
                    }
                    msg = t.getComment();
                }
            }
        }
        return msg;
    }

    @CallbackApi
    @PostMapping(params = "method=ssotest")
    public JSONObject ssotest(@RequestBody SsoUserSearch search) {
        JSONArray array = ssomsService.searchEffectiveSsoUsersV2(search);
        log.info("array={}", array.toJSONString());
        return new JSONObject();
    }

    @CallbackApi
    @PostMapping(params = "method=feishuTest")
    public JSONObject feishuTest(@RequestBody Map<String, Object> params) {
        int type = Integer.parseInt(params.get("type").toString());
        Long orgId = Long.parseLong(params.get("orgId").toString());
        Organization orgInfo = organizationService.getOrgInfo(orgId);
        if (orgInfo == null) {
            return new JSONObject();
        }
        String code = null;
        if (type == 1) {
            code = feiShuService.approvalAdd(orgInfo, "zxlsh");
        } else if (type == 2) {
            code = feiShuService.approvalModifyOrg(orgInfo, "zxlsh");
        } else if (type == 3) {
            code = feiShuService.approvalFinancial(orgInfo, "zxlsh");
        } else if (type == 4) {
            code = feiShuService.approvalCert(orgInfo, "zxlsh");
        }
        JSONObject object = new JSONObject();
        object.set("code", code);
        return object;
    }

    @CallbackApi
    @PostMapping(params = "method=getApprovalInstance")
    public JSONObject getApprovalInstance(@RequestBody Map<String, Object> map) {
        String instanceCode = map.get("code").toString();
        ApprovalInstanceInfo info = feiShuService.getApprovalInstanceInfo(instanceCode);
        return JSONUtil.parseObj(info);
    }
}
