package cn.taqu.gonghui.controller.user;

import cn.taqu.core.orm.PageSearch;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.service.LiveRecommendApplyService;
import cn.taqu.gonghui.system.vo.LiveRecommendApplyVo;
import cn.taqu.gonghui.system.vo.PageDataVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by gjj on 2021/06/01.
 * 推荐位管理
 */
@RequestMapping(value = "api", params = "service=recommendApply")
@RestController
public class LiveRecommendApplyController {

    @Autowired
    private LiveRecommendApplyService liveRecommendApplyService;

    /**
     * 获取推荐位申请记录
     *
     * @param params
     * @param search
     * @return
     */
    @RequestMapping(params = "method=getApplyList")
    @PreAuthorize("@ss.hasPermi('recommendApply:getApplyList')")
    public JsonResult getApplyList(RequestParams params, PageSearch search) {
        String date = params.getFormStringDefault(0, null);//申请日期
        String hostUuid = params.getFormStringDefault(1, null);//主播uuid
        String nickname = params.getFormStringDefault(2, null); //昵称
        Integer status = params.getFormIntegerDefault(3, null);//审批状态 0-全部，1-待审核，2-通过，-1-拒绝，-2-撤销
        String location = params.getFormStringDefault(4, null);//推荐位
        Integer type = params.getFormIntegerDefault(5,1);
        Integer page = params.getFormIntegerDefault(6, 1);//页数
        Integer pageSize = params.getFormIntegerDefault(7, 20);//每页数量
        Long teamId = params.getFormLongOption(8);
        PageDataVo<LiveRecommendApplyVo> applyList = liveRecommendApplyService.getApplyList(teamId,date, hostUuid, nickname, status, location, page, pageSize,type);
        return JsonResult.success(applyList);
    }

    /**
     * 根据id撤销推荐位申请
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=cancelApplyById")
    @PreAuthorize("@ss.hasPermi('recommendApply:cancelApplyById')")
    public JsonResult cancelApplyById(RequestParams params) {
        Long id = params.getFormLong(0);
        liveRecommendApplyService.cancelApplyById(id);
        return JsonResult.success();
    }

    /**
     * 获取推荐位列表
     *
     * @return
     */
    @RequestMapping(params = "method=getRecommendList")
    @PreAuthorize("@ss.hasPermi('recommendApply:getRecommendList')")
    public JsonResult getRecommendList(RequestParams params) {
        String hostUuid = params.getFormString(0);
        return JsonResult.success(liveRecommendApplyService.getRecommendList(hostUuid));
    }

    /**
     * 推荐位申请aa
     *
     * @return
     */
    @RequestMapping(params = "method=saveRecommendApply")
    @PreAuthorize("@ss.hasPermi('recommendApply:saveRecommendApply')")
    public JsonResult saveRecommendApply(RequestParams params) {
        String hostUuid = params.getFormStringOption(0);
        String date = params.getFormStringOption(1);
        String location = params.getFormStringOption(2);
        String time = params.getFormStringOption(3);
        if (StringUtils.isBlank(hostUuid)) {
            return JsonResult.failed("请选择主播");
        }
        if (StringUtils.isBlank(hostUuid)) {
            return JsonResult.failed("请选择日期");
        }
        if (StringUtils.isBlank(location)) {
            return JsonResult.failed("请选择位置");
        }
        if (StringUtils.isBlank(hostUuid)) {
            return JsonResult.failed("请输入时间");
        }
        try {
            liveRecommendApplyService.saveRecommendApply(hostUuid, date, location, time);
        } catch (Exception e){
            return JsonResult.failed(e.getMessage());
        }
        return JsonResult.success("操作成功");
    }
}
