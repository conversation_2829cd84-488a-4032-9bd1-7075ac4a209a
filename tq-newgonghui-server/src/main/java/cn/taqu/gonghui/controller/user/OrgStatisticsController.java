package cn.taqu.gonghui.controller.user;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.search.LivePerDayStatisticSearch;
import cn.taqu.gonghui.system.service.OrgStatisticsService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 主播数据统计
 */
@RequestMapping(value = "/api", params = "service=orgStatistic")
@RestController
public class OrgStatisticsController {

    @Autowired
    private OrgStatisticsService orgStatisticsService;

    /**
     * 2021-03-25
     * 新增主播统计需求
     * 计算新增主播的各种数据
     * 由php提供
     *
     * @return
     */
    @RequestMapping(params = "method=getConsortiaNewHostStat")
    @PreAuthorize("@ss.hasPermi('orgStatistic:getConsortiaNewHostStat')")
    public JsonResult getConsortiaNewHostStat(RequestParams params) {
        String employeeId = params.getFormStringOption(0);
        String teamId = params.getFormStringOption(1);
        String date = params.getFormStringOption(2);
        String orderType = params.getFormStringOption(3);
        String sortType = params.getFormStringOption(4);
        Integer export = params.getFormIntegerDefault(5, 0);
        String applyLevel = params.getFormStringOption(6);
        String liveNo = params.getFormStringOption(7);
        Integer page = params.getFormIntegerDefault(8, 1);
        Integer pageSize = params.getFormIntegerDefault(9, 10);
        Integer type = params.getFormInteger(10);
        if (StringUtils.isBlank(date)) {
            return JsonResult.failed("月份不能为空");
        }
        return JsonResult.success(orgStatisticsService.getLiveHostStatisticNew(employeeId, date, orderType, sortType, export, teamId, applyLevel, liveNo, page, pageSize,type));
    }

    /**
     * 用户端--直播数据
     * @param params
     * @return
     */
    @RequestMapping(params = "method=findConsortiaList")
    @PreAuthorize("@ss.hasPermi('orgStatistic:findConsortiaList')")
    public JsonResult findConsortiaList(RequestParams params) {
        String startTime = params.getFormStringOption(0);
        String endTime = params.getFormStringOption(1);
        String hostUuid = params.getFormStringOption(2);
        Long teamId = params.getFormLong(3);
        //20201009第六期需求,新增通过经纪人搜索主播
        String agentManageUuid = params.getFormStringOption(4);
        String applyLevel = params.getFormStringOption(5);
        String liveStatus = params.getFormStringOption(6);
        Integer page = params.getFormInteger(7);
        Integer pageSize = params.getFormInteger(8);
        String orderType = params.getFormStringOption(9);
        String sortType = params.getFormStringOption(10);
        //将筛选条件放入对象中
        LivePerDayStatisticSearch search = new LivePerDayStatisticSearch();
        search.setPage(page);
        search.setRows(pageSize);
        search.setStart(startTime);
        search.setEnd(endTime);
        search.setHost_uuid(hostUuid);
        search.setApply_level(applyLevel);
        search.setLive_status(liveStatus);
        search.setAgentManageUuid(agentManageUuid);
        search.setTeamId(teamId);
        search.setOrder_type(orderType);
        search.setSort_type(sortType);
        return JsonResult.success(orgStatisticsService.getOrgStatisticsByUser(search));
    }

    /**
     * 用户端--直播每日数据
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=findConsortiaPerDayList")
    @PreAuthorize("@ss.hasPermi('orgStatistic:findConsortiaPerDayList')")
    public JsonResult findConsortiaPerDayList(RequestParams params) {
        String paramJson = params.getFormStringOption(0);
        LivePerDayStatisticSearch search = JSON.parseObject(paramJson, new TypeReference<LivePerDayStatisticSearch>() {
        });
        return JsonResult.success(orgStatisticsService.getOrgDailyStatisticsByUser(search));
    }
}
