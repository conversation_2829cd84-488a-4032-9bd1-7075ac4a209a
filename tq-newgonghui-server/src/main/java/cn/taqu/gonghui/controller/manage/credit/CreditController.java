package cn.taqu.gonghui.controller.manage.credit;

import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.system.entity.CreditGradeLog;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.mapper.CreditGradeLogMapper;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api", params = "service=credit")
@Slf4j
public class CreditController {

    @Autowired
    private CreditGradeLogMapper creditGradeLogMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private TokenService tokenService;


    @RequestMapping(params = "method=creditPage")
    public JsonResult creditPage(RequestParams params) {
        Integer pageNum = params.getFormIntegerOption(0);
        Integer pageSize = params.getFormIntegerOption(1);
        String org_id = params.getFormStringOption(2);
        log.info("credit请求参数pageNum={},pageSize={},org_id={}", pageNum, pageSize, org_id);

        PageHelper.startPage(pageNum, pageSize);
        QueryWrapper<CreditGradeLog> creditGradeLogQueryWrapper = new QueryWrapper<>();
        creditGradeLogQueryWrapper.eq("org_id",org_id);
        creditGradeLogQueryWrapper.orderByDesc("modify_time");
        List<CreditGradeLog>  creditGradeLogList = creditGradeLogMapper.selectList(creditGradeLogQueryWrapper);
        PageHelper.clearPage();

        return JsonResult.success(new PageInfo<>(creditGradeLogList));
    }

    @RequestMapping(params = "method=getCreditGrade")
    public JsonResult getCreditGrade(RequestParams params) {

        String org_id = params.getFormStringOption(0);
        log.info("getCreditGrade请求参数org_id={}", org_id);


        QueryWrapper<CreditGradeLog> creditGradeLogQueryWrapper = new QueryWrapper<>();
        creditGradeLogQueryWrapper.eq("org_id",org_id);
        creditGradeLogQueryWrapper.orderByDesc("modify_time");
        List<CreditGradeLog>  creditGradeLogList = creditGradeLogMapper.selectList(creditGradeLogQueryWrapper);
        CreditGradeLog creditGradeLog = null;
        if(!creditGradeLogList.isEmpty()){
            creditGradeLog = creditGradeLogList.get(0);
        }
        return JsonResult.success(creditGradeLog);
    }

    @RequestMapping(params = "method=updateCreditGrade")
    public JsonResult updateCreditGrade(RequestParams params) {
        String orgId = params.getFormStringOption(0);
        Integer creditGrad = params.getFormInteger(1);
        String reason = params.getFormString(2);

        log.info("credit请求参数orgId={},creditGrad={},reason={}", orgId, creditGrad,reason);
        String userName = SoaBaseParams.fromThread().getToken();
        log.info("user={}", userName);

        QueryWrapper<Organization> organizationQueryWrapper = new QueryWrapper<>();
        organizationQueryWrapper.eq("org_id",orgId);

        Organization organization =  organizationMapper.selectByPrimaryKey(Long.valueOf(orgId));

        organization.setCreditGrade(creditGrad);
        organizationMapper.updateByPrimaryKeySelective(organization);
        CreditGradeLog creditGradeLog = new CreditGradeLog();
        creditGradeLog.setOrgId(orgId);
        creditGradeLog.setReason(reason);
        creditGradeLog.setCreditGrade(creditGrad);

        creditGradeLog.setOperationName(userName);
        creditGradeLogMapper.insert(creditGradeLog);

        return JsonResult.success("修改信用分成功");
    }





}
