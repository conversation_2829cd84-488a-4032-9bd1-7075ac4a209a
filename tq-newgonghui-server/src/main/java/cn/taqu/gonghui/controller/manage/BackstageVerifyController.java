package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.service.VerifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-11 16:52
 */
@RestController
@RequestMapping(value = "/api", params = "service=backstageVerify")
public class BackstageVerifyController {

    @Autowired
    private VerifyService verifyService;

    /**
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getCaptcha")
    public JsonResult getCaptch(RequestParams params){
        Long time = params.getFormLongDefault(0, null);
        Map<String, String> result = verifyService.getCaptcha(time);
        return JsonResult.success(result);
    }

}
