package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.dto.SharingProfitRecordDto;
import cn.taqu.gonghui.system.search.SharingProfitRecordSearch;
import cn.taqu.gonghui.system.service.HostSharingProfitRecordService;
import cn.taqu.gonghui.system.vo.HostSharingProfitRecordVo;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 调整分润比例记录控制器
 * @user guojiajun
 */
@RestController
@RequestMapping(value = "/api", params = "service=ManageSharingProfitRecord")
public class ManageSharingProfitRecordController {

    @Autowired
    private HostSharingProfitRecordService sharingProfitRecordService;

    /**
     * 主播分润比例确认结果
     * 给php回调
     * @param params
     * @return
     */
    @RequestMapping(params = "method=hostConfirmSharingRate")
    public JsonResult hostConfirmSharingRate(RequestParams params) {
        Long id = params.getFormLongOption(0);
        Integer status = params.getFormIntegerOption(1);
        String hostUuid = params.getFormStringOption(2);
        SharingProfitRecordDto dto = new SharingProfitRecordDto();
        dto.setId(id);
        dto.setStatus(status);
        dto.setHostUuid(hostUuid);
        return JsonResult.success(sharingProfitRecordService.hostConfirmSharingRate(dto));
    }

    /**
     * 管理端获取分润比例调整记录
     */
    @RequestMapping(params = "method=list")
    public JsonResult list(RequestParams params){
        String formString = params.getFormString(0);
        SharingProfitRecordSearch search = JsonUtils.stringToObject2(formString, SharingProfitRecordSearch.class);
        List<HostSharingProfitRecordVo> voList =  sharingProfitRecordService.manageList(search);
        return JsonResult.success(new PageInfo<>(voList));
    }

    /**
     * 返回分润比例详情
     */
    @RequestMapping(params = "method=detail")
    public JsonResult detail(RequestParams params){
        Long id = params.getFormLongOption(0);
        String hostUuid = params.getFormStringOption(1);
        return JsonResult.success(sharingProfitRecordService.detail(id,hostUuid));
    }

}
