package cn.taqu.gonghui.controller;

import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.Encodes;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

@RequestMapping(path = "/api")
@RestController
@Slf4j
public class ApiController {

    @Autowired
    @Qualifier("localRestTemplate")
    private RestTemplate localRestTemplate;
    @Autowired
    private TokenService tokenService;

    @PostMapping("/rest")
    public JsonResult restExecute(
            @RequestParam("method") String method,
            @RequestParam("service") String service,
            @RequestBody String requestBody
    ) {

        log.info("soaExecute exec service {} method {} ", method, service);
        Object[] form = new Object[]{requestBody};
        String url = "/api?service={service}&method={method}";

        String authToken = tokenService.getToken(ServletUtils.getRequest());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        if (StringUtils.isNotBlank(authToken)) {
            headers.add("Authorization", "Bearer " + authToken);
        }
        LinkedMultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<>();
        paramMap.add("form", Encodes.encodeBase64(Encodes.decodeCharset(JsonUtils.objectToString(form), "UTF-8")));
        HttpEntity<LinkedMultiValueMap<String, Object>> httpEntity = new HttpEntity<>(paramMap, headers);

        ResponseEntity<JsonResult> responseEntity = localRestTemplate.postForEntity(url, httpEntity, JsonResult.class,
                ImmutableMap.of("service", service, "method", method)
        );
        return responseEntity.getBody();
    }


}
