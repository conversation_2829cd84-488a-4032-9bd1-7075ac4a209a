package cn.taqu.gonghui.controller.user;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.search.TeamEmployeeSearch;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
@RestController
@RequestMapping(value = "api", params = "service=teamEmployee")
public class TeamEmployeeController {

    @Autowired
    private TeamEmployeeService teamEmployeeService;

    /**
     * 成员列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getList")
    @PreAuthorize("@ss.hasPermi('teamEmployee:getList')")
    public JsonResult list(RequestParams params) {
        String formStr = params.getFormStringOption(0);
        Integer pageNo = params.getFormIntegerDefault(1,1);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        TeamEmployeeSearch search = JsonUtils.stringToObject(formStr, TeamEmployeeSearch.class);
        return JsonResult.success(teamEmployeeService.selectTeamEmployeePageForUser(search,pageNo,pageSize));
    }


    /**
     * 添加成员
     * @param params
     * @return
     */
    @RequestMapping(params = "method=addMember")
    @PreAuthorize("@ss.hasPermi('teamEmployee:addMember')")
    public JsonResult addMember(RequestParams params) {
        String mobile = params.getFormStringDefault(0, "");
        String verify = params.getFormStringDefault(1, "");
        String userName = params.getFormStringDefault(2,"");
        Long teamId = params.getFormLong(3);
        Long roleId = params.getFormLong(4);
        Integer businessType = params.getFormIntegerDefault(5,null);
        teamEmployeeService.saveMember(mobile,verify,userName,teamId,roleId,businessType);
        return JsonResult.success();
    }


    /**
     * 编辑成员信息
     * @param params
     * @return
     */
    @RequestMapping(params = "method=editMember")
    @PreAuthorize("@ss.hasPermi('teamEmployee:editMember')")
    public JsonResult editMember(RequestParams params) {
        Long id = params.getFormLong(0);
        String employeeName = params.getFormString(1);
        teamEmployeeService.updateName(id,employeeName);
        return JsonResult.success();
    }

    /**
     * 获取详情
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getMemberById")
    @PreAuthorize("@ss.hasPermi('teamEmployee:getMemberById')")
    public JsonResult getMemberById(RequestParams params) {
        Long id = params.getFormLong(0);
        return JsonResult.success(teamEmployeeService.getMemberById(id));
    }

    /**
     * 根据teamId获取经纪人下拉
     * @return
     */
    @RequestMapping(params = "method=tree")
    public JsonResult tree(RequestParams params){
        Long teamId = params.getFormLong(0);
        return JsonResult.success(teamEmployeeService.getAgenterTree(teamId));
    }


    /**
     * 获取当前用户经纪人下拉tree
     */
    @RequestMapping(params = "method=treeByAccountUuid")
    public JsonResult treeByAccountUuid(RequestParams params){
        Integer type = params.getFormInteger(0);
        return JsonResult.success(teamEmployeeService.getAgenterTreeByAccountUuid(type));
    }


    /**
     * 经纪人离职
     * @return
     */
    @RequestMapping(params = "method=agentDeparture")
    @PreAuthorize("@ss.hasPermi('teamEmployee:agentDeparture')")
    public JsonResult agentDeparture(RequestParams params){
        //离职人员的成员id
        Long employeeId = params.getFormLong(0);
        teamEmployeeService.agentDeparture(employeeId);
        return JsonResult.success("该成员离职成功");
    }


    /**
     * 获取当前登录人的经纪人列表
     * @return
     */
    @RequestMapping(params = "method=getAgenter")
    public JsonResult getAgenter(RequestParams params){
        Integer type = params.getFormInteger(0);
        return JsonResult.success(teamEmployeeService.getAgenterForLogin(type));
    }


}
