package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.ApplyStatusEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.VerifyCodeEnum;
import cn.taqu.gonghui.common.entity.OrgFlowLog;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.service.VerifyService;
import cn.taqu.gonghui.common.utils.RedisUtil;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.common.vo.OrganizationVo;
import cn.taqu.gonghui.soa.SsomsService;
import cn.taqu.gonghui.soa.TqmqService;
import cn.taqu.gonghui.system.dto.TeamDto;
import cn.taqu.gonghui.system.entity.ApplyTypeLog;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.mapper.ApplyTypeLogMapper;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.mapper.TeamMapper;
import cn.taqu.gonghui.system.search.OrganizationInfoSearch;
import cn.taqu.gonghui.system.search.SsoUserSearch;
import cn.taqu.gonghui.system.service.OrganizationService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.vo.SsoUserCombobox;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping(value = "api", params = "service=manageOrganization")
@Slf4j
public class ManageOrganizationController {

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private RedisUtil redisUtil;
    @SoaReference(application = "ssoms", value = "ssoms")
    private SsomsService ssomsService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private ApplyTypeLogMapper applyTypeLogMapper;
    @Value("${contact.department}")
    private String department;
    @SoaReference("tqmq")
    private TqmqService tqmqService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private TeamMapper teamMapper;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @EtcdValue("biz.whiteList.org")
    public static String orgWhiteList;

    /**
     * 待审核机构列表(待审核列表,所有的记录都在这儿)
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=findOrgInfoPageList")
    public JsonResult findGuildInfoPageList(RequestParams params) {
        String paramJson = params.getFormStringOption(0);
        Integer page = params.getFormInteger(1);
        Integer pageSize = params.getFormInteger(2);
        OrganizationInfoSearch search;
        search = JSON.parseObject(paramJson, new TypeReference<OrganizationInfoSearch>() {
        });
        return JsonResult.success(organizationService.findOrgInfoPageList(search, page, pageSize));
    }

    /**
     * 审核成功的机构(只有通过的机构在这)
     */
    @RequestMapping(params = "method=findPassPageList")
    public JsonResult findPassPageList(RequestParams params) {
        String paramJson = params.getFormStringOption(0);
        Integer page = params.getFormInteger(1);
        Integer pageSize = params.getFormInteger(2);
        OrganizationInfoSearch search = JSON.parseObject(paramJson, new TypeReference<OrganizationInfoSearch>() {
        });
        return JsonResult.success(organizationService.findPassOrgInfoPageList(search, page, pageSize));
    }


    /**
     * 查看机构详情
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getOrgInfo")
    public JsonResult getOrgInfo(RequestParams params) {
        Long id = params.getFormLong(0);
        OrganizationInfoSearch search = new OrganizationInfoSearch();
        search.setOrgId(id);
        return JsonResult.success(organizationService.getOrgInfo(search.getOrgId()));
    }


    /**
     * 机构审核
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=auditOrgApply")
    public JsonResult auditOrgApply(RequestParams params) {
        Long id = params.getFormLong(0);
        Integer status = params.getFormInteger(1);
        String auditMsg = params.getFormStringOption(2);
        String operator = params.getFormStringOption(3);
        String operationSpecialistId = params.getFormStringOption(4);
        Integer settlementeType = params.getFormIntegerOption(5);
        Integer[] businessPermissions = params.getFormIntegerArray(6);
        if (ApplyStatusEnum.SUCCESS.getValue() != status) {
            settlementeType = 1;
        }
        organizationService.changeApplyStatus(id, status, auditMsg, operator, operationSpecialistId, settlementeType, businessPermissions);
        return JsonResult.success("审核流程已完毕");
    }

    /**
     * 修改机构信息
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=updateOrgInfo")
    public JsonResult updateOrgInfo(RequestParams params) {
        String paramJson = params.getFormString(0);
        String operator = params.getFormString(1);
        organizationService.updateOrgInfo(paramJson, operator);
        return JsonResult.success("信息修改成功");
    }


    /**
     * 解散机构  暂且预留
     */
//    @RequestMapping(params = "method=changeOrgStatus")
//    public JsonResult changeOrgStatus(RequestParams params) {
//        Long id = params.getFormLong(0);
//        String operator = params.getFormString(1);
//        organizationService.changeOrgStatus(id, operator);
//        return JsonResult.success("更改成功");
//    }

    /**
     * 更改机构会长手机号绑定
     * 输入手机号+验证码就可以换绑
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=rebindMobile")
    public JsonResult rebindMobile(RequestParams params) {
        String orgId = params.getFormString(0);
        String mobile = params.getFormString(1);
        String vCode = params.getFormString(2);
        String operator = params.getFormString(3);
        organizationService.bindMobile(orgId, mobile, vCode, operator);
        return JsonResult.success("绑定手机号成功");
    }

    /**
     * 更改机构会长手机号绑定
     * 输入手机号+验证码就可以换绑
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=rebindMobile2")
    public JsonResult rebindMobile2(RequestParams params) {
        String orgId = params.getFormString(0);
        String mobile = params.getFormString(1);
        String vCode = params.getFormString(2);
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        String operator = params.getFormStringDefault(3,user.getUserName());
        String oldMobile = params.getFormString(4);
        log.info("rebindMobile2方法,orgId={},mobile={},vCode={},operator={},oldMobile={}",orgId,mobile,vCode,operator,oldMobile);
        organizationService.bindMobile2(orgId, mobile, vCode, operator,oldMobile);
        return JsonResult.success("绑定手机号成功了");
    }

    /**
     * 换绑超管请求验证码
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=sendVcode")
    public JsonResult sendVcode(RequestParams params) {
        String mobile = params.getFormString(0);
        Boolean isSend = redisUtil.set(mobile, "send", 60L, TimeUnit.SECONDS);
        if (!isSend) {
            return JsonResult.failed("发送频率过高,请稍等后再尝试");
        }
        verifyService.sendVCode(VerifyCodeEnum.REBIND_ORG.getCode(), mobile);
        return JsonResult.success("短信验证码发送成功");
    }

    /**
     * 后台创建机构请求验证码
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=sendVcodeBindOrg")
    public JsonResult sendVcodeBindOrg(RequestParams params) {
        String mobile = params.getFormString(0);
        Boolean isSend = redisUtil.set(mobile, "send", 60L, TimeUnit.SECONDS);
        if (!isSend) {
            return JsonResult.failed("发送频率过高,请稍等后再尝试");
        }
        verifyService.sendVCode(VerifyCodeEnum.BIND_ORG.getCode(), mobile);
        return JsonResult.success("短信验证码发送成功");
    }

    /**
     * 所有审核通过的工会
     *
     * @return
     */
    @RequestMapping(params = "method=findPassList")
    public JsonResult findPassList() {
        return JsonResult.success(organizationService.findPassList());
    }


    /**
     * 获取所有机构列表
     *
     * @return
     */
    @RequestMapping(params = "method=findOrgInfoList")
    public JsonResult findOrgInfoList() {
        return JsonResult.success(organizationService.findGuildInfoUuidAndNameList());
    }

    /**
     * 获取审核通过机构列表（uuid-orgname）
     *
     * @return
     */
    @RequestMapping(params = "method=findOrgUuidAndNameList")
    public JsonResult findOrgUuidAndNameList() {
        return JsonResult.success(organizationService.findOrgUuidAndNameList());
    }


    /**
     * 通过的机构创建默认团队
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=createTeam")
    public JsonResult createTeam(RequestParams params) {
        Long id = params.getFormLong(0);
        Integer onlineStatus = params.getFormInteger(1);
        Integer liveStatus = params.getFormInteger(2);
        Integer quliaoStatus = params.getFormInteger(3);
        String operator = params.getFormString(4);
        TeamDto teamDto = new TeamDto();
        teamDto.setOrgId(id);
        teamDto.setCreateBy(operator);
        Map<Integer, Integer> mapType = new HashMap<>();
        mapType.put(TeamTypeEnum.LIVE_TEAM.getValue(), liveStatus);
        mapType.put(TeamTypeEnum.CALL_TEAM.getValue(), quliaoStatus);
        mapType.put(TeamTypeEnum.TALK_TEAM.getValue(), onlineStatus);
        teamDto.setTypeMap(mapType);
        teamService.insertDefaultTeam(teamDto);
        Organization organization = organizationMapper.selectByPrimaryKey(id);
        organization.setQuliaoPermissions(quliaoStatus);
        organization.setLivePermissions(liveStatus);
        organization.setChatRoomPermissions(onlineStatus);
        organizationMapper.updateByPrimaryKeySelective(organization);
        return JsonResult.success("赋予公会类型并创建默认团队完毕");
    }

    /**
     * 从oa获取运营人员
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getBusinessPerson")
    public JsonResult getBusinessPerson(RequestParams params) {
        String[] departmentIdList = department.split(",");
        List<SsoUserCombobox> ssoUserComboboxes = new ArrayList<SsoUserCombobox>();
        for (int i = 0; i < departmentIdList.length; i++) {
            SsoUserSearch ssoUserSearch = new SsoUserSearch();
            ssoUserSearch.setOrganizationId(Long.parseLong(departmentIdList[i]));
            JSONArray array = ssomsService.searchEffectiveSsoUsersV2(ssoUserSearch);
            List<SsoUserCombobox> ssoUserComboboxeOne = JsonUtils.stringToObject(JsonUtils.objectToString(array), new com.fasterxml.jackson.core.type.TypeReference<List<SsoUserCombobox>>() {
            });
            ssoUserComboboxes.addAll(ssoUserComboboxeOne);
        }
        return JsonResult.success(ssoUserComboboxes);
    }


    /**
     * 后台直接创建机构
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=saveOrgAll")
    public JsonResult saveOrgAll(RequestParams params) {
        //3.公司信息
        String formStringOption = params.getFormString(0);
        String operator = params.getFormString(1);
        OrganizationVo vo = JsonUtils.stringToObject2(formStringOption, OrganizationVo.class);
        return JsonResult.success(organizationService.saveOrgAll(vo, operator));
    }

    /**
     * 运营审核操作历史
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getApplyLog")
    public JsonResult getApplyLog(RequestParams params) {
        //3.公司信息
        Long orgId = params.getFormLong(0);
        List<ApplyTypeLog> applyTypeLog = applyTypeLogMapper.selectByOrgId(orgId);
        return JsonResult.success(applyTypeLog);
    }


    /**
     * 批量查询公会名称
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getOrgNames")
    public JsonResult getOrgNames(RequestParams params) {
        //3.公司信息
        String[] orgUuid = params.getFormStringArray(0);
        Map<String, String> map = new HashMap<>();
        if (orgUuid.length != 0) {
            for (int i = 0; i < orgUuid.length; i++) {
                Organization organization = organizationMapper.getByUuid(orgUuid[i]);
                map.put(orgUuid[i], organization.getOrgName());
            }
        }
        return JsonResult.success(map);
    }


    /**
     * 关闭机构
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=orgClose")
    public JsonResult orgClose(RequestParams params) {
        Long id = params.getFormLong(0);
        String sceneId = params.getFormStringDefault(1, "");
        String code = params.getFormStringDefault(2, "");
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        String userName = soaBaseParams.getToken();
        log.info("请求参数id={},userName={}", id,userName);
        List<Long> teamIdlist = teamMapper.selectTeamListIds(id, TeamTypeEnum.LIVE_TEAM.getValue());
        log.info("teamIdlist={}",JSON.toJSONString(teamIdlist));
        /**
         * off_consortia_and_disable_host
         * {
         *     "create_time": 1659061201796,
         *     "host_uuids": [
         *         "iop44ezeziy"
         *     ],
         *     "opeator_name": "admin"
         * }
         */
        List<String> pushStrList = new ArrayList<>();
        Map<String,List<String>> listMap = new HashMap<>();
        for(Long teamid : teamIdlist){
            List<String> teamHostIdList =  teamHostMapper.findHostsByTeamId(teamid);
            log.info("teamHostIdList={}",teamHostIdList.size());
            List<List<String>> teamHostIdListGroup = this.groupList(teamHostIdList);
            for(List<String> stringList : teamHostIdListGroup){
                Map<String, Object> map = new HashMap<>();
                map.put("host_uuids", stringList);
                map.put("opeator_name", userName);
                map.put("create_time", System.currentTimeMillis());
                pushStrList.add(JSON.toJSONString(map));
            }
        }
        listMap.put("off_consortia_and_disable_host",pushStrList);
        log.info("off_consortia_and_disable_host 推送参数,map={}", JSON.toJSONString(listMap));
        organizationService.orgClose(id, sceneId, code);

        tqmqService.multiplePush(listMap, 0L);


        return JsonResult.success("成功");
    }

    /**
     * 机构关闭审核接口
     * @param params
     * @return
     */
    @PostMapping(params = "method=orgCloseReview")
    public JsonResult orgCloseReview(RequestParams params) {
        Long orgId = params.getFormLongDefault(0, 0L);
        String reason = params.getFormStringDefault(1, "");
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        loginUser.getUser().setUserName("admin");

        organizationService.orgCloseReview(orgId, loginUser.getUsername(), reason);

        return JsonResult.success();
    }

    @RequestMapping(params = "method=orgFlowLog")
    public JsonResult orgFlowLog(RequestParams params) {
        Long orgId = params.getFormLong(0);
        List<OrgFlowLog> orgFlowLogList = organizationService.getOrgFlowLogById(orgId);

        return JsonResult.success(orgFlowLogList);
    }

    public List<List<String>> groupList(List<String> teamHostIdList) {
        log.info("thirdPartDOList切割前的大小={}", teamHostIdList.size());
        List<List<String>> listGroup = new ArrayList<List<String>>();
        int listSize = teamHostIdList.size();
        //子集合的长度，比如 500
        int toIndex = 1000;
        for (int i = 0; i < teamHostIdList.size(); i += toIndex) {
            if (i + toIndex > listSize) {
                toIndex = listSize - i;
            }
            List<String> newList = teamHostIdList.subList(i, i + toIndex);
            listGroup.add(newList);
        }
        log.info("切割后listGroup的size={}", listGroup.size());
        return listGroup;
    }

    /**
     * 运营撤销机构的审核
     */
    @RequestMapping(params = "method=changeOrg")
    public JsonResult changeOrg(RequestParams params) {
        Long id = params.getFormLong(0);
        organizationService.changeOrg(id);
        return JsonResult.success("撤销申请成功");
    }

    /**
     *校验修改机构短信验证码
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=checkUpdateVCode")
    public JsonResult checkUpdateVCode(RequestParams params) {
        String mobile =  params.getFormString(0);
        String vCode =  params.getFormString(1);
        verifyService.verifyCommon(VerifyCodeEnum.REBIND_ORG.getCode(), mobile, vCode);
        return JsonResult.success("绑定手机号成功");
    }

    /**
     * 修改机构信息发送短信验证码
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=sendUpdateVCode")
    public JsonResult sendUpdateVCode(RequestParams params) {
        String mobile =  params.getFormString(0);

        Boolean isSend = redisUtil.set(mobile, "send", 60L, TimeUnit.SECONDS);
        if (!isSend) {
            return JsonResult.failed("发送频率过高,请稍等后再尝试");
        }
        verifyService.sendVCode(VerifyCodeEnum.REBIND_ORG.getCode(), mobile);
        return JsonResult.success("短信验证码发送成功");
    }

    @RequestMapping(params = "method=updateOrgOperator")
    public JsonResult modifyOrgInfo(RequestParams params) {

        String operator = params.getFormString(0);
        String modifyOperator = params.getFormString(1);
        Long orgId = params.getFormLong(2);
        log.info("updateOrgOperator方法,operator={},modifyOperator={},orgId={}", operator, modifyOperator, orgId);
        Organization organizationVo = new Organization();
        organizationVo.setBusinessPerson(modifyOperator);
        organizationVo.setOrgId(orgId);

        organizationService.modifyOrgInfo(organizationVo, operator);
        return JsonResult.success("信息修改成功");
    }

    /**
     * 修改机构信息
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=updateOrgName")
    public JsonResult updateOrgName(RequestParams params) {
        /**
         * @param operator String operator，必填 当前操作人
         * @param orgId Long orgId，必填 公会id
         * @param orgName String orgName，必填 修改后的公会名称
         */
        String operator = params.getFormString(0);
        Long orgId = params.getFormLong(1);
        String orgName = params.getFormString(2);
        log.info("updateOrgName方法,operator={},orgId={},orgName={}", operator, orgId, orgName);

        Organization organizationVo = new Organization();
        organizationVo.setOrgName(orgName);
        organizationVo.setOrgId(orgId);

        organizationService.modifyOrgInfo(organizationVo, operator);
        return JsonResult.success("信息修改成功");
    }

    @PostMapping(params = "method=userInWhiteList")
    public JsonResult userInWhiteList() {
        String operator = SoaBaseParams.fromThread().getToken();
        return JsonResult.success(orgWhiteList.contains(operator));
    }

    /**
     * 变更业务类型
     * @param params
     * @return
     */
    @RequestMapping(params = "method=changeBusinessType")
    public JsonResult changeBusinessType(RequestParams params){
        String sceneId = params.getFormString(0);
        String code = params.getFormString(1);
        Long orgId = params.getFormLong(2);
        Integer businessType = params.getFormInteger(3);
        Integer permissions = params.getFormInteger(4);
        Integer settlementeType = params.getFormIntegerDefault(5, null);
        organizationService.changeBusinessType(sceneId, code, orgId, businessType, permissions, settlementeType);
        return JsonResult.success("变更成功");
    }
}
