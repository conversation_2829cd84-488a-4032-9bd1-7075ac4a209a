package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.live.service.EmployeeService;

import cn.taqu.gonghui.system.service.SysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @Date 2021/6/17
 */
@RestController
@RequestMapping(value = "/api",params = "service=manageEmployee")
public class ManageEmployeeController {
    @Autowired
    private EmployeeService employeeService;


    @RequestMapping(params = "method=getList")
    public JsonResult pageList(RequestParams params){

        return JsonResult.success(employeeService.getMap());
    }
}
