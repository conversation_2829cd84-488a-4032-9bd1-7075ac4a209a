package cn.taqu.gonghui.controller.manage.recreation;


import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.soa.GonghuiService;
import cn.taqu.gonghui.soa.GonghuiSoaService;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.mapper.TeamMapper;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.impl.GonhuiSoaServiceImpl;
import cn.taqu.gonghui.system.vo.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 经纪人 传经纪人下的主播uuid数组  公会id就传他所在的团队id
 * 如果是团队管理员就是传公会id
 * 如果是机构管理员就是传机构下的所有公会id
 */
@RestController
@RequestMapping(value = "/api", params = "service=Gonghui2")
@Slf4j
public class LiveController {
    @Autowired
    private GonhuiSoaServiceImpl gonhuiSoaServiceImpl;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private TeamEmployeeService teamEmployeeService;
    @Autowired
    private TeamHostMapper teamHostMapper;
    @Autowired
    private TeamMapper teamMapper;

    /**
     * 经纪人 传经纪人下的主播uuid数组  公会id就传他所在的团队id
     * 如果是团队管理员就是传公会id
     * 如果是机构管理员就是传机构下的所有公会id
     */
    /**
     * 请求值说明
     * <p>
     * ChartData getLineChartData(String type, List<String> consortiaIdList, String businessmanUuid);
     *
     * @return
     */
    @RequestMapping(params = "method=getLineChartData")
    public JsonResult getLineChartData(RequestParams params) throws ParseException {
        String type = params.getFormStringOption(0);
//        Object[] consortiaIdList = params.getFormLongArray(1);
//        String businessman_uuid = params.getFormStringOption(2);
        List<Long> consortiaIdList =  new ArrayList<>();
        String businessman_uuid = null;
        log.info("getLineChartData参数,type={},consortiaIdList={},businessman_uuid={}", type, JSON.toJSONString(consortiaIdList), businessman_uuid);

        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        log.info("user={}", JSON.toJSONString(user));

        if (UserTypeEnum.AGENTER.getType().equals(user.getUserType())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            businessman_uuid = user.getAccountUuid();
            consortiaIdList.add(teamEmployee.getTeamId()) ;
        } else if (UserTypeEnum.LEADER.getType().equals(user.getUserType())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortiaIdList.add(teamEmployee.getTeamId()) ;
        } else if (UserTypeEnum.MANAGER.getType().equals(user.getUserType())) {
            List<Long> teamIdlist = teamMapper.selectTeamListIds(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortiaIdList.addAll(teamIdlist);
        } else {
            log.warn("该用户没有此功能的查询权限");
            return JsonResult.failed("该用户没有此功能的查询权限");
        }

        JSONObject jsonObject = gonhuiSoaServiceImpl.getLineChartData(type, consortiaIdList, businessman_uuid);
        return JsonResult.success(jsonObject);
    }

    /**
     * StatData getStatData(List<String> consortiaIdList, String businessmanUuid);
     *
     * @param params
     * @return
     * @throws ParseException
     */
    @RequestMapping(params = "method=getStatData")
    public JsonResult getStatData(RequestParams params) throws ParseException {
//        Long[] consortiaIdList = params.getFormLongArray(0);
//        String businessman_uuid = params.getFormStringOption(1);
        List<Long> consortiaIdList =  new ArrayList<>();
        String businessman_uuid = null;

        // 获取用户信息
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();

        if (UserTypeEnum.AGENTER.getType().equals(user.getUserType())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            businessman_uuid = user.getAccountUuid();
            consortiaIdList.add(teamEmployee.getTeamId());
        } else if (UserTypeEnum.LEADER.getType().equals(user.getUserType())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortiaIdList.add(teamEmployee.getTeamId());
        } else if (UserTypeEnum.MANAGER.getType().equals(user.getUserType())) {
            List<Long> teamIdlist = teamMapper.selectTeamListIds(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortiaIdList.addAll(teamIdlist);

        } else {
            log.warn("该用户没有此功能的查询权限");
            return JsonResult.failed("该用户没有此功能的查询权限");
        }

        log.info("getStatData参数,consortiaIdList={},businessman_uuid={}", JSON.toJSONString(consortiaIdList), businessman_uuid);
        JSONObject jsonObject = gonhuiSoaServiceImpl.getStatData(consortiaIdList, businessman_uuid);
        return JsonResult.success(jsonObject);
    }


//    DayHostRank getDayHostRank(List<String> consortiaIdList, int page, int limit);

    @RequestMapping(params = "method=getDayHostRank")
    public JsonResult getDayHostRank(RequestParams params) throws ParseException {
//        Long[] consortiaIdList = params.getFormLongArray(0);
        List<Long> consortiaIdList = new ArrayList<>();

        int page = params.getFormInteger(1);
        int limit = params.getFormInteger(2);
        String host_uuid = params.getFormStringOption(3);
        // 获取用户信息
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();

        if (UserTypeEnum.AGENTER.getType().equals(user.getUserType())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortiaIdList.add(teamEmployee.getTeamId());
        } else if (UserTypeEnum.LEADER.getType().equals(user.getUserType())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortiaIdList.add(teamEmployee.getTeamId());
        } else if (UserTypeEnum.MANAGER.getType().equals(user.getUserType())) {
            List<Long> teamIdlist = teamMapper.selectTeamListIds(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortiaIdList.addAll(teamIdlist);

        } else {
            log.warn("该用户没有此功能的查询权限");
            return JsonResult.success();
        }
        log.info("getDayHostRank参数,consortiaIdList={},page={},limit={}", JSON.toJSONString(consortiaIdList), page, limit);
        JSONObject jsonObject= gonhuiSoaServiceImpl.getDayHostRank(consortiaIdList, page, limit,host_uuid);
        return JsonResult.success(jsonObject);
    }

    @RequestMapping(params = "method=getFrameDetail")
    public JsonResult getFrameDetail(RequestParams params) throws ParseException {
        String host_uuid = params.getFormStringOption(0);
        String consortia_id = params.getFormStringOption(1);

        log.info("getFrameDetail333参数,host_uuid={},consortia_id={}", host_uuid, consortia_id);
        JSONObject jsonObject = gonhuiSoaServiceImpl.getFrameDetail(host_uuid, consortia_id);
        return JsonResult.success(jsonObject);
    }

    @RequestMapping(params = "method=getFrameListExtraInfo")
    public JsonResult getFrameListExtraInfo(RequestParams params) {
//        String consortia_id = params.getFormStringOption(0);
//        String businessman_uuid = params.getFormStringOption(1);
        List<Long> consortiaIdList =  new ArrayList<>();
        String businessman_uuid = null;
        Integer page = params.getFormIntegerOption(2);
        Integer pageSize = params.getFormIntegerOption(3);
        String host_uuid = params.getFormStringOption(4);
        String apply_level = params.getFormStringOption(5);

        log.info("getFrameListExtraInfo参数,host_uuid={},consortia_id={},page={},pageSize={},host_uuid={},apply_level={}", consortiaIdList, businessman_uuid, page, pageSize, host_uuid, apply_level);

        // 获取用户信息
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();

        if (UserTypeEnum.AGENTER.getType().equals(user.getUserType())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            businessman_uuid = user.getAccountUuid();
            consortiaIdList.add(teamEmployee.getTeamId());
        } else if (UserTypeEnum.LEADER.getType().equals(user.getUserType())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortiaIdList.add(teamEmployee.getTeamId());
        } else if (UserTypeEnum.MANAGER.getType().equals(user.getUserType())) {
            List<Long> teamIdlist = teamMapper.selectTeamListIds(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortiaIdList.addAll(teamIdlist);

        } else {
            log.warn("该用户没有此功能的查询权限");
            return JsonResult.success();
        }

        JSONObject jsonObject= gonhuiSoaServiceImpl.getFrameListExtraInfo(consortiaIdList, businessman_uuid, page, pageSize, host_uuid, apply_level);

        return JsonResult.success(jsonObject);
    }

    @RequestMapping(params = "method=getHostAmountTaskList")
    public JsonResult getHostAmountTaskList(RequestParams params) throws ParseException {

        Integer page = params.getFormIntegerOption(0);
        Integer stat_time = params.getFormIntegerOption(1);
//        Integer pageSize = params.getFormIntegerOption(3);
//        Long[] consortia_id = params.getFormLongArray(3);
        List<Long> consortia_ids = new ArrayList<>() ;
        String businessman_uuid = params.getFormStringOption(3);
        String host_uuid = params.getFormStringOption(4);
        String is_new_host = params.getFormStringOption(5);
        Integer is_export = params.getFormIntegerOption(6);
        // 获取用户信息
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();


        if (UserTypeEnum.AGENTER.getType().equals(user.getUserType())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            businessman_uuid = user.getAccountUuid();
            consortia_ids.add(teamEmployee.getTeamId());
        } else if (UserTypeEnum.LEADER.getType().equals(user.getUserType())) {
            TeamEmployee teamEmployee = teamEmployeeService.getOneByUserIdAndType(user.getUserId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortia_ids.add(teamEmployee.getTeamId());
        } else if (UserTypeEnum.MANAGER.getType().equals(user.getUserType())) {
            List<Long> teamIdlist = teamMapper.selectTeamListIds(user.getOrgId(), TeamTypeEnum.LIVE_TEAM.getValue());
            consortia_ids.addAll(teamIdlist);

        } else {
            log.warn("该用户没有此功能的查询权限");
            return JsonResult.failed("该用户没有此功能的查询权限");
        }

        JSONObject jsonObject = gonhuiSoaServiceImpl.getHostAmountTaskList(page, stat_time, consortia_ids,businessman_uuid, host_uuid, is_new_host, is_export);

        return JsonResult.success(jsonObject);
    }


}
