package cn.taqu.gonghui.controller.user;

import cn.taqu.core.orm.PageSearch;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.live.entity.LiveHostApplyInfo;
import cn.taqu.gonghui.live.service.LiveHostApplyInfoService;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/6/17
 */

@Slf4j
@RestController
@RequestMapping(value = "api", params = "service=liveHostApplyInfo")
public class LiveHostApplyInfoController {

    @Autowired
    private LiveHostApplyInfoService liveHostApplyInfoService;

    /**
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=saveHostApply")
    @PreAuthorize("@ss.hasPermi('liveHostApplyInfo:saveHostApply')")
    public JsonResult saveHostApply(RequestParams params) {
        LiveHostApplyInfo entity = new LiveHostApplyInfo();
        Long id = params.getFormLongDefault(0, 0L);
        if (Objects.equals(id, 0L)) {
            id = null;
        }
        entity.setId(id);//id
        entity.setAccountMobile(params.getFormString(1));//他趣账号
        entity.setBusinessmanUuid(params.getFormStringDefault(2,""));//经纪人uuid
        String idCardUrl = params.getFormString(3); //身份证照片(正面，反面，手持)
        List<String> split = Splitter.on(",").splitToList(idCardUrl);
        entity.setCardZmUrl(split.get(0));
        entity.setCardFmUrl(split.get(1));
        entity.setCardScUrl(split.get(2));
        entity.setCoverUrl(params.getFormStringDefault(4, ""));//主播封面
        entity.setWidth(params.getFormIntegerDefault(5, 0));//封面宽度
        entity.setHeight(params.getFormIntegerDefault(6, 0));//封面高度
        entity.setVideo(params.getFormStringDefault(7, ""));//主播视频
        entity.setConsortiaId(params.getFormIntegerDefault(8, null)); //团队id
        Integer operStatus = params.getFormInteger(9);//操作状态：1-提交，2-保存

        liveHostApplyInfoService.saveHostApply(entity, operStatus);
        return JsonResult.success("操作成功");
    }



    /**
     * 根据id撤销主播申请
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=cancelHostApplyById")
    @PreAuthorize("@ss.hasPermi('liveHostApplyInfo:cancelHostApplyById')")
    public JsonResult cancelHostApplyById(RequestParams params) {
        Long id = params.getFormLong(0);
        liveHostApplyInfoService.cancelHostApplyById(id);
        return JsonResult.success("撤销成功");
    }

    /**
     * 根据id查询主播申请
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getHostApplyById")
    public JsonResult getHostApplyById(RequestParams params) {
        Long id = params.getFormLong(0);
        return JsonResult.success(liveHostApplyInfoService.getHostApplyById(id));
    }


    /**
     * 获取当前登陆账号下，主播申请列表
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getHostApplyList")
    @PreAuthorize("@ss.hasPermi('liveHostApplyInfo:getHostApplyList')")
    public JsonResult getHostApplyList(RequestParams params) {
        String nickname = params.getFormStringDefault(0, null); //昵称
        String hostUuid = params.getFormStringDefault(1, null);//主播uuid
        Integer applyStatus = params.getFormIntegerDefault(2, 0);//审批状态 0-全部，1-通过，2-拒绝，3-待审核
        Integer page = params.getFormIntegerDefault(3, 1);//页数
        Integer pageSize = params.getFormIntegerDefault(4, 10);//每页数量

        return JsonResult.success(liveHostApplyInfoService.getHostApplyList(nickname, hostUuid, applyStatus, page, pageSize));
    }


    /**
     * 根据手机号码获取实名认证
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getInfoByMobile")
    public JsonResult getInfoByMobile(RequestParams params) {
        String mobile = params.getFormStringDefault(0,"");
        return JsonResult.success(  liveHostApplyInfoService.getInfoByMobile(mobile));
    }
}
