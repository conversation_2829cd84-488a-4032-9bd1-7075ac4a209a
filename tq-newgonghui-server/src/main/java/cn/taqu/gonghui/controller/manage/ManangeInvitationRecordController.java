package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.service.InvitationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2021/5/11
 */
@RestController
@RequestMapping(value = "/api", params = "service=manangeInvitationRecord")
public class ManangeInvitationRecordController {

    @Autowired
    private InvitationRecordService invitationRecordService;

    @RequestMapping(params = "method=getByPage")
    public JsonResult list(RequestParams params) {
        String orgUuid = params.getFormStringDefault(0,"");
        Integer pageNo = params.getFormIntegerDefault(1,1);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        return JsonResult.success(invitationRecordService.findByPageForAdmin(orgUuid,pageNo,pageSize));
    }

}
