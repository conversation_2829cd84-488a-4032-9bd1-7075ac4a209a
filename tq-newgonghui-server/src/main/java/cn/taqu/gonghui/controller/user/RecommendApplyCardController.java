package cn.taqu.gonghui.controller.user;

import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.service.RecommendApplyCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户端首页 - 获取推荐位信息
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/api", params = "service=recommendApplyCard")
public class RecommendApplyCardController {

    @Autowired
    private RecommendApplyCardService applyCardService;

    @RequestMapping(params = "method=getRecommendApplyCardInfo")
    @PreAuthorize("@ss.hasPermi('recommendApplyCard:getRecommendApplyCardInfo')")
    public JsonResult getRecommendApplyCardInfo() {
        return JsonResult.success(applyCardService.getHomeRecommendApplyCardInfo());
    }
}
