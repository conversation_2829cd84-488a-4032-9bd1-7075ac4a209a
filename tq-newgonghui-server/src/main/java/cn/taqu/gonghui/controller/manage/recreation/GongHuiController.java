package cn.taqu.gonghui.controller.manage.recreation;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.entity.Notice;
import cn.taqu.gonghui.system.search.NoticeSearch;
import cn.taqu.gonghui.system.service.NoticeService;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api", params = "service=recreation")
@Slf4j
public class GongHuiController {

    @Autowired
    private NoticeService noticeService;


    @RequestMapping(params = "method=noticePage")
    public JsonResult noticePage(RequestParams params) {
        Integer pageNum = params.getFormIntegerDefault(0, 1);
        Integer pageSize = params.getFormIntegerDefault(1, 15);
        String formString = params.getFormStringOption(2);
        log.info("noticePage请求参数pageNum={},pageSize={},formString={}", pageNum, pageSize, formString);
        NoticeSearch noticeSearch = null;
        noticeSearch = JsonUtils.stringToObject2(formString, NoticeSearch.class);
        noticeSearch.setPageSize(pageSize);
        noticeSearch.setPageNum(pageNum);
        List<Notice> pagelist = noticeService.pagelist(noticeSearch);
        return JsonResult.success(new PageInfo<>(pagelist));
    }

    /**
     * 详情
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=noticeDetail")
    public JsonResult detail(RequestParams params) {
        Long id = params.getFormLong(0);
        log.info("noticeDetail请求入参id={}", id);
        return JsonResult.success(noticeService.detail(id));
    }

    /**
     * 详情
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=detailBytitle")
    public JsonResult detailBytitle(RequestParams params) {
        String title = params.getFormStringOption(0);
        return JsonResult.success(noticeService.detailBytitle(title));
    }

}
