package cn.taqu.gonghui.controller.user;


import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.service.SysMenuService;
import cn.taqu.gonghui.system.service.SysUserService;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2021/4/25
 */
@RestController
@RequestMapping(value = "api", params = "service=user")
public class SysUserController {

    private static Logger logger = LoggerFactory.getLogger(SysUserController.class);

    @Autowired
    private SysMenuService sysMenuService;
    @Autowired
    private SysUserService sysUserService;


    @RequestMapping(params = "method=getMenuInfo")
    public JsonResult getMenuInfo(RequestParams params) {
        Integer businessType = params.getFormIntegerDefault(0,null);
        logger.info("getMenuInfo参数打印,businessType={}",businessType);
        return JsonResult.success(sysMenuService.getMenuListByLoginAccount(businessType));
    }

    /**
     * 得到有效经纪人下拉列表
     *
     * @return
     */
    @RequestMapping(params = "method=getAgentSelect")
    public JsonResult getAgentSelect() {
        return JsonResult.success(sysUserService.getAgentSelect());
    }

    /**
     * 获取用户权限菜单
     *
     * @return
     */
    @RequestMapping(params = "method=selectMenuPermsMap")
    public JsonResult selectMenuPermsMap(RequestParams params) {
        String pathStr = params.getFormStringOption(0);
        logger.info("[selectMenuPermsMap]获取pathStr：{}", pathStr);
        if (StringUtils.isBlank(pathStr)) {
            return JsonResult.failed("菜单路径不能为空");
        }
        Gson gson = new GsonBuilder().create();
        List<String> pathList = gson.fromJson(pathStr, new TypeToken<List<String>>() {}.getType());
        logger.info("formStringArray转换为list:{}",JsonUtils.objectToString(pathList));
        Map<String, Boolean> maps = sysMenuService.selectMenuPermsMap(pathList);
        return JsonResult.success(maps);
    }
}
