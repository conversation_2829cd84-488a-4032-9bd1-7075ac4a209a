package cn.taqu.gonghui.controller;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.soa.client.config.SoaClientConfiguration;
import cn.taqu.core.soa.client.proxy.SoaClientProxy;
import cn.taqu.core.soa.client.proxy.SoaClientRegister;
import cn.taqu.core.soa.common.HttpMethod;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.utils.SpringUtils;
import cn.taqu.gonghui.soa.CertificationService;
import cn.taqu.gonghui.soa.InfoManager;
import cn.taqu.gonghui.soa.dto.Info;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.service.TeamHostService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RequestMapping(path = "/api")
@RestController
@Slf4j
@RequiredArgsConstructor
public class SoaController {
    @Autowired
    private TeamHostMapper teamHostMapper;

    @SoaReference("account")
    private CertificationService certificationService;
    @Autowired
    private InfoManager infoManager;


    @PostMapping("/data_soa")
    public JsonResult dataSoa(
            @RequestParam("method") String method,
            @RequestParam("service") String service,
            @RequestBody String paramsStr
    ) {
        if (!SpringUtils.isDevOrTest()) {
            return JsonResult.success();
        }
        return JsonResult.success(SoaClientFactory.create(SoaServer.JAVA.DATA_API)
                .call(service, method, JsonUtils.stringToObject2(paramsStr, List.class)
                        .toArray(new Object[0])));


    }

    @PostMapping("/getAccountDemo")
    public JsonResult getAccountDemo(@RequestParam Integer size) {

        if (!SpringUtils.isDevOrTest()) {
            return JsonResult.success();
        }
        List<String> accountUuids = teamHostMapper.selectList(
                        new LambdaQueryWrapper<TeamHost>()
                                .select(TeamHost::getHostUuid))
                .stream()
                .map(TeamHost::getHostUuid)
                .collect(Collectors.toList());
        Map<String, String> trueNameMap = new HashMap<>();

        int i = 0;
        while (true) {
            List<String> accountUuids_ = accountUuids.stream()
                    .skip(i)
                    .limit(size)
                    .collect(Collectors.toList());
            if (accountUuids_.isEmpty()) {
                break;
            }

            Map<String, Map<String, Object>> cinfoMaps = certificationService.getByUuids(accountUuids_);

            cinfoMaps.forEach((k, v) -> {
                if (StringUtils.isEmpty((String) v.get("real_name"))) {
                    return;
                }
                trueNameMap.put(k, (String) v.get("real_name"));
            });
            i += size;
        }

        Map<String, Info> infoMaps = infoManager.getInfoByUuidsNoSecret(new ArrayList<>(trueNameMap.keySet()));
        List<Map<String, String>> retMaps = new ArrayList<>();


        trueNameMap.forEach((k, truename) -> {
            Info info = infoMaps.get(k);
            if (info == null) {
                return;
            }
            Map<String, String> m = new HashMap<>();
            m.put("他趣ID", info.getDefaultCardId());
            m.put("真实名字", truename);
            m.put("手机号", info.getMobile());
            m.put("他趣UUID",k);
            retMaps.add(m);

        });


        return JsonResult.success(retMaps);
    }


    @PostMapping("/soa")
    public JsonResult accountSoa(
            @RequestParam("method") String method,
            @RequestParam("service") String service,
            @RequestParam("application") String application,
            @RequestBody String paramsStr
    ) {
        if (!SpringUtils.isDevOrTest()) {
            return JsonResult.success();
        }

        SoaClientConfiguration soaClientConfiguration = SpringUtils.getBean(SoaClientConfiguration.class);
        SoaClientRegister soaClientRegister = new SoaClientRegister(soaClientConfiguration);
        SoaClient soaClient =
                soaClientRegister.register(application, 3, 3, 3, TimeUnit.SECONDS, HttpMethod.POST);
        return JsonResult.success(soaClient
                .call(service, method, JsonUtils.stringToObject2(paramsStr, List.class)
                        .toArray(new Object[0])));


    }
}
