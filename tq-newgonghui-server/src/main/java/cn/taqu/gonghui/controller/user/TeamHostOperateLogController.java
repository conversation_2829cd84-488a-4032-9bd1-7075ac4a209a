package cn.taqu.gonghui.controller.user;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.search.TeamHostOperateLogSearch;
import cn.taqu.gonghui.system.service.TeamHostService;
import cn.taqu.gonghui.system.vo.TeamHostOperateLogVo;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping(value = "api", params = "service=operateLog")
@RestController
public class TeamHostOperateLogController {
    @Autowired
    private TeamHostService teamHostService;


    /**
     * 获取操作日志列表
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getOperateLogList")
    @PreAuthorize("@ss.hasPermi('operateLog:getOperateLogList')")
    public JsonResult getOperateLogList(RequestParams params) {
        Integer type = params.getFormIntegerOption(0);
        String hostUuid = params.getFormStringOption(1);
        Integer page = params.getFormIntegerDefault(2, 1);
        Integer pageSize = params.getFormIntegerDefault(3, 10);
        TeamHostOperateLogSearch search = new TeamHostOperateLogSearch();
        search.setType(type);
        search.setHostUuid(hostUuid);
        search.setPage(page);
        search.setPageSize(pageSize);
        List<TeamHostOperateLogVo> logList = teamHostService.getLogList(search);
        return JsonResult.success(new PageInfo<>(logList));
    }
}
