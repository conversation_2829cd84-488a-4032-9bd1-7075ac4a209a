package cn.taqu.gonghui.controller.user;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.entity.Notice;
import cn.taqu.gonghui.system.search.NoticeSearch;
import cn.taqu.gonghui.system.service.NoticeService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 获取公告信息 - 用户端
 */
@RestController
@RequestMapping(value = "api", params = "service=notice")
public class NoticeController {

    @Autowired
    private NoticeService noticeService;

    /**
     * 获取所有公告类型列表返回
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getNoticeList")
    @PreAuthorize("@ss.hasPermi('notice:getNoticeList')")
    public JsonResult getNoticeList(RequestParams params){
        Integer businessType = params.getFormInteger(0);
        // 公告类型
        NoticeSearch search = new NoticeSearch();
        search.setPageNum(1);
        search.setPageSize(5);
        search.setNoticeType(1);
        search.setBusinessType(businessType);
        List<Notice> noticeList = noticeService.pagelist(search);
        // 规范类型
        NoticeSearch search2 = new NoticeSearch();
        search2.setPageNum(1);
        search2.setPageSize(3);
        search2.setNoticeType(2);
        search2.setBusinessType(businessType);
        List<Notice> standardList = noticeService.pagelist(search);
        // 帮助中心
        NoticeSearch search3 = new NoticeSearch();
        search3.setPageNum(1);
        search3.setPageSize(3);
        search3.setNoticeType(3);
        search3.setBusinessType(businessType);
        List<Notice> helpCenterList = noticeService.pagelist(search);
        Map<String,Object> map = new HashMap<>();
        map.put("noticeList",noticeList);
        map.put("standardList",standardList);
        map.put("helpCenterList",helpCenterList);
        return JsonResult.success(map);
    }

    /**
     * 根据公告类型返回分页列表
     */
    @RequestMapping(params = "method=getNoticeListByType")
    @PreAuthorize("@ss.hasPermi('notice:getNoticeListByType')")
    public JsonResult getNoticeListByType(RequestParams params){
        Integer noticeType = params.getFormInteger(0);
        Integer pageNum = params.getFormIntegerDefault(1, 1);
        Integer pageSize = params.getFormIntegerDefault(2, 15);
        Integer businessType = params.getFormInteger(3);
        NoticeSearch search = new NoticeSearch();
        search.setNoticeType(noticeType);
        search.setPageNum(pageNum);
        search.setPageSize(pageSize);
        search.setBusinessType(businessType);
        List<Notice> pagelist = noticeService.pagelist(search);
        return JsonResult.success(new PageInfo<>(pagelist));
    }

    /**
     * 根据noticeId获取详情
     */
    @RequestMapping(params = "method=detailByNoticeId")
    @PreAuthorize("@ss.hasPermi('notice:detailByNoticeId')")
    public JsonResult detailByNoticeId(RequestParams params){
        Long noticeId = params.getFormLong(0);
        Notice detail = noticeService.detail(noticeId);
        return JsonResult.success(detail);
    }
}
