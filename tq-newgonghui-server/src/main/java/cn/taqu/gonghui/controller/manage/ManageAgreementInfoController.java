package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.dto.AgreementDto;
import cn.taqu.gonghui.system.search.AgreementSignLogSearch;
import cn.taqu.gonghui.system.service.AgreementInfoService;
import cn.taqu.gonghui.system.vo.AgreementSignLogVo;
import cn.taqu.gonghui.system.vo.AgreementVo;
import com.github.pagehelper.PageInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 协议管理
 * <AUTHOR>
 * @date 2021/05/19
 */
@RestController
@RequestMapping(value = "/api",params = "service=manageAgreement")
public class ManageAgreementInfoController {

    @Autowired
    private AgreementInfoService agreementInfoService;

    /**
     * 获取有效协议
     * @return
     */
    @RequestMapping(params = "method=findList")
    public JsonResult findList(RequestParams params) {
        String title = params.getFormStringDefault(0, "");
        return JsonResult.success(agreementInfoService.findList(title));
    }

    /**
     * 添加协议
     * @param params
     * @return
     */
    @RequestMapping(params = "method=add")
    public JsonResult add(RequestParams params) {
        String form = params.getFormString(0);
        String operator = params.getFormString(1);
        AgreementDto agreementDto = JsonUtils.stringToObject2(form, AgreementDto.class);
        agreementDto.setOperator(operator);
        agreementInfoService.add(agreementDto);
        return JsonResult.success("操作成功");
    }

    /**
     * 更新协议
     * @param params
     * @return
     */
    @RequestMapping(params = "method=update")
    public JsonResult update(RequestParams params) {
        String form = params.getFormString(0);
        String operator = params.getFormString(1);
        AgreementDto agreementDto = JsonUtils.stringToObject2(form, AgreementDto.class);
        agreementDto.setOperator(operator);
        agreementInfoService.update(agreementDto);
        return JsonResult.success("操作成功");
    }

    /**
     * 禁用协议
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=close")
    public JsonResult close(RequestParams params) {
        Long agrId = params.getFormLong(0);
        agreementInfoService.close(agrId);
        return JsonResult.success("操作成功");
    }

    /**
     * 获取协议
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getById")
    public JsonResult getById(RequestParams params) {
        Long agrId = params.getFormLong(0);
        Integer valid = params.getFormIntegerDefault(1, null);
        if (null == valid) {
            return JsonResult.success(agreementInfoService.getAgreementLogById(agrId));
        } else {
            return JsonResult.success(agreementInfoService.getById(agrId));
        }
    }


    /**
     * 获取机构签署的历史记录 -分页查询
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=findAgreementSignLogPageList")
    public JsonResult findAgreementSignLogPageList(RequestParams params) {
        String orgId = params.getFormStringDefault(0, "");
        String agrId = params.getFormStringOption(1);
        Integer page = params.getFormInteger(2);
        Integer pageSize = params.getFormInteger(3);
        AgreementSignLogSearch signLogSearch = new AgreementSignLogSearch();
        signLogSearch.setOrgId(orgId);
        if (StringUtils.isNotBlank(agrId)) {
            signLogSearch.setAgrId(Long.valueOf(agrId));
        }
        signLogSearch.setPageNum(page);
        signLogSearch.setPageSize(pageSize);
        List<AgreementSignLogVo> logPageList = agreementInfoService.findAgreementSignLogPageList(signLogSearch);
        return JsonResult.success(new PageInfo<>(logPageList));
    }

    /**
     * 获取有效下拉tree
     * @return
     */
    @RequestMapping(params = "method=findAgreementListForSearch")
    public JsonResult findAgreementListForSearch() {
        return JsonResult.success(agreementInfoService.findMapForSearch());
    }

    /**
     * 根据机构id和排序获取协议列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=findAgreementByOrgIdAndOrderLevel")
    public JsonResult findAgreementByOrgIdAndOrderLevel(RequestParams params) {
        Long orgId = params.getFormLong(0);
        Integer orderLevel = params.getFormInteger(1);
        List<AgreementVo> list = agreementInfoService.findAgreementByOrgIdAndOrderLevel(orgId, orderLevel);
        return JsonResult.success(list);
    }
}
