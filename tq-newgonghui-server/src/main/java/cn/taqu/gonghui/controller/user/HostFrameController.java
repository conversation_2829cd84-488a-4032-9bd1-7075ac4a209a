package cn.taqu.gonghui.controller.user;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.search.HostFrameListSearch;
import cn.taqu.gonghui.system.service.HostFrameService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping(value = "api", params = "service=hostFrame")
@RestController
@Slf4j
public class HostFrameController {

    @Autowired
    private HostFrameService hostFrameService;

    @RequestMapping(params = "method=findOnlineHostList")
    @PreAuthorize("@ss.hasPermi('hostFrame:findOnlineHostList')")
    public JsonResult findHostList() {
        return JsonResult.success(hostFrameService.findOnlineHostList());
    }

    @RequestMapping(params = "method=findOnlineFrameList")
    @PreAuthorize("@ss.hasPermi('hostFrame:findOnlineFrameList')")
    public JsonResult findOnlineFrameList(RequestParams params) {

        String queryParam = params.getFormString(0);
        Integer page = params.getFormInteger(1);
        Integer rows = params.getFormInteger(2);

        HostFrameListSearch search = JSON.parseObject(queryParam, new TypeReference<HostFrameListSearch>() {
        });
        search.setPage(page);
        search.setRows(rows);
        return JsonResult.success(hostFrameService.findOnlineFrameList(search));
    }


}
