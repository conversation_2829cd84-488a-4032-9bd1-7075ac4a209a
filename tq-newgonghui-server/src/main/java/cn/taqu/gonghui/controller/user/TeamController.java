package cn.taqu.gonghui.controller.user;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.req.TransferTeamReq;
import cn.taqu.gonghui.system.dto.HostOrgTeamDto;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.service.TeamService;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2021/5/7
 */
@RestController
@RequestMapping(value = "api", params = "service=team")
public class TeamController {

    @Autowired
    private TeamService teamService;
    @Autowired
    private TokenService tokenService;

    /**
     * 获取直播类型团队tree
     * 管理员：获取管理员所属机构所有团队tree
     * 负责人/经纪人：获取负责人或者经纪人所属团队
     * @param params
     * @return
     */
    @RequestMapping(params = "method=tree")
//    @PreAuthorize("@ss.hasPermi('team:tree')")
    public JsonResult tree(RequestParams params){
        Integer type = params.getFormInteger(0);
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        return JsonResult.success(teamService.selectTree(user.getOrgId(),type));
    }

    @RequestMapping(params = "method=orgTeamList")
    public JsonResult orgTeamList(RequestParams params){
        Long orgId = params.getFormLongDefault(0, 0L);
        Integer teamType = params.getFormIntegerDefault(1, 0);
        List<Team> teamList = teamService.selectTeamList(orgId, teamType);
        return JsonResult.success(teamList);
    }

    /**
     * 指定机构或团队 转移 到目标机构-团队(灰度接口调用)
     * @param params
     * @return
     */
    @RequestMapping(params = "method=transferTeam")
    public JsonResult transferTeam(RequestParams params){
        String form = params.getFormString(0);
        TransferTeamReq req = JsonUtils.stringToObject(form, new TypeReference<TransferTeamReq>() {});
        List<TeamHost> res = teamService.transferTeam(req);

        return JsonResult.success(res);
    }

}
