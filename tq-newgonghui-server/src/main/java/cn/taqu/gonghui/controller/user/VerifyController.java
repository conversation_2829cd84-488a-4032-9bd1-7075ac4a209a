package cn.taqu.gonghui.controller.user;


import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.VerifyCodeEnum;
import cn.taqu.gonghui.common.service.VerifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>  2019/9/22 10:46 AM
 */
@RestController
@RequestMapping(value = "/api", params = "service=verify")
public class VerifyController {

    @Autowired
    private VerifyService verifyService;

    /**
     * 发送登录验证码
     * @param params
     * @return
     */
    @ResponseBody
    @RequestMapping(params = "method=sendLoginVerify")
    public JsonResult sendLoginVerify(RequestParams params) {
        String mobile = params.getFormString(0);
        String sessionId = params.getFormStringDefault(1,"");
        verifyService.sendLoginVCode("web_login",mobile,sessionId);
        return JsonResult.success("验证码发送成功!");
    }


    /**
     * 发送通用验证码
     * @param params
     * @return
     */
    @ResponseBody
    @RequestMapping(params = "method=sendCommonVerify")
    public JsonResult sendCommonVerify(RequestParams params) {
        String mobile = params.getFormString(0);
        String code = params.getFormString(1);
        verifyService.sendVCode(code,mobile);
        return JsonResult.success("验证码发送成功!");
    }


    /**
     * 发送添加成员验证码
     * @param params
     * @return
     */
    @ResponseBody
    @RequestMapping(params = "method=sendAddMemberVerify")
    public JsonResult sendAddMemberVerify(RequestParams params) {
        String mobile = params.getFormString(0);
        verifyService.sendVCode(VerifyCodeEnum.ADD_MEMBER.getCode(),mobile);
        return JsonResult.success("验证码发送成功!");
    }


    /**
     * 发送邀请主播验证码
     * @param params
     * @return
     */
    @ResponseBody
    @RequestMapping(params = "method=sendInviteHostVerify")
    public JsonResult sendInviteHostVerify(RequestParams params) {
        String mobile = params.getFormString(0);
        verifyService.sendVCode(VerifyCodeEnum.INVITE_HOST.getCode(),mobile);
        return JsonResult.success("验证码发送成功!");
    }

    /**
     * 验证码通用验证
     * @param params
     * @return
     */
    @ResponseBody
    @RequestMapping(params = "method=verifyCommon")
    public JsonResult verifyCommon(RequestParams params) {
        String code = params.getFormString(0);
        String mobile = params.getFormString(1);
        String vcode = params.getFormString(2);
        verifyService.verifyCommon(code,mobile,vcode);
        return JsonResult.success("验证成功!");
    }



}
