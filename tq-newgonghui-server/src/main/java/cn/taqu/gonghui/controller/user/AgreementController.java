package cn.taqu.gonghui.controller.user;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.service.AgreementInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 协议管理
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "api", params = "service=agreement")
public class AgreementController {

    @Autowired
    private AgreementInfoService agreementInfoService;

    /**
     * 根据协议id获取协议np
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getByAgrId")
    @PreAuthorize("@ss.hasPermi('agreement:getByAgrId')")
    public JsonResult getAgentManageById(RequestParams params) {

        Long agrId = params.getFormLong(0);
        return JsonResult.success(agreementInfoService.getByAgrId(agrId));
    }

    /**
     * 签署协议
     */
    @RequestMapping(params = "method=sign")
    @PreAuthorize("@ss.hasPermi('agreement:sign')")
    public JsonResult sign(RequestParams params) {
        Long agrId = params.getFormLong(0);
        agreementInfoService.sign(agrId);
        return JsonResult.success();
    }
}
