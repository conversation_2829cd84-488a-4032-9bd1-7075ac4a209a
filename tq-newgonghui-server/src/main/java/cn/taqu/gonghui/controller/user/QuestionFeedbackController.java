package cn.taqu.gonghui.controller.user;


import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.entity.QuestionFeedback;
import cn.taqu.gonghui.system.search.QuestionFeedbackSearch;
import cn.taqu.gonghui.system.service.QuestionFeedbackService;
import cn.taqu.gonghui.system.vo.QuestionFeedbackVo;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户端 用户意见反馈
 */
@RequestMapping(value = "/api", params = "service=feedback")
@RestController
public class QuestionFeedbackController {

    @Autowired
    QuestionFeedbackService feedbackService;

    /**
     * 用户端 - 用户反馈列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=list")
    @PreAuthorize("@ss.hasPermi('feedback:list')")
    public JsonResult list(RequestParams params){
        Integer page = params.getFormIntegerDefault(0, 1);
        Integer pageSize = params.getFormIntegerDefault(1, 20);
        QuestionFeedbackSearch search = new QuestionFeedbackSearch();
        search.setPage(page);
        search.setPageSize(pageSize);
        List<QuestionFeedbackVo> vos = feedbackService.userList(search);
        return JsonResult.success(new PageInfo<>(vos));
    }

    /**
     * 用户端 - 提交用户反馈
     * @param params
     * @return
     */
    @RequestMapping(params = "method=add")
    @PreAuthorize("@ss.hasPermi('feedback:add')")
    public JsonResult add(RequestParams params){
        String pageName = params.getFormStringOption(0);
        String pageUrl = params.getFormStringOption(1);
        String title = params.getFormStringOption(2);
        String content = params.getFormStringOption(3);
        String imageUrl = params.getFormStringOption(4);
        Integer type = params.getFormIntegerDefault(5,1);
        Integer businessType = params.getFormIntegerDefault(6,null);

        // 上传图片地址参数类型转换
        // 最多只能上传5个附件
        List<String> urlList = null;
        String urlListStr = "";
        if (StringUtils.isNotBlank(imageUrl)) {
            Gson gson = new GsonBuilder().create();
            urlList = gson.fromJson(imageUrl, new TypeToken<List<String>>() {}.getType());
        }
        if (CollectionUtils.isNotEmpty(urlList)) {
            if (urlList.size() > 5) {
                return JsonResult.failed("最多上传5个附件");
            }
            for (String url : urlList) {
                urlListStr = urlListStr + url + ",";
            }
            urlListStr = urlListStr.substring(0,urlListStr.length() - 1);
        }

        QuestionFeedback record = new QuestionFeedback();
        record.setContent(content);
        record.setImageUrl(urlListStr);
        record.setTitle(title);
        record.setPageName(pageName);
        record.setPageUrl(pageUrl);
        record.setType(type);
        feedbackService.add(record,businessType);
        return JsonResult.success("意见反馈已提交");
    }
}
