package cn.taqu.gonghui.controller.user;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.service.SysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2021/5/11
 */
@RestController
@RequestMapping(value = "/api", params = "service=role")
public class UserRoleController {

    @Autowired
    private SysRoleService sysRoleService;

    /**
     * 返回 负责人和经纪人 角色列表
     */

    @RequestMapping(params = "method=getTeamRoleList")
//    @PreAuthorize("@ss.hasPermi('role:getTeamRoleList')")
    public JsonResult getTeamRoleList(RequestParams params){
        Integer type = params.getFormInteger(0);
        return JsonResult.success(sysRoleService.getLeaderAndAgent(type));
    }


    /**
     *
     * 返回下拉角色列表
     */

    @RequestMapping(params = "method=getRoleSelect")
    public JsonResult getRoleSelect(RequestParams params){
        return JsonResult.success(sysRoleService.getRoleSelect());
    }
}
