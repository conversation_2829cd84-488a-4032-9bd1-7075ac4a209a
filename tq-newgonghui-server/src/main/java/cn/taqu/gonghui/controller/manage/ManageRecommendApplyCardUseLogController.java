package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.entity.RecommendApplyCardUseLog;
import cn.taqu.gonghui.system.search.RecommendApplyCardUseLogSearch;
import cn.taqu.gonghui.system.service.RecommendApplyCardUseLogService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 营销管理（推荐位申请卡使用记录管理）
 */
@RestController
@RequestMapping(value = "/api",params = "service=manageRecommendApplyCardUseLog")
public class ManageRecommendApplyCardUseLogController {

    @Autowired
    private RecommendApplyCardUseLogService applyCardUseLogService;

    /**
     * 列表查询
     * @param params
     * @return
     */
    @RequestMapping(params = "method=pageList")
    public JsonResult pageList(RequestParams params){
        String formStr = params.getFormStringOption(0);
        RecommendApplyCardUseLogSearch search = JsonUtils.stringToObject2(formStr, RecommendApplyCardUseLogSearch.class);
        List<RecommendApplyCardUseLog> recommendApplyCardUseLogs = applyCardUseLogService.pageList(search);
        return JsonResult.success(new PageInfo<>(recommendApplyCardUseLogs));
    }
}
