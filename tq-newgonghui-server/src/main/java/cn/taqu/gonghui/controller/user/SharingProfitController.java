package cn.taqu.gonghui.controller.user;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.common.domain.TreeSelect;
import cn.taqu.gonghui.system.service.SysSharingProfitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 分润管理 - 用户端
 */
@RestController
@RequestMapping(value = "api", params = "service=sharingProfit")
public class SharingProfitController {

    @Autowired
    private SysSharingProfitService profitService;

    /**
     * 获取有效分润比例下拉tree
     * @return
     */
    @RequestMapping(params = "method=tree")
    @PreAuthorize("@ss.hasPermi('sharingProfit:tree')")
    public JsonResult tree(RequestParams params){
        List<CommonSelect> tree = profitService.tree();
        return JsonResult.success(tree);
    }


    /**
     * 获取有效分润比例列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getValus")
    public JsonResult getValus(RequestParams params){
        return JsonResult.success(profitService.getValues());
    }
}
