package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.search.TeamHostOperateLogSearch;
import cn.taqu.gonghui.system.service.TeamHostService;
import cn.taqu.gonghui.system.vo.TeamHostOperateLogVo;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-10 15:45
 */
@RestController
@RequestMapping(value = "/api",params = "service=manageLog")
public class ManageLogController {

    @Autowired
    private TeamHostService teamHostService;

    @RequestMapping(params = "method=getLiveTeamChangLogPage")
    public JsonResult getLiveTeamChangLogPage(RequestParams params){
        Integer pageNum = params.getFormInteger(0);
        Integer pageSize = params.getFormInteger(1);
        String accountUuid = params.getFormStringDefault(2, null);
        Long startTime = params.getFormLongDefault(3, null);
        Long endTime = params.getFormLongDefault(4, null);
        String operator = params.getFormStringDefault(5, null);

        TeamHostOperateLogSearch search = new TeamHostOperateLogSearch();
        search.setHostUuid(accountUuid);
        search.setPage(pageNum);
        search.setPageSize(pageSize);
        search.setStartTime(startTime);
        search.setEndTime(endTime);
        search.setOperator(operator);
        List<TeamHostOperateLogVo> logList = teamHostService.getLiveChangeTeamLogList(search);
        return JsonResult.success(new PageInfo<>(logList));
    }

}
