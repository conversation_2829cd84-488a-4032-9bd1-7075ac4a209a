package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.entity.Notice;
import cn.taqu.gonghui.system.search.NoticeSearch;
import cn.taqu.gonghui.system.service.NoticeService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公告管理
 */
@RestController
@RequestMapping(value = "/api",params = "service=manageNotice")
public class ManageNoticeController {

    @Autowired
    private NoticeService noticeService;

    /**
     * 分页列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=pageList")
    public JsonResult pageList(RequestParams params){
        Integer pageNum = params.getFormIntegerDefault(0, 1);
        Integer pageSize = params.getFormIntegerDefault(1, 15);
        String formString = params.getFormStringOption(2);
        NoticeSearch noticeSearch = null;
        if (formString.equals("{}")) {
            noticeSearch = new NoticeSearch();
        } else {
            noticeSearch = JsonUtils.stringToObject2(formString, NoticeSearch.class);
        }
        noticeSearch.setPageSize(pageSize);
        noticeSearch.setPageNum(pageNum);
        List<Notice> pagelist = noticeService.pagelist(noticeSearch);
        return JsonResult.success(new PageInfo<>(pagelist));
    }

    /**
     * 添加
     * @param params
     * @return
     */
    @RequestMapping(params = "method=add")
    public JsonResult add(RequestParams params){
        String formString = params.getFormString(0);
        String operator = params.getFormString(1);
        Notice record = JsonUtils.stringToObject2(formString, Notice.class);
        record.setOperator(operator);
        noticeService.add(record);
        return JsonResult.success("操作成功");
    }

    /**
     * 更新
     * @param params
     * @return
     */
    @RequestMapping(params = "method=modify")
    public JsonResult modify(RequestParams params){
        String formString = params.getFormString(0);
        String operator = params.getFormString(1);
        Notice record = JsonUtils.stringToObject2(formString, Notice.class);
        record.setOperator(operator);
        noticeService.modify(record);
        return JsonResult.success("操作成功");
    }

    /**
     * 详情
     * @param params
     * @return
     */
    @RequestMapping(params = "method=detail")
    public JsonResult detail(RequestParams params){
        Long id = params.getFormLong(0);
        return JsonResult.success(noticeService.detail(id));
    }

    /**
     * 删除
     * @param params
     * @return
     */
    @RequestMapping(params = "method=remove")
    public JsonResult remove(RequestParams params){
        Long id = params.getFormLong(0);
        noticeService.remove(id);
        return JsonResult.success("操作成功");
    }
}
