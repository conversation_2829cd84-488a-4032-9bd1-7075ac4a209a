package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.TeamOperateTypeEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.domain.CommonSelect;
import cn.taqu.gonghui.common.service.VerifyService;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.search.TeamSearch;
import cn.taqu.gonghui.system.service.TeamOperateService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.vo.TeamOperateRequestVo;
import cn.taqu.gonghui.system.vo.TeamVo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队管理
 */
@RestController
@RequestMapping(value = "/api", params = "service=manageTeam")
public class ManageTeamController {


    @Autowired
    private TeamService teamService;
    @Autowired
    private TeamOperateService teamOperateService;
    @Autowired
    private VerifyService verifyService;

    /**
     * 获取团队列表
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=list")
    public JsonResult list(RequestParams params) {
        String formStringOption = params.getFormStringOption(0);
        Integer pageNum = params.getFormIntegerOption(1);
        Integer pageSize = params.getFormIntegerOption(2);
        TeamSearch search = JsonUtils.stringToObject2(formStringOption, TeamSearch.class);
        search.setPageNum(pageNum);
        search.setPageSize(pageSize);
        List<TeamVo> teamVos = teamService.selectTeamList(search);
        return JsonResult.success(new PageInfo<>(teamVos));
    }

    @RequestMapping(params = "method=detail")
    public JsonResult detail(RequestParams params) {
        Long teamId = params.getFormLong(0);
        return JsonResult.success(teamService.detail(teamId));
    }

    /**
     * 新增团队
     *
     * @return
     */
    @RequestMapping(params = "method=add")
    public JsonResult add(RequestParams params) {
        String record = params.getFormString(0);
        String createBy = params.getFormString(1);
        Team team = JsonUtils.stringToObject2(record, Team.class);
        team.setCreateBy(createBy);
        teamService.insertTeam(team);
        return JsonResult.success("添加成功");
    }

    /**
     * 修改团队信息
     *
     * @return
     */
    @RequestMapping(params = "method=modify")
    public JsonResult modify(RequestParams params) {
        String formString = params.getFormString(0);
        Team team = JsonUtils.stringToObject2(formString, Team.class);
        teamService.updateTeam(team);
        return JsonResult.success("修改成功");
    }

    /**
     * 解散团队
     * 1.默认团队不可解散
     * 2.团队解散之后，所有人员默认归入默认团队
     * 3.团队负责人归入默认团队，身份为负责人
     *
     * @return
     */
    @RequestMapping(params = "method=remove")
    public JsonResult remove(RequestParams params) {
        Long teamId = params.getFormLong(0);
        teamService.remove(teamId);
        return JsonResult.success("删除成功");
    }

    /**
     * 启用禁用团队
     * 1.所属团队成员依然可以正常登陆后台，但是点击任意功能提示团队异常（其他也行）
     * 2.不影响主播正常开播
     * 3.无法调整团队成员的任意操作
     *
     * @return
     */
    @RequestMapping(params = "method=startOrForbidden")
    public JsonResult startOrForbidden(RequestParams params) {
        Long teamId = params.getFormLong(0);
        Integer status = params.getFormInteger(1);
        teamService.startOrForbidden(teamId, status);
        return JsonResult.success("操作成功");
    }

    /**
     * 获取团队标识符
     *
     * @return
     */
    @RequestMapping(params = "method=getTeamSign")
    public JsonResult getTeamSign(RequestParams params) {
        return JsonResult.success(teamService.getTeamSignList());
    }

    /**
     * 获取团队下拉tree
     */
    @RequestMapping(params = "method=tree")
    public JsonResult tree(RequestParams params) {
        Long orgId = params.getFormLongOption(0);
        Integer type = params.getFormIntegerOption(1);
        List<Team> teams = teamService.selectTeamList(orgId, type);
        return JsonResult.success(teams.stream().map(CommonSelect::new).collect(Collectors.toList()));
    }


    /**
     * 获取所有直播团队下拉tree
     */
    @RequestMapping(params = "method=liveTeamTree")
    public JsonResult liveTeamTree(RequestParams params) {
        return JsonResult.success(teamService.getTeamListByBusinessType(TeamTypeEnum.LIVE_TEAM.getValue()));
    }

    /**
     * 获取所有聊天室团队下拉tree
     */
    @RequestMapping(params = "method=chatTeamTree")
    public JsonResult chatTeamTree(RequestParams params) {
        return JsonResult.success(teamService.getTeamListByBusinessType(TeamTypeEnum.TALK_TEAM.getValue()));
    }

    /**
     * 管理端批量调整主播所属公会 下拉
     */
    @RequestMapping(params = "method=teamList")
    public JsonResult teamList(RequestParams params) {
        return JsonResult.success(teamService.sharingRecordTeamList());
    }

    /**
     * 迁移团队到新公会
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=teamMove")
    public JsonResult teamMove(RequestParams params) {
        // 校验图形验证码
        String sceneId = params.getFormStringOption(0);
        String code = params.getFormStringOption(1);
        Boolean verifyResult = verifyService.verifyCaptcha(sceneId, code);
        if (!verifyResult) {
            throw new ServiceException("verify_captcha_error", "验证码错误");
        }
        TeamOperateRequestVo vo = new TeamOperateRequestVo();
        vo.setOldOrgId(params.getFormLongOption(2));
        vo.setOldTeamId(params.getFormLongOption(3));
        vo.setNewOrgId(params.getFormLongOption(4));
        vo.setNewTeamId(params.getFormLongDefault(5, null));
        teamOperateService.operateTeam(vo);
        return JsonResult.success("成功");
    }

    /**
     * 迁移团队，获取团队信息
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=teamInfo")
    public JsonResult teamInfo(RequestParams params) {
        Long teamId = params.getFormLongOption(0);
        TeamVo info = teamService.info(teamId);
        return JsonResult.success(info);
    }

    @RequestMapping(params = "method=teamMoveHistory")
    public JsonResult teamMoveHistory(RequestParams params) {
        int pageNo = params.getFormIntegerDefault(0, 1);
        int pageSize = params.getFormIntegerDefault(1, 20);
        return JsonResult.success(teamOperateService.getOperateList(pageNo, pageSize, TeamOperateTypeEnum.TEAM_MOVE.ordinal()));
    }

    @RequestMapping(params = "method=teamStatus")
    public JsonResult teamStatus(RequestParams params) {
        Long[] teamIdArray = params.getFormLongArray(0);
        if (teamIdArray.length > 100) {
            throw new ServiceException("too_long", "长度不要超过100个");
        }
        return JsonResult.success(teamService.getTeamStatus(Lists.newArrayList(teamIdArray)));
    }
}
