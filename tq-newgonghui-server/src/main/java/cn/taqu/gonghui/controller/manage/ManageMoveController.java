package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.live.service.ManangeOrgTransferService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/6/23
 */
@RestController
@RequestMapping(value = "/api",params = "service=manageMove")
@Slf4j
public class ManageMoveController {

    @Autowired
    private ManangeOrgTransferService transferService;

    /**
     * 手动机构数据迁移
     */
    @RequestMapping(params = "method=moveData")
    public JsonResult moveData(RequestParams params){
        String oldOrgUuid = params.getFormStringOption(0);
        Long orgId = params.getFormLongOption(1);
//        Long teamId = params.getFormLong(2);
        log.info("moveData请求参数,oldOrgUuid={},orgId={}",oldOrgUuid,orgId);
        transferService.handOperateAgentAndEmployee(oldOrgUuid,orgId);
        return JsonResult.success("迁移成功");
    }

    /**
     * 迁移校验
     */
    @RequestMapping(params = "method=valid")
    public JsonResult valid(RequestParams params){
        String oldOrgUuid = params.getFormStringOption(0);
        Long orgId = params.getFormLongOption(1);
//        Long teamId = params.getFormLong(2);
        Map<String, String> map = transferService.handOperateValid(oldOrgUuid, orgId);
        return JsonResult.success(map);
    }

    /**
     * 获取老公会下拉
     */
    @RequestMapping(params = "method=tree")
    public  JsonResult tree(RequestParams params){
        return JsonResult.success(transferService.tree());
    }
}
