package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.service.RegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping(value = "/api", params = "service=region")
@RestController
public class RegionController {
    @Autowired
    private RegionService regionService;


    @RequestMapping(params = "method=findRegionList")
    public JsonResult findRegionList() {
        return JsonResult.success(regionService.findRegionList());
    }
}
