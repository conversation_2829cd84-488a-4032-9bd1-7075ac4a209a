package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.UserConstants;
import cn.taqu.gonghui.common.domain.TreeSelect;
import cn.taqu.gonghui.system.entity.SysMenu;
import cn.taqu.gonghui.system.service.SysMenuService;
import cn.taqu.gonghui.system.vo.SysMenuVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@RequestMapping(value = "/api",params = "service=manageMenu")
public class ManageSysMenuController {

    @Autowired
    private SysMenuService sysMenuService;

    /**
     * 管理端调用
     * 查询所有菜单列表
     */
    @RequestMapping(params = "method=list")
    public JsonResult list(RequestParams params){
        String formStringOption = params.getFormStringOption(0);
        SysMenu menu = JsonUtils.stringToObject2(formStringOption, SysMenu.class);
        List<SysMenu> menus = sysMenuService.selectMenuList(menu);
        List<SysMenu> sysMenus = sysMenuService.buildMenuTree(menus);
        return JsonResult.success(sysMenus);
    }

    /**
     * 管理端调用（角色管理新增获取有效菜单下拉）
     * 获取菜单下拉tree
     */
    @RequestMapping(params = "method=tree")
    public JsonResult tree(RequestParams params){
        Integer type = params.getFormIntegerDefault(0,null);
        Integer menuType = params.getFormIntegerDefault(1,null);
        String menuTypeStr = "";
        if (0 == menuType) {
            menuTypeStr = "M,C";
        }
        SysMenu menu = new SysMenu();
        menu.setStatus("1");
        menu.setMenuType(menuTypeStr);
        menu.setType(type);
        List<SysMenu> menus = sysMenuService.selectMenuList(menu);
        List<TreeSelect> treeSelects = sysMenuService.buildMenuTreeSelect(menus);
        return JsonResult.success(treeSelects);
    }

    /**
     * 管理端调用
     * 根据menuId获取当前菜单详情
     */
    @RequestMapping(params = "method=detail")
    public JsonResult detail(RequestParams params){
        Long menuId = params.getFormLong(0);
        SysMenu sysMenu = sysMenuService.selectMenuById(menuId);
        return JsonResult.success(sysMenu);
    }

    /**
     * 管理端调用(角色管理编辑回显角色拥有菜单)
     * 根据roleId获取当前角色下的有效菜单下拉tree
     */
    @RequestMapping(params = "method=roleMenuTreeSelect")
    public JsonResult roleMenuTreeSelect(RequestParams params){
        Long roleId = params.getFormLong(0);
        Integer type = params.getFormIntegerDefault(1,null);
        Integer menuType = params.getFormIntegerDefault(2,null);
        String menuTypeStr = "";
        if (0 == menuType) {
            menuTypeStr = "M,C";
        }
        SysMenu menu = new SysMenu();
        menu.setStatus("1");
        menu.setMenuType(menuTypeStr);
        menu.setType(type);
        List<SysMenu> menus = sysMenuService.selectMenuList(menu);
        Map<String, Object> map = new HashMap<>();
        map.put("checkedKeys",sysMenuService.selectMenuListByRoleId(roleId));
        map.put("menus",sysMenuService.buildMenuTreeSelect(menus));
        return JsonResult.success(map);
    }

    /**
     * 新增菜单
     */
    @RequestMapping(params = "method=add")
    public JsonResult add(RequestParams params){
        String formString = params.getFormString(0);
        String createBy = params.getFormString(1);
        SysMenu menu = JsonUtils.stringToObject2(formString, SysMenu.class);
        if (UserConstants.NOT_UNIQUE.equals(sysMenuService.checkMenuNameUnique(menu))) {
            return JsonResult.failed("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        }
        menu.setCreateBy(createBy);
        sysMenuService.insertMenu(menu);
        return JsonResult.success("操作成功");
    }

    /**
     * 更新菜单
     */
    @RequestMapping(params = "method=modify")
    public JsonResult modify(RequestParams params){
        String formString = params.getFormString(0);
        String updateBy = params.getFormString(1);
        SysMenu menu = JsonUtils.stringToObject2(formString, SysMenu.class);
        if (UserConstants.NOT_UNIQUE.equals(sysMenuService.checkMenuNameUnique(menu))) {
            return JsonResult.failed("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        }
        if (menu.getMenuId().equals(menu.getParentId())) {
            return JsonResult.failed("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
        }
        // 如果当前菜单被分配或者有子菜单，不能被停用
        if (!sysMenuService.selectMenuById(menu.getMenuId()).getStatus().equals(menu.getStatus())) {
            if (menu.getStatus().equals("0") && sysMenuService.selectMenuById(menu.getMenuId()).getStatus().equals("1")) {
                if (sysMenuService.hasChildByMenuId(menu.getMenuId())) {
                    return JsonResult.failed("存在子菜单,不允许被停用");
                }
                if (sysMenuService.checkMenuExistRole(menu.getMenuId())) {
                    return JsonResult.failed("菜单已分配,不允许被停用");
                }
            }
        }
        menu.setUpdateBy(updateBy);
        sysMenuService.updateMenu(menu);
        return JsonResult.success("操作成功");
    }

    /**
     * 删除菜单
     */
    @RequestMapping(params = "method=delete")
    public JsonResult delete(RequestParams params){
        Long menuId = params.getFormLong(0);
        if (sysMenuService.hasChildByMenuId(menuId)) {
            return JsonResult.failed("存在子菜单,不允许删除");
        }
        if (sysMenuService.checkMenuExistRole(menuId)) {
            return JsonResult.failed("菜单已分配,不允许删除");
        }
        sysMenuService.deleteMenuById(menuId);
        return JsonResult.success("操作成功");
    }
}
