package cn.taqu.gonghui.controller.manage;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.ResetTypeEnum;
import cn.taqu.gonghui.common.vo.RemitSearchVO;
import cn.taqu.gonghui.common.vo.req.ResetHostReq;
import cn.taqu.gonghui.system.dto.HostChangeInfoDTO;
import cn.taqu.gonghui.system.dto.HostLastProfitDTO;
import cn.taqu.gonghui.system.entity.SysSharingProfit;
import cn.taqu.gonghui.system.search.SysSharingProfitSearch;
import cn.taqu.gonghui.system.service.HostSharingProfitRecordService;
import cn.taqu.gonghui.system.service.SysSharingProfitService;
import cn.taqu.gonghui.system.service.TeamHostService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 分润管理
 */
@RestController
@RequestMapping(value = "/api",params = "service=manageSharingProfit")
@Slf4j
public class ManageSharingProfitController {

    @Autowired
    private SysSharingProfitService sharingProfitService;
    @Autowired
    private TeamHostService teamHostService;
    @Autowired
    private HostSharingProfitRecordService hostSharingProfitRecordService;

    /**
     * 列表
     * @param params
     * @return
     */
    @RequestMapping(params = "method=list")
    public JsonResult list(RequestParams params){
        List<SysSharingProfit> list = sharingProfitService.list(new SysSharingProfitSearch());
        return JsonResult.success(list);
    }

    /**
     * 添加
     */
    @RequestMapping(params = "method=add")
    public JsonResult add(RequestParams params){
        String formString = params.getFormString(0);
        String createBy = params.getFormString(1);
        SysSharingProfit record = JsonUtils.stringToObject2(formString, SysSharingProfit.class);
        record.setCreateBy(createBy);
        sharingProfitService.add(record);
        return JsonResult.success("操作成功");
    }

    /**
     * 启用/禁用
     * @return
     */
    @RequestMapping(params = "method=changeStatus")
    public JsonResult changeStatus(RequestParams params){
        Long id = params.getFormLong(0);
        Integer status = params.getFormInteger(1);
        sharingProfitService.changeStatus(id,status);
        return JsonResult.success("操作成功");
    }

    @RequestMapping(params = "method=tree")
    public JsonResult tree(RequestParams params){
        return JsonResult.success(sharingProfitService.getManageValues());
    }

    /**
     * 根据主播uuid获取主播当前分润比例
     * @return
     */
    @RequestMapping(params = "method=getRateByHostUuids")
    public JsonResult getRateByHostUuids(RequestParams params){
        String[] hostUuids = params.getFormStringArray(0);
        log.info("getRateByHostUuids请求参数，hostUuids={}", JSON.toJSONString(hostUuids));
        ArrayList<String> uuidList = new ArrayList<>(Arrays.asList(hostUuids)) ;
        return JsonResult.success(teamHostService.getHostSharingProfitInfo(uuidList));
    }

    /**
     * 重置公会主播分润比例为0
     * @param params
     * @return
     */
    @RequestMapping(params = "method=resetProfit")
    public JsonResult resetProfit(RequestParams params) {
        String form = params.getFormStringDefault(0, "");
        ResetHostReq resetHostReq = JsonUtils.stringToObject(form, ResetHostReq.class);
        if (resetHostReq.getType().equals(ResetTypeEnum.ALL.getCode())) {
            return JsonResult.failed("http不允许全部");
        }
        List<String> res = hostSharingProfitRecordService.resetProfit(resetHostReq);
        return JsonResult.success(res);
    }

    /**
     * 查询重置后是否有发起分润调整
     * @param params
     * @return
     */
    @RequestMapping(params = "method=resetAndChange")
    public JsonResult resetAndChange(RequestParams params) {
        String hostUuid = params.getFormStringDefault(0, "");
        HostChangeInfoDTO hostChangeInfo = hostSharingProfitRecordService.getResetChangeInfo(hostUuid);
        log.info("resetAndChange,hostUuid:{},res:{}", hostUuid, hostChangeInfo.getIsResetAndChange());
        return JsonResult.success(hostChangeInfo);
    }

    /**
     * 查询上一次的分润比例
     * @param params
     * @return
     */
    @RequestMapping(params = "method=lastProfit")
    public JsonResult lastProfit(RequestParams params) {
        String hostUuid = params.getFormStringDefault(0, "");
        HostLastProfitDTO hostLastProfitDTO = hostSharingProfitRecordService.getLastProfit(hostUuid);
        return JsonResult.success(hostLastProfitDTO);
    }

}
