package cn.taqu.gonghui.controller.user;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.system.search.SharingProfitRecordSearch;
import cn.taqu.gonghui.system.service.HostSharingProfitRecordService;
import cn.taqu.gonghui.system.vo.HostSharingProfitRecordVo;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 调整分润比例记录控制器
 * @user guojiajun
 */
@RestController
@RequestMapping(value = "/api", params = "service=sharingProfitRecord")
public class SharingProfitRecordController {

    @Autowired
    private HostSharingProfitRecordService sharingProfitRecordService;

    @RequestMapping(params = "method=list")
    @PreAuthorize("@ss.hasPermi('sharingProfitRecord:list')")
    public JsonResult list(RequestParams params){
        String formString = params.getFormString(0);
        SharingProfitRecordSearch search = JsonUtils.stringToObject2(formString, SharingProfitRecordSearch.class);
        List<HostSharingProfitRecordVo> voList = sharingProfitRecordService.clientList(search);
        return JsonResult.success(new PageInfo<>(voList));
    }
}
