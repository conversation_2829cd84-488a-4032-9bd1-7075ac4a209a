package cn.taqu.gonghui.controller.manage;

import cn.hutool.core.util.PageUtil;
import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.Constants;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.common.utils.PageResult;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.common.vo.RemitSearchVO;
import cn.taqu.gonghui.system.service.OrganizationService;
import cn.taqu.gonghui.system.vo.RemitItemVO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/11/3 15:49
 */
@RestController
@RequestMapping(value = "api", params = "service=financeManage")
@Slf4j
public class FinanceManageController {

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private TokenService tokenService;

    @EtcdValue("biz.whiteList.remitAllowUserJson")
    private static String allowUserJson;

    /**
     * 导出最大数
     */
    private final int EXPORT_MAX_SIZE = 9999;

    /**
     * 机构关闭审核接口
     * @param params
     * @return
     */
    @RequestMapping(params = "method=remitList")
    public JsonResult remitList(RequestParams params) {
        String formStr = params.getFormStringDefault(0, "");
        Integer pageNum = params.getFormIntegerDefault(1, 0);
        Integer pageSize = params.getFormIntegerDefault(2, 10);
        RemitSearchVO remitSearchVO = JsonUtils.stringToObject(formStr, RemitSearchVO.class);

        PageInfo pageInfo = new PageInfo<>();
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        Integer isExport = Optional.ofNullable(remitSearchVO).map(RemitSearchVO::getIsExport).orElse(0);
//        if (isExport.equals(Constants.ONE)) {
//            pageInfo.setPageNum(Constants.ONE);
//            pageInfo.setPageSize(EXPORT_MAX_SIZE);
//        }

        // 维护角色权限
        List<String> allowUserList = JsonUtils.stringToObject(allowUserJson, new TypeReference<List<String>>() {});
        if (!allowUserList.contains(remitSearchVO.getLoginName())) {
            return JsonResult.success();
        }

        if(isExport.equals(Constants.ONE)){ // 如果是导出的情况，分页获取
            List<RemitItemVO> allDataList = new ArrayList<>();
            pageNum = 1;
            pageInfo.setPageNum(pageNum);
            pageSize = 200;
            pageInfo.setPageSize(pageSize);
            PageInfo<RemitItemVO> firstPage = organizationService.getRemitPageList(remitSearchVO, pageInfo);
            allDataList.addAll(firstPage.getList());
            long totalPage = PageUtil.totalPage((int) firstPage.getTotal(), pageSize); // 总页数
            while (pageNum < totalPage){
                pageNum ++;
                pageInfo.setPageNum(pageNum);
                PageInfo<RemitItemVO> pageResult = organizationService.getRemitPageList(remitSearchVO, pageInfo);
                if(CollectionUtils.isNotEmpty(pageResult.getList())){
                    allDataList.addAll(pageResult.getList());
                }
            }
            PageInfo<RemitItemVO> allPageResult = new PageInfo<>();
            allPageResult.setPageNum(1);
            allPageResult.setPageSize(200);
            allPageResult.setTotal(firstPage.getTotal());
            allPageResult.setList(allDataList);
            return JsonResult.success(allPageResult);
        }
        // 如果是导出 就把pageSize设置最大 并且page第一页
        PageInfo<RemitItemVO> remitItemVOPageInfo = organizationService.getRemitPageList(remitSearchVO, pageInfo);



        return JsonResult.success(remitItemVOPageInfo);
    }

}
