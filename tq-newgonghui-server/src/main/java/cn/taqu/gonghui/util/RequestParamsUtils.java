package cn.taqu.gonghui.util;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.http.RequestParams;

public class RequestParamsUtils {


    public static <T> T parseObject(String form, Class<T> clz) {
        return parseObject(form,0,clz);
    }

    public static <T> T parseObject(String form,int idx, Class<T> clz) {
        RequestParams requestParams = new RequestParams();
        requestParams.setForm(form);
        String json = requestParams.getFormString(idx);
        return JsonUtils.stringToObject2(json, clz);
    }

}
