package cn.taqu.gonghui.util;

import ch.qos.logback.classic.PatternLayout;
import ch.qos.logback.classic.pattern.TargetLengthBasedClassNameAbbreviator;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.IThrowableProxy;
import ch.qos.logback.classic.spi.StackTraceElementProxy;
import ch.qos.logback.core.pattern.PatternLayoutEncoderBase;
import cn.taqu.core.common.constant.SystemConstant;
import cn.taqu.core.configuration.properties.ServiceProperty;
import cn.taqu.core.utils.*;
import cn.taqu.core.web.protocol.SerialSequenceWrapper;
import cn.taqu.core.web.protocol.SoaBaseParams;
import org.apache.commons.lang3.StringUtils;

public class GonghuiPatternLayoutEncoder extends PatternLayoutEncoderBase<ILoggingEvent> {
    private static final String LOG_SPLITER = "|";
    private static final String BLANK_STRING = " ";
    private static final String PLACEHOLDER = " - ";
    private static final String COLON = ":";
    private static final String[] LEVEL = {"trace", "debug", "info", "warning", "error"};
    private static final int LEVEL_MAX_INDEX = LEVEL.length - 1;
    private static final TargetLengthBasedClassNameAbbreviator targetLengthBasedClassNameAbbreviator
            = new TargetLengthBasedClassNameAbbreviator(15);
    @Override
    public void start() {
        PatternLayout patternLayout = new PatternLayout();
        patternLayout.setContext(context);
        patternLayout.setPattern(getPattern());
        patternLayout.setOutputPatternAsHeader(outputPatternAsHeader);
        patternLayout.start();
        this.layout = patternLayout;
        super.start();
    }

    @Override
    public byte[] encode(ILoggingEvent event) {
        String txt = layout.doLayout(event);
        return convertToBytes(doGonghuiLayout(event,txt));
    }

    private byte[] convertToBytes(String s) {
        if (getCharset() == null) {
            return s.getBytes();
        } else {
            return s.getBytes(getCharset());
        }
    }



    private String doGonghuiLayout(ILoggingEvent event,String message) {
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        StringBuilder msg = new StringBuilder();
        int levelInt = event.getLevel().levelInt/10000;
        IThrowableProxy throwable = event.getThrowableProxy();

        String service = soaBaseParams.getService();
        String method = soaBaseParams.getMethod();
        if(StringUtils.isBlank(service)) {
            StackTraceElement[] stackTraceElements = event.getCallerData();
            StackTraceElement startElement = stackTraceElements[stackTraceElements.length - 1];
            for(int i=stackTraceElements.length - 1; i>=0; i--) {
                StackTraceElement stackTraceElement = stackTraceElements[i];
                String className = stackTraceElement.getClassName();
                if(StringUtils.startsWith(className, "cn.taqu.") && !StringUtils.contains(className, "FastClassBySpringCGLIB")) {
                    startElement = stackTraceElement;
                    break;
                }
            }

            service = StringUtils.substringAfterLast(startElement.getClassName(), ".");
            method = startElement.getMethodName();
        }

        String uri = "v"+ SystemConstant.SYS_VERSION+"/"+service+"/"+method;

        //结构化日志数据
        msg.append(toFirstLogItem(levelInt<0||levelInt>LEVEL_MAX_INDEX?String.valueOf(levelInt):LEVEL[levelInt])).append(LOG_SPLITER);//日志级别
        msg.append(toLogItem(String.valueOf(Thread.currentThread().getId()))).append(LOG_SPLITER);//线程id
        msg.append(toLogItem(String.valueOf(event.getTimeStamp()))).append(LOG_SPLITER);//时间戳
        msg.append(toLogItem(DateUtil.getCurrentTime())).append(LOG_SPLITER);//时间
        msg.append(toLogItem(uri)).append(LOG_SPLITER);//uri
        msg.append(toLogItem(LocalConfUtil.getLocalEnv())).append(LOG_SPLITER);//环境online、auto等
        msg.append(toLogItem(StringUtil.nullNumberToEmptyString(soaBaseParams.getAppcode()))).append(LOG_SPLITER);//appcode
        msg.append(toLogItem(StringUtil.nullNumberToEmptyString(soaBaseParams.getCloned()))).append(LOG_SPLITER);//cloned
        msg.append(toLogItem(StringUtils.trimToEmpty(soaBaseParams.getClientUri()))).append(LOG_SPLITER);//client_uri
        msg.append(toLogItem(RequestUtils.getClientIp())).append(LOG_SPLITER);//转发器ip
        msg.append(toLogItem(soaBaseParams.getOrigin())).append(LOG_SPLITER);//表示api接口或soa接口
        msg.append(toLogItem(StringUtils.trimToEmpty(ServiceProperty.getIndex()))).append(LOG_SPLITER);//系统名称
        msg.append(toLogItem(serialSequence(soaBaseParams))).append(LOG_SPLITER);//日志时序
        msg.append(toLogItem(LocalConfUtil.getLocalIp())).append(LOG_SPLITER);//本机ip
        msg.append(toLogItem(soaBaseParams.getMtracerId())).append(LOG_SPLITER);//mtracer_id
        msg.append(toLogItem(SystemConstant.SYS_VERSION)).append(LOG_SPLITER);//访问的版本号
        msg.append(toLogItem(service)).append(LOG_SPLITER);//interface
        msg.append(toLogItem(method)).append(LOG_SPLITER);//method
        msg.append(toLogItem(soaBaseParams.getDistinctRequestId())).append(LOG_SPLITER);//分布式distinctRequestId
        msg.append(toLogItem(soaBaseParams.getToken())).append(LOG_SPLITER);//用户token
        msg.append(toLogItem(soaBaseParams.getPlatformName())).append(LOG_SPLITER);//平台名称
        msg.append(toLogItem(StringUtil.nullNumberToEmptyString(soaBaseParams.getAppVersion()))).append(LOG_SPLITER);//app版本号
        msg.append(toLogItem(soaBaseParams.getAccess())).append(LOG_SPLITER);//用户访问使用的网络
        msg.append(toLogItem(String.valueOf(soaBaseParams.getNextOffsetTimeMills()))).append(LOG_SPLITER);//时间差值
        msg.append(toLogItem(String.valueOf(soaBaseParams.getAndResetDurationTimeMills()))).append(LOG_SPLITER);//操作的持续时间
        // 增加pod相关信息
        msg.append(toLogItem(EnviromentUtil.getProperties(EnviromentUtil.POD_NAME))).append(LOG_SPLITER);
        msg.append(toLogItem(EnviromentUtil.getProperties(EnviromentUtil.POD_NAMESPACE))).append(LOG_SPLITER);
        msg.append(toLogItem(EnviromentUtil.getProperties(EnviromentUtil.POD_IP))).append(LOG_SPLITER);
        msg.append(toLogItem(EnviromentUtil.getProperties(EnviromentUtil.NODE_NAME))).append(LOG_SPLITER);

        msg.append(toLastLogItem( message));//日志内容
        msg.append(System.lineSeparator());
        //异常堆栈
        appendEnclosingThrowable(throwable, msg);
        return msg.toString();
    }

    private String serialSequence(SoaBaseParams soaRequestWrapper) {
        SerialSequenceWrapper r = soaRequestWrapper.getSerialSequenceWrapper();
        StringBuilder builder = new StringBuilder();
        return builder.append(r.getPrevSerialSequence()).append(r.getSoaSequence()).append(".").append(r.getAndIncLogSequence()).toString();
    }

    private void appendEnclosingThrowable(IThrowableProxy throwable, StringBuilder msg) {
        IThrowableProxy _throwable = throwable;
        StackTraceElementProxy[] enclosingTraces = null;
        while(_throwable != null) {
            msg.append(_throwable.getClassName()).append(COLON).append(BLANK_STRING).append(_throwable.getMessage()).append(System.lineSeparator());
            StackTraceElementProxy[] traces = _throwable.getStackTraceElementProxyArray();
            int m = traces==null ? -1 : traces.length - 1;
            int n = enclosingTraces==null ? -1 : enclosingTraces.length - 1;
            while (m >= 0 && n >=0 && traces[m].equals(enclosingTraces[n])) {
                m--; n--;
            }
            int framesInCommon = traces.length - 1 - m;

            for (int i = 0; i <= m; i++) {
                StackTraceElementProxy trace = traces[i];
                msg.append("\t").append(trace.toString()).append(System.lineSeparator());
            }

            if (framesInCommon > 0)
                msg.append("\t... ").append(framesInCommon).append(" more").append(System.lineSeparator());

            IThrowableProxy cause = _throwable.getCause();
            if(cause == _throwable) {
                break;
            }

            _throwable = cause;
            enclosingTraces = traces;
        }
    }

    private String toLogItem(String string) {
        if(StringUtils.isBlank(string)) {
            return PLACEHOLDER;
        }
        return new StringBuilder().append(BLANK_STRING).append(string).append(BLANK_STRING).toString();
    }
    private String toFirstLogItem(String string) {
        if(StringUtils.isBlank(string)) {
            return PLACEHOLDER;
        }
        return new StringBuilder().append(string).append(BLANK_STRING).toString();
    }
    private String toLastLogItem(String string) {
        if(StringUtils.isBlank(string)) {
            return PLACEHOLDER;
        }
        return new StringBuilder().append(BLANK_STRING).append(string).toString();
    }
}
