package cn.taqu.gonghui.util;

import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/6/1 5:27 PM
 */
@UtilityClass
public class EnvUtil {

    public final String GRAY = "gray";

    public String getEnv() {
        String env = System.getenv("TQ_K8S_ENV");
        if (StringUtils.isBlank(env)) {
            env = "dev";
        }
        return env;
    }

    /**
     * 根据环境获取不同固定值
     * @param testValue
     * @param onlineValue
     * @return
     */
    public Integer getFixedValue(Integer testValue, Integer onlineValue) {
        return isOnlineOrGray() ? onlineValue : testValue;
    }

    /**
     * 是否线上或灰度
     * @return
     */
    public boolean isOnlineOrGray() {
        return getEnv().equals("online") || getEnv().equals("gray");
    }

}
