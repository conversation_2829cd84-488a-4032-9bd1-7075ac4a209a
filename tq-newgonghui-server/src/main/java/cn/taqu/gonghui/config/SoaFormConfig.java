package cn.taqu.gonghui.config;

import cn.taqu.core.soa.server.annotation.SoaParam;
import cn.taqu.gonghui.config.annotation.SoaForm;
import cn.taqu.gonghui.util.RequestParamsUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSet;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Set;

public class SoaFormConfig implements WebMvcConfigurer {

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(soaFormResolver());
    }


    /**
     * 该注解 支持将form进行解码后转换成对象
     * TODO 支持 valid
     *
     * @see SoaForm
     */
    private HandlerMethodArgumentResolver soaFormResolver() {
        return new HandlerMethodArgumentResolver() {

            private Set<String> supportContentTypeSet = ImmutableSet.of(
                    MediaType.APPLICATION_FORM_URLENCODED_VALUE,
                    MediaType.APPLICATION_JSON_VALUE);

            @Override
            public boolean supportsParameter(MethodParameter parameter) {
                if (!parameter.hasParameterAnnotation(SoaForm.class)) {
                    return false;
                }

                return true;
            }


            @Override
            public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {

                String contentType = webRequest.getHeader("Content-Type");
                if (!supportContentTypeSet.contains(contentType)) {
                    throw new RuntimeException("Content-Type不支持");
                }

                switch (contentType) {
                    case MediaType.APPLICATION_FORM_URLENCODED_VALUE:
                        return handle4Form(parameter, webRequest);
                    case MediaType.APPLICATION_JSON_VALUE:
                        return handle4Json(parameter, webRequest);
                }

                if (!StringUtils.equals(contentType, MediaType.APPLICATION_FORM_URLENCODED_VALUE)) {

                }
                String form = webRequest.getParameter("form");

                if (StringUtils.isEmpty(form)) {
                    throw new RuntimeException("form 为空");
                }
                int idx = parameter.getParameterIndex();
                Class paramClz = parameter.getParameterType();
                return RequestParamsUtils.parseObject(form, idx, paramClz);
            }


            private Object handle4Form(MethodParameter parameter, NativeWebRequest webRequest) throws Exception {
                String form = webRequest.getParameter("form");

                if (StringUtils.isEmpty(form)) {
                    throw new RuntimeException("form 为空");
                }
                int idx = parameter.getParameterIndex();
                Class paramClz = parameter.getParameterType();
                return RequestParamsUtils.parseObject(form, idx, paramClz);
            }

            private Object handle4Json(MethodParameter parameter, NativeWebRequest webRequest) throws Exception {
                HttpServletRequest servletRequest = webRequest.getNativeRequest(HttpServletRequest.class);
                String bodyStr = StreamUtils.copyToString(servletRequest.getInputStream(), Charset.defaultCharset());

                if (StringUtils.isEmpty(bodyStr)) {
                    throw new RuntimeException("body 为空");
                }

                Class paramClz = parameter.getParameterType();
                return JSON.parseObject(bodyStr, paramClz);
            }
        };
    }

}
