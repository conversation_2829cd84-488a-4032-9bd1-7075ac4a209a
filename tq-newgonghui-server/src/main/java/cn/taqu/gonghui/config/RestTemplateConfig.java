package cn.taqu.gonghui.config;

import org.springframework.boot.test.web.client.LocalHostUriTemplateHandler;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate localRestTemplate(Environment environment) {

        return new RestTemplateBuilder()
                .uriTemplateHandler(new LocalHostUriTemplateHandler(environment))
                .build();
    }
}
