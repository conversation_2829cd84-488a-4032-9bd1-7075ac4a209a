package cn.taqu.gonghui.config;

import com.alibaba.druid.pool.DruidDataSource;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.MeterBinder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/2/1 16 23
 * discription
 */
@Slf4j
@Component
public class DruidDataSourceMetrics implements MeterBinder, ApplicationContextAware {
    private ApplicationContext applicationContext;

    public boolean collect(MeterRegistry meterRegistry) {
        Map<String, DruidDataSource> datasources = applicationContext.getBeansOfType(DruidDataSource.class);
        if (datasources.values().contains(null)) {
            return false;
        }
        for (Map.Entry<String, DruidDataSource> entry : datasources.entrySet()) {
            Gauge.builder("druid.pool.error.count",()->(entry.getValue().getErrorCount())).tags("datasourcename", entry.getKey()).description("druid pool error count")
                    .register(meterRegistry);
            Gauge.builder("druid.pool.active.connections", () -> (entry.getValue().getActiveCount())).tags("datasourcename", entry.getKey()).description("druid pool active count")
                    .register(meterRegistry);
            Gauge.builder("druid.pool.idle.connections", () -> (entry.getValue().getPoolingCount())).tags("datasourcename", entry.getKey()).description("druid pool idle count")
                    .register(meterRegistry);
            Gauge.builder("druid.pool.wait.connection.thread", () -> (entry.getValue().getWaitThreadCount())).tags("datasourcename", entry.getKey()).description("druid pool wait thread count")
                    .register(meterRegistry);
            Gauge.builder("druid.pool.sum.connections", () -> ((entry.getValue().getPoolingCount() + entry.getValue().getActiveCount()))).tags("datasourcename", entry.getKey()).description("druid pool sum count")
                    .register(meterRegistry);
        }
        return true;
    }

    public void bindTo(MeterRegistry meterRegistry) {
        try {
            Executors.newSingleThreadScheduledExecutor().schedule(() -> {
                while (!collect(meterRegistry)) {
                }
            }, 5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("druid datasource collected error{}",e);
        }

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
