package cn.taqu.gonghui.common.utils;

import java.util.Random;

public class RandomStringUtil {
    /**
     * 生成邀请码规则  N+7位随机数字
     */
    public final static int NUM_LENGTH=7;
    public static String getRandString(int length) {
        String charList = "0123456789";
        String rev = "";
        Random f = new Random();
        for (int i = 0; i < length; i++) {
            rev += charList.charAt(Math.abs(f.nextInt()) % charList.length());
        }
        rev="N"+rev;
        return rev;

    }

    /**
     * 生rondom(100 000,999 999)
     */
    public static String genInviteCode() {
        int num = new Random().nextInt(999_999);
        if(num < 100_000){
            num += 100_000;
        }
        return String.valueOf(num);
    }

}
