package cn.taqu.gonghui.common.mapper;

import cn.taqu.gonghui.common.entity.ApprovalFlowLog;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ApprovalFlowLogMapper {

    /**
     * 根据审核表id查询
     * @param flowId
     * @return
     */
    List<ApprovalFlowLog> selectByFlowId(Integer flowId);

    /**
     * 写入
     * @param approvalFlowLog
     */
    void insert(ApprovalFlowLog approvalFlowLog);

    /**
     * 批量写入
     * @param logList
     */
    void insertBatch(List<ApprovalFlowLog> logList);

}