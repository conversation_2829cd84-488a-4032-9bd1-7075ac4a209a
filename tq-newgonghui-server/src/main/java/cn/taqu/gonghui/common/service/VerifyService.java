package cn.taqu.gonghui.common.service;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.Constants;
import cn.taqu.gonghui.common.utils.*;
import cn.taqu.gonghui.soa.VerificationCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @Date 2021/5/13
 */
@Service
@Slf4j
public class VerifyService {


    @SoaReference(application ="push",value = "push")
    private VerificationCodeService verificationCodeService;
    @Autowired
    private RedisUtil redisUtil;


    /**
     * 登录时 验证码验证
     * @param mobile
     * @param vcode
     */
    public void verifyLogin(String mobile,String vcode){
        boolean isVer = verificationCodeService.verify("web_login", mobile, vcode);
        log.info("验证码-{}，结果-{}",vcode,isVer);
        if(!isVer){
            throw new ServiceException(CodeStatus.VERIFY_CODE_ERROR);
        }
    }

    /**
     *
     * @param mobile
     * @param vcode
     */
    public void verifyCommon(String code,String mobile,String vcode){
        log.info("verifyCommon,code={},mobile={},vcode={}",code,mobile,vcode);
        boolean isVer = verificationCodeService.verify(code, mobile, vcode);
        log.info("验证码-{}，结果-{}",vcode,isVer);
        if(!isVer){
            throw new ServiceException(CodeStatus.VERIFY_CODE_ERROR);
        }
    }


    public void sendVCode(String code,String mobile){
//        if(StringUtils.isEmpty(mobile)){
//            throw new ServiceException("mobile_empty","请输入手机号码");
//        }
//        Boolean isSend = redisUtil.set(mobile, "send", 60L, TimeUnit.SECONDS);
//        if (!isSend) {
//            throw new ServiceException("send_vcode_to_many","发送频率过高,请稍等后再尝试");
//        }
        log.info("sendVCode,code={},mobile={}",code,mobile);
        verificationCodeService.requestVerificationCode(code,"SINGLE_VCODE",mobile);
    }

    public void sendLoginVCode(String code,String mobile,String sessionId){

        if(StringUtils.isEmpty(mobile)){
            throw new ServiceException("mobile_is_empty","当前手机号码不存在");
        }
        if(StringUtils.isEmpty(sessionId)){
            throw new ServiceException("sessionId_is_empty","当前会话不存在");
        }
        String slideValid = redisUtil.get(sessionId);
        log.info("sessionId:{},结果：{}",sessionId,slideValid);
        if(!"0".equals(slideValid)){
            throw new ServiceException("slide_valid_fail","滑块验证失败");
        }
        verificationCodeService.requestVerificationCode(code,"SINGLE_VCODE",mobile);
    }

    /**
     * 获取图形验证码
     * @param time 过期时间,默认60秒
     * @return
     */
    public Map<String, String> getCaptcha(Long time) {
        if(time == null || time < 0L){
            time = 60L;
        }
        String sceneId = UUID.genUuid();
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        Object[] objs = VerifyUtil.newBuilder()
                .setLines(0)
                .setBackgroundColor(new Color(230, 230, 230))
                .build()
                .createImage();
        BufferedImage image = (BufferedImage) objs[1];
        try {
            ImageIO.write(image, "png", out);
        } catch (IOException e) {
            throw new ServiceException("buildCaptcha", "验证码生成错误");
        }
        String base64bytes = Base64Utils.encodeToString(out.toByteArray());

        //该字符串传输至前端放入src即可显示图片，安卓可以去掉data:image/png;base64,
        String src = "data:image/png;base64," + base64bytes;
        redisUtil.set(Constants.CAPTCHA_CODE_KEY + sceneId, String.valueOf(objs[0]), time, TimeUnit.SECONDS);

        Map<String, String> result = new HashMap<>();
        result.put("src", src);
        result.put("sceneId", sceneId);
        return result;
    }

    /**
     * 验证图形验证码
     * @param sceneId
     * @param code
     * @return
     */
    public Boolean verifyCaptcha(String sceneId, String code){
        String realCode = redisUtil.get(Constants.CAPTCHA_CODE_KEY + sceneId);
        if (StringUtils.isEmpty(realCode)) {
            throw new ServiceException("code_expire","验证码已过期，请重新获取验证码");
        }
        return Objects.equals(realCode.toLowerCase(), code.toLowerCase());
    }
}
