package cn.taqu.gonghui.common.vo;

import lombok.Data;
import scala.Int;

import java.util.List;

@Data
public class LiveHostStatisticVo {
    private String amount; //总收益
    private String live_num; //开播人数
    private String no_live_num; //未开播人数
    private String total_live_time; //总时长
    private String lost; //流失主播

    /**
     * 游戏收入
     */
    private Integer game_amount;

    /**
     * 游戏收入占比
     */
    private String game_amount_ratio;

    /**
     * 贝壳相关
     */
    private Integer shell_amount;

    private String shell_ratio;

    private String total_score;

    private List<LiveHostVo> list;
}
