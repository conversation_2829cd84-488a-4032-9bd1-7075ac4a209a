package cn.taqu.gonghui.common.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 主播类型
 * <AUTHOR>
 */
@Getter
public enum HostTypeEnum {

    /**
     * 未设置
     */
    UNSET(0, "未设置"),
    SELF_SUP(1, "自营"),
    SELF_LIFT(2, "自提"),
    ;

    private Integer code;
    private String name;

    HostTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code) {
        for (HostTypeEnum location : HostTypeEnum.values()) {
            if (Objects.equals(location.getCode(), code)) {
                return location.getName();
            }
        }
        return "";
    }

}
