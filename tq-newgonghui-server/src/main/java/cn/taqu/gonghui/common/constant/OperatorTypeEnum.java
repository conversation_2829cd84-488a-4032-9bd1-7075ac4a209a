package cn.taqu.gonghui.common.constant;

public enum OperatorTypeEnum {

    /**
     * 更新机构
     */
    UPDATE_GUILD(1),
    /**
     * 开启机构
     */
    OPEN_GUILD(2),
    /**
     * 关闭机构
     */
    CLOSE_GUILD(3),
    /**
     * 通过机构
     */
    PASS_GUILD(4),
    /**
     * 拒绝
     */
    REJECT_GUILD(5),
    /**
     * 重新绑定手机
     */
    REBIND_MOBILE(6);


    private Integer value;

    OperatorTypeEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return this.value;
    }
}
