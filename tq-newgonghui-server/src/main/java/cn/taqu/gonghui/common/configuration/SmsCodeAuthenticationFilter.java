package cn.taqu.gonghui.common.configuration;

import cn.taqu.core.web.protocol.http.RequestParams;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;

/**
 * <AUTHOR>
 * @Date 2021/5/13
 */
public class SmsCodeAuthenticationFilter  extends AbstractAuthenticationProcessingFilter {

    private static final String TAQU_SOA_REQUEST_PAMRM_KEY = "form";

    public SmsCodeAuthenticationFilter() {
        super(new SoaIncludeMatcher().includeMethod("account","login"));
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
            String form = null;
            Enumeration paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = (String) paramNames.nextElement();
                if(TAQU_SOA_REQUEST_PAMRM_KEY.equals(paramName)){
                    String[] paramValues = request.getParameterValues(paramName);
                    if (paramValues.length == 1) {
                        String paramValue = paramValues[0];
                        if (paramValue.length() != 0) {
                            form = paramValue;
                            break;
                        }
                    }
                }
            }
        RequestParams params = new RequestParams();
        params.setForm(form);
        String mobile = obtainMobile(params);
        if (mobile == null) {
            mobile = "";
        }
        mobile = mobile.trim();

        SmsCodeAuthenticationToken authRequest = new SmsCodeAuthenticationToken(mobile);
        setDetails(request, authRequest);
        return this.getAuthenticationManager().authenticate(authRequest);
    }

    protected String obtainMobile(RequestParams params) {
        String mobile = params.getFormString(0);
        return mobile;
    }

    protected void setDetails(HttpServletRequest request, SmsCodeAuthenticationToken authRequest) {
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }





}
