package cn.taqu.gonghui.common.constant.chatroom;

/**
 * @Description 聊天室公会-数据统计-公会数据，对应字段
 * <AUTHOR>
 * @Date 2022/5/24 7:54 下午
 **/
public enum OrganizationDataEnum {
    dt("统计日期"),
    teamName("所属团队"),
    orgName("所属机构"),
    consume_cnt("房间总消费人数"),
    receive_amt("艺人收益"),
    consume_amt("房间消费金额"),
    receive_game_amt("艺人游戏礼物收益"),
    receive_panel_amt("艺人非游戏礼物收益"),
    receive_game_amt_ratio("艺人个人游戏礼物收益占比"),
    chat_open_duration("艺人开房总时长"),
    meeting_cnt("房间上麦用户数"),
    meeting_duration("房间用户上麦时长"),
    receive_cnt("艺人总人数"),
    new_receive_cnt("新增艺人数"),
    meeting_open_duration("房间上麦用户开麦时长"),
    chat_receive_game_amt("艺人开房时房间游戏礼物收益"),
    chat_receive_game_amt_ratio("艺人开房时房间游戏礼物收益占比"),
    chat_open_cnt("艺人开房次数"),
    meeting_open_cnt("房间开麦用户数"),
    chat_receive_cnt("艺人开房时房间收益人数"),
    ;

    public static String getName(String code) {
        for (OrganizationDataEnum value : values()) {
            if (value.name().equals(code)) {
                return value.getName();
            }
        }
        return code;
    }

    OrganizationDataEnum(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }
}
