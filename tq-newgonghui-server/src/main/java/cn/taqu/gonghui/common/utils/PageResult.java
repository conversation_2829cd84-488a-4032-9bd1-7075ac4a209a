package cn.taqu.gonghui.common.utils;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/5/8
 */


public class PageResult<T> implements Serializable {

    private Long total;
    private List<T> list;


    public PageResult(Long total, List<T> list) {
        this.total = total;
        this.list = list;
    }


    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}

