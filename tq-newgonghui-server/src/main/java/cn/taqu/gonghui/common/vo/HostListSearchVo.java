package cn.taqu.gonghui.common.vo;

import cn.taqu.core.orm.PageSearch;
import lombok.Data;

@Data
public class HostListSearchVo extends PageSearch {

    private String liveNo;
    private String hostUuid;
    private String nickname;
    private String agentUuid;
    private Integer haveAgent;
    private Integer liveStatus;

    private Long lastLiveTimeStart; //主播最后在线时间(开始)
    private Long lastLiveTimeEnd; //主播最后在线时间(结束)
    private String grade; //主播评级
    private String orderType;
}
