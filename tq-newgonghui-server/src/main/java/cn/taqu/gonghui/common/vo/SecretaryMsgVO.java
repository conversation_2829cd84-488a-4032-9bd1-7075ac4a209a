package cn.taqu.gonghui.common.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 15:26
 */
@Data
public class SecretaryMsgVO {

    /**
     * 接收的用户uuid
     */
    private List<String> to_user_id;

    /**
     * 不同消息类型
     */
    private ContentData content;

    /**
     * 推送内容
     */
    private String push_content;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 频道
     */
    private String msg_channel;

    /**
     * 推送中台的id
     */
    private String push_id;

    /**
     *  1为屏蔽im 只落库
     */
    private String shield_im;

    /**
     * 推送标题
     */
    private String push_title;

    @Data
    public static class ContentData {

        private String content;

        private String title;

        private String relaction;

    }

}
