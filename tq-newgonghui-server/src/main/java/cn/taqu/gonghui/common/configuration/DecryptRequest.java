package cn.taqu.gonghui.common.configuration;

import cn.hutool.core.codec.Base64Decoder;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJacksonInputMessage;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/11 3:16 PM
 **/
@Slf4j
@ControllerAdvice
public class DecryptRequest extends RequestBodyAdviceAdapter {


    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        PostMapping mapping = methodParameter.getMethodAnnotation(PostMapping.class);
        String[] params = mapping.params();
        for (String param : params) {
            boolean contains = param.contains("method=");
            if (contains) {
                return true;
            }
        }
        return false;
    }


    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        if (inputMessage.getBody().available() == 0) {
            return super.beforeBodyRead(inputMessage, parameter, targetType, converterType);
        }
        byte[] bytes = new byte[inputMessage.getBody().available()];
        inputMessage.getBody().read(bytes);
        String str = new String(bytes);
        if (JSONUtil.isJson(str)) {
            // 如果是application/json没有加密的请求，就直接返回结果
            return new MappingJacksonInputMessage(new ByteArrayInputStream(JSONUtil.parse(str).toString().getBytes()), inputMessage.getHeaders());
        }
        String[] param = str.split("&");
        byte[] result = new byte[]{};
        for (String p : param) {
            if (!p.startsWith("form=")) {
                continue;
            }
            String formValue = p.replace("form=", "");
            byte[] decode = Base64Decoder.decode(URLDecoder.decode(formValue, Charset.defaultCharset()));
            JSONArray array = JSONUtil.parseArray(new String(decode));
            if (BeanUtils.isSimpleProperty(parameter.getParameterType()) || array.size() == 0) {
                // 基本类型的就直接返回
                result = array.size() == 0 ? decode : array.get(0).toString().getBytes(StandardCharsets.UTF_8);
                break;
            }
            JSONObject jsonObject = new JSONObject();
            if (JSONUtil.isJsonObj(array.get(0).toString())) {
                // 第一个是json对象的，单独处理
                jsonObject = array.getJSONObject(0);
            } else {
                // 循环获取子类和父类的字段
                List<Field> fieldList = new ArrayList<>();
                Class<?> parameterType = parameter.getParameterType();
                while (parameterType != null && parameterType != Object.class) {
                    fieldList.addAll(0, Lists.newArrayList(parameterType.getDeclaredFields()));
                    parameterType = parameterType.getSuperclass();
                }
                // 把值塞进对应的字段里面去
                for (int i = 0; i < fieldList.size(); i++) {
                    if (i > array.size() - 1) {
                        // 防止array数组越界，多加一层判断
                        break;
                    }
                    Field field = fieldList.get(i);
                    jsonObject.set(field.getName(), array.get(i));
                }
            }
            result = jsonObject.toString().getBytes(StandardCharsets.UTF_8);

        }
        return new MappingJacksonInputMessage(new ByteArrayInputStream(result), inputMessage.getHeaders());
    }
}
