package cn.taqu.gonghui.common.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class LiveHostSelfStatisticVo {
    /**
     * 主播uuid
     */
    private String host_uuid;
    /**
     * 头像url
     */
    private String avatar;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 开播天数
     */
    private Integer month_live_days;
    /**
     * 有效开播天数
     */
    private Integer month_valid_live_days;
    /**
     * 当月总时长(秒)
     */
    private String month_live_time;

    private String fmt_month_live_time;
    /**
     * 当月收益
     */
    private Integer month_host_amount;
    private Integer pre_week_host_amount;
    /**
     * 当周收益
     */
    private Integer week_host_amount;
    /**
     * 上周收益
     */
    private Integer last_week_host_amount;
    /**
     * 历史收益
     */
    private Long history_host_amount;
    /**
     * 本月游戏收入
     */
    private Integer month_game_amount;

    /**
     * 本月游戏收入比例
     */
    private String month_game_ratio;

    private List<DataVo> list;

    @Data
    public static class DataVo {
        /**
         * 日期
         */
        private Long date_time;
        /**
         * 开播时间
         */
        private Long start_time;
        /**
         * 结束时间
         */
        private Long end_time;
        /**
         * 当次直播时长
         */
        private Integer duration;
        /**
         * 当日直播时长
         */
        private Integer total_live_time;
        /**
         * 当日收益
         */
        private Integer total_host_amount;
        /**
         * 当次直播时长Str
         */
        private String duration_Str;
        /**
         * 当日总直播时长Str
         */
        private String total_live_time_Str;
        /**
         * 游戏收益
         */
        private Integer total_game_amount;
        /**
         * 游戏收益收入比例
         */
        private String game_amount_ratio;

        private String total_score;

        private String shell_amount;

        private String shell_ratio;
    }


    private String orgName;    //机构名称
    private String orgUuid;    //机构uuid
    private String teamName;    //团队名称
    private String agentName;    //经纪人名称
    private Integer teamType;    //团队名称consortia_id 公会id
    private String follow_num;  // 粉丝数
    private String live_no; // 直播号(他趣id)
    private String last_online_time;// 上一次开播时间
    private String  create_time; // 注册时间
    private String add_consortia_time; // 加入公会时间
    private String currentSharingProfitRate;   //当前分润
    private String oldSharingProfitRate;     // 调整前分润比例
    private String newSharingProfitRate;     // 调整后分润比例
    private String invite_remark;  //备注
    private String flower_num;  //鲜花束

    private Integer is_set_split; // 是否电子分润

    private Integer isUpdate;// 当前月是否调整了分润比例（1-时，0-否）
    private Integer changeStatus; // 分润比例调整状态（1-待确认，2-待生效，3-已生效，4-已拒绝）

    /**
     * 主播类型名称 0未设置 1自营 2自提
     */
    private String hostTypeName;

    private Integer hostType;

    private String month_total_score;

    private String month_shell_amount;

    private String month_shell_ratio;

    private String pre_week_total_score;

    private String total_score;

}
