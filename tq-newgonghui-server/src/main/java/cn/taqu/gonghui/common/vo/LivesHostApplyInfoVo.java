package cn.taqu.gonghui.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 主播申请VO
 * <p>
 * Created by zqlt on 2021/6/1.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LivesHostApplyInfoVo {
    private Long id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 主播uuid
     */
    private String hostUuid;
    /**
     * 身份证
     */
    private String idCard;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 他趣账号
     */
    private String tqAccount;
    /**
     * 支付宝账号
     */
    private String alipayAccount;
    /**
     * 申请时间
     */
    private Long createTime;
    /**
     * 身份证照片url
     */
    private String[] idCardUrl;
    /**
     * 主播封面url
     */
    private String hostCoverUrl;
    /**
     * 封面宽度
     */
    private Integer width;
    /**
     * 封面高度
     */
    private Integer height;
    /**
     * 主播视频url
     */
    private String hostVideoUrl;
    /**
     * 审批状态 1-通过，2-拒绝，3-待审核，4-待提交
     */
    private Integer applyStatus;
    /**
     * 拒绝原因
     */
    private String refuseReason;
    /**
     * 经纪人uuid
     */
    private String agentUuid;


    //********新增主播申请字段加密
    private String alipayAccountCipher;

    private String accountMobileCipher;

    private String realNameCipher;

    private String idCardCipher;


    private String alipayAccountSm3;

    private String accountMobileSm3;

    private String realNameSm3;

    private String idCardSm3;


    private String cardZmUrl;
    private String cardFmUrl;
    private String cardScUrl;


    @Data
    public static class SimpleInfo {
        private String hostUuid;
        private String nickname;
        private String realName;
        private String sex;
        private String phone;
        /**
         * 身份证照片url
         */
        private String[] idCardUrl;
        private String cardZmUrl;
        private String cardFmUrl;
        private String cardScUrl;
        /**
         * 主播封面url
         */
        private String hostCoverUrl;
        /**
         * 主播视频url
         */
        private String hostVideoUrl;


    }
}
