package cn.taqu.gonghui.common.constant;

import cn.taqu.core.common.constant.ICodeStatus;
import cn.taqu.core.common.constant.SysCodeStatus;

/**
 * 客户端通过value来判断是否返回了错误当value为0时，表示有正常，否则表示有错误，此时需要去读取相应的reasonPhrase来查看具体的错误原因，
 * 错误原因中的错误码（txxxx）是用来当用户反馈错误时，能够及时快速的定位到相应的错误位置，因此需要在定义错误码时确保不重复，
 * 而且需要将错误码进行分类，具体的分类规则如下:<br/>
 *
 * <AUTHOR>
 * @ClassName:CodeStatus.java
 */
public enum ErrorCodeStatus implements ICodeStatus {
    /**
     * 机构不存在,无法更新
     */
    GUILD_INFO_NOT_EXIST("guild_info_not_exist", "机构不存在,无法更新."),
    /**
     * 机构存在,无法更新
     */
    GUILD_IS_EXIST("guild_is_exist", "机构已经存在,请勿重复通过."),
    /**
     * 机构审核参数错误
     */
    GUILD_AUDIT_PARAMS_ERROR("guild_audit_params_error", "机构审核参数错误"),
    /**
     * 待审核机构状态错误
     */
    GUILD_APPLY_STATUS_ERROR("guild_apply_status_error", "待审核机构状态错误"),
    /**
     * 数据查询失败，该数据或已删除
     */
    DATA_NOT_FOUNT_ERROR("data_not_fount_error", "数据查询失败，该数据或已删除"),
    ;

    private final String value;// code

    private final String reasonPhrase;// message description

    private ErrorCodeStatus(String value, String reasonPhrase) {
        this.value = value;
        this.reasonPhrase = reasonPhrase;
    }

    /**
     * Return the integer value of this status code.
     */
    @Override
    public String value() {
        return this.value;
    }

    /**
     * Return the reason phrase of this status code.
     */
    @Override
    public String getReasonPhrase() {
        return reasonPhrase;
    }

    @Override
    public String toString() {
        return value;
    }

    public static ICodeStatus getCodeStatus(String statusCode) {
        for (ErrorCodeStatus status : values()) {
            if (status.value.equals(statusCode)) {
                return status;
            }
        }
        for (SysCodeStatus status : SysCodeStatus.values()) {
            if (status.value().equals(statusCode)) {
                return status;
            }
        }
        return SysCodeStatus.UNDEFINED;
    }
}

