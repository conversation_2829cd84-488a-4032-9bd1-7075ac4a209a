package cn.taqu.gonghui.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("audit_order")
public class AuditOrder extends BaseEntity {
    private String no;
    /**
     *  申请单类型
     *  1. 报备房
     */
    private Integer type;

    /**
     * 状态
     * 0. 未审核
     * 1. 部分通过
     * 2. 全通过
     * -1. 已拒绝
     */
    private Integer status;

}
