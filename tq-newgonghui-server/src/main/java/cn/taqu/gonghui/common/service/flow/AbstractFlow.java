package cn.taqu.gonghui.common.service.flow;

import cn.hutool.core.date.DateUtil;
import cn.taqu.gonghui.common.constant.ApprovalStatusEnum;
import cn.taqu.gonghui.common.constant.FlowTypeEnum;
import cn.taqu.gonghui.common.constant.PassRejectEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.entity.ApprovalFlowLog;
import cn.taqu.gonghui.common.mapper.ApprovalFlowLogMapper;
import cn.taqu.gonghui.common.mapper.ApprovalFlowMapper;
import cn.taqu.gonghui.common.mapper.ApprovalFlowNodeMapper;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.ApprovalFlowVO;
import cn.taqu.gonghui.common.vo.PassRejectVO;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 审批流 模板方法
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractFlow<T> {

    @Resource
    private ApprovalFlowMapper approvalFlowMapper;

    @Resource
    private ApprovalFlowNodeMapper approvalFlowNodeMapper;

    @Resource
    private ApprovalFlowLogMapper approvalFlowLogMapper;

    @Autowired
    private SysUserService sysUserService;

    /**
     * 获取flow 类型
     * @return
     */
    protected abstract Integer getFlowType();

    /**
     * 构建vo 不同的审批流有差别 所以由上层实现
     * @param t
     * @return
     */
    protected abstract ApprovalFlowVO buildApprovalFlowVO(T t);

    /**
     * 审批前置操作
     * @param passRejectVO
     */
    protected abstract void preReview(PassRejectVO passRejectVO);

    /**
     * 审批后操作
     * @param passRejectVO
     */
    protected abstract void afterReview(PassRejectVO passRejectVO);

    /**
     * 创建前校验
     * @param t
     */
    protected abstract void preCreate(T t);

    /**
     * 创建后 钩子
     * @param approvalFlowVO
     */
    protected abstract void afterCreate(ApprovalFlowVO approvalFlowVO);

    /**
     * 创建审批流程
     * @param t
     */
    @Transactional(rollbackFor = Exception.class)
    public void create(T t) {
        // 创建前校验
        preCreate(t);

        // 构建vo
        ApprovalFlowVO approvalFlowVO = buildApprovalFlowVO(t);

        // 写入主表
        ApprovalFlow approvalFlow = approvalFlowVO.getApprovalFlow();
        approvalFlowMapper.insertGetId(approvalFlow);

        // 批量写入子表
        List<ApprovalFlowNode> approvalFlowNodeList = approvalFlowVO.getApprovalFlowNodeList();
        approvalFlowNodeList.forEach(item -> item.setFlowId(approvalFlow.getId()));
        approvalFlowNodeMapper.insertBatch(approvalFlowNodeList);

        // 写入审核日志表
        ApprovalFlowLog approvalFlowLog = new ApprovalFlowLog();
        approvalFlowLog.setFlowId(approvalFlow.getId());
        approvalFlowLog.setFlowStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        // 此处如果有新的审批类型 可以枚举类定义
        String remark = approvalFlow.getNickName() + "发起"+ FlowTypeEnum.getName(getFlowType()) +"单;" + " 下一流程节点审批人："
                + approvalFlowNodeList.get(0).getNodeName() + " "
                + approvalFlowNodeList.get(0).getMobile();
        approvalFlowLog.setRemark(remark);
        String userTimeInfo = "主播：" + approvalFlow.getNickName()
                + " uuid：" + approvalFlow.getHostUuid() + " " + DateUtil.now();
        approvalFlowLog.setUserTimeInfo(userTimeInfo);
        approvalFlowLog.setCreateUser(approvalFlow.getNickName());
        approvalFlowLogMapper.insert(approvalFlowLog);

        // 创建后
        afterCreate(approvalFlowVO);
    }

    /**
     * 通过审批
     * @param action
     * @param passRejectVO
     */
    @Transactional(rollbackFor = Exception.class)
    public void review(Integer action, PassRejectVO passRejectVO) {
        log.info("[quitGuildReview],passRejectVo:{}", passRejectVO);
        passRejectVO.setAction(action);
        // 前置操作
        preReview(passRejectVO);

        // 查询当前审批单需要修改的角色和用户
        Date now = new Date();
        String reviewUser = passRejectVO.getReviewUser();
        String remark = passRejectVO.getRemark();
        ApprovalFlowNode currentNode = approvalFlowNodeMapper.selectCurrentNode(passRejectVO.getFlowId());
        currentNode.setReviewTime(now);
        currentNode.setModifyUser(reviewUser);
        currentNode.setModifyTime(now);
        currentNode.setNodeStatus(action);
        currentNode.setRemark(remark);

        // 修改节点审核状态
        approvalFlowNodeMapper.updateByRecord(currentNode);

        // 保存当前节点
        passRejectVO.setCurrentNode(currentNode);

        Integer flowStatus = ApprovalStatusEnum.WAIT_APPROVAL.getCode();
        // (拒绝 或者 (同意 & 最后一个节点) 则更新主表状态
        boolean rejectFlag = action.equals(PassRejectEnum.REJECT.getCode());
        boolean passAndAgreeFlag = action.equals(PassRejectEnum.PASS.getCode()) && currentNode.getNextIndex().equals(0);
        if (rejectFlag || passAndAgreeFlag) {
            ApprovalFlow approvalFlow = new ApprovalFlow();
            approvalFlow.setId(currentNode.getFlowId());
            approvalFlow.setFlowStatus(action);
            approvalFlow.setModifyTime(now);
            approvalFlow.setModifyUser(reviewUser);
            // 如果是拒绝 需要把原因回写主表
            if (rejectFlag) {
                approvalFlow.setRejectReason(remark);
            }
            approvalFlowMapper.updateByRecord(approvalFlow);
            flowStatus = action;
        }

        // 记录审核日志表
        ApprovalFlowLog approvalFlowLog = new ApprovalFlowLog();
        approvalFlowLog.setFlowId(currentNode.getFlowId());
        approvalFlowLog.setFlowStatus(flowStatus);

        // 判断是否存在下一节点
        ApprovalFlowNode nextCondition = new ApprovalFlowNode();
        nextCondition.setFlowId(passRejectVO.getFlowId().longValue());
        nextCondition.setNodeStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        ApprovalFlowNode nextNode = approvalFlowNodeMapper.selectOneByCondition(nextCondition);
        String extendRemark = "";
        if (Objects.nonNull(nextNode) && currentNode.getNodeStatus().equals(ApprovalStatusEnum.PASS.getCode())) {
            extendRemark = " 下一个流程节点审批人：" + UserTypeEnum.getByType(nextNode.getNodeRole()) + " " + nextNode.getMobile();
        }

        String logRemark = PassRejectEnum.getName(action) + (StringUtils.isNotEmpty(remark) ? ":" + remark : "")
                + extendRemark;
        approvalFlowLog.setRemark(logRemark);
        //SysUser sysUser = sysUserService.selectUserByAccountUuid(currentNode.getNodeUser());

        //String mobile = Objects.isNull(sysUser) ? "[sysuser无手机号]" : sysUser.getMobile();
        // userTimeInfo 可能其他审批流不适用 如果新增可以提取
        String userTimeInfo = UserTypeEnum.getByType(currentNode.getNodeRole()) + "：" + currentNode.getMobile() + " " + DateUtil.now();
        approvalFlowLog.setUserTimeInfo(userTimeInfo);
        approvalFlowLog.setCreateUser(reviewUser);
        approvalFlowLogMapper.insert(approvalFlowLog);

        // 审批通过后置方法
        afterReview(passRejectVO);
    }

}
