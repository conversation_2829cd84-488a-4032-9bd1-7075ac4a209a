package cn.taqu.gonghui.common.domain;

import cn.taqu.gonghui.live.entity.GuildInfo;
import cn.taqu.gonghui.system.entity.SysRole;
import cn.taqu.gonghui.system.entity.SysSharingProfit;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.vo.TeamEmployeeVo;
import cn.taqu.gonghui.system.vo.TeamTreeVo;
import lombok.Data;
import org.redisson.api.RLexSortedSet;

/**
 * 通用的下拉类
 * <AUTHOR>
 * @Date 2021/5/11
 */
@Data
public class CommonSelect {


    private String label;

    private Long value;

    public CommonSelect(){

    }

    public CommonSelect(Team team){
        this.value = team.getTeamId();
        this.label = team.getTeamName();
    }

    public CommonSelect(SysSharingProfit sharingProfit){
        this.value = sharingProfit.getId();
        this.label = String.valueOf(sharingProfit.getPercentNumber());
    }

    public CommonSelect(SysRole role){
        this.value = role.getRoleId();
        this.label = role.getRoleName();
    }

    public CommonSelect(TeamEmployee teamEmployee){
        this.value = teamEmployee.getEmployeeId();
        this.label = teamEmployee.getEmployeeName();
    }

    public CommonSelect(TeamEmployeeVo teamEmployeeVo){
        this.value = teamEmployeeVo.getEmployeeId();
        this.label = teamEmployeeVo.getEmployeeName();
    }

    public CommonSelect(GuildInfo guildInfo){
        this.value = Long.valueOf(guildInfo.getUuid());
        this.label = guildInfo.getGuildName();
    }

    public CommonSelect(TeamTreeVo vo){
        this.value = vo.getValue();
        this.label = vo.getLabel();
    }
}
