package cn.taqu.gonghui.common.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/25 15:26
 */
@Data
public class SecretaryLinkVO {

    /**
     * 接收的用户uuid
     */
    private List<String> to_user_id;

    /**
     * 不同消息类型
     */
    private List<ContentLink> content;

    /**
     * 推送内容
     */
    private String push_content;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 频道
     */
    private String msg_channel;

    /**
     * 推送中台的id
     */
    private String push_id;

    /**
     *  1为屏蔽im 只落库
     */
    private String shield_im;

    /**
     * 推送标题
     */
    private String push_title;

    @Data
    public static class ContentLink {
        private String title;
        private ContentChild content;
        @Data
        public static class ContentChild {
            private String content;
            private String describe;
            private String is_local_push;
            private List<ChildItem> content_replace;
            @Data
            public static class ChildItem {
                private String w;
                private String c;
                private String r;
            }
        }

    }

}
