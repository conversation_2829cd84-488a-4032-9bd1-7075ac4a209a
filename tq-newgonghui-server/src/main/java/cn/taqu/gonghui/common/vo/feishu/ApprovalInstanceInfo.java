package cn.taqu.gonghui.common.vo.feishu;

import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/23 18:05
 **/
@Data
public class ApprovalInstanceInfo {
    private String approval_code;
    private String approval_name;
    private String department_id;
    private Long end_time;
    private String form;
    private String open_id;
    private Boolean reverted;
    private Long serial_number;
    private Long start_time;
    private String status;
    private List<TaskList> task_list;
    private List<Timeline> timeline;
    private String user_id;
    private String uuid;

    @Data
    public static class TaskList {
        private Long end_time;
        private Long id;
        private String node_id;
        private String node_name;
        private String open_id;
        private Long start_time;
        private String status;
        private String type;
        private String user_id;
    }

    @Data
    public static class Timeline {
        private Long create_time;
        private String ext;
        private String node_key;
        private String open_id;
        private String type;
        private String user_id;
        private String comment;
        private String task_id;
    }
}
