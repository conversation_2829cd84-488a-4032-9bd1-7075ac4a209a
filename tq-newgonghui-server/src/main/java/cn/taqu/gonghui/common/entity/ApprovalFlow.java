package cn.taqu.gonghui.common.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ApprovalFlow {
    private Long id;

    private String flowTitle;

    private Integer flowType;

    private Integer flowStatus;

    private String applyReason;

    private Date applyTime;

    private Long orgId;

    private String orgName;

    private Long teamId;

    private String hostUuid;

    private String nickName;

    private String avatar;

    private String applyLevel;

    private String rejectReason;

    private String liveNo;

    private Date createTime;

    private String createUser;

    private Date modifyTime;

    private String modifyUser;

    private Integer isDel;

}