package cn.taqu.gonghui.common.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.taqu.gonghui.common.entity.AuditOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public interface AuditOrderMapper extends BaseMapper<AuditOrder> {
    List<AuditOrder> listByStatusOrderById(

            @Param("statusList") List<Integer> status,
            @Param("lastId") Long lastId,
            @Param("limit") Integer limit
    );

}
