package cn.taqu.gonghui.common.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 流程类型
 * <AUTHOR>
 */
@Getter
public enum ResetTypeEnum {

    /**
     * 全部
     */
    ALL(0, "全部"),
    LIVE_SOURCE(1, "直播提供hostuuid"),
    TEAM(2, "团队id"),
    ORG(3, "公会id"),
    ;

    private Integer code;
    private String name;

    ResetTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code) {
        for (ResetTypeEnum location : ResetTypeEnum.values()) {
            if (Objects.equals(location.getCode(), code)) {
                return location.getName();
            }
        }
        return "";
    }

}
