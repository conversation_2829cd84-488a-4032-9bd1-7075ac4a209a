package cn.taqu.gonghui.common.utils;

import cn.taqu.core.common.client.MqClient;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.callback.feishu.FeiShuRelationType;
import cn.taqu.gonghui.common.vo.SmsFormVO;
import cn.taqu.gonghui.soa.VerificationCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/25 19:39
 */
@Slf4j
public class SmsUtil {

    private final static int APP_CODE = 1;

    private final static String TAG_CODE = "gonghui_quit_guild_notice";

    private final static String SMS_QUEUE = "mp_sms_async_invoke_queue";

    /**
     * 发送短信
     * @param content
     * @param mobile
     */
    public static Boolean sendMsg(String mobile, String content) {
        SmsFormVO.FormData formData = new SmsFormVO.FormData();
        formData.setPhone(mobile);
        formData.setContent(content);
        formData.setAppcode(APP_CODE);
        formData.setTagCode(TAG_CODE);
        formData.setCloned(APP_CODE);

        SmsFormVO smsFormVO = new SmsFormVO();
        smsFormVO.setService("businessSms");
        smsFormVO.setMethod("processBusinessSms");
        smsFormVO.setAsyncforms(formData);

        try {
            log.debug("[sendMsg],req:{}", smsFormVO);
            MqResponse response = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ)
                    .push(SMS_QUEUE, smsFormVO, null);
            log.info("rrrrrrr,{}", JsonUtils.objectToString(response));
            return response.success();
        } catch (Exception e) {
            log.error("发送失败[sendMsg],msg:{},req:{}", e.getMessage(), smsFormVO);
            return false;
        }
    }

}
