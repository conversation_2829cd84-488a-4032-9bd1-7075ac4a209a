package cn.taqu.gonghui.common.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum ApprovalStatusEnum {

    /**
     * 初始化
     */
    WAIT_APPROVAL(0, "审核中"),
    PASS(1, "审核通过"),
    REJECT(2, "审核拒绝"),
    ;

    private Integer code;
    private String name;

    ApprovalStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code) {
        for (ApprovalStatusEnum location : ApprovalStatusEnum.values()) {
            if (Objects.equals(location.getCode(), code)) {
                return location.getName();
            }
        }
        return "";
    }

}
