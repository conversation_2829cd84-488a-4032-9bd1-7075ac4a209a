package cn.taqu.gonghui.common.utils;

import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.gonghui.soa.OperationService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class EncryptUtil {


    public static final String GONGHUI = "gonghui";
    public static final String LIVE_API = "live_api";
    @SoaReference("encrypt")
    private OperationService operationService;

    /**
     * 批量加密
     *
     * @param content
     * @return
     */
    public Map<String, String> batchEncrypt(String code, Map<String, String> content) {
        Map<String, String> result = Maps.newHashMap();
        if (MapUtils.isEmpty(content)) {
            return result;
        }
        return operationService.batchEncrypt(code, content);
    }

    /**
     * 批量解密
     *
     * @param contentMap
     * @return
     */
    public Map<String, String> batchDecrypt(String code, Map<String, String> contentMap) {
        Map<String, String> result = Maps.newHashMap();
        if (MapUtils.isEmpty(contentMap)) {
            return result;
        }
        return operationService.batchDecrypt(code, contentMap);
    }

    /**
     * 批量解密
     *
     * @param listMap
     * @return
     */
    public List<Map<String, String>> batchDecryptList(String code, List<Map<String, String>> listMap) {
        List<Map<String, String>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(listMap)) {
            return result;
        }
        return operationService.batchDecrypt(code, listMap);
    }


    public Map<String, String> sm3(Map<String, String> contentMap) {
        Map<String, String> result = Maps.newHashMap();
        if (MapUtils.isEmpty(contentMap)) {
            return result;
        }
        return operationService.batchsm3(contentMap);
    }

}
