package cn.taqu.gonghui.common.constant;

import cn.taqu.gonghui.constant.BaseEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum AuditOrderStatusEnum implements BaseEnum {


    WAIT_AUDIT(0, "待审核"),
    PART_ACCEPT(1, "部分审核通过"),
    ALL_ACCEPT(2, "审核通过"),


    REJECT(-1, "审核拒绝"),
    CANCEL(-2, "取消");


    private final int code;
    private final String name;


}
