package cn.taqu.gonghui.common.service;

import cn.taqu.gonghui.common.constant.TeamStatusEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.service.SysMenuService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


@Service("ss")
@Slf4j
public class PermissionService
{
    /** 所有权限标识 */
    private static final String ALL_PERMISSION = "*:*";

    /** 管理员角色权限标识 */
    private static final String SUPER_ADMIN = "admin";

    private static final String ROLE_DELIMETER = ",";

    private static final String PERMISSION_DELIMETER = ",";

    @Autowired
    private TokenService tokenService;
    @Autowired
    private TeamEmployeeService teamEmployeeService;
    @Autowired
    private TeamService teamService;
    @Autowired
    private SysMenuService sysMenuService;

    /**
     * 验证用户是否具备某权限
     *
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    public boolean hasPermi(String permission)
    {
        if (StringUtils.isEmpty(permission))
        {
            return false;
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getPermissions()))
        {
            return false;
        }
        // 如果是机构管理员 返回ture 代表拥有所有权限
        if (UserTypeEnum.MANAGER.getType().equals(loginUser.getUser().getUserType())) {
            return true;
        }
        // 空角色 禁用所有权限
        if(UserTypeEnum.DEFAULT.getType().equals(loginUser.getUser().getUserType())){
            return false;
        }
        // 团队禁用
        SysUser user = loginUser.getUser();
        List<TeamEmployee> teamEmployeeList = teamEmployeeService.getOneByUserId(user.getUserId());

        if(CollectionUtils.isEmpty(teamEmployeeList)){
            // 不是团队成员 禁用
            return false;
        }
        boolean teamFlag = false;
        List<Team> teams = new ArrayList<>();
        for(TeamEmployee teamEmployee:teamEmployeeList){
            Team team = teamService.detail(teamEmployee.getTeamId());
            // 当前用户无团队禁用
            if(team ==null){
                log.info("权限验证-当前用户无团队,被禁用 成员id:{}团队id:{}",teamEmployee.getEmployeeId(),teamEmployee.getTeamId());
                teamFlag = true;
            }
            // 当前用户的团队被禁用 收回权限
            if(TeamStatusEnum.NO_VALID.getValue() == team.getStatus()){
                log.info("权限验证-当前用户所在团队{}被禁用了 成员id:{}团队id:{}",team.getTeamName(),teamEmployee.getEmployeeId(),teamEmployee.getTeamId());
                teamFlag = true;
            }
            // 记录有效的团队 下查询有效的业务类型的权限
            if(team != null && TeamStatusEnum.VALID.getValue() ==  team.getStatus()){
               teams.add(team);
            }
        }
        if(CollectionUtils.isEmpty(teams)){
            return false;
        }
        if(teamFlag){
             Set<String> permsSet = new HashSet<>();
             for(Team team: teams){
                 Set<String> strings = sysMenuService.selectMenuPermsByUserId(loginUser.getUser().getAccountUuid(), team.getType());
                 permsSet.addAll(strings);
             }
             return hasPermissions(permsSet,permission);
        }

        return  hasPermissions(loginUser.getPermissions(), permission);
    }

    /**
     * 验证用户是否不具备某权限，与 hasPermi逻辑相反
     *
     * @param permission 权限字符串
     * @return 用户是否不具备某权限
     */
    public boolean lacksPermi(String permission)
    {
        return hasPermi(permission) != true;
    }

    /**
     * 验证用户是否具有以下任意一个权限
     *
     * @param permissions 以 PERMISSION_NAMES_DELIMETER 为分隔符的权限列表
     * @return 用户是否具有以下任意一个权限
     */
    public boolean hasAnyPermi(String permissions)
    {
        if (StringUtils.isEmpty(permissions))
        {
            return false;
        }
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getPermissions()))
        {
            return false;
        }
        Set<String> authorities = loginUser.getPermissions();
        for (String permission : permissions.split(PERMISSION_DELIMETER))
        {
            if (permission != null && hasPermissions(authorities, permission))
            {
                return true;
            }
        }
        return false;
    }



    /**
     * 判断是否包含权限
     *
     * @param permissions 权限列表
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    private boolean hasPermissions(Set<String> permissions, String permission)
    {
        return permissions.contains(ALL_PERMISSION) || permissions.contains(StringUtils.trim(permission));
    }
}
