package cn.taqu.gonghui.common.client;

import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.soa.SOAUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 公司加解密服务
 *
 * <AUTHOR>
 * @date 2022/1/13 1:36 PM
 **/
@Component
@Slf4j
public class EncryptDecryptClient {

    private static final String OPERATION = "operation";

    private static final String ENCRYPT = "encrypt";

    private static final String DECRYPT = "decrypt";

    private static final String SM3 = "sm3";

    private static final String BATCHSM3 = "batchSm3";

    private static final String BATCHDECRYPTV2 = "batchDecryptV2";

    private static final String BATCHENCRYPT = "batchEncrypt";

    private static final String SOA_CODE_SUCCESS = "0";

    private static final String ENCRYPT_SERVER_URL = "/soa/go/encrypt";

    private static final String BIZ_CODE = "gonghui";


    /**
     * sm2加密内容
     *
     * @param content 待加密内容
     * @return 加密内容
     */
    @Timed(value = "sm2_encrypt", extraTags = {"soa_method", "sm2_encrypt"})
    public String encrypt(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        String result = null;
        Object[] form = {BIZ_CODE, content};
        StringBuilder sb = new StringBuilder();
        sb.append("encrypt请求前form=").append(JSON.toJSONString(form));
        SoaResponse soaResponse = SOAUtil.create(ENCRYPT_SERVER_URL).call(OPERATION, ENCRYPT, form);
        sb.append("-----").append("响应结果为:").append(JSON.toJSONString(soaResponse));
        if (soaResponse.fail()) {
            log.error("encrypt请求错误,form={},soaResponse={}", JSON.toJSONString(form), JSON.toJSONString(soaResponse));
            throw new ServiceException("encrypt_error", "encrypt请求失败");
        }

        if (StringUtils.isBlank(soaResponse.getCode()) || SOA_CODE_SUCCESS.equals(soaResponse.getCode())) {
            result = soaResponse.getData();
        } else {
            log.error("encrypt第三方返回错误,form={},soaResponse={}", JSON.toJSONString(form),JSON.toJSONString(soaResponse));
            throw new ServiceException("encrypt_error", "encrypt请求失败");
        }
        log.debug(sb.toString());
        return result;
    }

    /**
     * sm2解密内容
     *
     * @param encryptedContent 加密数据
     * @return 解密内容
     */
    @Timed(value = "sm2_decrypt", extraTags = {"soa_method", "sm2_decrypt"})
    public String decrypt(String encryptedContent) {
        if (StringUtils.isBlank(encryptedContent)) {
            return null;
        }
        return this.decrypt2(encryptedContent,true);
    }

    public String decrypt2(String encryptedContent, Boolean logFlag) {
        if (StringUtils.isBlank(encryptedContent)) {
            return null;
        }
        if(logFlag == null){
            logFlag = false;
        }
        StringBuilder sb = new StringBuilder();
        String result = null;
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("content", encryptedContent);
        SoaResponse soaResponse = null;
        if (logFlag) {
            sb.append("decrypt请求前paramsMap=").append(JSON.toJSONString(paramsMap));
//            log.debug("decrypt请求前paramsMap={}", JSON.toJSONString(paramsMap));
            soaResponse =SOAUtil.create(ENCRYPT_SERVER_URL).call(OPERATION, DECRYPT, BIZ_CODE, paramsMap);
//            log.debug("decrypt请求后paramsMap={}", JSON.toJSONString(soaResponse));
            sb.append("------响应结果为：").append(JSON.toJSONString(soaResponse));

        }else {
            sb.append("decrypt请求前paramsMap=").append(JSON.toJSONString(paramsMap));
//            log.debug("decrypt请求前paramsMap={}", JSON.toJSONString(paramsMap));
            soaResponse = SOAUtil.create(ENCRYPT_SERVER_URL).call(OPERATION, DECRYPT, BIZ_CODE, paramsMap);
//            log.debug("decrypt请求后paramsMap={}", JSON.toJSONString(soaResponse));
            sb.append("------响应结果为：").append(JSON.toJSONString(soaResponse));
        }
        if (soaResponse.fail()) {
            log.error("decrypt请求失败,encryptedContent={}soaResponse={}", encryptedContent,JSON.toJSONString(soaResponse));

            throw new ServiceException("decrypt_error", "decrypt请求失败");

        }
        log.debug(sb.toString());
        if (StringUtils.isBlank(soaResponse.getCode()) || SOA_CODE_SUCCESS.equals(soaResponse.getCode())) {
            result = JSONObject.parseObject(soaResponse.getData()).getString("content");
        } else {
            log.error("encrypt第三方返回错误,encryptedContent={}, soaResponse={}", encryptedContent,JSON.toJSONString(soaResponse));
//            MonitorService.incCounterMetrices(PrometheusMetricsEnum.MP_ACCOUNT_ENCRYPT_ERR);
//            throw new BusinessException(ErrorCodeConstant.ENCRYPT_DECRYPT_ERROR, "encrypt第三方返回错误");
            throw new ServiceException("decrypt_error", "encrypt第三方返回错误");
        }
        return result;
    }


    /**
     * sm2批量解密内容
     *
     * @param encryptedContent 加密数据
     * @return 解密内容
     */
    @Timed(value = "sm2_batchDecryptV2", extraTags = {"soa_method", "sm2_batchDecryptV2"})
    public Map<String, String> batchDecryptV2(Map<String, String> encryptedContent) {
        return this.batchDecryptV2(encryptedContent,true);
    }

    private Map<String, String> batchDecryptV2(Map<String, String> encryptedContent, Boolean logFlag) {
        Map<String, String> result = Maps.newHashMap();
        if (MapUtils.isEmpty(encryptedContent)) {
            return result;
        }
        if(logFlag == null){
            logFlag = false;
        }
        Map<String, Object> paramsMap = new HashMap<>();

        for (Map.Entry<String, String> entry : encryptedContent.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            Map<String, Object> paramsMapItem = new HashMap<>();
            paramsMapItem.put("content", value);
            paramsMap.put(key, paramsMapItem);
        }

        SoaResponse soaResponse = null;
        if (logFlag) {
            log.info("batchDecryptV2请求前paramsMap={}", JSON.toJSONString(paramsMap));
            soaResponse = SOAUtil.create(ENCRYPT_SERVER_URL).call(OPERATION, BATCHDECRYPTV2, BIZ_CODE, paramsMap);
            log.info("batchDecryptV2请求后paramsMap={}", JSON.toJSONString(soaResponse));

        }else {
            log.debug("batchDecryptV2请求前paramsMap={}", JSON.toJSONString(paramsMap));
            soaResponse = SOAUtil.create(ENCRYPT_SERVER_URL).call(OPERATION, BATCHDECRYPTV2, BIZ_CODE, paramsMap);
            log.debug("batchDecryptV2请求后paramsMap={}", JSON.toJSONString(soaResponse));
        }

        if (soaResponse.fail()) {
            log.error("batchDecryptV2请求失败,encryptedContent={}soaResponse={}", JSON.toJSONString(encryptedContent), JSON.toJSONString(soaResponse));
//            MonitorService.incCounterMetrices(PrometheusMetricsEnum.MP_ACCOUNT_BATCHDECRYPTV2_ERR);

//            throw new BusinessException(ErrorCodeConstant.ENCRYPT_DECRYPT_ERROR, "batchDecryptV2请求失败");
            throw new ServiceException("batch_decrypt_error","batchDecryptV2请求失败");
        }
        if (StringUtils.isBlank(soaResponse.getCode()) || SOA_CODE_SUCCESS.equals(soaResponse.getCode())) {
            Map<String, Map<String, String>> map = JsonUtils.stringToObject2(soaResponse.getData(), new TypeReference<Map<String, Map<String, String>>>() {});
            if (MapUtils.isNotEmpty(map)) {
                for (Map.Entry<String, Map<String, String>> entry : map.entrySet()) {
                    String key = entry.getKey();
                    Map<String, String> val = entry.getValue();
                    result.put(key, MapUtils.getString(val, "content", ""));
                }
            }
        } else {
            log.error("batchDecryptV2第三方返回错误,encryptedContent={}, soaResponse={}", JSON.toJSONString(encryptedContent), JSON.toJSONString(soaResponse));
//            MonitorService.incCounterMetrices(PrometheusMetricsEnum.MP_ACCOUNT_BATCHDECRYPTV2_ERR);
//            throw new BusinessException(ErrorCodeConstant.ENCRYPT_DECRYPT_ERROR, "batchDecryptV2第三方返回错误");
            throw new ServiceException("batch_decrypt_error","batchDecryptV2第三方返回错误");
        }
        return result;
    }

    /**
     * sm3摘要算法
     *
     * @param content
     * @return
     */
    @Timed(value = "sm3_encode", extraTags = {"soa_method", "sm3_encode"})
    public String encode(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        String result = null;
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put(BIZ_CODE, content);
        StringBuilder sb = new StringBuilder();
        sb.append("encode请求前paramsMap=").append(JSON.toJSONString(paramsMap));


//        log.debug("encode请求前paramsMap={}", JSON.toJSONString(paramsMap));
        SoaResponse soaResponse = SOAUtil.create(ENCRYPT_SERVER_URL).call(OPERATION, SM3, content);
        sb.append("------响应结果为：").append(JSON.toJSONString(soaResponse));
        log.debug(sb.toString());
        if (soaResponse.fail()) {
            log.error("encode请求失败,content={},soaResponse={}", content,JSON.toJSONString(soaResponse));
//            MonitorService.incCounterMetrices(PrometheusMetricsEnum.MP_ACCOUNT_ENCRYPT_ERR);
//
//            throw new BusinessException(ErrorCodeConstant.ENCRYPT_DECRYPT_ERROR, "encode请求失败");
            throw  new ServiceException("encrypt_error","encode请求失败");
        }
        if (StringUtils.isBlank(soaResponse.getCode()) || SOA_CODE_SUCCESS.equals(soaResponse.getCode())) {
            result = soaResponse.getData();
        } else {
            log.error("encode第三方返回错误,content={}, soaResponse={}", content,JSON.toJSONString(soaResponse));
//            MonitorService.incCounterMetrices(PrometheusMetricsEnum.MP_ACCOUNT_ENCRYPT_ERR);
//            throw new BusinessException(ErrorCodeConstant.ENCRYPT_DECRYPT_ERROR, "encode 第三方返回错误");
            throw  new ServiceException("encrypt_error","encode 第三方返回错误");
        }
        return result;
    }

    /**
     * 批量加密接口
     *
     * @param content 待加密内容
     * @return 加密内容
     */
    @Timed(value = "batch_encrypt", extraTags = {"soa_method", "batch_encrypt"})
    public Map<String, String> batchEncrypt(Map<String, String> content) {
        if (content.isEmpty()) {
            return new HashMap<String, String>();
        }
        Map<String, String> result = null;
        Object[] form = new Object[2];
        form[0] = BIZ_CODE;
        form[1] = content;
//        log.debug("batchEncrypt请求参数form={}", JSON.toJSONString(form));
        SoaResponse soaResponse = SOAUtil.create(ENCRYPT_SERVER_URL).call(OPERATION, BATCHENCRYPT, form);
//        log.debug("batchEncrypt返回参数soaResponse={}", JSON.toJSONString(soaResponse));

        if (soaResponse.fail()) {
            log.error("batchEncrypt 第三方请求错误 soaResponse={}", JSON.toJSONString(soaResponse));
//            throw new BusinessException(ErrorCodeConstant.ENCRYPT_DECRYPT_ERROR, "batchEncrypt 第三方请求错误");
            throw  new ServiceException("batch_encrypt_error","batchEncrypt 第三方请求错误");
        }
        if (StringUtils.isBlank(soaResponse.getCode()) || SOA_CODE_SUCCESS.equals(soaResponse.getCode())) {
            result = JSON.parseObject(soaResponse.getData(), Map.class);
        } else {
            log.error("batchEncrypt 第三方返回错误 soaResponse={}", JSON.toJSONString(soaResponse));
//            MonitorService.incCounterMetrices(PrometheusMetricsEnum.MP_ACCOUNT_ENCRYPT_ERR);
//
//            throw new BusinessException(ErrorCodeConstant.ENCRYPT_DECRYPT_ERROR, "batchEncrypt 第三方返回错误");
            throw  new ServiceException("batch_encrypt_error","batchEncrypt 第三方返回错误");
        }
        return result;
    }


    /**
     * 批量摘要接口
     *
     * @param content
     * @return
     */
    @Timed(value = "batch_sm3", extraTags = {"soa_method", "batch_sm3"})
    public Map<String, String> batchSm3(Map<String, String> content) {
        if (content.isEmpty()) {
            return new HashMap<String, String>();
        }
        Map<String, String> result = null;
        log.debug("batchSm3请求前content={}", JSON.toJSONString(content));
        SoaResponse soaResponse = SOAUtil.create(ENCRYPT_SERVER_URL).call(OPERATION, BATCHSM3, content);
        log.debug("batchSm3请求后soaResponse={}", JSON.toJSONString(soaResponse));
        if (soaResponse.fail()) {
            log.error("batchSm3请求第三方失败,soaResponse={}", soaResponse.getCode(), soaResponse.getMsg());
//            MonitorService.incCounterMetrices(PrometheusMetricsEnum.MP_ACCOUNT_ENCRYPT_ERR);
//            throw new BusinessException(ErrorCodeConstant.ENCRYPT_DECRYPT_ERROR, "batchSm3请求第三方失败");
            throw new ServiceException("batch_encrypt_error","batchSm3请求第三方失败");
        }
        if (StringUtils.isBlank(soaResponse.getCode()) || SOA_CODE_SUCCESS.equals(soaResponse.getCode())) {
            result = JSON.parseObject(soaResponse.getData(), Map.class);
        } else {
            log.error("batchSm3 第三方返回错误 soaResponse={}", JSON.toJSONString(soaResponse));
//            MonitorService.incCounterMetrices(PrometheusMetricsEnum.MP_ACCOUNT_ENCRYPT_ERR);
//            throw new BusinessException(ErrorCodeConstant.ENCRYPT_DECRYPT_ERROR, "batchSm3 第三方返回错误");
            throw new ServiceException("batch_encrypt_error","batchSm3 第三方返回错误");
        }
        return result;
    }
}
