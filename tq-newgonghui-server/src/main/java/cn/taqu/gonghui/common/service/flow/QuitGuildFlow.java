package cn.taqu.gonghui.common.service.flow;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.chatroom.service.ChatRoomTransferService;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.common.mapper.ApprovalFlowMapper;
import cn.taqu.gonghui.common.mapper.ApprovalFlowNodeMapper;
import cn.taqu.gonghui.common.service.GeneralService;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.SmsUtil;
import cn.taqu.gonghui.common.vo.ApprovalFlowVO;
import cn.taqu.gonghui.common.vo.CanQuitVO;
import cn.taqu.gonghui.common.vo.PassRejectVO;
import cn.taqu.gonghui.common.vo.QuitGuildFlowVO;
import cn.taqu.gonghui.live.service.LiveSoaService;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.service.HostModifyRecordService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.impl.TeamHostServiceImpl;
import cn.taqu.gonghui.system.vo.CommonHostInfo;
import cn.taqu.gonghui.system.vo.FlowNodeVo;
import cn.taqu.gonghui.system.vo.TeamHostVo;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 退会申请审批流程
 * <AUTHOR>
 */
@Service
public class QuitGuildFlow extends AbstractFlow<QuitGuildFlowVO> {

    @Resource
    private ApprovalFlowNodeMapper approvalFlowNodeMapper;

    @Resource
    private ApprovalFlowMapper approvalFlowMapper;

    @Resource
    private TeamHostMapper teamHostMapper;

    @Autowired
    private GeneralService generalService;

    @Autowired
    private HostModifyRecordService hostModifyRecordService;

    @Autowired
    private TeamHostServiceImpl teamHostService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private LiveSoaService liveSoaService;

    private static final String SYSTEM_USER = "system";

    private static final String YMD_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    protected Integer getFlowType() {
        return FlowTypeEnum.QUIT_GUILD.getCode();
    }

    @Override
    public ApprovalFlowVO buildApprovalFlowVO(QuitGuildFlowVO quitGuildFlowVO) {
        Date now = new Date();
        CommonHostInfo commonHostInfo = quitGuildFlowVO.getCommonHostInfo();

        // 组装审批主表数据
        ApprovalFlow approvalFlow = new ApprovalFlow();
        approvalFlow.setFlowTitle(commonHostInfo.getNickName() + "发起退会申请");
        approvalFlow.setOrgId(commonHostInfo.getOrgId());
        approvalFlow.setOrgName(commonHostInfo.getOrgName());
        approvalFlow.setHostUuid(commonHostInfo.getHostUuid());
        approvalFlow.setTeamId(Optional.ofNullable(commonHostInfo.getTeamId()).orElse(0L));
        approvalFlow.setAvatar(commonHostInfo.getAvatar());
        approvalFlow.setNickName(Optional.ofNullable(commonHostInfo.getNickName()).orElse(""));
        approvalFlow.setApplyLevel(commonHostInfo.getApplyLevel());
        approvalFlow.setCreateTime(now);
        approvalFlow.setCreateUser(commonHostInfo.getNickName());
        approvalFlow.setApplyTime(now);
        approvalFlow.setFlowStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        approvalFlow.setFlowType(FlowTypeEnum.QUIT_GUILD.getCode());
        approvalFlow.setApplyReason(quitGuildFlowVO.getApplyReason());
        approvalFlow.setModifyTime(now);
        approvalFlow.setModifyUser(commonHostInfo.getNickName());
        approvalFlow.setModifyTime(now);
        approvalFlow.setLiveNo(commonHostInfo.getLiveNo());

        List<ApprovalFlowNode> approvalFlowNodeList = new ArrayList<>();
        int nodeIndex = 1;
        // 如果有团队负责人则先审批
        if (Objects.nonNull(commonHostInfo.getTeamAccountUuid())) {
            ApprovalFlowNode teamNode = buildNode(commonHostInfo.getTeamAccountUuid(), UserTypeEnum.LEADER
                    , nodeIndex,++nodeIndex, commonHostInfo.getTeamMobile());
            approvalFlowNodeList.add(teamNode);
        }
        // 机构管理员节点
        ApprovalFlowNode orgNode = buildNode(commonHostInfo.getOrgAccountUuid(), UserTypeEnum.MANAGER, nodeIndex, 0, commonHostInfo.getOrgMobile());
        approvalFlowNodeList.add(orgNode);

        // 组装ApprovalFlowVO
        ApprovalFlowVO approvalFlowVO = new ApprovalFlowVO();
        approvalFlowVO.setApprovalFlow(approvalFlow);
        approvalFlowVO.setApprovalFlowNodeList(approvalFlowNodeList);

        return approvalFlowVO;
    }

    @Override
    protected void preCreate(QuitGuildFlowVO quitGuildFlowVO) {
        // 校验之前是否申请过
        String hostUuid = quitGuildFlowVO.getCommonHostInfo().getHostUuid();
        ApprovalFlow record = new ApprovalFlow();
        record.setHostUuid(hostUuid);
        record.setFlowStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        ApprovalFlow approvalFlow = approvalFlowMapper.selectOneByCondition(record);
        if (Objects.nonNull(approvalFlow)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(), "申请失败，存在审核中数据");
        }
    }

    /**
     * 构建Node
     * @param nodeUser
     * @param typeEnum
     * @param nodeIndex
     * @param nextIndex
     */
    private ApprovalFlowNode buildNode(String nodeUser, UserTypeEnum typeEnum, Integer nodeIndex, Integer nextIndex, String mobile) {
        Date now = new Date();
        ApprovalFlowNode node = new ApprovalFlowNode();
        node.setNodeName(typeEnum.getInfo());
        node.setNodeRole(typeEnum.getType());
        node.setNodeUser(nodeUser);
        node.setMobile(mobile);
        node.setNodeIndex(nodeIndex);
        node.setNextIndex(nextIndex);
        node.setNodeStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        node.setCreateTime(now);
        node.setCreateUser(SYSTEM_USER);
        node.setModifyTime(now);
        node.setModifyUser(SYSTEM_USER);

        return node;
    }

    @Override
    public void afterCreate(ApprovalFlowVO approvalFlowVO) {
        // 通知第一位审核人
        List<ApprovalFlowNode> approvalFlowNodeList = approvalFlowVO.getApprovalFlowNodeList();
        ApprovalFlowNode firstNode = approvalFlowNodeList.get(0);
        if (Objects.nonNull(firstNode.getMobile())) {
            // 查询负责人手机号
            String content = String.format("主播%s发起了退会申请，请前往公会系统审批，7天内未审批将自动通过，登录地址：「%s」"
                    , approvalFlowVO.getApprovalFlow().getNickName(), "https://union.taqu.cn");
            SmsUtil.sendMsg(firstNode.getMobile(), content);
        }
    }

    @Override
    public void preReview(PassRejectVO passRejectVO) {
        // 查询当前单据是否已完成
        ApprovalFlow approvalFlow = approvalFlowMapper.selectOneById(passRejectVO.getFlowId());
        if (!approvalFlow.getFlowStatus().equals(ApprovalStatusEnum.WAIT_APPROVAL.getCode())) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value()
                    ,"单据已" + ApprovalStatusEnum.getName(approvalFlow.getFlowStatus()) + "，请勿操作");
        }

        // 同意的时候再判断
        if (ApprovalStatusEnum.PASS.getCode().equals(passRejectVO.getAction())) {
            // 判断余额是否为0 等上线一个月后这个逻辑不用判断 因为申请的时候已经有判断了 主要解决上线后审核中的数据
            CanQuitVO canQuitVO = liveSoaService.canQuitNormalConsortia(approvalFlow.getHostUuid());
            if (canQuitVO.getCan_quit().equals(Constants.ZERO)) {
                throw new ServiceException(CodeStatus.LOGIC_ERROR.value()
                        ,"当前主播账户余额大于0，暂时无法进行退会，请点击【拒绝】后，告知主播在app端“我-我的收益-直播收益”提现清零，并再次发起退会申请");
            }
        }

        // 查询当前审批单需要修改的角色和用户
        ApprovalFlowNode currentNode = approvalFlowNodeMapper.selectCurrentNode(passRejectVO.getFlowId());
        // 如果查无数据 则无法审核
        if (Objects.isNull(currentNode)) {
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value(),"查无审批节点数据");
        }
        SysUser user = tokenService.getLoginUser(ServletUtils.getRequest()).getUser();
        // 对比审核人角色是否满足条件
        if (!currentNode.getNodeRole().equals(user.getUserType())) {
            // 兼容机构负责人没有改uuid的情况
            if (currentNode.getNodeRole().equals(UserTypeEnum.MANAGER.getType())) {
                // 如果是机构管理人 并且机构id一致 那么允许
                if (user.getUserType().equals(UserTypeEnum.MANAGER.getType()) && approvalFlow.getOrgId().equals(user.getOrgId())) {
                    return;
                }
            }
            throw new ServiceException(CodeStatus.LOGIC_ERROR.value()
                    , "您不是当前节点审批人,当前审批人角色" + UserTypeEnum.getByType(currentNode.getNodeRole()) + "手机号:"+currentNode.getMobile());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterReview(PassRejectVO passRejectVO) {
        ApprovalFlowNode node = passRejectVO.getCurrentNode();

        // 驳回
        if (node.getNodeStatus().equals(ApprovalStatusEnum.REJECT.getCode())) {
            ApprovalFlow approvalFlow = approvalFlowMapper.selectOneById(node.getFlowId().intValue());
            String hostUuid = approvalFlow.getHostUuid();
            TeamHostVo teamHost = teamHostMapper.selectTeamHostByUuid(hostUuid);
            String content = "退会申请已被驳回，驳回理由：" + node.getRemark() + "，目前依旧归属在"
                    + teamHost.getOrgName() + "公会，退会申请被拒后七个工作日内无法再提交申请";
            generalService.sendSecretary(hostUuid, content);
            return;
        }

        // 全部同意
        if (node.getNextIndex().equals(0)) {
            ApprovalFlow approvalFlow = approvalFlowMapper.selectOneById(node.getFlowId().intValue());
            String hostUuid = approvalFlow.getHostUuid();
            // 中转公会
            teamHostService.transferGuild(hostUuid);
            ModifyRecordInfoDTO dto = hostModifyRecordService.getModifyRecordInfo(hostUuid, HostOperateTypeEnum.QUIT_GUILD_MOVE);
            StringBuilder sb = new StringBuilder("您已成功退出");
            if (Objects.nonNull(dto)) {
                sb.append(dto.getOldOrgName());
            }
            sb.append("公会，现归属于中转公会，可直接开播或自行选择接受新的公会邀约");
            generalService.sendSecretary(hostUuid, sb.toString());
            return;
        }

        // 第一节点同意
        if (node.getNodeIndex().equals(Constants.YES_1)
                && node.getNodeStatus().equals(ApprovalStatusEnum.PASS.getCode())
        ) {
            ApprovalFlowNode nextCondition = new ApprovalFlowNode();
            nextCondition.setFlowId(passRejectVO.getFlowId().longValue());
            nextCondition.setNodeStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
            ApprovalFlowNode nextNode = approvalFlowNodeMapper.selectOneByCondition(nextCondition);
            ApprovalFlow approvalFlow = approvalFlowMapper.selectOneById(node.getFlowId().intValue());
            if (Objects.nonNull(nextNode.getMobile())) {
                String content = String.format("主播%s发起了退会申请，请前往公会系统审批，7天内未审批将自动通过，登录地址：「%s」"
                        , approvalFlow.getNickName(), "https://union.taqu.cn");
                SmsUtil.sendMsg(nextNode.getMobile(), content);
            }
        }
    }

    /**
     * 节点详情
     * @param flowId
     * @return
     */
    public List<FlowNodeVo> nodeDetail(Integer flowId) {
        List<ApprovalFlowNode> nodeList = approvalFlowNodeMapper.selectByFlowId(flowId.longValue());
        List<FlowNodeVo> nodeVoList = new ArrayList<>();
        for (ApprovalFlowNode node : nodeList) {
            FlowNodeVo vo = new FlowNodeVo();
            vo.setNodeName(node.getNodeName());
            vo.setRemark(node.getRemark());
            vo.setMobile(node.getMobile());
            String reviewDate = Objects.nonNull(node.getReviewTime()) ? DateFormatUtils.format(node.getReviewTime(), YMD_FORMAT) : "";
            vo.setReviewDate(reviewDate);
            vo.setNodeStatusName(ApprovalStatusEnum.getName(node.getNodeStatus()));
            nodeVoList.add(vo);
        }

        return nodeVoList;
    }

}
