package cn.taqu.gonghui.common.service.flow;

import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.chatroom.service.ChatRoomService;
import cn.taqu.gonghui.common.constant.*;
import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.common.mapper.ApprovalFlowMapper;
import cn.taqu.gonghui.common.service.GeneralService;
import cn.taqu.gonghui.common.vo.ApprovalFlowVO;
import cn.taqu.gonghui.common.vo.ChatRoomInviteVO;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.common.vo.PassRejectVO;
import cn.taqu.gonghui.soa.CertificationService;
import cn.taqu.gonghui.system.dto.ModifyRecordInfoDTO;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 聊天室邀约入会
 * <AUTHOR>
 */
@Service
@Slf4j
public class ChatRoomInviteFlow extends AbstractFlow<ChatRoomInviteVO> {

    @Resource
    private ApprovalFlowMapper approvalFlowMapper;

    @Resource
    private TeamHostMapper teamHostMapper;

    @Autowired
    private GeneralService generalService;

    @SoaReference("account")
    private CertificationService certificationService;

//    @Resource
//    private

    @EtcdValue("biz.chatroom.inviteLinkUrl")
    private static String inviteLinkUrl;

    @Lazy
    @Autowired
    private ChatRoomService chatRoomService;

    private static final String SYSTEM_USER = "system";

    @Override
    protected Integer getFlowType() {
        return FlowTypeEnum.CHATROOM_INVITE.getCode();
    }

    @Override
    public ApprovalFlowVO buildApprovalFlowVO(ChatRoomInviteVO chatRoomInviteVO) {
        Date now = new Date();
        LoginUser loginUser = chatRoomInviteVO.getLoginUser();

        // 组装审批主表数据
        ApprovalFlow approvalFlow = new ApprovalFlow();
        approvalFlow.setFlowTitle(chatRoomInviteVO.getLoginUser().getUsername() + "邀请" + chatRoomInviteVO.getNickName() + "入会");
        approvalFlow.setOrgId(chatRoomInviteVO.getOrgId());
        approvalFlow.setOrgName(chatRoomInviteVO.getOrgName());
        approvalFlow.setHostUuid(chatRoomInviteVO.getHostUuid());
        approvalFlow.setTeamId(chatRoomInviteVO.getTeamId());
        approvalFlow.setAvatar(chatRoomInviteVO.getAvatar());
        approvalFlow.setNickName(chatRoomInviteVO.getNickName());
        approvalFlow.setApplyLevel(chatRoomInviteVO.getApplyLevel());
        approvalFlow.setCreateTime(now);
        approvalFlow.setCreateUser(loginUser.getUsername());
        approvalFlow.setApplyTime(now);
        approvalFlow.setFlowStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        approvalFlow.setFlowType(FlowTypeEnum.CHATROOM_INVITE.getCode());
        approvalFlow.setApplyReason(Optional.ofNullable(chatRoomInviteVO.getRemark()).orElse(""));
        approvalFlow.setModifyTime(now);
        approvalFlow.setModifyUser(loginUser.getUsername());
        approvalFlow.setModifyTime(now);
        approvalFlow.setLiveNo(chatRoomInviteVO.getLiveNo());

        List<ApprovalFlowNode> approvalFlowNodeList = new ArrayList<>();
        ApprovalFlowNode node = new ApprovalFlowNode();
        node.setNodeName("艺人审核");
        node.setNodeRole(UserTypeEnum.DEFAULT.getType());
        node.setNodeUser(chatRoomInviteVO.getHostUuid());
        node.setMobile(chatRoomInviteVO.getMobile());
        node.setMobileDigest(chatRoomInviteVO.getMobile());
        node.setMobileCipher(chatRoomInviteVO.getMobile());
        node.setNodeIndex(1);
        node.setNextIndex(0);
        node.setNodeStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        node.setCreateTime(now);
        node.setCreateUser(SYSTEM_USER);
        node.setModifyTime(now);
        node.setModifyUser(SYSTEM_USER);
        approvalFlowNodeList.add(node);

        // 组装ApprovalFlowVO
        ApprovalFlowVO approvalFlowVO = new ApprovalFlowVO();
        approvalFlowVO.setApprovalFlow(approvalFlow);
        approvalFlowVO.setApprovalFlowNodeList(approvalFlowNodeList);

        return approvalFlowVO;
    }

    @Override
    protected void preCreate(ChatRoomInviteVO ChatRoomInviteVO) {
    }

    @Override
    public void afterCreate(ApprovalFlowVO approvalFlowVO) {
        // 发送小秘书
        ApprovalFlow approvalFlow = approvalFlowVO.getApprovalFlow();
        String content = approvalFlow.getOrgName() + "邀请您加入他们的大家庭，收益奖励丰厚诱人，查看详情";
        String replaceContent = approvalFlow.getOrgName() + "邀请您加入他们的大家庭，收益奖励丰厚诱人，%s";
        String replaceStr = "查看详情";
        String link = inviteLinkUrl + "?inviteId=" + approvalFlow.getId();
        generalService.sendLinkSecretary(approvalFlow.getHostUuid(), content, replaceContent, replaceStr, link);
    }

    @Override
    public void preReview(PassRejectVO passRejectVO) {
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void afterReview(PassRejectVO passRejectVO) {
        ApprovalFlow approvalFlow = approvalFlowMapper.selectOneById(passRejectVO.getFlowId());
        if (approvalFlow.getFlowStatus().equals(InviteStatusEnum.AGREE.getCode())) {
            // 设置info & 设定公会
            ModifyRecordInfoDTO infoDTO = new ModifyRecordInfoDTO();
            infoDTO.setNewTeamId(approvalFlow.getTeamId());
            infoDTO.setReason("邀请聊天室艺人，同意入会");
            chatRoomService.changeTeamForChat(approvalFlow.getHostUuid(), approvalFlow.getTeamId(), JsonUtils.objectToString(infoDTO), "", HostOperateTypeEnum.HOST_MOVE, TeamTypeEnum.TALK_TEAM);
            // 更新实名
            TeamHost teamHost = teamHostMapper.selectHostOneByUuid(approvalFlow.getHostUuid(), TeamTypeEnum.TALK_TEAM.getValue());
            if (Objects.nonNull(teamHost)) {
                Map<String,Object> certMap = certificationService.getInfoByUuid(approvalFlow.getHostUuid());
                if(MapUtils.isEmpty(certMap)){
                    throw new ServiceException(CodeStatus.PARAM_ERROR.value(), "请主播先登录APP实名认证");
                }
                teamHost.setRealName(certMap.get("real_name").toString());
                teamHost.setInviteTime(System.currentTimeMillis() / 1000);
                log.info("inviteTime:teamHost:{}", teamHost);
                teamHostMapper.updateByPrimaryKey(teamHost);
            } else {
                log.info("查无teamHost信息,{}", approvalFlow.getHostUuid());
            }

            // 小秘书通知
            String content = "恭喜，您已成功加入" + approvalFlow.getOrgName();
            generalService.sendSecretary(approvalFlow.getHostUuid(), content);
        }
        if (approvalFlow.getFlowStatus().equals(InviteStatusEnum.REJECT.getCode())) {
            // 小秘书通知
            String content = "很遗憾，您拒绝了" + approvalFlow.getOrgName() + "的入会邀约";
            generalService.sendSecretary(approvalFlow.getHostUuid(), content);
        }
    }

}
