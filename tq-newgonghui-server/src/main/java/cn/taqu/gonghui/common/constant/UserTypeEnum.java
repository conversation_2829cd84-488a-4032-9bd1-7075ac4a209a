package cn.taqu.gonghui.common.constant;

/**
 * <AUTHOR>
 * @Date 2021/4/29
 */
public enum UserTypeEnum {

    /**
     * 空
     */
    DEFAULT(0,"default","空角色"),
    MANAGER(1,"manager", "机构管理人"),
    LEADER(2,"leader", "团队负责人"),
    AGENTER(3,"agenter", "经纪人");

    private final Integer type;
    private final String code;
    private final String info;

    UserTypeEnum(Integer type,String code, String info)
    {
        this.type = type;
        this.code = code;
        this.info = info;
    }

    public static UserTypeEnum getByCode(String code) {
        for (UserTypeEnum typeEnum : UserTypeEnum.values()) {
            if (code.equals(typeEnum.getCode())) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 是否在角色类型中
     * @param type
     * @return
     */
    public static Boolean getInType(Integer type) {
        for (UserTypeEnum typeEnum : UserTypeEnum.values()) {
            if (type.equals(typeEnum.getType())) {
                return true;
            }
        }
        return false;
    }

    public static String getByType(Integer type) {
        for (UserTypeEnum typeEnum : UserTypeEnum.values()) {
            if (type.equals(typeEnum.getType())) {
                return typeEnum.getInfo();
            }
        }
        return null;
    }

    public Integer getType() { return type; }
    public String getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }


}
