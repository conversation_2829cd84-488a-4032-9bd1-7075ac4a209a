package cn.taqu.gonghui.common.mapper;

import cn.taqu.gonghui.common.entity.ApprovalFlow;
import cn.taqu.gonghui.common.vo.ApprovalCustomVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ApprovalFlowMapper extends BaseMapper<T> {

    /**
     * 写入并获取id
     * @param record
     * @return
     */
    void insertGetId(ApprovalFlow record);

    /**
     * 更新数据
     * @param record
     */
    void updateByRecord(ApprovalFlow record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    ApprovalFlow selectOneById(Integer id);

    /**
     * 根据条件查询
     * @param record
     * @return
     */
    ApprovalFlow selectOneByCondition(ApprovalFlow record);

    /**
     * xx
     * @param sql
     */
    void updateSql(String sql);

    /**
     * 根据条件查询list
     * @param record
     * @return
     */
    List<ApprovalFlow> selectByCondition(ApprovalFlow record);

    /**
     * 批量更新
     * @param list
     */
    void updateBatchStatus(List<Long> list);

    /**
     * 根据主键批量查询
     * @param idList
     * @return
     */
    List<ApprovalFlow> selectBatchIdList(List<Long> idList);

    /**
     * 自定义查询
     * @param approvalCustomVO
     * @return
     */
    List<ApprovalFlow> selectByCustom(ApprovalCustomVO approvalCustomVO);
}