package cn.taqu.gonghui.common.utils;

import cn.taqu.core.web.protocol.http.RequestParams;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * <AUTHOR>
 * @Date 2021/5/13
 */
public class BuildRequestParams {

    private static final String TAQU_SOA_REQUEST_PAMRM_KEY = "form";


    public static RequestParams getRequestParams(HttpServletRequest request){
        String form = null;
        Enumeration paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = (String) paramNames.nextElement();
            if(TAQU_SOA_REQUEST_PAMRM_KEY.equals(paramName)){
                String[] paramValues = request.getParameterValues(paramName);
                if (paramValues.length == 1) {
                    String paramValue = paramValues[0];
                    if (paramValue.length() != 0) {
                        form = paramValue;
                        break;
                    }
                }
            }
        }
        RequestParams params = new RequestParams();
        params.setForm(form);
        return params;
    }
}
