package cn.taqu.gonghui.common.constant.chatroom;

/**
 * 公会-聊天室公会-数据统计-导出类型
 */
public enum ExportDataType {
    org("公会数据"),
    host("艺人数据");

    public static String getName(Integer type) {
        for (ExportDataType value : values()) {
            if (value.ordinal() == type) {
                return value.getName();
            }
        }
        return "";
    }

    ExportDataType(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }
}
