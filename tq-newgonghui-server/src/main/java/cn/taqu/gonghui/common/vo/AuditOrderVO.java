package cn.taqu.gonghui.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AuditOrderVO {

    /**
     * 申请单类型
     * 1. 报备房
     */
    private Integer type;

    private String remark;


    private List<ItemVO> applyItems;

    @Getter
    @Setter
    public static class ItemVO {
        private String auditorUuid;
        private Integer layer;
    }
}
