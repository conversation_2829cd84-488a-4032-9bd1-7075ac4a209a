package cn.taqu.gonghui.common.configuration;

import cn.taqu.core.utils.SpringContextHolder;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.exporter.common.TextFormat;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * A custom HttpServlet to combine jvm-metrics with business-metrics
 * that is much more convenient for prometheus administration.
 */
public class TaquMetricsServlet extends HttpServlet {
    private CollectorRegistry registry;

    public TaquMetricsServlet() {
        this(CollectorRegistry.defaultRegistry);
    }

    public TaquMetricsServlet(CollectorRegistry registry) {
        this.registry = registry;
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        resp.setStatus(200);
        resp.setContentType("text/plain; version=0.0.4; charset=utf-8");
        BufferedWriter writer = new BufferedWriter(resp.getWriter());

        try {
            TextFormat.write004(writer, this.registry.filteredMetricFamilySamples(this.parse(req)));
            PrometheusMeterRegistry prometheusMeterRegistry = SpringContextHolder.getBean(PrometheusMeterRegistry.class);
            if(prometheusMeterRegistry != null){
                writer.write(prometheusMeterRegistry.scrape());
            }
            writer.flush();
        } finally {
            writer.close();
        }

    }

    private Set<String> parse(HttpServletRequest req) {
        String[] includedParam = req.getParameterValues("name[]");
        return (Set)(includedParam == null ? Collections.emptySet() : new HashSet(Arrays.asList(includedParam)));
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        this.doGet(req, resp);
    }
}
