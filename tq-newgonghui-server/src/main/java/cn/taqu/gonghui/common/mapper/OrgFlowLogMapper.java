package cn.taqu.gonghui.common.mapper;

import cn.taqu.gonghui.common.entity.OrgFlowLog;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface OrgFlowLogMapper {

    /**
     * 根据id查询
     * @param orgId
     * @return
     */
    List<OrgFlowLog> selectByOrgId(Long orgId);

    /**
     * 写入
     * @param orgFlowLog
     */
    void insert(OrgFlowLog orgFlowLog);

    /**
     * 批量写入
     * @param logList
     */
    void insertBatch(List<OrgFlowLog> logList);

}