package cn.taqu.gonghui.common.configuration;

import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BeanConfig {

    @Configuration
    static class PrometheusConfiguration {
        @Bean
        public ServletRegistrationBean registerPrometheusExporterServlet() {
            return new ServletRegistrationBean<>(new TaquMetricsServlet(), "/metrics");
        }
    }
}
