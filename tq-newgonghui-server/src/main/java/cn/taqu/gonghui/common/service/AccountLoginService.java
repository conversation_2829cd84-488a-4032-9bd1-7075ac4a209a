package cn.taqu.gonghui.common.service;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.common.configuration.SmsCodeAuthenticationToken;
import cn.taqu.gonghui.common.constant.ApplyStatusEnum;
import cn.taqu.gonghui.common.constant.FormStatusEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.common.vo.LoginVo;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.service.AgreementInfoService;
import cn.taqu.gonghui.system.service.SysMenuService;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.service.TeamService;
import cn.taqu.gonghui.system.vo.RoleVo;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 登录校验方法
 *
 */
@Component
@Slf4j
public class AccountLoginService
{

    @Autowired
    private TokenService tokenService;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysMenuService sysMenuService;
    @Autowired
    private AgreementInfoService agreementInfoService;
    /**
     * 登录验证
     * @param mobile 手机号
     * @param verify 验证码
     * @return
     */
    public LoginVo login(String mobile, String verify)
    {
        // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
        Authentication authentication = authenticationManager.authenticate(new SmsCodeAuthenticationToken(mobile));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        log.info("loginUser={}", JSON.toJSONString(loginUser));
        // 生成token
        String token = tokenService.createToken(loginUser);
        LoginVo loginVo = new LoginVo();
        loginVo.setToken(token);
        loginVo.setUserType(loginUser.getUser().getUserType());
        loginVo.setUserName(loginUser.getUsername());
        loginVo.setOrgId(loginUser.getUser().getOrgId());
        if(loginUser.getUser().getOrgId() != null && loginUser.getUser().getOrgId()!=0){
            Organization organization = organizationMapper.selectByPrimaryKey(loginUser.getUser().getOrgId());
            if(organization == null){
                loginVo.setApplyStatus(ApplyStatusEnum.DEFAULT.getValue());
                loginVo.setFormStatus(FormStatusEnum.DEFAULT.getValue());
                loginVo.setLiveSettlementeType(1);
            }else{
                loginVo.setApplyStatus(organization.getApplyStatus());
                loginVo.setFormStatus(organization.getFormStatus());
                loginVo.setLiveSettlementeType(organization.getLiveSettlementeType());
                loginVo.setSettlementeType(organization.getSettlementeType());
                loginVo.setCreditGrade(organization.getCreditGrade());
            }
        }else{
            loginVo.setApplyStatus(ApplyStatusEnum.DEFAULT.getValue());
            loginVo.setFormStatus(FormStatusEnum.DEFAULT.getValue());
        }
        if(UserTypeEnum.MANAGER.getType().equals(loginUser.getUser().getUserType())){
            Organization organization = organizationMapper.selectByPrimaryKey(loginUser.getUser().getOrgId());
            List<Integer> values = new ArrayList<>();
            if(organization.getLivePermissions() == 1){
                values.add(TeamTypeEnum.LIVE_TEAM.getValue());
            }
            if(organization.getQuliaoPermissions() == 1){
                values.add(TeamTypeEnum.CALL_TEAM.getValue());
            }
            if(organization.getChatRoomPermissions() == 1){
                values.add(TeamTypeEnum.TALK_TEAM.getValue());
            }
            loginVo.setTeamType(TeamTypeEnum.getByValues(values));
            loginVo.setSignMap(agreementInfoService.getSignMap(loginUser.getUser().getOrgId()));
        }
        if(!UserTypeEnum.MANAGER.getType().equals(loginUser.getUser().getUserType()) && !UserTypeEnum.DEFAULT.getType().equals(loginUser.getUser().getUserType())){
            List<RoleVo> rolelist = sysUserService.getRoleByUserId(loginUser.getUser().getUserId());
          if(CollectionUtils.isNotEmpty(rolelist)){
              List<Integer> values = rolelist.stream().map(RoleVo::getType).collect(Collectors.toList());
              loginVo.setTeamType(TeamTypeEnum.getByValues(values));
          }else{
              loginVo.setTeamType(null);
          }
            loginVo.setSignMap(agreementInfoService.getSignMap(loginUser.getUser().getOrgId()));
        }
        if(UserTypeEnum.MANAGER.getType().equals(loginUser.getUser().getUserType())){
            loginVo.setManager(true);
        }else{
            loginVo.setManager(false);
        }

        return loginVo;
    }


    public void refreshAccount(String accountUuid){
        if(StringUtils.isEmpty(accountUuid)){
            throw new ServiceException("account_uuid_empty","account_uuid为空");
        }
        SysUser sysUser = sysUserService.selectUserByAccountUuid(accountUuid);
        if(sysUser == null){
            throw new ServiceException("account_uuid_empty",accountUuid+"用户不存在");
        }
        Set<String> permissions = sysMenuService.selectMenuPermsByUserId(sysUser.getAccountUuid());
        LoginUser loginUser = new LoginUser(sysUser, permissions);
        tokenService.refreshToken(loginUser);

    }

}
