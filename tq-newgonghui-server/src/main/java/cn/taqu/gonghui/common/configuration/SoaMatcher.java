package cn.taqu.gonghui.common.configuration;


import org.springframework.security.web.util.matcher.RequestMatcher;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;


public class SoaMatcher implements RequestMatcher {

    private Map<String, Set<String>> excludeServiceMethods;

    public SoaMatcher() {
        this.excludeServiceMethods = new HashMap<>();
    }

    public SoaMatcher excludeService(String service) {
        return this.excludeMethods(service, null);
    }

    public SoaMatcher excludeMethod(String service, String method) {
        return this.excludeMethods(service, Arrays.asList(method));
    }

    public SoaMatcher excludeMethods(String service, Collection<String> methods) {
        if(service == null || (service = service.trim()).isEmpty()) {
            throw new IllegalArgumentException("service could not be blank");
        }

        Set<String> nonBlankMethods = null;
        if(methods != null) {
            nonBlankMethods = methods.stream().filter(s -> s != null && !s.trim().isEmpty()).map(s -> s.trim()).collect(Collectors.toSet());
        }

        if(nonBlankMethods == null || nonBlankMethods.isEmpty()) {
            this.getExcludeServiceMethodSet(service).add("*");
        } else {
            this.getExcludeServiceMethodSet(service).addAll(nonBlankMethods);
        }
        return this;
    }

    private Set<String> getExcludeServiceMethodSet(String service) {
        Set<String> methodSet = excludeServiceMethods.get(service);
        if(methodSet == null) {
            methodSet = new HashSet<>();
            excludeServiceMethods.put(service, methodSet);
        }
        return methodSet;
    }



    @Override
    public boolean matches(HttpServletRequest request) {
        //service包含*表示排除所有，不拦截
        if(excludeServiceMethods.containsKey("*")) {
            return false;
        }

        String service = request.getParameter("service");
        String method = request.getParameter("method");

        Set<String> methodSet = excludeServiceMethods.get(service);
        //不包含这个service，拦截
        if(methodSet == null) {
            return true;
        }
        //方法包含*表示排除所有service，不拦截
        if(methodSet.contains("*") || methodSet.contains(method)) {
            return false;
        }

        return true;
    }
}
