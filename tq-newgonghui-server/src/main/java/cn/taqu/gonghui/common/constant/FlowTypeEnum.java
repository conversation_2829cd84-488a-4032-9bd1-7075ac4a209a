package cn.taqu.gonghui.common.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * 流程类型
 * <AUTHOR>
 */
@Getter
public enum FlowTypeEnum {

    /**
     * 退会申请
     */
    QUIT_GUILD(1, "退会申请"),
    CHATROOM_INVITE(2, "聊天室邀请入会"),
    ;

    private Integer code;
    private String name;

    FlowTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code) {
        for (FlowTypeEnum location : FlowTypeEnum.values()) {
            if (Objects.equals(location.getCode(), code)) {
                return location.getName();
            }
        }
        return "";
    }

}
