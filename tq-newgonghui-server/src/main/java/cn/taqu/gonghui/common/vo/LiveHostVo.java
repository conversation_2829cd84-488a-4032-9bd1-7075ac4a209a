package cn.taqu.gonghui.common.vo;

import lombok.Data;

@Data
public class LiveHostVo {
        /**
         * 经纪人uuid
         */
        private String businessman_uuid;
        /**
         * 时间
         */
        private String dayTime;
        /**
         * 经纪人姓名
         */
        private String businessman_name;
        /**
         * 主播uuid
         */
        private String host_uuid;
        /**
         * 昵称
         */
        private String nickname;
        /**
         * 工会id
         */
        private String consortia_id;
        /**
         * 工会名称
         */
        private String consortia_name;
        /**
         * 鲜花
         */
        private String flower;
        /**
         * 时长
         */
        private String total_live_time;
        /**
         * 观看人数
         */
        private String viewer;
        /**
         * 送礼人数
         */
        private String send;
        /**
         * 粉丝
         */
        private String fans;
        /**
         * 消息数
         */
        private String message;
        /**
         * 直播状态
         */
        private String live_status;
        /**
         * 有效天数
         */
        private String valid_live;
        /**
         * 收益(趣豆)
         */
        private String amount;
        /**
         * 游戏收入
         */
        private Integer game_amount;
        /**
         * 游戏收入占比
         */
        private String game_amount_ratio;

        //新增主播相关
        private String host_amount; //收益
        private String valid_day; //有效天数
        private String card_id; //身份证
        private String add_consortia_time; //加入公会时间
        private String teamName; //团队名称

        /**
         * 贝壳相关
         */
        private String total_score;

        private Integer shell_amount;

        private String shell_ratio;


}
