package cn.taqu.gonghui.common.configuration;


import org.springframework.security.web.util.matcher.RequestMatcher;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;


public class SoaIncludeMatcher implements RequestMatcher {

    private Map<String, Set<String>> includeServiceMethods;

    public SoaIncludeMatcher() {
        this.includeServiceMethods = new HashMap<>();
    }

    public SoaIncludeMatcher excludeService(String service) {
        return this.includeMethods(service, null);
    }

    public SoaIncludeMatcher includeMethod(String service, String method) {
        return this.includeMethods(service, Arrays.asList(method));
    }

    public SoaIncludeMatcher includeMethods(String service, Collection<String> methods) {
        if(service == null || (service = service.trim()).isEmpty()) {
            throw new IllegalArgumentException("service could not be blank");
        }

        Set<String> nonBlankMethods = null;
        if(methods != null) {
            nonBlankMethods = methods.stream().filter(s -> s != null && !s.trim().isEmpty()).map(s -> s.trim()).collect(Collectors.toSet());
        }

        if(nonBlankMethods == null || nonBlankMethods.isEmpty()) {
            this.getExcludeServiceMethodSet(service).add("*");
        } else {
            this.getExcludeServiceMethodSet(service).addAll(nonBlankMethods);
        }
        return this;
    }

    private Set<String> getExcludeServiceMethodSet(String service) {
        Set<String> methodSet = includeServiceMethods.get(service);
        if(methodSet == null) {
            methodSet = new HashSet<>();
            includeServiceMethods.put(service, methodSet);
        }
        return methodSet;
    }



    @Override
    public boolean matches(HttpServletRequest request) {


        String service = request.getParameter("service");
        String method = request.getParameter("method");

        Set<String> methodSet = includeServiceMethods.get(service);
        //不包含这个service，拦截
        if(methodSet != null && methodSet.contains(method)) {
            return true;
        }else{
            return false;
        }


    }
}
