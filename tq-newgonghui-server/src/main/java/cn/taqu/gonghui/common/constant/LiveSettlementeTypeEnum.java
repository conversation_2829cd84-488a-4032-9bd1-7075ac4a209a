package cn.taqu.gonghui.common.constant;

import java.util.Objects;

/**
 * 申请状态
 */
public enum LiveSettlementeTypeEnum {

    MONTH(1), // 月结
    WEEK(2), // 周结
    NEW_MONTH(3); // 新月结


    LiveSettlementeTypeEnum(Integer value) {
        this.value = value;
    }

    private int value;

    public int getValue() {
        return this.value;
    }

    /**
     * 校验结算类型是否存在业务
     * @param settlemetType
     * @return
     */
    public static Boolean isSettlementTypeInValues(Integer settlemetType){
        for (LiveSettlementeTypeEnum typeEnum: LiveSettlementeTypeEnum.values()) {
            if(Objects.equals(typeEnum.getValue(), settlemetType)){
                return true;
            }
        }
        return false;
    }
}
