package cn.taqu.gonghui.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 时间转化
 *
 * <AUTHOR>
 * @date 2022/11/15 5:35 下午
 */
public class DateTimeUtil {

    /**
     * 8位时间格式：到天
     */
    private static final String DATE_STRING_DAY = "yyyy-MM-dd";

    /**
     * 10位时间格式：到小时
     */
    private static final String DATE_STRING_HOUR = "yyyy-MM-dd HH";

    /**
     * 12位时间格式：到分钟
     */
    private static final String DATE_STRING_MINUTE = "yyyy-MM-dd HH:mm";

    /**
     * 14位时间格式：到秒
     */
    private static final String DATE_STRING_SECOND = "yyyy-MM-dd HH:mm:ss";

    /**
     * 时间戳转8位时间格式(天)
     *
     * @param milli
     * @return
     */
    public static String milliToStringDay(Long milli) {
        return milliToStringHandle(milli, DATE_STRING_DAY);
    }

    /**
     * 时间戳转10位时间格式(小时)
     *
     * @param milli
     * @return
     */
    public static String milliToStringHour(Long milli) {
        return milliToStringHandle(milli, DATE_STRING_HOUR);
    }

    /**
     * 时间戳转12位时间格式(分钟)
     *
     * @param milli
     * @return
     */
    public static String milliToStringMinute(Long milli) {
        return milliToStringHandle(milli, DATE_STRING_MINUTE);
    }

    /**
     * 时间戳转14位时间格式（秒）
     *
     * @param milli
     * @return
     */
    public static String milliToStringSecond(Long milli) {
        return milliToStringHandle(milli, DATE_STRING_SECOND);
    }

    /**
     * 时间字符串转毫秒值（分钟）
     *
     * @param dateTime
     * @return
     */
    public static Long stringToMilliMinute(String dateTime) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern(DATE_STRING_MINUTE));
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 截取到分钟毫秒值
     *
     * @return
     */
    public static Long sliceToMinute(Long milli) {
        String minute = DateTimeUtil.milliToStringMinute(milli);
        if (StringUtils.isBlank(minute)) {
            return 0L;
        }
        return DateTimeUtil.stringToMilliMinute(minute);
    }

    /**
     * 时间戳转时间格式模型
     *
     * @param milli
     * @param pattern
     * @return
     */
    private static String milliToStringHandle(Long milli, String pattern) {
        if (milli == null) {
            return Strings.EMPTY;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(milli), ZoneId.systemDefault());
        return dateTimeFormatter.format(localDateTime);
    }
}
