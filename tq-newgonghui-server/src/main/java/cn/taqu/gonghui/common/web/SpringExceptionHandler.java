package cn.taqu.gonghui.common.web;

import cn.taqu.core.common.constant.ICodeStatus;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ApiException;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.log.Log;
import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.TeamStatusEnum;
import cn.taqu.gonghui.common.exception.CustomException;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.Team;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.service.SysMenuService;
import cn.taqu.gonghui.system.service.TeamEmployeeService;
import cn.taqu.gonghui.system.service.TeamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.UnsatisfiedServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ControllerAdvice
@Slf4j
public class SpringExceptionHandler extends ResponseEntityExceptionHandler implements ResponseBodyAdvice<Object>  {

	@Autowired
    private SysMenuService menuService;
	@Autowired
	private TokenService tokenService;
	@Autowired
	private TeamEmployeeService teamEmployeeService;
	@Autowired
	private TeamService teamService;


	@ExceptionHandler(value = { ServiceException.class })
	public @ResponseBody
	JsonResult serviceHandelException(ServiceException ex, HttpServletResponse response, HttpServletRequest request) throws IOException{
		ICodeStatus codeStatus = ex.getCodeStatus();
		if(codeStatus == null) {
			codeStatus = CodeStatus.getCodeStatus(ex.getMessage());
		}
		log.warn("{}-{}-{}", codeStatus.value(), codeStatus.getReasonPhrase(), ex.getMessage());
		log.warn("[j47]请求参数异常： " + request.getParameter("form"), ex);
		return httpErrorJson(response, codeStatus);
	}

	@ExceptionHandler(value = { CustomException.class })
	public @ResponseBody
	Map serviceHandelCustomException(ServiceException ex, HttpServletResponse response, HttpServletRequest request) throws IOException{
		ICodeStatus codeStatus = ex.getCodeStatus();
		if(codeStatus == null) {
			codeStatus = CodeStatus.getCodeStatus(ex.getMessage());
		}
		log.warn("{}-{}-{}", codeStatus.value(), codeStatus.getReasonPhrase(), ex.getMessage());
		Map map = new HashMap();
		map.put("response_status","success");
		map.put("msg","请求成功");
		java.util.Map info = new HashMap();
		info.put("data",null);
		info.put("extra","");
		map.put("info",info);
		map.put("response_status","failed");
		map.put("msg",ex.getMessage());
		log.warn("[j47]请求参数异常： " + request.getParameter("form"), ex);
		return map;
	}

	@ExceptionHandler(value = { AuthenticationException.class })
	public @ResponseBody
	JsonResult handelAuthenticationException(AuthenticationException ex, HttpServletResponse response, HttpServletRequest request){
		log.warn(SysCodeStatus.ERROR.getReasonPhrase(), ex.getMessage());
		String method = request.getParameter("method");
		if("authLogin".equals(method)){
           return JsonResult.success(0);
		}
		return httpErrorJson(response, CodeStatus.LOGIN_OUT_EXPIRED);
	}

	@ExceptionHandler(value = { AccessDeniedException.class })
	public @ResponseBody
	JsonResult handerAccessDeniedException(AccessDeniedException ex, HttpServletResponse response, HttpServletRequest request){
		String service = request.getParameter("service");
		String method = request.getParameter("method");
		String menuName = menuService.getMenuNameByServiceAndMethod(service, method);
		response.setHeader("Content-Type", "application/json");
		response.setStatus(HttpServletResponse.SC_OK);
		if(StringUtils.isEmpty(menuName)){
			menuName="";
		}

		// 团队禁用
		SysUser user = tokenService.getLoginUser(request).getUser();
		List<TeamEmployee> teamEmployeeList = teamEmployeeService.getOneByUserId(user.getUserId());
		for(TeamEmployee teamEmployee:teamEmployeeList){
			if(teamEmployee!=null){
				Team team = teamService.detail(teamEmployee.getTeamId());
				// 当前用户的团队被禁用 收回权限
				if(team !=null && TeamStatusEnum.NO_VALID.getValue() == team.getStatus()){
					log.info("异常处理器-当前用户所在团队{}被禁用了 成员id:{}团队id:{}",team.getTeamName(),teamEmployee.getEmployeeId(),teamEmployee.getTeamId());
					return JsonResult.failed("团队：【" +team.getTeamName() + "】异常!");
				}
			}
		}
		return JsonResult.failed("sorry! 您无权限访问 "+menuName);
	}

	@ResponseBody
	@ExceptionHandler(value = { ApiException.class })
	public final JsonResult handleException(ApiException ex,HttpServletRequest request) {
		Log.warn(ex.getMessage());
		log.warn("[j47]请求参数异常： " + request.getParameter("form"), ex);
		return JsonResult.failed(ex.getMessage());
	}

	@ResponseBody
	@ExceptionHandler(value = { UnsatisfiedServletRequestParameterException.class })
	public final JsonResult unsatisfiedServletRequestParameterException(UnsatisfiedServletRequestParameterException ex, HttpServletRequest request) {
		String message = MessageFormat.format("错误码[{0}]:{1}", SysCodeStatus.NOT_METHOD.value(), SysCodeStatus.NOT_METHOD.getReasonPhrase());
		log.error(request.getQueryString() + " " + message);
		log.warn("[j47]请求参数异常： " + request.getParameter("form"), ex);
		return JsonResult.failedCode(SysCodeStatus.NOT_METHOD);
	}

	@ExceptionHandler(value = Exception.class)
	public @ResponseBody
	JsonResult handleUncaughtException(Exception ex, HttpServletResponse response, HttpServletRequest request) throws IOException {
		log.warn(SysCodeStatus.ERROR.getReasonPhrase(), ex);
		log.warn("[j47]请求参数异常： " + request.getParameter("form"), ex);
		return httpErrorJson(response, SysCodeStatus.ERROR);
	}

	private JsonResult httpErrorJson(HttpServletResponse response, ICodeStatus codeStatus){
		response.setHeader("Content-Type", "application/json");
        response.setStatus(HttpServletResponse.SC_OK);
        return JsonResult.failedCode(codeStatus);
	}

	@Override
	public boolean supports(MethodParameter methodParameter, Class<? extends HttpMessageConverter<?>> aClass) {
		return true;
	}

	@Override
	public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class<? extends HttpMessageConverter<?>> aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
		if(o instanceof JsonResult){
            JsonResult jsonResult = (JsonResult)o;
			if("soa_call_failed".equals(jsonResult.getCode()) || "soa_fail".equals(jsonResult.getCode())){
				return JsonResult.failed("服务端异常，请联系管理员！");
			}
			return jsonResult;
		}
		if(o instanceof Map){
			return (Map)o;
		}
		return null;

	}

//
//	@Override
//	public Object beforeBodyWrite(JsonResult jsonResult, MethodParameter methodParameter, MediaType mediaType, Class<? extends HttpMessageConverter<?>> aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
//
//         if("soa_call_failed".equals(jsonResult.getCode()) || "soa_fail".equals(jsonResult.getCode())){
//             return JsonResult.failed("服务端异常，请联系管理员！");
//		 }
//		return jsonResult;
//	}
}
