package cn.taqu.gonghui.common.constant;

import java.text.MessageFormat;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @date 2021/6/3
 * @Desc redisKey manage classs
 */
public class RedisKeyConstant {

    /**
     * 主播分润比例
     */
    public static final Formater SHARING_PROFIT_RATE_UUID = new Formater("sharing:profit:rate:uuid:{0}");

    public static final Formater SLIDE_SESSIONID = new Formater("slide:sessionid:{0}");

    //记录uuid 和 teamid 的关系
    public static final Formater UUID_TEAMID_RELATION = new Formater("j47:UUID_TEAMID_RELATION:{0}");

    /**
     * 每个月记录主播首次申请分润调整key
     */
    public static final Formater MONTH_FIRST_APPLY = new Formater("j47:live:profit:"
            + (Calendar.getInstance().get(Calendar.MONTH) + 1)+ ":{0}");

    /**
     * key format
     */
    public static class Formater {
        private String pattern;
        private MessageFormat format;
        private Formater(String pattern) {
            this.pattern = pattern;
            this.format = new MessageFormat(this.pattern);
        }
        public String setArg(Object... args) {
            return this.format.format(args);
        }
        public String getPattern() {
            return this.pattern;
        }
        @Override
        public String toString() {
            return this.format.format(null);
        }
    }

    public static void main(String[] args) {
        System.out.println(RedisKeyConstant.UUID_TEAMID_RELATION.setArg("33"));
    }
}
