package cn.taqu.gonghui.common.service;


import cn.taqu.gonghui.common.vo.req.AuditItemReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuditOrderBizService extends BaseBizService {

    private final AuditOrderService auditOrderService;


    /**
     * 通过审核
     */

    public void accept4Manager(AuditItemReq auditItemReq) {
        auditOrderService.accept(auditItemReq.getItemNo(),
                auditItemReq.getAuditorName(),
                auditItemReq.getRemark());
    }

    /**
     * 拒绝
     */

    public void reject4Manager(AuditItemReq auditItemReq) {
        auditOrderService.reject(auditItemReq.getItemNo(),
                auditItemReq.getAuditorName(),
                auditItemReq.getRemark());
    }


}
