package cn.taqu.gonghui.common.service;

import cn.taqu.core.common.client.SoaClientFactory;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.chatroom.util.FilterCondition;
import cn.taqu.gonghui.chatroom.vo.HostRoomData;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.ConditionEnum;
import cn.taqu.gonghui.common.domain.DataPageRequest;
import cn.taqu.gonghui.common.domain.DataPageResult;
import cn.taqu.gonghui.common.domain.DataSortRequest;
import com.fasterxml.jackson.core.type.TypeReference;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 大数据对接接口
 */
@Service
@Slf4j
public class DataManager implements InitializingBean {

    private Config root;

    public static final ThreadLocal<List<Sorting>> sortingHolder = new ThreadLocal<>();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Sorting {

        private String key;

        private String order;
    }



    @Override
    public void afterPropertiesSet() throws Exception {
        root = ConfigFactory.load("bigdata/root.conf");
    }

    /**
     * 分页查询数据
     */

    public DataPageResult<Map<String, Object>> dateApiSoaV2(String apiKey,
                                                            Map<String, Object> params,
                                                            DataPageRequest dataPageRequest,
                                                            List<DataSortRequest> sortList
    ) {
        Config apiConfig = root.getConfig("api");
        if (!apiConfig.hasPath(apiKey)) {
            return DataPageResult.emptyResult();
        }
        String api = apiConfig.getString(apiKey);

        Map<String, String> sortMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sortList)) {
            sortList.forEach(sort -> sortMap.put(sort.getSort(), sort.getSortType()));
        }

        SoaResponse soaResponse = doCall("api", "executeV2", api,
                params,
                dataPageRequest,
                sortMap
        );
        if (soaResponse.fail()) {
            log.error("Request API error: api:{}, params:{}, datePageRequest:{}, sortMap:{}, {}",
                    api,
                    JsonUtils.objectToString(params),
                    JsonUtils.objectToString(dataPageRequest),
                    JsonUtils.objectToString(sortMap),
                    soaResponse.getMsg());
            throw new ServiceException(SysCodeStatus.ERROR);
        }

        String dataStr = soaResponse.getData();
        if ("[]".equals(dataStr)) {
            return DataPageResult.emptyResult();
        }
        Map data = JsonUtils.stringToObject2(soaResponse.getData(), Map.class);
        if (data.containsKey("data")) {
            Object dataList = data.get("data");

            if (dataList instanceof List) {

                long total = (int) data.get("total");
                long page = (int) data.get("page");
                long pageSize = (int) data.get("pageSize");
                DataPageResult<Map<String, Object>> result = new DataPageResult<>();
                result.setList((List<Map<String, Object>>) dataList);
                result.setTotal(total);
                result.setPageSize(pageSize);
                result.setPage(page);
                return result;
            }
        }
        return DataPageResult.emptyResult();
    }

    /**
     * 分页查询数据
     */

    public DataPageResult<Map<String, Object>> dateApiSoaWithExcuteV2(String apiKey,
                                                            Map<String, Object> params,
                                                            DataPageRequest dataPageRequest,
                                                            DataSortRequest dataSortRequest

    ) {
        Config apiConfig = root.getConfig("api");
        if (!apiConfig.hasPath(apiKey)) {
            return DataPageResult.emptyResult();
        }
        String api = apiConfig.getString(apiKey);

        Map<String, String> sortMap = new HashMap<>();
        sortMap.put(dataSortRequest.getSort(), dataSortRequest.getSortType());
        SoaResponse soaResponse = doCall("api", "executeV2", api,
                params,
                dataPageRequest,
                sortMap
        );
        if (soaResponse.fail()) {
            log.error("Request API error: {}", soaResponse.getMsg());
            throw new ServiceException(SysCodeStatus.ERROR);
        }

        String dataStr = soaResponse.getData();
        if ("[]".equals(dataStr)) {
            return DataPageResult.emptyResult();
        }
        Map data = JsonUtils.stringToObject2(soaResponse.getData(), Map.class);
        if (data.containsKey("data")) {
            Object dataList = data.get("data");

            if (dataList instanceof List) {

                long total = (int) data.get("total");
                long page = (int) data.get("page");
                long pageSize = (int) data.get("pageSize");
                DataPageResult<Map<String, Object>> result = new DataPageResult<>();
                result.setList((List<Map<String, Object>>) dataList);
                result.setTotal(total);
                result.setPageSize(pageSize);
                result.setPage(page);
                return result;
            }
        }
        return DataPageResult.emptyResult();
    }

    @Deprecated
    public List<Map<String, Object>> dateApiSoa(String apiKey,
                                                List<FilterCondition> params,
                                                DataPageRequest dataPageRequest
    ) {
        Config apiConfig = root.getConfig("api");
        if (!apiConfig.hasPath(apiKey)) {
            return Collections.emptyList();
        }
        String api = apiConfig.getString(apiKey);

        SoaResponse soaResponse = doCall("api", "execute", api,
                params,
                dataPageRequest
        );
        if (soaResponse.fail()) {
            log.error("Request API error: {}", soaResponse.getMsg());
            throw new ServiceException(SysCodeStatus.ERROR);
        }
        if (dataPageRequest != null) {
            String dataStr = soaResponse.getData();
            if ("[]".equals(dataStr)) {
                return Collections.emptyList();
            }
            Map data = JsonUtils.stringToObject2(soaResponse.getData(), Map.class);
            if (data.containsKey("data")) {
                Object dataList = data.get("data");
                if (dataList instanceof List) {
                    return (List<Map<String, Object>>) dataList;
                }
            }
            return Collections.emptyList();

        }
        return JsonUtils.stringToObject2(soaResponse.getData(), List.class);
    }


    /**
     * 通用api接口
     *
     * @param startTime
     * @param endTime
     * @param teamIds
     * @param page
     */
    public <T> T dataApiSoa(String api, String startTime, String endTime, List<Long> teamIds, List<String> chatUuids, Integer chatStatus, Map page, Class<T> clazz) {

        List<FilterCondition> filterConditions = buildFilterConditions(startTime, endTime, teamIds, chatUuids, chatStatus);
        List<Sorting> sorting = sortingHolder.get();
        SoaResponse soaResponse;
        try {
            soaResponse = doCall("api", "execute", api, filterConditions, page, sorting);
            // SOA 请求
            if (soaResponse.fail()) {
                log.error("Request Data API v1 error: {}", soaResponse.getMsg());
                throw new ServiceException(SysCodeStatus.ERROR);
            }
            return JsonUtils.stringToObject2(soaResponse.getData(), clazz);
        } finally {
            sortingHolder.remove();
        }
    }

    /**
     * 通用api接口
     *
     * @param startTime
     * @param endTime
     * @param teamIds
     * @param page
     */
    public <T> T dataApiSoa(String api, String startTime, String endTime, List<Long> teamIds, List<String> chatUuids, Integer chatStatus, Map page, TypeReference<T> clazz) {
        List<FilterCondition> filterConditions = buildFilterConditions(startTime, endTime, teamIds, chatUuids, chatStatus);
        SoaResponse soaResponse = null;
        if (MapUtils.isEmpty(page)) {
            soaResponse = doCall("api", "execute", api, filterConditions);
        } else {
            soaResponse = doCall("api", "execute", api, filterConditions, page);
        }
        // SOA 请求
        if (soaResponse.fail()) {
            log.error("Request API error: {}", soaResponse.getMsg());
            throw new ServiceException(SysCodeStatus.ERROR);
        }

        return JsonUtils.stringToObject2(soaResponse.getData(), clazz);
    }

    /**
     * 构建艺人的条件
     *
     * @param startTime
     * @param endTime
     * @param teamIds
     * @return
     */

    private List<FilterCondition> buildFilterConditions(String startTime, String endTime, List<Long> teamIds, List<String> hostUuids, Integer chatStatus) {
        log.info("构建查询条件：起始时间:{},结束时间：{},团队集合:{},hostUuids:{},chatStatus:{}", startTime, endTime, teamIds, hostUuids, chatStatus);
        List<FilterCondition> filterConditions = new ArrayList<>();
        FilterCondition range = FilterCondition.builder()
                .key("dt")
                .operator(ConditionEnum.BETWEEN.getValue())
                .value(Arrays.asList(startTime, endTime))
                .build();
        filterConditions.add(range);
        if (CollectionUtils.isNotEmpty(teamIds)) {
            FilterCondition teamIdInList = FilterCondition.builder()
                    .key("consortia_id")
                    .operator(ConditionEnum.IN.getValue())
                    .value(teamIds)
                    .build();
            filterConditions.add(teamIdInList);
        }
        if (CollectionUtils.isNotEmpty(hostUuids)) {
            FilterCondition hostUuidInList = FilterCondition.builder()
                    .key("account_uuid")
                    .operator(ConditionEnum.IN.getValue())
                    .value(hostUuids)
                    .build();
            filterConditions.add(hostUuidInList);
        }
        return filterConditions;
    }


    private SoaResponse doCall(String service, String method, Object... params) {
        return SoaClientFactory.create(SoaServer.JAVA.DATA_API).call(service, method, params);
    }

    /**
     * 每周报备房数据
     * @param weekFirstDateStr
     * @return
     */
    public List<HostRoomData> weeklyReportingDownload(String weekFirstDateStr) {
        Map<String, String> paramMap = new HashMap<>(10);
        paramMap.put("dt", weekFirstDateStr);
        log.info("soa请求weeklyReportingDownload,req={}", paramMap);
        try {
            SoaResponse soaResponse = doCall("api", "executeV2", "/backendTechnology/chatroom/weeklyReportingDownload", paramMap);
            log.info("soaResponse={}", JsonUtils.objectToString(soaResponse));
            return JsonUtils.stringToObject(soaResponse.getData(), new TypeReference<List<HostRoomData>>() {});
        } catch (Exception e) {
            log.error("soa请求weeklyReportingDownload失败，msg:{},req:{}",e.getMessage(), paramMap);
            throw new ServiceException(CodeStatus.OPERATION_FAIL_ERROR.value(), "系统异常（f218）");
        }
    }


}
