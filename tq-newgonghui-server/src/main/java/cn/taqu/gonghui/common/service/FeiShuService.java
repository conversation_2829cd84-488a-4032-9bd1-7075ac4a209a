package cn.taqu.gonghui.common.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.gonghui.common.constant.ApplyStatusEnum;
import cn.taqu.gonghui.common.constant.callback.feishu.FeiShuRelationType;
import cn.taqu.gonghui.common.constant.callback.feishu.FeiShuStatusEnum;
import cn.taqu.gonghui.common.utils.PictureUtil;
import cn.taqu.gonghui.common.utils.RedisUtil;
import cn.taqu.gonghui.common.vo.feishu.ApprovalInstanceInfo;
import cn.taqu.gonghui.common.vo.feishu.ApprovalInstanceRequest;
import cn.taqu.gonghui.common.vo.feishu.FeiShuResponse;
import cn.taqu.gonghui.common.vo.feishu.FeiShuTokenResponse;
import cn.taqu.gonghui.common.vo.feishu.UploadResponse;
import cn.taqu.gonghui.soa.SsomsService;
import cn.taqu.gonghui.system.config.FeishuConfig;
import cn.taqu.gonghui.system.entity.BusinessLicense;
import cn.taqu.gonghui.system.entity.ChargePerson;
import cn.taqu.gonghui.system.entity.FeishuAuditRelation;
import cn.taqu.gonghui.system.entity.HostScreenshot;
import cn.taqu.gonghui.system.entity.LegalPerson;
import cn.taqu.gonghui.system.entity.OrgCooperationFlow;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.mapper.BusinessLicenseMapper;
import cn.taqu.gonghui.system.mapper.ChargePersonMapper;
import cn.taqu.gonghui.system.mapper.FeishuAuditRelationMapper;
import cn.taqu.gonghui.system.mapper.HostScreenshotMapper;
import cn.taqu.gonghui.system.mapper.LegalPersonMapper;
import cn.taqu.gonghui.system.mapper.OpeningPermitMapper;
import cn.taqu.gonghui.system.mapper.OrgCooperationFlowMapper;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.search.SsoUserSearch;
import cn.taqu.gonghui.system.service.OrganizationService;
import cn.taqu.gonghui.system.service.impl.QiNiuService;
import cn.taqu.gonghui.system.vo.SsoUserCombobox;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/15 18:15
 **/
@Slf4j
@Service
public class FeiShuService {

    private static final String ACCESS_TOKEN_CACHE_KEY = "feishu:access:token";
    public static final String UPLOAD_URL = "https://www.feishu.cn/approval/openapi/v2/file/upload";
    public static final String ACCESS_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal";
    public static final String APPROVAL_URL = "https://open.feishu.cn/open-apis/approval/v4/instances";
    public static final String APPROVAL_INSTANCE_URL = "https://open.feishu.cn/open-apis/approval/v4/instances/";

    @Autowired
    private RedisUtil redisUtil;
    @SoaReference(application = "ssoms", value = "ssoms")
    private SsomsService ssomsService;
    @Autowired
    private FeishuAuditRelationMapper feishuAuditRelationMapper;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private QiNiuService qiNiuService;
    @Autowired
    private ChargePersonMapper chargePersonMapper;
    @Autowired
    private LegalPersonMapper legalPersonMapper;
    @Autowired
    private BusinessLicenseMapper businessLicenseMapper;
    @Autowired
    private HostScreenshotMapper hostScreenshotMapper;
    @Autowired
    private OrgCooperationFlowMapper orgCooperationFlowMapper;

    public static void main(String[] args) {
    }

    /**
     * 创建机构流程审批
     * @param organization
     * @param operator 单点登录账号，比如测试环境的admin
     * @return 返回null则表示创建失败，需要提示前端
     */
    public String approvalAdd(Organization organization, String operator) {
        SsoUserCombobox combobox = this.getSsoUserCombobox(operator);
        if (combobox == null) {
            return null;
        }
        ApprovalInstanceRequest instance = new ApprovalInstanceRequest();
        instance.setBusinessType(FeiShuRelationType.ADD_ORG.ordinal());
        instance.setApproval_code(FeishuConfig.ADD_ORG_APPROVAL_CODE);
        instance.setUser_id(combobox.getLarkUserId());
        instance.setOpen_id(combobox.getOpenId());
        List<ApprovalInstanceRequest.Form> formList = new ArrayList<>();
        formList.add(new ApprovalInstanceRequest.Form("instructions", "input", organization.getOrgName() + "机构提交机构入驻申请，因为该机构为外部机构，故由运营" + combobox.getName() + "代为发起机构入驻申请流程"));
        // 机构信息
        ApprovalInstanceRequest.Form orgInfo = new ApprovalInstanceRequest.Form("orgInfo", "fieldList");
        List<ApprovalInstanceRequest.Form> orgDetailList = new ArrayList<>();
        orgDetailList.add(new ApprovalInstanceRequest.Form("orgName", "input", organization.getOrgName()));
        orgDetailList.add(new ApprovalInstanceRequest.Form("chargePersonPhone", "input", organization.getChargePersonPhone()));
        orgDetailList.add(new ApprovalInstanceRequest.Form("businessPerson", "input", organization.getBusinessPerson()));
        String orgType = "直播";
        if (organization.getChatRoomPermissions() == 1) {
            orgType = "聊天室";
        } else if (organization.getLivePermissions() == 1) {
            orgType = "直播";
        } else if (organization.getQuliaoPermissions() == 1) {
            orgType = "趣聊";
        }
        orgDetailList.add(new ApprovalInstanceRequest.Form("orgType", "input", orgType));
        orgDetailList.add(new ApprovalInstanceRequest.Form("createTime", "date", Instant.ofEpochSecond(organization.getCreateTime()).atZone(ZoneId.of("UTC")).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)));
        if (organization.getLiveSettlementeType() != null) {
            orgDetailList.add(new ApprovalInstanceRequest.Form("liveSettlementType", "input", organization.getLiveSettlementeType() == 1 ? "月结" : "周结"));
        }
        orgDetailList.add(new ApprovalInstanceRequest.Form("content", "textarea", organization.getContent()));
        List<List<ApprovalInstanceRequest.Form>> orgValueList = new ArrayList<>();
        orgValueList.add(orgDetailList);
        orgInfo.setValue(orgValueList);
        formList.add(orgInfo);

        // 账号简介
        ApprovalInstanceRequest.Form accountInfo = new ApprovalInstanceRequest.Form("accountInfo", "fieldList");
        List<ApprovalInstanceRequest.Form> accountDetailList = new ArrayList<>();
        accountDetailList.add(new ApprovalInstanceRequest.Form("chargePerson", "input", organization.getChargePerson()));
        accountDetailList.add(new ApprovalInstanceRequest.Form("contactPhone", "input", organization.getContactPhone()));
        accountDetailList.add(new ApprovalInstanceRequest.Form("chargePersonVx", "input", organization.getChargePersonVx()));
        accountDetailList.add(new ApprovalInstanceRequest.Form("chargePersonBirthday", "date", Instant.ofEpochSecond(organization.getChargePersonBirthday()).atZone(ZoneId.of("UTC")).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)));
        accountDetailList.add(new ApprovalInstanceRequest.Form("chargePersonIdCard", "input", organization.getChargePersonIdCard()));
        accountDetailList.add(new ApprovalInstanceRequest.Form("receivingAddress", "input", organization.getReceivingAddress()));
        accountDetailList.add(new ApprovalInstanceRequest.Form("chargePersonEmail", "input", organization.getChargePersonEmail()));
        List<ChargePerson> urls = chargePersonMapper.selectByOrgUuid(organization.getOrgUuid());
        if (CollectionUtil.isNotEmpty(urls)) {
            String url = urls.stream().map(ChargePerson::getUrl).collect(Collectors.joining(","));
            accountDetailList.add(new ApprovalInstanceRequest.Form("chargePersonUrl", "image", this.getUrl(url)));
        }
        List<List<ApprovalInstanceRequest.Form>> accountValueList = new ArrayList<>();
        accountValueList.add(accountDetailList);
        accountInfo.setValue(accountValueList);
        formList.add(accountInfo);

        // 公司信息
        ApprovalInstanceRequest.Form certInfo = new ApprovalInstanceRequest.Form("certInfo", "fieldList");
        List<ApprovalInstanceRequest.Form> certDetailList = new ArrayList<>();
        certDetailList.add(new ApprovalInstanceRequest.Form("legalPerson", "input", organization.getLegalPerson()));
        certDetailList.add(new ApprovalInstanceRequest.Form("legalPersonPhone", "input", organization.getLegalPersonPhone()));
        certDetailList.add(new ApprovalInstanceRequest.Form("legalPersonIdCard", "input", organization.getLegalPersonIdCard()));
        certDetailList.add(new ApprovalInstanceRequest.Form("enterpriseName", "input",  organization.getEnterpriseName()));
        certDetailList.add(new ApprovalInstanceRequest.Form("socialUnifiedCreditCode", "input", organization.getSocialUnifiedCreditCode()));
        certDetailList.add(new ApprovalInstanceRequest.Form("premises", "input", organization.getPremises()));
        List<LegalPerson> persons = legalPersonMapper.getAllByOrgUuid(organization.getOrgUuid());
        if (CollectionUtil.isNotEmpty(persons)) {
            String url = persons.stream().map(LegalPerson::getUrl).collect(Collectors.joining(","));
            certDetailList.add(new ApprovalInstanceRequest.Form("legalPersonUrl", "image", this.getUrl(url)));
        }
        List<BusinessLicense> businessList = businessLicenseMapper.getAllByOrgUuid(organization.getOrgUuid());
        if (CollectionUtil.isNotEmpty(businessList)) {
            String url = businessList.stream().map(BusinessLicense::getUrl).collect(Collectors.joining(","));
            certDetailList.add(new ApprovalInstanceRequest.Form("businessLicenseUrl", "image", this.getUrl(url)));
        }
        List<List<ApprovalInstanceRequest.Form>> certValueList = new ArrayList<>();
        certValueList.add(certDetailList);
        certInfo.setValue(certValueList);
        formList.add(certInfo);

        // 银行信息
        ApprovalInstanceRequest.Form financialInfo = new ApprovalInstanceRequest.Form("financialInfo", "fieldList");
        List<ApprovalInstanceRequest.Form> financialDetailList = new ArrayList<>();
        financialDetailList.add(new ApprovalInstanceRequest.Form("accountBankName", "input", organization.getAccountBankName()));
        financialDetailList.add(new ApprovalInstanceRequest.Form("accountArea", "input", organization.getProvince() + organization.getCity()));
        financialDetailList.add(new ApprovalInstanceRequest.Form("accountName", "input", organization.getAccountName()));
        financialDetailList.add(new ApprovalInstanceRequest.Form("chargePersonPhone", "input", organization.getChargePersonPhone()));
        financialDetailList.add(new ApprovalInstanceRequest.Form("chargePersonWx", "input", organization.getChargePersonVx()));
        List<List<ApprovalInstanceRequest.Form>> financialValueList = new ArrayList<>();
        financialValueList.add(financialDetailList);
        financialInfo.setValue(financialValueList);
        formList.add(financialInfo);

        // 其他信息
        ApprovalInstanceRequest.Form otherInfo = new ApprovalInstanceRequest.Form("otherInfo", "fieldList");
        List<ApprovalInstanceRequest.Form> otherDetailList = new ArrayList<>();
        List<OrgCooperationFlow> flowList = orgCooperationFlowMapper.getAllByOrgUuid(organization.getOrgUuid());
        if (CollectionUtil.isNotEmpty(flowList)) {
            String url = flowList.stream().map(OrgCooperationFlow::getUrl).collect(Collectors.joining(","));
            otherDetailList.add(new ApprovalInstanceRequest.Form("orgCooperationFlowUrl", "image", this.getUrl(url)));
        }
        List<HostScreenshot> shotList = hostScreenshotMapper.getAllByOrgUuid(organization.getOrgUuid());
        if (CollectionUtil.isNotEmpty(shotList)) {
            String url = shotList.stream().map(HostScreenshot::getUrl).collect(Collectors.joining(","));
            otherDetailList.add(new ApprovalInstanceRequest.Form("hostScreenshotUrl", "image", this.getUrl(url)));
        }
        List<List<ApprovalInstanceRequest.Form>> otherValueList = new ArrayList<>();
        otherValueList.add(otherDetailList);
        otherInfo.setValue(otherValueList);
        formList.add(otherInfo);
        instance.setForm(JSONUtil.toJsonStr(formList));

        return this.getFeishuApprovalResponse(instance, organization);
    }

    /**
     * 修改机构信息发起飞书流程审核
     * @param organization
     * @param operator
     * @return 返回null则表示创建失败，需要提示前端
     */
    public String approvalModifyOrg(Organization organization, String operator) {
        // 这里的机构信息不完整，需要再查一次
        Organization orgInfo = organizationService.getOrgInfo(organization.getOrgId());
        BeanUtil.copyProperties(organization, orgInfo, CopyOptions.create().setIgnoreNullValue(true));
        SsoUserCombobox combobox = this.getSsoUserCombobox(operator);
        if (combobox == null) {
            return null;
        }
        ApprovalInstanceRequest instance = new ApprovalInstanceRequest();
        instance.setBusinessType(FeiShuRelationType.MODIFY_ORG.ordinal());
        instance.setApproval_code(FeishuConfig.MODIFY_ORG_APPROVAL_CODE);
        instance.setUser_id(combobox.getLarkUserId());
        instance.setOpen_id(combobox.getOpenId());

        List<ApprovalInstanceRequest.Form> formList = new ArrayList<>();
        formList.add(new ApprovalInstanceRequest.Form("instructions", "input", orgInfo.getOrgName() + "机构提交修改机构信息申请，因为该机构为外部机构，故由运营" + combobox.getName() + "代为发起修改机构信息申请流程"));
        formList.add(new ApprovalInstanceRequest.Form("orgId", "input", orgInfo.getOrgId().toString()));
        formList.add(new ApprovalInstanceRequest.Form("orgName", "input", orgInfo.getOrgName()));
        formList.add(new ApprovalInstanceRequest.Form("chargePerson", "input", orgInfo.getChargePerson()));
        formList.add(new ApprovalInstanceRequest.Form("contactPhone", "input", orgInfo.getContactPhone()));
        formList.add(new ApprovalInstanceRequest.Form("chargePersonVx", "input", orgInfo.getChargePersonVx()));
        formList.add(new ApprovalInstanceRequest.Form("receivingAddress", "input", orgInfo.getReceivingAddress()));
        formList.add(new ApprovalInstanceRequest.Form("chargePersonEmail", "input", orgInfo.getChargePersonEmail()));
        formList.add(new ApprovalInstanceRequest.Form("chargePersonIdCard", "input", orgInfo.getChargePersonIdCard()));
        formList.add(new ApprovalInstanceRequest.Form("chargePersonUrl", "image", this.getUrl(orgInfo.getChargePersonUrl())));
        instance.setForm(JSONUtil.toJsonStr(formList));

        return this.getFeishuApprovalResponse(instance, orgInfo);
    }

    /**
     * 修改财税信息发起飞书流程
     * @param org
     * @param operator
     * @return 返回null则表示创建失败，需要提示前端
     */
    public String approvalFinancial(Organization org, String operator) {
        // 这里的机构信息不完整，需要再查一次
        Organization orgInfo = organizationService.getOrgInfo(org.getOrgId());
        BeanUtil.copyProperties(org, orgInfo, CopyOptions.create().setIgnoreNullValue(true));
        SsoUserCombobox combobox = this.getSsoUserCombobox(operator);
        if (combobox == null) {
            return null;
        }
        ApprovalInstanceRequest instance = new ApprovalInstanceRequest();
        instance.setBusinessType(FeiShuRelationType.MODIFY_FINANCIAL.ordinal());
        instance.setApproval_code(FeishuConfig.MODIFY_FINANCIAL_APPROVAL_CODE);
        instance.setUser_id(combobox.getLarkUserId());
        instance.setOpen_id(combobox.getOpenId());

        List<ApprovalInstanceRequest.Form> formList = new ArrayList<>();
        formList.add(new ApprovalInstanceRequest.Form("instructions", "input", orgInfo.getOrgName() + "机构提交修改财务信息申请，因为该机构为外部机构，故由运营" + combobox.getName() + "代为发起修改财务信息申请流程"));
        formList.add(new ApprovalInstanceRequest.Form("orgId", "input", orgInfo.getOrgId().toString()));
        formList.add(new ApprovalInstanceRequest.Form("orgName", "input", orgInfo.getOrgName()));
        formList.add(new ApprovalInstanceRequest.Form("publicReceivingBankAccount", "input", orgInfo.getPublicReceivingBankAccount()));
        formList.add(new ApprovalInstanceRequest.Form("accountName", "input", orgInfo.getAccountName()));
        formList.add(new ApprovalInstanceRequest.Form("accountBankName", "input", orgInfo.getAccountBankName()));
        formList.add(new ApprovalInstanceRequest.Form("accountArea", "input", orgInfo.getProvince() + orgInfo.getCity()));
        formList.add(new ApprovalInstanceRequest.Form("subBranchName", "input", orgInfo.getSubBranchName()));
        instance.setForm(JSONUtil.toJsonStr(formList));

        return this.getFeishuApprovalResponse(instance, orgInfo);
    }

    /**
     * 修改公会资质信息
     * @param org
     * @param operator
     * @return 返回null则表示创建失败，需要提示前端
     */
    public String approvalCert(Organization org, String operator) {
        // 这里的机构信息不完整，需要再查一次
        Organization orgInfo = organizationService.getOrgInfo(org.getOrgId());
        BeanUtil.copyProperties(org, orgInfo, CopyOptions.create().setIgnoreNullValue(true));
        SsoUserCombobox combobox = this.getSsoUserCombobox(operator);
        if (combobox == null) {
            return null;
        }
        ApprovalInstanceRequest instance = new ApprovalInstanceRequest();
        instance.setBusinessType(FeiShuRelationType.MODIFY_CERT.ordinal());
        instance.setApproval_code(FeishuConfig.MODIFY_CERT_APPROVAL_CODE);
        instance.setUser_id(combobox.getLarkUserId());
        instance.setOpen_id(combobox.getOpenId());

        List<ApprovalInstanceRequest.Form> formList = new ArrayList<>();
        formList.add(new ApprovalInstanceRequest.Form("instructions", "input", orgInfo.getOrgName() + "机构提交修改资质信息申请，因为该机构为外部机构，故由运营" + combobox.getName() + "代为发起修改资质信息申请流程"));
        formList.add(new ApprovalInstanceRequest.Form("orgId", "input", orgInfo.getOrgId().toString()));
        formList.add(new ApprovalInstanceRequest.Form("orgName", "input", orgInfo.getOrgName()));
        formList.add(new ApprovalInstanceRequest.Form("enterpriseName", "input", orgInfo.getEnterpriseName()));
        formList.add(new ApprovalInstanceRequest.Form("legalPerson", "input", orgInfo.getLegalPerson()));
        formList.add(new ApprovalInstanceRequest.Form("legalPersonPhone", "input", orgInfo.getLegalPersonPhone()));
        formList.add(new ApprovalInstanceRequest.Form("legalPersonIdCard", "input", orgInfo.getLegalPersonIdCard()));
        formList.add(new ApprovalInstanceRequest.Form("legalPersonUrl", "image", this.getUrl(orgInfo.getLegalPersonUrl())));
        formList.add(new ApprovalInstanceRequest.Form("businessLicenseUrl", "image", this.getUrl(orgInfo.getBusinessLicenseUrl())));
        formList.add(new ApprovalInstanceRequest.Form("socialUnifiedCreditCode", "input", orgInfo.getSocialUnifiedCreditCode()));
        formList.add(new ApprovalInstanceRequest.Form("premises", "input", orgInfo.getPremises()));
        instance.setForm(JSONUtil.toJsonStr(formList));

        return this.getFeishuApprovalResponse(instance, orgInfo);
    }

    public SsoUserCombobox getSsoUserCombobox(String operator) {
        SsoUserSearch search = new SsoUserSearch();
        // 这个条件好像没啥用，会查出所有的，需要再过滤一遍
        search.setName(operator);
        JSONArray array = ssomsService.searchEffectiveSsoUsersV2(search);
        String loginName = "";
        for (int i = 0; i < array.size(); i++) {
            com.alibaba.fastjson.JSONObject object = array.getJSONObject(i);
            if (operator.equals(object.get("name"))) {
                loginName = object.getString("loginname");
            }
        }
        if (StringUtils.isEmpty(loginName)) {
            throw new ServiceException("operator_empty", "运营人员获取失败");
        }
        return ssomsService.getSsoUser(loginName);
    }


    /**
     * 通过审核实例去请求飞书，拿到返回结果
     * @param instance
     * @return
     */
    public String getFeishuApprovalResponse(ApprovalInstanceRequest instance, Organization org) {
        HttpRequest httpRequest = HttpUtil.createPost(APPROVAL_URL);
        httpRequest.bearerAuth(this.getAccessToken());
        httpRequest.contentType("application/json; charset=utf-8");
        String body = JSONUtil.toJsonStr(instance);
        httpRequest.body(body);
        HttpResponse httpResponse = httpRequest.execute();
        if (!httpResponse.isOk()) {
            log.warn("httpResponse返回报错");
            log.warn(httpResponse.body());
            throw new ServiceException("approval_error", "发起流程失败");
        }
        FeiShuResponse feiShuResponse = JSONUtil.toBean(httpResponse.body(), FeiShuResponse.class);
        if (!feiShuResponse.isSuccess()) {
            log.warn("feiShuResponse返回报错");
            log.warn(JSONUtil.toJsonStr(feiShuResponse));
            throw new ServiceException("approval_error", "发起流程失败");
        }
        JSONObject object = JSONUtil.parseObj(feiShuResponse.getData().toString());
        String instanceCode = object.getStr("instance_code");

        // 保存对应关系
        FeishuAuditRelation relation = new FeishuAuditRelation();
        relation.setApprovalCode(instance.getApproval_code());
        relation.setInstanceCode(instanceCode);
        relation.setBusinessId(org.getOrgId());
        relation.setBusinessType(instance.getBusinessType());
        relation.setStatus(FeiShuStatusEnum.PENDING.name());
        relation.setCreateTime(new Date());
        relation.setModifyTime(new Date());
        feishuAuditRelationMapper.insert(relation);
        return instanceCode;
    }

    public ApprovalInstanceInfo getApprovalInstanceInfo(String instanceCode) {
        HttpRequest httpRequest = HttpUtil.createGet(APPROVAL_INSTANCE_URL + instanceCode);
        httpRequest.bearerAuth(this.getAccessToken());
        HttpResponse httpResponse = httpRequest.execute();
        if (!httpResponse.isOk()) {
            log.warn("httpResponse返回报错");
            log.warn(httpResponse.body());
            return null;
        }
        FeiShuResponse feiShuResponse = JSONUtil.toBean(httpResponse.body(), FeiShuResponse.class);
        return JSONUtil.toBean(JSONUtil.parseObj(feiShuResponse.getData()), ApprovalInstanceInfo.class);
    }

    private List<String> getUrl(String urlStr) {
        if (StringUtils.isEmpty(urlStr)) {
            return new ArrayList<>();
        }
        List<String> urlList = new ArrayList<>();
        for (String url : urlStr.split(",")) {
            try {
                Object card = qiNiuService.privateDownloadUrl("card", "https://cardimg.jiaoliuqu.com/" + url);
                if (card == null) {
                    continue;
                }
                UploadResponse uploadResponse = this.uploadImage(card.toString());
                if (uploadResponse != null) {
                    urlList.add(uploadResponse.getCode());
                }
            } catch (Exception e) {
                log.warn("上传文件异常", e);
            }
        }
        return urlList;
    }

    public UploadResponse uploadImage(String url) {
        HttpRequest request = HttpUtil.createPost(UPLOAD_URL);
        request.bearerAuth(this.getAccessToken());
        request.contentType("multipart/form-data");
        request.form("name", URLUtil.getPath(url));
        request.form("type", "image");
        File file = PictureUtil.Url2File(url);
        if (file == null) {
            return null;
        }
        request.form("content", file);
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            System.out.println(response.body());
            return null;
        }
        FeiShuResponse feishuResponse = JSONUtil.toBean(response.body(), FeiShuResponse.class);
        return feishuResponse.isSuccess() ? JSONUtil.toBean(feishuResponse.getData().toString(), UploadResponse.class) : null;
    }

    /**
     * token 有效期为 2 小时，在此期间调用该接口 token 不会改变。
     * 当 token 有效期小于 30 分的时候，再次请求获取 token 的时候，
     * 会生成一个新的 token，与此同时老的 token 依然有效。
     * @return
     */
    public String getAccessToken() {
        // 先不缓存，使用缓存的会报错，慢就慢点吧，流程好歹能走通
        // 找不到再去飞书获取
        JSONObject object = new JSONObject();
        object.set("app_id", FeishuConfig.APP_ID);
        object.set("app_secret", FeishuConfig.APP_SECRET);
        String post = HttpUtil.post(ACCESS_TOKEN_URL, object.toString());
        FeiShuTokenResponse response = JSONUtil.toBean(post, FeiShuTokenResponse.class);
        if (!response.isSuccess()) {
            return "";
        }
        return response.getApp_access_token();
    }

}
