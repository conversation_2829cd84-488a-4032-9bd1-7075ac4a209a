package cn.taqu.gonghui.common.service;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.common.constant.UserStatus;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.service.SysMenuService;
import cn.taqu.gonghui.system.service.SysUserService;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户验证处理
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService
{
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysMenuService sysMenuService;

    @Override
    public UserDetails loadUserByUsername(String mobile) throws UsernameNotFoundException
    {

        if (StringUtils.isNull(mobile))
        {
            log.info("登录用户：{} 不存在.");
            throw new ServiceException("account_not_exsist","登录用户：" + mobile + " 不存在");
        }
        SysUser sysUser = null;
        List<SysUser> sysUsers = sysUserService.selectUserListByMobile(mobile);

        if(CollectionUtils.isEmpty(sysUsers)){
            sysUser = sysUserService.registerAccount(mobile, UserTypeEnum.DEFAULT);
        }else{

            if(sysUsers.size() > 1){
                List<SysUser> okUsers = sysUsers.stream().filter(item -> UserStatus.OK.getCode().equals(item.getStatus())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(okUsers) && okUsers.size() > 1){
                    log.warn("该账号{}登录时异常，存在多条正常相同的号码", mobile);
                    throw new ServiceException("account_exception","该账号存在异常，请联系您的对接运营进行处理[j0009]");
                }else if(CollectionUtils.isNotEmpty(okUsers) && okUsers.size() == 1){
                    sysUser = okUsers.get(0);
                }else{
                    sysUser = sysUsers.get(0);
                }

            }else{
                sysUser = sysUsers.get(0);
            }
        }
         if (UserStatus.DELETED.getCode().equals(sysUser.getStatus()))
        {
            log.info("登录用户：{} 已被注销.", mobile);
            throw new ServiceException("account_deleted","对不起，您的账号：" + mobile + " 已被注销");
        }
         if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus()))
        {
            log.info("登录用户：{} 已被停用.", mobile);
            throw new ServiceException("account_disabled","对不起，您的账号：" + mobile + " 已停用");
        }

         sysUserService.updateLastLoginTime(sysUser.getUserId());

        return createLoginUser(sysUser);
    }

    public UserDetails createLoginUser(SysUser user)
    {
        Set<String> permissions = null;
        // 不是管理员
        if(!UserTypeEnum.MANAGER.getType().equals(user.getUserType()) || !UserTypeEnum.DEFAULT.getType().equals(user.getUserType())){
            permissions = sysMenuService.selectMenuPermsByUserId(user.getAccountUuid());
        }
        return new LoginUser(user, permissions);
    }


}
