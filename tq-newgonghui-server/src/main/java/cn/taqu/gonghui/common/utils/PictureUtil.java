package cn.taqu.gonghui.common.utils;

import cn.hutool.core.util.URLUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/16 16:17
 **/
@Slf4j
public class PictureUtil {

    /**
        将Url转换为File
     */
    public static File Url2File(String url) {
        OutputStream os = null;
        InputStream ins = null;
        File file = null;
        try {
            HttpURLConnection httpUrl = (HttpURLConnection) new URL(url).openConnection();
            httpUrl.connect();
            ins = httpUrl.getInputStream();
            String fileName = URLUtil.getPath(url);
            file = new File(System.getProperty("java.io.tmpdir") + File.separator + fileName);//System.getProperty("java.io.tmpdir")缓存
            if (file.exists()) {
                // TODO 存在就直接返回
                return file;
            }
            os = Files.newOutputStream(file.toPath());
            int bytesRead;
            int len = 8192;
            byte[] buffer = new byte[len];
            while ((bytesRead = ins.read(buffer, 0, len)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            log.warn("url:{" + url + "}，转成file异常", e);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (ins != null) {
                    ins.close();
                }
            } catch (IOException e) {
                log.warn("关闭os,ins异常", e);
            }
        }
        return file;
    }

    public static void main(String[] args) {
        System.out.println(URLUtil.getPath("https://cardimg.jiaoliuqu.com/816540064150007376.jpg?imageView2/1/w/200/h/200&e=1663562887&token=N9v6m6QoLvNJR0NUieJC-l7KdCI7YKroIzKelwdW:4FWBchSu-VW70cCjUBVmDpxIF2Q=").replaceAll("/", ""));
//        Url2File("https://avatar01.jiaoliuqu.com//taqu_android_avatar_101_1638425415628_1_0_50217.JPEG");
        System.out.println(System.getProperty("java.io.tmpdir"));
    }
}
