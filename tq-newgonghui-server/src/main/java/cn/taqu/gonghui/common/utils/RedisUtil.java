package cn.taqu.gonghui.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RedisUtil {

    @Autowired
    private StringRedisTemplate masterStringRedisTemplate;

    public Boolean set(String key, String value, Long number, TimeUnit timeUnit) {
        log.info("当前key=[{}]的过期时间为[{}]秒", key, masterStringRedisTemplate.getExpire(key));
        return masterStringRedisTemplate.opsForValue().setIfAbsent(key, value, number, timeUnit);
    }

    public String get(String key) {
        return masterStringRedisTemplate.opsForValue().get(key);
    }

    public void del(String key) {
        masterStringRedisTemplate.delete(key);
    }

    /**
     * 请求锁 （临时用 要改写注解）
     * @param uniqueKey
     * @return
     */
    public boolean requestLock(String uniqueKey) {
        String prefix = "taqu:j47:tmp:";
        String key = prefix.concat(uniqueKey);
        boolean lockFlag = Boolean.TRUE.equals(masterStringRedisTemplate.hasKey(key));
        set(key, "lock", 3L, TimeUnit.SECONDS);

        return lockFlag;
    }

}
