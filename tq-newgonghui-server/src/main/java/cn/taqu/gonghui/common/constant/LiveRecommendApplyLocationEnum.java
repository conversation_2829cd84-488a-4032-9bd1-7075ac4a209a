package cn.taqu.gonghui.common.constant;

import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * Created by 杜晓东 on 2019/10/18.
 */
@Getter
public enum LiveRecommendApplyLocationEnum {
    HOT_2("100", "热二"),
    BANNER_1("201", "横栏1"),
    BANNER_2("202", "横栏2"),
    BANNER_3("203", "横栏3"),
    BANNER_4("204", "横栏4"),
    BANNER_5("205", "横栏5"),
    BANNER_6("206", "横栏6"),
    BANNER_7("207", "横栏7"),
    BANNER_8("208", "横栏8"),
    BANNER_9("209", "横栏9"),
    BANNER_LOBBY("300", "大厅banner"),
    BANNER_FORUM("400", "社区banner"),
    BANNER_START("500", "开屏"),;


    private String value;
    private String name;

    LiveRecommendApplyLocationEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getName(String value) {
        for (LiveRecommendApplyLocationEnum location : LiveRecommendApplyLocationEnum.values()) {
            if (Objects.equals(location.getValue(), value)) {
                return location.getName();
            }
        }
        return "";
    }

    public static Map<String, String> getMapping() {
        Map<String, String> map = Maps.newHashMap();
        Arrays.stream(LiveRecommendApplyLocationEnum.values()).forEach(item -> {
            map.put(item.getValue(), item.getName());
        });

        return map;
    }

}
