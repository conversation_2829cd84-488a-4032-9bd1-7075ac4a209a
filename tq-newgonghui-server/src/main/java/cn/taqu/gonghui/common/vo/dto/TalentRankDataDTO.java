package cn.taqu.gonghui.common.vo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 才艺榜数据dto
 *
 * <AUTHOR>
 * @date 2024/9/26 1:56 下午
 */
@Data
public class TalentRankDataDTO {

    /**
     * 日期
     */
    private String dt;

    /**
     * 艺人uuid
     */
    @JsonProperty("host_uuid")
    private String hostUuid;

    /**
     * 他趣id
     */
    @JsonProperty("taqu_id")
    private String taquId;

    /**
     * 才艺值排名
     */
    @JsonProperty("talent_rank")
    private Integer talentRank;

    /**
     * 时间段(小时)
     */
    @JsonProperty("time_point")
    private String timePoint;

    /**
     * 公会id
     */
    @JsonProperty("consortia_id")
    private Long consortiaId;

    /**
     * 工会昵称
     */
    @JsonProperty("consortia_name")
    private String consortiaName;

    /**
     * 开麦时长(单位:秒)
     */
    @JsonProperty("meeting_duration")
    private String meetingDuration;

    /**
     * 上麦时长(单位:秒)
     */
    @JsonProperty("meeting_seat_duration")
    private String meetingSeatDuration;

    /**
     * 才艺值
     */
    @JsonProperty("talent_score")
    private Integer talentScore;

}
