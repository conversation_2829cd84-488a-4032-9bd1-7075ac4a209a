package cn.taqu.gonghui.common.utils;


import org.apache.poi.ss.formula.functions.T;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class ReportUtils {

    /**
     * 获取两个日期之间的日期，包括开始结束日期
     *
     * @param beginData 开始日期
     * @param endData   结束日期
     * @return 日期集合
     */
    public static List<String> getBetweenDates(Date beginData, Date endData) {
        List<String> result = new ArrayList<String>();
        LocalDate ld1 = new LocalDate(new DateTime(beginData));
        LocalDate ld2 = new LocalDate(new DateTime(endData));
        if (ld1.equals(ld2)) {
            result.add(new SimpleDateFormat("yyyyMMdd").format(beginData));
        } else {
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(beginData);
            tempStart.add(Calendar.DAY_OF_YEAR, 1);

            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(endData);
            result.add(new SimpleDateFormat("yyyyMMdd").format(beginData));
            while (tempStart.before(tempEnd)) {
                result.add(new SimpleDateFormat("yyyyMMdd").format(tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
            }
            result.add(new SimpleDateFormat("yyyyMMdd").format(endData));
        }
        return result;
    }
}
