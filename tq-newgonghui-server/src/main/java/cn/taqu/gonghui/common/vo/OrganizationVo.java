package cn.taqu.gonghui.common.vo;

import lombok.Data;

import java.util.List;

@Data
public class OrganizationVo {

    private Long id;
    private Long orgId;
    private String uuid; //机构uuid
    private String accountUuid;
    private String orgName; //机构名称
    private String orgUuid; //机构名称
    private String chargePerson; //负责人名字
    private String chargePersonIdCard; //负责人名字
    private String chargePersonPhone; //登录者联系电话
    private String chargePersonEmail; //负责人邮箱
    private String chargePersonVx; //负责人邮箱
    private Long chargePersonBirthday; //负责人出生日期
    private String receivingAddress; //收件地址
    private String legalPerson; //法人姓名
    private String legalPersonIdCard; //法人身份证
    private String businessPerson; //业务对接人的姓名
    private String publicReceivingBankAccount; //对公收款账号
    private String accountName; //开户名
    private String accountBankName; //开户行
    private String province;
    private Integer provinceId;
    private String city;
    private Integer cityId;
    private String subBranchName;
    private Integer orgStatus; //机构状态 1开启 2关闭
    private Integer applyStatus; //1待审核 2审核通过 3审核拒绝
    private Integer remitModifyStatus; // 是否打款信息审核中 0否 1是
    private String businessLicenseUrl; //营业执照url
    private String openingPermitUrl; //开户许可证url
    private String legalPersonUrl; //法人信息身份证, url1,url2,url3   正面,反面,手持
    private String chargePersonUrl; //负责人信息身份证, url1,url2,url3   正面,反面,手持
    private String orgCooperationFlowUrl; //流水截图
    private String hostScreenshotUrl; //主播截图
    private Integer formStatus; //分步表单的状态 1就是跳第一步
    private Boolean check;
    private String socialUnifiedCreditCode;   //社会统一信用代码
    private String enterpriseName;  //企业全名
    private String legalPersonPhone;   //法人手机号
    private String applyLog;    //审核记录
    private String premises;   //经营场所
    private Integer settlementeType;   //结算类型
    private Integer liveSettlementeType;   //结算方式（1-月结算，2-周结算，3-新月结）
    private String vcode; //短信验证码
    private String operator;   //后台当前操作人员
    private String contactPhone;  //机构联系人
    private String content;
    private List<Integer> businessPermissions;

}
