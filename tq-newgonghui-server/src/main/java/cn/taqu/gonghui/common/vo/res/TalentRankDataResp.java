package cn.taqu.gonghui.common.vo.res;

import lombok.Data;

/**
 * 才艺榜数据
 *
 * <AUTHOR>
 * @date 2024/9/26 1:56 下午
 */
@Data
public class TalentRankDataResp {

    /**
     * 日期
     */
    private String dt;

    /**
     * 时间段(小时)
     */
    private String duration;

    /**
     * 才艺值排名
     */
    private Integer rank;

    /**
     * 艺人uuid
     */
    private String accountUuid;

    /**
     * 他趣id
     */
    private String accountId;

    /**
     * 工会昵称
     */
    private String consortiaName;

    /**
     * 开麦时长(单位:秒)
     */
    private String meetingOpenDuration;

    /**
     * 上麦时长(单位:秒)
     */
    private String meetingDuration;

    /**
     * 才艺值
     */
    private Integer receiveAmt;

}
