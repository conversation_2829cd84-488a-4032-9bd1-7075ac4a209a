package cn.taqu.gonghui.common.service;


import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.common.constant.AuditItemStatusEnum;
import cn.taqu.gonghui.common.constant.AuditOrderStatusEnum;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.entity.AuditItem;
import cn.taqu.gonghui.common.entity.AuditOrder;
import cn.taqu.gonghui.common.mapper.AuditItemMapper;
import cn.taqu.gonghui.common.mapper.AuditOrderMapper;
import cn.taqu.gonghui.common.utils.UUID;
import cn.taqu.gonghui.common.vo.AuditOrderVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.taqu.gonghui.common.constant.CodeStatus.EXEC_ERROR;

@Service
@RequiredArgsConstructor
public class AuditOrderService {

    private final AuditOrderMapper auditOrderMapper;

    private final AuditItemMapper auditItemMapper;


    private String buildNo() {
        return UUID.genUuid();


    }

    private String buildItemNo() {
        return UUID.genUuid();


    }

    /**
     * 定时检查工单审核情况
     *
     * @param batchSize
     */

    public void checkAuditOrder(int batchSize) {
        Long lastId = 0L;
        while (true) {
            //TODO
            List<AuditOrder> auditOrders = auditOrderMapper.listByStatusOrderById(
                    ImmutableList.of(AuditOrderStatusEnum.WAIT_AUDIT.getCode(),
                            AuditOrderStatusEnum.PART_ACCEPT.getCode()),
                    lastId,
                    batchSize);
            if (CollectionUtils.isEmpty(auditOrders)) {
                break;
            }
           List<String> auditOrderNos = auditOrders.stream().map(AuditOrder::getNo)
                    .collect(Collectors.toList());
            Map<String,List<AuditItem>>  auditItemsMaps=
                    auditItemMapper.selectList(new LambdaQueryWrapper<AuditItem>()
                    .in(AuditItem::getOrderNo,auditOrderNos))
                    .stream()
                    .collect(Collectors.groupingBy(AuditItem::getOrderNo,Collectors.toList()));

            for (AuditOrder auditOrder : auditOrders) {
                String orderNo = auditOrder.getNo();
                List<AuditItem> auditItems = auditItemsMaps.getOrDefault(orderNo, Collections.emptyList());
                int count = 0;
                int acceptCount = 0;
                int rejectCount = 0;
                for (AuditItem auditItem : auditItems) {
                    if (AuditItemStatusEnum.ACCEPT.getCode() ==
                            auditItem.getStatus()) {
                        acceptCount++;
                    }
                    if (AuditItemStatusEnum.REJECT.getCode() ==
                            auditItem.getStatus()) {
                        rejectCount++;
                    }
                    count++;

                }
                if (rejectCount > 0) {
                    //拒绝
                    AuditOrder update = new AuditOrder();
                    update.setId(auditOrder.getId());
                    update.setStatus(AuditOrderStatusEnum.REJECT.getCode());
                    auditOrderMapper.updateById(update);
                    continue;
                }
                if (acceptCount > 0) {
                    if (acceptCount == count) {
                        AuditOrder update = new AuditOrder();
                        update.setId(auditOrder.getId());
                        update.setStatus(AuditOrderStatusEnum.ALL_ACCEPT.getCode());
                        auditOrderMapper.updateById(update);
                    } else if (!Objects.equals(auditOrder.getStatus(),
                            AuditOrderStatusEnum.PART_ACCEPT.getCode())) {
                        AuditOrder update = new AuditOrder();
                        update.setId(auditOrder.getId());
                        update.setStatus(AuditOrderStatusEnum.PART_ACCEPT.getCode());
                        auditOrderMapper.updateById(update);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(auditOrders)) {
                lastId = auditOrders.get(auditOrders.size() - 1).getId();
            }
        }


    }

    /**
     * 通过审核
     *
     * @param itemNo
     */

    public void accept(String itemNo,
                       String auditorName,
                       String remark
    ) {

        if(StringUtils.isNotEmpty(remark)){
            if(remark.length() > 100){
                throw new ServiceException(EXEC_ERROR.value(), "备注长度不得超过100");
            }
        }

        AuditItem auditItem = auditItemMapper.selectOne(new LambdaQueryWrapper<AuditItem>()
                .eq(AuditItem::getNo, itemNo)
        );
        if (auditItem == null) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "审核项不存在");
        }

        if(auditItem.getStatus() != AuditItemStatusEnum.WAIT_AUDIT.getCode()){
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "审核项状态应为待审核");
        }

        AuditItem update = new AuditItem();
        update.setId(auditItem.getId());
        update.setStatus(AuditItemStatusEnum.ACCEPT.getCode());
        update.setRemark(remark);
        update.setUpdateBy(auditorName);
        auditItemMapper.updateById(update);
    }

    /**
     * 通过审核
     *
     * @param itemNo
     */

    public void reject(String itemNo,
                       String auditorName,
                       String remark) {
        AuditItem auditItem = auditItemMapper.selectOne(new LambdaQueryWrapper<AuditItem>()
                .eq(AuditItem::getNo, itemNo)
        );
        if (auditItem == null) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "审核项不存在");
        }

        if(auditItem.getStatus() != AuditItemStatusEnum.WAIT_AUDIT.getCode()){
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "审核项状态应为待审核");
        }

        AuditItem update = new AuditItem();
        update.setId(auditItem.getId());
        update.setStatus(AuditItemStatusEnum.REJECT.getCode());
        update.setRemark(remark);
        update.setUpdateBy(auditorName);
        auditItemMapper.updateById(update);
        //TODO 同步更新auditOrder
    }

    public AuditOrder save(AuditOrderVO auditOrderVO) {

        List<AuditOrderVO.ItemVO> applyItems = auditOrderVO.getApplyItems();
        if (CollectionUtils.isEmpty(applyItems)) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "applyItems is empty");
        }

        AuditOrder auditOrder = new AuditOrder();

        auditOrder.setNo(buildNo());
        auditOrder.setStatus(AuditOrderStatusEnum.WAIT_AUDIT.getCode());

        auditOrder.setType(auditOrderVO.getType());
        auditOrder.setRemark(auditOrderVO.getRemark());
        auditOrderVO.getApplyItems().stream()
                .forEach(itemVO -> {
                    AuditItem auditItem = new AuditItem();
                    auditItem.setNo(buildItemNo());
                    auditItem.setOrderNo(auditOrder.getNo());
                    auditItem.setLayer(itemVO.getLayer());
                    auditItem.setAuditorUuid(itemVO.getAuditorUuid());
                    auditItem.setStatus(AuditItemStatusEnum.WAIT_AUDIT.getCode());
                    auditItemMapper.insert(auditItem);
                });

        auditOrderMapper.insert(auditOrder);
        return auditOrder;

    }


    public void cancel(String no) {
        AuditOrder auditOrder = auditOrderMapper.selectOne(
                new LambdaQueryWrapper<AuditOrder>()
                        .eq(AuditOrder::getNo, no));

        if (auditOrder == null) {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), "auditOrder is not exists");
        }
        AuditOrder updateAuditOrder = new AuditOrder();
        updateAuditOrder.setId(auditOrder.getId());
        updateAuditOrder.setStatus(AuditOrderStatusEnum.CANCEL.getCode());
        auditOrderMapper.updateById(updateAuditOrder);
    }


    public Map<String, AuditOrder> getMapByOrderNos(List<String> auditOrderNos) {
        return auditOrderMapper.selectList(new LambdaQueryWrapper<AuditOrder>()
                        .in(AuditOrder::getNo, auditOrderNos)
                ).stream()
                .collect(Collectors.toMap(AuditOrder::getNo, Function.identity(), (o, n) -> n));
    }

    public Map<String, List<AuditItem>> getItemListMapByOrderNos(List<String> auditOrderNos) {
        return auditItemMapper.selectList(new LambdaQueryWrapper<AuditItem>()
                        .in(AuditItem::getOrderNo, auditOrderNos))
                .stream()
                .collect(Collectors.groupingBy(AuditItem::getOrderNo,Collectors.toList()));

    }
}
