package cn.taqu.gonghui.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@RequiredArgsConstructor
public class PageWrapper<T> {

     private final Page<T> page;

     public List<T> getList(){
         return page.getRecords();
     }
     public long getTotal(){
         return page.getTotal();
     }

     public long getPage(){
         return page.getCurrent();
     }

     public long getPageSize(){
         return page.getSize();
     }







}
