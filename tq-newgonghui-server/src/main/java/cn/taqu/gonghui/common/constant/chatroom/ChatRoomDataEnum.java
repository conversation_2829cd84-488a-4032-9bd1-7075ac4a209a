package cn.taqu.gonghui.common.constant.chatroom;

/**
 * @Description 艺人数据导出的字段对应关系
 * <AUTHOR>
 * @Date 2022/5/24 7:33 下午
 **/
public enum ChatRoomDataEnum {
    dt("统计日期"),
    account_uuid("艺人uuid"),
    nickname("昵称"),
    avatar("头像"),
    card_id("他趣Id"),
    teamName("所属团队"),
    orgName("所属机构"),
    receive_amt_std("历史总收益"),
    receive_amt("艺人收益"),
    consume_amt("房间消费金额"),
    chat_open_cnt("开房次数"),
    chat_open_duration("开房时长"),
    meeting_cnt("房间上麦人数"),
    meeting_duration("房间上麦用户上麦时长"),
    consume_transform_ratio("房间新用户转化率"),
    meeting_open_duration("房间上麦用户上麦时长"),
    chat_receive_game_amt("艺人开房时房间游戏礼物收益"),
    chat_receive_game_amt_ratio("艺人开房时房间游戏礼物收益占比"),
    consume_cnt("房间消费用户数"),
    meeting_open_cnt("房间开麦用户数"),
    chat_receive_cnt("艺人开房时房间收益人数"),
    receive_self_consortia_amt("艺人个人在所属公会收益"),
    receive_other_consortia_amt("艺人个人在其他公会收益"),
    receive_self_consortia_game_amt("艺人个人在所属公会游戏收益"),
    receive_other_consortia_game_amt("艺人个人在其他公会游戏收益"),
    ;

    public static String getName(String code) {
        for (ChatRoomDataEnum value : values()) {
            if (value.name().equals(code)) {
                return value.getName();
            }
        }
        return code;
    }

    ChatRoomDataEnum(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
