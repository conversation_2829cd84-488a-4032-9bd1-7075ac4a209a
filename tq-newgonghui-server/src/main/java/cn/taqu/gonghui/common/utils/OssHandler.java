package cn.taqu.gonghui.common.utils;

import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.comm.Protocol;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/9/8 13:44
 */
@Component
public class OssHandler {

    private static OssMeta ossMeta;

    private Integer expireTime = 3600 * 24 * 180;

    @EtcdValue("storage.oss.default")
    public static void config(String config) {
        ossMeta = JsonUtils.stringToObject2(config, new TypeReference<OssMeta>() {});
    }

    /**
     * 将JSON数据转换为CSV文件并上传到OSS，返回下载链接
     *
     * @param jsonData JSON数据
     * @return OSS下载链接
     */
    public String json2DownloadUrl(String jsonData) {
        try {
            // 将JSON数据转换为CSV文件
            File csvFile = convertJsonToCsv(jsonData);

            // 生成随机文件名
            String fileName = UUID.randomUUID().toString().replace("-", "");

            // 上传CSV文件到OSS
            ObjectMetadata metadata = new ObjectMetadata();
            // 设置CSV文件的内容类型
            metadata.setContentType("text/csv");
            OSS ossClient = new OSSClientBuilder()
                    .build(ossMeta.getEndpoint(), ossMeta.getOssKey(), ossMeta.getOssSecret());
            ossClient.putObject(new PutObjectRequest(ossMeta.getBucket(), fileName, csvFile, metadata));

            // 获取文件访问地址
            String downloadUrl = ossClient.generatePresignedUrl(ossMeta.getBucket(), fileName, new Date(System.currentTimeMillis() + expireTime * 1000L)).toString();
            downloadUrl = downloadUrl.replace("http://", "https://");

            // 关闭OSS客户端
            ossClient.shutdown();

            // 删除临时生成的CSV文件
            if (csvFile.exists()) {
                csvFile.delete();
            }

            return downloadUrl;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    /**
     * 使用文件及文件的ContentType上传文件到OSS，获取下载地址
     *
     * @param file
     * @return OSS下载链接
     */
    public String getDownloadUrlWithFileAndContentType(File file, String metaContentType) {
        try {
            // 将JSON数据转换为CSV文件
//            File file = convertJsonToCsv(jsonData);
            String fileName = file.getName();
            // 上传xlsx文件到OSS
            ObjectMetadata metadata = new ObjectMetadata();
            // 设置xlsx文件的内容类型
//            metadata.setContentType("application/vnd.ms-excel");
            metadata.setContentType(metaContentType);
            OSS ossClient = new OSSClientBuilder()
                    .build(ossMeta.getEndpoint(), ossMeta.getOssKey(), ossMeta.getOssSecret());
            ossClient.putObject(new PutObjectRequest(ossMeta.getBucket(), fileName, file, metadata));

            // 获取文件访问地址
            String downloadUrl = ossClient.generatePresignedUrl(ossMeta.getBucket(), fileName, new Date(System.currentTimeMillis() + expireTime * 1000L)).toString();
            downloadUrl = downloadUrl.replace("http://", "https://");

            // 关闭OSS客户端
            ossClient.shutdown();
            // 删除临时生成的CSV文件
            if (file.exists()) {
                file.delete();
            }
            return downloadUrl;
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    /**
     * 将JSON数据转换为CSV文件并返回文件流
     *
     * @param jsonData JSON数据
     * @return CSV文件流
     */
    public static File convertJsonToCsv(String jsonData) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonData);

            File csvFile = File.createTempFile(UUID.randomUUID().toString(), ".csv");
            FileWriter fileWriter = new FileWriter(csvFile);
            CSVPrinter csvPrinter = new CSVPrinter(fileWriter, CSVFormat.DEFAULT);

            // 获取CSV标题行
            List<String> headers = getHeadersFromJson(jsonNode);
            csvPrinter.printRecord(headers);

            // 获取CSV数据行
            List<List<String>> dataRows = getDataRowsFromJson(jsonNode);
            for (List<String> dataRow : dataRows) {
                csvPrinter.printRecord(dataRow);
            }

            csvPrinter.close();
            fileWriter.close();

            return csvFile;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static List<String> getHeadersFromJson(JsonNode jsonNode) {
        List<String> headers = new ArrayList<>();
        if (jsonNode.isArray() && jsonNode.size() > 0) {
            JsonNode firstEntry = jsonNode.get(0);
            firstEntry.fieldNames().forEachRemaining(headers::add);
        }
        return headers;
    }

    private static List<List<String>> getDataRowsFromJson(JsonNode jsonNode) {
        List<List<String>> dataRows = new ArrayList<>();
        if (jsonNode.isArray()) {
            for (JsonNode entry : jsonNode) {
                List<String> dataRow = new ArrayList<>();
                entry.fieldNames().forEachRemaining(fieldName -> {
                    dataRow.add(entry.get(fieldName).asText());
                });
                dataRows.add(dataRow);
            }
        }
        return dataRows;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OssMeta {
        /**
         * 桶 （bucket）
         */
        private String bucket;
        /**
         * 端点
         */
        private String endpoint;
        /**
         * 密钥
         */
        private String ossSecret;
        /**
         * 公钥
         */
        private String ossKey;
    }
}
