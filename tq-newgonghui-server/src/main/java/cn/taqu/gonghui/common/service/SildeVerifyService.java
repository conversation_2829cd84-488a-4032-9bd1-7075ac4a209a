package cn.taqu.gonghui.common.service;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.common.utils.IpUtils;
import cn.taqu.gonghui.common.utils.RedisUtil;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigRequest;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2021/6/23
 */
@Slf4j
@Service
public class SildeVerifyService {

    @Value("${aliyun.regionId}")
    private String regionId;
    @Value("${aliyun.accessKeyId}")
    private String accessKeyId;
    @Value("${aliyun.accessKeySecret}")
    private String accessKeySecret;
    @Value("${aliyun.product}")
    private String product;
    @Value("${aliyun.domain}")
    private String domain;


    @Autowired
    private RedisUtil redisUtil;

    private static IClientProfile profile;

    private IClientProfile initProfile() throws ClientException {
        // Create a new IClientProfile instance
        //IClientProfile可以复用，建议将其设置成应用程序全局唯一。
        if (null == profile) {
            synchronized (SildeVerifyService.class) {
                if (null == profile) {
                    profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
                }
            }
        }
        return profile;
    }

    //验签
    public boolean isValid(HttpServletRequest httpRequest, String sign, String sessionId , String token, String scene) {
         String code = "1";
         boolean valid = false;
        try {
            IAcsClient client = new DefaultAcsClient(initProfile());
            DefaultProfile.addEndpoint(regionId,regionId, product, domain);

            AuthenticateSigRequest request = new AuthenticateSigRequest();
            // 会话ID。必填参数，从前端获取，不可更改。
            request.setSessionId(sessionId);
            // 签名串。必填参数，从前端获取，不可更改。
            request.setSig(sign);
            // 请求唯一标识。必填参数，从前端获取，不可更改。
            request.setToken(token);
            // 场景标识。必填参数，从前端获取，不可更改。
            request.setScene(scene);
            // 应用类型标识。必填参数，后端填写。
            request.setAppKey("FFFF000000000169E182");
            // 客户端IP。必填参数，后端填写。
            request.setRemoteIp(IpUtils.getIpAddr(httpRequest));
            log.info("阿里云滑块验签-sign:{},sessionId:{},token:{},scene:{}",sign,sessionId,token,scene);
            //response的code枚举：100验签通过，900验签失败
            AuthenticateSigResponse response = client.getAcsResponse(request);
            log.info("阿里云滑块验签-response-code:{},msg={},detail:{},risklevel:{}",  response.getCode(),response.getMsg(),response.getDetail(),response.getRiskLevel());

            if(response!=null && response.getCode().equals(100))
            {
                code="0";
                valid = true;
            }else{
                valid= false;
            }
        } catch (ClientException ce) {
            log.error("初始化IClientProfile异常",ce);
        } catch (Exception e) {
            log.error("阿里云滑块验签异常",e);
        }
        Boolean isSend = redisUtil.set(sessionId, code, 90L, TimeUnit.SECONDS);
        log.info("设置sessionId:{},设置结果：{}",sessionId,isSend);
        if (!isSend) {
            throw new ServiceException("slide_code_to_many","滑块频率过高,请稍等后再尝试");
        }
        return valid;
    }


}
