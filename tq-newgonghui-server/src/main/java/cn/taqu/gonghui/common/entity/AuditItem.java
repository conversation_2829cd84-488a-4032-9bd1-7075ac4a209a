package cn.taqu.gonghui.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("audit_item")
public class AuditItem extends BaseEntity {
    private String no;
    private String orderNo;
    private Integer layer;
    private String auditorUuid;
    private Integer status;


}
