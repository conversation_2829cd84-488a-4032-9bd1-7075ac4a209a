package cn.taqu.gonghui.common.configuration;

import cn.taqu.gonghui.common.utils.SecurityUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.system.entity.SysUser;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;

@Configuration
@Slf4j
public class GongguiMybatisPlusConfig {

    @Bean
    public MetaObjectHandler myMetaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
                this.strictUpdateFill(metaObject, "modifyTime", Date.class, new Date());

                try {
                    Optional<String> userOption = Optional.ofNullable(SecurityUtils.getLoginUser())
                            .map(LoginUser::getUser)
                            .map(SysUser::getAccountUuid);
                    String accountUuid = userOption.get();
                    if (userOption.isPresent()) {
                        this.strictInsertFill(metaObject, "createBy", String.class, accountUuid);
                        this.strictUpdateFill(metaObject, "updateBy", String.class, accountUuid);
                    } else {
                        this.strictInsertFill(metaObject, "createBy", String.class, accountUuid);
                        this.strictUpdateFill(metaObject, "updateBy", String.class, accountUuid);
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    this.strictUpdateFill(metaObject, "updateBy", String.class, "system");
                    this.strictInsertFill(metaObject, "createBy", String.class, "system");
                }


            }

            @Override
            public void updateFill(MetaObject metaObject) {
                this.strictUpdateFill(metaObject, "modifyTime", Date.class, new Date());
                try {
                    Optional<String> userOption = Optional.ofNullable(SecurityUtils.getLoginUser())
                            .map(LoginUser::getUser)
                            .map(SysUser::getAccountUuid);
                    String accountUuid = userOption.get();
                    if (userOption.isPresent()) {
                        this.strictUpdateFill(metaObject, "updateBy", String.class, accountUuid);
                    } else {
                        this.strictUpdateFill(metaObject, "updateBy", String.class, accountUuid);
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    this.strictUpdateFill(metaObject, "updateBy", String.class, "system");
                }
            }
        };
    }

}
