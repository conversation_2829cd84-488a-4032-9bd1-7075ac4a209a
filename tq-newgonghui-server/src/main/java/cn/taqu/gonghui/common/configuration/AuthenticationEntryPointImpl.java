package cn.taqu.gonghui.common.configuration;

import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.constant.HttpStatus;
import cn.taqu.gonghui.common.domain.AjaxResult;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;

/**
 * 认证失败处理类 返回未授权
 */
@Component
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint, Serializable
{
    private static final long serialVersionUID = -8970718410437077606L;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException e)
            throws IOException
    {
        int code = HttpStatus.UNAUTHORIZED;
//        if(StringUtils.equals(CodeStatus.LOGIN_OUT_EXPIRED.value(),e.getMessage())){
//            ServletUtils.renderString(response, JSON.toJSONString(JsonResult.failedCode(CodeStatus.LOGIN_OUT_EXPIRED)));
//        }else{
//            String msg = StringUtils.format("请求访问：{}，认证失败，无法访问系统资源", request.getRequestURI());
//            ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(code, msg)));
//        }
        String method = request.getParameter("method");
        if("authLogin".equals(method)){
            ServletUtils.renderString(response, JSON.toJSONString(JsonResult.success(0)));
        }else{
            ServletUtils.renderString(response, JSON.toJSONString(JsonResult.failedCode(CodeStatus.LOGIN_OUT_EXPIRED)));

        }

    }
}
