package cn.taqu.gonghui.common.constant;

/**
 * 变更类型
 * PS：只能在尾部追加枚举类型
 */
public enum HostOperateTypeEnum {
    // 占位
    EMPTY,
    // 团队迁移
    TEAM_MOVE,
    // 单个艺人设定工会
    HOST_MOVE,
    // 关闭工会
    ORG_CLOSE,
    // 解散团队
    TEAM_CLOSE,
    // 机构业务变更
    ORG_BUSINESS_CHANGE,
    // 邀请加入聊天室
    INVITE_CHAT_ROOM_TEAM,
    // 更新直播团队
    UPDATE_LIVE_TEAM,
    // 新增到直播团队
    ADD_TO_LIVE_TEAM,
    // 批量变更
    BATCH_UPDATE_LIVE_TEAM,
    // 用户端修改团队
    USER_CHANGE_TEAM,
    // 关闭直播业务
    CLOSE_LIVE_BUSINESS,
    // 申请退会中转公会
    QUIT_GUILD_MOVE,
    // 批量中转公会
    BATCH_MOVE,
    ;
}
