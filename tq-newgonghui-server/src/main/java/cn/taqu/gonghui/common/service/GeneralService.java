package cn.taqu.gonghui.common.service;

import cn.hutool.core.net.URLEncoder;
import cn.hutool.core.util.URLUtil;
import cn.taqu.core.common.client.MqClientFactory;
import cn.taqu.core.common.client.MqResponse;
import cn.taqu.core.common.client.SoaServer;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.chatroom.service.ChatRoomService;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.chatroom.ExportDataType;
import cn.taqu.gonghui.common.vo.IdNameVO;
import cn.taqu.gonghui.common.vo.SecretaryLinkVO;
import cn.taqu.gonghui.common.vo.SecretaryMsgVO;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/25 13:44
 */
@Slf4j
@Service
public class GeneralService {

    @Resource
    private OrganizationMapper organizationMapper;

    @Autowired
    @Lazy
    private ChatRoomService chatRoomService;

    /**
     * 小秘书队列
     */
    private final static String SYSTEM_NOTICE_QUEUE = "system_notice";

    /**
     * 小秘书channel
     */
    private final static String SYSTEM_MSG_CHANNEL = "system_msg";

    /**
     * 内容类型
     */
    private final static String TXT_TYPE = "text";

    private final static String LINK_TYPE = "system_link_text";

    /**
     * 1为屏蔽im
     */
    private final static String SHIELD_IM = "0";

    /**
     * 获取通用选项list
     * @param conditionStr
     * @return
     */
    public Map<String, List<IdNameVO>> getSelectList(String conditionStr) {
        // 分割条件字符串
        String[] arr = conditionStr.split(",");
        List<String> conditionList = Arrays.asList(arr);
        Map<String, List<IdNameVO>> map = new HashMap<>(4);
        for (String str : conditionList) {
            String[] strArr = str.split("_");
            // 字典表数据有特殊标识 dict
            if ("dict".equals(strArr[0])) {
                Integer dictId = Integer.valueOf(strArr[1]);
                List<IdNameVO> list = getDictMapList(dictId);
                // 取第3个为key值
                map.put(strArr[2],list);
            } else {
                // 自定义获取select list
                customSelectList(str, map);
            }
        }

        return map;
    }

    /**
     * 自定义获取list
     * @param str
     * @param map
     */
    private void customSelectList(String str, Map<String, List<IdNameVO>> map) {
        switch (str) {
            // 一级错误类型列表
            case "guildList":
                List<Organization> gList = organizationMapper.findGuildInfoUuidAndNameList();
                List<IdNameVO> list = gList.stream().map(item -> {
                    IdNameVO idName = new IdNameVO();
                    idName.setId(item.getOrgId());
                    idName.setName(item.getOrgName());
                    return idName;
                }).collect(Collectors.toList());
                map.put(str,list);
                break;
            case "chatRoomOrgList":
                List<IdNameVO> chatRoomOrgList = chatRoomService.getOrgListByType(TeamTypeEnum.TALK_TEAM.getValue());
                map.put(str,chatRoomOrgList);
                break;
            default:break;
        }
    }

    /**
     * 获取字典枚举值
     * @param dictId
     * @return
     * <AUTHOR>
     */
    public List<IdNameVO> getDictMapList(Integer dictId) {
        // todo 此处预留 需要字典枚举值时维护
        return null;
    }

    /**
     * 发送小秘书
     * @param hostUuid
     * @param content
     * @return
     */
    public Boolean sendSecretary(String hostUuid, String content) {
        SecretaryMsgVO msgVO = new SecretaryMsgVO();
        // 发送到消息队列
        msgVO.setTo_user_id(Collections.singletonList(hostUuid));
        SecretaryMsgVO.ContentData contentData = new SecretaryMsgVO.ContentData();
        contentData.setContent(content);
        msgVO.setContent(contentData);
        msgVO.setMsg_channel(SYSTEM_MSG_CHANNEL);
        msgVO.setType(TXT_TYPE);
        msgVO.setPush_content(content);
        msgVO.setPush_title("退会申请提示");
        msgVO.setShield_im(SHIELD_IM);
        try {
            log.info("[sendSecretary],req:{}",JsonUtils.objectToString(msgVO));
            MqResponse response = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(SYSTEM_NOTICE_QUEUE, msgVO, null);
            log.info("[ssssss],res:{}", JsonUtils.objectToString(response));
            return response.success();
        } catch (Exception e) {
            log.error("发送小秘书失败[sendSecretary],msg:{},req:{}", e.getMessage(), msgVO);
            return false;
        }
    }

    /**
     * 发送小秘书 带链接的
     * @param hostUuid
     * @param content
     * @return
     */
    public Boolean sendLinkSecretary(String hostUuid, String content, String replaceContent, String replaceStr, String url) {
        SecretaryLinkVO linkVO = new SecretaryLinkVO();
        // 发送到消息队列
        linkVO.setTo_user_id(Collections.singletonList(hostUuid));
        SecretaryLinkVO.ContentLink contentLink = new SecretaryLinkVO.ContentLink();
        SecretaryLinkVO.ContentLink.ContentChild.ChildItem childItem = new SecretaryLinkVO.ContentLink.ContentChild.ChildItem();
        childItem.setC("#0000FF");
        childItem.setR("m=web&a=url&ul=" + URLEncoder.ALL.encode(url, StandardCharsets.UTF_8));
        childItem.setW(replaceStr);
        SecretaryLinkVO.ContentLink.ContentChild contentChild = new SecretaryLinkVO.ContentLink.ContentChild();
        contentChild.setContent(replaceContent);
        contentChild.setDescribe(content);
        contentChild.setIs_local_push(SHIELD_IM);
        contentChild.setContent_replace(Collections.singletonList(childItem));
        contentLink.setContent(contentChild);
        linkVO.setContent(Collections.singletonList(contentLink));
        linkVO.setMsg_channel(SYSTEM_MSG_CHANNEL);
        linkVO.setType(LINK_TYPE);
        try {
            log.info("[sendLinkSecretary],req:{}",JsonUtils.objectToString(linkVO));
            MqResponse response = MqClientFactory.createTqMq(SoaServer.PHP.TQMQ).push(SYSTEM_NOTICE_QUEUE, linkVO, null);
            log.info("[ssssss],res:{}", JsonUtils.objectToString(response));
            return response.success();
        } catch (Exception e) {
            log.error("发送小秘书失败[sendSecretary],msg:{},req:{}", e.getMessage(), linkVO);
            return false;
        }
    }

}
