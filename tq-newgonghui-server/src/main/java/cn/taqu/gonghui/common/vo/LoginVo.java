package cn.taqu.gonghui.common.vo;

import cn.taqu.gonghui.common.domain.CommonSelect;
import lombok.Data;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>  2019/9/22 10:22 AM
 */
@Data
public class LoginVo {
    /**机构id*/
    private Long orgId;
    /**
     * 身份认证令牌
     */

    private String token;


    /**
     * 机构申请状态  0无工会 1待审核 2审核通过 3审核拒绝
     */
    private Integer applyStatus;

    private Integer formStatus;

    private List<CommonSelect> teamType;

    private List<Map<String, Object>> signMap;

    private boolean isManager;

    private Integer liveSettlementeType;

    private Integer settlementeType;// '结算类型（  1 周结  2月结）',

    private Integer userType; //1 管理员 2 负责人 3 经纪人
    /**
     * 登录账号
     */
    private String userName;
    private Integer creditGrade; //信用得分



}
