package cn.taqu.gonghui.common.constant;

import cn.taqu.gonghui.common.domain.CommonSelect;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公会类型
 */
public enum TeamTypeEnum {

    LIVE_TEAM(1,"直播公会"), //直播
    CALL_TEAM(2,"趣聊公会"), // 趣聊
    TALK_TEAM(3,"聊天室公会"); //聊天室



    TeamTypeEnum(Integer value,String msg) {
        this.value = value;
        this.msg = msg;
    }

    private int value;
    private String msg;

    public int getValue() {
        return this.value;
    }

    public String getMsg() {
        return msg;
    }

    public static TeamTypeEnum getByValue(Integer teamType) {
        if (teamType == null) {
            return null;
        }
        for (TeamTypeEnum anEnum : values()) {
            if (anEnum.getValue() == teamType) {
                return anEnum;
            }
        }
        return null;
    }

    /**
     *获取全部数据
     * @return
     */
    public static List<CommonSelect> getByValues(List<Integer> values1){
        if(CollectionUtils.isEmpty(values1)){
            return null;
        }
        List<CommonSelect> list = Lists.newArrayList();

        for (TeamTypeEnum e : TeamTypeEnum.values()) {
            CommonSelect select = new CommonSelect();
            select.setLabel(e.getMsg());
            select.setValue(Long.valueOf(e.getValue()));
            list.add(select);
        }
        return list.stream().filter(item->
             values1.contains(item.getValue().intValue())
        ).collect(Collectors.toList());
    }


}
