package cn.taqu.gonghui.common.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum InviteStatusEnum {

    /**
     * 初始化
     */
    WAIT_CONFIRM(0, "等待确认"),
    AGREE(1, "同意加入"),
    REJECT(2, "拒绝加入"),
    EXPIRE(3, "已过期"),
    ;

    private Integer code;
    private String name;

    InviteStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code) {
        for (InviteStatusEnum location : InviteStatusEnum.values()) {
            if (Objects.equals(location.getCode(), code)) {
                return location.getName();
            }
        }
        return "";
    }

}
