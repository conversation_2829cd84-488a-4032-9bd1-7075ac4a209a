package cn.taqu.gonghui.common.utils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/5/8
 */


public class PageDataResult<T> implements Serializable {

    private Integer page;

    private Integer pageSize;

    private Long total;
    private List<T> list;

    public PageDataResult() {
        this(0, 20);
    }

    public PageDataResult(int page, int pageSize) {
        this.page = Math.max(page, 0);
        this.pageSize = pageSize <= 0 ? 20 : pageSize;
    }

    public PageDataResult(int page, int pageSize, long total) {
        this(page, pageSize);
        this.total = total;
//        this.totalPage = PageUtil.totalPage(total, pageSize);
    }

    public PageDataResult(int page, int pageSize, long total, List<T> list) {
        this(page, pageSize, total);
        this.total = total;
        this.list = list;
    }

    public static <T> PageDataResult<T> build() {
        return new PageDataResult<>(0, 0);
    }

    public Integer getPage(){
        return page;
    }

    public void setPage(Integer page){
        this.page = page;
    }

    public Integer getPageSize(){
        return pageSize;
    }

    public void setPageSize(Integer pageSize){
        this.pageSize = pageSize;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}

