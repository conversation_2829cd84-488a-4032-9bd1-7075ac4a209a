package cn.taqu.gonghui.common.constant;

import cn.taqu.core.common.constant.ICodeStatus;
import cn.taqu.core.common.constant.SysCodeStatus;

/**
 * <AUTHOR>
 * @Date 2021/4/27
 */
public enum CodeStatus implements ICodeStatus {
    APPLY_ORG_PARAM_ERROR("apply_org_param_error", "机构申请参数错误."),
    ORG_NOT_FOUNT_ERROR("org_not_found_error", "机构信息不存在,请联系管理员"),
    EMPLOYEE_NOT_FOUNT_ERROR("employee_not_found_error", "机构职员不存在,请联系管理员"),


    ORG_ALREADY_EXIST_ERROR("org_already_exist_error", "机构已经存在"),
    EMPLOYEE_ALREADY_EXIST_ERROR("employee_already_exist_error", "职员已经存在"),
    AGENT_MANAGE_PARAM_ERROR("agent_manage_param_error", "经纪人添加参数错误"),
    EMPLOYEE_PARAM_ERROR("employee_param_error", "职员添加参数错误"),
    ORG_APPLY_STATUS_ERROR("org_apply_status_error", "机构审核状态错误,必须为审核失败才能重新发起申请"),
    ROLE_ERROR("role_error", "角色错误"),

    //经纪人相关
    AGENT_NOT_FOUNT_ERROR("agent_not_found_error", "经纪人信息不存在,请联系管理员"),

    HTTP_UNAUTHORIZED("401", "认证失败"),
    HTTP_SUCCESS("200","请求成功"),
    DATA_NOT_FOUND_ERROR("data_not_found_error", "数据查询失败，该数据或已删除"),

    USER_NOT_EXSISTS("user_not_exsists","用户不存在"),
    USER_PASSWORD_NOT_MATCH("user_password_not_match","账号密码不匹配"),
    LOGIN_TYPE_ERROR("login_type_error", "登录类型错误"),
    LOGIN_OUT_EXPIRED("login_out_expired", "当前登录认证失败，请重新登录"),
    SOA_FAIL("soa_fail", "系统出错，请骚后再试"),
    DEPT_IS_FORBIDDEN("dept_is_forbidden", "部门停用"),
    CHECK_TEAM_PARAMS_FAILED("check_team_params_failed", "团队信息校验失败"),
    GET_HOST_INFO_FAILED("get_host_info_failed", "获取主播信息失败"),

    USER_NOT_EXSISTS_IN_TAQU("user_not_exsists_in_taqu", "关联用户不存在"),

    USER_IS_DISABLED("user_is_disabled", "当前用户已被禁用"),
    VERIFY_CODE_ERROR("verify_code_error", "验证码错误"),

    OPERATE_TEAM_REFUSE("operate_team_refuse", "拒绝执行此操作"),

    ACCESS_DENIED_EXCEPTION("access_denied_exception", "无权限访问"),

    LOGIC_ERROR("logic_error", "逻辑错误"),

    //主播相关
    PARAM_ERROR("host_param_error", "主播申请参数错误"),
    OPERATION_FAIL_ERROR("operation_fail_error", "操作失败"),

    PARAM_NOT_EMPTY("param_not_empty", "参数不能为空"),
    REQUEST_REPEAT("request_repeat", "请求过于频繁"),

    EXEC_ERROR("exec_error", "执行异常"),

    ;

    private final String value;// code
    private final String reasonPhrase;// message description
    private CodeStatus(String value, String reasonPhrase) {
        this.value = value;
        this.reasonPhrase = reasonPhrase;
    }

    /**
     * Return the integer value of this status code.
     */
    @Override
    public String value() {
        return this.value;
    }

    /**
     * Return the reason phrase of this status code.
     */
    @Override
    public String getReasonPhrase() {
        return reasonPhrase;
    }

    @Override
    public String toString() {
        return value;
    }

    public static ICodeStatus getCodeStatus(String statusCode) {
        for (CodeStatus status : values()) {
            if (status.value.equals(statusCode)) {
                return status;
            }
        }
        for (SysCodeStatus status : SysCodeStatus.values()) {
            if (status.value().equals(statusCode)) {
                return status;
            }
        }
        return SysCodeStatus.UNDEFINED;
    }
}
