package cn.taqu.gonghui.common.constant;

/**
 * <AUTHOR>
 * @Date 2021/5/19
 */
public enum InvitationRecordStatusEnum {

    UNHANDLER(1, "未处理"),REJECT(2, "拒绝"), AGREED(3, "同意"),EXPIRED(3, "已过期");

    private Integer value;
    private String statusMsg;


    public Integer getValue() {
        return value;
    }

    public String getStatusMsg() {
        return statusMsg;
    }

    InvitationRecordStatusEnum(Integer value, String statusMsg) {
        this.value = value;
        this.statusMsg = statusMsg;
    }
}
