package cn.taqu.gonghui.common.mapper;

import cn.taqu.gonghui.common.entity.ApprovalFlowNode;

import java.util.List;

import cn.taqu.gonghui.common.vo.ApprovalCustomVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface ApprovalFlowNodeMapper extends BaseMapper<ApprovalFlowNode> {

    /**
     * 批量写入
     * @param nodeList
     */
    void insertBatch(List<ApprovalFlowNode> nodeList);

    /**
     * 查询当前审核记录
     * @param flowId
     * @return
     */
    ApprovalFlowNode selectCurrentNode(Integer flowId);

    /**
     * 根据条件查询
     * @param approvalFlowNode
     * @return
     */
    ApprovalFlowNode selectByCondition(ApprovalFlowNode approvalFlowNode);

    /**
     * 根据条件查询 1个
     * @param approvalFlowNode
     * @return
     */
    ApprovalFlowNode selectOneByCondition(ApprovalFlowNode approvalFlowNode);

    /**
     * 更新记录
     * @param approvalFlowNode
     */
    void updateByRecord(ApprovalFlowNode approvalFlowNode);

    /**
     * 根据审批表id查询
     * @param flowId
     * @return
     */
    List<ApprovalFlowNode> selectByFlowId(Long flowId);

    /**
     * 查询超过创建时间
     * @param approvalCustomVO
     * @return
     */
    List<ApprovalFlowNode> selectByCustom(ApprovalCustomVO approvalCustomVO);

    /**
     * 批量更新
     * @param list
     */
    void updateBatch(List<ApprovalFlowNode> list);

    void updateClearTxtByRange(@Param("curStartId") Long curStartId, @Param("curEndId") Long curEndId);
}