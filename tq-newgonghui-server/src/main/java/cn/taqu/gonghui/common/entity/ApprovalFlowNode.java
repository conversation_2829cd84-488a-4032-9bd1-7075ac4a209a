package cn.taqu.gonghui.common.entity;

import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.common.EncryptTypeHandler;
import cn.taqu.gonghui.system.common.SetNullTypeHandler;
import cn.taqu.gonghui.system.common.Sm3EncryptTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "approval_flow_node", autoResultMap = true)
public class ApprovalFlowNode {
    private Long id;

    private Long flowId;

    private Integer nodeIndex;

    private Integer nextIndex;

    private String nodeName;

    private Integer nodeRole;

    private String nodeUser;

    @TableField(typeHandler = SetNullTypeHandler.class)
    private String mobile;

    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = Sm3EncryptTypeHandler.class)
    private String mobileDigest;

    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = EncryptTypeHandler.class)
    private String mobileCipher;

    private Integer nodeStatus;

    private String remark;

    private Date reviewTime;

    private Date createTime;

    private String createUser;

    private Date modifyTime;

    private String modifyUser;

    private Integer isDel;


    public String getMobile(){
        if(EncryptSwitchConfig.readFrom.equals(2)){
            return this.mobileCipher;
        }
        if(StringUtils.isBlank(this.mobile) && StringUtils.isNotBlank(this.mobileCipher)){
            return this.mobileCipher;
        }
        return this.mobile;
    }

    public String getMobileDigest() {
        return getMobile();
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
        this.mobileCipher = mobile;
        this.mobileDigest = mobile;
    }
}