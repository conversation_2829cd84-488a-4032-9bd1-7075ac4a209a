package cn.taqu.gonghui.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HostVo {
    private Long id;
    /**
     * 经纪人uuid
     */
    private String businessmanUuid;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 主播uuid
     */
    private String hostUuid;
    /**
     * 经纪人名称
     */
    private String agentName;
    /**
     * 工会名称
     */
    private String consortiaName;
    /**
     * 申请时间
     */
    private Long createTime;
    /**
     * 主播评级
     */
    private String applyLevel;
    /**
     * 累计收益
     */
    private Long receiveGiftValue;


    /**
     * 最后开播结束时间
     */
    private Long lastOnlineTime;
    /**
     * 直播状态 0-删除 , 1-正常，2-停止，3-禁播，4-暂离
     */
    private Integer liveStatus;
    /**
     * 主播状态 1-流失主播 2-沉寂主播 4-收入主播 5-新入驻
     */
    private Integer hostStatus;
    /**
     * 直播号
     */
    private String liveNo;

    private List<DataVo> list;

    @Data
    public static class DataVo {

        /**
         * 直播号
         */
        private String live_no;
        /**
         * id
         */
        private String id;
        /**
         * 主播uuid
         */
        private String host_uuid;
        /**
         * 直播状态
         */
        private String live_status;
        /**
         * 评级
         */
        private String apply_level;
        private String create_time;
        private String max_num;
        private String receive_gift_value;
        private String consortia_id;
        private String businessman_uuid;
        private String last_online_time;
        private String live_time;
        private String tag_id;
        private String is_dance_priv;
        private String is_sing_priv;
        private String can_open_pc;
        private String personal;
        private String nickname;
        private String now_num;
        private Integer host_status;
        private Integer forbidden_dance_status;
        private Integer follow_num;
        private String add_consortia_time;
        private Integer is_set_split;
        /**
         * 所属团队名称
         */
        private String teamName;
        /**
         * 用户头像
         */
        private String avatar;
        /**
         * 机构名称
         */
        private String orgName;
        /**
         * 经纪人名称
         */
        private String AgentName;

        /**
         * 当前分润比例
         */
        private String currentSharingRate;

        /**
         * 老分润比例
         */
        private String oldSharingRate;

        /**
         * 新分润比例
         */
        private String newSharingRate;

        /**
         * 分润记录状态(1-待确认，2-已拒绝，3-已同意调整 待生效，4-已经生效)
         */
        private Integer sharingStatus;

        private Integer hostType;

        private String hostTypeName;

        private Integer isGroup;

        private Long total_score;
    }
    private String len;
    private Integer total;
}
