package cn.taqu.gonghui.common.constant;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum PassRejectEnum {

    /**
     * 通过
     */
    PASS(1, "同意退会"),
    REJECT(2, "拒绝退会"),
    ;

    private Integer code;
    private String name;

    PassRejectEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code) {
        for (PassRejectEnum location : PassRejectEnum.values()) {
            if (Objects.equals(location.getCode(), code)) {
                return location.getName();
            }
        }
        return "";
    }

}
