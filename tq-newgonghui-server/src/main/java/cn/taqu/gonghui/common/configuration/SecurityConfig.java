package cn.taqu.gonghui.common.configuration;


import cn.taqu.gonghui.common.utils.SpringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.filter.CorsFilter;

import java.util.Arrays;

/**
 * spring security配置

 */
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter
{


    /**
     * 自定义用户认证逻辑
     */
    @Autowired
    private UserDetailsService userDetailsService;
    /**
     * 认证失败处理类
     */
    @Autowired
    private AuthenticationEntryPointImpl unauthorizedHandler;

    /**
     * 退出处理类
     */
    @Autowired
    private LogoutSuccessHandlerImpl logoutSuccessHandler;

    /**
     * token认证过滤器
     */
    @Autowired
    private JwtAuthenticationTokenFilter authenticationTokenFilter;

    /**
     * 跨域过滤器
     */
    @Autowired
    private CorsFilter corsFilter;

    @Autowired
    private SmsCodeAuthenticationSecurityConfig smsCodeAuthenticationSecurityConfig;

    /**
     * 解决 无法直接注入 AuthenticationManager
     *
     * @return
     * @throws Exception
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception
    {
        return super.authenticationManagerBean();
    }

    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception
    {

        httpSecurity.apply(smsCodeAuthenticationSecurityConfig).and().authorizeRequests();

        SoaMatcher soaMatcher = new SoaMatcher()
            //.excludeMethod("verify", "sendLoginVerify")
            .excludeService("verify")
            .excludeMethods("account", Arrays.asList(new String[]{"login","sildeVerify"}))
            .excludeMethods("common", Arrays.asList(new String[]{"getSelectList","refreshAccount"}))
            .excludeService("callback")
            .excludeMethods("teamHost", Arrays.asList("quitGuildLog","quitGuildList","quitGuildReview","applyQuitGuild","getQuitGuildStatus","validQuitGuild"
            ,"createTokenForTest","inviteHostView","inviteHostReview","nodeDetail","hostInfo","multiHostInfo","testFinance"))
            .excludeService("manageUser")
            .excludeService("manageMenu")
            .excludeService("financeManage")
            .excludeService("manageTeam")
            .excludeService("manageRole")
            .excludeService("manageNotice")
            .excludeService("manageSharingProfit")
            .excludeService("manageOrganization")
            .excludeService("manageMove")
            .excludeService("manageTeamHost")
            .excludeService("manageRecommendApplyCardUseLog")
            .excludeService("manageAgreement")
            .excludeService("manageTeamEmployee")
            .excludeService("manageOrgStatistic")
            .excludeService("manangeInvitationRecord")
            .excludeService("manageEmployee")
            .excludeService("qiniu")
            .excludeService("region")
            .excludeService("manageFeedback")
            .excludeService("ManageSharingProfitRecord")
            .excludeService("manageChatRoom")
            .excludeService("manageLog")
            .excludeService("manageSystemActivityIncome")
            .excludeMethods("teamHost", Arrays.asList("getPunishTagConfig","riskPunishList","createTokenForTest"))
            .excludeService("manageOperate")
            .excludeService("manageOrganization")
            .excludeService("tabulateData")
            .excludeService("businessTask")
            .excludeService("Gonghui2")
            .excludeService("credit")
            .excludeService("backstageVerify")
            .excludeService("credit")
            .excludeService("orgLog")
            .excludeService("managerAuditOrder")
                .excludeService("managerRoom")
                .excludeService("auditOrderTask")
                .excludeService("roomTask")
                .excludeService("hostTask")
                .excludeService("manageMultiLive")
                ;
        httpSecurity
                // CSRF禁用，因为不使用session
                .csrf().disable()
                // 认证失败处理类
                .exceptionHandling().authenticationEntryPoint(unauthorizedHandler).and()
                // 基于token，所以不需要session
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
                .headers().frameOptions().disable();
        httpSecurity.requestMatcher(soaMatcher);
        httpSecurity.authorizeRequests().antMatchers("/metrics" ).permitAll();
        httpSecurity.authorizeRequests().antMatchers("/actuator" ).permitAll();
        httpSecurity.authorizeRequests().antMatchers("/actuator/*" ).permitAll();


        httpSecurity.authorizeRequests().anyRequest().authenticated();

        SoaIncludeMatcher logoutMatcher = new SoaIncludeMatcher()
                .includeMethod("account", "logout");
        httpSecurity.logout().logoutRequestMatcher(logoutMatcher).logoutSuccessHandler(logoutSuccessHandler);

        // 添加JWT filter
        httpSecurity.addFilterBefore(authenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
        // 添加CORS filter
        httpSecurity.addFilterBefore(corsFilter, JwtAuthenticationTokenFilter.class);
    }


    /**
     * 身份认证接口
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception
    {
        auth.userDetailsService(userDetailsService);
    }
}
