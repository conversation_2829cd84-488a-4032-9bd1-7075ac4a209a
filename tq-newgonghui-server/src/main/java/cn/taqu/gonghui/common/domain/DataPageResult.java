package cn.taqu.gonghui.common.domain;

import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;
@Getter
@Setter
public class DataPageResult<T> {

    private List<T> list = Collections.emptyList();

    private static final DataPageResult<?> empty = new DataPageResult<>();

    private long total = 0;
    private long pageSize = 15;
    private long page = 1;

    @SuppressWarnings("unchecked")
    public static <T> DataPageResult<T> emptyResult(){
        return (DataPageResult<T>) empty;
    }
}
