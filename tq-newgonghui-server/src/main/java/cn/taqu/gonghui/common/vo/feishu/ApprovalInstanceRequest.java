package cn.taqu.gonghui.common.vo.feishu;

import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/16 14:59
 **/
@Data
public class ApprovalInstanceRequest {
    private Integer businessType;
    private String approval_code;
    private String user_id;
    private String open_id;
    private String form;
    /**
     * 审批实例 uuid，用于幂等操作,
     * 每个租户下面的唯一key，同一个 uuid 只能用于创建一个审批实例，
     * 如果冲突，返回错误码 60012
     */
    private String uuid;
    /**
     * 可配置是否可以再次提交
     */
    private Boolean allow_resubmit = false;
    /**
     * 可配置是否可以重新提交
     */
    private Boolean allow_submit_again = false;
    @Data
    public static class Form {
        public Form() {
        }

        public Form(String id, String type) {
            this.id = id;
            this.type = type;
        }

        public Form(String id, String type, Object value) {
            this.id = id;
            this.type = type;
            this.value = value;
        }

        private String id;
        private String type;
        private Object value;
    }
}
