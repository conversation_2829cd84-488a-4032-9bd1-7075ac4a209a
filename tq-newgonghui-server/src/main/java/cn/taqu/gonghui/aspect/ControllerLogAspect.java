package cn.taqu.gonghui.aspect;

import cn.hutool.json.JSONUtil;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.core.web.protocol.http.RequestParams;
import cn.taqu.gonghui.common.utils.SpringUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.DispatcherServlet;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @description: some desc
 * @author: chenshuibi
 * @email: <EMAIL>
 * @date: 2022/06/22
 */
@Aspect
@Order(100)
@Slf4j
@Component
public class ControllerLogAspect {
    @Pointcut("execution(* cn.taqu.gonghui.controller..*(..))")
    public void controller() {
    }

    private String getRequestURL() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return requestAttributes.getRequest().getRequestURL().toString();
    }

    @Before("controller()")
    public void doBefore(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            List<Object> requestParams = Lists.newArrayList();
            for (int i = 0, length = args.length; i < length; i++) {
                if (args[i] instanceof MultipartFile || args[i] instanceof HttpServletResponse || args[i] instanceof HttpServletRequest) {
                    continue;
                }
                if (args[i] instanceof RequestParams) {
                    RequestParams params = (RequestParams) args[i];
                    for (int j = 0; j < params.getCount(); j++) {
                        requestParams.add(params.getFormJsonNode(j).toString());
                    }
                }
            }
            SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
            Integer appCode = soaBaseParams.getAppcode();
            Integer cloned = soaBaseParams.getCloned();
            log.debug("appCode={},cloned={},请求参数：{}", appCode, cloned, JSONUtil.toJsonStr(requestParams));
        } catch(Exception e) {
            log.warn("获取controller参数异常", e);
        }
    }

    @AfterReturning(value = "controller()", returning = "result")
    public void doAfter(Object result) {
        log.debug("返回参数：{}", JSON.toJSONString(result));
    }
}


