package cn.taqu.gonghui.aspect.norepeat;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 防止同时提交注解
 * <AUTHOR>
 * @date 2022/10/11
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface NoRepeatCommit {
    // 唯一标识
    String uniqueKey() default "";

    // 提示语
    String msg() default "请勿重复提交";

    // 释放锁类型 1过期时间自动释放 2执行完成手动释放
    int releaseType() default 1;

    // key的过期时间3s
    int expire() default 3;
}