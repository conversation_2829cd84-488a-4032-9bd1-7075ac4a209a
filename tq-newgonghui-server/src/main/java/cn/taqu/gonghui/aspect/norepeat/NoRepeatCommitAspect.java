package cn.taqu.gonghui.aspect.norepeat;

import cn.taqu.core.exception.ServiceException;
import cn.taqu.gonghui.common.constant.CodeStatus;
import cn.taqu.gonghui.common.service.TokenService;
import cn.taqu.gonghui.common.utils.RedisUtil;
import cn.taqu.gonghui.common.utils.ServletUtils;
import cn.taqu.gonghui.common.vo.LoginUser;
import cn.taqu.gonghui.system.entity.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @data 2022/10/11
 */
@Component
@Slf4j
@Aspect
public class NoRepeatCommitAspect {

    /**
     * 执行完成释放
     */
    private static final int PROCESS_DONE = 2;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TokenService tokenService;

    @Pointcut("@annotation(cn.taqu.gonghui.aspect.norepeat.NoRepeatCommit)")
    public void point() {}

    @Around("point()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        NoRepeatCommit noRepeatCommit = method.getAnnotation(NoRepeatCommit.class);

        // 请求地址
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        String requestUrl = requestAttributes.getRequest().getRequestURL().toString();
        // 设置val 防止ab线程误删
        String lockVal = requestAttributes.getSessionId();
        // 登录用户 (必须是能获取到用户信息的)
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        // 释放锁类型
        int releaseType = noRepeatCommit.releaseType();
        // 唯一key
        String key = requestUrl.concat(user.getUserName()).concat(noRepeatCommit.uniqueKey());
        // 过期时间
        long expire = (long) noRepeatCommit.expire();

        // 获取锁
        boolean isSuccess = tryRedisLock(key, lockVal, expire);
        if (isSuccess) {
            try {
                // 执行
                return joinPoint.proceed();
            } finally {
                // 如果设置执行完成类型 则手动删除锁 并且要比对val值是否相等 防止删除另一个线程的lock
                if (PROCESS_DONE == releaseType && redisUtil.get(key).equals(lockVal)) {
                    redisUtil.del(key);
                }
            }
        } else {
            throw new ServiceException(CodeStatus.EXEC_ERROR.value(), noRepeatCommit.msg());
        }
    }

    /**
     * 获取锁
     * @param key
     * @param value
     * @param expire
     * @return
     */
    private boolean tryRedisLock(String key, String value, Long expire) {
        try {
            return redisUtil.set(key, value, expire, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("防重注解,获取锁失败",e);
            e.printStackTrace();
        }
        return false;
    }
}