package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.gonghui.soa.dto.CertificationInfo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CertificationManager {
    @SoaReference("account")
    private CertificationClient certificationClient;

    public CertificationInfo getInfoByUuid(String accountUuid) {
        return certificationClient.getInfoByUuid(accountUuid);
    }



}
