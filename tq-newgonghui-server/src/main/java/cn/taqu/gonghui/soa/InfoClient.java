package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;
import cn.taqu.gonghui.soa.dto.Info;

import java.util.Map;

@SoaProvider("info")
public interface InfoClient {

    /**
     * 根据用户普通ID查询用户信息
     */
    Info getInfoByNormalCard(String cardId);


    Map<String, Map<String,Object>> getInfoByUuidsNoSecret(String[] accountUuids, String[] fields, String version, boolean isSecret, boolean returnLevelNum);

}
