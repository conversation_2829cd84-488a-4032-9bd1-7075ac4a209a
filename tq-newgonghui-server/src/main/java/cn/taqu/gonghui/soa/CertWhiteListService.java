package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-11 11:26
 */
@SoaProvider("certWhiteList")
public interface CertWhiteListService {

    /**
     * 添加认证白名单
     * @param accountUuid
     * @param type
     * @param remark
     * @param operator
     */
    void addCertWhiteListFromServer(String accountUuid, Integer type, String remark, String operator);
}
