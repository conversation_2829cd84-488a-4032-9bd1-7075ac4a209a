package cn.taqu.gonghui.soa;

import cn.taqu.core.etcd.annotation.EtcdValue;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.gonghui.common.utils.SoaHeaderBuilder;
import com.google.common.collect.ImmutableMap;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/25 上午10:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TqTradeCenterService {

    @EtcdValue("/soa/go/taqu-trade-center")
    private static String host;

    private final RestTemplate restTemplate;

    public BigDecimal getAccountFee(String uuid, BigDecimal amount) {
        Map<String, Object> body = ImmutableMap.of(
            "account_uuid", uuid,
            "amount", amount
        );
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, SoaHeaderBuilder.buildDefaultHeaders(SoaBaseParams.fromThread()));
        try {
            HttpEntity<Fee> resp = restTemplate.postForEntity(host.concat("/Withdrawal/getAccountFee"), entity, Fee.class);
            BigDecimal fee = Optional.ofNullable(resp.getBody()).map(f -> f.getFee().divide(BigDecimal.valueOf(100))).orElse(BigDecimal.ZERO);
            log.info("account fee is {} {} {}", uuid, amount, fee);
            return fee;
        } catch (Exception e) {
            log.error(uuid + " get fee error", e);
            return BigDecimal.ZERO;
        }
    }

    @Getter
    @Setter
    public static class Fee {
        private BigDecimal fee;
    }

}
