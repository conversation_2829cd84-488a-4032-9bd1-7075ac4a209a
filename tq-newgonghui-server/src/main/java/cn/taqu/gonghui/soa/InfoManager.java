package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.client.annotation.SoaReference;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.soa.dto.Info;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class InfoManager {
    @SoaReference("account")
    private InfoClient infoClient;

    public Info getInfoByNormalCard(String cardId) {
        try{
            return infoClient.getInfoByNormalCard(cardId);
        }catch (Exception e){
            log.error(e.getMessage(),e);
            return null;
        }

    }

    public Map<String,Info> getInfoByUuidsNoSecret(List<String> accountUuids) {

        String[] fields = new String[]{
                "default_card_id",
                "account_uuid",
                "avatar",
                "account_name",
                "mobile"
        };
        String[] accountUuids_ = accountUuids.toArray(new String[0]);

        Map<String,Map<String, Object>> retMaps =
                infoClient.getInfoByUuidsNoSecret(accountUuids_, fields, null, false, false);


        if (MapUtils.isNotEmpty(retMaps)) {
            Map<String,Info> infoMap = new HashMap<>();
            retMaps.forEach((key,retMap)->{
                Info info = JsonUtils.stringToObject2(JsonUtils.objectToString2(retMap), Info.class);
                infoMap.put(key,info);
            });
            return infoMap;

        }
        return Collections.emptyMap();
    }

    public Info getInfoByUuidNoSecret(String accountUuid) {

        String[] fields = new String[]{
                "default_card_id",
                "account_uuid",
                "avatar",
                "account_name",
                "mobile"
        };
        String[] accountUuids = new String[]{
                accountUuid
        };

        Map<String, Object> retMap = infoClient.getInfoByUuidsNoSecret(accountUuids, fields, null, false, false)
                .get(accountUuid);


        if (retMap != null) {
            return JsonUtils.stringToObject2(JsonUtils.objectToString2(retMap), Info.class);

        }
        return null;
    }
}
