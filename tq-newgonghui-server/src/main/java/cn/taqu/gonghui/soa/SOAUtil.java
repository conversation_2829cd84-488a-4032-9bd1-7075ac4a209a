package cn.taqu.gonghui.soa;

import cn.taqu.core.common.pool.ConnectParam;
import cn.taqu.core.common.pool.PoolSoaClient;
import cn.taqu.core.etcd.EtcdClientFactory;
import cn.taqu.core.etcd.EtcdListener;
import cn.taqu.core.etcd.lib.EtcdClient;
import cn.taqu.core.etcd.lib.EtcdClientException;
import cn.taqu.core.etcd.lib.EtcdResult;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.ConfigurableEnvironment;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Slf4j
public class SOAUtil {
    private static final Object THREAD_LOCK = new Object();

    private static final Map<String, PoolSoaClient> SOA_CLIENT_CACHE = new HashMap<>();

    public static PoolSoaClient create(String url) {
        PoolSoaClient soaClient = SOA_CLIENT_CACHE.get(url);
        if (soaClient == null) {
            synchronized (THREAD_LOCK) {
                soaClient = SOA_CLIENT_CACHE.computeIfAbsent(url, SOAUtil::createFromEtcd);
            }
        }
        return soaClient;
    }

    private static PoolSoaClient createFromEtcd(String url) {
        ConfigurableEnvironment env = SpringContextHolder.getBean(ConfigurableEnvironment.class);
        final String etcdUrl = env.getProperty("etcd.url");
        final String watchDir = "/" + env.getProperty("etcd.watchDir") + "/" + StringUtils.removeStart(url, "/");

        final EtcdClient etcdClient = EtcdClientFactory.getClient(etcdUrl, watchDir, new EtcdListener() {
            @Override
            public Properties doInitProperties(EtcdClient client) {
                return null;
            }

            @Override
            public void doListen(String key, String value) {
                if (StringUtils.isBlank(value)) {
                    log.error("etcd中soa服务[{}]的url为空", key);
                    return;
                }

                if (watchDir.equals(key)) {
                    PoolSoaClient oldClient = SOA_CLIENT_CACHE.get(key);
                    if (oldClient == null) {
                        ConnectParam connectParam = new ConnectParam();
                        connectParam.setMaxPerRoute(10);
                        connectParam.setMaxConnection(10);
                        connectParam.setConnectTimeout(2000);
                        connectParam.setRequestTimeout(2000);
                        connectParam.setSocketTimeout(2000);
                        SOA_CLIENT_CACHE.put(key, new PoolSoaClient(value, connectParam));
                    } else {
                        oldClient.setUrl(value);
                    }
                }
                log.info("soa服务[{}]的url修改为[{}]", key, value);
            }
        });

        try {
            EtcdResult etcdResult = etcdClient.get(watchDir);
            String soaServerUrl = etcdResult == null ? null : etcdResult.node == null ? null : etcdResult.node.value;
            if (StringUtils.isBlank(soaServerUrl)) {
                throw new ServiceException("soa_url_empty", "从etcd获取soa服务地址失败，key:[" + watchDir + "]，该配置不存在，请检查!");
            }
            ConnectParam connectParam = new ConnectParam();
            connectParam.setMaxPerRoute(10);
            connectParam.setMaxConnection(10);
            connectParam.setConnectTimeout(2000);
            connectParam.setRequestTimeout(2000);
            connectParam.setSocketTimeout(2000);
            return new PoolSoaClient(soaServerUrl, connectParam);
        } catch (EtcdClientException e) {
            throw new ServiceException("从etcd[serviceDiscoveryUrl]获取[" + watchDir + "]失败", e);
        }
    }


}
