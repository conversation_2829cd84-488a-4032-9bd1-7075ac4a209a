package cn.taqu.gonghui.soa;

import cn.taqu.core.web.springmvc.JsonResult;
import cn.taqu.gonghui.common.vo.res.LinkAccountRes;
import cn.taqu.gonghui.system.dto.PunishLogDTO;
import cn.taqu.gonghui.system.dto.PunishTagConfigDTO;
import cn.taqu.gonghui.system.vo.PunishLogVO;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import org.apache.poi.ss.formula.functions.T;
import springfox.documentation.spring.web.json.Json;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/08
 */
public interface PunishTicketSoaService {

    /**
     * 直播处罚
     * @param punishLogDTO
     * @return
     */
    JsonResult doPunish(PunishLogDTO punishLogDTO);

    /**
     * 处罚理由列表
     * @param punishTagConfigDTO
     * @return
     */
    List<Object> getPunishTagConfig(PunishTagConfigDTO punishTagConfigDTO);

    /**
     * 风控处罚列表
     * @param punishLogDTO
     * @return
     */
    PageInfo<PunishLogVO> riskPunishList(PunishLogDTO punishLogDTO);


    /**
     * 根据uuid查询绑定设备的关联账户
     * @param hostUuid
     * @return
     */
    LinkAccountRes linkAccountFromToken(String hostUuid);

}
