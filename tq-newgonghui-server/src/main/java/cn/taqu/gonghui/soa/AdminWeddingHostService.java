/*
package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;

import java.util.List;
import java.util.Map;

*/
/**
 * <AUTHOR> Wu.D.J
 *//*

@SoaProvider("AdminWeddingHost")
public interface AdminWeddingHostService {

    List<Map<String, Object>> getHostStat(Object[] param, Integer page);

    List<Map<String, Object>> allOnlineHost();

    String changeHost(String weddingUuid, String hostUuid, Long consortiaId);
}
*/
