package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;
import cn.taqu.gonghui.common.vo.HostVo;
import cn.taqu.gonghui.common.vo.LiveHostSelfStatisticVo;
import cn.taqu.gonghui.common.vo.LiveHostStatisticVo;
import cn.taqu.gonghui.common.domain.CommonPage;
import cn.taqu.gonghui.system.vo.*;


import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/6/1
 */
@SoaProvider("Gonghui")
public interface GonghuiService {


    /**
     * live_api/Gonghui/inviteHost($live_no, $mobile, $real_name, $businessman_uuid, $consortia_id, $remark = "", $oper_uuid = "", $spilt_ratio = 0);
     * * @param $live_no 直播id
     * * @param $mobile 手机号
     * * @param $real_name 真实名字
     * * @param $businessman_uuid 经济人uuid
     * * @param $consortia_id 公会id
     * * @param $remark 备注
     * * @param $oper_uuid 操作人uuid
     * * @param $spilt_ratio 分成比例
     * * @param $host_settlement_type 主播类型 1自营 2自提
     */
    void inviteHost(String liveNo, String mobile, String realName, String businessmanId, String consortiaId, String remark, String operUuid, Integer spiltRatio,Integer hostType);


    /**
     * 重新邀请主播
     * @param inviteId
     * @param accountUuid
     */
    void reInvite(Long inviteId,String accountUuid);


    /**
     * "id": "138",
     * "host_uuid": "depx1u6d80r5",
     * "live_no": "*********",
     * "businessman_uuid": "cgrqyri7fmc7",
     * "consortia_id": "110033",
     * "remark": "",
     * "status": "1",
     * "create_time": "**********",
     * "update_time": "**********",
     * "oper_uuid": "cgrqyri7fmc7"
     *
     * @param page
     * @param pageSize
     * @param inviteId
     * @param teamIdList
     * @param businessmanUuids
     */
    CommonPage<Map<String,Object>> getInviteList(Integer page, Integer pageSize, Long inviteId, List<Long> teamIdList, List<String> businessmanUuids);

    /**
     * 根据主播uuid和机构id获取推荐位信息
     * @param hostUuid
     * @param consortiaId
     * @return
     */
    Map<String,Object> getRecommendApplyInfo(String hostUuid,String consortiaId);

    /**
     * 申请推荐位
     * @param consortiaId
     * @param date
     * @param location
     * @param hostUuid
     * @param time
     * @param isGold
     */
    void addRecommendApplyG(String consortiaId, String date, String location, String hostUuid, String time, Integer isGold);




    /**
     * 获取主播新增数据
     * @param consortiaIdList
     * @param hostUuids
     * @param date
     * @param order_type
     * @param sort_type
     * @param page
     * @param pageSize
     * @param export
     * @return
     */
    LiveHostStatisticNewVo getConsortiaNewHostStat(List<Long> consortiaIdList, List<String> hostUuids, String date, String order_type, String sort_type, Integer page,
                                                   Integer pageSize, Integer export,String apply_level,String live_no);

    void changeHostBusinessman(String hostUuid,String businessUuid);

    void changeHostConsortia(String consortiaId, List<String> hostUuidList);


    LiveHostStatisticVo hostStatistic(List<Long> orgUuid,String start,String end,String type,String order_type,String sort_type,List<String> host_uuid,String apply_level,Integer page,Integer rows,String live_status);

    /**
     * @param teamIds
     * @param businessmanUuids
     * @param hostUuids
     * @param page
     * @param pageSize
     * @param liveStatus
     * @param idCard
     * @param liveNo
     * @param hostStatus
     * @param lastOnlineTimeStart
     * @param lastOnlineTimeEnd
     * @param applyLevel
     * @param orderType
     * @param addConsortiaTimeStart
     * @param addConsortiaTimeEnd
     * @param createStartTime
     * @param createStartEnd
     * @param isUpdate
     * @return
     */
    HostVo getHostList(List<Long> teamIds, List<String> businessmanUuids, List<String> hostUuids, Integer page, Integer pageSize,String liveStatus,String idCard, String liveNo,String hostStatus,String lastOnlineTimeStart,String lastOnlineTimeEnd,String applyLevel,String orderType,String addConsortiaTimeStart ,String addConsortiaTimeEnd,String createStartTime ,String createStartEnd,Integer isUpdate );

    LiveHostSelfStatisticVo getHostLiveTimeStat(String hostUuid,String startDate,String endDate);

    List<Map<String, String>> getAllConsortiaHost(List<Long> org_uuid,List<String> agent_uuid);

    List<Map<String, String>> getFrameList(List<Long> teamIds,List<String> agent_uuid, Integer page,Integer pageSize,List<String> host_uuid ,String apply_level);

    /**
     * 获取中转机构的团队id
     * @return
     */
    Map<String,Integer> getTaquConsortiaId();

    /**
     * 获取素人公会的团队id
     * @return
     */
    Map<String,Integer> getPersonalConsortiaId();

    void sendSpiltRatioNotice(Long changeId, String hostUuid, String orgName, Long changeTime);

    /**
     * 获取处罚理由列表
     * @return
     */
    Map<String,Object> getPunishReasonList();

    /**
     * 警告主播
     * @param hostUuid 主播uuid
     * @param operatorName 操作人名称
     * @param operatorId   操作人uuid
     * @param reason    警告理由
     */
    int ghWarnHost(String hostUuid,String operatorName,String operatorId,String reason);


    /**
     * 处罚主播
     * @param hostUuid 主播uuid
     * @param operatorName
     * @param operatorId
     * @param reason  禁播时长 1-72小时之间l
     * @param punishHour 处罚理由
     */
    void ghPunish(String hostUuid,String operatorName,String operatorId,String reason,Integer punishHour);


    /**
     * 警告列表
     * @param page
     * @param pageSize
     * @param startTime
     * @param endTime
     * @param liveNo 他趣主播id
     * @param hostName 主播名称
     * @param consortiaId 团队id
     * @param businessmanUuid 经纪人uuid
     * @return
     */
    Map<String,Object> getWarnLog(Integer page,Integer pageSize,Long startTime,Long endTime,String liveNo,String hostName,List<Long> consortiaId,String businessmanUuid);

    /**
     *
     * @param page
     * @param pageSize
     * @param startTime
     * @param endTime
     * @param liveNo
     * @param consortiaId
     * @param businessmanUuid
     * @param source  处罚来源
     * @param punishStatus 处罚状态
     * @return
     */
    Map<String,Object> getPunishLog(Integer page,Integer pageSize,Long startTime,Long endTime,String liveNo,List<Long> consortiaId,String businessmanUuid,Integer source,Integer punishStatus);


    /**
     * 获取系统活动收益统计
     * @param startTime
     * @param endTime
     * @param page
     * @param consortiaIds
     * @param hostUuids
     * @param applyLevel
     * @param liveNo
     * @param export
     * @return
     */

    List<Map<String, Object>> getSystemActivityIncomeStat(Long startTime, Long endTime, Integer page, List<Long> consortiaIds, List<String> hostUuids, String applyLevel, String liveNo, Integer export);
//
//
//
//    /**
//     * 获取首页统计数据
//     * service: Gonghui
//     * method: getStatData($consortia_id = [], $businessman_uuid = ‘’)
//     *
//     * @return
//     */
//    StatData getStatData(List<Long> consortiaIdList, String businessmanUuid);
//
//    //    method: getLineChartData($type = 1, $consortia_id = [], $businessman_uuid = ‘’)
////    请求值说明
////    @param type string 类型 1【今日】 2【昨日】 3【7日】 4 【30日】
////    @param consortia_id array 公会（团队）id数组
////    @param businessman_uuid string 经纪人id
//    ChartData getLineChartData(String type, List<Long> consortiaIdList, String businessmanUuid);
//
//    /**
//     * method:getDayHostRank($consortia_id = [], $page = 1, $limit = 10)
//     * 请求值说明
//     * @param consortiaIdList array 公会（团队）id数组
//     * @param page array 页数
//     * @param limit array 每页数量
//     */
//
//    DayHostRank getDayHostRank(List<Long> consortiaIdList, int page, int limit);
//    //    service: Gonghui
////    method:getFrameDetail($host_uuid, $consortia_id)
//    FrameDetail getFrameDetail(String host_uuid, String consortia_id);
//
//    //    service: Gonghui
////    method:getFrameListExtraInfo($consortia_id, $businessman_uuid = ‘’, $page, $pageSize = ‘’, $host_uuid = ‘’, $apply_level = ‘’)
//    FrameListExtraInfo getFrameListExtraInfo(String consortia_id, String businessman_uuid, int page, int pageSize, String host_uuid, String apply_level);
//
//    //    method:getHostAmountTaskList($page = 1, $stat_time = 0, $consortia_id = [], $businessman_uuid = ‘’, $host_uuid = ‘’, $is_new_host = ‘’)
//    HostAmountTaskList getHostAmountTaskList(int page, int stat_time, List<Long> consortia_id,String businessman_uuid,String host_uuid,String is_new_host,Integer is_export);


}
