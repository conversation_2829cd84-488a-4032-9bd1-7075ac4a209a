package cn.taqu.gonghui.soa;

import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.live.param.*;
import cn.taqu.gonghui.live.vo.multilive.dto.*;

/**
 * <AUTHOR>
 * @Date 2024/02/18
 * 多人娱乐SOA服务
 */
public interface MultiLiveSoaService {


    /**
     * 管理端--多人娱乐主播数据
     * @param search 搜索条件
     * @return
     */
    PageDataResult<MultiLiveHostDataDto> adminHostStatsData(MultiLiveHostSearch search);

    /**
     * 管理端--多人娱乐公会数据
     * @param search
     * @return
     */
    PageDataResult<MultiLiveConsortiaDataDto> adminConsortiaStatsData(MultiLiveConsortiaSearch search);


    /**
     * 用户端--多人娱乐主播数据
     * @param search
     * @return
     */
    PageDataResult<MultiLiveHostDataOfUMDto> hostStatsData(MultiLiveHostSearchOfUM search);

//    /**
//     * 用户端--多人娱乐房间数据
//     * @param search
//     * @param consortiaId
//     * @return
//     */
//    PageDataResult<MultiLiveRoomDataOfUMVo> roomStatsData(MultiLiveRoomSearchOfUM search, Long consortiaId);

    PageDataResult<MultiLiveConsortiaDataOfUMDto> consortiaStatsData(MultiLiveConsortiaSearchOfUM search);

    PageDataResult<MultiLiveRoomDataOfUMDto> roomStatsData(MultiLiveRoomSearchOfUM search);
}
