package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;
import cn.taqu.gonghui.common.domain.CommonPage;
import cn.taqu.gonghui.common.vo.HostVo;
import cn.taqu.gonghui.common.vo.LiveHostSelfStatisticVo;
import cn.taqu.gonghui.common.vo.LiveHostStatisticVo;
import cn.taqu.gonghui.system.vo.*;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/6/1
 */
public interface GonghuiSoaService {




    /**
     * 获取首页统计数据
     * service: Gonghui
     * method: getStatData($consortia_id = [], $businessman_uuid = ‘’)
     *
     * @return
     */
    JSONObject getStatData(List<Long> consortiaIdList, String businessmanUuid);

    //    method: getLineChartData($type = 1, $consortia_id = [], $businessman_uuid = ‘’)
//    请求值说明
//    @param type string 类型 1【今日】 2【昨日】 3【7日】 4 【30日】
//    @param consortia_id array 公会（团队）id数组
//    @param businessman_uuid string 经纪人id
    JSONObject getLineChartData(String type, List<Long> consortiaIdList, String businessmanUuid);

    /**
     * method:getDayHostRank($consortia_id = [], $page = 1, $limit = 10)
     * 请求值说明
     * @param consortiaIdList array 公会（团队）id数组
     * @param page array 页数
     * @param limit array 每页数量
     */

    JSONObject getDayHostRank(List<Long> consortiaIdList, Integer page, Integer limit,String host_uuid);
    //    service: Gonghui
//    method:getFrameDetail($host_uuid, $consortia_id)
    JSONObject getFrameDetail(String host_uuid, String consortia_id);

    //    service: Gonghui
//    method:getFrameListExtraInfo($consortia_id, $businessman_uuid = ‘’, $page, $pageSize = ‘’, $host_uuid = ‘’, $apply_level = ‘’)
    JSONObject getFrameListExtraInfo(List<Long> consortia_ids, String businessman_uuid, Integer page, Integer pageSize, String host_uuid, String apply_level);

    //    method:getHostAmountTaskList($page = 1, $stat_time = 0, $consortia_id = [], $businessman_uuid = ‘’, $host_uuid = ‘’, $is_new_host = ‘’)
    JSONObject getHostAmountTaskList(Integer page, Integer stat_time, List<Long> consortia_id,String businessman_uuid,String host_uuid,String is_new_host,Integer is_export);


}
