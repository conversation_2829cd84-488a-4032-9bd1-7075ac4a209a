package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;

import java.util.List;
import java.util.Map;

@SoaProvider("operation")
public interface OperationService {

    /**
     * 批量加密
     * @param code
     * @param content
     * @return
     */
    Map<String, String>  batchEncrypt(String code, Map<String, String> content);

    /**
     * 批量解密
     * @param code
     * @param contentMap
     * @return
     */
    Map<String, String> batchDecrypt(String code, Map<String, String> contentMap);


    /**
     * 批量解密
     * @param code
     * @param listMap
     * @return
     */
    List<Map<String, String>> batchDecrypt(String code, List<Map<String, String>> listMap);


    Map<String, String>  batchsm3(Map<String, String> contentMap);



}
