package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2021/4/28
 */
@SoaProvider("info")
public interface InfoService {


    /**
     * 根据uuid获取用户信息
     *
     * @param accountUuids   用户uuid数组
     * @param fields         要获取的字段数组
     * @param version        头像版本 0或空: 返回全路径; 其他: 返回不带host的uri
     * @param isSecret       手机号是否需要加密
     * @param returnLevelNum 是否返回账号等级
     * @return
     */
    Map<String, Map<String,Object>> getInfoByUuid(String[] accountUuids, String[] fields, String version, boolean isSecret, boolean returnLevelNum);

    /**
     * 获取上次活跃时间
     * @param uuid
     * @return
     */
    Long getPrevActiveTime(String uuid);


    /**
     * 根据uuid获取用户信息
     *
     * @param accountUuids   用户uuid数组
     * @param fields         要获取的字段数组
     * @param version        头像版本 0或空: 返回全路径; 其他: 返回不带host的uri
     * @param isSecret       手机号是否需要加密
     * @param returnLevelNum 是否返回账号等级
     * @return
     */
    Map<String, Map<String, Object>> getInfoByUuidsNoSecret(String[] accountUuids, String[] fields, String version, boolean isSecret, boolean returnLevelNum);

    /**
     * 根据手机号获取uuid
     *
     * @param mobile
     * @return
     */
    Map<String, String> getUuidAndNameByMobile(String mobile);


    Map<String, String> getInfoByUuidOrNicknameOrCardId(String uuidOrNickNameOrCardId, String[] fields);


    List<String> listUuidByAccountName(String accountName);

    /**
     * 根据用户普通ID查询用户信息
     * @link https://api.admin.internal.taqu.cn/docs/api/api-1c5mtvjr6jsep
     * @param cardId
     * @return
     */
    Map<String, String> getInfoByNormalCard(String cardId);
}
