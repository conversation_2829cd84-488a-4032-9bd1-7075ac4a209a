package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;
import cn.taqu.gonghui.live.vo.CertificationVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/6/17
 */
@SoaProvider("certification")
public interface CertificationService {

    Map<String,Map<String,Object>> getByUuids(String accountUuid);

    Map<String,Map<String,Object>> getByUuids(List<String> accountUuid);
    //is_checked: Integer 蚂蚁金服认证： 0：未认证 1：已认证
    //real_name: String 真实姓名
    //identity_no: String 身份证号
    //biz_no: String 芝麻认证业务编码
    //reward_account: String 用户提现帐号
    //identity_no_birth: String 出生日期
    Map<String, Object> getInfoByUuid(String accountUuid);

}
