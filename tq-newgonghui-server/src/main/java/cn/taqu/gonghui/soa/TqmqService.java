package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.mq.TqmqResponse;
import cn.taqu.core.soa.server.annotation.SoaProvider;
import java.util.List;
import java.util.Map;

@SoaProvider("operation")
public interface TqmqService {
    TqmqResponse push(String queue, Object msgInfo, Long time);

    TqmqResponse prePush(String queue, Object msgInfo);

    TqmqResponse multiplePush(Map<String, List<String>> messageMap, Long time);

    TqmqResponse pop(String queue);

    TqmqResponse pop(String queue, Long visibilityTimeOut);

    TqmqResponse remove(String queue, Object msgId);
}
