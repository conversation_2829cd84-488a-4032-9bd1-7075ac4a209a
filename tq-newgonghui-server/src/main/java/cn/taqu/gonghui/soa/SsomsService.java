package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;
import cn.taqu.gonghui.system.search.SsoUserSearch;
import cn.taqu.gonghui.system.vo.SsoUserCombobox;
import com.alibaba.fastjson.JSONArray;


@SoaProvider("ssouser")
public interface SsomsService {
    JSONArray searchEffectiveSsoUsersV2(SsoUserSearch ssoUserSearch);

    /**
     * 根据登陆账号获取单点账号信息
     * @param loginname 比如测试环境传admin
     * @return
     */
    SsoUserCombobox getSsoUser(String loginname);
}
