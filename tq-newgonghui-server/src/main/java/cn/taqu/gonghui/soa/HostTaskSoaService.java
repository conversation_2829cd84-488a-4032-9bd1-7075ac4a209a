package cn.taqu.gonghui.soa;

import cn.taqu.gonghui.soa.dto.HostTaskStatisticInfo;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/02/18
 */
public interface HostTaskSoaService {


    /**
     * 获取指定月份的主播成长任务的统计数据
     * @param hostUuidList 主播uuid列表
     * @param monthDate 月份1号0点时间戳
     * @return
     */
    List<HostTaskStatisticInfo> listStatData(List<String> hostUuidList, Long monthDate);

    JSONObject listDetail(String hostUuid, Integer taskUuid, Long month);
}
