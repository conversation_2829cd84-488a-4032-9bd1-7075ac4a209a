package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;
import cn.taqu.gonghui.system.vo.HostApply;
import cn.taqu.gonghui.system.vo.PageData;

import java.util.List;

/**
 * 推荐位接口
 */
@SoaProvider("HostApply")
public interface HostApplyService {

    PageData getRecommendApplyList(String date, List<String> host_uuid, String host_name,
                                   String location, Integer status, String consortia_id, String limit, Integer type);

    /**
     * 查询主播申请相关信息
     * @param page
     * @param pageSize
     * @param orderBy
     * @param condition
     * @return
     */
    HostApply getListWithCondition(String page, String pageSize, String orderBy, String[] condition);


    void cancel_apply(Long id);


}
