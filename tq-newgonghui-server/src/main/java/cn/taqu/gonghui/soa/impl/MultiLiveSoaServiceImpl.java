package cn.taqu.gonghui.soa.impl;

import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.common.utils.StringUtils;
import cn.taqu.gonghui.live.param.*;
import cn.taqu.gonghui.live.vo.multilive.dto.*;
import cn.taqu.gonghui.soa.MultiLiveSoaService;
import cn.taqu.gonghui.soa.SOAUtil;
import cn.taqu.gonghui.soa.impl.res.*;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 协议管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MultiLiveSoaServiceImpl implements MultiLiveSoaService {
    private static final String LIVE_URL = "/soa/application/live/api";


    @Override
    public PageDataResult<MultiLiveHostDataDto> adminHostStatsData(MultiLiveHostSearch search) {
//        Map<String, Object> params = new HashMap<>();
//        params.put("page", search.getPage());
//        params.put("time_type", search.getTimeType());
//        params.put("start_time", search.getStartTime());
//        params.put("end_time", search.getEndTime());
//        params.put("host_uuid", search.getHostUuid());
//        params.put("person_type", search.getPersonal());
//        params.put("consortia_id", search.getTeamIdList());
//        params.put("multi_live_status", search.getMultiRecreationAuthority());
//        params.put("export", search.getExport());

//        Object[] data = {JsonUtils.objectToString(params)};
        Object[] data = {search.getPage(),
                search.getTimeType(),
                search.getStartTime()/1000,
                search.getEndTime()/1000,
                search.getHostUuid(),
                search.getPersonal(),
                search.getTeamIdList(),
                search.getMultiRecreationAuthority(),
                search.getExport()};
        log.info("soa请求AdminMultiLiveStat/getHostData,traceId={}, request data={}", SoaBaseParams.fromThread().getDistinctRequestId(), JSON.toJSONString(data));
        SoaResponse soaResponse = SOAUtil.create(LIVE_URL)
                .call("AdminMultiLiveStat", "getHostData", data);
        log.info("soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();

        if (StringUtils.isBlank(resData)) {
            log.warn("响应数据为空:{}", resData);
            return new PageDataResult<>(search.getPage(), search.getPageSize(), 0L, new ArrayList<>());
        }
        // DTO 转 VO
        PageMultiLiveHostData pageDataResult = JsonUtils.stringToObject(resData, PageMultiLiveHostData.class);
        return new PageDataResult<>(search.getPage(), search.getPageSize(), pageDataResult.getTotal(), pageDataResult.getList());
    }

    @Override
    public PageDataResult<MultiLiveConsortiaDataDto> adminConsortiaStatsData(MultiLiveConsortiaSearch search) {
        Object[] data = {
                search.getPage(),
                search.getTimeType(),
                search.getStartTime()/1000,
                search.getEndTime()/1000,
                search.getTeamIdList(),
                search.getExport()};
        log.info("soa请求AdminMultiLiveStat/getConsortiaData, request data={}", JSON.toJSONString(data));
        SoaResponse soaResponse = SOAUtil.create(LIVE_URL)
                .call("AdminMultiLiveStat", "getConsortiaData", data);
        log.info("soa请求AdminMultiLiveStat/getConsortiaData, soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();

        PageDataResult<MultiLiveConsortiaDataDto> pageDataResult = new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        if (StringUtils.isBlank(resData)) {
            return pageDataResult;
        }
        PageMultiLiveConsortiaData pageMultiLiveConsortiaData = JsonUtils.stringToObject(resData, PageMultiLiveConsortiaData.class);
        pageDataResult.setList(pageMultiLiveConsortiaData.getList());
        pageMultiLiveConsortiaData.setTotal(pageDataResult.getTotal());
        return pageDataResult;
    }

    public PageDataResult<MultiLiveHostDataOfUMDto> hostStatsData(MultiLiveHostSearchOfUM search) {
        Object[] data = {
                search.getPage(),
                search.getConsortiaIds(),
                search.getStartTime()/1000,
                search.getEndTime()/1000,
                search.getHostUuids(),
                search.getExport()
        };
        log.info("soa请求Gonghui/getMultiLiveHostData, request data={}", JSON.toJSONString(data));
        SoaResponse soaResponse = SOAUtil.create(LIVE_URL)
                .call("Gonghui", "getMultiLiveHostData", data);
        log.info("soa请求Gonghui/getMultiLiveHostData, soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();
        PageDataResult<MultiLiveHostDataOfUMDto> pageDataResult = new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        if (StringUtils.isBlank(resData)) {
            return pageDataResult;
        }
        PageMultiLiveHostDataOfUM pageMultiLiveHostDataOfUM = JsonUtils.stringToObject(resData, PageMultiLiveHostDataOfUM.class);
        if (pageMultiLiveHostDataOfUM == null) {
            return pageDataResult;
        }
        if (CollectionUtils.isNotEmpty(pageMultiLiveHostDataOfUM.getList())) {
            pageDataResult.setList(pageMultiLiveHostDataOfUM.getList());
            pageDataResult.setTotal(pageMultiLiveHostDataOfUM.getTotal());
        } else {
            pageDataResult.setTotal(pageMultiLiveHostDataOfUM.getTotal());
        }
        return pageDataResult;
    }

    @Override
    public PageDataResult<MultiLiveConsortiaDataOfUMDto> consortiaStatsData(MultiLiveConsortiaSearchOfUM search) {

        List<Long> teamIdList = (search.getTeamId() != null && search.getTeamId() > 0) ?
                Arrays.asList(search.getTeamId()) : search.getTeamIdList();
        Object[] data = {
                search.getPage(),
                teamIdList,
                search.getStartTime()/1000,
                search.getEndTime()/1000,
                search.getExport()
        };
        log.info("soa请求Gonghui/getMultiLiveConsortiaData, request data={}", JSON.toJSONString(data));
        SoaResponse soaResponse = SOAUtil.create(LIVE_URL)
                .call("Gonghui", "getMultiLiveConsortiaData", data);
        log.info("soa请求Gonghui/getMultiLiveConsortiaData, soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();
        PageDataResult<MultiLiveConsortiaDataOfUMDto> pageDataResult = new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        if (StringUtils.isBlank(resData)) {
            return pageDataResult;
        }
        PageMultiLiveConsortiaDataOfUM pageConsortiaData = JsonUtils.stringToObject(resData, PageMultiLiveConsortiaDataOfUM.class);
        if (pageConsortiaData == null) {
            return pageDataResult;
        }
        if (CollectionUtils.isNotEmpty(pageConsortiaData.getList())) {
            pageDataResult.setList(pageConsortiaData.getList());
            pageDataResult.setTotal(pageConsortiaData.getTotal());
        } else {
            pageDataResult.setTotal(pageConsortiaData.getTotal());
        }
        return pageDataResult;
    }

    @Override
    public PageDataResult<MultiLiveRoomDataOfUMDto> roomStatsData(MultiLiveRoomSearchOfUM search) {
        Object[] data = {
                search.getPage(),
                search.getTeamIds(),
                search.getStartTime()/1000,
                search.getEndTime()/1000,
                search.getRoomUuid(),
                search.getExport()
        };
        log.info("soa请求Gonghui/getMultiLiveRoomData, request data={}", JSON.toJSONString(data));
        SoaResponse soaResponse = SOAUtil.create(LIVE_URL)
                .call("Gonghui", "getMultiLiveRoomData", data);
        log.info("soa请求Gonghui/getMultiLiveRoomData, soaResponse={}", JSON.toJSONString(soaResponse));
        String resData = soaResponse.getData();
        PageDataResult<MultiLiveRoomDataOfUMDto> pageDataResult = new PageDataResult<>(search.getPage(), search.getPageSize(), 0, new ArrayList<>());
        if (StringUtils.isBlank(resData)) {
            return pageDataResult;
        }
        PageMultiLiveRoomDataOfUM pageConsortiaData = JsonUtils.stringToObject(resData, PageMultiLiveRoomDataOfUM.class);
        if (pageConsortiaData == null) {
            return pageDataResult;
        }
        if (CollectionUtils.isNotEmpty(pageConsortiaData.getList())) {
            pageDataResult.setList(pageConsortiaData.getList());
            pageDataResult.setTotal(pageConsortiaData.getTotal());
        } else {
            pageDataResult.setTotal(pageConsortiaData.getTotal());
        }
        return pageDataResult;
    }
}
