package cn.taqu.gonghui.soa.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Info {
    /**
     * 他趣ID
     */
    @JsonProperty("default_card_id")
    private String defaultCardId;
    /**
     * 用户uuid
     */
    @JsonProperty("account_uuid")
    private String accountUuid;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 昵称
     */
    @JsonProperty("account_name")
    private String accountName;

    private String mobile;
}
