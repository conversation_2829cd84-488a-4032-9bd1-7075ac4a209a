package cn.taqu.gonghui.soa;

import cn.taqu.core.soa.server.annotation.SoaProvider;
import cn.taqu.gonghui.system.vo.QiNiuVo;

/**
 * <AUTHOR>
 * @Date 2021/5/18
 */
@SoaProvider("picture")
public interface PictureService {


    QiNiuVo getUploadToken(String bucket,Integer param1,Integer param2);

    QiNiuVo getUploadTokenByMediaType(String type, Object param1, String source,  Object param2, Integer param3);

    Object privateDownloadUrl(String bucket, String baseUrl, Integer DEFAULT_APPCODE);
}
