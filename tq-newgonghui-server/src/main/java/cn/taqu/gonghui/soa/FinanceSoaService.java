package cn.taqu.gonghui.soa;

import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.gonghui.soa.dto.BusinessRequest;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/21 15:52
 */
public interface FinanceSoaService {

    /**
     * 业财下单接口
     * @param businessRequest
     * @return
     */
    SoaResponse chatUserToGuildWithdrawal(BusinessRequest businessRequest);

    /**
     * 获取聊天室个人剩下全部收益
     * @param uuid
     * @return
     */
    BigDecimal getForumAccountChatReward(String uuid);

}
