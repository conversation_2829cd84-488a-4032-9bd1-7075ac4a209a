package cn.taqu.gonghui.soa.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CertificationInfo {
   private Integer isChecked;//蚂蚁金服认证： 0：未认证 1：已认证
   private String realName;// 真实姓名
   private String identityNo;// 身份证号
   private String bizNo;//  芝麻认证业务编码
   private String rewardAccount;//  用户提现帐号
   private String identityNoBirth;// 出生日期
}
