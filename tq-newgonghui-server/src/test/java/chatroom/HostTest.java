package chatroom;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.common.utils.ParamsCheckUtil;
import com.github.pagehelper.PageInfo;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/16 6:06 下午
 **/
public class HostTest {

    @Test
    void testLegalIndentityNO(){
        String id_no = "511133197502050215";
        boolean legalPattern = ParamsCheckUtil.isLegalPattern(id_no);
        System.out.println("是否合法："+legalPattern);
    }

    @Test
    public void hostModifyListTest() {
        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageTeamHost", "hostModifyList", "uw03qewn10zq44h9", 0);
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void hostMoveRecordTest() {
        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageTeamHost", "hostMoveRecord", 1, 20);
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void pageInfoBuildTest(){
        List<String> list = new ArrayList<>();
        list.add("11111");
        list.add("22222");
        PageInfo dataResult = new PageInfo<>(list);
        dataResult.setTotal(300);
        System.out.println(JsonUtils.objectToString(dataResult));
    }
}
