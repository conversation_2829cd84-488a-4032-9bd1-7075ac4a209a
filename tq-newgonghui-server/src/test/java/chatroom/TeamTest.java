package chatroom;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/12 5:39 下午
 **/
@Slf4j
public class TeamTest {

    @Test
    public void teamMoveTest() {
        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageTeam", "teamMove", 1001380, 110257, 1001381, 79);
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void teamInfoTest() {
        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageTeam", "teamInfo", 79);
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void teamMoveHistoryTest() {
        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageTeam", "teamMoveHistory", 1, 20);
        System.out.println(JsonUtils.objectToString(call));
    }
}
