package chatroom;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.common.constant.SysCodeStatus;
import cn.taqu.core.exception.ServiceException;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.core.web.protocol.SoaBaseParams;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.chatroom.search.ChatRoomSearch;
import cn.taqu.gonghui.chatroom.util.FilterCondition;
import cn.taqu.gonghui.common.constant.ConditionEnum;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.live.param.MultiLiveHostSearch;
import cn.taqu.gonghui.soa.dto.BusinessRequest;
import cn.taqu.gonghui.system.dto.PunishLogDTO;
import cn.taqu.gonghui.system.service.impl.FinanceSoaServiceImpl;
import cn.taqu.gonghui.system.service.multilive.MultiLiveStatsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})
public class ChatRoomDataApiTest {





    @Autowired
    private FinanceSoaServiceImpl financeSoaService;

    @Test
    public void testDailyOperationData() {
        ChatRoomSearch search = new ChatRoomSearch();
        search.setStartTime("2022-03-12");
        search.setEndTime("2022-05-25");
        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageChatRoom", "dailyOperationData", search, 1, 10);
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void exportDataTest() {
        ChatRoomSearch search = new ChatRoomSearch();
        search.setStartTime("2022-03-12");
        search.setEndTime("2022-05-25");
        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageChatRoom", "exportData", 65, 1, search);
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void testInviteHost() {
        ChatRoomSearch search = new ChatRoomSearch();
        search.setStartTime("2022-03-12");
        search.setEndTime("2022-03-13");
        search.setTeamId(227L);
        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageChatRoom", "dailyOperationData", search, 1, 10);
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void testInviteHost1() {
        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageChatRoom", "getChatListByPage", 1, 20);
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void testLinetData() {

        List<Map> list = dataApiSoa("/consortiaService/dailyOperationData/line", "2022-03-12", "2022-03-13", Arrays.asList(227L), null, null, null, List.class);
        System.out.println(JsonUtils.objectToString(list));

        ///consortiaService/chatData/count
    }

    @Test
    public void testChatDataCount() {

        List list = dataApiSoa("/consortiaService/chatData/count", "2022-03-22", "2022-03-31", null, Arrays.asList("di7jrie1fsq2"), null, null, List.class);
        System.out.println(JsonUtils.objectToString(list));

        ///consortiaService/chatData/count
        ///consortiaService/chatDetail/report
        ///consortiaService/chatData/report
    }

    @Test
    public void testChatDetailReport() {

        List list = dataApiSoa("/consortiaService/chatDetail/report", "2022-03-26", "2022-04-1", null, Arrays.asList("ed1sskuqhq6x"), null, null, List.class);
        System.out.println(JsonUtils.objectToString(list));
    }

    @Test
    public void testChatDataReport() {

        List list = dataApiSoa("/consortiaService/chatData/report", "2022-03-26", "2022-04-1", Arrays.asList(1000538L, 1000537L), null, null, null, List.class);
        System.out.println(JsonUtils.objectToString(list));


    }


    @Test
    public void testDailyOperationDataDataApi() {

        Map pageMap = new HashMap();
        pageMap.put("page", 1);
        pageMap.put("pageSize", 10);
        List list = dataApiSoa("/consortiaService/roomChatData/overview", "2022-03-25", "2022-03-31", null, null, null, null, List.class);
        System.out.println(JsonUtils.objectToString(list));
    }

    @Test
    public void testInfo() {
        Object[] form = new Object[]{
                "cgv7hf0kwxhf",
                new String[]{"uuid"}
        };
        SoaClient soaClient = new SoaClient("http://j2.test.k8s.taqu.cn/tq-account/api");
        SoaResponse call = soaClient.call("info", "getInfoByUuidOrNicknameOrCardId", form);
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void testNickNameInfo() {
        Object[] form = new Object[]{
                "测试的"

        };
        SoaClient soaClient = new SoaClient("http://j2.test.k8s.taqu.cn/tq-account/api");
        SoaResponse call = soaClient.call("info", "listUuidByAccountName", form);
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void testNickNameInfo1() {

        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageChatRoom", "testNickname");
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void testUuid() {

        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageChatRoom", "testUuid");
        System.out.println(JsonUtils.objectToString(call));
    }

    @Test
    public void testPunishList() {
        Object[] form = new Object[]{
                "yxh"
        };
        SoaClient soaClient = new SoaClient("http://*************:31922/tq-newgonghui/api");
        SoaResponse call = soaClient.call("teamHost", "getPunishTagConfig", form);
        log.info("xxxx,{}", JsonUtils.objectToString(call));
    }

    @Test
    public void testPunishSoa() {
        Object[] form = new Object[]{
                8,
                "xxxxx",
                "2323"
        };
        SoaClient soaClient = new SoaClient("http://*************:31922/tq-newgonghui/api");
        SoaResponse call = soaClient.call("teamHost", "doPunish");
        log.info("xxxx,{}", JsonUtils.objectToString(call));
    }

    @Test
    public void testPunish() {
        PunishLogDTO dto = new PunishLogDTO();
        dto.setAppCode(1);
        dto.setPunishId(7L);
        dto.setAccountUuid("wfzcfmbaeab2btry");
        dto.setBasis("123");
        dto.setBasisType("3");//1.图片、2.文字、3.私信
        dto.setInReason("对内处罚理由");
        dto.setOutReason("对外处罚理由");
        dto.setOperator("admin");

        SoaClient soaClient = new SoaClient("http://j63.test.k8s.taqu.cn/risk-punish/api?");
        SoaResponse call = soaClient.call("punishTicket", "doPunish", dto);
        log.info("xxxx,res:{}", call);
    }

    @Test
    public void testFinance() {
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        BusinessRequest businessRequest = new BusinessRequest();
        Integer appCode = Optional.ofNullable(soaBaseParams.getAppcode()).orElse(1);
        Integer cloned = Optional.ofNullable(soaBaseParams.getCloned()).orElse(1);
        businessRequest.setAccount_uuid("wfzcfmbaeab2btry");
        businessRequest.setAppcode(appCode);
        businessRequest.setCloned(cloned);
        // 调用业财触发 聊天室素人转公会提现 清除素人账户余额
        financeSoaService.chatUserToGuildWithdrawal(businessRequest);
    }

    /**
     * 通用api接口
     *
     * @param startTime
     * @param endTime
     * @param teamIds
     * @param page
     */
    public <T> T dataApiSoa(String api, String startTime, String endTime, List<Long> teamIds, List<String> chatUuids, Integer chatStatus, Map page, Class<T> clazz) {

        List<FilterCondition> filterConditions = buildFilterConditions(startTime, endTime, teamIds);
        SoaResponse soaResponse = null;
        if (MapUtils.isEmpty(page)) {
            soaResponse = doCall("api", "execute", api, filterConditions);
        } else {
            soaResponse = doCall("api", "execute", api, filterConditions, page);
        }
        // SOA 请求
        if (soaResponse.fail()) {
            log.error("Request API error: {}", soaResponse.getMsg());
            throw new ServiceException(SysCodeStatus.ERROR);
        }

        T t = JsonUtils.stringToObject2(soaResponse.getData(), clazz);
        return t;
    }

    /**
     * 构建艺人的条件
     *
     * @param startTime
     * @param endTime
     * @param teamIds
     * @return
     */
    private List<FilterCondition> buildFilterConditions(String startTime, String endTime, List<Long> teamIds) {
        return buildFilterConditions(startTime, endTime, teamIds, null, null);
    }

    private List<FilterCondition> buildFilterConditions(String startTime, String endTime, List<Long> teamIds, List<String> hostUuids, Integer chatStatus) {
        List<FilterCondition> filterConditions = new ArrayList<>();
        FilterCondition range = FilterCondition.builder()
                .key("dt")
                .operator(ConditionEnum.BETWEEN.getValue())
                .value(Arrays.asList(startTime, endTime))
                .build();
        filterConditions.add(range);
        if (CollectionUtils.isNotEmpty(teamIds)) {
            FilterCondition teamIdInList = FilterCondition.builder()
                    .key("consortia_id")
                    .operator(ConditionEnum.IN.getValue())
                    .value(teamIds)
                    .build();
            filterConditions.add(teamIdInList);
        }
        if (CollectionUtils.isNotEmpty(hostUuids)) {
            FilterCondition hostUuidInList = FilterCondition.builder()
                    .key("account_uuid")
                    .operator(ConditionEnum.IN.getValue())
                    .value(hostUuids)
                    .build();
            filterConditions.add(hostUuidInList);
        }
        if (chatStatus != null) {
            FilterCondition chatStatusEq = FilterCondition.builder()
                    .key("chat_status")
                    .operator(ConditionEnum.EQ.getValue())
                    .value(chatStatus)
                    .build();
            filterConditions.add(chatStatusEq);
        }
        return filterConditions;
    }

    private SoaResponse doCall(String service, String method, Object... params) {
        SoaClient soaClient = new SoaClient("http://j74.test.k8s.taqu.cn/data-api/api");
        SoaResponse call = soaClient.call(service, method, params);
        return call;
    }
}
