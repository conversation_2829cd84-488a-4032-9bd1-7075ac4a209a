package chatroom;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.chatroom.service.soa.dto.MasterApprenticeData;
import org.junit.Test;
import springfox.documentation.spring.web.json.Json;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/31 11 39
 * discription
 */
public class MapUseTest {

    @Test
    public void mapPutTest() {
        Map<String, Long> map = new HashMap<>();
        map.put("1", 0L);
        map.put("2", 0L);
        map.put("3", 0L);

        map.put("2", 2L);

        System.out.println("此时，集合为：" + JsonUtils.objectToString(map));
        Assert.isTrue(map.get("2").equals(2L));
    }

    @Test
    public  void  mapToBeanTest(){
        Map<String, Object> obj1 = new HashMap<>();
        obj1.put("dt","2024-03-08");
        obj1.put("account_uuid","jkfdkjfd");
        obj1.put("consortia_name","11公会");
        obj1.put("consortia_id",11);
        obj1.put("income_amt_1d",9999);

        MasterApprenticeData bean = BeanUtil.mapToBean(obj1, MasterApprenticeData.class, true);

        System.out.println(JsonUtils.objectToString(bean));
    }
}
