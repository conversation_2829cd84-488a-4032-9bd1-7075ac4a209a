package cn.taqu.gonghui.corn;

import cn.hutool.core.bean.BeanUtil;
import cn.taqu.core.utils.BeanUtils;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.common.client.EncryptDecryptClient;
import cn.taqu.gonghui.common.constant.ApprovalStatusEnum;
import cn.taqu.gonghui.common.constant.OperatorTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.common.mapper.ApprovalFlowNodeMapper;
import cn.taqu.gonghui.common.mapper.GeneralUserListMapper;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.OperatorLogMapper;
import cn.taqu.gonghui.system.mapper.OrgAccountLogMapper;
import cn.taqu.gonghui.system.mapper.OrgBankLogMapper;
import cn.taqu.gonghui.system.mapper.OrgCompanyLogMapper;
import cn.taqu.gonghui.system.service.IOperatorLogService;
import cn.taqu.gonghui.system.service.IOrgBankLogService;
import cn.taqu.gonghui.system.service.IOrgCompanyLogService;
import cn.taqu.gonghui.system.service.OrganizationService;
import cn.taqu.gonghui.system.service.impl.OrganizationServiceImpl;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/6/26 16 50
 * discription
 */

@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})

public class EncryptDecryptClientTest {

    @Resource
    private ApprovalFlowNodeMapper approvalFlowNodeMapper;


    @Resource
    private OrganizationServiceImpl organizationServiceImpl;

    @Resource
    private IOperatorLogService iOperatorLogService;

    @Resource
    private OrgAccountLogMapper orgAccountLogMapper;

    @Resource
    private OrgBankLogMapper orgBankLogMapper;

    @Resource
    private IOrgBankLogService iOrgBankLogService;

    @Resource
    private OrgCompanyLogMapper orgCompanyLogMapper;

    @Resource
    private IOrgCompanyLogService iOrgCompanyLogService;


    @Test
    void testGetByRelevanceId(){
        OrgCompanyLog lastByRelevanceId = iOrgCompanyLogService.getLastByRelevanceId(1000992L);
        System.out.println("获取最近一条关联记录为："+ JsonUtils.objectToString(lastByRelevanceId));
    }

    @Test
    void testAddOrgCompanyLog(){
        OrgCompanyLog orgCompanyLog = new OrgCompanyLog();
        orgCompanyLog.setOldLegalPerson("张三");
        orgCompanyLog.setNewLegalPerson("李四");
        orgCompanyLog.setOldLegalPersonPhone("***********");
        orgCompanyLog.setNewLegalPersonPhone("***********");
        orgCompanyLog.setOldLegalPersonidCard("350623199309057642");
        orgCompanyLog.setNewLegalPersonidCard("350623199309057643");
        orgCompanyLog.setCreateOperator("系统管理员");
        orgCompanyLog.setRelevanceId(1333L);
        int insert = orgCompanyLogMapper.insert(orgCompanyLog);
        System.out.println("保存结果为："+ insert);
    }

    @Test
    void testGetOrgBankLogByRelevanceId(){

        OrgBankLog lastByRelevanceId = iOrgBankLogService.getLastByRelevanceId(1333L);
        System.out.println("获取最近一条关联记录为："+ JsonUtils.objectToString(lastByRelevanceId));
    }

    @Test
    void testAddOrgBankLog(){
        OrgBankLog orgBankLog = new OrgBankLog();
        orgBankLog.setOldAccount("***********234567890");
        orgBankLog.setNewAccount("***********234567891");
        orgBankLog.setOldAddress("福建省厦门市软件园三期C08-开户地址-旧");
        orgBankLog.setNewAddress("福建省厦门市软件园三期C08-开户地址-新");
        orgBankLog.setOldBankName("福建省厦门支行-杏林支行1");
        orgBankLog.setNewBankName("福建省厦门支行-杏林支行2");
        orgBankLog.setOldBank("中国工商银行2");
        orgBankLog.setNewBank("中国工商银行3");
        orgBankLog.setCreateOperator("系统管理员");
        orgBankLog.setRelevanceId(1333L);
        int insert = orgBankLogMapper.insert(orgBankLog);
        System.out.println("保存结果为："+ insert);
    }

    @Test
    void testGetOrgAccountLogByRelevanceId(){
//        OrgAccountLog lastByRelevanceId = orgAccountLogMapper.getLastByRelevanceId(1213L);

        OrgAccountLog lastByRelevanceId = orgAccountLogMapper.getLastByRelevanceId(1000794L);
        System.out.println("查询结果为："+ JsonUtils.objectToString(lastByRelevanceId));
    }

    @Test
    void testAddOrgAccountLog(){
        OrgAccountLog log = new OrgAccountLog();
        log.setOldPhone("***********");
        log.setNewPhone("***********");
        log.setOldPrincipal("旧名字");
        log.setNewPrincipal("新名字");
        log.setOldMobilePhone("***********");
        log.setNewMobilePhone("***********");
        log.setOldWeixin("微信号-1");
        log.setNewWeixin("微信号-2");
        log.setOldIdentity("1122334455667788a");
        log.setNewIdentity("1122334455667788b");
        log.setOldSite("福建省厦门市软件园三期-旧地址");
        log.setNewSite("福建省厦门市软件园三期-新地址");
        log.setOldIdentityFront("11111.png");
        log.setNewIdentityFront("22222.png");
        log.setOldIdentityReverse("3333.png");
        log.setNewIdentityReverse("4444.png");
        log.setOldIdentityHand("5555.png");
        log.setNewIdentityHand("6666.png");
        log.setCreateOperator("system");
        log.setRelevanceId(1213L);
        int insert = orgAccountLogMapper.insert(log);
        System.out.println("保存结果为："+ insert);
    }

    @Test
    void testGetOperatorLog(){
        OperatorLog byId = iOperatorLogService.getById(623);
        System.out.println("查询操作日志结果为:" + JsonUtils.objectToString(byId));
    }

    @Test
    void testSaveOperatorLog(){

        Long orgId = 1000703L;
        Organization oldorganization = organizationServiceImpl.getOrgInfo(orgId);
        oldorganization.setChargePersonPhoneCipher(oldorganization.getChargePersonPhone());
        Organization organization = BeanUtil.copyProperties(oldorganization, Organization.class);
        organization.setOrgName(oldorganization.getOrgName()+"_修改后");
        organization.setChargePersonPhoneCipher(organization.getChargePersonPhone());


        organizationServiceImpl. saveOperatorLog(OperatorTypeEnum.UPDATE_GUILD,
                null,
                JSON.toJSONString(oldorganization),
                JSON.toJSONString(organization),
                "system", null);

//        operatorLogMapper.selectByPrimaryKey()
    }





    @Test
    void testSelectCurrentNode(){
        Long flowId = 1343L;
        ApprovalFlowNode approvalFlowNode = approvalFlowNodeMapper.selectCurrentNode(Math.toIntExact(flowId));

        System.out.println("查询到当前节点为："+ JsonUtils.objectToString(approvalFlowNode));
    }

    @Test
    void testInsertApprovalFlowNode(){

        Long flowId = 1343L;
        Date now = new Date();
        ApprovalFlowNode node = new ApprovalFlowNode();
        node.setNodeName("艺人审核");
        node.setNodeRole(UserTypeEnum.DEFAULT.getType());
        node.setNodeUser("1223");
        node.setMobile("13476558199");
        node.setMobileDigest("13476558199");
        node.setMobileCipher("13476558199");
        node.setNodeIndex(1);
        node.setNextIndex(0);
        node.setNodeStatus(ApprovalStatusEnum.WAIT_APPROVAL.getCode());
        node.setCreateTime(now);
        node.setCreateUser("system");
        node.setModifyTime(now);
        node.setModifyUser("system");

        node.setFlowId(flowId);

        List<ApprovalFlowNode> approvalFlowNodeList = new ArrayList<>();
        approvalFlowNodeList.add(node);
        approvalFlowNodeMapper.insertBatch(approvalFlowNodeList);

        List<ApprovalFlowNode> approvalFlowNodes = approvalFlowNodeMapper.selectByFlowId(flowId);

        System.out.println("查询结果为："+ JsonUtils.objectToString(approvalFlowNodes));
    }

    @Resource
    private EncryptDecryptClient encryptDecryptClient;


    @Test
    void testEncryptSM2(){

        String clearText = "13276008977";

        String encrypt = encryptDecryptClient.encrypt(clearText);
        System.out.println(clearText+"加密后的密文为:"+ encrypt);
    }

    @Test
    void testDecryptSM2(){
        String encryptText = "BE+R/bE5kqUUUWjyy8UagldabWW0JBX7/ZDgw+0JAmYpk4eZ8QbysbfgzNE64zMeAb0O5pobqpv71PtXasmML9981lKTbjDXlEH3K8doddEjFTTcdkUG0mtrKeKYGL+/dFhMBH9jLQw9Bazwi277XDxyvg==";
//                "BEC1WlH/XNfNbDMakmoO/qMOceIS9rs9LHOI145w86EkAj0ZRySV6WKLCGklclrKchrdyZc+DvEVaLK/vvs73vKzHp7htlKRX/oocY5w3YoD07p1BLWh3hqNXrPFh3veqqI6Sk9D4k5B";

        String clearText = encryptDecryptClient.decrypt(encryptText);

        System.out.println(encryptText+" 解密后的密文为:"+ clearText);

    }

    @Test
    void TestEncode() {
        String clearTxt = "12232211122";
        String encode = encryptDecryptClient.encode(clearTxt);
        System.out.println(clearTxt+" 加密后摘要为:"+ encode);
    }
}
