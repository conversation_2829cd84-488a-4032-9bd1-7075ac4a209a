package cn.taqu.gonghui.corn;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.soa.InfoManager;
import cn.taqu.gonghui.soa.dto.Info;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-11 10:09
 */
public class ApiTest {

    @Test
    public void testAddCertWhite(){
        HttpPostTestHelper.send(true, "test", "testAddCertWhite", getAuthToken(), "a11111111111a");
    }

    public static String getAuthToken(){
        String service = "account";
        String method = "login";
//        String telephone = "***********";
        String telephone = "***********";
        String s = HttpPostTestHelper.sendWithoutAuthorization(false, service, method, telephone, "123");
        return JSON.parseObject(s).getJSONObject("data").getString("token");
    }


}
