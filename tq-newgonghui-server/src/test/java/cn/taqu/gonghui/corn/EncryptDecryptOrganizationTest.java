package cn.taqu.gonghui.corn;

import cn.hutool.core.bean.BeanUtil;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.chatroom.service.RoomService;
import cn.taqu.gonghui.common.client.EncryptDecryptClient;
import cn.taqu.gonghui.common.constant.ApprovalStatusEnum;
import cn.taqu.gonghui.common.constant.OperatorTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.common.mapper.ApprovalFlowNodeMapper;
import cn.taqu.gonghui.common.mapper.GeneralUserListMapper;
import cn.taqu.gonghui.cron.encrypt.OrgLegalPersonPhoneSyncToEncryptionProcessor;
import cn.taqu.gonghui.system.entity.*;
import cn.taqu.gonghui.system.mapper.OrgAccountLogMapper;
import cn.taqu.gonghui.system.mapper.OrgBankLogMapper;
import cn.taqu.gonghui.system.mapper.OrgCompanyLogMapper;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.search.OrganizationInfoSearch;
import cn.taqu.gonghui.system.service.IOperatorLogService;
import cn.taqu.gonghui.system.service.IOrgBankLogService;
import cn.taqu.gonghui.system.service.IOrgCompanyLogService;
import cn.taqu.gonghui.system.service.IOrgnizatonService;
import cn.taqu.gonghui.system.service.impl.OrganizationServiceImpl;
import com.alibaba.fastjson.JSON;
import com.sun.org.apache.xpath.internal.operations.Or;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/6/26 16 50
 * discription
 */

@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})

public class EncryptDecryptOrganizationTest {

    @Resource
    private IOrgnizatonService iOrgnizatonService;

    @Resource
    private OrganizationMapper organizationMapper;

    @Resource
    private OrgLegalPersonPhoneSyncToEncryptionProcessor orgLegalPersonPhoneSyncToEncryptionProcessor;


    @Test
    void testGet(){
        Organization xdfdfdsa = organizationMapper.getByUuid("xdfdfdsa");
        System.out.println("获取到结果为："+JsonUtils.objectToString(xdfdfdsa));
    }

    @Test
    void testInsert(){
        Organization organization = new Organization();
        organization.setContactPhone("***********");
        organization.setChargePerson("张三呢");
        organization.setOrgName("阳光与微笑");
        organization.setOrgUuid("210291");
        organization.setChargePersonIdCard("352207199908047455");
        organization.setChargePersonPhone("***********");
        organization.setChargePersonEmail("<EMAIL>");
        organization.setChargePersonBirthday(System.currentTimeMillis()/1000);
        organization.setReceivingAddress("福建省厦门市集美区软件园三期C08");
        organization.setLegalPerson("李四如何");
        organization.setLegalPersonIdCard("352207199908076766");
        organization.setPublicReceivingBankAccount("6222020510006854823");
        organization.setAccountName("张三强");
        organization.setAccountBankName("中国工商");
        organization.setProvince("福建省");
        organization.setCity("厦门市");
        organization.setCityId(220);
        organization.setSubBranchName("杏林支行");
        organization.setBusinessPerson("李建国");
        int save = organizationMapper.insert(organization);
        System.out.println("插入organization数据结果 id：" + organization.getOrgId());
    }

    @Test
    void getById(){
        Organization organization = organizationMapper.selectByPrimaryKey(1001087L);
        System.out.println("查询organization数据结果：" + JsonUtils.objectToString(organization));
    }

    @Test
    void testUpdateBySelect(){
        Organization organization = new Organization();
        organization.setOrgId(1001087L);
//        organization.setContactPhone("***********");
//        organization.setChargePerson("张三修改");
//        organization.setOrgName("阳光与微笑修改");
//        organization.setOrgUuid("xdfdfdsa");
//        organization.setChargePersonIdCard("352207199908047455");
//        organization.setChargePersonPhone("***********");
//        organization.setChargePersonEmail("<EMAIL>");
//        organization.setChargePersonBirthday(System.currentTimeMillis()/1000);
//        organization.setReceivingAddress("福建省厦门市集美区软件园三期C08");
//        organization.setLegalPerson("李四如何");
//        organization.setLegalPersonIdCard("352207199908076766");
//        organization.setPublicReceivingBankAccount("6222020510006854823");
//        organization.setAccountName("张三强");
//        organization.setAccountBankName("中国工商");
//        organization.setProvince("福建省");
//        organization.setCity("厦门市");
//        organization.setCityId(220);
//        organization.setSubBranchName("杏林支行");
        organization.setBusinessPerson("李建国");
        int save = organizationMapper.updateByPrimaryKeySelective(organization);
    }

    @Test
    void testFindOrgInfoPageList(){
        OrganizationInfoSearch search = new OrganizationInfoSearch();
        search.setBusinessPerson("李建国");
        search.setMobile("***********");
        List<Organization> orgInfoPageList = organizationMapper.findOrgInfoPageList(search);
        System.out.println("查找结果为："+ JsonUtils.objectToString(orgInfoPageList));
    }

    @Test
    void testUpdateLegalPersonPhoneCipherByRange(){

//        orgLegalPersonPhoneSyncToEncryptionProcessor.queryAndUpdateLegalPersonPhoneCipher(1000754L, 1000853L);
    }

}
