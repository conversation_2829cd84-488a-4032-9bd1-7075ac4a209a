package cn.taqu.gonghui.corn;

import cn.hutool.log.Log;
import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.entity.ApprovalFlowNode;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.service.iservice.IApprovalFlowNodeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/6/26 16 50
 * discription
 */

@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})
@Slf4j
public class EncryptSyncTest {


    @Resource
    private IApprovalFlowNodeService iApprovalFlowNodeService;

    @Test
    void testGetAndSyncEncryption(){
        Long startId = 0L;
        Long endId = 10L;
        List<ApprovalFlowNode> byRange = iApprovalFlowNodeService.getByRange(startId, endId);
        if(CollectionUtils.isEmpty(byRange)){
            log.info("区间：{}~{},没有记录，继续~", startId, endId);
        }else {
            log.info("拉取区间：{}~{},记录:{}条~", startId, endId, byRange.size());

            //更新数据
            byRange.forEach(l->l.setMobile(l.getMobile()));
            iApprovalFlowNodeService.updateBatchById(byRange);
        }

    }


}
