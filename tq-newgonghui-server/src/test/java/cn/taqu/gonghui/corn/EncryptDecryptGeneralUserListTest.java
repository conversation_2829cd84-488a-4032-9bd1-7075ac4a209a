package cn.taqu.gonghui.corn;

import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.common.mapper.GeneralUserListMapper;
import cn.taqu.gonghui.system.entity.GeneralUserList;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.service.IOrgnizatonService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2024/6/26 16 50
 * discription
 */

@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})

public class EncryptDecryptGeneralUserListTest {

    @Resource
    private GeneralUserListMapper generalUserListMapper;


    @Test
    void testInsertGeneralUserListItem(){
        GeneralUserList generalUserList = new GeneralUserList();
        generalUserList.setScene(1);
        generalUserList.setCreateTime(DateUtil.currentTimeSeconds());
        generalUserList.setCreateUname("system");
        generalUserList.setLoginName("admin");
        generalUserList.setTrueName("哈哈斧蛤");
        generalUserList.setTrueNameCipher("哈哈斧蛤");
        generalUserList.setModifyUname("admin");
        generalUserListMapper.insert(generalUserList);

    }

    @Test
    void testGetGeneralUserListItem(){
        QueryWrapper<GeneralUserList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", 27);
        GeneralUserList exists = generalUserListMapper.selectOne(queryWrapper);
        System.out.println("获取到id=27的记录为："+JsonUtils.objectToString(exists));
    }




}
