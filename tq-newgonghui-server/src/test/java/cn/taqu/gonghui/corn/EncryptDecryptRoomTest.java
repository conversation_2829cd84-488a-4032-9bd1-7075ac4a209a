package cn.taqu.gonghui.corn;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.chatroom.entity.Room;
import cn.taqu.gonghui.chatroom.mapper.RoomMapper;
import cn.taqu.gonghui.chatroom.service.RoomService;
import cn.taqu.gonghui.common.constant.OrgIdEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.system.entity.Organization;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.mapper.OrganizationMapper;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.service.IOrgnizatonService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/6/26 16 50
 * discription
 */

@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})

public class EncryptDecryptRoomTest {

    @Resource
    private RoomService roomService;

    @Resource
    private RoomMapper roomMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Test
    void testGet(){
        List<Room> rooms = roomMapper.listByStatusOrderByIdDesc(Arrays.asList(0), 94L, 10);
        System.out.println("查询结果:"+ JsonUtils.objectToString(rooms));
    }

    @Test
    void testInsert(){
        Room room = new Room();
        room.setRoomNo("131415");
        room.setAuditOrderNo("p001");
        room.setOrgUuid("hhhhhhhh");
        room.setTeamId(1315L);
        room.setAccountUuid("hhhhhh");
        room.setMobile("***********");
        room.setOwnerName("张三呀");
        room.setType(1);
        room.setSource(1);
        room.setStatus(0);
        room.setCreateTime(new Date());
        room.setRemark("备注备注");
        room.setCreateBy("sys");
        room.setUpdateBy("sys");
        room.setModifyTime(new Date());
        roomService.save(room);
//        roomMapper.insert(room);
        System.out.println("插入organization数据===" );
    }


    @Test
    void testUpdateSysUserById(){

        SysUser sysUserUpdate = sysUserMapper.selectById(1000100);
        sysUserUpdate.setOrgId((long) OrgIdEnum.ZERO.getValue());
        sysUserUpdate.setOrgName("");
        sysUserUpdate.setUserName("张撒12");
        sysUserUpdate.setUserType(UserTypeEnum.DEFAULT.getType());
        sysUserMapper.updateById(sysUserUpdate);
    }


}
