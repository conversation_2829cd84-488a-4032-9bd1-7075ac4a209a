package cn.taqu.gonghui.corn;

import cn.taqu.core.utils.Encodes;
import cn.taqu.core.utils.JsonUtils;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-11 10:06
 */
public class HttpPostTestHelper {

    // 开发环境
//    private final static String PREFIX_URL = "http://127.0.0.1:8087/tq-newgonghui/api";
    private final static String PREFIX_URL = "http://10.73.154.221:31122/tq-newgonghui/api";

    private static RestTemplate restTemplate(){
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(30000);// 设置连接超时，单位毫秒
        requestFactory.setReadTimeout(30000);  //设置读取超时
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(requestFactory);
        return restTemplate;
    }

    /**
     * 发送 【不带登录态的】 http post请求
     * @param service
     * @param method
     * @param form
     * @return
     */
    public static String sendWithoutAuthorization(Boolean printFlag, String service, String method, Object...form){
        return send(printFlag, service, method, null, form);
    }

    /**
     * 发送 【带登录态的】 http post请求
     * @param service
     * @param method
     * @param authToken
     * @param form
     * @return
     */
    public static String send(Boolean printFlag, String service, String method, String authToken, Object...form){
        String url = PREFIX_URL + "?service=" + service + "&method=" + method;
        RestTemplate restTemplate = restTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        if(StringUtils.isNotBlank(authToken)){
            headers.add("Authorization", "Bearer " + authToken);
        }

        LinkedMultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<>();
        paramMap.add("form", Encodes.encodeBase64(Encodes.decodeCharset(JsonUtils.objectToString(form), "UTF-8")));
        HttpEntity<LinkedMultiValueMap<String, Object>> httpEntity = new HttpEntity<>(paramMap, headers);

        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, httpEntity, JSONObject.class);
        String jsonStr = responseEntity.getBody().toJSONString();
        if(printFlag){
            TestUtil.printConsole(jsonStr);
        }
        return jsonStr;
    }
}
