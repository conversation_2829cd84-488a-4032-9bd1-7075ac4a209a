package cn.taqu.gonghui.corn;

import cn.taqu.core.utils.DateUtil;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.common.constant.EmployeeStatusEnum;
import cn.taqu.gonghui.common.constant.TeamTypeEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.entity.TeamHost;
import cn.taqu.gonghui.system.mapper.TeamEmployeeMapper;
import cn.taqu.gonghui.system.mapper.TeamHostMapper;
import cn.taqu.gonghui.system.search.TeamEmployeeSearch;
import cn.taqu.gonghui.system.service.TeamHostService;
import cn.taqu.gonghui.system.vo.TeamEmployeeVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/6/26 16 50
 * discription
 */

@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})

public class EncryptDecryptTeamHostTest {


    @Resource
    private TeamHostMapper teamHostMapper;

    @Resource
    private TeamHostService teamHostService;

    @Test
    void testInsert(){
        TeamHost teamHost = new TeamHost();
        teamHost.setTeamId(1000520L);
        teamHost.setOrgId(113L);
        teamHost.setTeamType(TeamTypeEnum.TALK_TEAM.getValue());
        teamHost.setHostUuid("yyyyyyyy");
        teamHost.setRealName("王唯一");
        teamHost.setUpdateTime(DateUtil.currentTimeSeconds());
        teamHost.setCreateTime(DateUtil.currentTimeSeconds());
        teamHost.setInviteTime(DateUtil.currentTimeSeconds());
        teamHost.setChangeTime(0l);
        teamHost.setStatus(1);
        teamHost.setEmployeeId(null);
        teamHost.setNewSharingProfitRate("");
        teamHost.setCurrentSharingProfitRate("");
        teamHost.setIsUpdate(0);
//        teamHostService.saveTeamHost(teamHost);
        teamHostMapper.insert(teamHost);

    }

    @Test
    void testUpdateRealName(){
        TeamHost teamHost = new TeamHost();
        teamHost.setId(23317L);
        teamHost.setTeamId(1000520L);
        teamHost.setOrgId(113L);
        teamHost.setTeamType(TeamTypeEnum.TALK_TEAM.getValue());
        teamHost.setHostUuid("yyyyyyyy");
        teamHost.setRealName("王唯san");
        teamHost.setUpdateTime(DateUtil.currentTimeSeconds());
        teamHost.setCreateTime(DateUtil.currentTimeSeconds());
        teamHost.setInviteTime(DateUtil.currentTimeSeconds());
        teamHost.setChangeTime(0l);
        teamHost.setStatus(1);
        teamHost.setEmployeeId(null);
        teamHost.setNewSharingProfitRate("");
        teamHost.setCurrentSharingProfitRate("");
        teamHost.setIsUpdate(0);
        teamHost.setIsGroup(1);
        teamHostMapper.updateByPrimaryKey(teamHost);
    }

    @Test
    void testSelectHostOneByUuid(){
        String hostUuid = "yyyyyyyy";
        Integer teamType = 3;
        TeamHost teamHost = teamHostMapper.selectHostOneByUuid(hostUuid, teamType);
        System.out.println("根据hostUuid:"+hostUuid+" teamType:"+teamType+",获取记录为："+ JsonUtils.objectToString(teamHost));
    }

    @Test
    void  testSelectByHostType()
    {
        List<TeamHost> list = teamHostService.getBatchHostType(TeamTypeEnum.TALK_TEAM.getValue());

        System.out.println("查询聊天室类型的所有主播，结果为："+ JsonUtils.objectToString(list));

    }

}
