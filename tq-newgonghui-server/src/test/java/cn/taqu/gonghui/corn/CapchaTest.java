package cn.taqu.gonghui.corn;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-11 17:53
 */
public class CapchaTest {


    @Test
    public void getLiveTeamChangLogPage(){
        String service = "backstageVerify";
        String method = "getCaptcha";

        String s = HttpPostTestHelper.sendWithoutAuthorization(true, service, method, 60);
    }
}
