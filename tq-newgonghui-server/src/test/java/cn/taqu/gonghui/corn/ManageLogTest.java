package cn.taqu.gonghui.corn;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-05-10 16:33
 */
public class ManageLogTest {

    @Test
    public void getLiveTeamChangLogPage(){
        String service = "manageLog";
        String method = "getLiveTeamChangLogPage";

        List<Object> objects = Arrays.asList("1", "30", null, null, null);
        String s = HttpPostTestHelper.sendWithoutAuthorization(true, service, method, "1", "1", null, 999999999999L, null);
    }
}
