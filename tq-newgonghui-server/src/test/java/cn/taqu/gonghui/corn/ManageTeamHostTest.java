package cn.taqu.gonghui.corn;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.utils.JsonUtils;
import org.junit.jupiter.api.Test;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/22 3:47 下午
 **/
public class ManageTeamHostTest {

    @Test
    public void testInviteHost() {
        String[] hostIds = new String[]{"havn8titzkg", "1"};
        SoaClient soaClient = new SoaClient("http://127.0.0.1:8087/tq-newgonghui/api");
        SoaResponse call = soaClient.call("manageTeamHost", "getHostOrg", hostIds);
        System.out.println(JsonUtils.objectToString(call));
    }
}
