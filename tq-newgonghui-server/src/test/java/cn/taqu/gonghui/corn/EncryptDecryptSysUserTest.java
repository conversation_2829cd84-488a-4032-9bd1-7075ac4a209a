package cn.taqu.gonghui.corn;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.chatroom.entity.Room;
import cn.taqu.gonghui.chatroom.mapper.RoomMapper;
import cn.taqu.gonghui.chatroom.service.RoomService;
import cn.taqu.gonghui.common.constant.OrgIdEnum;
import cn.taqu.gonghui.common.constant.UserStatus;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.search.UserSearch;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.vo.SysUserVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections.MapUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2024/6/26 16 50
 * discription
 */

@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})

public class EncryptDecryptSysUserTest {


    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysUserService sysUserService;


//    @Test
//    void testInsertSysUser() {
//        SysUser sysUserRecord = new SysUser();
//        sysUserRecord.setOrgId(1001L);
//        sysUserRecord.setOrgName("不存在的机构");
//        sysUserRecord.setAccountUuid("xxxxxx");
//        sysUserRecord.setUserName("不存在的用户名");
//        sysUserRecord.setMobile("***********");
//        sysUserRecord.setUserType(UserTypeEnum.LEADER.getType());
//        sysUserRecord.setStatus(UserStatus.OK.getCode());
//        sysUserRecord.setCreateTime(System.currentTimeMillis() / 1000);
//        sysUserRecord.setUpdateTime(System.currentTimeMillis() / 1000);
//        int insert = sysUserMapper.insert(sysUserRecord);
//        System.out.println("插入一条SysUser记录，结果为：" + insert);
//    }


    @Test
    void testUpdateSysUserById() {

        SysUser sysUserUpdate = sysUserMapper.selectById(1000630);
        sysUserUpdate.setOrgId((long) OrgIdEnum.ZERO.getValue());
        sysUserUpdate.setOrgName("测试看看");
        sysUserUpdate.setUserName("不存在的用户名2");
        sysUserUpdate.setMobile("***********");
        sysUserUpdate.setUserType(UserTypeEnum.DEFAULT.getType());
        sysUserMapper.updateById(sysUserUpdate);
    }

    @Test
    void testGetByMobile(){
        String mobile = "12232211122";
        SysUser sysUser = sysUserMapper.selectUserByMobile(mobile, EncryptSwitchConfig.selectByDigest);
        System.out.println("获取到手机号"+mobile + "的系统用户信息为："+ JsonUtils.objectToString(sysUser));
    }

    @Test
    void testGetByMobile2(){
        String mobile = "12232211122";
        SysUser sysUser = sysUserService.selectUserByMobile(mobile);
        System.out.println("获取到手机号"+mobile + "的系统用户信息为："+ JsonUtils.objectToString(sysUser));
    }

    @Test
    void testSelectUserBySearch(){
        String mobile = "***********";
        UserSearch search = new UserSearch();
        search.setMobile(mobile);
        Page<SysUserVo> page = new Page<>(1, 10);
        IPage<SysUserVo> sysUserVoIPage = sysUserMapper.selectUserBySearch(page, search);
        System.out.println("获取到手机号"+mobile + "的系统用户信息为："+ JsonUtils.objectToString(sysUserVoIPage));
    }



}
