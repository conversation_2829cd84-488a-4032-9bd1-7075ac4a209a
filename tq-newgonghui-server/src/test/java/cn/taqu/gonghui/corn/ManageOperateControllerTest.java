package cn.taqu.gonghui.corn;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.system.service.TimedTaskService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.ParseException;


@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})
@Slf4j
@RunWith(SpringRunner.class)
class ManageOperateControllerTest extends BaseControllerTest {

    @Autowired
    TimedTaskService timedTaskService;
    @Test
    void updateOrgOperatorTest() {
        String operator="admin";
        String modifyOperator="曾俊杰";
        Long orgId=1000968L;
//        String uuid = "";
//        String operatorName ="";
//        Integer pageNo = 1;
//        Integer pageSize = 10;

        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        Object[] data = {operator, modifyOperator, orgId}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("manageOrganization", "updateOrgOperator", data);
        System.out.println("===========checkPassword========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========checkPassword=========end======");

    }
    @Test
    void operatorAddTest() {
        String logname = "陈小陈登录";
        String name = "陈小陈";
        Integer permission = 1;

        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        Object[] data = {logname, name, permission}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("manageOperate", "operatorAdd", data);
        System.out.println("===========checkPassword========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========checkPassword=========end======");

    }

    @Test
    void targetSaveTest() {
        Long money = 2000L;
        Integer survivalRate = 30;
        Long number = 200L;

        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        Object[] data = {money, survivalRate, number}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("manageOperate", "targetSave", data);
        System.out.println("===========checkPassword========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========checkPassword=========end======");

    }

    @Test
    void targetInfoTest() {


        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        Object[] data = {}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("manageOperate", "targetInfo", data);
        System.out.println("===========checkPassword========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========checkPassword=========end======");

    }

    @Test
    void myDataListTest() {


        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        String name = "陈小陈";
        Object[] data = {name}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("tabulateData", "tabulateDataList", data);
        System.out.println("===========checkPassword========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========checkPassword=========end======");

    }
    @Test
    void manageSharingProfitTest() {


        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        String[] name = new String[]{"wfbcfwbababwlh27","wfbgfybababasazw"};
        Object[] data = {name}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("manageSharingProfit", "getRateByHostUuids", data);
        System.out.println("===========checkPassword========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========checkPassword=========end======");

    }

    @Test
    void manageTeamTest() {


        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        String[] name = new String[]{"wfbcfwbababwlh27","wfbgfybababasazw"};
        Object[] data = {name}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("manageTeam", "liveTeamTree", data);
        System.out.println("===========liveTeamTree========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========liveTeamTree=========end======");

    }

    @Test
    void Gonghui2Test() {


        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        String name = "22";
        String name2 = "223";

        Object[] data = {name,name2}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("Gonghui2", "getFrameListExtraInfo", data);
        System.out.println("===========liveTeamTree========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========liveTeamTree=========end======");

    }

    @Test
    void recreationTest() {


        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        String name = "22";
        String name2 = "223";

        Object[] data = {name,name2}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("recreation", "detailBytitle", data);
        System.out.println("===========liveTeamTree========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========liveTeamTree=========end======");

    }





    @Test

    void timedTaskServiceTest() throws ParseException {
//        timedTaskService.myDataTimedTask("202204");
        timedTaskService.myPerformance("202206");
//        timedTaskService.currentMonth("202205");
//        timedTaskService.currentMonth("202204");
//        timedTaskService.currentMonth("202203");
//        timedTaskService.currentMonth("202202");
//        timedTaskService.currentMonth("202201");

    }


    @Test
    public void testAddCertWhite(){
        HttpPostTestHelper.send(true, "manageOrganization", "userOrganization", getAuthToken(), "a11111111111a");
    }

    public static String getAuthToken(){
        String service = "account";
        String method = "login";
//        String telephone = "***********";
        String telephone = "***********";
        String s = HttpPostTestHelper.sendWithoutAuthorization(false, service, method, telephone, "123");
        return JSON.parseObject(s).getJSONObject("data").getString("token");
    }

}
