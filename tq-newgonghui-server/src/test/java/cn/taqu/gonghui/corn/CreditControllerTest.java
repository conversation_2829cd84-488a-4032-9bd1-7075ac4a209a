package cn.taqu.gonghui.corn;

import cn.taqu.core.common.client.SoaClient;
import cn.taqu.core.common.client.SoaResponse;
import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.common.client.EncryptDecryptClient;
import cn.taqu.gonghui.soa.InfoManager;
import cn.taqu.gonghui.soa.dto.Info;
import cn.taqu.gonghui.system.service.TimedTaskService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;


@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})
@Slf4j
@RunWith(SpringRunner.class)
class CreditControllerTest extends BaseControllerTest {

    @Autowired
    TimedTaskService timedTaskService;
    @Test
    void creditPageTest() {

        Integer pageNo = 1;
        Integer pageSize = 10;
        String orgId = "1";

        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        Object[] data = { pageNo, pageSize,orgId}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("credit", "creditPage", data);
        System.out.println("===========creditPage========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========creditPage=========end======");

    }

    @Test
    void getCreditGradeTest() {

        String orgId = "1";

        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        Object[] data = { orgId}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("credit", "getCreditGrade", data);
        System.out.println("===========creditPage========start=======");
        System.out.println(JSON.toJSONString(soaResponse.getData()));
        System.out.println("===========creditPage=========end======");

    }

    @Test
    void updateCreditGrade() {

        String orgId ="1000688";
        Integer creditGrad = 40;
        String reason = "可以";

        SoaClient soaClient = new SoaClient("http://127.0.0.1:8071/tq-newgonghui/api");
        Object[] data = { orgId, creditGrad,reason}; //uuid password
        this.iniSoaBaseParams();
        SoaResponse soaResponse = soaClient.call("credit", "updateCreditGrade", data);
        System.out.println("===========creditPage========start=======");
        System.out.println(JSON.toJSONString(soaResponse));
        System.out.println("===========creditPage=========end======");

    }


    @Resource
    private InfoManager infoManager;

    @Test
    public void testGetAccountInfoByuuids(){

        Map<String, Info> infoByUuidsNoSecret = infoManager.getInfoByUuidsNoSecret(Lists.newArrayList("bhacaabieafabafb", "bghheigicdigcedi"));
        System.out.println(JsonUtils.objectToString(infoByUuidsNoSecret));
    }


}
