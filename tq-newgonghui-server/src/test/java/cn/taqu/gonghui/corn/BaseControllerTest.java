package cn.taqu.gonghui.corn;

import cn.taqu.core.web.protocol.SoaBaseParams;


abstract class BaseControllerTest {

    public void iniSoaBaseParams() {
        SoaBaseParams soaBaseParams = SoaBaseParams.fromThread();
        soaBaseParams.setAppcode(String.valueOf(1));
        soaBaseParams.setCloned(String.valueOf(1));
        soaBaseParams.setToken("1");
        soaBaseParams.setPlatformName("android");
        soaBaseParams.setIp("*************");

    }
}
