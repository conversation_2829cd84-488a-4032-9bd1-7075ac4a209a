package cn.taqu.gonghui.corn;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.common.constant.EmployeeStatusEnum;
import cn.taqu.gonghui.common.constant.OrgIdEnum;
import cn.taqu.gonghui.common.constant.UserTypeEnum;
import cn.taqu.gonghui.live.entity.Employee;
import cn.taqu.gonghui.system.common.EncryptSwitchConfig;
import cn.taqu.gonghui.system.entity.SysUser;
import cn.taqu.gonghui.system.entity.TeamEmployee;
import cn.taqu.gonghui.system.mapper.SysUserMapper;
import cn.taqu.gonghui.system.mapper.TeamEmployeeMapper;
import cn.taqu.gonghui.system.search.TeamEmployeeSearch;
import cn.taqu.gonghui.system.service.SysUserService;
import cn.taqu.gonghui.system.vo.TeamEmployeeVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/6/26 16 50
 * discription
 */

@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})

public class EncryptDecryptTeamEmployeeTest {


    @Resource
    private TeamEmployeeMapper teamEmployeeMapper;
    


    @Test
    void testInsertSysUser() {

        TeamEmployee teamEmployee = new TeamEmployee();
        teamEmployee.setOrgId(113L);
        teamEmployee.setOrgName("不存在的机构");
        teamEmployee.setTeamId(114L);
        teamEmployee.setUserId(123L);
        // 解密
        teamEmployee.setMobile("12345678901");
        teamEmployee.setEmployeeName("王五");
        teamEmployee.setType(UserTypeEnum.AGENTER.getType());
        teamEmployee.setStatus(EmployeeStatusEnum.IN_THE_JOB.getValue());
        teamEmployee.setInviteTime(System.currentTimeMillis()/1000);
        teamEmployee.setCreateTime(System.currentTimeMillis()/1000);
        teamEmployee.setUpdateTime(System.currentTimeMillis()/1000);
        int insert = teamEmployeeMapper.insert(teamEmployee);
        System.out.println("插入一条SysUser记录，结果为：" + insert);
    }

    @Test
    void testPageList(){
        IPage<TeamEmployeeVo> page = new Page<>(1, 10);
        TeamEmployeeSearch search = new TeamEmployeeSearch();
        search.setSelectByDigest(1);
        search.setMobile("12345678901");
        IPage<TeamEmployeeVo> teamEmployeeVoIPage = teamEmployeeMapper.selectTeamEmployeeList(page, search);
        System.out.println("查询结果为：" + JsonUtils.objectToString(teamEmployeeVoIPage));
    }

    @Test
    void testSelectList(){
        Long orgId = 113L;
        List<TeamEmployeeVo> teamEmployeeVos = teamEmployeeMapper.selectLeaderList(orgId, 1);
        System.out.println("查询结果为：" + JsonUtils.objectToString(teamEmployeeVos));
    }

    @Test
    void testSelectMobile(){
        String mobile = "12345678901";
        TeamEmployee teamEmployee = teamEmployeeMapper.selectMobile(mobile, 1, EncryptSwitchConfig.selectByDigest);
        System.out.println("查询结果为：" + JsonUtils.objectToString(teamEmployee));
    }

    @Test
    void testGetAgenterTree(){

        Long teamId = 1000520L;
        List<TeamEmployee> agenterTree = teamEmployeeMapper.getAgenterTree(teamId);
        System.out.println("查询结果为：" + JsonUtils.objectToString(agenterTree));
    }






}
