package cn.taqu.gonghui.live;

import cn.taqu.core.utils.JsonUtils;
import cn.taqu.gonghui.Application;
import cn.taqu.gonghui.common.utils.PageDataResult;
import cn.taqu.gonghui.live.param.*;
import cn.taqu.gonghui.soa.MultiLiveSoaService;
import cn.taqu.gonghui.system.service.multilive.MultiLiveStatsService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/16 6:06 下午
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class}, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT, properties = {"server.port=8071"})
public class MultiLiveServiceTest {



    @Resource
    private MultiLiveStatsService multiLiveStatsService;

    @Test
    public void testSearchAdminHostData() {

        MultiLiveHostSearch search = new MultiLiveHostSearch();
        search.setTimeType(1);
        search.setStartTime(1722355200L);
        search.setEndTime(1722441600L);
        search.setPage(2);
        PageDataResult result = multiLiveStatsService.searchAdminHostData(search);
        System.out.println("搜索管理端主播数据信息，结果为:" + JsonUtils.objectToString(result));

    }


    @Test
    public void testGetAdminHostDataDownload() {

        MultiLiveHostSearch search = new MultiLiveHostSearch();
        search.setTimeType(1);
        search.setStartTime(1722355200L);
        search.setEndTime(1722441600L);
        search.setPage(2);
        String url = multiLiveStatsService.getAdminHostDataDownloadUrl(search);
        System.out.println("获取管理端主播数据信息的下载地址，结果为:" + JsonUtils.objectToString(url));

    }

    @Test
    public void testSearchAdminConsortiaData() {

        MultiLiveConsortiaSearch search = new MultiLiveConsortiaSearch();
        search.setTimeType(1);
        search.setStartTime(1722355200L);
        search.setEndTime(1722441600L);
        search.setPage(1);
        PageDataResult result = multiLiveStatsService.searchAdminConsortiaData(search);
        System.out.println("搜索管理端公会数据信息，结果为:" + JsonUtils.objectToString(result));

    }

    @Test
    public void testGetAdminConsortiaDataDownloadUrl() {

        MultiLiveConsortiaSearch search = new MultiLiveConsortiaSearch();
        search.setTimeType(1);
        search.setStartTime(1722355200L);
        search.setEndTime(1722441600L);
        search.setPage(1);
        String url = multiLiveStatsService.getAdminConsortiaDataDownloadUrl(search);
        System.out.println("获取管理端公会数据信息下载地址，结果为:" + url);

    }

    @Resource
    private MultiLiveSoaService multiLiveSoaService;

    @Test
    public void testSearchHostData(){
        MultiLiveHostSearchOfUM search = new MultiLiveHostSearchOfUM();
        search.setStartTime(1722355200L);
        search.setEndTime(1722441600L);
        search.setPage(1);
        search.setConsortiaIds(Lists.newArrayList( 1000309L));

        PageDataResult result = multiLiveSoaService.hostStatsData(search);
        System.out.println("搜索用户端主播数据信息，结果为:" + JsonUtils.objectToString(result));
    }

    @Test
    public void testSearchRoomData(){

        MultiLiveRoomSearchOfUM search = new MultiLiveRoomSearchOfUM();
        search.setStartTime(1722355200L);
        search.setEndTime(1722441600L);
        search.setPage(1);
        search.setTeamIds(Lists.newArrayList( 1000309L));

        PageDataResult result = multiLiveSoaService.roomStatsData(search);
        System.out.println("搜索用户端房间数据信息，结果为:" + JsonUtils.objectToString(result));
    }


    @Test
    public void testSearchConsortiaData(){

        MultiLiveConsortiaSearchOfUM search = new MultiLiveConsortiaSearchOfUM();
        search.setStartTime(1722355200L);
        search.setEndTime(1722441600L);
        search.setPage(1);
        search.setTeamIdList(Lists.newArrayList( 1000309L));

        PageDataResult result = multiLiveSoaService.consortiaStatsData(search);
        System.out.println("搜索用户端房间数据信息，结果为:" + JsonUtils.objectToString(result));
    }



}
