<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>tq-taqu</groupId>
        <artifactId>tq-newgonghui</artifactId>
        <version>1.0.0</version>
    </parent>
    <groupId>cn.taqu</groupId>
    <artifactId>tq-newgonghui-server</artifactId>
    <name>tq-newgonghui-server</name>
    <description>gonghui project for Spring Boot</description>
    <properties>
        <java.version>1.8</java.version>
        <mysql.connector.version>5.1.47</mysql.connector.version>
        <spring-security.version>5.7.8</spring-security.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.taqu</groupId>
            <artifactId>xy-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taqu</groupId>
            <artifactId>xy-mybatisplus</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taqu</groupId>
            <artifactId>xy-tqrpc</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.taqu</groupId>
            <artifactId>xy-tqmq</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>cn.taqu</groupId>
            <artifactId>xy-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-actuator</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-actuator</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-actuator-autoconfigure</artifactId>-->
<!--                </exclusion>-->
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-actuator</artifactId>-->
<!--            <version>2.3.7.RELEASE</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-actuator</artifactId>-->
<!--            <version>2.3.7.RELEASE</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-actuator-autoconfigure</artifactId>-->
<!--            <version>2.3.7.RELEASE</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <!-- jet cache-->
        <dependency>
            <groupId>cn.taqu</groupId>
            <artifactId>tq-mp-jetcache</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-anno</artifactId>
            <version>2.6.4</version>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-redis</artifactId>
            <version>2.6.4</version>
        </dependency>
        <!-- spring security 安全认证 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
            <version>2.7.11</version>
        </dependency>
        <!--Token生成与解析-->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.aliyun</groupId>-->
        <!--            <artifactId>aliyun-java-sdk-core</artifactId>-->
        <!--            <version>4.5.13</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-afs</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.17</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>1.6.6</version>
        </dependency>
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient</artifactId>
            <version>0.9.0</version>
        </dependency>
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_servlet</artifactId>
            <version>0.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
            <version>1.4.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.9.0</version> <!-- 根据需要选择合适的版本 -->
        </dependency>


        <dependency>
            <groupId>cn.taqu</groupId>
            <artifactId>sentinel-nacos-datasource</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>tq-mp-soa</artifactId>
                    <groupId>cn.taqu</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.csp</groupId>
                    <artifactId>sentinel-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.csp</groupId>
                    <artifactId>sentinel-transport-simple-http</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.csp</groupId>
                    <artifactId>sentinel-annotation-aspectj</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.csp</groupId>
                    <artifactId>sentinel-datasource-nacos</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    sentinel核心包    -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-core</artifactId>
            <version>1.8.0</version>
        </dependency>
        <!--    与sentinel控制台通信包    -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-transport-simple-http</artifactId>
            <version>1.8.0</version>
        </dependency>
        <!--    sentinel注解支持包    -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-annotation-aspectj</artifactId>
            <version>1.8.0</version>
        </dependency>
<!--            sentinel微服务集成包-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>
        <!--   sentinel和nacos集成包     -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
            <version>1.8.0</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.13.4</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.3</version>
        </dependency>

        <dependency>
            <groupId>tech.powerjob</groupId>
            <artifactId>powerjob-worker-spring-boot-starter</artifactId>
            <version>4.3.9</version>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <!-- 版本号不能高于1.4.2，否则jsp页面访问有限制。IDE内、spring-boot:run可以访问，java -jar方式无法访问，貌似是1.5的JarLoader跟1.4不同，未深究，留待有缘人解决
                    <version>1.4.2.RELEASE</version> <executions> <execution> <goals> <goal>repackage</goal>
                    </goals> </execution> </executions> -->
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <!-- demo不用发布到私服 -->
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>

                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <!--配置文件的位置-->
                    <configurationFile>src/main/resources/MyBatisGenerator.xml</configurationFile>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
                <executions>
                    <execution>
                        <id>Generate MyBatis Artifacts</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql.connector.version}</version>
                        <!--<scope>runtime</scope>-->
                    </dependency>


                </dependencies>

            </plugin>

        </plugins>
        <finalName>tq-newgonghui-1.0.0</finalName>
    </build>

</project>
