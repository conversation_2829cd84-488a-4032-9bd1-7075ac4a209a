<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.taqu</groupId>
        <artifactId>xy-framework</artifactId>
        <version>3.0.19</version>
    </parent>
    <groupId>tq-taqu</groupId>
    <artifactId>tq-newgonghui</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <modules>
        <module>tq-newgonghui-api</module>
        <module>tq-newgonghui-server</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.encoding>UTF-8</project.encoding>
        <xy.framework.version>3.0.19</xy.framework.version>
        <jwt.version>0.9.1</jwt.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.taqu</groupId>
                <artifactId>xy-jpa</artifactId>
                <version>${xy.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.taqu</groupId>
                        <artifactId>xy-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.taqu</groupId>
                <artifactId>xy-mybatisplus</artifactId>
                <version>${xy.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.taqu</groupId>
                        <artifactId>xy-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.taqu</groupId>
                <artifactId>xy-tqrpc</artifactId>
                <version>${xy.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.taqu</groupId>
                        <artifactId>xy-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.taqu</groupId>
                <artifactId>xy-tqmq</artifactId>
                <version>${xy.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.taqu</groupId>
                        <artifactId>xy-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>cn.taqu</groupId>
                <artifactId>xy-core</artifactId>
                <version>${xy.framework.version}</version>
            </dependency>
            <!--Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.alibaba.cloud</groupId>-->
<!--                <artifactId>spring-cloud-alibaba-dependencies</artifactId>-->
<!--                <version>2.2.5.RELEASE</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.alibaba.cloud</groupId>-->
<!--                <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>-->
<!--                <version>2021.0.1.0</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.alibaba.cloud</groupId>-->
<!--                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
<!--                <version>2.2.5.RELEASE</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>com.alibaba.cloud</groupId>-->
<!--                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>-->
<!--                <version>2.2.5.RELEASE</version>-->
<!--            </dependency>-->
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <executions>
                    <execution>
                        <!-- 绑定source插件到Maven的生命周期 -->
                        <phase>compile</phase>
                        <!--在生命周期后执行绑定的source插件的goals -->
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <compilerVersion>${java.version}</compilerVersion>
                    <encoding>${project.encoding}</encoding>
                    <showDeprecation>true</showDeprecation>
                    <showWarnings>true</showWarnings>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.0.0-M1</version>
                <executions>
                    <execution>
                        <id>enforce-rules</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <message>
                                        <![CDATA[You are running an older version of Java. Requires at least JDK ${java.version}]]>
                                    </message>
                                    <version>${java.version}</version>
                                </requireJavaVersion>
                                <bannedDependencies>
                                    <searchTransitive>true</searchTransitive>
                                    <excludes>
                                        <exclude>commons-logging*</exclude>
                                        <exclude>aspectj:aspectj*</exclude>
                                        <exclude>org.springframework:2.*</exclude>
                                        <exclude>org.springframework:3.*</exclude>
                                    </excludes>
                                </bannedDependencies>
                            </rules>
                            <fail>true</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.20</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>taqu</id>
            <name>Internal Releases</name>
            <url>http://nexus.proxy.internal.taqu.cn/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>taqu</id>
            <name>Internal Snapshots</name>
            <url>http://nexus.proxy.internal.taqu.cn/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>nexus</id>
            <url>http://nexus.proxy.internal.taqu.cn/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
</project>
